<?php
require_once 'includes/header.php';

// CSS for movie cards
?>
<style>
    /* Movie Card Styles */
    .movie-card-link {
        display: block;
        text-decoration: none;
        color: white;
        height: 100%;
    }

    .movie-card {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        height: 100%;
        margin-bottom: 0;
    }

    .movie-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    }

    .movie-card img {
        width: 100%;
        height: auto;
        aspect-ratio: 2/3;
        object-fit: cover;
        transition: all 0.3s ease;
    }

    .movie-card:hover img {
        filter: brightness(0.7);
    }

    .movie-card-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(to top, rgba(0, 0, 0, 0.9), transparent);
        padding: 15px 10px;
        transition: all 0.3s ease;
    }

    .movie-card:hover .movie-card-overlay {
        background: linear-gradient(to top, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.7));
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
    }

    .movie-card-title {
        font-size: 0.9rem;
        font-weight: bold;
        margin-bottom: 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .movie-card:hover .movie-card-title {
        white-space: normal;
        overflow: visible;
    }

    .movie-card-info {
        font-size: 0.7rem;
        color: #aaa;
        margin-bottom: 5px;
    }

    .movie-card-rating {
        font-size: 0.8rem;
        color: #ffc107;
    }

    .movie-card-buttons {
        position: absolute;
        top: 10px;
        right: 10px;
        display: flex;
        flex-direction: column;
        gap: 5px;
        opacity: 0;
        transition: all 0.3s ease;
    }

    .movie-card:hover .movie-card-buttons {
        opacity: 1;
    }

    .movie-card-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        font-size: 0.8rem;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
    }

    .movie-card-btn:hover {
        background-color: #dc3545;
        transform: scale(1.1);
        color: white;
    }

    .premium-badge {
        position: absolute;
        top: 10px;
        left: 10px;
        background-color: #ffc107;
        color: #000;
        padding: 3px 6px;
        border-radius: 3px;
        font-size: 0.7rem;
        font-weight: bold;
        z-index: 2;
    }

    /* Responsive adjustments */
    @media (max-width: 767px) {
        .movie-card-title {
            font-size: 0.8rem;
        }

        .movie-card-info {
            font-size: 0.65rem;
        }
    }
</style>
<?php

// Check if user is logged in
if (!isLoggedIn()) {
    // Save current page as redirect destination after login
    $_SESSION['redirect_to'] = SITE_URL . '/watchlist.php';
    redirect(SITE_URL . '/login.php');
}

// Get user's watchlist
$watchlist_query = "SELECT w.id as watchlist_id, w.content_type, w.content_id, w.added_at,
                   CASE
                       WHEN w.content_type = 'movie' THEN m.title
                       ELSE t.title
                   END as title,
                   CASE
                       WHEN w.content_type = 'movie' THEN m.poster
                       ELSE t.poster
                   END as poster,
                   CASE
                       WHEN w.content_type = 'movie' THEN m.rating
                       ELSE t.rating
                   END as rating,
                   CASE
                       WHEN w.content_type = 'movie' THEN c1.name
                       ELSE c2.name
                   END as category_name,
                   CASE
                       WHEN w.content_type = 'movie' THEN m.release_year
                       ELSE t.start_year
                   END as year,
                   CASE
                       WHEN w.content_type = 'movie' THEN NULL
                       ELSE t.end_year
                   END as end_year,
                   CASE
                       WHEN w.content_type = 'movie' THEN m.premium_only
                       ELSE t.premium_only
                   END as premium_only
                   FROM watchlist w
                   LEFT JOIN movies m ON w.content_type = 'movie' AND w.content_id = m.id
                   LEFT JOIN tvshows t ON w.content_type = 'tvshow' AND w.content_id = t.id
                   LEFT JOIN categories c1 ON m.category_id = c1.id
                   LEFT JOIN categories c2 ON t.category_id = c2.id
                   WHERE w.user_id = {$_SESSION['user_id']}
                   ORDER BY w.added_at DESC";

$watchlist_result = mysqli_query($conn, $watchlist_query);
?>

<!-- Page Header -->
<section class="py-5 bg-dark">
    <div class="container">
        <h1 class="display-4 fw-bold">My Watchlist</h1>
        <p class="lead text-muted">Keep track of movies and TV shows you want to watch</p>
    </div>
</section>

<!-- Watchlist Section -->
<section class="py-5">
    <div class="container" id="watchlist-container">
        <?php if(mysqli_num_rows($watchlist_result) > 0): ?>
            <div class="row">
                <?php while($item = mysqli_fetch_assoc($watchlist_result)): ?>
                <div class="col-6 col-sm-4 col-md-3 col-lg-2 mb-3 watchlist-item" id="watchlist-item-<?php echo $item['watchlist_id']; ?>">
                    <?php if($item['content_type'] == 'movie'): ?>
                    <a href="<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $item['content_id']; ?>" class="movie-card-link">
                    <?php else: ?>
                    <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $item['content_id']; ?>" class="movie-card-link">
                    <?php endif; ?>
                        <div class="movie-card">
                            <?php if($item['premium_only']): ?>
                            <div class="premium-badge">
                                <i class="fas fa-crown"></i>
                            </div>
                            <?php endif; ?>
                            <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $item['poster']; ?>" alt="<?php echo $item['title']; ?>">
                            <div class="movie-card-overlay">
                                <h5 class="movie-card-title"><?php echo $item['title']; ?></h5>
                                <div class="movie-card-info">
                                    <?php if($item['content_type'] == 'movie'): ?>
                                    <span><i class="fas fa-calendar-alt"></i> <?php echo $item['year']; ?></span>
                                    <?php else: ?>
                                    <span><i class="fas fa-calendar-alt"></i> <?php echo $item['year']; ?><?php echo $item['end_year'] ? ' - ' . $item['end_year'] : ' - Present'; ?></span>
                                    <?php endif; ?> •
                                    <span><i class="fas fa-film"></i> <?php echo $item['category_name']; ?></span>
                                </div>
                                <div class="movie-card-rating">
                                    <i class="fas fa-star"></i> <?php echo number_format($item['rating'], 1); ?>
                                </div>
                                <small class="text-muted d-block mt-2">Added on <?php echo date('M j, Y', strtotime($item['added_at'])); ?></small>
                            </div>
                            <div class="movie-card-buttons">
                                <span class="movie-card-btn" title="More Info">
                                    <i class="fas fa-info-circle"></i>
                                </span>
                                <span class="movie-card-btn remove-from-watchlist" data-id="<?php echo $item['watchlist_id']; ?>" title="Remove from Watchlist" onclick="event.stopPropagation(); removeFromWatchlist(<?php echo $item['watchlist_id']; ?>); return false;">
                                    <i class="fas fa-trash"></i>
                                </span>
                            </div>
                        </div>
                    </a>
                </div>
                <?php endwhile; ?>
            </div>
        <?php else: ?>
            <div class="alert alert-info text-center">
                <h4>Your watchlist is empty</h4>
                <p>Start adding movies and TV shows to your watchlist!</p>
                <div class="mt-3">
                    <a href="<?php echo SITE_URL; ?>/movies.php" class="btn btn-danger me-2">Browse Movies</a>
                    <a href="<?php echo SITE_URL; ?>/tvshows.php" class="btn btn-outline-danger">Browse TV Shows</a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<script>
    // Function to remove from watchlist
    function removeFromWatchlist(watchlistId) {
        $.ajax({
            url: '<?php echo SITE_URL; ?>/ajax/watchlist.php',
            type: 'POST',
            data: {
                action: 'remove',
                watchlist_id: watchlistId
            },
            success: function(response) {
                var data = JSON.parse(response);
                if (data.success) {
                    $('#watchlist-item-' + watchlistId).fadeOut(300, function() {
                        $(this).remove();

                        // Check if watchlist is empty
                        if ($('.watchlist-item').length === 0) {
                            location.reload();
                        }
                    });
                } else {
                    alert(data.message);
                }
            },
            error: function() {
                alert('An error occurred. Please try again.');
            }
        });
    }
</script>

<?php require_once 'includes/footer.php'; ?>
