<!-- Topbar -->
<nav class="topbar">
    <!-- Sidebar Toggle -->
    <button class="topbar-toggle" id="sidebarToggle">
        <i class="fas fa-bars"></i>
    </button>
    
    <!-- Search Form -->
    <div class="topbar-search d-none d-md-block">
        <form action="search.php" method="GET">
            <div class="input-group">
                <input type="text" class="form-control" name="q" placeholder="সার্চ করুন..." aria-label="Search">
                <button class="btn btn-primary" type="submit">
                    <i class="fas fa-search fa-sm"></i>
                </button>
            </div>
        </form>
    </div>
    
    <!-- Topbar Divider -->
    <div class="topbar-divider d-none d-md-block"></div>
    
    <!-- Topbar Navbar -->
    <ul class="topbar-nav ms-auto">
        <!-- Notifications Dropdown -->
        <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-bell fa-fw"></i>
                <?php
                // Count unread notifications
                $notifications_query = "SELECT COUNT(*) as count FROM notifications WHERE user_id = {$_SESSION['user_id']} AND is_read = FALSE";
                $notifications_result = mysqli_query($conn, $notifications_query);
                $unread_count = 0;
                if ($notifications_result) {
                    $unread_count = mysqli_fetch_assoc($notifications_result)['count'];
                }
                
                if($unread_count > 0):
                ?>
                <span class="badge badge-counter bg-danger"><?php echo $unread_count; ?></span>
                <?php endif; ?>
            </a>
            
            <!-- Notifications Dropdown Menu -->
            <div class="dropdown-menu dropdown-menu-end shadow animated--grow-in" aria-labelledby="notificationsDropdown">
                <h6 class="dropdown-header">
                    নোটিফিকেশন সেন্টার
                </h6>
                <?php
                // Get recent notifications
                $recent_notifications_query = "SELECT * FROM notifications WHERE user_id = {$_SESSION['user_id']} ORDER BY created_at DESC LIMIT 5";
                $recent_notifications_result = mysqli_query($conn, $recent_notifications_query);
                
                if (mysqli_num_rows($recent_notifications_result) > 0):
                    while($notification = mysqli_fetch_assoc($recent_notifications_result)):
                ?>
                <a class="dropdown-item d-flex align-items-center" href="<?php echo $notification['link']; ?>">
                    <div class="me-3">
                        <div class="icon-circle bg-<?php echo $notification['type']; ?>-light">
                            <i class="fas fa-<?php echo getNotificationIcon($notification['type']); ?> text-<?php echo $notification['type']; ?>"></i>
                        </div>
                    </div>
                    <div>
                        <div class="small text-muted"><?php echo timeAgo($notification['created_at']); ?></div>
                        <span class="<?php echo $notification['is_read'] ? '' : 'fw-bold'; ?>"><?php echo $notification['message']; ?></span>
                    </div>
                </a>
                <?php 
                    endwhile;
                else:
                ?>
                <div class="dropdown-item text-center">কোন নোটিফিকেশন নেই</div>
                <?php endif; ?>
                <a class="dropdown-item text-center small text-muted" href="notifications.php">সব নোটিফিকেশন দেখুন</a>
            </div>
        </li>
        
        <!-- Messages Dropdown -->
        <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" id="messagesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-envelope fa-fw"></i>
                <?php
                // Count unread messages
                $messages_query = "SELECT COUNT(*) as count FROM chat_messages WHERE receiver_id = {$_SESSION['user_id']} AND is_read = FALSE";
                $messages_result = mysqli_query($conn, $messages_query);
                $unread_messages = 0;
                if ($messages_result) {
                    $unread_messages = mysqli_fetch_assoc($messages_result)['count'];
                }
                
                if($unread_messages > 0):
                ?>
                <span class="badge badge-counter bg-danger"><?php echo $unread_messages; ?></span>
                <?php endif; ?>
            </a>
            
            <!-- Messages Dropdown Menu -->
            <div class="dropdown-menu dropdown-menu-end shadow animated--grow-in" aria-labelledby="messagesDropdown">
                <h6 class="dropdown-header">
                    মেসেজ সেন্টার
                </h6>
                <?php
                // Get recent messages
                $recent_messages_query = "SELECT cm.*, u.username, u.profile_image 
                                         FROM chat_messages cm
                                         JOIN users u ON cm.sender_id = u.id
                                         WHERE cm.receiver_id = {$_SESSION['user_id']}
                                         ORDER BY cm.created_at DESC
                                         LIMIT 3";
                $recent_messages_result = mysqli_query($conn, $recent_messages_query);
                
                if (mysqli_num_rows($recent_messages_result) > 0):
                    while($message = mysqli_fetch_assoc($recent_messages_result)):
                ?>
                <a class="dropdown-item d-flex align-items-center" href="live_chat.php?session=<?php echo $message['session_id']; ?>">
                    <div class="dropdown-list-image me-3">
                        <img class="rounded-circle" src="<?php echo !empty($message['profile_image']) ? SITE_URL . '/uploads/' . $message['profile_image'] : SITE_URL . '/assets/img/default-avatar.png'; ?>" alt="<?php echo $message['username']; ?>">
                        <div class="status-indicator bg-success"></div>
                    </div>
                    <div class="font-weight-bold">
                        <div class="text-truncate"><?php echo substr($message['message'], 0, 30) . (strlen($message['message']) > 30 ? '...' : ''); ?></div>
                        <div class="small text-muted"><?php echo $message['username']; ?> · <?php echo timeAgo($message['created_at']); ?></div>
                    </div>
                </a>
                <?php 
                    endwhile;
                else:
                ?>
                <div class="dropdown-item text-center">কোন মেসেজ নেই</div>
                <?php endif; ?>
                <a class="dropdown-item text-center small text-muted" href="live_chat.php">সব মেসেজ দেখুন</a>
            </div>
        </li>
        
        <div class="topbar-divider d-none d-md-block"></div>
        
        <!-- User Information -->
        <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <span class="d-none d-lg-inline text-gray-600 small me-2"><?php echo $_SESSION['username']; ?></span>
                <img class="img-profile rounded-circle" src="<?php echo !empty($_SESSION['profile_image']) ? SITE_URL . '/uploads/' . $_SESSION['profile_image'] : SITE_URL . '/assets/img/default-avatar.png'; ?>" width="32" height="32" alt="Profile">
            </a>
            
            <!-- User Dropdown Menu -->
            <div class="dropdown-menu dropdown-menu-end shadow animated--grow-in" aria-labelledby="userDropdown">
                <a class="dropdown-item" href="profile.php">
                    <i class="fas fa-user fa-sm fa-fw me-2 text-gray-400"></i>
                    প্রোফাইল
                </a>
                <a class="dropdown-item" href="site_settings.php">
                    <i class="fas fa-cogs fa-sm fa-fw me-2 text-gray-400"></i>
                    সেটিংস
                </a>
                <a class="dropdown-item" href="activity_logs.php">
                    <i class="fas fa-list fa-sm fa-fw me-2 text-gray-400"></i>
                    অ্যাক্টিভিটি লগ
                </a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="<?php echo SITE_URL; ?>/logout.php">
                    <i class="fas fa-sign-out-alt fa-sm fa-fw me-2 text-gray-400"></i>
                    লগআউট
                </a>
            </div>
        </li>
    </ul>
</nav>

<?php
// Helper function to get notification icon
function getNotificationIcon($type) {
    switch ($type) {
        case 'primary':
            return 'info-circle';
        case 'success':
            return 'check-circle';
        case 'warning':
            return 'exclamation-triangle';
        case 'danger':
            return 'times-circle';
        default:
            return 'bell';
    }
}

// Helper function for time ago
function timeAgo($timestamp) {
    $time_ago = strtotime($timestamp);
    $current_time = time();
    $time_difference = $current_time - $time_ago;
    $seconds = $time_difference;
    
    $minutes = round($seconds / 60);
    $hours = round($seconds / 3600);
    $days = round($seconds / 86400);
    $weeks = round($seconds / 604800);
    $months = round($seconds / 2629440);
    $years = round($seconds / 31553280);
    
    if ($seconds <= 60) {
        return "এইমাত্র";
    } else if ($minutes <= 60) {
        return "$minutes মিনিট আগে";
    } else if ($hours <= 24) {
        return "$hours ঘন্টা আগে";
    } else if ($days <= 7) {
        return "$days দিন আগে";
    } else if ($weeks <= 4.3) {
        return "$weeks সপ্তাহ আগে";
    } else if ($months <= 12) {
        return "$months মাস আগে";
    } else {
        return "$years বছর আগে";
    }
}
?>
