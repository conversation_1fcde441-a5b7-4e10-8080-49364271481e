/* Header/Navbar */
.navbar.bg-gradient {
  background: linear-gradient(90deg, #1a1a2e 0%, #16213e 100%) !important;
  box-shadow: 0 4px 24px rgba(26,26,46,0.18);
}
.navbar .navbar-brand {
  font-size: 2rem;
  letter-spacing: 1px;
  color: #fff !important;
  text-shadow: 0 2px 8px rgba(0,0,0,0.18);
}
.navbar .nav-link {
  color: #e0e0e0 !important;
  font-weight: 500;
  font-size: 1.1rem;
  margin-right: 8px;
  transition: color 0.2s;
}
.navbar .nav-link.active, .navbar .nav-link:hover {
  color: #ff4b2b !important;
}
.navbar .btn-outline-light {
  border-color: #fff;
  color: #fff;
}
.navbar .btn-outline-light:hover {
  background: #fff;
  color: #1a1a2e;
}

/* Swiper Main Slider */
.main-slider .swiper {
  border-radius: 1.5rem;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
}
.main-slider .slide-image {
  min-height: 340px;
  background-size: cover;
  background-position: center;
  border-radius: 1.5rem;
  position: relative;
}
.main-slider .slide-overlay {
  background: rgba(0,0,0,0.25);
  border-radius: 1.5rem;
  color: #fff;
}
.slide-title {
  font-size: 2.3rem;
  font-weight: 700;
  text-shadow: 0 2px 8px rgba(0,0,0,0.18);
}
.text-shadow-lg {
  text-shadow: 0 4px 16px rgba(0,0,0,0.25);
}
.text-shadow-sm {
  text-shadow: 0 2px 8px rgba(0,0,0,0.12);
}
.slide-meta span {
  font-size: 1.1rem;
  margin-right: 12px;
}
.slide-buttons .btn {
  min-width: 150px;
  font-size: 1.15rem;
  border-radius: 2rem;
}
.slide-buttons .btn.shadow {
  box-shadow: 0 4px 16px rgba(255,75,43,0.12);
}

/* Ad Container */
.ad-container {
  background: #fff;
  border-radius: 1.2rem;
  box-shadow: 0 2px 12px rgba(26,26,46,0.10);
  padding: 1.2rem 0.5rem;
  margin-bottom: 1.5rem;
  text-align: center;
}
body.theme-dark .ad-container {
  background: #23272b;
  box-shadow: 0 2px 12px rgba(24,26,27,0.18);
}

/* Card Design */
.movie-card.card {
  border-radius: 1.2rem;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  min-height: 340px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 16px rgba(26,26,46,0.10);
}
.movie-card.card:hover {
  transform: translateY(-6px) scale(1.03);
  box-shadow: 0 8px 32px rgba(255,75,43,0.12);
}
.movie-card .card-img-top {
  height: 220px;
  object-fit: cover;
}
.card-title {
  font-size: 1.1rem;
  font-weight: 600;
}
.section-title {
  font-size: 1.5rem;
  font-weight: 700;
}
.badge.bg-warning {
  font-size: 1rem;
  font-weight: 600;
}

@media (max-width: 991px) {
  .navbar .navbar-collapse {
    display: none !important;
  }
}
@media (max-width: 767px) {
  .main-slider .slide-title {
    font-size: 1.2rem;
  }
  .main-slider .slide-meta span {
    font-size: 0.9rem;
  }
  .main-slider .slide-buttons .btn {
    font-size: 0.95rem;
    min-width: 100px;
  }
  .movie-card .card-img-top {
    height: 160px;
  }
  .section-title {
    font-size: 1.1rem;
  }
}
body.theme-dark .main-slider .slide-overlay {
  background: rgba(24,26,27,0.65);
  color: #f1f1f1;
}
body.theme-dark .navbar.bg-gradient {
  background: linear-gradient(90deg, #23272b 0%, #181a1b 100%) !important;
}
body.theme-dark .navbar .nav-link {
  color: #f1f1f1 !important;
}
body.theme-dark .navbar .nav-link.active, body.theme-dark .navbar .nav-link:hover {
  color: #ff4b2b !important;
}
body.theme-dark .navbar .btn-outline-light {
  color: #fff;
  border-color: #fff;
}
body.theme-dark .navbar .btn-outline-light:hover {
  background: #fff;
  color: #23272b;
} 