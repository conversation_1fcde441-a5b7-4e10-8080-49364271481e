<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Set some test session data for debugging
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';
$_SESSION['user_role'] = 'admin';
$_SESSION['is_premium'] = 1;

// Set page title
$page_title = 'ড্যাশবোর্ড - ডিবাগ মোড';
$current_page = 'index.php';

// Mock statistics for testing
$stats = [
    'movies' => 150,
    'premium_movies' => 50,
    'tvshows' => 75,
    'premium_tvshows' => 25,
    'episodes' => 1200,
    'premium_episodes' => 400,
    'users' => 500,
    'premium_users' => 100,
    'pending_payments' => 15,
    'completed_payments' => 85,
    'payments' => 100,
    'categories' => 20,
    'reviews' => 300,
    'revenue' => 15000
];

// Include header
include 'includes/header.php';
?>

<!-- Include Sidebar -->
<?php include 'includes/sidebar.php'; ?>

<!-- Main Content -->
<div class="main-content">
    <!-- Debug Info -->
    <div class="alert alert-info">
        <h5><i class="fas fa-bug me-2"></i>ডিবাগ মোড</h5>
        <p>এই পেজটি ডেটাবেস কানেকশন ছাড়াই চালানো হচ্ছে। সব ডেটা মক করা।</p>
    </div>

    <!-- Page Header -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-title">
                    <i class="fas fa-tachometer-alt me-3"></i>ড্যাশবোর্ড
                </h1>
                <p class="page-subtitle text-muted">CinePix Admin Panel এ স্বাগতম</p>
            </div>
            <div class="col-auto">
                <div class="page-actions">
                    <button class="btn btn-outline-primary me-2" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-2"></i>রিফ্রেশ
                    </button>
                    <div class="dropdown">
                        <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-plus me-2"></i>নতুন যোগ করুন
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-film me-2"></i>নতুন মুভি</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-tv me-2"></i>নতুন টিভি শো</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user-plus me-2"></i>নতুন ইউজার</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row stats-grid mb-4">
        <!-- Movies Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card bg-gradient-primary">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-film"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number"><?php echo number_format($stats['movies']); ?></h3>
                                <p class="stat-label">মোট মুভি</p>
                                <small class="stat-sublabel">
                                    <i class="fas fa-crown me-1"></i><?php echo $stats['premium_movies']; ?> প্রিমিয়াম
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- TV Shows Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card bg-gradient-success">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-tv"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number"><?php echo number_format($stats['tvshows']); ?></h3>
                                <p class="stat-label">টিভি শো</p>
                                <small class="stat-sublabel">
                                    <i class="fas fa-crown me-1"></i><?php echo $stats['premium_tvshows']; ?> প্রিমিয়াম
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card bg-gradient-info">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number"><?php echo number_format($stats['users']); ?></h3>
                                <p class="stat-label">মোট ইউজার</p>
                                <small class="stat-sublabel">
                                    <i class="fas fa-crown me-1"></i><?php echo $stats['premium_users']; ?> প্রিমিয়াম
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card bg-gradient-warning">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number">৳<?php echo number_format($stats['revenue']); ?></h3>
                                <p class="stat-label">মোট আয়</p>
                                <small class="stat-sublabel">
                                    <i class="fas fa-clock me-1"></i><?php echo $stats['pending_payments']; ?> পেন্ডিং
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Analytics -->
    <div class="row mb-4">
        <!-- Revenue Chart -->
        <div class="col-xl-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>রেভিনিউ ট্রেন্ড
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="revenueChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="col-xl-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tachometer-alt me-2"></i>দ্রুত পরিসংখ্যান
                    </h5>
                </div>
                <div class="card-body">
                    <div class="quick-stat-item">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>মোট এপিসোড</span>
                            <span class="badge bg-primary"><?php echo number_format($stats['episodes']); ?></span>
                        </div>
                        <div class="progress mb-3" style="height: 6px;">
                            <div class="progress-bar bg-primary" style="width: 75%"></div>
                        </div>
                    </div>
                    
                    <div class="quick-stat-item">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>ক্যাটাগরি</span>
                            <span class="badge bg-success"><?php echo number_format($stats['categories']); ?></span>
                        </div>
                        <div class="progress mb-3" style="height: 6px;">
                            <div class="progress-bar bg-success" style="width: 60%"></div>
                        </div>
                    </div>
                    
                    <div class="quick-stat-item">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>রিভিউ</span>
                            <span class="badge bg-info"><?php echo number_format($stats['reviews']); ?></span>
                        </div>
                        <div class="progress mb-3" style="height: 6px;">
                            <div class="progress-bar bg-info" style="width: 45%"></div>
                        </div>
                    </div>
                    
                    <div class="quick-stat-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>পেমেন্ট</span>
                            <span class="badge bg-warning"><?php echo number_format($stats['payments']); ?></span>
                        </div>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-warning" style="width: 80%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity and Quick Actions -->
    <div class="row">
        <!-- Recent Activity -->
        <div class="col-xl-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>সাম্প্রতিক কার্যকলাপ
                    </h5>
                </div>
                <div class="card-body">
                    <div class="activity-item">
                        <div class="activity-icon bg-primary">
                            <i class="fas fa-film"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">নতুন মুভি যোগ করা হয়েছে</div>
                            <div class="activity-description">Avengers: Endgame মুভি সফলভাবে যোগ করা হয়েছে</div>
                            <div class="activity-time">৫ মিনিট আগে</div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon bg-success">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">নতুন ইউজার রেজিস্ট্রেশন</div>
                            <div class="activity-description">john_doe নামে নতুন ইউজার রেজিস্ট্রেশন করেছে</div>
                            <div class="activity-time">১০ মিনিট আগে</div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon bg-warning">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">নতুন পেমেন্ট</div>
                            <div class="activity-description">৳৯০ প্রিমিয়াম প্ল্যানের জন্য পেমেন্ট পেন্ডিং</div>
                            <div class="activity-time">১৫ মিনিট আগে</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-xl-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>দ্রুত অ্যাকশন
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="#" class="btn btn-outline-primary">
                            <i class="fas fa-film me-2"></i>নতুন মুভি যোগ করুন
                        </a>
                        <a href="#" class="btn btn-outline-success">
                            <i class="fas fa-tv me-2"></i>নতুন টিভি শো যোগ করুন
                        </a>
                        <a href="#" class="btn btn-outline-warning">
                            <i class="fas fa-crown me-2"></i>প্রিমিয়াম ম্যানেজ করুন
                        </a>
                        <a href="#" class="btn btn-outline-info">
                            <i class="fas fa-credit-card me-2"></i>পেমেন্ট দেখুন
                        </a>
                        <a href="#" class="btn btn-outline-secondary">
                            <i class="fas fa-database me-2"></i>ব্যাকআপ নিন
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Page-specific scripts
$page_scripts = '
<script>
// Initialize dashboard charts
document.addEventListener("DOMContentLoaded", function() {
    // Revenue Chart
    const ctx = document.getElementById("revenueChart");
    if (ctx) {
        new Chart(ctx, {
            type: "line",
            data: {
                labels: ["জানুয়ারি", "ফেব্রুয়ারি", "মার্চ", "এপ্রিল", "মে", "জুন"],
                datasets: [{
                    label: "রেভিনিউ (৳)",
                    data: [1200, 1900, 800, 1500, 2000, 2400],
                    borderColor: "#e50914",
                    backgroundColor: "rgba(229, 9, 20, 0.1)",
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: "#ffffff"
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: "#b3b3b3",
                            callback: function(value) {
                                return "৳" + value;
                            }
                        },
                        grid: {
                            color: "#333333"
                        }
                    },
                    x: {
                        ticks: {
                            color: "#b3b3b3"
                        },
                        grid: {
                            color: "#333333"
                        }
                    }
                }
            }
        });
    }
});
</script>
';

// Include footer
include 'includes/footer.php';
?>
