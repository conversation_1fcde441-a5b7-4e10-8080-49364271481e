<?php
// Include direct config file
require_once '../direct_config.php';

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed',
        'data' => null
    ]);
    exit;
}

// Get request body
$request_body = file_get_contents('php://input');
$data = json_decode($request_body, true);

// Validate required fields
if (empty($data['username']) || empty($data['email']) || empty($data['password']) || empty($data['name'])) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Username, email, password, and name are required',
        'data' => null
    ]);
    exit;
}

$username = $data['username'];
$email = $data['email'];
$password = $data['password'];
$name = $data['name'];

// Validate username (alphanumeric and underscore only)
if (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Username can only contain letters, numbers, and underscores',
        'data' => null
    ]);
    exit;
}

// Validate email
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Invalid email format',
        'data' => null
    ]);
    exit;
}

// Validate password (at least 6 characters)
if (strlen($password) < 6) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Password must be at least 6 characters',
        'data' => null
    ]);
    exit;
}

// Check if username already exists
$check_username_query = "SELECT id FROM users WHERE username = ?";
$stmt = mysqli_prepare($conn, $check_username_query);
mysqli_stmt_bind_param($stmt, 's', $username);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) > 0) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Username already exists',
        'data' => null
    ]);
    exit;
}

// Check if email already exists
$check_email_query = "SELECT id FROM users WHERE email = ?";
$stmt = mysqli_prepare($conn, $check_email_query);
mysqli_stmt_bind_param($stmt, 's', $email);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) > 0) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Email already exists',
        'data' => null
    ]);
    exit;
}

// Hash password
$hashed_password = password_hash($password, PASSWORD_DEFAULT);

// Insert new user
$insert_query = "INSERT INTO users (username, email, password, name, role, is_premium, created_at) 
                VALUES (?, ?, ?, ?, 'user', 0, NOW())";

$stmt = mysqli_prepare($conn, $insert_query);
mysqli_stmt_bind_param($stmt, 'ssss', $username, $email, $hashed_password, $name);

if (!mysqli_stmt_execute($stmt)) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to register user: ' . mysqli_error($conn),
        'data' => null
    ]);
    exit;
}

$user_id = mysqli_insert_id($conn);

// Generate JWT token
$token = generate_jwt($user_id, $username, 'user');

// Return user data and token
http_response_code(201);
echo json_encode([
    'success' => true,
    'message' => 'Registration successful',
    'data' => [
        'user' => [
            'id' => $user_id,
            'username' => $username,
            'email' => $email,
            'name' => $name,
            'role' => 'user',
            'profile_image' => null,
            'is_premium' => false,
            'premium_expires' => null,
            'created_at' => date('Y-m-d H:i:s')
        ],
        'token' => $token
    ]
]);
?>
