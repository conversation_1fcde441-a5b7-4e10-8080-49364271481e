<?php
require_once 'includes/config.php';

echo "<h2>শেয়ার লিংক টেস্ট - এখনই</h2>";

// Step 1: Create a test shared link
echo "<h3>ধাপ ১: টেস্ট শেয়ার লিংক তৈরি</h3>";

// Get first movie
$movie_query = mysqli_query($conn, "SELECT id, title FROM movies LIMIT 1");
if (mysqli_num_rows($movie_query) == 0) {
    echo "❌ কোনো মুভি নেই। প্রথমে একটি মুভি যোগ করুন।";
    exit;
}

$movie = mysqli_fetch_assoc($movie_query);
echo "✅ মুভি পাওয়া গেছে: {$movie['title']}<br>";

// Create shared link
$link_token = 'test_' . time() . '_' . rand(1000, 9999);
$content_type = 'movie';
$content_id = $movie['id'];
$title = "টেস্ট - {$movie['title']}";
$description = "এই একটি টেস্ট শেয়ার লিংক";
$access_limit = 10;
$expires_at = date('Y-m-d H:i:s', strtotime('+7 days'));
$allow_download = 1;
$allow_streaming = 1;
$is_active = 1;

$insert_query = "INSERT INTO shared_links (link_token, content_type, content_id, created_by, title, description, access_limit, expires_at, allow_download, allow_streaming, password, is_active) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)";

$stmt = mysqli_prepare($conn, $insert_query);
mysqli_stmt_bind_param($stmt, 'ssiissisiis', $link_token, $content_type, $content_id, 1, $title, $description, $access_limit, $expires_at, $allow_download, $allow_streaming, $is_active);

if (mysqli_stmt_execute($stmt)) {
    echo "✅ শেয়ার লিংক তৈরি হয়েছে<br>";
    echo "<strong>টোকেন:</strong> $link_token<br>";
} else {
    echo "❌ শেয়ার লিংক তৈরি করতে সমস্যা হয়েছে: " . mysqli_error($conn) . "<br>";
    exit;
}

// Step 2: Test the query
echo "<h3>ধাপ ২: কুয়েরি টেস্ট</h3>";

$test_query = "SELECT sl.*, 
                      CASE 
                          WHEN sl.content_type = 'movie' THEN m.title
                          WHEN sl.content_type = 'tvshow' THEN t.title
                      END as content_title,
                      CASE 
                          WHEN sl.content_type = 'movie' THEN m.description
                          WHEN sl.content_type = 'tvshow' THEN t.description
                      END as content_description,
                      CASE 
                          WHEN sl.content_type = 'movie' THEN m.poster
                          WHEN sl.content_type = 'tvshow' THEN t.poster
                      END as content_poster,
                      CASE 
                          WHEN sl.content_type = 'movie' THEN m.banner
                          WHEN sl.content_type = 'tvshow' THEN t.banner
                      END as content_banner,
                      CASE 
                          WHEN sl.content_type = 'movie' THEN m.rating
                          WHEN sl.content_type = 'tvshow' THEN t.rating
                      END as content_rating,
                      CASE 
                          WHEN sl.content_type = 'movie' THEN m.release_year
                          WHEN sl.content_type = 'tvshow' THEN t.release_year
                      END as content_year
               FROM shared_links sl
               LEFT JOIN movies m ON sl.content_type = 'movie' AND sl.content_id = m.id
               LEFT JOIN tvshows t ON sl.content_type = 'tvshow' AND sl.content_id = t.id
               WHERE sl.link_token = '$link_token' AND sl.is_active = 1";

$result = mysqli_query($conn, $test_query);

if (!$result) {
    echo "❌ কুয়েরি ফেইল: " . mysqli_error($conn) . "<br>";
} else {
    $num_rows = mysqli_num_rows($result);
    echo "✅ কুয়েরি সফল। ফলাফল: $num_rows সারি<br>";
    
    if ($num_rows > 0) {
        $shared_link = mysqli_fetch_assoc($result);
        echo "<div style='background: #e8f5e8; padding: 15px; margin: 15px 0; border: 2px solid #4caf50; border-radius: 5px;'>";
        echo "<h4>✅ শেয়ার লিংক তথ্য:</h4>";
        echo "<strong>ID:</strong> {$shared_link['id']}<br>";
        echo "<strong>টাইটেল:</strong> {$shared_link['title']}<br>";
        echo "<strong>কন্টেন্ট টাইপ:</strong> {$shared_link['content_type']}<br>";
        echo "<strong>কন্টেন্ট ID:</strong> {$shared_link['content_id']}<br>";
        echo "<strong>কন্টেন্ট টাইটেল:</strong> {$shared_link['content_title']}<br>";
        echo "<strong>এক্সেস কাউন্ট:</strong> {$shared_link['access_count']}<br>";
        echo "<strong>এক্সেস লিমিট:</strong> {$shared_link['access_limit']}<br>";
        echo "<strong>মেয়াদ:</strong> {$shared_link['expires_at']}<br>";
        echo "<strong>সক্রিয়:</strong> " . ($shared_link['is_active'] ? 'হ্যাঁ' : 'না') . "<br>";
        echo "</div>";
    }
}

// Step 3: Create share URLs
echo "<h3>ধাপ ৩: শেয়ার লিংক</h3>";

$share_url = SITE_URL . '/share.php?token=' . $link_token;
$debug_url = SITE_URL . '/share.php?token=' . $link_token . '&debug=1';

echo "<div style='background: #f0f8ff; padding: 15px; margin: 15px 0; border: 2px solid #007bff; border-radius: 5px;'>";
echo "<h4>🔗 শেয়ার লিংক:</h4>";
echo "<p><strong>সাধারণ লিংক:</strong><br>";
echo "<a href='$share_url' target='_blank' style='color: #007bff; font-size: 16px; text-decoration: underline;'>$share_url</a></p>";

echo "<p><strong>ডিবাগ লিংক:</strong><br>";
echo "<a href='$debug_url' target='_blank' style='color: #28a745; font-size: 16px; text-decoration: underline;'>$debug_url</a></p>";
echo "</div>";

// Step 4: Instructions
echo "<h3>ধাপ ৪: পরবর্তী ধাপ</h3>";
echo "<div style='background: #fff3cd; padding: 15px; margin: 15px 0; border: 2px solid #ffc107; border-radius: 5px;'>";
echo "<h4>📋 নির্দেশনা:</h4>";
echo "<ol>";
echo "<li><strong>প্রথমে ডিবাগ লিংকে ক্লিক করুন</strong> - এটি বিস্তারিত তথ্য দেখাবে</li>";
echo "<li>যদি ডিবাগ লিংক কাজ করে তবে সাধারণ লিংকে ক্লিক করুন</li>";
echo "<li>যদি এখনও 404 আসে তবে নিচের তথ্য চেক করুন:</li>";
echo "</ol>";

echo "<h4>🔍 সমস্যা সমাধান:</h4>";
echo "<ul>";
echo "<li><a href='check_share_links.php' style='color: #007bff;'>সব শেয়ার লিংক দেখুন</a></li>";
echo "<li><a href='test_share_debug.php' style='color: #007bff;'>বিস্তারিত ডিবাগ টেস্ট</a></li>";
echo "<li><a href='admin/shared_links.php' style='color: #007bff;'>অ্যাডমিন প্যানেলে যান</a></li>";
echo "</ul>";
echo "</div>";

// Clean up old test links
$cleanup_query = "DELETE FROM shared_links WHERE link_token LIKE 'test_%' AND created_at < DATE_SUB(NOW(), INTERVAL 1 HOUR)";
mysqli_query($conn, $cleanup_query);
?> 