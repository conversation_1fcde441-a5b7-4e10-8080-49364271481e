-- Drop existing constraints
ALTER TABLE download_links DROP INDEX unique_download_link;
ALTER TABLE streaming_links DROP INDEX unique_stream_link;
ALTER TABLE episode_links DROP INDEX unique_episode_link;

-- Add new constraints that allow multiple links of the same quality
ALTER TABLE download_links ADD CONSTRAINT unique_download_content UNIQUE(content_type, content_id, id);
ALTER TABLE streaming_links ADD CONSTRAINT unique_stream_content UNIQUE(content_type, content_id, id);
ALTER TABLE episode_links ADD CONSTRAINT unique_episode_content UNIQUE(episode_id, id);

-- Add server_name and file_size columns to download_links if they don't exist
ALTER TABLE download_links ADD COLUMN IF NOT EXISTS server_name VARCHAR(50) AFTER link_type;
ALTER TABLE download_links ADD COLUMN IF NOT EXISTS file_size VARCHAR(50) AFTER server_name;
