<?php
// Database connection
$host = 'localhost';
$username = 'tipsbdxy_4525';
$password = '@mdsrabon13';
$database = 'tipsbdxy_4525';

$conn = mysqli_connect($host, $username, $password, $database);

if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// Check if premium_plans table exists
$check_table_query = "SHOW TABLES LIKE 'premium_plans'";
$check_table_result = mysqli_query($conn, $check_table_query);

if (mysqli_num_rows($check_table_result) == 0) {
    echo "The premium_plans table does not exist!";
    exit;
}

// Check if description column already exists
$check_column_query = "SHOW COLUMNS FROM premium_plans LIKE 'description'";
$check_column_result = mysqli_query($conn, $check_column_query);

if (mysqli_num_rows($check_column_result) > 0) {
    echo "<h2>The 'description' column already exists in the premium_plans table.</h2>";
} else {
    // Add description column
    $add_column_query = "ALTER TABLE premium_plans ADD COLUMN description TEXT AFTER name";
    
    if (mysqli_query($conn, $add_column_query)) {
        echo "<h2>Successfully added 'description' column to premium_plans table!</h2>";
        
        // Update existing plans with default descriptions
        $update_query = "UPDATE premium_plans SET description = CONCAT(name, ' plan with ', duration, ' days validity')";
        
        if (mysqli_query($conn, $update_query)) {
            echo "<p>Updated existing plans with default descriptions.</p>";
        } else {
            echo "<p>Error updating existing plans: " . mysqli_error($conn) . "</p>";
        }
    } else {
        echo "<h2>Error adding 'description' column: " . mysqli_error($conn) . "</h2>";
    }
}

// Show table structure
echo "<h3>Premium Plans Table Structure:</h3>";
$structure_query = "DESCRIBE premium_plans";
$structure_result = mysqli_query($conn, $structure_query);

if ($structure_result) {
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = mysqli_fetch_assoc($structure_result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
}

// Show existing plans
echo "<h3>Existing Premium Plans:</h3>";
$plans_query = "SELECT * FROM premium_plans ORDER BY price";
$plans_result = mysqli_query($conn, $plans_query);

if (mysqli_num_rows($plans_result) > 0) {
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>ID</th><th>Name</th><th>Description</th><th>Price</th><th>Duration</th><th>Features</th></tr>";
    
    while ($plan = mysqli_fetch_assoc($plans_result)) {
        echo "<tr>";
        echo "<td>" . $plan['id'] . "</td>";
        echo "<td>" . $plan['name'] . "</td>";
        echo "<td>" . ($plan['description'] ?? 'N/A') . "</td>";
        echo "<td>৳" . $plan['price'] . "</td>";
        echo "<td>" . $plan['duration'] . " days</td>";
        echo "<td><pre>" . $plan['features'] . "</pre></td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>No premium plans found.</p>";
}

echo "<p><a href='admin/premium_plans.php'>Go to Premium Plans Admin Page</a></p>";

mysqli_close($conn);
?>
