class Review {
  final int id;
  final int userId;
  final String username;
  final String userImage;
  final String contentType; // 'movie' or 'tvshow'
  final int contentId;
  final int rating;
  final String comment;
  final String createdAt;

  Review({
    required this.id,
    required this.userId,
    required this.username,
    required this.userImage,
    required this.contentType,
    required this.contentId,
    required this.rating,
    required this.comment,
    required this.createdAt,
  });

  factory Review.fromJson(Map<String, dynamic> json) {
    return Review(
      id: json['id'] ?? 0,
      userId: json['user_id'] ?? 0,
      username: json['username'] ?? 'Unknown User',
      userImage: json['user_image'] ?? '',
      contentType: json['content_type'] ?? 'movie',
      contentId: json['content_id'] ?? 0,
      rating: json['rating'] ?? 0,
      comment: json['comment'] ?? '',
      createdAt: json['created_at'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'username': username,
      'user_image': userImage,
      'content_type': contentType,
      'content_id': contentId,
      'rating': rating,
      'comment': comment,
      'created_at': createdAt,
    };
  }
}
