<?php
// Include database connection
require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    echo "You must be logged in as an admin to view this page.";
    exit;
}

// Check if the episode_links table exists
$check_table_query = "SHOW TABLES LIKE 'episode_links'";
$table_result = mysqli_query($conn, $check_table_query);

if (mysqli_num_rows($table_result) > 0) {
    echo "Table 'episode_links' exists.<br>";
    
    // Get table structure
    $describe_query = "DESCRIBE episode_links";
    $describe_result = mysqli_query($conn, $describe_query);
    
    echo "<h3>Table Structure:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = mysqli_fetch_assoc($describe_result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Add file_size column if it doesn't exist
    $add_column_query = "ALTER TABLE episode_links ADD COLUMN IF NOT EXISTS file_size VARCHAR(50) NULL AFTER server_name";
    if (mysqli_query($conn, $add_column_query)) {
        echo "<p>Added 'file_size' column if it didn't exist.</p>";
    } else {
        echo "<p>Error adding 'file_size' column: " . mysqli_error($conn) . "</p>";
    }
    
    // Show updated table structure
    $describe_query = "DESCRIBE episode_links";
    $describe_result = mysqli_query($conn, $describe_query);
    
    echo "<h3>Updated Table Structure:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = mysqli_fetch_assoc($describe_result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "Table 'episode_links' does not exist.";
    
    // Create the table
    $create_table_query = "CREATE TABLE episode_links (
        id INT(11) NOT NULL AUTO_INCREMENT,
        episode_id INT(11) NOT NULL,
        link_type ENUM('download', 'stream') NOT NULL,
        quality VARCHAR(20) NOT NULL,
        server_name VARCHAR(100) NULL,
        file_size VARCHAR(50) NULL,
        link_url TEXT NOT NULL,
        is_premium TINYINT(1) NOT NULL DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        INDEX (episode_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    if (mysqli_query($conn, $create_table_query)) {
        echo "<p>Table 'episode_links' created successfully.</p>";
    } else {
        echo "<p>Error creating table: " . mysqli_error($conn) . "</p>";
    }
}
?>
