class Movie {
  final int id;
  final String title;
  final String description;
  final int releaseYear;
  final int duration;
  final String poster;
  final String banner;
  final String trailerUrl;
  final double rating;
  final int categoryId;
  final String categoryName;
  final bool featured;
  final bool premiumOnly;
  final String status;

  Movie({
    required this.id,
    required this.title,
    required this.description,
    required this.releaseYear,
    required this.duration,
    required this.poster,
    required this.banner,
    required this.trailerUrl,
    required this.rating,
    required this.categoryId,
    required this.categoryName,
    required this.featured,
    required this.premiumOnly,
    required this.status,
  });

  factory Movie.fromJson(Map<String, dynamic> json) {
    return Movie(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      releaseYear: json['release_year'] ?? 0,
      duration: json['duration'] ?? 0,
      poster: json['poster'] ?? '',
      banner: json['banner'] ?? '',
      trailerUrl: json['trailer_url'] ?? '',
      rating: (json['rating'] ?? 0.0).toDouble(),
      categoryId: json['category_id'] ?? 0,
      categoryName: json['category_name'] ?? '',
      featured: json['featured'] ?? false,
      premiumOnly: json['premium_only'] ?? false,
      status: json['status'] ?? 'active',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'release_year': releaseYear,
      'duration': duration,
      'poster': poster,
      'banner': banner,
      'trailer_url': trailerUrl,
      'rating': rating,
      'category_id': categoryId,
      'category_name': categoryName,
      'featured': featured,
      'premium_only': premiumOnly,
      'status': status,
    };
  }
}
