<?php
require_once 'includes/header.php';

// Add custom CSS for payment page
$custom_css = <<<EOT
<style>
    /* Payment Method Cards */
    .payment-method-card {
        border: 2px solid #343a40;
        border-radius: 10px;
        padding: 15px;
        text-align: center;
        transition: all 0.3s ease;
        height: 100%;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        background-color: #1a1a1a;
    }

    /* Payment method specific styles */
    #nagad:checked + .payment-label .payment-method-card {
        border-color: #e3101e;
        background-color: rgba(227, 16, 30, 0.1);
    }

    #rocket:checked + .payment-label .payment-method-card {
        border-color: #0d6efd;
        background-color: rgba(13, 110, 253, 0.1);
    }

    #manual:checked + .payment-label .payment-method-card {
        border-color: #198754;
        background-color: rgba(25, 135, 84, 0.1);
    }

    .payment-method-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.2);
    }

    .payment-label {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        width: 100%;
    }

    .payment-logo {
        max-height: 50px;
        margin-bottom: 10px;
    }

    .form-check-input:checked + .payment-label .payment-logo {
        transform: scale(1.1);
    }

    .form-check-input:checked + .payment-label {
        font-weight: bold;
    }

    /* This is replaced by payment method specific styles above */

    /* bKash specific */
    #bkash:checked ~ .payment-method-card {
        border-color: #e2136e;
        background-color: rgba(226, 19, 110, 0.1);
    }

    /* Nagad specific */
    #nagad:checked ~ .payment-method-card {
        border-color: #f60;
        background-color: rgba(255, 102, 0, 0.1);
    }

    /* Rocket specific */
    #rocket:checked ~ .payment-method-card {
        border-color: #8c3ac9;
        background-color: rgba(140, 58, 201, 0.1);
    }

    /* Manual specific */
    #manual:checked ~ .payment-method-card {
        border-color: #28a745;
        background-color: rgba(40, 167, 69, 0.1);
    }

    /* Hide default radio button */
    .payment-method-card .form-check-input {
        position: absolute;
        opacity: 0;
    }

    /* Transaction ID inputs */
    .transaction-input {
        transition: all 0.3s ease;
        font-size: 1.1rem;
        letter-spacing: 0.5px;
    }

    .transaction-input:focus {
        transform: scale(1.02);
        box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
    }

    /* Pricing Table Styles */
    .pricing-table {
        margin-bottom: 2rem;
    }

    .pricing-card {
        border-radius: 15px;
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
        position: relative;
    }

    .pricing-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
    }

    .pricing-header {
        padding: 2rem 1.5rem;
        text-align: center;
    }

    .pricing-price {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }

    .pricing-period {
        font-size: 1rem;
        opacity: 0.7;
    }

    .pricing-features {
        padding: 1.5rem;
    }

    .pricing-features ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .pricing-features li {
        padding: 0.75rem 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .pricing-features li:last-child {
        border-bottom: none;
    }

    .pricing-features i {
        margin-right: 0.5rem;
    }

    .pricing-footer {
        padding: 1.5rem;
        text-align: center;
    }

    .pricing-popular {
        position: absolute;
        top: 0;
        right: 0;
        background: #ff5e14;
        color: white;
        padding: 0.5rem 1rem;
        transform: rotate(45deg) translateX(3.5rem) translateY(-1rem);
        font-size: 0.8rem;
        font-weight: bold;
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    }

    /* Responsive Pricing Table */
    @media (max-width: 992px) {
        .pricing-card {
            margin-bottom: 1.5rem;
        }
    }

    @media (max-width: 576px) {
        .pricing-price {
            font-size: 2rem;
        }

        .pricing-features li {
            font-size: 0.9rem;
        }
    }

    /* Payment Box Styling */
    .payment-box {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        margin-bottom: 30px;
        background: #1a1a1a;
    }

    .payment-box .card-header {
        padding: 20px;
        border-bottom: 2px solid rgba(220, 53, 69, 0.5);
    }

    .payment-box .card-body {
        padding: 30px;
    }

    .payment-instructions {
        border-radius: 10px;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .payment-instructions .alert {
        border-width: 2px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .payment-instructions h5 {
        font-size: 1.3rem;
        margin-bottom: 15px;
    }

    .payment-instructions ol {
        padding-left: 1.5rem;
    }

    .payment-instructions li {
        margin-bottom: 10px;
        line-height: 1.6;
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .payment-box .card-body {
            padding: 20px 15px;
        }

        .payment-method-card {
            padding: 10px;
            margin-bottom: 10px;
        }

        .payment-logo {
            max-height: 40px;
        }

        .payment-instructions h5 {
            font-size: 1.1rem;
        }

        .payment-instructions li {
            font-size: 0.9rem;
        }

        .form-label.fs-5 {
            font-size: 1rem !important;
        }
    }

    /* Animated elements */
    .payment-box .btn-lg {
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .payment-box .btn-lg:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(220, 53, 69, 0.3);
    }

    .payment-box .btn-lg:active {
        transform: translateY(0);
    }

    .payment-box .btn-lg::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 5px;
        height: 5px;
        background: rgba(255, 255, 255, 0.5);
        opacity: 0;
        border-radius: 100%;
        transform: scale(1, 1) translate(-50%);
        transform-origin: 50% 50%;
    }

    .payment-box .btn-lg:focus:not(:active)::after {
        animation: ripple 1s ease-out;
    }

    @keyframes ripple {
        0% {
            transform: scale(0, 0);
            opacity: 0.5;
        }
        100% {
            transform: scale(20, 20);
            opacity: 0;
        }
    }
</style>
EOT;

// Check if user is logged in
if (!isLoggedIn()) {
    $_SESSION['redirect_to'] = SITE_URL . '/premium.php';
    redirect(SITE_URL . '/login.php');
}

// Check if plan is provided
if (!isset($_GET['plan'])) {
    redirect(SITE_URL . '/premium.php');
}

$plan_id = (int)$_GET['plan'];

// Get plan details
$plan_query = "SELECT * FROM premium_plans WHERE id = $plan_id";
$plan_result = mysqli_query($conn, $plan_query);

if (mysqli_num_rows($plan_result) == 0) {
    redirect(SITE_URL . '/premium.php');
}

$plan = mysqli_fetch_assoc($plan_result);

// Process payment form
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Debug POST data
    error_log('POST data: ' . print_r($_POST, true));

    $payment_method = sanitize($_POST['payment_method']);
    $transaction_id = isset($_POST['transaction_id']) ? sanitize($_POST['transaction_id']) : '';

    // Debug variables
    error_log('Payment method: ' . $payment_method);
    error_log('Transaction ID: ' . $transaction_id);

    // Get user ID
    $user_id = $_SESSION['user_id'];

    // Check if user has already made a payment request today
    $today = date('Y-m-d');
    $check_payment_query = "SELECT COUNT(*) as count FROM payments
                           WHERE user_id = $user_id
                           AND DATE(created_at) = '$today'";
    $check_payment_result = mysqli_query($conn, $check_payment_query);
    $payment_count = mysqli_fetch_assoc($check_payment_result)['count'];

    // Validate input
    if (empty($payment_method)) {
        $error = 'দয়া করে একটি পেমেন্ট মেথড নির্বাচন করুন।';
    } elseif (($payment_method == 'nagad' || $payment_method == 'rocket' || $payment_method == 'manual') && empty($transaction_id)) {
        $error = 'দয়া করে ট্রানজেকশন আইডি লিখুন।';
    } elseif ($payment_count > 0) {
        $error = 'আপনি ইতিমধ্যে আজ একটি পেমেন্ট রিকুয়েস্ট করেছেন। দয়া করে আগামীকাল আবার চেষ্টা করুন অথবা আপনার পেমেন্ট স্ট্যাটাস দেখুন।';
    } else {
        // Create subscription
        // User ID is already set above
        $start_date = date('Y-m-d H:i:s');
        $end_date = date('Y-m-d H:i:s', strtotime("+{$plan['duration']} days"));

        // All payments are pending now since bKash is hidden
        $subscription_status = 'pending';

        // Insert subscription
        $subscription_query = "INSERT INTO subscriptions (user_id, plan_id, start_date, end_date, status)
                              VALUES ($user_id, $plan_id, '$start_date', '$end_date', '$subscription_status')";

        if (mysqli_query($conn, $subscription_query)) {
            $subscription_id = mysqli_insert_id($conn);

            // Insert payment
            // Set payment status to pending for all payment methods
            // It will be updated to completed by admin after verification
            $payment_status = 'pending';

            $payment_query = "INSERT INTO payments (user_id, subscription_id, amount, payment_method, transaction_id, payment_status)
                             VALUES ($user_id, $subscription_id, {$plan['price']}, '$payment_method', '$transaction_id', '$payment_status')";

            if (mysqli_query($conn, $payment_query)) {
                // Don't update user to premium yet
                // It will be updated by admin after payment verification
                // Keep is_premium = FALSE for all payment methods except bKash
                // For bKash, it will be updated after payment confirmation

                if ($payment_method == 'bkash') {
                    // For bKash, set payment status to pending
                    $payment_id = mysqli_insert_id($conn);

                    // Store payment ID in session for bKash callback
                    $_SESSION['payment_id'] = $payment_id;

                    // Redirect to bKash payment page
                    redirect(SITE_URL . '/bkash_payment.php?payment_id=' . $payment_id);
                } else {
                    // For manual payment methods (nagad, rocket, manual), keep payment status as pending
                    $payment_id = mysqli_insert_id($conn);

                    // Show success message for manual payment
                    $success = 'আপনার পেমেন্ট গ্রহণ করা হয়েছে এবং প্রক্রিয়াধীন আছে। এডমিন যাচাইয়ের পর আপনার প্রিমিয়াম অ্যাকসেস সক্রিয় করা হবে।';
                }
            } else {
                $error = 'পেমেন্ট প্রক্রিয়াকরণে সমস্যা: ' . mysqli_error($conn);
            }
        } else {
            $error = 'সাবস্ক্রিপশন তৈরিতে সমস্যা: ' . mysqli_error($conn);
        }
    }
}
?>

<!-- Payment Section -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <div class="card payment-box">
                    <div class="card-header bg-danger text-white">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-crown fa-2x me-3"></i>
                            <h3 class="mb-0">Subscribe to <?php echo $plan['name']; ?> Plan</h3>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if($error): ?>
                        <div class="alert alert-danger bg-danger text-white border-danger shadow-sm">
                            <?php echo $error; ?>
                        </div>
                        <?php endif; ?>

                        <?php if($success): ?>
                        <div class="alert alert-success bg-success text-white border-success shadow-sm">
                            <div class="text-center mb-3">
                                <i class="fas fa-check-circle fa-4x mb-3"></i>
                                <h4 class="mb-3">পেমেন্ট গ্রহণ করা হয়েছে!</h4>
                                <p class="mb-3"><?php echo $success; ?></p>
                            </div>
                            <div class="text-center mt-3">
                                <a href="<?php echo SITE_URL; ?>/payment_status.php" class="btn btn-light btn-lg fw-bold">
                                    <i class="fas fa-receipt me-2"></i>পেমেন্ট স্ট্যাটাস দেখুন
                                </a>
                            </div>
                        </div>
                        <?php else: ?>

                        <!-- Selected Plan Details -->
                        <div class="card bg-dark border-primary shadow-lg mb-4">
                            <div class="card-header bg-primary text-white text-center py-3">
                                <h4 class="mb-0 fw-bold">নির্বাচিত প্ল্যান: <?php echo $plan['name']; ?></h4>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-4">
                                    <h2 class="text-white mb-0">৳<?php echo number_format($plan['price'], 0); ?></h2>
                                    <p class="text-muted"><?php
                                    // Calculate months more accurately
                                    $days = $plan['duration'];
                                    if ($days == 365 || $days == 366) {
                                        echo "১২ মাসের জন্য"; // 12 months for 365/366 days
                                    } else if ($days >= 30 && $days <= 31) {
                                        echo "১ মাসের জন্য"; // 1 month for 30/31 days
                                    } else if ($days >= 90 && $days <= 92) {
                                        echo "৩ মাসের জন্য"; // 3 months for 90-92 days
                                    } else if ($days >= 180 && $days <= 183) {
                                        echo "৬ মাসের জন্য"; // 6 months for 180-183 days
                                    } else {
                                        echo round($days/30) . " মাসের জন্য"; // Otherwise use rounding
                                    }
                                    ?></p>
                                </div>

                                <ul class="list-group list-group-flush bg-dark">
                                    <li class="list-group-item bg-dark text-white border-secondary d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-check-circle text-success me-2"></i> সমস্ত প্রিমিয়াম কন্টেন্ট</span>
                                    </li>
                                    <li class="list-group-item bg-dark text-white border-secondary d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-check-circle text-success me-2"></i> হাই কোয়ালিটি ডাউনলোড</span>
                                    </li>
                                    <li class="list-group-item bg-dark text-white border-secondary d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-check-circle text-success me-2"></i> সরাসরি স্ট্রিমিং</span>
                                    </li>
                                    <li class="list-group-item bg-dark text-white border-secondary d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-check-circle text-success me-2"></i> <?php
                                        // Calculate months more accurately
                                        $days = $plan['duration'];
                                        if ($days == 365 || $days == 366) {
                                            echo "১২ মাসের মেয়াদ"; // 12 months for 365/366 days
                                        } else if ($days >= 30 && $days <= 31) {
                                            echo "১ মাসের মেয়াদ"; // 1 month for 30/31 days
                                        } else if ($days >= 90 && $days <= 92) {
                                            echo "৩ মাসের মেয়াদ"; // 3 months for 90-92 days
                                        } else if ($days >= 180 && $days <= 183) {
                                            echo "৬ মাসের মেয়াদ"; // 6 months for 180-183 days
                                        } else {
                                            echo round($days/30) . " মাসের মেয়াদ"; // Otherwise use rounding
                                        }
                                        ?></span>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-footer bg-dark border-primary text-center py-3">
                                <h5 class="text-white mb-0">পেমেন্ট মেথড নির্বাচন করুন</h5>
                            </div>
                        </div>

                        <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF'] . '?plan=' . $plan_id); ?>" id="paymentForm">
                            <div class="mb-4">
                                <h5 class="mb-3 text-white"><i class="fas fa-money-check-alt me-2"></i>পেমেন্ট মেথড নির্বাচন করুন:</h5>
                                <div class="row row-cols-2 row-cols-md-4 g-3">
                                    <!-- bKash payment option hidden temporarily
                                    <div class="col">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="payment_method" id="bkash" value="bkash">
                                            <label class="form-check-label payment-label" for="bkash">
                                                <div class="payment-method-card">
                                                    <img src="<?php echo SITE_URL; ?>/images/bkash.png" alt="bKash" class="img-fluid payment-logo">
                                                    <span class="fw-bold">bKash</span>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    -->
                                    <div class="col">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="payment_method" id="nagad" value="nagad" checked>
                                            <label class="form-check-label payment-label" for="nagad">
                                                <div class="payment-method-card">
                                                    <img src="<?php echo SITE_URL; ?>/images/nagad.png" alt="Nagad" class="img-fluid payment-logo">
                                                    <span class="fw-bold text-danger">Nagad</span>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="payment_method" id="rocket" value="rocket">
                                            <label class="form-check-label payment-label" for="rocket">
                                                <div class="payment-method-card">
                                                    <img src="<?php echo SITE_URL; ?>/images/rocket.png" alt="Rocket" class="img-fluid payment-logo">
                                                    <span class="fw-bold text-primary">Rocket</span>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="payment_method" id="manual" value="manual">
                                            <label class="form-check-label payment-label" for="manual">
                                                <div class="payment-method-card">
                                                    <img src="<?php echo SITE_URL; ?>/images/bkash.png" alt="bKash Manual" class="img-fluid payment-logo">
                                                    <span class="fw-bold text-success">বিকাশ ম্যানুয়াল</span>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Instructions -->
                            <div class="payment-instructions mb-4" id="bkashInstructions">
                                <div class="alert alert-info bg-dark text-white border-info">
                                    <h5 class="alert-heading"><i class="fab fa-bitcoin me-2"></i>বিকাশ পেমেন্ট নির্দেশনা</h5>
                                    <p>আপনার পেমেন্ট নিরাপদে সম্পন্ন করতে বিকাশ পেমেন্ট গেটওয়েতে রিডাইরেক্ট করা হবে।</p>
                                </div>
                            </div>

                            <div class="payment-instructions mb-4 d-none" id="nagadInstructions">
                                <div class="alert alert-info bg-dark text-white border-info">
                                    <h5 class="alert-heading"><i class="fas fa-mobile-alt me-2"></i>নগদ পেমেন্ট নির্দেশনা</h5>
                                    <hr class="border-info">
                                    <ol class="mb-3">
                                        <li>নগদ পেমেন্টের জন্য এই নম্বরে <strong>৳<?php echo number_format($plan['price'], 0); ?></strong> পাঠান:
                                            <div class="input-group mt-2 mb-2">
                                                <input type="text" class="form-control bg-dark text-white" id="nagadNumber" value="01921209102" readonly>
                                                <button class="btn btn-danger" type="button" onclick="copyToClipboard('nagadNumber')">
                                                    <i class="fas fa-copy"></i> কপি
                                                </button>
                                            </div>
                                        </li>
                                        <li>আপনার পেমেন্ট কনফার্মেশন মেসেজ থেকে ট্রানজেকশন আইডি নিচের বক্সে লিখুন</li>
                                        <li>আপনার পেমেন্ট পেন্ডিং অবস্থায় থাকবে এবং এডমিন যাচাইয়ের পর প্রিমিয়াম অ্যাকসেস সক্রিয় করা হবে</li>
                                        <li>পেমেন্ট সংক্রান্ত সমস্যার জন্য আমাদের কাস্টমার সাপোর্টে যোগাযোগ করুন <strong><EMAIL></strong></li>
                                    </ol>
                                    <div class="mb-3 mt-4">
                                        <label for="nagadTransaction" class="form-label fw-bold fs-5 text-danger">ট্রানজেকশন আইডি</label>
                                        <div class="input-group input-group-lg border border-danger rounded shadow-sm">
                                            <span class="input-group-text bg-danger text-white"><i class="fas fa-receipt"></i></span>
                                            <input type="text" class="form-control form-control-lg border-danger transaction-input bg-black text-white" id="nagadTransaction" name="transaction_id" placeholder="উদা: 9HN8GD7F" required>
                                        </div>
                                        <div class="form-text text-danger fw-bold mt-2">
                                            <i class="fas fa-info-circle me-1"></i> নগদ থেকে প্রাপ্ত ট্রানজেকশন আইডি লিখুন
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="payment-instructions mb-4 d-none" id="rocketInstructions">
                                <div class="alert alert-info bg-dark text-white border-info">
                                    <h5 class="alert-heading"><i class="fas fa-rocket me-2"></i>রকেট পেমেন্ট নির্দেশনা</h5>
                                    <hr class="border-info">
                                    <ol class="mb-3">
                                        <li>রকেট পেমেন্টের জন্য এই নম্বরে <strong>৳<?php echo number_format($plan['price'], 0); ?></strong> পাঠান:
                                            <div class="input-group mt-2 mb-2">
                                                <input type="text" class="form-control bg-dark text-white" id="rocketNumber" value="01987024978" readonly>
                                                <button class="btn btn-primary" type="button" onclick="copyToClipboard('rocketNumber')">
                                                    <i class="fas fa-copy"></i> কপি
                                                </button>
                                            </div>
                                        </li>
                                        <li>আপনার পেমেন্ট কনফার্মেশন মেসেজ থেকে ট্রানজেকশন আইডি নিচের বক্সে লিখুন</li>
                                        <li>আপনার পেমেন্ট পেন্ডিং অবস্থায় থাকবে এবং এডমিন যাচাইয়ের পর প্রিমিয়াম অ্যাকসেস সক্রিয় করা হবে</li>
                                        <li>পেমেন্ট সংক্রান্ত সমস্যার জন্য আমাদের কাস্টমার সাপোর্টে যোগাযোগ করুন <strong><EMAIL></strong></li>
                                    </ol>
                                    <div class="mb-3 mt-4">
                                        <label for="rocketTransaction" class="form-label fw-bold fs-5 text-primary">ট্রানজেকশন আইডি</label>
                                        <div class="input-group input-group-lg border border-primary rounded shadow-sm">
                                            <span class="input-group-text bg-primary text-white"><i class="fas fa-receipt"></i></span>
                                            <input type="text" class="form-control form-control-lg border-primary transaction-input bg-black text-white" id="rocketTransaction" name="transaction_id" placeholder="উদা: AB123XYZ" required>
                                        </div>
                                        <div class="form-text text-primary fw-bold mt-2">
                                            <i class="fas fa-info-circle me-1"></i> রকেট থেকে প্রাপ্ত ট্রানজেকশন আইডি লিখুন
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="payment-instructions mb-4 d-none" id="manualInstructions">
                                <div class="alert alert-info bg-dark text-white border-info">
                                    <h5 class="alert-heading"><i class="fas fa-money-bill-wave me-2"></i>বিকাশ ম্যানুয়াল পেমেন্ট নির্দেশনা</h5>
                                    <hr class="border-info">
                                    <ol class="mb-3">
                                        <li>বিকাশ ম্যানুয়াল পেমেন্টের জন্য এই নম্বরে <strong>৳<?php echo number_format($plan['price'], 0); ?></strong> পাঠান:
                                            <div class="input-group mt-2 mb-2">
                                                <input type="text" class="form-control bg-dark text-white" id="bkashNumber" value="01921209102" readonly>
                                                <button class="btn btn-success" type="button" onclick="copyToClipboard('bkashNumber')">
                                                    <i class="fas fa-copy"></i> কপি
                                                </button>
                                            </div>
                                        </li>
                                        <li>নিচের বক্সে পেমেন্ট রেফারেন্স বা নোট লিখুন</li>
                                        <li>আপনার পেমেন্ট পেন্ডিং অবস্থায় থাকবে এবং এডমিন যাচাইয়ের পর প্রিমিয়াম অ্যাকসেস সক্রিয় করা হবে</li>
                                    </ol>
                                    <div class="mb-3 mt-4">
                                        <label for="manualReference" class="form-label fw-bold fs-5 text-success">ট্রানজেকশন আইডি</label>
                                        <div class="input-group input-group-lg border border-success rounded shadow-sm">
                                            <span class="input-group-text bg-success text-white"><i class="fas fa-sticky-note"></i></span>
                                            <input type="text" class="form-control form-control-lg border-success transaction-input bg-black text-white" id="manualReference" name="transaction_id" placeholder="উদা: 8AB12XYZ" required>
                                        </div>
                                        <div class="form-text text-success fw-bold mt-2">
                                            <i class="fas fa-info-circle me-1"></i> বিকাশ থেকে প্রাপ্ত ট্রানজেকশন আইডি লিখুন
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mt-5">
                                <button type="submit" class="btn btn-danger btn-lg px-5 py-3 fw-bold">
                                    <i class="fas fa-credit-card me-2"></i>পেমেন্ট সম্পন্ন করুন
                                </button>
                            </div>
                        </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Custom JavaScript for Payment Method Selection and Copy Function -->
<script>
// Function to copy text to clipboard
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    if (!element) return;

    // Select the text
    element.select();
    element.setSelectionRange(0, 99999); // For mobile devices

    // Copy the text
    document.execCommand('copy');

    // Show feedback
    const originalText = element.nextElementSibling.innerHTML;
    element.nextElementSibling.innerHTML = '<i class="fas fa-check"></i> কপি হয়েছে';

    // Reset button text after 2 seconds
    setTimeout(() => {
        element.nextElementSibling.innerHTML = originalText;
    }, 2000);
}

document.addEventListener('DOMContentLoaded', function() {
    const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
    const instructionDivs = document.querySelectorAll('.payment-instructions');
    const paymentForm = document.getElementById('paymentForm');

    // Function to show the selected payment method instructions
    function showInstructions(method) {
        instructionDivs.forEach(div => {
            div.classList.add('d-none');
        });

        document.getElementById(method + 'Instructions').classList.remove('d-none');
    }

    // Add event listeners to payment method radios
    paymentMethods.forEach(radio => {
        radio.addEventListener('change', function() {
            showInstructions(this.value);

            // Toggle required attribute on transaction inputs
            document.querySelectorAll('input[name="transaction_id"]').forEach(input => {
                input.required = false;
                input.disabled = true; // Disable all inputs by default
            });

            // Set required for all payment methods
            let inputId = null;

            if (this.value === 'nagad') {
                inputId = 'nagadTransaction';
            } else if (this.value === 'rocket') {
                inputId = 'rocketTransaction';
            } else if (this.value === 'manual') {
                inputId = 'manualReference';
            }

            if (inputId) {
                const input = document.getElementById(inputId);
                if (input) {
                    input.required = true;
                    input.disabled = false; // Enable only the selected input
                }
            }
        });
    });

    // Show the default selected payment method instructions
    const selectedMethod = document.querySelector('input[name="payment_method"]:checked').value;
    showInstructions(selectedMethod);

    // Set required attribute for default selected payment method
    document.querySelectorAll('input[name="transaction_id"]').forEach(input => {
        input.required = false;
        input.disabled = true; // Disable all inputs by default
    });

    // Set required for all payment methods
    let inputId = null;

    if (selectedMethod === 'nagad') {
        inputId = 'nagadTransaction';
    } else if (selectedMethod === 'rocket') {
        inputId = 'rocketTransaction';
    } else if (selectedMethod === 'manual') {
        inputId = 'manualReference';
    }

    if (inputId) {
        const input = document.getElementById(inputId);
        if (input) {
            input.required = true;
            input.disabled = false; // Enable only the selected input
        }
    }

    // Form validation
    if (paymentForm) {
        paymentForm.addEventListener('submit', function(event) {
            // Prevent default form submission
            event.preventDefault();

            const selectedMethod = document.querySelector('input[name="payment_method"]:checked').value;
            console.log('Selected payment method:', selectedMethod);

            // Disable all transaction inputs except the selected one
            document.querySelectorAll('input[name="transaction_id"]').forEach(input => {
                input.disabled = true;
            });

            let canSubmit = true;
            let transactionInput = null;

            // Validate transaction ID for all payment methods
            if (selectedMethod === 'nagad' || selectedMethod === 'rocket' || selectedMethod === 'manual') {
                if (selectedMethod === 'nagad') {
                    transactionInput = document.getElementById('nagadTransaction');
                } else if (selectedMethod === 'rocket') {
                    transactionInput = document.getElementById('rocketTransaction');
                } else if (selectedMethod === 'manual') {
                    transactionInput = document.getElementById('manualReference');
                }

                if (transactionInput) {
                    // Enable only the selected transaction input
                    transactionInput.disabled = false;

                    console.log('Transaction input value:', transactionInput.value);

                    if (!transactionInput.value.trim()) {
                        alert('দয়া করে ট্রানজেকশন আইডি লিখুন।');
                        transactionInput.focus();
                        canSubmit = false;
                    }
                }
            }

            if (canSubmit) {
                console.log('Form is valid, submitting...');
                paymentForm.submit();
            }
        });
    }
});
</script>

<!-- Output the custom CSS -->
<?php echo $custom_css; ?>

<?php require_once 'includes/footer.php'; ?>
