<?php
require_once '../../includes/config.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

header('Content-Type: application/json');

try {
    // Get statistics
    $stats = [];

    // Online users (active in last 5 minutes)
    $online_query = "SELECT COUNT(DISTINCT session_id) as count FROM user_activity 
                     WHERE last_activity >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)";
    $online_result = mysqli_query($conn, $online_query);
    $stats['online'] = $online_result ? mysqli_fetch_assoc($online_result)['count'] : 0;

    // Today's visitors
    $today_query = "SELECT COUNT(DISTINCT session_id) as count FROM user_activity 
                    WHERE DATE(first_visit) = CURDATE()";
    $today_result = mysqli_query($conn, $today_query);
    $stats['today'] = $today_result ? mysqli_fetch_assoc($today_result)['count'] : 0;

    // Premium users online
    $premium_query = "SELECT COUNT(DISTINCT session_id) as count FROM user_activity 
                      WHERE last_activity >= DATE_SUB(NOW(), INTERVAL 5 MINUTE) AND is_premium = TRUE";
    $premium_result = mysqli_query($conn, $premium_query);
    $stats['premium'] = $premium_result ? mysqli_fetch_assoc($premium_result)['count'] : 0;

    // Unread messages
    $messages_query = "SELECT COUNT(*) as count FROM user_messages 
                       WHERE message_type = 'user' AND is_read = FALSE";
    $messages_result = mysqli_query($conn, $messages_query);
    $stats['messages'] = $messages_result ? mysqli_fetch_assoc($messages_result)['count'] : 0;

    // Get active users
    $users_query = "SELECT ua.*, u.username, u.email 
                    FROM user_activity ua 
                    LEFT JOIN users u ON ua.user_id = u.id 
                    WHERE ua.last_activity >= DATE_SUB(NOW(), INTERVAL 5 MINUTE) 
                    ORDER BY ua.last_activity DESC 
                    LIMIT 50";
    $users_result = mysqli_query($conn, $users_query);
    $active_users = [];
    if ($users_result) {
        while ($row = mysqli_fetch_assoc($users_result)) {
            $active_users[] = $row;
        }
    }

    echo json_encode([
        'success' => true,
        'stats' => $stats,
        'users' => $active_users
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}
?>
