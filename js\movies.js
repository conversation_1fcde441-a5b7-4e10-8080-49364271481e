// Movies Page JavaScript

// Function to play trailer
function playTrailer(trailerUrl) {
    // Create modal for trailer
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'trailerModal';
    modal.setAttribute('tabindex', '-1');
    modal.setAttribute('aria-labelledby', 'trailerModalLabel');
    modal.setAttribute('aria-hidden', 'true');

    modal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content bg-dark">
                <div class="modal-header border-0">
                    <h5 class="modal-title text-white" id="trailerModalLabel">Movie Trailer</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-0">
                    <div class="ratio ratio-16x9">
                        <iframe src="${trailerUrl}" allowfullscreen></iframe>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Initialize and show the modal
    const trailerModal = new bootstrap.Modal(modal);
    trailerModal.show();

    // Remove modal from DOM when hidden
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

// Function to add to watchlist
function addToWatchlist(contentId, contentType) {
    // Check if user is logged in
    const isLoggedIn = document.body.classList.contains('logged-in');

    if (!isLoggedIn) {
        // Show login prompt
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'Login Required',
                text: 'Please login to add items to your watchlist',
                icon: 'info',
                showCancelButton: true,
                confirmButtonText: 'Login',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = 'login.php';
                }
            });
        } else {
            if (confirm('Please login to add items to your watchlist. Go to login page?')) {
                window.location.href = 'login.php';
            }
        }
        return;
    }

    // Add to watchlist via AJAX
    fetch('ajax/watchlist.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=add&content_id=${contentId}&content_type=${contentType}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: 'Added to Watchlist',
                    text: data.message,
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });
            } else {
                alert('Added to watchlist successfully!');
            }

            // Change button appearance
            const button = document.querySelector(`.add-to-watchlist[data-id="${contentId}"]`);
            if (button) {
                button.innerHTML = '<i class="fas fa-check"></i>';
                button.classList.add('added');
                button.title = 'Added to Watchlist';
            }
        } else {
            // Show error message
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: 'Error',
                    text: data.message,
                    icon: 'error'
                });
            } else {
                alert('Error: ' + data.message);
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'Error',
                text: 'An error occurred. Please try again.',
                icon: 'error'
            });
        } else {
            alert('An error occurred. Please try again.');
        }
    });
}

document.addEventListener('DOMContentLoaded', function() {
    // Add hover effect to movie cards
    const movieCards = document.querySelectorAll('.movie-card');

    movieCards.forEach(card => {
        // Add staggered animation delay based on index
        const index = Array.from(movieCards).indexOf(card);
        card.style.animationDelay = `${index * 0.05}s`;
    });

    // Filter animation
    const filterButtons = document.querySelectorAll('.filter-btn');

    filterButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
