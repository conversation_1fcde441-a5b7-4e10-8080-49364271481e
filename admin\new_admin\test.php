<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Testing Admin Panel</h1>";

// Test 1: Check if config file exists and can be included
echo "<h2>Test 1: Config File</h2>";
if (file_exists('../../includes/config.php')) {
    echo "✅ Config file exists<br>";
    try {
        require_once '../../includes/config.php';
        echo "✅ Config file included successfully<br>";
        
        // Test database connection
        if (isset($conn) && $conn) {
            echo "✅ Database connection successful<br>";
        } else {
            echo "❌ Database connection failed<br>";
        }
        
        // Test constants
        if (defined('SITE_URL')) {
            echo "✅ SITE_URL defined: " . SITE_URL . "<br>";
        } else {
            echo "❌ SITE_URL not defined<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Error including config: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ Config file not found<br>";
}

// Test 2: Check if functions file exists and can be included
echo "<h2>Test 2: Functions File</h2>";
if (file_exists('../../includes/functions.php')) {
    echo "✅ Functions file exists<br>";
    try {
        require_once '../../includes/functions.php';
        echo "✅ Functions file included successfully<br>";
        
        // Test functions
        if (function_exists('isLoggedIn')) {
            echo "✅ isLoggedIn function exists<br>";
        } else {
            echo "❌ isLoggedIn function not found<br>";
        }
        
        if (function_exists('isAdmin')) {
            echo "✅ isAdmin function exists<br>";
        } else {
            echo "❌ isAdmin function not found<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Error including functions: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ Functions file not found<br>";
}

// Test 3: Check session
echo "<h2>Test 3: Session</h2>";
if (session_status() == PHP_SESSION_ACTIVE) {
    echo "✅ Session is active<br>";
    echo "Session ID: " . session_id() . "<br>";
    
    if (isset($_SESSION['user_id'])) {
        echo "✅ User logged in: " . $_SESSION['username'] . "<br>";
        echo "User role: " . $_SESSION['user_role'] . "<br>";
    } else {
        echo "❌ User not logged in<br>";
    }
} else {
    echo "❌ Session not active<br>";
}

// Test 4: Check file paths
echo "<h2>Test 4: File Paths</h2>";
$files_to_check = [
    'includes/header.php',
    'includes/sidebar.php', 
    'includes/footer.php',
    'assets/css/admin-style.css',
    'assets/css/responsive.css',
    'assets/js/admin-script.js'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✅ $file exists<br>";
    } else {
        echo "❌ $file not found<br>";
    }
}

// Test 5: Check current directory
echo "<h2>Test 5: Directory Info</h2>";
echo "Current directory: " . getcwd() . "<br>";
echo "Script name: " . $_SERVER['SCRIPT_NAME'] . "<br>";
echo "Document root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";

// Test 6: PHP Info
echo "<h2>Test 6: PHP Info</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Memory limit: " . ini_get('memory_limit') . "<br>";
echo "Max execution time: " . ini_get('max_execution_time') . "<br>";

echo "<h2>Test Complete</h2>";
echo "<p>If you see this message, the basic PHP execution is working.</p>";
echo "<p><a href='index.php'>Try Admin Panel</a></p>";
?>
