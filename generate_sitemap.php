<?php
// Include database configuration
require_once 'includes/config.php';
require_once 'includes/seo_helper.php';

// Set header to XML
header("Content-Type: application/xml; charset=utf-8");

// Start XML file
echo '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"
        xmlns:video="http://www.google.com/schemas/sitemap-video/1.1">' . PHP_EOL;

// Function to add URL to sitemap
function addUrl($url, $lastmod, $changefreq, $priority, $image = null) {
    echo "\t<url>" . PHP_EOL;
    echo "\t\t<loc>" . htmlspecialchars($url) . "</loc>" . PHP_EOL;
    echo "\t\t<lastmod>" . $lastmod . "</lastmod>" . PHP_EOL;
    echo "\t\t<changefreq>" . $changefreq . "</changefreq>" . PHP_EOL;
    echo "\t\t<priority>" . $priority . "</priority>" . PHP_EOL;
    
    if ($image) {
        echo "\t\t<image:image>" . PHP_EOL;
        echo "\t\t\t<image:loc>" . htmlspecialchars($image['loc']) . "</image:loc>" . PHP_EOL;
        echo "\t\t\t<image:caption>" . htmlspecialchars($image['caption']) . "</image:caption>" . PHP_EOL;
        echo "\t\t</image:image>" . PHP_EOL;
    }
    
    echo "\t</url>" . PHP_EOL;
}

// Get site domain
$site_domain = SITE_URL;

// Add static pages
$static_pages = [
    ['url' => $site_domain . '/', 'lastmod' => date('c'), 'changefreq' => 'daily', 'priority' => '1.0'],
    ['url' => $site_domain . '/movies.php', 'lastmod' => date('c'), 'changefreq' => 'daily', 'priority' => '0.9'],
    ['url' => $site_domain . '/tvshows.php', 'lastmod' => date('c'), 'changefreq' => 'daily', 'priority' => '0.9'],
    ['url' => $site_domain . '/categories.php', 'lastmod' => date('c'), 'changefreq' => 'weekly', 'priority' => '0.8'],
    ['url' => $site_domain . '/about.php', 'lastmod' => date('c'), 'changefreq' => 'monthly', 'priority' => '0.7'],
    ['url' => $site_domain . '/contact.php', 'lastmod' => date('c'), 'changefreq' => 'monthly', 'priority' => '0.7'],
    ['url' => $site_domain . '/privacy-policy.php', 'lastmod' => date('c'), 'changefreq' => 'monthly', 'priority' => '0.5'],
    ['url' => $site_domain . '/terms-of-service.php', 'lastmod' => date('c'), 'changefreq' => 'monthly', 'priority' => '0.5'],
    ['url' => $site_domain . '/blog.php', 'lastmod' => date('c'), 'changefreq' => 'weekly', 'priority' => '0.8'],
    ['url' => $site_domain . '/search.php', 'lastmod' => date('c'), 'changefreq' => 'daily', 'priority' => '0.6'],
    ['url' => $site_domain . '/register.php', 'lastmod' => date('c'), 'changefreq' => 'monthly', 'priority' => '0.5'],
    ['url' => $site_domain . '/login.php', 'lastmod' => date('c'), 'changefreq' => 'monthly', 'priority' => '0.5'],
    ['url' => $site_domain . '/faq.php', 'lastmod' => date('c'), 'changefreq' => 'monthly', 'priority' => '0.6'],
];

foreach ($static_pages as $page) {
    addUrl($page['url'], $page['lastmod'], $page['changefreq'], $page['priority']);
}

// Add movie pages with SEO URLs
$movie_query = "SELECT id, title, release_year as year, poster, updated_at FROM movies WHERE is_active = 1 ORDER BY updated_at DESC LIMIT 2000";
$movie_result = mysqli_query($conn, $movie_query);

if ($movie_result && mysqli_num_rows($movie_result) > 0) {
    while ($movie = mysqli_fetch_assoc($movie_result)) {
        // SEO-friendly URL
        $movie_seo_url = getMovieSeoUrl($movie['id'], $movie['title'], $movie['year']);

        // Original URL for backward compatibility
        $movie_url = $site_domain . '/movie_details.php?id=' . $movie['id'];

        // Download URL
        $download_url = getMovieDownloadUrl($movie['id'], $movie['title'], $movie['year']);

        // Watch URL
        $watch_url = getMovieWatchUrl($movie['id'], $movie['title'], $movie['year']);

        $lastmod = date('c', strtotime($movie['updated_at']));
        $image = [
            'loc' => $site_domain . '/uploads/' . $movie['poster'],
            'caption' => htmlspecialchars($movie['title'] . ' (' . $movie['year'] . ') - Download Full Movie HD'),
            'title' => htmlspecialchars($movie['title'] . ' (' . $movie['year'] . ') Poster')
        ];

        // Add SEO URL
        addUrl($movie_seo_url, $lastmod, 'weekly', '0.8', $image);

        // Add download URL
        addUrl($download_url, $lastmod, 'weekly', '0.7', $image);

        // Add watch URL
        addUrl($watch_url, $lastmod, 'weekly', '0.7', $image);

        // Add original URL for backward compatibility
        addUrl($movie_url, $lastmod, 'weekly', '0.6', $image);
    }
}

// Add TV show pages with SEO URLs
$tvshow_query = "SELECT id, title, release_year as year, poster, updated_at FROM tvshows WHERE is_active = 1 ORDER BY updated_at DESC LIMIT 2000";
$tvshow_result = mysqli_query($conn, $tvshow_query);

if ($tvshow_result && mysqli_num_rows($tvshow_result) > 0) {
    while ($tvshow = mysqli_fetch_assoc($tvshow_result)) {
        // SEO-friendly URL
        $tvshow_seo_url = getTvShowSeoUrl($tvshow['id'], $tvshow['title'], $tvshow['year']);

        // Original URL for backward compatibility
        $tvshow_url = $site_domain . '/tvshow_details.php?id=' . $tvshow['id'];

        // Watch URL
        $watch_url = getTvShowWatchUrl($tvshow['id'], $tvshow['title'], $tvshow['year']);

        $lastmod = date('c', strtotime($tvshow['updated_at']));
        $image = [
            'loc' => $site_domain . '/uploads/' . $tvshow['poster'],
            'caption' => htmlspecialchars($tvshow['title'] . ' (' . $tvshow['year'] . ') - Watch TV Series Online'),
            'title' => htmlspecialchars($tvshow['title'] . ' (' . $tvshow['year'] . ') Poster')
        ];

        // Add SEO URL
        addUrl($tvshow_seo_url, $lastmod, 'weekly', '0.8', $image);

        // Add watch URL
        addUrl($watch_url, $lastmod, 'weekly', '0.7', $image);

        // Add original URL for backward compatibility
        addUrl($tvshow_url, $lastmod, 'weekly', '0.6', $image);

        // Add season-specific URLs
        $seasons_query = "SELECT DISTINCT season_number FROM episodes WHERE tvshow_id = " . $tvshow['id'] . " ORDER BY season_number";
        $seasons_result = mysqli_query($conn, $seasons_query);

        if ($seasons_result && mysqli_num_rows($seasons_result) > 0) {
            while ($season = mysqli_fetch_assoc($seasons_result)) {
                $season_url = getTvShowSeoUrl($tvshow['id'], $tvshow['title'], $tvshow['year'], $season['season_number']);
                addUrl($season_url, $lastmod, 'weekly', '0.7', $image);
            }
        }
    }
}

// Add category-based URLs
$categories_query = "SELECT id, name, slug FROM categories WHERE is_active = 1";
$categories_result = mysqli_query($conn, $categories_query);

if ($categories_result && mysqli_num_rows($categories_result) > 0) {
    while ($category = mysqli_fetch_assoc($categories_result)) {
        $category_slug = !empty($category['slug']) ? $category['slug'] : generateSlug($category['name']);

        // Movie category URLs
        $movie_category_url = getMovieCategoryUrl($category_slug);
        addUrl($movie_category_url, date('c'), 'weekly', '0.7');

        // TV show category URLs
        $tvshow_category_url = getTvShowCategoryUrl($category_slug);
        addUrl($tvshow_category_url, date('c'), 'weekly', '0.7');
    }
}

// Add year-based URLs (last 10 years)
$current_year = date('Y');
for ($year = $current_year; $year >= ($current_year - 10); $year--) {
    // Movie year URLs
    $movie_year_url = getMovieYearUrl($year);
    addUrl($movie_year_url, date('c'), 'monthly', '0.6');

    // TV show year URLs
    $tvshow_year_url = getTvShowYearUrl($year);
    addUrl($tvshow_year_url, date('c'), 'monthly', '0.6');
}

// Add quality-based URLs
$qualities = ['720p', '1080p', '4k'];
foreach ($qualities as $quality) {
    $quality_url = getMovieQualityUrl($quality);
    addUrl($quality_url, date('c'), 'monthly', '0.6');
}

// Add language-based URLs
$languages = ['bengali', 'hindi', 'english'];
foreach ($languages as $language) {
    $language_url = getMovieLanguageUrl($language);
    addUrl($language_url, date('c'), 'monthly', '0.6');
}

// Add popular search terms
$popular_searches = [
    'avatar', 'avengers', 'spider-man', 'batman', 'superman', 'iron-man',
    'bengali-movie', 'hindi-movie', 'english-movie', 'bollywood', 'hollywood',
    'action-movie', 'comedy-movie', 'drama-series', 'thriller-movie'
];

foreach ($popular_searches as $search_term) {
    $search_url = getSearchUrl($search_term);
    addUrl($search_url, date('c'), 'monthly', '0.5');
}

// Add category pages
$category_query = "SELECT id, name, updated_at FROM categories ORDER BY updated_at DESC";
$category_result = mysqli_query($conn, $category_query);

if ($category_result && mysqli_num_rows($category_result) > 0) {
    while ($category = mysqli_fetch_assoc($category_result)) {
        $category_url = $site_domain . '/category.php?id=' . $category['id'];
        $lastmod = date('c', strtotime($category['updated_at']));
        
        addUrl($category_url, $lastmod, 'weekly', '0.7');
    }
}

// Close XML file
echo '</urlset>';

// Save the generated sitemap to a file
$sitemap_content = ob_get_contents();
file_put_contents('sitemap.xml', $sitemap_content);
?>
