<?php
require_once 'includes/config.php';
require_once 'includes/seo_helper.php';
require_once 'includes/seo_content.php';

echo "<h1>TV Show SEO Test</h1>";

// Test with a real TV show from database
$query = "SELECT t.*, c.name as category_name FROM tvshows t 
          LEFT JOIN categories c ON t.category_id = c.id 
          LIMIT 1";
$result = mysqli_query($conn, $query);

if ($result && mysqli_num_rows($result) > 0) {
    $tvshow = mysqli_fetch_assoc($result);
    
    echo "<h2>Testing with TV Show: " . htmlspecialchars($tvshow['title']) . "</h2>";
    echo "<p><strong>Database columns available:</strong> " . implode(', ', array_keys($tvshow)) . "</p>";
    
    echo "<h3>SEO Keywords Test:</h3>";
    try {
        $keywords = getTvShowKeywords($tvshow);
        echo "<p style='color: green;'>✅ Keywords generated successfully:</p>";
        echo "<p>" . htmlspecialchars($keywords) . "</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error generating keywords: " . $e->getMessage() . "</p>";
    }
    
    echo "<h3>SEO Content Test:</h3>";
    try {
        $seo_content = generateTvShowSeoContent($tvshow);
        echo "<p style='color: green;'>✅ SEO content generated successfully:</p>";
        echo "<p><strong>Description:</strong> " . htmlspecialchars($seo_content['description']) . "</p>";
        echo "<p><strong>Keywords:</strong> " . implode(', ', $seo_content['keywords']) . "</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error generating SEO content: " . $e->getMessage() . "</p>";
    }
    
} else {
    echo "<p style='color: red;'>No TV shows found in database for testing.</p>";
}

// Test with sample data to ensure backward compatibility
echo "<h2>Testing with Sample Data (Old Structure)</h2>";
$sample_tvshow_old = [
    'id' => 999,
    'title' => 'Test Series',
    'description' => 'This is a test TV series for SEO testing.',
    'release_year' => 2023,
    'category_name' => 'Drama',
    'language' => 'Bengali'
];

echo "<h3>Old Structure Keywords Test:</h3>";
try {
    $keywords = getTvShowKeywords($sample_tvshow_old);
    echo "<p style='color: green;'>✅ Keywords with old structure work:</p>";
    echo "<p>" . htmlspecialchars($keywords) . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error with old structure: " . $e->getMessage() . "</p>";
}

// Test with sample data (New Structure)
echo "<h2>Testing with Sample Data (New Structure)</h2>";
$sample_tvshow_new = [
    'id' => 999,
    'title' => 'Test Series New',
    'description' => 'This is a test TV series for SEO testing with new structure.',
    'start_year' => 2023,
    'end_year' => 2024,
    'category_name' => 'Action',
    'language' => 'English'
];

echo "<h3>New Structure Keywords Test:</h3>";
try {
    $keywords = getTvShowKeywords($sample_tvshow_new);
    echo "<p style='color: green;'>✅ Keywords with new structure work:</p>";
    echo "<p>" . htmlspecialchars($keywords) . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error with new structure: " . $e->getMessage() . "</p>";
}

echo "<h3>New Structure SEO Content Test:</h3>";
try {
    $seo_content = generateTvShowSeoContent($sample_tvshow_new);
    echo "<p style='color: green;'>✅ SEO content with new structure works:</p>";
    echo "<p><strong>Description:</strong> " . htmlspecialchars($seo_content['description']) . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error with new structure SEO content: " . $e->getMessage() . "</p>";
}

echo "<p style='margin-top: 30px;'><strong>Test completed!</strong> If all tests show green checkmarks, the SEO functions are working correctly.</p>";
?>
