<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$error = '';
$success = '';

// Get user's payments - use try-catch to handle errors
try {
    $payments_query = "SELECT p.*, s.plan_id, s.start_date, s.end_date, s.status as subscription_status
                      FROM payments p
                      LEFT JOIN subscriptions s ON p.subscription_id = s.id
                      WHERE p.user_id = $user_id
                      ORDER BY p.created_at DESC";
    $payments_result = mysqli_query($conn, $payments_query);
} catch (Exception $e) {
    // If there's an error, set payments_result to null
    $payments_result = null;
}

// Check if payments table exists
if (!$payments_result) {
    // Create payments table if it doesn't exist
    $create_payments_table = "CREATE TABLE IF NOT EXISTS payments (
        id INT(11) NOT NULL AUTO_INCREMENT,
        user_id INT(11) NOT NULL,
        subscription_id INT(11) NOT NULL,
        payment_method VARCHAR(50) NOT NULL,
        transaction_id VARCHAR(255) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        payment_status ENUM('pending', 'completed', 'cancelled') NOT NULL DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    mysqli_query($conn, $create_payments_table);

    // Try again
    $payments_result = mysqli_query($conn, $payments_query);
}

// First, check if plans table exists and create it if it doesn't
$check_plans_table = "SHOW TABLES LIKE 'plans'";
$plans_table_exists = mysqli_query($conn, $check_plans_table);

if (!$plans_table_exists || mysqli_num_rows($plans_table_exists) == 0) {
    // Create plans table if it doesn't exist
    $create_plans_table = "CREATE TABLE IF NOT EXISTS plans (
        id INT(11) NOT NULL AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        duration INT(11) NOT NULL,
        duration_months INT(11) NOT NULL,
        features TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    mysqli_query($conn, $create_plans_table);

    // Insert a default plan
    $insert_default_plan = "INSERT INTO plans (name, price, duration, duration_months, features)
                           VALUES ('Basic Plan', 100, 30, 1, 'Basic features')";
    mysqli_query($conn, $insert_default_plan);
}

// Check if subscriptions table exists
$check_subscriptions_table = "SHOW TABLES LIKE 'subscriptions'";
$subscriptions_table_exists = mysqli_query($conn, $check_subscriptions_table);

if (!$subscriptions_table_exists || mysqli_num_rows($subscriptions_table_exists) == 0) {
    // Create subscriptions table if it doesn't exist
    $create_subscriptions_table = "CREATE TABLE IF NOT EXISTS subscriptions (
        id INT(11) NOT NULL AUTO_INCREMENT,
        user_id INT(11) NOT NULL,
        plan_id INT(11) NOT NULL,
        start_date DATETIME NOT NULL,
        end_date DATETIME NOT NULL,
        status ENUM('pending', 'active', 'expired', 'cancelled') NOT NULL DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    mysqli_query($conn, $create_subscriptions_table);



}

// Check if payments table exists
$check_payments_table = "SHOW TABLES LIKE 'payments'";
$payments_table_exists = mysqli_query($conn, $check_payments_table);

if (!$payments_table_exists || mysqli_num_rows($payments_table_exists) == 0) {
    // Create payments table if it doesn't exist
    $create_payments_table = "CREATE TABLE IF NOT EXISTS payments (
        id INT(11) NOT NULL AUTO_INCREMENT,
        user_id INT(11) NOT NULL,
        subscription_id INT(11) NOT NULL,
        payment_method VARCHAR(50) NOT NULL,
        transaction_id VARCHAR(255) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        payment_status ENUM('pending', 'completed', 'cancelled') NOT NULL DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    mysqli_query($conn, $create_payments_table);
}

// Now try to get active subscription - use try-catch to handle errors
try {
    $active_subscription_query = "SELECT s.*, pl.name as plan_name, pl.price, pl.duration_months
                                 FROM subscriptions s
                                 LEFT JOIN plans pl ON s.plan_id = pl.id
                                 WHERE s.user_id = $user_id AND s.status = 'active'
                                 ORDER BY s.end_date DESC
                                 LIMIT 1";
    $active_subscription_result = mysqli_query($conn, $active_subscription_query);
    $active_subscription = $active_subscription_result ? mysqli_fetch_assoc($active_subscription_result) : null;
} catch (Exception $e) {
    // If there's an error, just set active_subscription to null
    $active_subscription = null;
}

// Get user's pending subscription - use LEFT JOIN to avoid errors if tables don't have data
$pending_subscription_query = "SELECT s.*, pl.name as plan_name, pl.price, pl.duration_months,
                              p.payment_method, p.transaction_id, p.payment_status, p.created_at as payment_date
                              FROM subscriptions s
                              LEFT JOIN plans pl ON s.plan_id = pl.id
                              LEFT JOIN payments p ON s.id = p.subscription_id
                              WHERE s.user_id = $user_id AND s.status = 'pending'
                              ORDER BY p.created_at DESC
                              LIMIT 1";

try {
    $pending_subscription_result = mysqli_query($conn, $pending_subscription_query);
    $pending_subscription = $pending_subscription_result ? mysqli_fetch_assoc($pending_subscription_result) : null;
} catch (Exception $e) {
    // If there's an error, just set pending_subscription to null
    $pending_subscription = null;
}

// Include header
include 'includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card bg-dark text-white border-secondary shadow-lg mb-4">
                <div class="card-header bg-dark border-secondary">
                    <h3 class="mb-0"><i class="fas fa-receipt me-2"></i>পেমেন্ট স্ট্যাটাস</h3>
                </div>
                <div class="card-body">
                    <?php if($error): ?>
                    <div class="alert alert-danger bg-danger text-white border-danger shadow-sm">
                        <?php echo $error; ?>
                    </div>
                    <?php endif; ?>

                    <?php if($success): ?>
                    <div class="alert alert-success bg-success text-white border-success shadow-sm">
                        <?php echo $success; ?>
                    </div>
                    <?php endif; ?>

                    <!-- Daily Payment Limit Notice -->
                    <div class="alert alert-info bg-dark border-info shadow-sm mb-4">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-info-circle fa-2x text-info me-3"></i>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                <h5 class="text-info mb-1">পেমেন্ট সীমাবদ্ধতা</h5>
                                <p class="text-white mb-0">ফেইক পেমেন্ট রিকুয়েস্ট প্রতিরোধ করতে একজন ইউজার দিনে মাত্র একবার পেমেন্ট রিকুয়েস্ট করতে পারবেন। পেমেন্ট রিকুয়েস্ট করার পর দয়া করে এই পেজে আপনার পেমেন্ট স্ট্যাটাস চেক করুন।</p>
                            </div>
                        </div>
                    </div>

                    <!-- Active Subscription -->
                    <?php if($active_subscription): ?>
                    <div class="card bg-dark border-success mb-4">
                        <div class="card-header bg-success text-white">
                            <h4 class="mb-0"><i class="fas fa-check-circle me-2"></i>সক্রিয় সাবস্ক্রিপশন</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="text-white"><strong class="text-white">প্ল্যান:</strong> <?php echo isset($active_subscription['plan_name']) ? $active_subscription['plan_name'] : 'N/A'; ?></p>
                                    <p class="text-white"><strong class="text-white">মূল্য:</strong> ৳<?php echo isset($active_subscription['price']) ? number_format($active_subscription['price'], 0) : '0'; ?></p>
                                    <p class="text-white"><strong class="text-white">মেয়াদ:</strong> <?php echo isset($active_subscription['duration_months']) ? $active_subscription['duration_months'] . ' মাস' : 'N/A'; ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p class="text-white"><strong class="text-white">শুরুর তারিখ:</strong> <?php echo isset($active_subscription['start_date']) ? date('d M Y', strtotime($active_subscription['start_date'])) : 'N/A'; ?></p>
                                    <p class="text-white"><strong class="text-white">শেষ তারিখ:</strong> <?php echo isset($active_subscription['end_date']) ? date('d M Y', strtotime($active_subscription['end_date'])) : 'N/A'; ?></p>
                                    <p class="text-white"><strong class="text-white">স্ট্যাটাস:</strong> <span class="badge bg-success">সক্রিয়</span></p>
                                </div>
                            </div>
                            <div class="text-center mt-3">
                                <a href="<?php echo SITE_URL; ?>/premium_content.php" class="btn btn-success">
                                    <i class="fas fa-crown me-2"></i>প্রিমিয়াম কন্টেন্ট দেখুন
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Pending Subscription -->
                    <?php if($pending_subscription): ?>
                    <div class="card bg-dark border-warning mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h4 class="mb-0"><i class="fas fa-clock me-2"></i>পেন্ডিং সাবস্ক্রিপশন</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="text-white"><strong class="text-white">প্ল্যান:</strong> <?php echo isset($pending_subscription['plan_name']) ? $pending_subscription['plan_name'] : 'N/A'; ?></p>
                                    <p class="text-white"><strong class="text-white">মূল্য:</strong> ৳<?php echo isset($pending_subscription['price']) ? number_format($pending_subscription['price'], 0) : '0'; ?></p>
                                    <p class="text-white"><strong class="text-white">মেয়াদ:</strong> <?php echo isset($pending_subscription['duration_months']) ? $pending_subscription['duration_months'] . ' মাস' : 'N/A'; ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p class="text-white"><strong class="text-white">পেমেন্ট মেথড:</strong> <?php echo isset($pending_subscription['payment_method']) ? ucfirst($pending_subscription['payment_method']) : 'N/A'; ?></p>
                                    <p class="text-white"><strong class="text-white">ট্রানজেকশন আইডি:</strong> <?php echo isset($pending_subscription['transaction_id']) ? $pending_subscription['transaction_id'] : 'N/A'; ?></p>
                                    <p class="text-white"><strong class="text-white">পেমেন্ট তারিখ:</strong> <?php echo isset($pending_subscription['payment_date']) ? date('d M Y H:i', strtotime($pending_subscription['payment_date'])) : 'N/A'; ?></p>
                                    <p class="text-white"><strong class="text-white">স্ট্যাটাস:</strong> <span class="badge bg-warning text-dark">পেন্ডিং</span></p>
                                </div>
                            </div>
                            <div class="alert alert-info bg-dark border-info mt-3">
                                <i class="fas fa-info-circle me-2"></i><span class="text-white">আপনার পেমেন্ট পেন্ডিং অবস্থায় আছে। এডমিন যাচাইয়ের পর আপনার প্রিমিয়াম অ্যাকসেস সক্রিয় করা হবে।</span>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Payment History -->
                    <div class="card bg-dark border-secondary">
                        <div class="card-header bg-secondary text-white">
                            <h4 class="mb-0"><i class="fas fa-history me-2"></i>পেমেন্ট হিস্টোরি</h4>
                        </div>
                        <div class="card-body">
                            <?php if(mysqli_num_rows($payments_result) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-dark table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>তারিখ</th>
                                            <th>পেমেন্ট মেথড</th>
                                            <th>ট্রানজেকশন আইডি</th>
                                            <th>মূল্য</th>
                                            <th>স্ট্যাটাস</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if($payments_result && mysqli_num_rows($payments_result) > 0): while($payment = mysqli_fetch_assoc($payments_result)):
                                            // Get plan details
                                            $plan = null;
                                            if (isset($payment['plan_id']) && $payment['plan_id']) {
                                                $plan_query = "SELECT * FROM plans WHERE id = " . $payment['plan_id'];
                                                $plan_result = mysqli_query($conn, $plan_query);
                                                if ($plan_result && mysqli_num_rows($plan_result) > 0) {
                                                    $plan = mysqli_fetch_assoc($plan_result);
                                                }
                                            }
                                        ?>
                                        <tr>
                                            <td><?php echo date('d M Y H:i', strtotime($payment['created_at'])); ?></td>
                                            <td><?php echo ucfirst($payment['payment_method']); ?></td>
                                            <td><?php echo $payment['transaction_id']; ?></td>
                                            <td>৳<?php echo $plan ? number_format($plan['price'], 0) : '0'; ?></td>
                                            <td>
                                                <?php if($payment['payment_status'] == 'completed'): ?>
                                                <span class="badge bg-success">সম্পন্ন</span>
                                                <?php elseif($payment['payment_status'] == 'pending'): ?>
                                                <span class="badge bg-warning text-dark">পেন্ডিং</span>
                                                <?php else: ?>
                                                <span class="badge bg-danger">বাতিল</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endwhile; endif; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="alert alert-info bg-dark border-info">
                                <i class="fas fa-info-circle me-2"></i><span class="text-white">আপনার কোন পেমেন্ট হিস্টোরি নেই।</span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
