<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Database connection
$db_host = 'localhost';
$db_user = 'tipsbdxy_4525';
$db_pass = 'tipsbdxy_4525';
$db_name = 'tipsbdxy_4525';

// Create connection
$conn = mysqli_connect($db_host, $db_user, $db_pass, $db_name);

// Check connection
if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// Check if status column exists in movies table
$check_movies_column = "SHOW COLUMNS FROM movies LIKE 'status'";
$movies_column_result = mysqli_query($conn, $check_movies_column);

// Check if status column exists in tvshows table
$check_tvshows_column = "SHOW COLUMNS FROM tvshows LIKE 'status'";
$tvshows_column_result = mysqli_query($conn, $check_tvshows_column);

$success_message = '';
$error_message = '';

// Add status column to movies table if it doesn't exist
if (mysqli_num_rows($movies_column_result) == 0) {
    $add_movies_column = "ALTER TABLE movies ADD COLUMN status ENUM('active', 'inactive') DEFAULT 'active'";
    if (mysqli_query($conn, $add_movies_column)) {
        $success_message .= "Added status column to movies table.\n";

        // Update existing movies to be active
        $update_movies = "UPDATE movies SET status = 'active' WHERE status IS NULL";
        if (mysqli_query($conn, $update_movies)) {
            $success_message .= "Updated existing movies to be active.\n";
        } else {
            $error_message .= "Error updating movies: " . mysqli_error($conn) . "\n";
        }
    } else {
        $error_message .= "Error adding status column to movies table: " . mysqli_error($conn) . "\n";
    }
} else {
    $success_message .= "status column already exists in movies table.\n";
}

// Add status column to tvshows table if it doesn't exist
if (mysqli_num_rows($tvshows_column_result) == 0) {
    $add_tvshows_column = "ALTER TABLE tvshows ADD COLUMN status ENUM('active', 'inactive') DEFAULT 'active'";
    if (mysqli_query($conn, $add_tvshows_column)) {
        $success_message .= "Added status column to tvshows table.\n";

        // Update existing tvshows to be active
        $update_tvshows = "UPDATE tvshows SET status = 'active' WHERE status IS NULL";
        if (mysqli_query($conn, $update_tvshows)) {
            $success_message .= "Updated existing TV shows to be active.\n";
        } else {
            $error_message .= "Error updating TV shows: " . mysqli_error($conn) . "\n";
        }
    } else {
        $error_message .= "Error adding status column to tvshows table: " . mysqli_error($conn) . "\n";
    }
} else {
    $success_message .= "status column already exists in tvshows table.\n";
}

// Return response
if (!empty($error_message)) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $error_message,
        'data' => null
    ]);
} else {
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'message' => 'Success: ' . $success_message,
        'data' => null
    ]);
}
?>
