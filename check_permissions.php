<?php
require_once 'includes/header.php';

// Display header information
echo "<h1>User Permissions Check</h1>";
echo "<p>This page helps diagnose permission issues with the website.</p>";

// Display user information
echo "<h2>User Information</h2>";
echo "<ul>";
echo "<li>User logged in: " . (isLoggedIn() ? 'Yes' : 'No') . "</li>";
echo "<li>User is admin: " . (isAdmin() ? 'Yes' : 'No') . "</li>";
echo "<li>User is premium: " . ($is_premium ? 'Yes' : 'No') . "</li>";
echo "</ul>";

// Display session information
echo "<h2>Session Information</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Check database connection
echo "<h2>Database Connection</h2>";
if ($conn) {
    echo "<p>Database connection is working.</p>";
    
    // Check if user exists in database
    if (isLoggedIn()) {
        $user_id = $_SESSION['user_id'];
        $user_query = "SELECT * FROM users WHERE id = $user_id";
        $user_result = mysqli_query($conn, $user_query);
        
        if (mysqli_num_rows($user_result) > 0) {
            $user = mysqli_fetch_assoc($user_result);
            echo "<h3>User Details from Database</h3>";
            echo "<ul>";
            echo "<li>Username: " . $user['username'] . "</li>";
            echo "<li>Email: " . $user['email'] . "</li>";
            echo "<li>Role: " . $user['role'] . "</li>";
            echo "<li>Is Premium: " . ($user['is_premium'] ? 'Yes' : 'No') . "</li>";
            echo "</ul>";
        } else {
            echo "<p>User not found in database!</p>";
        }
        
        // Check user's subscriptions
        $subscription_query = "SELECT * FROM subscriptions WHERE user_id = $user_id ORDER BY created_at DESC";
        $subscription_result = mysqli_query($conn, $subscription_query);
        
        echo "<h3>User Subscriptions</h3>";
        if (mysqli_num_rows($subscription_result) > 0) {
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>ID</th><th>Package</th><th>Status</th><th>Start Date</th><th>End Date</th></tr>";
            
            while ($subscription = mysqli_fetch_assoc($subscription_result)) {
                echo "<tr>";
                echo "<td>" . $subscription['id'] . "</td>";
                echo "<td>" . $subscription['package_id'] . "</td>";
                echo "<td>" . $subscription['status'] . "</td>";
                echo "<td>" . $subscription['start_date'] . "</td>";
                echo "<td>" . $subscription['end_date'] . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        } else {
            echo "<p>No subscriptions found.</p>";
        }
    }
} else {
    echo "<p>Database connection failed!</p>";
}

// Check for episodes in a TV show
echo "<h2>TV Show Episodes Check</h2>";
echo "<p>Checking for episodes in a sample TV show:</p>";

// Get a sample TV show
$tvshow_query = "SELECT * FROM tvshows ORDER BY id ASC LIMIT 1";
$tvshow_result = mysqli_query($conn, $tvshow_query);

if (mysqli_num_rows($tvshow_result) > 0) {
    $tvshow = mysqli_fetch_assoc($tvshow_result);
    echo "<p>Sample TV Show: " . $tvshow['title'] . " (ID: " . $tvshow['id'] . ")</p>";
    
    // Check for seasons
    $seasons_query = "SELECT DISTINCT season_number FROM episodes WHERE tvshow_id = {$tvshow['id']} ORDER BY season_number ASC";
    $seasons_result = mysqli_query($conn, $seasons_query);
    
    if (mysqli_num_rows($seasons_result) > 0) {
        echo "<p>Seasons found: " . mysqli_num_rows($seasons_result) . "</p>";
        
        // Get first season
        $season = mysqli_fetch_assoc($seasons_result);
        $season_number = $season['season_number'];
        
        // Check for episodes in this season
        $episodes_query = "SELECT * FROM episodes WHERE tvshow_id = {$tvshow['id']} AND season_number = $season_number ORDER BY episode_number ASC";
        $episodes_result = mysqli_query($conn, $episodes_query);
        
        if (mysqli_num_rows($episodes_result) > 0) {
            echo "<p>Episodes found in Season $season_number: " . mysqli_num_rows($episodes_result) . "</p>";
            
            // Get first episode
            $episode = mysqli_fetch_assoc($episodes_result);
            echo "<p>Sample Episode: " . $episode['title'] . " (ID: " . $episode['id'] . ")</p>";
            
            // Check for links for this episode
            $links_query = "SELECT * FROM episode_links WHERE episode_id = {$episode['id']} ORDER BY quality DESC";
            $links_result = mysqli_query($conn, $links_query);
            
            if (mysqli_num_rows($links_result) > 0) {
                echo "<p>Links found for this episode: " . mysqli_num_rows($links_result) . "</p>";
                
                echo "<table border='1' cellpadding='5'>";
                echo "<tr><th>ID</th><th>Quality</th><th>Link Type</th><th>Is Premium</th><th>Server Name</th></tr>";
                
                while ($link = mysqli_fetch_assoc($links_result)) {
                    echo "<tr>";
                    echo "<td>" . $link['id'] . "</td>";
                    echo "<td>" . $link['quality'] . "</td>";
                    echo "<td>" . $link['link_type'] . "</td>";
                    echo "<td>" . ($link['is_premium'] ? 'Yes' : 'No') . "</td>";
                    echo "<td>" . $link['server_name'] . "</td>";
                    echo "</tr>";
                }
                
                echo "</table>";
            } else {
                echo "<p>No links found for this episode!</p>";
            }
        } else {
            echo "<p>No episodes found in Season $season_number!</p>";
        }
    } else {
        echo "<p>No seasons found for this TV show!</p>";
    }
} else {
    echo "<p>No TV shows found in the database!</p>";
}

// Check for download links in a movie
echo "<h2>Movie Download Links Check</h2>";
echo "<p>Checking for download links in a sample movie:</p>";

// Get a sample movie
$movie_query = "SELECT * FROM movies ORDER BY id ASC LIMIT 1";
$movie_result = mysqli_query($conn, $movie_query);

if (mysqli_num_rows($movie_result) > 0) {
    $movie = mysqli_fetch_assoc($movie_result);
    echo "<p>Sample Movie: " . $movie['title'] . " (ID: " . $movie['id'] . ")</p>";
    
    // Check for download links
    $download_query = "SELECT * FROM download_links WHERE content_type = 'movie' AND content_id = {$movie['id']} ORDER BY quality DESC";
    $download_result = mysqli_query($conn, $download_query);
    
    if (mysqli_num_rows($download_result) > 0) {
        echo "<p>Download links found: " . mysqli_num_rows($download_result) . "</p>";
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Quality</th><th>Link Type</th><th>Is Premium</th><th>Server Name</th></tr>";
        
        while ($download = mysqli_fetch_assoc($download_result)) {
            echo "<tr>";
            echo "<td>" . $download['id'] . "</td>";
            echo "<td>" . $download['quality'] . "</td>";
            echo "<td>" . $download['link_type'] . "</td>";
            echo "<td>" . ($download['is_premium'] ? 'Yes' : 'No') . "</td>";
            echo "<td>" . $download['server_name'] . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>No download links found for this movie!</p>";
    }
} else {
    echo "<p>No movies found in the database!</p>";
}

require_once 'includes/footer.php';
?>
