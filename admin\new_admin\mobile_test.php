<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set page title
$page_title = 'মোবাইল ডিজাইন টেস্ট';
$current_page = 'mobile_test.php';

// Start session
session_start();

// Set test session data
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';
$_SESSION['user_role'] = 'admin';
$_SESSION['is_premium'] = 1;

// Include header
include 'includes/header.php';
?>

<!-- Include Sidebar -->
<?php include 'includes/sidebar.php'; ?>

<!-- Main Content -->
<div class="main-content">
    <!-- Page Header -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-title">
                    <i class="fas fa-mobile-alt me-3"></i>মোবাইল ডিজাইন টেস্ট
                </h1>
                <p class="page-subtitle text-muted">মোবাইল-স্পেসিফিক ডিজাইন এবং ফিচার টেস্ট</p>
            </div>
            <div class="col-auto">
                <div class="page-actions">
                    <button class="btn btn-primary" onclick="testMobileFeatures()">
                        <i class="fas fa-play me-2"></i>মোবাইল টেস্ট
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Device Info -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-info-circle me-2"></i>ডিভাইস তথ্য
            </h5>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <div class="text-center p-3 bg-dark rounded">
                        <div class="h4 text-primary mb-2" id="screenWidth">-</div>
                        <div class="text-muted">স্ক্রিন প্রস্থ</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center p-3 bg-dark rounded">
                        <div class="h4 text-success mb-2" id="screenHeight">-</div>
                        <div class="text-muted">স্ক্রিন উচ্চতা</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center p-3 bg-dark rounded">
                        <div class="h4 text-warning mb-2" id="deviceType">-</div>
                        <div class="text-muted">ডিভাইস টাইপ</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center p-3 bg-dark rounded">
                        <div class="h4 text-info mb-2" id="orientation">-</div>
                        <div class="text-muted">অরিয়েন্টেশন</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Stats Demo -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stat-card bg-gradient-primary">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number">1,234</h3>
                                <p class="stat-label">মোট ইউজার</p>
                                <small class="stat-sublabel">
                                    <i class="fas fa-arrow-up me-1"></i>+12%
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stat-card bg-gradient-success">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-film"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number">567</h3>
                                <p class="stat-label">মুভি</p>
                                <small class="stat-sublabel">
                                    <i class="fas fa-plus me-1"></i>নতুন
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stat-card bg-gradient-warning">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-crown"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number">89</h3>
                                <p class="stat-label">প্রিমিয়াম</p>
                                <small class="stat-sublabel">
                                    <i class="fas fa-star me-1"></i>অ্যাক্টিভ
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stat-card bg-gradient-info">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number">৳45,678</h3>
                                <p class="stat-label">রেভিনিউ</p>
                                <small class="stat-sublabel">
                                    <i class="fas fa-chart-line me-1"></i>এই মাস
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Form Test -->
    <div class="row mb-4">
        <div class="col-xl-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>মোবাইল ফর্ম টেস্ট
                    </h5>
                </div>
                <div class="card-body">
                    <form>
                        <div class="row g-3">
                            <div class="col-12">
                                <label class="form-label">নাম</label>
                                <input type="text" class="form-control" placeholder="আপনার নাম লিখুন">
                            </div>
                            <div class="col-12">
                                <label class="form-label">ইমেইল</label>
                                <input type="email" class="form-control" placeholder="<EMAIL>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">ক্যাটাগরি</label>
                                <select class="form-select">
                                    <option>ক্যাটাগরি নির্বাচন করুন</option>
                                    <option>অ্যাকশন</option>
                                    <option>কমেডি</option>
                                    <option>ড্রামা</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">স্ট্যাটাস</label>
                                <select class="form-select">
                                    <option>স্ট্যাটাস নির্বাচন করুন</option>
                                    <option>অ্যাক্টিভ</option>
                                    <option>ইনঅ্যাক্টিভ</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label class="form-label">বিবরণ</label>
                                <textarea class="form-control" rows="3" placeholder="বিস্তারিত লিখুন..."></textarea>
                            </div>
                            <div class="col-12">
                                <div class="btn-group w-100">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>সেভ করুন
                                    </button>
                                    <button type="reset" class="btn btn-outline-secondary">
                                        <i class="fas fa-undo me-2"></i>রিসেট
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Mobile Button Test -->
        <div class="col-xl-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-mouse-pointer me-2"></i>মোবাইল বাটন টেস্ট
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <button class="btn btn-primary btn-lg">
                            <i class="fas fa-plus me-2"></i>প্রাইমারি বাটন
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-check me-2"></i>সাকসেস বাটন
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>ওয়ার্নিং বাটন
                        </button>
                        <button class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>ডেঞ্জার বাটন
                        </button>
                        <button class="btn btn-outline-info">
                            <i class="fas fa-info-circle me-2"></i>আউটলাইন বাটন
                        </button>
                        
                        <div class="btn-group w-100">
                            <button class="btn btn-outline-primary">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-success">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-danger">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Table Test -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-table me-2"></i>মোবাইল টেবিল টেস্ট
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>নাম</th>
                                    <th>ইমেইল</th>
                                    <th>স্ট্যাটাস</th>
                                    <th>তারিখ</th>
                                    <th>অ্যাকশন</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td data-label="নাম">জন ডো</td>
                                    <td data-label="ইমেইল"><EMAIL></td>
                                    <td data-label="স্ট্যাটাস"><span class="badge bg-success">অ্যাক্টিভ</span></td>
                                    <td data-label="তারিখ">২৫/১২/২০২৩</td>
                                    <td data-label="অ্যাকশন">
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-outline-danger"><i class="fas fa-trash"></i></button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td data-label="নাম">জেন স্মিথ</td>
                                    <td data-label="ইমেইল"><EMAIL></td>
                                    <td data-label="স্ট্যাটাস"><span class="badge bg-warning">পেন্ডিং</span></td>
                                    <td data-label="তারিখ">২৪/১২/২০২৩</td>
                                    <td data-label="অ্যাকশন">
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-outline-danger"><i class="fas fa-trash"></i></button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Page-specific scripts
$page_scripts = '
<script>
function updateDeviceInfo() {
    const width = window.innerWidth;
    const height = window.innerHeight;
    
    document.getElementById("screenWidth").textContent = width + "px";
    document.getElementById("screenHeight").textContent = height + "px";
    
    // Determine device type
    let deviceType = "";
    if (width < 576) {
        deviceType = "মোবাইল";
    } else if (width < 768) {
        deviceType = "বড় মোবাইল";
    } else if (width < 992) {
        deviceType = "ট্যাবলেট";
    } else if (width < 1200) {
        deviceType = "ডেস্কটপ";
    } else {
        deviceType = "বড় ডেস্কটপ";
    }
    
    document.getElementById("deviceType").textContent = deviceType;
    
    // Determine orientation
    const orientation = width > height ? "ল্যান্ডস্কেপ" : "পোর্ট্রেট";
    document.getElementById("orientation").textContent = orientation;
}

function testMobileFeatures() {
    // Test mobile navigation
    if (window.innerWidth < 992) {
        cinepixAdmin.showToast("মোবাইল নেভিগেশন টেস্ট - হ্যামবার্গার মেনু ক্লিক করুন!", "info");
    } else {
        cinepixAdmin.showToast("ডেস্কটপ ভিউ - ব্রাউজার রিসাইজ করে মোবাইল ভিউ টেস্ট করুন", "warning");
    }
    
    // Test responsive features
    setTimeout(() => {
        cinepixAdmin.showToast("রেস্পন্সিভ ফিচার সফলভাবে কাজ করছে!", "success");
    }, 1000);
}

// Initialize
document.addEventListener("DOMContentLoaded", function() {
    updateDeviceInfo();
    
    // Update on resize
    window.addEventListener("resize", updateDeviceInfo);
    
    // Test orientation change
    window.addEventListener("orientationchange", function() {
        setTimeout(updateDeviceInfo, 100);
    });
});
</script>
';

// Include footer
include 'includes/footer.php';
?>
