<?php
/**
 * Adstera Ad Integration
 * This file contains all Adstera ad codes and configurations
 */

// Adstera Configuration
$adstera_config = [
    'publisher_id' => 'ca-pub-YOUR_PUBLISHER_ID', // Replace with your actual Publisher ID
    'enabled' => true, // Set to false to disable all ads
    'test_mode' => false, // Set to true for testing
    'ad_blocks' => [
        'header_banner' => [
            'slot' => 'YOUR_HEADER_AD_SLOT',
            'size' => '728x90',
            'responsive' => true
        ],
        'sidebar_rectangle' => [
            'slot' => 'YOUR_SIDEBAR_AD_SLOT',
            'size' => '300x250',
            'responsive' => true
        ],
        'content_middle' => [
            'slot' => 'YOUR_CONTENT_AD_SLOT',
            'size' => '300x250',
            'responsive' => true
        ],
        'footer_banner' => [
            'slot' => 'YOUR_FOOTER_AD_SLOT',
            'size' => '728x90',
            'responsive' => true
        ],
        'mobile_banner' => [
            'slot' => 'YOUR_MOBILE_AD_SLOT',
            'size' => '320x50',
            'responsive' => true
        ]
    ]
];

/**
 * Generate Adstera ad code
 */
function generateAdsteraAd($ad_block, $config) {
    if (!$config['enabled']) {
        return '';
    }
    
    $publisher_id = $config['publisher_id'];
    $slot = $config['ad_blocks'][$ad_block]['slot'];
    $size = $config['ad_blocks'][$ad_block]['size'];
    $responsive = $config['ad_blocks'][$ad_block]['responsive'];
    
    if ($config['test_mode']) {
        return generateTestAd($ad_block, $size);
    }
    
    $ad_code = '
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=' . $publisher_id . '"></script>
    <ins class="adsbygoogle"
         style="display:block"
         data-ad-client="' . $publisher_id . '"
         data-ad-slot="' . $slot . '"
         data-ad-format="auto"
         data-full-width-responsive="' . ($responsive ? 'true' : 'false') . '"></ins>
    <script>
         (adsbygoogle = window.adsbygoogle || []).push({});
    </script>';
    
    return $ad_code;
}

/**
 * Generate test ad placeholder
 */
function generateTestAd($ad_block, $size) {
    $colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'];
    $color = $colors[array_rand($colors)];
    
    return '
    <div class="test-ad" style="background: ' . $color . '; color: white; padding: 20px; text-align: center; border-radius: 10px; margin: 10px 0;">
        <h4>Test Ad: ' . ucfirst(str_replace('_', ' ', $ad_block)) . '</h4>
        <p>Size: ' . $size . '</p>
        <p>This is a test advertisement placeholder.</p>
    </div>';
}

/**
 * Display ad banner with proper styling
 */
function displayAdBanner($ad_block, $config, $position = '') {
    $ad_code = generateAdsteraAd($ad_block, $config);
    
    if (empty($ad_code)) {
        return '';
    }
    
    $position_class = $position ? ' ad-banner-' . $position : '';
    
    return '
    <div class="ad-banner' . $position_class . '">
        <div class="container">
            <div class="ad-placeholder">
                <div class="ad-label">Advertisement</div>
                <div class="ad-content">
                    ' . $ad_code . '
                </div>
            </div>
        </div>
    </div>';
}

/**
 * Display sidebar ad
 */
function displaySidebarAd($ad_block, $config) {
    $ad_code = generateAdsteraAd($ad_block, $config);
    
    if (empty($ad_code)) {
        return '';
    }
    
    return '
    <div class="sidebar-ad">
        <div class="ad-placeholder">
            <div class="ad-label">Advertisement</div>
            <div class="ad-content">
                ' . $ad_code . '
            </div>
        </div>
    </div>';
}

/**
 * Display in-content ad
 */
function displayInContentAd($ad_block, $config) {
    $ad_code = generateAdsteraAd($ad_block, $config);
    
    if (empty($ad_code)) {
        return '';
    }
    
    return '
    <div class="in-content-ad">
        <div class="ad-placeholder">
            <div class="ad-label">Advertisement</div>
            <div class="ad-content">
                ' . $ad_code . '
            </div>
        </div>
    </div>';
}

/**
 * Ad blocker detection script
 */
function getAdBlockerDetectionScript() {
    return '
    <script>
    // Ad Blocker Detection
    function detectAdBlocker() {
        let adBlockEnabled = false;
        const testAd = document.createElement("div");
        testAd.innerHTML = "&nbsp;";
        testAd.className = "adsbox";
        testAd.style.position = "absolute";
        testAd.style.left = "-9999px";
        document.body.appendChild(testAd);
        
        window.setTimeout(function() {
            if (testAd.offsetHeight === 0) {
                adBlockEnabled = true;
            }
            testAd.remove();
            
            if (adBlockEnabled) {
                showAdBlockerMessage();
            }
        }, 100);
    }
    
    function showAdBlockerMessage() {
        const message = document.createElement("div");
        message.innerHTML = `
            <div class="ad-blocker-message" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); z-index: 9999; max-width: 400px; text-align: center;">
                <h3 style="color: #e74c3c; margin-bottom: 15px;">Ad Blocker Detected</h3>
                <p style="color: #666; margin-bottom: 20px;">Please disable your ad blocker to support our site and continue enjoying free content.</p>
                <button onclick="this.parentElement.parentElement.remove()" style="background: #e74c3c; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">Close</button>
            </div>
        `;
        document.body.appendChild(message);
    }
    
    // Run detection on page load
    document.addEventListener("DOMContentLoaded", detectAdBlocker);
    </script>';
}

/**
 * Ad performance tracking script
 */
function getAdPerformanceTrackingScript() {
    return '
    <script>
    // Ad Performance Tracking
    function trackAdPerformance(adSlot) {
        const startTime = performance.now();
        
        // Track ad load time
        window.addEventListener("load", function() {
            const loadTime = performance.now() - startTime;
            console.log("Ad " + adSlot + " loaded in " + loadTime + "ms");
            
            // Send to analytics if available
            if (typeof gtag !== "undefined") {
                gtag("event", "ad_load_time", {
                    "ad_slot": adSlot,
                    "load_time": loadTime
                });
            }
        });
    }
    
    // Track all ads on page
    document.addEventListener("DOMContentLoaded", function() {
        const adElements = document.querySelectorAll(".ad-content");
        adElements.forEach(function(ad, index) {
            trackAdPerformance("ad_" + index);
        });
    });
    </script>';
}

/**
 * Lazy loading for ads
 */
function getAdLazyLoadingScript() {
    return '
    <script>
    // Lazy Loading for Ads
    document.addEventListener("DOMContentLoaded", function() {
        const adElements = document.querySelectorAll(".ad-content");
        
        const adObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // Load ad when it comes into view
                    loadAd(entry.target);
                    adObserver.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: "0px 0px -50px 0px"
        });
        
        adElements.forEach(ad => {
            adObserver.observe(ad);
        });
    });
    
    function loadAd(adElement) {
        // Adstera ad loading logic here
        if (typeof adsbygoogle !== "undefined") {
            (adsbygoogle = window.adsbygoogle || []).push({});
        }
    }
    </script>';
}

// Include ad blocker detection and performance tracking
echo getAdBlockerDetectionScript();
echo getAdPerformanceTrackingScript();
echo getAdLazyLoadingScript();
?> 