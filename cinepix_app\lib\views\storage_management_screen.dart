import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cinepix_app/constants/app_constants.dart';
import 'package:cinepix_app/services/storage_manager.dart';

class StorageManagementScreen extends StatefulWidget {
  const StorageManagementScreen({super.key});

  @override
  State<StorageManagementScreen> createState() => _StorageManagementScreenState();
}

class _StorageManagementScreenState extends State<StorageManagementScreen> {
  final StorageManager _storageManager = StorageManager();
  StorageInfo? _storageInfo;
  List<Map<String, dynamic>> _storageBreakdown = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadStorageInfo();
  }

  Future<void> _loadStorageInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final storageInfo = await _storageManager.getStorageInfo();
      final breakdown = await _storageManager.getStorageBreakdown();
      
      setState(() {
        _storageInfo = storageInfo;
        _storageBreakdown = breakdown;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      Get.snackbar(
        'Error',
        'Failed to load storage info: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _clearCache() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Cache'),
        content: const Text('Are you sure you want to clear all cached data?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('Clear', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _storageManager.clearAllCache();
        _loadStorageInfo();
        
        Get.snackbar(
          'Success',
          'Cache cleared successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } catch (e) {
        Get.snackbar(
          'Error',
          'Failed to clear cache: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  Future<void> _performCleanup() async {
    try {
      await _storageManager.performAutoCleanup();
      _loadStorageInfo();
      
      Get.snackbar(
        'Success',
        'Cleanup completed successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to perform cleanup: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Storage Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadStorageInfo,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _storageInfo == null
              ? const Center(child: Text('Failed to load storage information'))
              : RefreshIndicator(
                  onRefresh: _loadStorageInfo,
                  child: ListView(
                    padding: const EdgeInsets.all(16),
                    children: [
                      // Storage Overview Card
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Storage Overview',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                              const SizedBox(height: 16),
                              
                              // Storage bar
                              Container(
                                height: 20,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  color: Colors.grey[300],
                                ),
                                child: Stack(
                                  children: [
                                    Container(
                                      width: (MediaQuery.of(context).size.width - 64) * 
                                          (_storageInfo!.usedPercentage / 100),
                                      height: 20,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(10),
                                        color: _storageInfo!.usedPercentage > 80
                                            ? Colors.red
                                            : _storageInfo!.usedPercentage > 60
                                                ? Colors.orange
                                                : Colors.green,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              
                              const SizedBox(height: 12),
                              
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Used: ${_storageManager.formatBytes(_storageInfo!.usedSpace)}',
                                    style: const TextStyle(fontSize: 14),
                                  ),
                                  Text(
                                    'Free: ${_storageManager.formatBytes(_storageInfo!.freeSpace)}',
                                    style: const TextStyle(fontSize: 14),
                                  ),
                                ],
                              ),
                              
                              const SizedBox(height: 8),
                              
                              Text(
                                'Total: ${_storageManager.formatBytes(_storageInfo!.totalSpace)}',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Storage Breakdown
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Storage Breakdown',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                              const SizedBox(height: 16),
                              
                              ..._storageBreakdown.map((item) => Padding(
                                padding: const EdgeInsets.only(bottom: 12),
                                child: Row(
                                  children: [
                                    Container(
                                      width: 16,
                                      height: 16,
                                      decoration: BoxDecoration(
                                        color: item['color'],
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text(item['name']),
                                    ),
                                    Text(
                                      _storageManager.formatBytes(item['size']),
                                      style: const TextStyle(fontWeight: FontWeight.bold),
                                    ),
                                  ],
                                ),
                              )),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Quick Actions
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Quick Actions',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                              const SizedBox(height: 16),
                              
                              ListTile(
                                leading: const Icon(Icons.cleaning_services, color: Colors.blue),
                                title: const Text('Clear Cache'),
                                subtitle: Text('Free up ${_storageManager.formatBytes(_storageInfo!.cacheSize)}'),
                                trailing: const Icon(Icons.chevron_right),
                                onTap: _clearCache,
                              ),
                              
                              const Divider(),
                              
                              ListTile(
                                leading: const Icon(Icons.auto_fix_high, color: Colors.green),
                                title: const Text('Auto Cleanup'),
                                subtitle: const Text('Remove old downloads and cache'),
                                trailing: const Icon(Icons.chevron_right),
                                onTap: _performCleanup,
                              ),
                              
                              const Divider(),
                              
                              ListTile(
                                leading: const Icon(Icons.download, color: Colors.purple),
                                title: const Text('Manage Downloads'),
                                subtitle: Text('${_storageManager.formatBytes(_storageInfo!.downloadedVideosSize)} used'),
                                trailing: const Icon(Icons.chevron_right),
                                onTap: () => Get.toNamed('/downloads'),
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Warning if storage is low
                      if (_storageInfo!.usedPercentage > 80)
                        Card(
                          color: Colors.orange.withOpacity(0.1),
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Row(
                              children: [
                                Icon(Icons.warning, color: Colors.orange[700]),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Storage Warning',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: Colors.orange[700],
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      const Text(
                                        'Your storage is running low. Consider clearing cache or removing old downloads.',
                                        style: TextStyle(fontSize: 14),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
    );
  }
}
