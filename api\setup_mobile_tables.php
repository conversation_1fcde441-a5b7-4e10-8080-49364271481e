<?php
// Setup Mobile App Tables
require_once 'config.php';

// Create device_tokens table for push notifications
$create_device_tokens_table = "CREATE TABLE IF NOT EXISTS device_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    device_token VARCHAR(255) NOT NULL,
    device_type ENUM('android', 'ios') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_device_token (device_token)
)";

if (mysqli_query($conn, $create_device_tokens_table)) {
    echo "device_tokens table created successfully<br>";
} else {
    echo "Error creating device_tokens table: " . mysqli_error($conn) . "<br>";
}

// Add last_watched_position to watchlist table
$alter_watchlist_table = "ALTER TABLE watchlist 
    ADD COLUMN IF NOT EXISTS last_watched_position INT DEFAULT 0,
    ADD COLUMN IF NOT EXISTS last_watched_at TIMESTAMP NULL";

if (mysqli_query($conn, $alter_watchlist_table)) {
    echo "watchlist table updated successfully<br>";
} else {
    echo "Error updating watchlist table: " . mysqli_error($conn) . "<br>";
}

// Create app_config table
$create_app_config_table = "CREATE TABLE IF NOT EXISTS app_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL,
    config_value TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_config_key (config_key)
)";

if (mysqli_query($conn, $create_app_config_table)) {
    echo "app_config table created successfully<br>";
    
    // Insert default app config values
    $default_configs = [
        ['app_version', '1.0.0'],
        ['min_app_version', '1.0.0'],
        ['force_update', 'false'],
        ['update_message', 'Please update to the latest version for new features and bug fixes.'],
        ['maintenance_mode', 'false'],
        ['maintenance_message', 'We are currently performing maintenance. Please try again later.']
    ];
    
    foreach ($default_configs as $config) {
        $check_query = "SELECT * FROM app_config WHERE config_key = '{$config[0]}'";
        $result = mysqli_query($conn, $check_query);
        
        if (mysqli_num_rows($result) == 0) {
            $insert_query = "INSERT INTO app_config (config_key, config_value) VALUES ('{$config[0]}', '{$config[1]}')";
            mysqli_query($conn, $insert_query);
            echo "Inserted default config: {$config[0]}<br>";
        }
    }
} else {
    echo "Error creating app_config table: " . mysqli_error($conn) . "<br>";
}

// Create notifications table
$create_notifications_table = "CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    image_url VARCHAR(255) NULL,
    action_type ENUM('none', 'movie', 'tvshow', 'url') DEFAULT 'none',
    action_id VARCHAR(100) NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
)";

if (mysqli_query($conn, $create_notifications_table)) {
    echo "notifications table created successfully<br>";
} else {
    echo "Error creating notifications table: " . mysqli_error($conn) . "<br>";
}

echo "Mobile app tables setup completed!";
