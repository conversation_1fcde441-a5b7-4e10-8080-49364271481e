import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cinepix_app/constants/app_constants.dart';
import 'package:cinepix_app/controllers/review_controller.dart';
import 'package:cinepix_app/models/review.dart';
import 'package:cinepix_app/widgets/add_review.dart';

class ReviewList extends StatelessWidget {
  final String contentType;
  final int contentId;
  
  const ReviewList({
    super.key,
    required this.contentType,
    required this.contentId,
  });
  
  @override
  Widget build(BuildContext context) {
    final ReviewController reviewController = Get.find<ReviewController>();
    
    // Load reviews when widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      reviewController.loadReviews(contentType, contentId);
    });
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'রিভিউ',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton.icon(
              onPressed: () {
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: AppConstants.surfaceColor,
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  builder: (context) => AddReview(
                    contentType: contentType,
                    contentId: contentId,
                  ),
                );
              },
              icon: const Icon(Icons.add, size: 18),
              label: const Text('রিভিউ যোগ করুন'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        // Reviews list
        Obx(() {
          if (reviewController.isLoading.value) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator(),
              ),
            );
          }
          
          if (reviewController.reviews.isEmpty) {
            return Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppConstants.surfaceColor,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Text(
                  'এখনো কোন রিভিউ নেই। প্রথম রিভিউ দিন!',
                  style: TextStyle(
                    color: Colors.grey,
                  ),
                ),
              ),
            );
          }
          
          return ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: reviewController.reviews.length,
            itemBuilder: (context, index) {
              final review = reviewController.reviews[index];
              return _buildReviewCard(review);
            },
          );
        }),
      ],
    );
  }
  
  Widget _buildReviewCard(Review review) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User info and rating
            Row(
              children: [
                // User avatar
                CircleAvatar(
                  backgroundColor: AppConstants.primaryColor,
                  radius: 20,
                  child: Text(
                    review.username.isNotEmpty ? review.username[0].toUpperCase() : 'U',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                
                // Username and date
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        review.username,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        review.createdAt,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[400],
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Rating
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppConstants.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.star,
                        color: Colors.amber,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${review.rating}/10',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            // Review comment
            if (review.comment.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                review.comment,
                style: const TextStyle(
                  height: 1.5,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
