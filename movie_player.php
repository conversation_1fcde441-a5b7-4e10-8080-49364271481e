<?php
require_once 'includes/config.php';

// Check if movie ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect(SITE_URL);
}

$movie_id = (int)$_GET['id'];

// Get movie details
$query = "SELECT m.*, c.name as category_name FROM movies m
          LEFT JOIN categories c ON m.category_id = c.id
          WHERE m.id = $movie_id";
$result = mysqli_query($conn, $query);

// Check if movie exists
if (mysqli_num_rows($result) == 0) {
    redirect(SITE_URL);
}

$movie = mysqli_fetch_assoc($result);

// Check if movie is premium and user is not premium
if ($movie['premium_only'] && (!isLoggedIn() || !isPremiumUser())) {
    redirect(SITE_URL . '/premium.php');
}

// Check if movie has TMDB ID
if (empty($movie['tmdb_id'])) {
    // If no TMDB ID, check if there's a streaming link
    $stream_query = "SELECT * FROM streaming_links
                    WHERE content_type = 'movie' AND content_id = $movie_id
                    ORDER BY is_premium ASC, id ASC LIMIT 1";
    $stream_result = mysqli_query($conn, $stream_query);

    if (mysqli_num_rows($stream_result) > 0) {
        $stream = mysqli_fetch_assoc($stream_result);
        $video_url = $stream['stream_url'];
    } else {
        // No streaming link available
        redirect(SITE_URL . '/details.php?type=movie&id=' . $movie_id);
    }
} else {
    // Use TMDB ID to create vidzee.wtf URL
    $video_url = "https://vidzee.wtf/movie/" . $movie['tmdb_id'];
}

// Increment view count (if you have a views table)
// mysqli_query($conn, "UPDATE movies SET views = views + 1 WHERE id = $movie_id");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $movie['title']; ?> - <?php echo SITE_NAME; ?></title>
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo SITE_URL; ?>/assets/img/favicon.png" type="image/x-icon">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- JW Player CSS -->
    <style>
        .jwplayer {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
    </style>
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #e50914;
            --secondary-color: #141414;
            --text-color: #ffffff;
            --dark-bg: #000000;
            --card-bg: #181818;
            --border-color: #333333;
        }

        body {
            background-color: var(--dark-bg);
            color: var(--text-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
        }

        .navbar {
            background-color: rgba(0, 0, 0, 0.9);
            padding: 0.5rem 1rem;
        }

        .player-container {
            background-color: var(--dark-bg);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            margin-bottom: 2rem;
        }

        .plyr {
            --plyr-color-main: var(--primary-color);
            --plyr-video-control-color: var(--text-color);
            --plyr-video-control-background-hover: var(--primary-color);
            --plyr-audio-control-background-hover: var(--primary-color);
            --plyr-audio-control-color: var(--text-color);
            --plyr-menu-background: var(--secondary-color);
            --plyr-menu-color: var(--text-color);
            border-radius: 8px;
        }

        .movie-info {
            background-color: var(--card-bg);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .movie-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--text-color);
        }

        .movie-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1rem;
            color: #aaa;
            font-size: 0.9rem;
        }

        .movie-meta span {
            display: flex;
            align-items: center;
        }

        .movie-meta i {
            margin-right: 0.5rem;
        }

        .movie-description {
            margin-bottom: 1.5rem;
            line-height: 1.7;
            color: #ddd;
        }

        .badge-premium {
            background-color: var(--primary-color);
            color: white;
            padding: 0.3rem 0.6rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: 0.5rem;
        }

        .player-options {
            background-color: var(--card-bg);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .player-options h4 {
            margin-bottom: 1rem;
            font-size: 1.2rem;
            color: var(--text-color);
        }

        .player-options .btn {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            border-radius: 4px;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #c00812;
            border-color: #c00812;
        }

        .btn-outline-light:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .back-button {
            margin-bottom: 1.5rem;
        }

        .back-button .btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            font-weight: 500;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .back-button .btn:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .player-wrapper {
            position: relative;
            padding-top: 56.25%; /* 16:9 Aspect Ratio */
            height: 0;
            overflow: hidden;
            border-radius: 8px;
        }

        .player-wrapper iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 0;
        }

        .quality-selector {
            margin-bottom: 1rem;
        }

        .quality-selector .btn {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .quality-selector .btn.active {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        @media (max-width: 768px) {
            .movie-title {
                font-size: 1.5rem;
            }

            .movie-meta {
                font-size: 0.8rem;
                gap: 0.5rem;
            }

            .player-options .btn {
                font-size: 0.9rem;
                padding: 0.4rem 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php require_once 'includes/header.php'; ?>

    <!-- Main Content -->
    <main class="py-4">
        <div class="container">
            <!-- Back Button -->
            <div class="back-button">
                <a href="<?php echo SITE_URL; ?>/details.php?type=movie&id=<?php echo $movie_id; ?>" class="btn btn-outline-light">
                    <i class="fas fa-arrow-left"></i> Back to Movie Details
                </a>
            </div>

            <!-- Movie Title -->
            <div class="movie-info">
                <h1 class="movie-title">
                    <?php echo $movie['title']; ?>
                    <?php if($movie['premium_only']): ?>
                    <span class="badge-premium">PREMIUM</span>
                    <?php endif; ?>
                </h1>
                <div class="movie-meta">
                    <span><i class="fas fa-calendar-alt"></i> <?php echo $movie['release_year']; ?></span>
                    <span><i class="fas fa-clock"></i> <?php echo $movie['duration']; ?> min</span>
                    <span><i class="fas fa-star"></i> <?php echo number_format($movie['rating'], 1); ?></span>
                    <span><i class="fas fa-tag"></i> <?php echo $movie['category_name']; ?></span>
                </div>
                <p class="movie-description"><?php echo $movie['description']; ?></p>
            </div>

            <!-- Video Player -->
            <div class="player-container">
                <div class="player-wrapper">
                    <?php
                    // Generate streaming URL using the helper function
                    require_once 'includes/streaming_helper.php';

                    // Determine file extension
                    $file_extension = strtolower(pathinfo(parse_url($video_url, PHP_URL_PATH), PATHINFO_EXTENSION));

                    // Use Shaka Player for MKV, HLS (m3u8), and DASH (mpd) formats
                    if ($file_extension == 'mkv' || $file_extension == 'm3u8' || $file_extension == 'mpd') {
                        $player_type = 'shaka';
                    } elseif (stripos($video_url, 'workers.dev') !== false) {
                        $player_type = 'plyr';
                    } else {
                        $player_type = 'default';
                    }

                    $poster_url = SITE_URL . '/uploads/' . $movie["poster"];
                    $streaming_url = getStreamingUrl(
                        $video_url,
                        $movie['title'],
                        $poster_url,
                        $movie['premium_only'],
                        $player_type
                    );
                    ?>

                    <?php if ($player_type == 'shaka' || $player_type == 'plyr'): ?>
                    <!-- Use iframe for Shaka Player or Plyr Player -->
                    <iframe src="<?php echo $streaming_url; ?>" allowfullscreen frameborder="0"></iframe>
                    <?php else: ?>
                    <!-- Use JW Player for other formats -->
                    <div id="jwplayer-container"></div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Player Options -->
            <div class="player-options">
                <h4>External Players</h4>
                <div class="d-flex flex-wrap">
                    <button onclick="openVLC('<?php echo $video_url; ?>')" class="btn btn-outline-light">
                        <i class="fas fa-play-circle me-2"></i>VLC Player
                    </button>
                    <button onclick="openMX('<?php echo $video_url; ?>')" class="btn btn-outline-light">
                        <i class="fas fa-play-circle me-2"></i>MX Player
                    </button>
                    <button onclick="openPlayit('<?php echo $video_url; ?>')" class="btn btn-outline-light">
                        <i class="fas fa-play-circle me-2"></i>Playit Player
                    </button>
                    <button onclick="openKMPlayer('<?php echo $video_url; ?>')" class="btn btn-outline-light">
                        <i class="fas fa-play-circle me-2"></i>KM Player
                    </button>
                    <a href="<?php echo $video_url; ?>" class="btn btn-primary" target="_blank">
                        <i class="fas fa-download me-2"></i>Download
                    </a>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <?php require_once 'includes/footer.php'; ?>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- JW Player JS -->
    <script src="https://cdn.jwplayer.com/libraries/64HPbvSQorQcd52B8XFuhMtEoitbvY/EXJmMBfKcXZQU2Rnn.js"></script>

    <script>
        <?php if ($player_type == 'default'): ?>
        // Initialize JW Player only if we're using it
        const player = jwplayer('jwplayer-container').setup({
            file: '<?php echo $video_url; ?>',
            image: '<?php echo SITE_URL; ?>/uploads/<?php echo $movie["poster"]; ?>',
            title: '<?php echo $movie["title"]; ?>',
            width: '100%',
            aspectratio: '16:9',
            primary: 'html5',
            hlshtml: true,
            controls: true,
            autostart: false,
            playbackRateControls: true,
            sharing: {
                sites: ['facebook', 'twitter', 'email', 'linkedin']
            }
        });
        <?php endif; ?>

        // External player functions
        function openVLC(url) {
            window.location.href = `vlc://${url}`;
        }

        function openMX(url) {
            window.location.href = `intent:${url}#Intent;package=com.mxtech.videoplayer.ad;end`;
        }

        function openPlayit(url) {
            window.location.href = `playit://playerv2/video?url=${url}`;
        }

        function openKMPlayer(url) {
            window.location.href = `intent:${url}#Intent;package=com.kmplayer;end`;
        }
    </script>
</body>
</html>

