<?php
// Database connection
$host = 'localhost';
$user = 'root';
$password = '';
$database = 'tipsbdxy_4525';

$conn = mysqli_connect($host, $user, $password, $database);
if (!$conn) {
    die('Connection failed: ' . mysqli_connect_error());
}

// Add type column if it doesn't exist
$sql = "ALTER TABLE categories ADD COLUMN IF NOT EXISTS type ENUM('both', 'movie', 'tvshow') DEFAULT 'both'";
if (mysqli_query($conn, $sql)) {
    echo "Type column added successfully.\n";
} else {
    echo "Error adding type column: " . mysqli_error($conn) . "\n";
}

// Update existing categories to have 'both' type
$sql = "UPDATE categories SET type = 'both' WHERE type IS NULL";
if (mysqli_query($conn, $sql)) {
    echo "Existing categories updated successfully.\n";
} else {
    echo "Error updating existing categories: " . mysqli_error($conn) . "\n";
}

mysqli_close($conn);
echo "Done!";
?>
