<?php
// Include configuration file
require_once '../config.php';

// Include database connection
require_once '../db_connect.php';

// Include functions
require_once '../functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

// Get statistics
$stats = [
    'movies' => 0,
    'premium_movies' => 0,
    'tvshows' => 0,
    'premium_tvshows' => 0,
    'episodes' => 0,
    'premium_episodes' => 0,
    'users' => 0,
    'premium_users' => 0,
    'pending_payments' => 0,
    'completed_payments' => 0,
    'payments' => 0,
    'categories' => 0,
    'reviews' => 0,
    'revenue' => 0
];

// Get movies count
$movies_query = "SELECT COUNT(*) as count, SUM(CASE WHEN premium_only = TRUE THEN 1 ELSE 0 END) as premium_count FROM movies";
$movies_result = mysqli_query($conn, $movies_query);
if ($movies_result) {
    $movies_data = mysqli_fetch_assoc($movies_result);
    $stats['movies'] = $movies_data['count'];
    $stats['premium_movies'] = $movies_data['premium_count'];
}

// Get TV shows count
$tvshows_query = "SELECT COUNT(*) as count, SUM(CASE WHEN premium_only = TRUE THEN 1 ELSE 0 END) as premium_count FROM tvshows";
$tvshows_result = mysqli_query($conn, $tvshows_query);
if ($tvshows_result) {
    $tvshows_data = mysqli_fetch_assoc($tvshows_result);
    $stats['tvshows'] = $tvshows_data['count'];
    $stats['premium_tvshows'] = $tvshows_data['premium_count'];
}

// Get episodes count
$episodes_query = "SELECT COUNT(*) as count, SUM(CASE WHEN premium_only = TRUE THEN 1 ELSE 0 END) as premium_count FROM episodes";
$episodes_result = mysqli_query($conn, $episodes_query);
if ($episodes_result) {
    $episodes_data = mysqli_fetch_assoc($episodes_result);
    $stats['episodes'] = $episodes_data['count'];
    $stats['premium_episodes'] = $episodes_data['premium_count'];
}

// Get users count
$users_query = "SELECT COUNT(*) as count, SUM(CASE WHEN is_premium = TRUE THEN 1 ELSE 0 END) as premium_count FROM users";
$users_result = mysqli_query($conn, $users_query);
if ($users_result) {
    $users_data = mysqli_fetch_assoc($users_result);
    $stats['users'] = $users_data['count'];
    $stats['premium_users'] = $users_data['premium_count'];
}

// Get payments count
$payments_query = "SELECT 
                  COUNT(*) as count, 
                  SUM(CASE WHEN payment_status = 'pending' THEN 1 ELSE 0 END) as pending_count,
                  SUM(CASE WHEN payment_status = 'completed' THEN 1 ELSE 0 END) as completed_count,
                  SUM(CASE WHEN payment_status = 'completed' THEN amount ELSE 0 END) as revenue
                  FROM payments";
$payments_result = mysqli_query($conn, $payments_query);
if ($payments_result) {
    $payments_data = mysqli_fetch_assoc($payments_result);
    $stats['payments'] = $payments_data['count'];
    $stats['pending_payments'] = $payments_data['pending_count'];
    $stats['completed_payments'] = $payments_data['completed_count'];
    $stats['revenue'] = $payments_data['revenue'];
}

// Get categories count
$categories_query = "SELECT COUNT(*) as count FROM categories";
$categories_result = mysqli_query($conn, $categories_query);
if ($categories_result) {
    $stats['categories'] = mysqli_fetch_assoc($categories_result)['count'];
}

// Get reviews count
$reviews_query = "SELECT COUNT(*) as count FROM reviews";
$reviews_result = mysqli_query($conn, $reviews_query);
if ($reviews_result) {
    $stats['reviews'] = mysqli_fetch_assoc($reviews_result)['count'];
}

// Get recent activities
$activities_query = "SELECT al.*, u.username 
                    FROM activity_logs al
                    LEFT JOIN users u ON al.user_id = u.id
                    ORDER BY al.created_at DESC
                    LIMIT 10";
$activities_result = mysqli_query($conn, $activities_query);

// Get recent users
$recent_users_query = "SELECT * FROM users ORDER BY created_at DESC LIMIT 5";
$recent_users_result = mysqli_query($conn, $recent_users_query);

// Get recent payments
$recent_payments_query = "SELECT p.*, u.username 
                         FROM payments p
                         LEFT JOIN users u ON p.user_id = u.id
                         ORDER BY p.created_at DESC
                         LIMIT 5";
$recent_payments_result = mysqli_query($conn, $recent_payments_query);

// Set page title
$page_title = 'ড্যাশবোর্ড';

// Include header
require_once 'includes/header_dark.php';

// Include sidebar
require_once 'includes/sidebar_dark.php';
?>

<!-- Content Wrapper -->
<div class="content">
    <!-- Topbar -->
    <?php include 'includes/topbar_dark.php'; ?>

    <!-- Begin Page Content -->
    <div class="container-fluid px-4">
        <!-- Page Heading -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0 text-gray-800">ড্যাশবোর্ড</h1>
            <div>
                <a href="#" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
                    <i class="fas fa-download fa-sm text-white-50 me-1"></i> রিপোর্ট জেনারেট করুন
                </a>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-primary fw-bold">মুভি</h6>
                            <div class="icon-circle bg-primary-light">
                                <i class="fas fa-film text-primary"></i>
                            </div>
                        </div>
                        <div class="mb-1">
                            <h2 class="fw-bold mb-0"><?php echo $stats['movies']; ?></h2>
                            <p class="text-muted small">মোট মুভি</p>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-primary-light text-primary me-2"><?php echo $stats['premium_movies']; ?></span>
                            <span class="small text-muted">প্রিমিয়াম মুভি</span>
                        </div>
                    </div>
                    <div class="card-footer py-2">
                        <a href="movies.php" class="text-primary text-decoration-none small">
                            সব মুভি দেখুন <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-success fw-bold">টিভি সিরিজ</h6>
                            <div class="icon-circle bg-success-light">
                                <i class="fas fa-tv text-success"></i>
                            </div>
                        </div>
                        <div class="mb-1">
                            <h2 class="fw-bold mb-0"><?php echo $stats['tvshows']; ?></h2>
                            <p class="text-muted small">মোট টিভি সিরিজ</p>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success-light text-success me-2"><?php echo $stats['premium_tvshows']; ?></span>
                            <span class="small text-muted">প্রিমিয়াম টিভি সিরিজ</span>
                        </div>
                    </div>
                    <div class="card-footer py-2">
                        <a href="tvshows.php" class="text-success text-decoration-none small">
                            সব টিভি সিরিজ দেখুন <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-info fw-bold">ব্যবহারকারী</h6>
                            <div class="icon-circle bg-info-light">
                                <i class="fas fa-users text-info"></i>
                            </div>
                        </div>
                        <div class="mb-1">
                            <h2 class="fw-bold mb-0"><?php echo $stats['users']; ?></h2>
                            <p class="text-muted small">মোট ব্যবহারকারী</p>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-info-light text-info me-2"><?php echo $stats['premium_users']; ?></span>
                            <span class="small text-muted">প্রিমিয়াম ব্যবহারকারী</span>
                        </div>
                    </div>
                    <div class="card-footer py-2">
                        <a href="users.php" class="text-info text-decoration-none small">
                            সব ব্যবহারকারী দেখুন <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-warning fw-bold">রেভিনিউ</h6>
                            <div class="icon-circle bg-warning-light">
                                <i class="fas fa-money-bill-wave text-warning"></i>
                            </div>
                        </div>
                        <div class="mb-1">
                            <h2 class="fw-bold mb-0">৳<?php echo number_format($stats['revenue'], 0); ?></h2>
                            <p class="text-muted small">মোট রেভিনিউ</p>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-warning-light text-warning me-2"><?php echo $stats['completed_payments']; ?></span>
                            <span class="small text-muted">সম্পূর্ণ পেমেন্ট</span>
                        </div>
                    </div>
                    <div class="card-footer py-2">
                        <a href="payments.php" class="text-warning text-decoration-none small">
                            সব পেমেন্ট দেখুন <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Row -->
        <div class="row">
            <!-- Recent Activities -->
            <div class="col-lg-8 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">সাম্প্রতিক অ্যাক্টিভিটি</h6>
                        <a href="activity_logs.php" class="btn btn-sm btn-primary">
                            সব দেখুন
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="activity-timeline">
                            <?php if (mysqli_num_rows($activities_result) > 0): ?>
                                <?php while ($activity = mysqli_fetch_assoc($activities_result)): ?>
                                    <div class="activity-item d-flex mb-3">
                                        <div class="activity-icon me-3">
                                            <div class="icon-circle bg-<?php echo getActivityColor($activity['activity_type']); ?>-light">
                                                <i class="fas fa-<?php echo getActivityIcon($activity['activity_type']); ?> text-<?php echo getActivityColor($activity['activity_type']); ?>"></i>
                                            </div>
                                        </div>
                                        <div class="activity-content flex-grow-1">
                                            <div class="d-flex justify-content-between">
                                                <h6 class="mb-1"><?php echo htmlspecialchars($activity['username'] ?? 'Unknown User'); ?></h6>
                                                <small class="text-muted"><?php echo timeAgo($activity['created_at']); ?></small>
                                            </div>
                                            <p class="mb-0"><?php echo htmlspecialchars($activity['description']); ?></p>
                                            <small class="text-muted"><?php echo htmlspecialchars($activity['ip_address']); ?></small>
                                        </div>
                                    </div>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                    <p>কোন অ্যাক্টিভিটি নেই</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Users & Payments -->
            <div class="col-lg-4 mb-4">
                <!-- Recent Users -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">নতুন ব্যবহারকারী</h6>
                    </div>
                    <div class="card-body">
                        <?php if (mysqli_num_rows($recent_users_result) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover align-middle">
                                    <thead>
                                        <tr>
                                            <th>ব্যবহারকারী</th>
                                            <th>স্ট্যাটাস</th>
                                            <th>অ্যাকশন</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($user = mysqli_fetch_assoc($recent_users_result)): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="<?php echo !empty($user['profile_image']) ? SITE_URL . '/uploads/' . $user['profile_image'] : SITE_URL . '/assets/img/default-avatar.png'; ?>" class="rounded-circle me-2" width="32" height="32" alt="<?php echo htmlspecialchars($user['username']); ?>">
                                                        <div>
                                                            <div class="fw-bold"><?php echo htmlspecialchars($user['username']); ?></div>
                                                            <div class="small text-muted"><?php echo htmlspecialchars($user['email']); ?></div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php if ($user['is_premium']): ?>
                                                        <span class="badge bg-warning">প্রিমিয়াম</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">ফ্রি</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <a href="user_details.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <p>কোন নতুন ব্যবহারকারী নেই</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Recent Payments -->
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">সাম্প্রতিক পেমেন্ট</h6>
                    </div>
                    <div class="card-body">
                        <?php if (mysqli_num_rows($recent_payments_result) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover align-middle">
                                    <thead>
                                        <tr>
                                            <th>ব্যবহারকারী</th>
                                            <th>পরিমাণ</th>
                                            <th>স্ট্যাটাস</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($payment = mysqli_fetch_assoc($recent_payments_result)): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($payment['username'] ?? 'Unknown'); ?></td>
                                                <td>৳<?php echo number_format($payment['amount'], 0); ?></td>
                                                <td>
                                                    <?php if ($payment['payment_status'] == 'completed'): ?>
                                                        <span class="badge bg-success">সম্পূর্ণ</span>
                                                    <?php elseif ($payment['payment_status'] == 'pending'): ?>
                                                        <span class="badge bg-warning">অপেক্ষমান</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">বাতিল</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                                <p>কোন সাম্প্রতিক পেমেন্ট নেই</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">দ্রুত অ্যাকশন</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <!-- Content Management -->
                            <div class="col-lg-3 col-md-6">
                                <div class="card bg-gradient-primary text-white shadow">
                                    <div class="card-body">
                                        <div class="text-center">
                                            <i class="fas fa-photo-video fa-3x mb-3"></i>
                                            <h5 class="mb-3">কনটেন্ট ম্যানেজমেন্ট</h5>
                                            <div class="d-grid gap-2">
                                                <a href="add_movie.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-plus-circle me-1"></i> মুভি যোগ করুন
                                                </a>
                                                <a href="add_tvshow.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-plus-circle me-1"></i> টিভি সিরিজ যোগ করুন
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- User Management -->
                            <div class="col-lg-3 col-md-6">
                                <div class="card bg-gradient-success text-white shadow">
                                    <div class="card-body">
                                        <div class="text-center">
                                            <i class="fas fa-users fa-3x mb-3"></i>
                                            <h5 class="mb-3">ব্যবহারকারী ম্যানেজমেন্ট</h5>
                                            <div class="d-grid gap-2">
                                                <a href="users.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-user-cog me-1"></i> ব্যবহারকারী ম্যানেজ করুন
                                                </a>
                                                <a href="manage_premium.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-crown me-1"></i> প্রিমিয়াম ম্যানেজ করুন
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Management -->
                            <div class="col-lg-3 col-md-6">
                                <div class="card bg-gradient-warning text-white shadow">
                                    <div class="card-body">
                                        <div class="text-center">
                                            <i class="fas fa-credit-card fa-3x mb-3"></i>
                                            <h5 class="mb-3">পেমেন্ট ম্যানেজমেন্ট</h5>
                                            <div class="d-grid gap-2">
                                                <a href="payments.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-money-check-alt me-1"></i> পেমেন্ট ম্যানেজ করুন
                                                </a>
                                                <a href="premium_plans.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-list-alt me-1"></i> প্ল্যান ম্যানেজ করুন
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- System Management -->
                            <div class="col-lg-3 col-md-6">
                                <div class="card bg-gradient-info text-white shadow">
                                    <div class="card-body">
                                        <div class="text-center">
                                            <i class="fas fa-cogs fa-3x mb-3"></i>
                                            <h5 class="mb-3">সিস্টেম ম্যানেজমেন্ট</h5>
                                            <div class="d-grid gap-2">
                                                <a href="site_settings.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-cog me-1"></i> সাইট সেটিংস
                                                </a>
                                                <a href="backup.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-database me-1"></i> ব্যাকআপ ও রিস্টোর
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap core JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- DataTables -->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.9/js/responsive.bootstrap5.min.js"></script>
<!-- Custom scripts -->
<script src="assets/js/admin.js"></script>

<?php
// Helper function to get activity icon
function getActivityIcon($type) {
    switch ($type) {
        case 'login':
            return 'sign-in-alt';
        case 'logout':
            return 'sign-out-alt';
        case 'register':
            return 'user-plus';
        case 'update_profile':
            return 'user-edit';
        case 'add_movie':
            return 'film';
        case 'edit_movie':
            return 'edit';
        case 'delete_movie':
            return 'trash-alt';
        case 'add_tvshow':
            return 'tv';
        case 'edit_tvshow':
            return 'edit';
        case 'delete_tvshow':
            return 'trash-alt';
        case 'payment':
            return 'credit-card';
        case 'status_change':
            return 'toggle-on';
        case 'settings_update':
            return 'cog';
        default:
            return 'history';
    }
}

// Helper function to get activity color
function getActivityColor($type) {
    switch ($type) {
        case 'login':
        case 'register':
            return 'success';
        case 'logout':
            return 'secondary';
        case 'update_profile':
        case 'edit_movie':
        case 'edit_tvshow':
        case 'settings_update':
            return 'info';
        case 'add_movie':
        case 'add_tvshow':
            return 'primary';
        case 'delete_movie':
        case 'delete_tvshow':
            return 'danger';
        case 'payment':
            return 'warning';
        case 'status_change':
            return 'success';
        default:
            return 'primary';
    }
}
?>
