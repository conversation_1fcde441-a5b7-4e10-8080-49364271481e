$(document).ready(function() {
    // Navbar scroll effect
    $(window).scroll(function() {
        if ($(this).scrollTop() > 50) {
            $('.navbar').addClass('scrolled');
        } else {
            $('.navbar').removeClass('scrolled');
        }
    });

    // Custom Main Slider
    const slides = $('.slide');
    const totalSlides = slides.length;
    let currentSlide = 0;
    let slideInterval;

    // Create dots
    const dotsContainer = $('.slider-dots');
    for (let i = 0; i < totalSlides; i++) {
        dotsContainer.append(`<div class="slider-dot" data-slide="${i}"></div>`);
    }

    // Initialize slider
    function initSlider() {
        // Set first slide as active if none is active
        if (!$('.slide.active').length) {
            $(slides[currentSlide]).addClass('active');
        } else {
            // Find the active slide index
            slides.each(function(index) {
                if ($(this).hasClass('active')) {
                    currentSlide = index;
                    return false; // break the loop
                }
            });
        }

        // Update dots
        $(`.slider-dot[data-slide="${currentSlide}"]`).addClass('active');

        // Start autoplay
        startSlideInterval();
    }

    // Go to specific slide
    function goToSlide(index) {
        // Remove active class from current slide and dot
        $(slides[currentSlide]).removeClass('active');
        $(`.slider-dot[data-slide="${currentSlide}"]`).removeClass('active');

        // Update current slide index
        currentSlide = index;

        // Handle index bounds
        if (currentSlide < 0) {
            currentSlide = totalSlides - 1;
        } else if (currentSlide >= totalSlides) {
            currentSlide = 0;
        }

        // Add active class to new slide and dot
        $(slides[currentSlide]).addClass('active');
        $(`.slider-dot[data-slide="${currentSlide}"]`).addClass('active');
    }

    // Next slide
    function nextSlide() {
        goToSlide(currentSlide + 1);
    }

    // Previous slide
    function prevSlide() {
        goToSlide(currentSlide - 1);
    }

    // Start autoplay interval
    function startSlideInterval() {
        stopSlideInterval(); // Clear any existing interval
        slideInterval = setInterval(nextSlide, 7000);
    }

    // Stop autoplay interval
    function stopSlideInterval() {
        if (slideInterval) {
            clearInterval(slideInterval);
        }
    }

    // Event listeners
    $('.slider-next').on('click', function() {
        nextSlide();
        startSlideInterval(); // Reset interval
    });

    $('.slider-prev').on('click', function() {
        prevSlide();
        startSlideInterval(); // Reset interval
    });

    $('.slider-dot').on('click', function() {
        const slideIndex = $(this).data('slide');
        goToSlide(slideIndex);
        startSlideInterval(); // Reset interval
    });

    // Pause autoplay on hover
    $('.main-slider').hover(
        function() { stopSlideInterval(); },
        function() { startSlideInterval(); }
    );

    // Initialize the slider
    initSlider();

    // Initialize Top 10 Carousels
    // We're using custom scrolling for these, not Owl Carousel
    // Add horizontal scroll with mouse wheel for top 10 carousels
    $('.top-10-carousel').each(function() {
        $(this).on('wheel', function(e) {
            // Only prevent default if we're not at the end or beginning of the scroll
            const scrollLeft = $(this).scrollLeft();
            const maxScrollLeft = $(this)[0].scrollWidth - $(this).outerWidth();

            if (e.originalEvent.deltaY > 0 && scrollLeft < maxScrollLeft) {
                // Scrolling down and not at the end
                e.preventDefault();
                $(this).scrollLeft(scrollLeft + e.originalEvent.deltaY);
            } else if (e.originalEvent.deltaY < 0 && scrollLeft > 0) {
                // Scrolling up and not at the beginning
                e.preventDefault();
                $(this).scrollLeft(scrollLeft + e.originalEvent.deltaY);
            }
            // If we're at the end or beginning, don't prevent default so page scrolling works
        });
    });

    // Initialize Owl Carousel for featured content
    $('.featured-carousel').owlCarousel({
        loop: true,
        margin: 20,
        nav: true,
        dots: false,
        autoplay: true,
        autoplayTimeout: 5000,
        autoplayHoverPause: true,
        navText: [
            '<i class="fas fa-chevron-left"></i>',
            '<i class="fas fa-chevron-right"></i>'
        ],
        responsive: {
            0: {
                items: 1
            },
            576: {
                items: 2
            },
            768: {
                items: 3
            },
            992: {
                items: 4
            },
            1200: {
                items: 5
            }
        }
    });

    // Initialize Owl Carousel for movie categories
    $('.category-carousel').owlCarousel({
        loop: true,
        margin: 20,
        nav: true,
        dots: false,
        navText: [
            '<i class="fas fa-chevron-left"></i>',
            '<i class="fas fa-chevron-right"></i>'
        ],
        responsive: {
            0: {
                items: 1
            },
            576: {
                items: 2
            },
            768: {
                items: 3
            },
            992: {
                items: 4
            }
        }
    });

    // Add to watchlist functionality
    $('.add-to-watchlist').on('click', function(e) {
        e.preventDefault();

        const contentId = $(this).data('id');
        const contentType = $(this).data('type');

        // Get the site URL from a meta tag
        const siteUrl = $('meta[name="site-url"]').attr('content') || '';

        $.ajax({
            url: siteUrl + '/includes/add_to_watchlist.php',
            type: 'POST',
            data: {
                content_id: contentId,
                content_type: contentType
            },
            success: function(response) {
                const data = JSON.parse(response);
                if (data.success) {
                    alert('Added to your watchlist!');
                } else {
                    if (data.message === 'login_required') {
                        window.location.href = 'login.php';
                    } else if (data.message === 'already_in_watchlist') {
                        alert('This item is already in your watchlist!');
                    } else {
                        alert('Error adding to watchlist. Please try again.');
                    }
                }
            },
            error: function() {
                alert('Error adding to watchlist. Please try again.');
            }
        });
    });

    // Remove from watchlist functionality
    $('.remove-from-watchlist').on('click', function(e) {
        e.preventDefault();

        const watchlistId = $(this).data('id');

        if (confirm('Are you sure you want to remove this from your watchlist?')) {
            // Get the site URL from a meta tag
            const siteUrl = $('meta[name="site-url"]').attr('content') || '';

            $.ajax({
                url: siteUrl + '/includes/remove_from_watchlist.php',
                type: 'POST',
                data: {
                    watchlist_id: watchlistId
                },
                success: function(response) {
                    const data = JSON.parse(response);
                    if (data.success) {
                        // Remove the card from the UI
                        $('#watchlist-item-' + watchlistId).fadeOut(300, function() {
                            $(this).remove();

                            // Check if there are any items left
                            if ($('.watchlist-item').length === 0) {
                                $('#watchlist-container').html('<div class="alert alert-info">Your watchlist is empty.</div>');
                            }
                        });
                    } else {
                        alert('Error removing from watchlist. Please try again.');
                    }
                },
                error: function() {
                    alert('Error removing from watchlist. Please try again.');
                }
            });
        }
    });

    // Play trailer functionality
    $('.play-trailer').on('click', function(e) {
        e.preventDefault();

        const trailerUrl = $(this).data('trailer');

        // Create modal with trailer
        const modal = `
            <div class="modal fade" id="trailerModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg modal-dialog-centered">
                    <div class="modal-content bg-dark">
                        <div class="modal-header border-0">
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="trailer-container">
                                <iframe src="${trailerUrl}" allowfullscreen></iframe>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Append modal to body
        $('body').append(modal);

        // Show modal
        const trailerModal = new bootstrap.Modal(document.getElementById('trailerModal'));
        trailerModal.show();

        // Remove modal from DOM when hidden
        $('#trailerModal').on('hidden.bs.modal', function() {
            $(this).remove();
        });
    });

    // Submit review form
    $('#reviewForm').on('submit', function(e) {
        e.preventDefault();

        const formData = $(this).serialize();

        // Get the site URL from a meta tag
        const siteUrl = $('meta[name="site-url"]').attr('content') || '';

        $.ajax({
            url: siteUrl + '/includes/submit_review.php',
            type: 'POST',
            data: formData,
            success: function(response) {
                const data = JSON.parse(response);
                if (data.success) {
                    alert('Your review has been submitted!');
                    location.reload();
                } else {
                    if (data.message === 'login_required') {
                        window.location.href = 'login.php';
                    } else {
                        alert('Error submitting review: ' + data.message);
                    }
                }
            },
            error: function() {
                alert('Error submitting review. Please try again.');
            }
        });
    });

    // Profile image preview
    $('#profileImage').on('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#imagePreview').attr('src', e.target.result);
            }
            reader.readAsDataURL(file);
        }
    });

    // Category filter
    $('.category-filter').on('click', function(e) {
        e.preventDefault();

        const categoryId = $(this).data('category');

        // Add active class to clicked button and remove from others
        $('.category-filter').removeClass('active');
        $(this).addClass('active');

        if (categoryId === 'all') {
            // Show all movies/shows
            $('.movie-card').parent().show();
        } else {
            // Hide all movies/shows
            $('.movie-card').parent().hide();
            // Show only movies/shows with the selected category
            $('.movie-card[data-category="' + categoryId + '"]').parent().show();
        }
    });

    // Search autocomplete
    $('.search-input').on('keyup', function() {
        const query = $(this).val();

        if (query.length > 2) {
            // Get the site URL from a meta tag
            const siteUrl = $('meta[name="site-url"]').attr('content') || '';

            $.ajax({
                url: siteUrl + '/includes/search_autocomplete.php',
                type: 'GET',
                data: {
                    query: query
                },
                success: function(response) {
                    // Implementation depends on your autocomplete library
                    console.log(response);
                }
            });
        }
    });

    // Ensure chat icon is always visible and positioned correctly
    // But don't add any event handlers - those are in footer.php
    setTimeout(function() {
        if ($('#chatIcon').length) {
            $('#chatIcon').css({
                'display': 'flex',
                'right': '30px',
                'left': 'auto'
            });

            // Remove any jQuery event handlers to prevent duplicates
            $('#chatIcon').off('click');
            $('#closeChatBtn').off('click');
            $(document).off('click.chatOutside');
        }
    }, 1000);

    // Chat functionality is now handled in footer.php with vanilla JavaScript
});
