<?php
// Mark Notification as Read Endpoint
require_once __DIR__ . '/../config.php';

// <PERSON>le preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed. Use POST.',
        'data' => null
    ]);
    exit;
}

// Get request body
$request_body = file_get_contents('php://input');
$data = json_decode($request_body, true);

// Extract notification ID from request
$notification_id = $data['notification_id'] ?? null;

// Validate notification ID
if ($notification_id === null) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Missing required field: notification_id',
        'data' => null
    ]);
    exit;
}

// Check if notifications table exists
$check_table = "SHOW TABLES LIKE 'notifications'";
$result = mysqli_query($conn, $check_table);

if (mysqli_num_rows($result) == 0) {
    http_response_code(404);
    echo json_encode([
        'success' => false,
        'message' => 'Notifications table does not exist',
        'data' => null
    ]);
    exit;
}

// Update notification to mark as read
$query = "UPDATE notifications SET is_read = TRUE WHERE id = ?";
$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, 'i', $notification_id);

if (mysqli_stmt_execute($stmt)) {
    // Check if any rows were affected
    if (mysqli_stmt_affected_rows($stmt) > 0) {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'message' => 'Notification marked as read',
            'data' => null
        ]);
    } else {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'Notification not found',
            'data' => null
        ]);
    }
} else {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to mark notification as read: ' . mysqli_error($conn),
        'data' => null
    ]);
}

mysqli_stmt_close($stmt);
mysqli_close($conn);
