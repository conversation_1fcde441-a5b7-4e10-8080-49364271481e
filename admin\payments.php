<?php
// Set page title
$page_title = 'Payments';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Process form submissions
$success_message = '';
$error_message = '';

// Approve Payment
if (isset($_GET['approve']) && $_GET['approve'] > 0) {
    $payment_id = (int)$_GET['approve'];
    
    // Begin transaction
    mysqli_begin_transaction($conn);
    
    try {
        // Get payment details
        $payment_query = "SELECT * FROM payments WHERE id = $payment_id AND payment_status = 'pending'";
        $payment_result = mysqli_query($conn, $payment_query);
        
        if (mysqli_num_rows($payment_result) == 0) {
            throw new Exception('Payment not found or already processed.');
        }
        
        $payment = mysqli_fetch_assoc($payment_result);
        $user_id = $payment['user_id'];
        $subscription_id = $payment['subscription_id'];
        
        // Update payment status
        $update_payment_query = "UPDATE payments SET payment_status = 'completed', updated_at = NOW() WHERE id = $payment_id";
        $update_payment_result = mysqli_query($conn, $update_payment_query);
        
        if (!$update_payment_result) {
            throw new Exception('Error updating payment: ' . mysqli_error($conn));
        }
        
        // Update subscription status
        $update_subscription_query = "UPDATE subscriptions SET status = 'active', updated_at = NOW() WHERE id = $subscription_id";
        $update_subscription_result = mysqli_query($conn, $update_subscription_query);
        
        if (!$update_subscription_result) {
            throw new Exception('Error updating subscription: ' . mysqli_error($conn));
        }
        
        // Update user premium status
        $update_user_query = "UPDATE users SET is_premium = 1, updated_at = NOW() WHERE id = $user_id";
        $update_user_result = mysqli_query($conn, $update_user_query);
        
        if (!$update_user_result) {
            throw new Exception('Error updating user: ' . mysqli_error($conn));
        }
        
        // Commit transaction
        mysqli_commit($conn);
        $success_message = 'Payment approved successfully.';
    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($conn);
        $error_message = $e->getMessage();
    }
}

// Reject Payment
if (isset($_GET['reject']) && $_GET['reject'] > 0) {
    $payment_id = (int)$_GET['reject'];
    
    // Begin transaction
    mysqli_begin_transaction($conn);
    
    try {
        // Get payment details
        $payment_query = "SELECT * FROM payments WHERE id = $payment_id AND payment_status = 'pending'";
        $payment_result = mysqli_query($conn, $payment_query);
        
        if (mysqli_num_rows($payment_result) == 0) {
            throw new Exception('Payment not found or already processed.');
        }
        
        $payment = mysqli_fetch_assoc($payment_result);
        $subscription_id = $payment['subscription_id'];
        
        // Update payment status
        $update_payment_query = "UPDATE payments SET payment_status = 'rejected', updated_at = NOW() WHERE id = $payment_id";
        $update_payment_result = mysqli_query($conn, $update_payment_query);
        
        if (!$update_payment_result) {
            throw new Exception('Error updating payment: ' . mysqli_error($conn));
        }
        
        // Update subscription status
        $update_subscription_query = "UPDATE subscriptions SET status = 'cancelled', updated_at = NOW() WHERE id = $subscription_id";
        $update_subscription_result = mysqli_query($conn, $update_subscription_query);
        
        if (!$update_subscription_result) {
            throw new Exception('Error updating subscription: ' . mysqli_error($conn));
        }
        
        // Commit transaction
        mysqli_commit($conn);
        $success_message = 'Payment rejected successfully.';
    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($conn);
        $error_message = $e->getMessage();
    }
}

// Delete Payment
if (isset($_GET['delete']) && $_GET['delete'] > 0) {
    $payment_id = (int)$_GET['delete'];
    
    // Delete payment
    $delete_query = "DELETE FROM payments WHERE id = $payment_id";
    
    if (mysqli_query($conn, $delete_query)) {
        $success_message = 'Payment deleted successfully.';
    } else {
        $error_message = 'Error deleting payment: ' . mysqli_error($conn);
    }
}

// Filter parameters
$filter_status = isset($_GET['status']) ? sanitize($_GET['status']) : '';
$filter_method = isset($_GET['method']) ? sanitize($_GET['method']) : '';
$search_term = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$date_from = isset($_GET['date_from']) ? sanitize($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? sanitize($_GET['date_to']) : '';

// Build query with filters
$payments_query = "SELECT p.*, u.username, pp.name as plan_name, pp.price as plan_price, pp.duration as plan_duration
                  FROM payments p
                  JOIN users u ON p.user_id = u.id
                  LEFT JOIN subscriptions s ON p.subscription_id = s.id
                  LEFT JOIN premium_plans pp ON s.plan_id = pp.id
                  WHERE 1=1";

if (!empty($filter_status)) {
    $payments_query .= " AND p.payment_status = '$filter_status'";
}

if (!empty($filter_method)) {
    $payments_query .= " AND p.payment_method = '$filter_method'";
}

if (!empty($search_term)) {
    $payments_query .= " AND (u.username LIKE '%$search_term%' 
                        OR p.transaction_id LIKE '%$search_term%'
                        OR pp.name LIKE '%$search_term%')";
}

if (!empty($date_from)) {
    $payments_query .= " AND DATE(p.created_at) >= '$date_from'";
}

if (!empty($date_to)) {
    $payments_query .= " AND DATE(p.created_at) <= '$date_to'";
}

$payments_query .= " ORDER BY p.created_at DESC";
$payments_result = mysqli_query($conn, $payments_query);

// Get payment methods
$payment_methods_query = "SELECT DISTINCT payment_method FROM payments";
$payment_methods_result = mysqli_query($conn, $payment_methods_query);
$payment_methods = [];
while ($method = mysqli_fetch_assoc($payment_methods_result)) {
    $payment_methods[] = $method['payment_method'];
}

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Payments</h1>
            </div>
            <div class="topbar-actions">
                <div class="topbar-search">
                    <form action="payments.php" method="GET">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="Search payments..." name="search" value="<?php echo $search_term; ?>">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
                <a href="payment_settings.php" class="btn btn-primary">
                    <i class="fas fa-cog me-2"></i>Payment Settings
                </a>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <?php if($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>
        
        <?php if($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>
        
        <!-- Filter Section -->
        <div class="card shadow mb-4">
            <div class="card-header bg-white py-3">
                <h6 class="m-0 font-weight-bold text-primary">Filter Payments</h6>
            </div>
            <div class="card-body">
                <form method="GET" action="" class="row g-3">
                    <div class="col-md-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All</option>
                            <option value="pending" <?php echo $filter_status == 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="completed" <?php echo $filter_status == 'completed' ? 'selected' : ''; ?>>Completed</option>
                            <option value="rejected" <?php echo $filter_status == 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="method" class="form-label">Payment Method</label>
                        <select class="form-select" id="method" name="method">
                            <option value="">All</option>
                            <?php foreach($payment_methods as $method): ?>
                            <option value="<?php echo $method; ?>" <?php echo $filter_method == $method ? 'selected' : ''; ?>><?php echo ucfirst($method); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="date_from" class="form-label">Date From</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="date_to" class="form-label">Date To</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" value="<?php echo $search_term; ?>" placeholder="Username, Transaction ID...">
                    </div>
                    <div class="col-12 text-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter me-2"></i>Apply Filters
                        </button>
                        <a href="payments.php" class="btn btn-secondary">
                            <i class="fas fa-undo me-2"></i>Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Payments List -->
        <div class="card shadow">
            <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">All Payments</h6>
                <div>
                    <?php if($filter_status == 'pending' || empty($filter_status)): ?>
                    <span class="badge bg-warning text-dark fs-6 me-2">
                        <?php 
                        $pending_count_query = "SELECT COUNT(*) as count FROM payments WHERE payment_status = 'pending'";
                        $pending_count_result = mysqli_query($conn, $pending_count_query);
                        $pending_count = mysqli_fetch_assoc($pending_count_result)['count'];
                        echo $pending_count . ' Pending';
                        ?>
                    </span>
                    <?php endif; ?>
                    <span class="badge bg-success fs-6">
                        <?php 
                        $total_revenue_query = "SELECT SUM(amount) as total FROM payments WHERE payment_status = 'completed'";
                        $total_revenue_result = mysqli_query($conn, $total_revenue_query);
                        $total_revenue = mysqli_fetch_assoc($total_revenue_result)['total'] ?? 0;
                        echo '৳' . number_format($total_revenue, 0) . ' Revenue';
                        ?>
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover align-middle datatable">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>User</th>
                                <th>Plan</th>
                                <th>Amount</th>
                                <th>Method</th>
                                <th>Transaction ID</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if(mysqli_num_rows($payments_result) > 0): ?>
                                <?php while($payment = mysqli_fetch_assoc($payments_result)): ?>
                                <tr>
                                    <td><?php echo $payment['id']; ?></td>
                                    <td>
                                        <a href="users.php?edit=<?php echo $payment['user_id']; ?>" class="text-decoration-none fw-bold">
                                            <?php echo $payment['username']; ?>
                                        </a>
                                    </td>
                                    <td>
                                        <?php echo $payment['plan_name']; ?>
                                        <div class="small text-muted">
                                            <?php echo $payment['plan_duration']; ?> days
                                        </div>
                                    </td>
                                    <td>৳<?php echo number_format($payment['amount'], 0); ?></td>
                                    <td>
                                        <?php if($payment['payment_method'] == 'bkash'): ?>
                                        <span class="badge bg-danger">bKash</span>
                                        <?php elseif($payment['payment_method'] == 'nagad'): ?>
                                        <span class="badge bg-warning text-dark">Nagad</span>
                                        <?php elseif($payment['payment_method'] == 'rocket'): ?>
                                        <span class="badge bg-primary">Rocket</span>
                                        <?php elseif($payment['payment_method'] == 'manual'): ?>
                                        <span class="badge bg-secondary">Manual</span>
                                        <?php else: ?>
                                        <span class="badge bg-info"><?php echo ucfirst($payment['payment_method']); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="text-monospace"><?php echo $payment['transaction_id']; ?></span>
                                    </td>
                                    <td>
                                        <?php if($payment['payment_status'] == 'pending'): ?>
                                        <span class="badge bg-warning text-dark">Pending</span>
                                        <?php elseif($payment['payment_status'] == 'completed'): ?>
                                        <span class="badge bg-success">Completed</span>
                                        <?php elseif($payment['payment_status'] == 'rejected'): ?>
                                        <span class="badge bg-danger">Rejected</span>
                                        <?php else: ?>
                                        <span class="badge bg-secondary"><?php echo ucfirst($payment['payment_status']); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('M j, Y g:i A', strtotime($payment['created_at'])); ?></td>
                                    <td>
                                        <div class="btn-group">
                                            <?php if($payment['payment_status'] == 'pending'): ?>
                                            <a href="payments.php?approve=<?php echo $payment['id']; ?>" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="Approve">
                                                <i class="fas fa-check"></i>
                                            </a>
                                            <a href="payments.php?reject=<?php echo $payment['id']; ?>" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="Reject">
                                                <i class="fas fa-times"></i>
                                            </a>
                                            <?php else: ?>
                                            <button type="button" class="btn btn-sm btn-secondary" data-bs-toggle="tooltip" title="Already processed">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <?php endif; ?>
                                            <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#paymentDetailsModal<?php echo $payment['id']; ?>" data-bs-toggle="tooltip" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <a href="payments.php?delete=<?php echo $payment['id']; ?>" class="btn btn-sm btn-danger btn-delete" data-bs-toggle="tooltip" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                        
                                        <!-- Payment Details Modal -->
                                        <div class="modal fade" id="paymentDetailsModal<?php echo $payment['id']; ?>" tabindex="-1" aria-labelledby="paymentDetailsModalLabel<?php echo $payment['id']; ?>" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header bg-primary text-white">
                                                        <h5 class="modal-title" id="paymentDetailsModalLabel<?php echo $payment['id']; ?>">Payment Details</h5>
                                                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="row mb-3">
                                                            <div class="col-md-6">
                                                                <p class="mb-1 fw-bold">Payment ID</p>
                                                                <p><?php echo $payment['id']; ?></p>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <p class="mb-1 fw-bold">Status</p>
                                                                <p>
                                                                    <?php if($payment['payment_status'] == 'pending'): ?>
                                                                    <span class="badge bg-warning text-dark">Pending</span>
                                                                    <?php elseif($payment['payment_status'] == 'completed'): ?>
                                                                    <span class="badge bg-success">Completed</span>
                                                                    <?php elseif($payment['payment_status'] == 'rejected'): ?>
                                                                    <span class="badge bg-danger">Rejected</span>
                                                                    <?php else: ?>
                                                                    <span class="badge bg-secondary"><?php echo ucfirst($payment['payment_status']); ?></span>
                                                                    <?php endif; ?>
                                                                </p>
                                                            </div>
                                                        </div>
                                                        <div class="row mb-3">
                                                            <div class="col-md-6">
                                                                <p class="mb-1 fw-bold">User</p>
                                                                <p><?php echo $payment['username']; ?></p>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <p class="mb-1 fw-bold">Plan</p>
                                                                <p><?php echo $payment['plan_name']; ?> (<?php echo $payment['plan_duration']; ?> days)</p>
                                                            </div>
                                                        </div>
                                                        <div class="row mb-3">
                                                            <div class="col-md-6">
                                                                <p class="mb-1 fw-bold">Amount</p>
                                                                <p>৳<?php echo number_format($payment['amount'], 0); ?></p>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <p class="mb-1 fw-bold">Payment Method</p>
                                                                <p><?php echo ucfirst($payment['payment_method']); ?></p>
                                                            </div>
                                                        </div>
                                                        <div class="row mb-3">
                                                            <div class="col-md-6">
                                                                <p class="mb-1 fw-bold">Transaction ID</p>
                                                                <p class="text-monospace"><?php echo $payment['transaction_id']; ?></p>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <p class="mb-1 fw-bold">Date</p>
                                                                <p><?php echo date('M j, Y g:i A', strtotime($payment['created_at'])); ?></p>
                                                            </div>
                                                        </div>
                                                        <?php if($payment['payment_status'] == 'pending'): ?>
                                                        <div class="d-grid gap-2 mt-4">
                                                            <a href="payments.php?approve=<?php echo $payment['id']; ?>" class="btn btn-success">
                                                                <i class="fas fa-check me-2"></i>Approve Payment
                                                            </a>
                                                            <a href="payments.php?reject=<?php echo $payment['id']; ?>" class="btn btn-danger">
                                                                <i class="fas fa-times me-2"></i>Reject Payment
                                                            </a>
                                                        </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="9" class="text-center">No payments found.</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
