<?php
// Include configuration file
require_once '../config.php';

// Include database connection
require_once '../db_connect.php';

// Include functions
require_once '../functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

// Check theme preference
$theme = isset($_SESSION['admin_theme']) ? $_SESSION['admin_theme'] : 'light';

// Redirect to appropriate theme
if ($theme === 'dark') {
    include 'index_dark.php';
    exit;
}

// Set page title
$page_title = 'ড্যাশবোর্ড';

// Get statistics
$stats = [];

// Total Movies
$movies_query = "SELECT COUNT(*) as count FROM movies";
$movies_result = mysqli_query($conn, $movies_query);
$stats['movies'] = mysqli_fetch_assoc($movies_result)['count'];

// Premium Movies
$premium_movies_query = "SELECT COUNT(*) as count FROM movies WHERE premium_only = 1";
$premium_movies_result = mysqli_query($conn, $premium_movies_query);
$stats['premium_movies'] = mysqli_fetch_assoc($premium_movies_result)['count'];

// Total TV Shows
$tvshows_query = "SELECT COUNT(*) as count FROM tvshows";
$tvshows_result = mysqli_query($conn, $tvshows_query);
$stats['tvshows'] = mysqli_fetch_assoc($tvshows_result)['count'];

// Premium TV Shows
$premium_tvshows_query = "SELECT COUNT(*) as count FROM tvshows WHERE premium_only = 1";
$premium_tvshows_result = mysqli_query($conn, $premium_tvshows_query);
$stats['premium_tvshows'] = mysqli_fetch_assoc($premium_tvshows_result)['count'];

// Total Episodes
$episodes_query = "SELECT COUNT(*) as count FROM episodes";
$episodes_result = mysqli_query($conn, $episodes_query);
$stats['episodes'] = mysqli_fetch_assoc($episodes_result)['count'];

// Premium Episodes
$premium_episodes_query = "SELECT COUNT(*) as count FROM episodes WHERE is_premium = 1";
$premium_episodes_result = mysqli_query($conn, $premium_episodes_query);
$stats['premium_episodes'] = mysqli_fetch_assoc($premium_episodes_result)['count'];

// Total Users
$users_query = "SELECT COUNT(*) as count FROM users";
$users_result = mysqli_query($conn, $users_query);
$stats['users'] = mysqli_fetch_assoc($users_result)['count'];

// Premium Users
$premium_users_query = "SELECT COUNT(*) as count FROM users WHERE is_premium = 1";
$premium_users_result = mysqli_query($conn, $premium_users_query);
$stats['premium_users'] = mysqli_fetch_assoc($premium_users_result)['count'];

// Pending Payments
$pending_payments_query = "SELECT COUNT(*) as count FROM payments WHERE payment_status = 'pending'";
$pending_payments_result = mysqli_query($conn, $pending_payments_query);
$stats['pending_payments'] = mysqli_fetch_assoc($pending_payments_result)['count'];

// Total Categories
$categories_query = "SELECT COUNT(*) as count FROM categories";
$categories_result = mysqli_query($conn, $categories_query);
$stats['categories'] = mysqli_fetch_assoc($categories_result)['count'];

// Total Reviews
$reviews_query = "SELECT COUNT(*) as count FROM reviews";
$reviews_result = mysqli_query($conn, $reviews_query);
$stats['reviews'] = mysqli_fetch_assoc($reviews_result)['count'];

// Total Payments
$payments_query = "SELECT COUNT(*) as count FROM payments";
$payments_result = mysqli_query($conn, $payments_query);
$stats['payments'] = mysqli_fetch_assoc($payments_result)['count'] ?? 0;

// Completed Payments
$completed_payments_query = "SELECT COUNT(*) as count FROM payments WHERE payment_status = 'completed'";
$completed_payments_result = mysqli_query($conn, $completed_payments_query);
$stats['completed_payments'] = mysqli_fetch_assoc($completed_payments_result)['count'] ?? 0;

// Total Revenue
$revenue_query = "SELECT SUM(amount) as total FROM payments WHERE payment_status = 'completed'";
$revenue_result = mysqli_query($conn, $revenue_query);
$stats['revenue'] = mysqli_fetch_assoc($revenue_result)['total'] ?? 0;

// Recent Movies
$recent_movies_query = "SELECT m.*, c.name as category_name FROM movies m
                       LEFT JOIN categories c ON m.category_id = c.id
                       ORDER BY m.created_at DESC LIMIT 5";
$recent_movies_result = mysqli_query($conn, $recent_movies_query);

// Recent TV Shows
$recent_tvshows_query = "SELECT t.*, c.name as category_name FROM tvshows t
                        LEFT JOIN categories c ON t.category_id = c.id
                        ORDER BY t.created_at DESC LIMIT 5";
$recent_tvshows_result = mysqli_query($conn, $recent_tvshows_query);

// Recent Users
$recent_users_query = "SELECT * FROM users ORDER BY created_at DESC LIMIT 5";
$recent_users_result = mysqli_query($conn, $recent_users_query);

// Recent Reviews
$recent_reviews_query = "SELECT r.*, u.username,
                        CASE
                            WHEN r.content_type = 'movie' THEN m.title
                            ELSE t.title
                        END as content_title
                        FROM reviews r
                        JOIN users u ON r.user_id = u.id
                        LEFT JOIN movies m ON r.content_type = 'movie' AND r.content_id = m.id
                        LEFT JOIN tvshows t ON r.content_type = 'tvshow' AND r.content_id = t.id
                        ORDER BY r.created_at DESC
                        LIMIT 5";
$recent_reviews_result = mysqli_query($conn, $recent_reviews_query);

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title d-flex align-items-center">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>ড্যাশবোর্ড</h1>
            </div>

            <div class="topbar-actions">
                <div class="topbar-search">
                    <form action="search.php" method="GET">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="সার্চ করুন..." name="q">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>

                <div class="dropdown">
                    <button class="btn btn-link dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <img src="assets/img/user.png" class="rounded-circle" width="32" height="32" alt="User">
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user fa-sm me-2 text-gray-400"></i> প্রোফাইল</a></li>
                        <li><a class="dropdown-item" href="site_settings.php"><i class="fas fa-cogs fa-sm me-2 text-gray-400"></i> সেটিংস</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/logout.php"><i class="fas fa-sign-out-alt fa-sm me-2 text-gray-400"></i> লগআউট</a></li>
                    </ul>
                </div>
            </div>
        </div>

    </div>

    <div class="container-fluid">
        <!-- Welcome Card -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-gradient-primary text-white">
                    <div class="card-body p-4">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h2 class="mb-1">স্বাগতম, <?php echo $_SESSION['username']; ?>!</h2>
                                <p class="mb-0">আপনার মুভি সাইটের আজকের অবস্থা দেখুন।</p>
                            </div>
                            <div class="text-end">
                                <h4><?php echo date('l, F j, Y'); ?></h4>
                                <p class="mb-0"><?php echo date('h:i A'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-primary fw-bold">মুভি</h6>
                            <div class="icon-circle bg-primary-light">
                                <i class="fas fa-film text-primary"></i>
                            </div>
                        </div>
                        <div class="mb-1">
                            <h2 class="fw-bold mb-0"><?php echo $stats['movies']; ?></h2>
                            <p class="text-muted small">মোট মুভি</p>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-primary-light text-primary me-2"><?php echo $stats['premium_movies']; ?></span>
                            <span class="small text-muted">প্রিমিয়াম মুভি</span>
                        </div>
                    </div>
                    <div class="card-footer py-2">
                        <a href="movies.php" class="text-primary text-decoration-none small">
                            বিস্তারিত দেখুন <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-danger fw-bold">টিভি সিরিজ</h6>
                            <div class="icon-circle bg-danger-light">
                                <i class="fas fa-tv text-danger"></i>
                            </div>
                        </div>
                        <div class="mb-1">
                            <h2 class="fw-bold mb-0"><?php echo $stats['tvshows']; ?></h2>
                            <p class="text-muted small">মোট টিভি সিরিজ</p>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-danger-light text-danger me-2"><?php echo $stats['premium_tvshows']; ?></span>
                            <span class="small text-muted">প্রিমিয়াম সিরিজ</span>
                        </div>
                    </div>
                    <div class="card-footer py-2">
                        <a href="tvshows.php" class="text-danger text-decoration-none small">
                            বিস্তারিত দেখুন <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-success fw-bold">ব্যবহারকারী</h6>
                            <div class="icon-circle bg-success-light">
                                <i class="fas fa-users text-success"></i>
                            </div>
                        </div>
                        <div class="mb-1">
                            <h2 class="fw-bold mb-0"><?php echo $stats['users']; ?></h2>
                            <p class="text-muted small">মোট ব্যবহারকারী</p>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success-light text-success me-2"><?php echo $stats['premium_users']; ?></span>
                            <span class="small text-muted">প্রিমিয়াম ব্যবহারকারী</span>
                        </div>
                    </div>
                    <div class="card-footer py-2">
                        <a href="users.php" class="text-success text-decoration-none small">
                            বিস্তারিত দেখুন <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-info fw-bold">রিভিউ</h6>
                            <div class="icon-circle bg-info-light">
                                <i class="fas fa-star text-info"></i>
                            </div>
                        </div>
                        <div class="mb-1">
                            <h2 class="fw-bold mb-0"><?php echo $stats['reviews']; ?></h2>
                            <p class="text-muted small">মোট রিভিউ</p>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-info-light text-info me-2"><?php echo date('M Y'); ?></span>
                            <span class="small text-muted">ব্যবহারকারী মতামত</span>
                        </div>
                    </div>
                    <div class="card-footer py-2">
                        <a href="reviews.php" class="text-info text-decoration-none small">
                            বিস্তারিত দেখুন <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue & Payments Row -->
        <div class="row mb-4">
            <div class="col-xl-4 col-md-6 mb-4">
                <div class="card shadow border-left-warning h-100">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Total Revenue</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">৳<?php echo number_format($stats['revenue'], 0); ?></div>
                                <div class="mt-2 text-xs text-muted">
                                    <span class="text-warning"><?php echo $stats['completed_payments']; ?> Completed Payments</span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-dollar-sign fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-light py-2">
                        <a href="payments.php" class="text-warning text-decoration-none small">
                            View Details <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-xl-4 col-md-6 mb-4">
                <div class="card shadow border-left-danger h-100">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Pending Payments</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['pending_payments']; ?></div>
                                <div class="mt-2 text-xs text-muted">
                                    <span class="text-danger">Require Approval</span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-danger"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-light py-2">
                        <a href="payments.php?status=pending" class="text-danger text-decoration-none small">
                            View Details <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-xl-4 col-md-6 mb-4">
                <div class="card shadow border-left-primary h-100">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Premium Content</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['premium_movies'] + $stats['premium_tvshows']; ?></div>
                                <div class="mt-2 text-xs text-muted">
                                    <span class="text-primary"><?php echo $stats['premium_movies']; ?> Movies, <?php echo $stats['premium_tvshows']; ?> TV Shows</span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-crown fa-2x text-primary"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-light py-2">
                        <a href="manage_premium.php" class="text-primary text-decoration-none small">
                            Manage Premium <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Content Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">Recent Content</h6>
                        <div class="nav nav-tabs card-header-tabs" id="recent-content-tab" role="tablist">
                            <button class="nav-link active" id="movies-tab" data-bs-toggle="tab" data-bs-target="#movies" type="button" role="tab" aria-controls="movies" aria-selected="true">Movies</button>
                            <button class="nav-link" id="tvshows-tab" data-bs-toggle="tab" data-bs-target="#tvshows" type="button" role="tab" aria-controls="tvshows" aria-selected="false">TV Shows</button>
                            <button class="nav-link" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab" aria-controls="users" aria-selected="false">Users</button>
                            <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" type="button" role="tab" aria-controls="reviews" aria-selected="false">Reviews</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="recent-content-tabContent">
                            <!-- Movies Tab -->
                            <div class="tab-pane fade show active" id="movies" role="tabpanel" aria-labelledby="movies-tab">
                                <div class="table-responsive">
                                    <table class="table table-hover align-middle">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Poster</th>
                                                <th>Title</th>
                                                <th>Category</th>
                                                <th>Year</th>
                                                <th>Premium</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            mysqli_data_seek($recent_movies_result, 0);
                                            while($movie = mysqli_fetch_assoc($recent_movies_result)):
                                            ?>
                                            <tr>
                                                <td>
                                                    <?php if (!empty($movie['poster']) && file_exists('../uploads/' . $movie['poster'])): ?>
                                                        <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $movie['poster']; ?>" alt="<?php echo $movie['title']; ?>" class="content-poster" style="width: 40px; height: 60px;">
                                                    <?php else: ?>
                                                        <div class="content-poster-placeholder" style="width: 40px; height: 60px;">
                                                            <i class="fas fa-film text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <a href="edit_movie.php?id=<?php echo $movie['id']; ?>" class="text-decoration-none fw-bold">
                                                        <?php echo $movie['title']; ?>
                                                    </a>
                                                </td>
                                                <td><?php echo $movie['category_name']; ?></td>
                                                <td><?php echo $movie['release_year']; ?></td>
                                                <td>
                                                    <?php if($movie['premium_only']): ?>
                                                    <span class="badge bg-danger">Premium</span>
                                                    <?php else: ?>
                                                    <span class="badge bg-secondary">Free</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a href="edit_movie.php?id=<?php echo $movie['id']; ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $movie['id']; ?>" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="View" target="_blank">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-end mt-3">
                                    <a href="movies.php" class="btn btn-primary">
                                        <i class="fas fa-film me-1"></i> View All Movies
                                    </a>
                                    <a href="add_movie.php" class="btn btn-success ms-2">
                                        <i class="fas fa-plus-circle me-1"></i> Add New Movie
                                    </a>
                                </div>
                            </div>

                            <!-- TV Shows Tab -->
                            <div class="tab-pane fade" id="tvshows" role="tabpanel" aria-labelledby="tvshows-tab">
                                <div class="table-responsive">
                                    <table class="table table-hover align-middle">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Poster</th>
                                                <th>Title</th>
                                                <th>Category</th>
                                                <th>Year</th>
                                                <th>Seasons</th>
                                                <th>Premium</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            mysqli_data_seek($recent_tvshows_result, 0);
                                            while($tvshow = mysqli_fetch_assoc($recent_tvshows_result)):
                                            ?>
                                            <tr>
                                                <td>
                                                    <?php if (!empty($tvshow['poster']) && file_exists('../uploads/' . $tvshow['poster'])): ?>
                                                        <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $tvshow['poster']; ?>" alt="<?php echo $tvshow['title']; ?>" class="content-poster" style="width: 40px; height: 60px;">
                                                    <?php else: ?>
                                                        <div class="content-poster-placeholder" style="width: 40px; height: 60px;">
                                                            <i class="fas fa-tv text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <a href="edit_tvshow.php?id=<?php echo $tvshow['id']; ?>" class="text-decoration-none fw-bold">
                                                        <?php echo $tvshow['title']; ?>
                                                    </a>
                                                </td>
                                                <td><?php echo $tvshow['category_name']; ?></td>
                                                <td><?php echo $tvshow['start_year']; ?><?php echo $tvshow['end_year'] ? ' - ' . $tvshow['end_year'] : ' - Present'; ?></td>
                                                <td><?php echo $tvshow['seasons']; ?></td>
                                                <td>
                                                    <?php if($tvshow['premium_only']): ?>
                                                    <span class="badge bg-danger">Premium</span>
                                                    <?php else: ?>
                                                    <span class="badge bg-secondary">Free</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a href="edit_tvshow.php?id=<?php echo $tvshow['id']; ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="manage_episodes.php?tvshow=<?php echo $tvshow['id']; ?>" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="Episodes">
                                                            <i class="fas fa-list"></i>
                                                        </a>
                                                        <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $tvshow['id']; ?>" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="View" target="_blank">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-end mt-3">
                                    <a href="tvshows.php" class="btn btn-primary">
                                        <i class="fas fa-tv me-1"></i> View All TV Shows
                                    </a>
                                    <a href="add_tvshow.php" class="btn btn-success ms-2">
                                        <i class="fas fa-plus-circle me-1"></i> Add New TV Show
                                    </a>
                                </div>
                            </div>

                            <!-- Users Tab -->
                            <div class="tab-pane fade" id="users" role="tabpanel" aria-labelledby="users-tab">
                                <div class="table-responsive">
                                    <table class="table table-hover align-middle">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Avatar</th>
                                                <th>Username</th>
                                                <th>Email</th>
                                                <th>Role</th>
                                                <th>Premium</th>
                                                <th>Joined</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            mysqli_data_seek($recent_users_result, 0);
                                            while($user = mysqli_fetch_assoc($recent_users_result)):
                                            ?>
                                            <tr>
                                                <td>
                                                    <?php if (!empty($user['profile_image']) && file_exists('../uploads/' . $user['profile_image'])): ?>
                                                        <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $user['profile_image']; ?>" alt="<?php echo $user['username']; ?>" class="user-avatar">
                                                    <?php else: ?>
                                                        <div class="user-avatar d-flex align-items-center justify-content-center bg-light">
                                                            <i class="fas fa-user text-secondary"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <a href="users.php?edit=<?php echo $user['id']; ?>" class="text-decoration-none fw-bold">
                                                        <?php echo $user['username']; ?>
                                                    </a>
                                                </td>
                                                <td><?php echo $user['email']; ?></td>
                                                <td>
                                                    <span class="badge <?php echo $user['role'] == 'admin' ? 'bg-danger' : 'bg-secondary'; ?>">
                                                        <?php echo ucfirst($user['role']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if($user['is_premium']): ?>
                                                    <span class="badge bg-success">Premium</span>
                                                    <?php else: ?>
                                                    <span class="badge bg-secondary">Free</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo date('M j, Y', strtotime($user['created_at'])); ?></td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a href="users.php?edit=<?php echo $user['id']; ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <?php if($user['id'] != $_SESSION['user_id']): ?>
                                                        <a href="users.php?delete=<?php echo $user['id']; ?>" class="btn btn-sm btn-danger btn-delete" data-bs-toggle="tooltip" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-end mt-3">
                                    <a href="users.php" class="btn btn-primary">
                                        <i class="fas fa-users me-1"></i> View All Users
                                    </a>
                                    <button type="button" class="btn btn-success ms-2" data-bs-toggle="modal" data-bs-target="#addUserModal">
                                        <i class="fas fa-user-plus me-1"></i> Add New User
                                    </button>
                                </div>
                            </div>

                            <!-- Reviews Tab -->
                            <div class="tab-pane fade" id="reviews" role="tabpanel" aria-labelledby="reviews-tab">
                                <div class="table-responsive">
                                    <table class="table table-hover align-middle">
                                        <thead class="table-light">
                                            <tr>
                                                <th>User</th>
                                                <th>Content</th>
                                                <th>Rating</th>
                                                <th>Comment</th>
                                                <th>Date</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            mysqli_data_seek($recent_reviews_result, 0);
                                            while($review = mysqli_fetch_assoc($recent_reviews_result)):
                                            ?>
                                            <tr>
                                                <td><?php echo $review['username']; ?></td>
                                                <td>
                                                    <?php echo $review['content_title']; ?>
                                                    <span class="badge <?php echo $review['content_type'] == 'movie' ? 'bg-primary' : 'bg-danger'; ?>">
                                                        <?php echo $review['content_type'] == 'movie' ? 'Movie' : 'TV Show'; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <i class="fas fa-star text-warning me-1"></i>
                                                        <span class="fw-bold"><?php echo $review['rating']; ?>/10</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="text-truncate" style="max-width: 200px;" data-bs-toggle="tooltip" title="<?php echo htmlspecialchars($review['comment']); ?>">
                                                        <?php echo $review['comment']; ?>
                                                    </div>
                                                </td>
                                                <td><?php echo date('M j, Y', strtotime($review['created_at'])); ?></td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a href="reviews.php?edit=<?php echo $review['id']; ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="reviews.php?delete=<?php echo $review['id']; ?>" class="btn btn-sm btn-danger btn-delete" data-bs-toggle="tooltip" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-end mt-3">
                                    <a href="reviews.php" class="btn btn-primary">
                                        <i class="fas fa-star me-1"></i> View All Reviews
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card shadow">
                    <div class="card-header bg-white py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <!-- Content Management -->
                            <div class="col-lg-3 col-md-6">
                                <div class="card bg-gradient-primary text-white shadow">
                                    <div class="card-body">
                                        <div class="text-center">
                                            <i class="fas fa-photo-video fa-3x mb-3"></i>
                                            <h5 class="mb-3">Content Management</h5>
                                            <div class="d-grid gap-2">
                                                <a href="add_movie.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-plus-circle me-1"></i> Add Movie
                                                </a>
                                                <a href="add_tvshow.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-plus-circle me-1"></i> Add TV Show
                                                </a>
                                                <a href="manage_server_links.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-server me-1"></i> Manage Server Links
                                                </a>
                                                <a href="categories.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-tags me-1"></i> Categories
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Import from TMDB -->
                            <div class="col-lg-3 col-md-6">
                                <div class="card bg-gradient-warning text-dark shadow">
                                    <div class="card-body">
                                        <div class="text-center">
                                            <i class="fas fa-file-import fa-3x mb-3"></i>
                                            <h5 class="mb-3">Import from TMDB</h5>
                                            <div class="d-grid gap-2">
                                                <a href="import_tmdb.php" class="btn btn-dark btn-sm">
                                                    <i class="fas fa-film me-1"></i> Import Movies
                                                </a>
                                                <a href="import_tmdb_tvshow.php" class="btn btn-dark btn-sm">
                                                    <i class="fas fa-tv me-1"></i> Import TV Shows
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Premium Management -->
                            <div class="col-lg-3 col-md-6">
                                <div class="card bg-gradient-success text-white shadow">
                                    <div class="card-body">
                                        <div class="text-center">
                                            <i class="fas fa-crown fa-3x mb-3"></i>
                                            <h5 class="mb-3">Premium Management</h5>
                                            <div class="d-grid gap-2">
                                                <a href="premium_plans.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-list-alt me-1"></i> Premium Plans
                                                </a>
                                                <a href="manage_premium.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-gem me-1"></i> Manage Premium
                                                </a>
                                                <a href="payments.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-money-bill-wave me-1"></i> Payments
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- System Management -->
                            <div class="col-lg-3 col-md-6">
                                <div class="card bg-gradient-danger text-white shadow">
                                    <div class="card-body">
                                        <div class="text-center">
                                            <i class="fas fa-cogs fa-3x mb-3"></i>
                                            <h5 class="mb-3">System Management</h5>
                                            <div class="d-grid gap-2">
                                                <a href="site_settings.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-cog me-1"></i> Site Settings
                                                </a>
                                                <a href="backup.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-database me-1"></i> Backup & Restore
                                                </a>
                                                <a href="logs.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-list me-1"></i> System Logs
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
