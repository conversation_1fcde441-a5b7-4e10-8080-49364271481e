<?php
require_once 'includes/config.php';

// SQL commands to execute
$sql_commands = [
    "ALTER TABLE download_links ADD COLUMN IF NOT EXISTS server_name VARCHAR(50) DEFAULT NULL COMMENT 'Server name for download'",
    "ALTER TABLE download_links ADD COLUMN IF NOT EXISTS file_size VARCHAR(20) DEFAULT NULL COMMENT 'File size e.g. 700MB, 1.5GB'"
];

// Execute each SQL command
$success = true;
$messages = [];

foreach ($sql_commands as $sql) {
    if (mysqli_query($conn, $sql)) {
        $messages[] = "Success: " . $sql;
    } else {
        $success = false;
        $messages[] = "Error: " . $sql . " - " . mysqli_error($conn);
    }
}

// Display results
echo "<h2>Database Update Results</h2>";
echo "<ul>";
foreach ($messages as $message) {
    echo "<li>" . $message . "</li>";
}
echo "</ul>";

if ($success) {
    echo "<p style='color: green;'>All database updates completed successfully!</p>";
} else {
    echo "<p style='color: red;'>Some database updates failed. Please check the errors above.</p>";
}

echo "<p><a href='admin/index.php'>Go to Admin Dashboard</a></p>";
?>
