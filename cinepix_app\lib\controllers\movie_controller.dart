import 'package:get/get.dart';
import 'package:cinepix_app/models/movie.dart';
import 'package:cinepix_app/models/download_link.dart';
import 'package:cinepix_app/services/api_service.dart';

class MovieController extends GetxController {
  final ApiService _apiService = ApiService();

  // Movies lists
  final RxList<Movie> featuredMovies = <Movie>[].obs;
  final RxList<Movie> latestMovies = <Movie>[].obs;
  final RxList<Movie> popularMovies = <Movie>[].obs;
  final RxList<Movie> topRatedMovies = <Movie>[].obs;
  final RxList<Movie> categoryMovies = <Movie>[].obs;

  // Current movie details
  final Rx<Movie?> currentMovie = Rx<Movie?>(null);
  final RxList<DownloadLink> downloadLinks = <DownloadLink>[].obs;
  final RxList<Movie> relatedMovies = <Movie>[].obs;

  // Loading states
  final RxBool isLoadingFeatured = false.obs;
  final RxBool isLoadingLatest = false.obs;
  final RxBool isLoadingPopular = false.obs;
  final RxBool isLoadingTopRated = false.obs;
  final RxBool isLoadingCategory = false.obs;
  final RxBool isLoadingDetails = false.obs;

  // Pagination
  final RxInt currentPage = 1.obs;
  final RxInt totalPages = 1.obs;
  final RxBool hasMoreMovies = true.obs;

  // Error handling
  final RxString errorMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();
    loadInitialData();
  }

  // Load initial data
  Future<void> loadInitialData() async {
    await Future.wait([
      loadFeaturedMovies(),
      loadLatestMovies(),
      loadPopularMovies(),
      loadTopRatedMovies(),
    ]);
  }

  // Load featured movies
  Future<void> loadFeaturedMovies() async {
    isLoadingFeatured.value = true;
    errorMessage.value = '';

    try {
      final movies = await _apiService.getMovies(featured: true, limit: 10);
      featuredMovies.value = movies;
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoadingFeatured.value = false;
    }
  }

  // Load latest movies
  Future<void> loadLatestMovies() async {
    isLoadingLatest.value = true;
    errorMessage.value = '';

    try {
      final movies = await _apiService.getMovies(page: 1, limit: 20);
      latestMovies.value = movies;
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoadingLatest.value = false;
    }
  }

  // Load popular movies
  Future<void> loadPopularMovies() async {
    isLoadingPopular.value = true;
    errorMessage.value = '';

    try {
      // Assuming popular movies are sorted by rating on the server
      final movies = await _apiService.getMovies(page: 1, limit: 20);
      popularMovies.value = movies;
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoadingPopular.value = false;
    }
  }

  // Load top rated movies
  Future<void> loadTopRatedMovies() async {
    isLoadingTopRated.value = true;
    errorMessage.value = '';

    try {
      // Get movies sorted by rating
      final movies =
          await _apiService.getMovies(page: 1, limit: 10, sortBy: 'rating');

      // Sort by rating in descending order (highest first)
      movies.sort((a, b) => b.rating.compareTo(a.rating));

      // Take only movies with rating >= 7.0
      final highRatedMovies =
          movies.where((movie) => movie.rating >= 7.0).toList();

      topRatedMovies.value = highRatedMovies;
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoadingTopRated.value = false;
    }
  }

  // Load movies by category
  Future<void> loadMoviesByCategory(int categoryId) async {
    isLoadingCategory.value = true;
    errorMessage.value = '';
    currentPage.value = 1;

    try {
      final movies = await _apiService.getMovies(
        page: currentPage.value,
        limit: 20,
        categoryId: categoryId,
      );

      categoryMovies.value = movies;
      hasMoreMovies.value = movies.length >= 20;
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoadingCategory.value = false;
    }
  }

  // Load more movies by category
  Future<void> loadMoreMoviesByCategory(int categoryId) async {
    if (!hasMoreMovies.value || isLoadingCategory.value) return;

    isLoadingCategory.value = true;
    errorMessage.value = '';

    try {
      currentPage.value++;

      final movies = await _apiService.getMovies(
        page: currentPage.value,
        limit: 20,
        categoryId: categoryId,
      );

      if (movies.isNotEmpty) {
        categoryMovies.addAll(movies);
      }

      hasMoreMovies.value = movies.length >= 20;
    } catch (e) {
      errorMessage.value = e.toString();
      currentPage.value--;
    } finally {
      isLoadingCategory.value = false;
    }
  }

  // Load movie details
  Future<void> loadMovieDetails(int movieId) async {
    isLoadingDetails.value = true;
    errorMessage.value = '';

    try {
      final details = await _apiService.getMovieDetails(movieId);

      currentMovie.value = details['movie'];
      downloadLinks.value = details['download_links'];
      relatedMovies.value = details['related_movies'];
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoadingDetails.value = false;
    }
  }

  // Search movies
  Future<List<Movie>> searchMovies(String query) async {
    if (query.isEmpty) return [];

    try {
      final results = await _apiService.search(query);
      final moviesList = results['movies'] ?? [];
      return moviesList.map((movieJson) => Movie.fromJson(movieJson)).toList();
    } catch (e) {
      errorMessage.value = e.toString();
      return [];
    }
  }

  // Get download links for current movie
  List<DownloadLink> getDownloadLinks() {
    return downloadLinks;
  }

  // Clear current movie
  void clearCurrentMovie() {
    currentMovie.value = null;
    downloadLinks.clear();
    relatedMovies.clear();
  }
}
