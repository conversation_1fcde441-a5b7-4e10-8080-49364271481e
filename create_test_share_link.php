<?php
require_once 'includes/config.php';

echo "<h2>টেস্ট শেয়ার লিংক তৈরি</h2>";

// Check if shared_links table exists
$check_table = mysqli_query($conn, "SHOW TABLES LIKE 'shared_links'");
if (mysqli_num_rows($check_table) == 0) {
    echo "<div style='color: red;'>❌ shared_links টেবিল নেই!</div>";
    exit;
}

// Check if movies table exists and has data
$check_movies = mysqli_query($conn, "SELECT COUNT(*) as total FROM movies");
$total_movies = mysqli_fetch_assoc($check_movies)['total'];

if ($total_movies == 0) {
    echo "<div style='color: red;'>❌ movies টেবিলে কোনো ডেটা নেই!</div>";
    exit;
}

// Get first movie
$movie_query = mysqli_query($conn, "SELECT id, title FROM movies LIMIT 1");
$movie = mysqli_fetch_assoc($movie_query);

echo "<div style='color: green;'>✅ প্রথম মুভি: {$movie['title']} (ID: {$movie['id']})</div>";

// Create test shared link
$content_type = 'movie';
$content_id = $movie['id'];
$title = "টেস্ট শেয়ার লিংক - {$movie['title']}";
$description = "এই একটি টেস্ট শেয়ার লিংক";
$access_limit = 10;
$expires_at = date('Y-m-d H:i:s', strtotime('+7 days'));
$allow_download = 1;
$allow_streaming = 1;
$password = NULL;

// Generate unique token
$link_token = bin2hex(random_bytes(16)) . '_' . time();

$query = "INSERT INTO shared_links (link_token, content_type, content_id, created_by, title, description, access_limit, expires_at, allow_download, allow_streaming, password, is_active) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)";

$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, 'ssiissisiis', $link_token, $content_type, $content_id, 1, $title, $description, $access_limit, $expires_at, $allow_download, $allow_streaming, $password);

if (mysqli_stmt_execute($stmt)) {
    echo "<div style='color: green;'>✅ টেস্ট শেয়ার লিংক সফলভাবে তৈরি হয়েছে!</div>";
    
    $share_url = SITE_URL . '/share.php?token=' . $link_token;
    
    echo "<div style='background: #f0f0f0; padding: 15px; margin: 15px 0; border-radius: 5px;'>";
    echo "<h3>শেয়ার লিংক তথ্য:</h3>";
    echo "<strong>টোকেন:</strong> $link_token<br>";
    echo "<strong>টাইটেল:</strong> $title<br>";
    echo "<strong>কন্টেন্ট টাইপ:</strong> $content_type<br>";
    echo "<strong>কন্টেন্ট ID:</strong> $content_id<br>";
    echo "<strong>এক্সেস লিমিট:</strong> $access_limit<br>";
    echo "<strong>মেয়াদ:</strong> $expires_at<br>";
    echo "<strong>ডাউনলোড অনুমতি:</strong> " . ($allow_download ? 'হ্যাঁ' : 'না') . "<br>";
    echo "<strong>স্ট্রিমিং অনুমতি:</strong> " . ($allow_streaming ? 'হ্যাঁ' : 'না') . "<br>";
    echo "</div>";
    
    echo "<h3>শেয়ার লিংক:</h3>";
    echo "<a href='$share_url' target='_blank' style='color: blue; font-size: 16px;'>$share_url</a>";
    
    echo "<h3>পরবর্তী ধাপ:</h3>";
    echo "<ol>";
    echo "<li>উপরের লিংকে ক্লিক করুন</li>";
    echo "<li>শেয়ার পেজ খুলবে কিনা দেখুন</li>";
    echo "<li>যদি 404 আসে তবে <a href='check_share_links.php'>check_share_links.php</a> চেক করুন</li>";
    echo "</ol>";
    
} else {
    echo "<div style='color: red;'>❌ শেয়ার লিংক তৈরি করতে সমস্যা হয়েছে: " . mysqli_error($conn) . "</div>";
}

// Show all shared links
echo "<h3>সব শেয়ার লিংক:</h3>";
$all_links = mysqli_query($conn, "SELECT * FROM shared_links ORDER BY created_at DESC");
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f0f0f0;'>";
echo "<th>ID</th>";
echo "<th>টোকেন</th>";
echo "<th>টাইটেল</th>";
echo "<th>স্ট্যাটাস</th>";
echo "<th>এক্সেস</th>";
echo "<th>লিংক</th>";
echo "</tr>";

while ($link = mysqli_fetch_assoc($all_links)) {
    $status = $link['is_active'] ? 'সক্রিয়' : 'নিষ্ক্রিয়';
    $share_url = SITE_URL . '/share.php?token=' . $link['link_token'];
    
    echo "<tr>";
    echo "<td>{$link['id']}</td>";
    echo "<td>{$link['link_token']}</td>";
    echo "<td>{$link['title']}</td>";
    echo "<td>$status</td>";
    echo "<td>{$link['access_count']}";
    if ($link['access_limit'] > 0) {
        echo " / {$link['access_limit']}";
    }
    echo "</td>";
    echo "<td><a href='$share_url' target='_blank'>খুলুন</a></td>";
    echo "</tr>";
}
echo "</table>";
?> 