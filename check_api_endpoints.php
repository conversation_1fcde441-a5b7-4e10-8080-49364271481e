<?php
// Include direct config file
require_once 'api/direct_config.php';

// No admin check required for testing
// if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
//     header("Location: login.php");
//     exit;
// }

echo "<h1>API Endpoints Check</h1>";

// List of API endpoints to check
$endpoints = [
    'api/v1/direct_config.php',
    'api/v1/direct_movies.php',
    'api/v1/direct_tvshows.php',
    'api/v1/direct_movie_details.php?id=1',
    'api/v1/direct_tvshow_details.php?id=1',
    'api/v1/direct_tvshow_episodes.php?id=1&season=1',
    'api/v1/direct_search.php?q=test',
    'api/v1/direct_categories.php'
];

foreach ($endpoints as $endpoint) {
    echo "<h2>$endpoint</h2>";

    // Get the contents of the endpoint
    $url = SITE_URL . '/' . $endpoint;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "<p>Status Code: $http_code</p>";

    if ($http_code == 200) {
        $json = json_decode($response, true);

        if ($json) {
            echo "<p>Response: Success</p>";

            // Check if the response has the expected structure
            if (isset($json['success']) && $json['success'] === true) {
                echo "<p style='color: green;'>✓ Response has success = true</p>";

                if (isset($json['data'])) {
                    echo "<p style='color: green;'>✓ Response has data</p>";

                    // Check specific data based on endpoint
                    if (strpos($endpoint, 'direct_movies.php') !== false) {
                        if (isset($json['data']['movies']) && is_array($json['data']['movies'])) {
                            $count = count($json['data']['movies']);
                            echo "<p style='color: green;'>✓ Response has $count movies</p>";

                            if ($count > 0) {
                                $sample = $json['data']['movies'][0];
                                echo "<p>Sample movie:</p>";
                                echo "<pre>" . json_encode($sample, JSON_PRETTY_PRINT) . "</pre>";
                            }
                        } else {
                            echo "<p style='color: red;'>✗ Response does not have movies array</p>";
                        }
                    } elseif (strpos($endpoint, 'direct_tvshows.php') !== false) {
                        if (isset($json['data']['tvshows']) && is_array($json['data']['tvshows'])) {
                            $count = count($json['data']['tvshows']);
                            echo "<p style='color: green;'>✓ Response has $count TV shows</p>";

                            if ($count > 0) {
                                $sample = $json['data']['tvshows'][0];
                                echo "<p>Sample TV show:</p>";
                                echo "<pre>" . json_encode($sample, JSON_PRETTY_PRINT) . "</pre>";
                            }
                        } else {
                            echo "<p style='color: red;'>✗ Response does not have tvshows array</p>";
                        }
                    } elseif (strpos($endpoint, 'direct_movie_details.php') !== false) {
                        if (isset($json['data']['movie'])) {
                            echo "<p style='color: green;'>✓ Response has movie details</p>";

                            if (isset($json['data']['download_links'])) {
                                $count = count($json['data']['download_links']);
                                echo "<p style='color: green;'>✓ Response has $count download links</p>";

                                if ($count > 0) {
                                    $sample = $json['data']['download_links'][0];
                                    echo "<p>Sample download link:</p>";
                                    echo "<pre>" . json_encode($sample, JSON_PRETTY_PRINT) . "</pre>";
                                }
                            } else {
                                echo "<p style='color: red;'>✗ Response does not have download_links array</p>";
                            }
                        } else {
                            echo "<p style='color: red;'>✗ Response does not have movie details</p>";
                        }
                    } elseif (strpos($endpoint, 'direct_tvshow_details.php') !== false) {
                        if (isset($json['data']['tvshow'])) {
                            echo "<p style='color: green;'>✓ Response has TV show details</p>";

                            if (isset($json['data']['seasons'])) {
                                $count = count($json['data']['seasons']);
                                echo "<p style='color: green;'>✓ Response has $count seasons</p>";
                            } else {
                                echo "<p style='color: red;'>✗ Response does not have seasons array</p>";
                            }
                        } else {
                            echo "<p style='color: red;'>✗ Response does not have TV show details</p>";
                        }
                    } elseif (strpos($endpoint, 'direct_tvshow_episodes.php') !== false) {
                        if (isset($json['data']['episodes'])) {
                            $count = count($json['data']['episodes']);
                            echo "<p style='color: green;'>✓ Response has $count episodes</p>";

                            if ($count > 0) {
                                $sample = $json['data']['episodes'][0];
                                echo "<p>Sample episode:</p>";
                                echo "<pre>" . json_encode($sample, JSON_PRETTY_PRINT) . "</pre>";

                                if (isset($sample['download_links'])) {
                                    $links_count = count($sample['download_links']);
                                    echo "<p style='color: green;'>✓ Episode has $links_count download links</p>";
                                } else {
                                    echo "<p style='color: red;'>✗ Episode does not have download_links array</p>";
                                }
                            }
                        } else {
                            echo "<p style='color: red;'>✗ Response does not have episodes array</p>";
                        }
                    } elseif (strpos($endpoint, 'direct_search.php') !== false) {
                        if (isset($json['data']['movies']) && isset($json['data']['tvshows'])) {
                            $movies_count = count($json['data']['movies']);
                            $tvshows_count = count($json['data']['tvshows']);
                            echo "<p style='color: green;'>✓ Response has $movies_count movies and $tvshows_count TV shows</p>";
                        } else {
                            echo "<p style='color: red;'>✗ Response does not have movies and tvshows arrays</p>";
                        }
                    }
                } else {
                    echo "<p style='color: red;'>✗ Response does not have data</p>";
                }
            } else {
                echo "<p style='color: red;'>✗ Response does not have success = true</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ Response is not valid JSON</p>";
            echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "...</pre>";
        }
    } else {
        echo "<p style='color: red;'>✗ Request failed</p>";
    }

    echo "<hr>";
}

// Check download_links table
$check_table = mysqli_query($conn, "SHOW TABLES LIKE 'download_links'");
if (mysqli_num_rows($check_table) > 0) {
    echo "<h2>Download Links Check</h2>";

    // Get sample movie data
    $movies_query = "SELECT id, title FROM movies LIMIT 5";
    $movies_result = mysqli_query($conn, $movies_query);

    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Movie ID</th><th>Title</th><th>Download Links</th></tr>";

    while ($movie = mysqli_fetch_assoc($movies_result)) {
        $movie_id = $movie['id'];
        $title = $movie['title'];

        // Get download links for this movie
        $links_query = "SELECT * FROM download_links WHERE content_type = 'movie' AND content_id = $movie_id";
        $links_result = mysqli_query($conn, $links_query);

        $links_count = mysqli_num_rows($links_result);

        echo "<tr>";
        echo "<td>$movie_id</td>";
        echo "<td>$title</td>";
        echo "<td>$links_count links";

        if ($links_count > 0) {
            echo "<ul>";
            while ($link = mysqli_fetch_assoc($links_result)) {
                $quality = $link['quality'];
                $url = isset($link['url']) ? $link['url'] : (isset($link['link_url']) ? $link['link_url'] : 'No URL');
                $is_premium = isset($link['is_premium']) ? ($link['is_premium'] ? 'Yes' : 'No') : 'Unknown';

                echo "<li>Quality: $quality, Premium: $is_premium<br>URL: " . substr($url, 0, 50) . "...</li>";
            }
            echo "</ul>";
        }

        echo "</td></tr>";
    }

    echo "</table>";
} else {
    echo "<p style='color: red;'>Error: download_links table does not exist!</p>";
}

echo "<h2>API Constants for Flutter App</h2>";
echo "<pre>";
echo "// API Base URL
static const String baseUrl = 'https://cinepix.top/api/v1';

// API Endpoints
static const String configEndpoint = '/direct_config.php';
static const String loginEndpoint = '/direct_login.php';
static const String registerEndpoint = '/direct_register.php';
static const String moviesEndpoint = '/direct_movies.php';
static const String movieDetailsEndpoint = '/direct_movie_details.php';
static const String tvShowsEndpoint = '/direct_tvshows.php';
static const String tvShowDetailsEndpoint = '/direct_tvshow_details.php';
static const String tvShowEpisodesEndpoint = '/direct_tvshow_episodes.php';
static const String searchEndpoint = '/direct_search.php';
static const String categoriesEndpoint = '/direct_categories.php';
static const String profileEndpoint = '/direct_profile.php';";
echo "</pre>";

echo "<p><a href='admin/index.php'>Return to Admin Dashboard</a></p>";
?>
