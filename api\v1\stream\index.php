<?php
// API Streaming Endpoints

// Get the request
global $request;

// Check if user is authenticated
if (!is_authenticated()) {
    api_error('Authentication required', 401);
}

// Get authenticated user
$current_user = get_authenticated_user();

// Handle different endpoints based on the path parts
$action = $request['parts'][0] ?? 'default';

switch ($action) {
    case 'movie':
        if (isset($request['parts'][1]) && is_numeric($request['parts'][1])) {
            handle_movie_stream($current_user, (int)$request['parts'][1], $request);
        } else {
            api_error('Invalid movie ID', 400);
        }
        break;
    
    case 'episode':
        if (isset($request['parts'][1]) && is_numeric($request['parts'][1])) {
            handle_episode_stream($current_user, (int)$request['parts'][1], $request);
        } else {
            api_error('Invalid episode ID', 400);
        }
        break;
    
    case 'update_position':
        handle_update_position($current_user, $request);
        break;
    
    case 'continue_watching':
        handle_continue_watching($current_user, $request);
        break;
    
    default:
        api_error('Invalid streaming endpoint', 404);
}

// Handle movie streaming
function handle_movie_stream($user, $movie_id, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get movie details
    $query = "SELECT m.*, c.name as category_name 
              FROM movies m 
              LEFT JOIN categories c ON m.category_id = c.id 
              WHERE m.id = ?";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $movie_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($result) === 0) {
        api_error('Movie not found', 404);
    }
    
    $movie = mysqli_fetch_assoc($result);
    
    // Check if movie is premium and user has premium access
    if ($movie['premium_only'] && !$user['is_premium']) {
        api_error('Premium subscription required to access this content', 403);
    }
    
    // Get streaming links
    $links_query = "SELECT * FROM download_links 
                   WHERE content_type = 'movie' AND content_id = ? 
                   ORDER BY quality DESC";
    
    $links_stmt = mysqli_prepare($conn, $links_query);
    mysqli_stmt_bind_param($links_stmt, 'i', $movie_id);
    mysqli_stmt_execute($links_stmt);
    $links_result = mysqli_stmt_get_result($links_stmt);
    
    $streaming_links = [];
    while ($link = mysqli_fetch_assoc($links_result)) {
        // Skip premium links if user doesn't have premium
        if ($link['is_premium'] && !$user['is_premium']) {
            continue;
        }
        
        $streaming_links[] = [
            'id' => (int)$link['id'],
            'quality' => $link['quality'],
            'url' => $link['url'],
            'server_name' => $link['server_name'],
            'file_size' => $link['file_size'],
            'is_premium' => (bool)$link['is_premium']
        ];
    }
    
    if (empty($streaming_links)) {
        api_error('No streaming links available for this movie', 404);
    }
    
    // Get last watched position
    $position_query = "SELECT last_watched_position FROM watchlist 
                      WHERE user_id = ? AND content_type = 'movie' AND content_id = ?";
    
    $position_stmt = mysqli_prepare($conn, $position_query);
    mysqli_stmt_bind_param($position_stmt, 'ii', $user['user_id'], $movie_id);
    mysqli_stmt_execute($position_stmt);
    $position_result = mysqli_stmt_get_result($position_stmt);
    
    $last_position = 0;
    if (mysqli_num_rows($position_result) > 0) {
        $position = mysqli_fetch_assoc($position_result);
        $last_position = (int)$position['last_watched_position'];
    } else {
        // Add to watchlist if not already
        $add_query = "INSERT INTO watchlist (user_id, content_type, content_id) 
                     VALUES (?, 'movie', ?)";
        
        $add_stmt = mysqli_prepare($conn, $add_query);
        mysqli_stmt_bind_param($add_stmt, 'ii', $user['user_id'], $movie_id);
        mysqli_stmt_execute($add_stmt);
    }
    
    // Get subtitles if available
    $subtitles_query = "SELECT * FROM subtitles 
                       WHERE content_type = 'movie' AND content_id = ?";
    
    $subtitles_stmt = mysqli_prepare($conn, $subtitles_query);
    mysqli_stmt_bind_param($subtitles_stmt, 'i', $movie_id);
    mysqli_stmt_execute($subtitles_stmt);
    $subtitles_result = mysqli_stmt_get_result($subtitles_stmt);
    
    $subtitles = [];
    while ($subtitle = mysqli_fetch_assoc($subtitles_result)) {
        $subtitles[] = [
            'id' => (int)$subtitle['id'],
            'language' => $subtitle['language'],
            'url' => $subtitle['url'],
            'is_default' => (bool)$subtitle['is_default']
        ];
    }
    
    // Return streaming data
    api_response([
        'movie' => [
            'id' => (int)$movie['id'],
            'title' => $movie['title'],
            'poster' => $movie['poster'],
            'duration' => (int)$movie['duration'],
            'release_year' => (int)$movie['release_year'],
            'category' => $movie['category_name']
        ],
        'streaming_links' => $streaming_links,
        'subtitles' => $subtitles,
        'last_position' => $last_position
    ]);
}

// Handle episode streaming
function handle_episode_stream($user, $episode_id, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get episode details
    $query = "SELECT e.*, t.title as tvshow_title, t.poster as tvshow_poster, t.premium_only 
              FROM episodes e 
              JOIN tvshows t ON e.tvshow_id = t.id 
              WHERE e.id = ?";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $episode_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($result) === 0) {
        api_error('Episode not found', 404);
    }
    
    $episode = mysqli_fetch_assoc($result);
    
    // Check if episode or tvshow is premium and user has premium access
    if (($episode['is_premium'] || $episode['premium_only']) && !$user['is_premium']) {
        api_error('Premium subscription required to access this content', 403);
    }
    
    // Get streaming links
    $links_query = "SELECT * FROM download_links 
                   WHERE content_type = 'episode' AND content_id = ? 
                   ORDER BY quality DESC";
    
    $links_stmt = mysqli_prepare($conn, $links_query);
    mysqli_stmt_bind_param($links_stmt, 'i', $episode_id);
    mysqli_stmt_execute($links_stmt);
    $links_result = mysqli_stmt_get_result($links_stmt);
    
    $streaming_links = [];
    while ($link = mysqli_fetch_assoc($links_result)) {
        // Skip premium links if user doesn't have premium
        if ($link['is_premium'] && !$user['is_premium']) {
            continue;
        }
        
        $streaming_links[] = [
            'id' => (int)$link['id'],
            'quality' => $link['quality'],
            'url' => $link['url'],
            'server_name' => $link['server_name'],
            'file_size' => $link['file_size'],
            'is_premium' => (bool)$link['is_premium']
        ];
    }
    
    if (empty($streaming_links)) {
        api_error('No streaming links available for this episode', 404);
    }
    
    // Get last watched position
    $position_query = "SELECT last_watched_position FROM watchlist 
                      WHERE user_id = ? AND content_type = 'episode' AND content_id = ?";
    
    $position_stmt = mysqli_prepare($conn, $position_query);
    mysqli_stmt_bind_param($position_stmt, 'ii', $user['user_id'], $episode_id);
    mysqli_stmt_execute($position_stmt);
    $position_result = mysqli_stmt_get_result($position_stmt);
    
    $last_position = 0;
    if (mysqli_num_rows($position_result) > 0) {
        $position = mysqli_fetch_assoc($position_result);
        $last_position = (int)$position['last_watched_position'];
    } else {
        // Add to watchlist if not already
        $add_query = "INSERT INTO watchlist (user_id, content_type, content_id) 
                     VALUES (?, 'episode', ?)";
        
        $add_stmt = mysqli_prepare($conn, $add_query);
        mysqli_stmt_bind_param($add_stmt, 'ii', $user['user_id'], $episode_id);
        mysqli_stmt_execute($add_stmt);
    }
    
    // Get subtitles if available
    $subtitles_query = "SELECT * FROM subtitles 
                       WHERE content_type = 'episode' AND content_id = ?";
    
    $subtitles_stmt = mysqli_prepare($conn, $subtitles_query);
    mysqli_stmt_bind_param($subtitles_stmt, 'i', $episode_id);
    mysqli_stmt_execute($subtitles_stmt);
    $subtitles_result = mysqli_stmt_get_result($subtitles_stmt);
    
    $subtitles = [];
    while ($subtitle = mysqli_fetch_assoc($subtitles_result)) {
        $subtitles[] = [
            'id' => (int)$subtitle['id'],
            'language' => $subtitle['language'],
            'url' => $subtitle['url'],
            'is_default' => (bool)$subtitle['is_default']
        ];
    }
    
    // Get next episode if available
    $next_episode_query = "SELECT id FROM episodes 
                          WHERE tvshow_id = ? AND season_number = ? AND episode_number = ? 
                          LIMIT 1";
    
    $next_episode_stmt = mysqli_prepare($conn, $next_episode_query);
    $next_episode_number = $episode['episode_number'] + 1;
    mysqli_stmt_bind_param($next_episode_stmt, 'iii', $episode['tvshow_id'], $episode['season_number'], $next_episode_number);
    mysqli_stmt_execute($next_episode_stmt);
    $next_episode_result = mysqli_stmt_get_result($next_episode_stmt);
    
    $next_episode_id = null;
    if (mysqli_num_rows($next_episode_result) > 0) {
        $next_episode = mysqli_fetch_assoc($next_episode_result);
        $next_episode_id = (int)$next_episode['id'];
    }
    
    // Return streaming data
    api_response([
        'episode' => [
            'id' => (int)$episode['id'],
            'title' => $episode['title'],
            'thumbnail' => $episode['thumbnail'],
            'duration' => (int)$episode['duration'],
            'season_number' => (int)$episode['season_number'],
            'episode_number' => (int)$episode['episode_number'],
            'tvshow_id' => (int)$episode['tvshow_id'],
            'tvshow_title' => $episode['tvshow_title'],
            'tvshow_poster' => $episode['tvshow_poster']
        ],
        'streaming_links' => $streaming_links,
        'subtitles' => $subtitles,
        'last_position' => $last_position,
        'next_episode_id' => $next_episode_id
    ]);
}

// Handle update playback position
function handle_update_position($user, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'POST') {
        api_error('Method not allowed', 405);
    }
    
    // Validate required fields
    if (empty($request['body']['content_type']) || 
        empty($request['body']['content_id']) || 
        !isset($request['body']['position'])) {
        api_error('Content type, content ID, and position are required', 400);
    }
    
    $content_type = $request['body']['content_type'];
    $content_id = (int)$request['body']['content_id'];
    $position = (int)$request['body']['position'];
    
    // Validate content type
    if (!in_array($content_type, ['movie', 'episode'])) {
        api_error('Invalid content type. Must be movie or episode', 400);
    }
    
    // Update or insert watchlist entry
    $query = "INSERT INTO watchlist (user_id, content_type, content_id, last_watched_position, last_watched_at) 
              VALUES (?, ?, ?, ?, NOW()) 
              ON DUPLICATE KEY UPDATE last_watched_position = ?, last_watched_at = NOW()";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'isiii', $user['user_id'], $content_type, $content_id, $position, $position);
    
    if (!mysqli_stmt_execute($stmt)) {
        api_error('Failed to update position: ' . mysqli_error($conn), 500);
    }
    
    // Return success response
    api_response([
        'content_type' => $content_type,
        'content_id' => $content_id,
        'position' => $position
    ], 200, 'Position updated successfully');
}

// Handle continue watching list
function handle_continue_watching($user, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get pagination parameters
    $limit = isset($request['params']['limit']) ? (int)$request['params']['limit'] : 10;
    
    // Get continue watching list
    $query = "SELECT w.*, 
              CASE 
                WHEN w.content_type = 'movie' THEN m.title 
                WHEN w.content_type = 'episode' THEN CONCAT(t.title, ' S', e.season_number, 'E', e.episode_number, ' - ', e.title)
              END as title,
              CASE 
                WHEN w.content_type = 'movie' THEN m.poster 
                WHEN w.content_type = 'episode' THEN COALESCE(e.thumbnail, t.poster)
              END as poster,
              CASE 
                WHEN w.content_type = 'movie' THEN m.duration 
                WHEN w.content_type = 'episode' THEN e.duration
              END as duration
              FROM watchlist w
              LEFT JOIN movies m ON w.content_type = 'movie' AND w.content_id = m.id
              LEFT JOIN episodes e ON w.content_type = 'episode' AND w.content_id = e.id
              LEFT JOIN tvshows t ON e.tvshow_id = t.id
              WHERE w.user_id = ? AND w.last_watched_position > 0
              ORDER BY w.last_watched_at DESC
              LIMIT ?";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'ii', $user['user_id'], $limit);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $continue_watching = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $progress = 0;
        if ($row['duration'] > 0) {
            $progress = min(100, round(($row['last_watched_position'] / ($row['duration'] * 60)) * 100));
        }
        
        $continue_watching[] = [
            'id' => (int)$row['id'],
            'content_type' => $row['content_type'],
            'content_id' => (int)$row['content_id'],
            'title' => $row['title'],
            'poster' => $row['poster'],
            'last_watched_position' => (int)$row['last_watched_position'],
            'duration' => (int)$row['duration'],
            'progress' => $progress,
            'last_watched_at' => $row['last_watched_at']
        ];
    }
    
    // Return continue watching list
    api_response([
        'continue_watching' => $continue_watching
    ]);
}
