$(document).ready(function() {
    // Profile image preview
    $('#profileImage').on('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#imagePreview').attr('src', e.target.result);
            }
            reader.readAsDataURL(file);
        }
    });
    
    // Password strength indicator
    $('#newPassword').on('input', function() {
        const password = $(this).val();
        let strength = 0;
        
        // Check password length
        if (password.length >= 6) strength += 1;
        if (password.length >= 10) strength += 1;
        
        // Check for lowercase and uppercase letters
        if (password.match(/[a-z]/)) strength += 1;
        if (password.match(/[A-Z]/)) strength += 1;
        
        // Check for numbers and special characters
        if (password.match(/[0-9]/)) strength += 1;
        if (password.match(/[^a-zA-Z0-9]/)) strength += 1;
        
        // Update strength indicator
        let strengthText = '';
        let strengthClass = '';
        
        if (password.length === 0) {
            strengthText = '';
            strengthClass = '';
        } else if (strength < 3) {
            strengthText = 'Weak';
            strengthClass = 'text-danger';
        } else if (strength < 5) {
            strengthText = 'Moderate';
            strengthClass = 'text-warning';
        } else {
            strengthText = 'Strong';
            strengthClass = 'text-success';
        }
        
        // Display strength indicator
        if (strengthText) {
            if (!$('#passwordStrength').length) {
                $(this).after('<div id="passwordStrength" class="mt-1"></div>');
            }
            $('#passwordStrength').attr('class', 'mt-1 ' + strengthClass).text(strengthText);
        } else {
            $('#passwordStrength').remove();
        }
    });
    
    // Password confirmation validation
    $('#confirmPassword').on('input', function() {
        const password = $('#newPassword').val();
        const confirmPassword = $(this).val();
        
        if (confirmPassword && password !== confirmPassword) {
            if (!$('#passwordMatch').length) {
                $(this).after('<div id="passwordMatch" class="mt-1 text-danger">Passwords do not match</div>');
            }
        } else {
            $('#passwordMatch').remove();
        }
    });
});
