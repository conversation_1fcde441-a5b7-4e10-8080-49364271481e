Permission Fix Summary

Problem:
Non-admin users (regular users) couldn't see download buttons, watch buttons, and season/episode lists in TV series details pages.

Solution:
1. Added a new function 'canViewContent()' in includes/config.php to check if a user can view content (all logged-in users can view content).
2. Modified tvshow_details.php to:
   - Show a login message for non-logged-in users
   - Only display episodes to logged-in users
   - Added debug information to help troubleshoot permission issues
3. Modified movie_details.php to:
   - Show a login message for non-logged-in users
   - Only display download and watch buttons to logged-in users
   - Added debug information to help troubleshoot permission issues
4. Created a check_permissions.php diagnostic page to help troubleshoot permission issues.

How to Test:
1. Log in with a normal user account (non-admin)
2. Visit a TV show details page - you should now see seasons, episodes, download links, and watch buttons
3. Visit a movie details page - you should now see download links and watch buttons
4. Log out and visit the same pages - you should see login messages instead of the content

If you still encounter issues:
1. Visit check_permissions.php to see detailed information about your user account and permissions
2. Uncomment the debug information in tvshow_details.php and movie_details.php to see more detailed information
3. Check that your user account has the correct role and permissions in the database

Note: Premium content will still only be accessible to premium users, but all logged-in users should now be able to see free content.
