/* Movie Details Page Styles */

/* Movie Details Section */
.movie-details-section {
    position: relative;
    padding: 60px 0 40px;
    color: #fff;
    min-height: 500px;
}

.movie-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    z-index: -1;
}

.backdrop-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.9) 100%);
    backdrop-filter: blur(5px);
}

.movie-details-container {
    background-color: rgba(20, 20, 20, 0.8);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

/* Movie Poster */
.movie-poster-container {
    position: relative;
    margin-bottom: 20px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.6);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.movie-poster-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.7);
}

.movie-poster {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 12px;
    transition: transform 0.5s ease;
}

.movie-poster-container:hover .movie-poster {
    transform: scale(1.05);
}

/* Premium Badge */
.premium-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(135deg, #ff7b00, #ff0062);
    color: white;
    padding: 8px 15px;
    border-radius: 30px;
    font-size: 14px;
    font-weight: bold;
    box-shadow: 0 5px 15px rgba(255, 0, 98, 0.4);
    display: flex;
    align-items: center;
    z-index: 2;
}

.premium-badge i {
    margin-right: 5px;
    font-size: 16px;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

/* Movie Info */
.movie-info {
    padding: 10px 0;
}

.movie-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    color: #fff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.movie-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 25px;
    font-size: 1rem;
}

.movie-meta span {
    display: flex;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.4);
    padding: 8px 15px;
    border-radius: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.movie-meta i {
    margin-right: 8px;
    color: #ff7b00;
}

.movie-rating i {
    color: #ffc107;
}

.movie-description {
    background-color: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 12px;
    margin-top: 20px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.movie-description h4 {
    font-size: 1.4rem;
    margin-bottom: 15px;
    color: #ff7b00;
    font-weight: 600;
}

.movie-description p {
    color: #e0e0e0;
    line-height: 1.8;
    font-size: 1.05rem;
}

/* Content Tabs Section */
.content-tabs-section {
    padding: 20px 0 50px;
}

.custom-tabs {
    background-color: rgba(20, 20, 20, 0.8);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.custom-tab-buttons {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 15px;
}

.tab-button {
    background: transparent;
    border: none;
    color: #adb5bd;
    padding: 12px 25px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.tab-button:hover {
    color: #fff;
    background-color: rgba(255, 123, 0, 0.1);
}

.tab-button.active {
    color: #fff;
    background: linear-gradient(135deg, #ff7b00, #ff0062);
    box-shadow: 0 5px 15px rgba(255, 0, 98, 0.3);
}

.tab-content-pane {
    display: none;
    animation: fadeIn 0.5s ease;
}

.tab-content-pane.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Stream Tab */
.stream-container {
    margin-bottom: 30px;
    width: 100%;
}

.stream-header {
    margin-bottom: 20px;
}

.stream-header h3 {
    font-size: 1.5rem;
    color: #fff;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.stream-header h3:before {
    content: '\f03d';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 10px;
    color: #ff7b00;
}

.player-wrapper {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 Aspect Ratio (default) */
    height: 0;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    transition: height 0.3s ease;
}

.player-wrapper iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 12px;
    max-width: 100%;
    object-fit: contain;
}

.streaming-links {
    margin-top: 20px;
}

.table-responsive {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.table {
    margin-bottom: 0;
}

.table-dark {
    background-color: rgba(33, 37, 41, 0.8);
}

.table-dark thead th {
    background-color: rgba(0, 0, 0, 0.6);
    color: #ff7b00;
    font-weight: 600;
    border-bottom: none;
    padding: 15px;
}

.table-dark tbody td {
    padding: 15px;
    vertical-align: middle;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-stream {
    background: linear-gradient(135deg, #4a00e0, #8e2de2);
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(142, 45, 226, 0.3);
}

.btn-stream:hover {
    background: linear-gradient(135deg, #5a17f3, #9d45e2);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(142, 45, 226, 0.4);
    color: white;
}

.btn-premium {
    background: linear-gradient(135deg, #ff7b00, #ff0062);
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(255, 0, 98, 0.3);
}

.btn-premium:hover {
    background: linear-gradient(135deg, #ff8b20, #ff2072);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 0, 98, 0.4);
    color: white;
}

/* Download Tab */
.download-container {
    margin-bottom: 30px;
}

.download-header {
    margin-bottom: 20px;
}

.download-header h3 {
    font-size: 1.5rem;
    color: #fff;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.download-header h3:before {
    content: '\f019';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 10px;
    color: #ff7b00;
}

.download-links {
    margin-top: 20px;
}

.download-card {
    display: flex;
    align-items: center;
    background-color: rgba(33, 37, 41, 0.8);
    padding: 15px 20px;
    border-radius: 12px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    text-decoration: none;
    color: #fff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
    height: 100%;
}

.download-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    background-color: rgba(40, 45, 50, 0.9);
    color: #fff;
}

.download-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: linear-gradient(to bottom, #4a00e0, #8e2de2);
}

.premium-download:before {
    background: linear-gradient(to bottom, #ff7b00, #ff0062);
}

.download-card-icon {
    width: 50px;
    height: 50px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.3rem;
    color: #4a00e0;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.download-card:hover .download-card-icon {
    background-color: rgba(0, 0, 0, 0.5);
    transform: scale(1.1);
}

.premium-download .download-card-icon {
    color: #ff7b00;
}

.download-card-info {
    flex-grow: 1;
}

.download-quality {
    display: block;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.download-type {
    display: block;
    font-size: 0.9rem;
    color: #adb5bd;
}

.download-card-action {
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    color: #fff;
    transition: all 0.3s ease;
}

.download-card:hover .download-card-action {
    background-color: #4a00e0;
    transform: scale(1.1);
}

.download-card-premium {
    background: linear-gradient(135deg, #ff7b00, #ff0062);
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-left: 10px;
    box-shadow: 0 3px 10px rgba(255, 0, 98, 0.3);
}

/* Reviews Tab */
.review-form-container {
    background-color: rgba(33, 37, 41, 0.8);
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.review-form-container h3 {
    font-size: 1.5rem;
    color: #fff;
    font-weight: 600;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.review-form-container h3:before {
    content: '\f4ad';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 10px;
    color: #ff7b00;
}

.form-label {
    color: #e0e0e0;
    font-weight: 500;
}

.form-control, .form-select {
    background-color: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #fff;
    border-radius: 8px;
    padding: 12px 15px;
}

.form-control:focus, .form-select:focus {
    background-color: rgba(0, 0, 0, 0.4);
    border-color: rgba(255, 123, 0, 0.5);
    color: #fff;
    box-shadow: 0 0 0 0.25rem rgba(255, 123, 0, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #4a00e0, #8e2de2);
    border: none;
    padding: 10px 25px;
    border-radius: 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(142, 45, 226, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a17f3, #9d45e2);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(142, 45, 226, 0.4);
}

.reviews-container {
    margin-top: 30px;
}

.reviews-container h3 {
    font-size: 1.5rem;
    color: #fff;
    font-weight: 600;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.reviews-container h3:before {
    content: '\f086';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 10px;
    color: #ff7b00;
}

.review-card {
    background-color: rgba(33, 37, 41, 0.8);
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.review-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.review-user {
    display: flex;
    align-items: center;
}

.review-user-img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 15px;
    border: 3px solid rgba(255, 123, 0, 0.5);
}

.review-username {
    font-weight: 600;
    color: #e0e0e0;
    font-size: 1.1rem;
}

.review-rating {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.stars-container {
    color: #ffc107;
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.rating-number {
    font-weight: 600;
    color: #e0e0e0;
}

.review-body {
    color: #adb5bd;
}

.review-text {
    line-height: 1.7;
    margin-bottom: 15px;
}

.review-date {
    color: #6c757d;
    text-align: right;
}

.no-reviews-message, .no-content-message {
    background-color: rgba(33, 37, 41, 0.8);
    padding: 30px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.no-reviews-message p, .no-content-message p {
    color: #adb5bd;
    font-size: 1.1rem;
}

/* Download Modal */
.modal-content {
    background-color: #1a1a1a;
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.5);
}

.modal-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px 25px;
}

.modal-title {
    color: #fff;
    font-weight: 600;
}

.modal-body {
    padding: 25px;
}

.download-quality-group {
    margin-bottom: 25px;
}

.download-quality-title {
    color: #ff7b00;
    font-size: 1.3rem;
    margin-bottom: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.download-quality-title:before {
    content: '\f3cd';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 10px;
    font-size: 1.1rem;
}

.download-links-list {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.download-link {
    display: flex;
    align-items: center;
    background-color: rgba(33, 37, 41, 0.8);
    padding: 15px 20px;
    border-radius: 12px;
    transition: all 0.3s ease;
    text-decoration: none;
    color: #fff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
    width: calc(50% - 8px);
}

.download-link:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    background-color: rgba(40, 45, 50, 0.9);
    color: #fff;
}

.premium-link {
    background: linear-gradient(135deg, rgba(255, 123, 0, 0.2), rgba(255, 0, 98, 0.2));
    border: 1px solid rgba(255, 123, 0, 0.3);
}

.download-link-type {
    margin-right: 15px;
    font-weight: 600;
    color: #4a00e0;
}

.premium-link .download-link-type {
    color: #ff7b00;
}

.download-link-info {
    flex-grow: 1;
    color: #adb5bd;
}

.download-link-action {
    background-color: rgba(0, 0, 0, 0.3);
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.download-link:hover .download-link-action {
    background-color: #4a00e0;
}

.premium-link:hover .download-link-action {
    background-color: #ff7b00;
}

/* Download link container for download and play buttons */
.download-link-container {
    display: flex;
    width: calc(50% - 8px);
}

.download-link-container .download-link {
    flex: 1;
    width: auto;
    border-radius: 12px 0 0 12px;
}

/* Play link styles */
.play-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #e50914;
    padding: 15px 10px;
    border-radius: 0 12px 12px 0;
    transition: all 0.3s ease;
    text-decoration: none;
    color: #fff;
    width: 60px;
}

.play-link:hover {
    background-color: #f40612;
    color: #fff;
}

.play-link-icon {
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.play-link-text {
    font-size: 0.8rem;
    text-align: center;
}

.modal-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px 25px;
}

.btn-secondary {
    background-color: #343a40;
    border: none;
    padding: 10px 25px;
    border-radius: 30px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background-color: #495057;
}

/* Responsive Adjustments */
@media (max-width: 991.98px) {
    .movie-title {
        font-size: 2rem;
    }

    .movie-meta {
        gap: 15px;
    }

    .download-link {
        width: 100%;
    }

    .player-wrapper {
        padding-bottom: 56.25%; /* Maintain 16:9 aspect ratio */
    }
}

@media (max-width: 767.98px) {
    .movie-details-section {
        padding: 50px 0 30px;
    }

    .movie-details-container {
        padding: 20px;
    }

    .movie-title {
        font-size: 1.8rem;
    }

    .movie-meta {
        gap: 10px;
        margin-bottom: 20px;
    }

    .movie-meta span {
        padding: 6px 12px;
        font-size: 0.9rem;
    }

    .custom-tabs {
        padding: 20px;
    }

    .custom-tab-buttons {
        flex-wrap: wrap;
    }

    .tab-button {
        padding: 10px 20px;
        font-size: 1rem;
    }

    .player-wrapper {
        padding-bottom: 56.25%; /* Maintain 16:9 aspect ratio */
        margin: 0 auto;
    }

    .stream-container {
        width: 100%;
        padding: 0;
    }
}

@media (max-width: 575.98px) {
    .movie-details-section {
        padding: 40px 0 20px;
    }

    .movie-details-container {
        padding: 15px;
    }

    .movie-title {
        font-size: 1.5rem;
    }

    .movie-meta {
        gap: 8px;
    }

    .movie-meta span {
        padding: 5px 10px;
        font-size: 0.8rem;
    }

    .movie-description h4 {
        font-size: 1.2rem;
    }

    .movie-description p {
        font-size: 0.95rem;
    }

    .custom-tabs {
        padding: 15px;
    }

    .tab-button {
        padding: 8px 15px;
        font-size: 0.9rem;
    }

    .download-card {
        padding: 12px 15px;
    }

    .download-card-icon {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
    }

    .download-quality {
        font-size: 1rem;
    }

    .download-type {
        font-size: 0.8rem;
    }

    .review-form-container, .review-card {
        padding: 15px;
    }

    .review-user-img {
        width: 40px;
        height: 40px;
    }

    .review-username {
        font-size: 1rem;
    }

    .player-wrapper {
        padding-bottom: 56.25%; /* Maintain 16:9 aspect ratio */
        border-radius: 8px;
    }

    .player-wrapper iframe {
        border-radius: 8px;
    }

    .stream-header h3 {
        font-size: 1.3rem;
    }

    .stream-info .alert {
        font-size: 0.9rem;
        padding: 10px;
    }
}
