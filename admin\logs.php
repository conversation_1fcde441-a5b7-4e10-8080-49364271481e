<?php
// Set page title
$page_title = 'System Logs';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Process form submissions
$success_message = '';
$error_message = '';

// Check if system_logs table exists
$check_table_query = "SHOW TABLES LIKE 'system_logs'";
$check_table_result = mysqli_query($conn, $check_table_query);

if (mysqli_num_rows($check_table_result) == 0) {
    // Create system_logs table
    $create_table_query = "CREATE TABLE IF NOT EXISTS system_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        log_type ENUM('info', 'warning', 'error', 'success') NOT NULL DEFAULT 'info',
        message TEXT NOT NULL,
        details TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if (mysqli_query($conn, $create_table_query)) {
        // Insert sample logs
        $sample_logs = [
            ["info", "System initialized", "Initial system setup completed"],
            ["success", "Database migration completed", "All tables created successfully"],
            ["warning", "Disk space running low", "Available space: 1.2GB"],
            ["error", "Failed to connect to payment gateway", "Connection timeout after 30 seconds"]
        ];
        
        foreach ($sample_logs as $log) {
            $log_type = $log[0];
            $message = $log[1];
            $details = $log[2];
            
            $insert_query = "INSERT INTO system_logs (log_type, message, details) 
                           VALUES ('$log_type', '$message', '$details')";
            mysqli_query($conn, $insert_query);
        }
    } else {
        $error_message = "Error creating system_logs table: " . mysqli_error($conn);
    }
}

// Clear logs
if (isset($_POST['clear_logs'])) {
    $clear_query = "TRUNCATE TABLE system_logs";
    
    if (mysqli_query($conn, $clear_query)) {
        $success_message = "All logs cleared successfully.";
    } else {
        $error_message = "Error clearing logs: " . mysqli_error($conn);
    }
}

// Delete log
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $log_id = (int)$_GET['delete'];
    
    $delete_query = "DELETE FROM system_logs WHERE id = $log_id";
    
    if (mysqli_query($conn, $delete_query)) {
        $success_message = "Log entry deleted successfully.";
    } else {
        $error_message = "Error deleting log entry: " . mysqli_error($conn);
    }
}

// Add test log
if (isset($_POST['add_test_log'])) {
    $log_type = sanitize($_POST['log_type']);
    $message = sanitize($_POST['message']);
    $details = sanitize($_POST['details']);
    
    $insert_query = "INSERT INTO system_logs (log_type, message, details) 
                   VALUES ('$log_type', '$message', '$details')";
    
    if (mysqli_query($conn, $insert_query)) {
        $success_message = "Test log added successfully.";
    } else {
        $error_message = "Error adding test log: " . mysqli_error($conn);
    }
}

// Get logs with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Filter by log type
$log_type_filter = isset($_GET['log_type']) ? sanitize($_GET['log_type']) : '';
$where_clause = '';

if (!empty($log_type_filter)) {
    $where_clause = "WHERE log_type = '$log_type_filter'";
}

// Get total logs count
$count_query = "SELECT COUNT(*) as total FROM system_logs $where_clause";
$count_result = mysqli_query($conn, $count_query);
$total_logs = mysqli_fetch_assoc($count_result)['total'];
$total_pages = ceil($total_logs / $limit);

// Get logs
$logs_query = "SELECT * FROM system_logs $where_clause ORDER BY created_at DESC LIMIT $offset, $limit";
$logs_result = mysqli_query($conn, $logs_query);

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title d-flex align-items-center">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>সিস্টেম লগ</h1>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <?php if($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>
        
        <?php if($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="fw-bold text-primary mb-0">লগ ফিল্টার</h6>
                            <div>
                                <form method="POST" action="" class="d-inline">
                                    <button type="submit" name="clear_logs" class="btn btn-sm btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি সমস্ত লগ মুছতে চান?');">
                                        <i class="fas fa-trash me-1"></i> সব লগ মুছুন
                                    </button>
                                </form>
                                <button type="button" class="btn btn-sm btn-primary ms-2" data-bs-toggle="modal" data-bs-target="#addTestLogModal">
                                    <i class="fas fa-plus me-1"></i> টেস্ট লগ যোগ করুন
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="log_type" class="form-label">লগ টাইপ</label>
                                        <select class="form-select" id="log_type" name="log_type">
                                            <option value="">সব</option>
                                            <option value="info" <?php echo $log_type_filter === 'info' ? 'selected' : ''; ?>>Info</option>
                                            <option value="warning" <?php echo $log_type_filter === 'warning' ? 'selected' : ''; ?>>Warning</option>
                                            <option value="error" <?php echo $log_type_filter === 'error' ? 'selected' : ''; ?>>Error</option>
                                            <option value="success" <?php echo $log_type_filter === 'success' ? 'selected' : ''; ?>>Success</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <div class="mb-3">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-filter me-1"></i> ফিল্টার
                                        </button>
                                        <?php if(!empty($log_type_filter)): ?>
                                        <a href="logs.php" class="btn btn-secondary ms-2">
                                            <i class="fas fa-times me-1"></i> রিসেট
                                        </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="fw-bold text-primary mb-0">লগ সারাংশ</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php
                            // Get log counts by type
                            $log_counts = [
                                'info' => 0,
                                'warning' => 0,
                                'error' => 0,
                                'success' => 0
                            ];
                            
                            $count_by_type_query = "SELECT log_type, COUNT(*) as count FROM system_logs GROUP BY log_type";
                            $count_by_type_result = mysqli_query($conn, $count_by_type_query);
                            
                            while ($row = mysqli_fetch_assoc($count_by_type_result)) {
                                $log_counts[$row['log_type']] = $row['count'];
                            }
                            ?>
                            <div class="col-6 mb-3">
                                <div class="card bg-info bg-opacity-10 border-info">
                                    <div class="card-body text-center py-2">
                                        <h3 class="mb-0 text-info"><?php echo $log_counts['info']; ?></h3>
                                        <small class="text-muted">Info</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="card bg-warning bg-opacity-10 border-warning">
                                    <div class="card-body text-center py-2">
                                        <h3 class="mb-0 text-warning"><?php echo $log_counts['warning']; ?></h3>
                                        <small class="text-muted">Warning</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="card bg-danger bg-opacity-10 border-danger">
                                    <div class="card-body text-center py-2">
                                        <h3 class="mb-0 text-danger"><?php echo $log_counts['error']; ?></h3>
                                        <small class="text-muted">Error</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="card bg-success bg-opacity-10 border-success">
                                    <div class="card-body text-center py-2">
                                        <h3 class="mb-0 text-success"><?php echo $log_counts['success']; ?></h3>
                                        <small class="text-muted">Success</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h6 class="fw-bold text-primary mb-0">সিস্টেম লগ</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead>
                            <tr>
                                <th>আইডি</th>
                                <th>টাইপ</th>
                                <th>মেসেজ</th>
                                <th>তারিখ</th>
                                <th>অ্যাকশন</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if(mysqli_num_rows($logs_result) > 0): ?>
                                <?php while($log = mysqli_fetch_assoc($logs_result)): ?>
                                <tr>
                                    <td><?php echo $log['id']; ?></td>
                                    <td>
                                        <?php if($log['log_type'] === 'info'): ?>
                                        <span class="badge bg-info">Info</span>
                                        <?php elseif($log['log_type'] === 'warning'): ?>
                                        <span class="badge bg-warning">Warning</span>
                                        <?php elseif($log['log_type'] === 'error'): ?>
                                        <span class="badge bg-danger">Error</span>
                                        <?php elseif($log['log_type'] === 'success'): ?>
                                        <span class="badge bg-success">Success</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo $log['message']; ?></td>
                                    <td><?php echo date('Y-m-d H:i:s', strtotime($log['created_at'])); ?></td>
                                    <td>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-primary view-details" data-bs-toggle="modal" data-bs-target="#logDetailsModal" data-log-id="<?php echo $log['id']; ?>" data-log-type="<?php echo $log['log_type']; ?>" data-log-message="<?php echo htmlspecialchars($log['message']); ?>" data-log-details="<?php echo htmlspecialchars($log['details']); ?>" data-log-date="<?php echo date('Y-m-d H:i:s', strtotime($log['created_at'])); ?>">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <a href="logs.php?delete=<?php echo $log['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই লগ এন্ট্রি মুছতে চান?');">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="5" class="text-center">কোন লগ পাওয়া যায়নি।</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <?php if($total_pages > 1): ?>
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo !empty($log_type_filter) ? '&log_type=' . $log_type_filter : ''; ?>" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        <?php for($i = 1; $i <= $total_pages; $i++): ?>
                        <li class="page-item <?php echo $page == $i ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($log_type_filter) ? '&log_type=' . $log_type_filter : ''; ?>"><?php echo $i; ?></a>
                        </li>
                        <?php endfor; ?>
                        <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo !empty($log_type_filter) ? '&log_type=' . $log_type_filter : ''; ?>" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Log Details Modal -->
<div class="modal fade" id="logDetailsModal" tabindex="-1" aria-labelledby="logDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="logDetailsModalLabel">লগ বিস্তারিত</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label fw-bold">আইডি</label>
                    <p id="log-id"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">টাইপ</label>
                    <p id="log-type"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">মেসেজ</label>
                    <p id="log-message"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">বিস্তারিত</label>
                    <p id="log-details"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">তারিখ</label>
                    <p id="log-date"></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বন্ধ করুন</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Test Log Modal -->
<div class="modal fade" id="addTestLogModal" tabindex="-1" aria-labelledby="addTestLogModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addTestLogModalLabel">টেস্ট লগ যোগ করুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="POST" action="">
                    <div class="mb-3">
                        <label for="log_type_input" class="form-label">লগ টাইপ</label>
                        <select class="form-select" id="log_type_input" name="log_type" required>
                            <option value="info">Info</option>
                            <option value="warning">Warning</option>
                            <option value="error">Error</option>
                            <option value="success">Success</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="message" class="form-label">মেসেজ</label>
                        <input type="text" class="form-control" id="message" name="message" required>
                    </div>
                    <div class="mb-3">
                        <label for="details" class="form-label">বিস্তারিত</label>
                        <textarea class="form-control" id="details" name="details" rows="3"></textarea>
                    </div>
                    <div class="d-grid">
                        <button type="submit" name="add_test_log" class="btn btn-primary">লগ যোগ করুন</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Log details modal
document.addEventListener('DOMContentLoaded', function() {
    const viewButtons = document.querySelectorAll('.view-details');
    
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const logId = this.getAttribute('data-log-id');
            const logType = this.getAttribute('data-log-type');
            const logMessage = this.getAttribute('data-log-message');
            const logDetails = this.getAttribute('data-log-details');
            const logDate = this.getAttribute('data-log-date');
            
            document.getElementById('log-id').textContent = logId;
            
            let typeHtml = '';
            if (logType === 'info') {
                typeHtml = '<span class="badge bg-info">Info</span>';
            } else if (logType === 'warning') {
                typeHtml = '<span class="badge bg-warning">Warning</span>';
            } else if (logType === 'error') {
                typeHtml = '<span class="badge bg-danger">Error</span>';
            } else if (logType === 'success') {
                typeHtml = '<span class="badge bg-success">Success</span>';
            }
            
            document.getElementById('log-type').innerHTML = typeHtml;
            document.getElementById('log-message').textContent = logMessage;
            document.getElementById('log-details').textContent = logDetails || 'No details available';
            document.getElementById('log-date').textContent = logDate;
        });
    });
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
