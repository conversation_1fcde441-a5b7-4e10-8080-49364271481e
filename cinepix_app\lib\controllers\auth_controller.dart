import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:cinepix_app/models/user.dart';
import 'package:cinepix_app/services/api_service.dart';
import 'package:cinepix_app/services/storage_service.dart';

class AuthController extends GetxController {
  final ApiService _apiService = ApiService();
  final StorageService _storageService = StorageService();

  final Rx<User?> user = Rx<User?>(null);
  final RxBool isLoading = false.obs;
  final RxBool isLoggedIn = false.obs;
  final RxBool isPremium = false.obs;
  final RxString errorMessage = ''.obs;

  // Auto refresh timer
  Timer? _profileRefreshTimer;
  static const Duration _refreshInterval = Duration(minutes: 5);

  @override
  void onInit() {
    super.onInit();
    checkLoginStatus();
  }

  @override
  void onClose() {
    _profileRefreshTimer?.cancel();
    super.onClose();
  }

  // Check if user is logged in
  Future<void> checkLoginStatus() async {
    isLoading.value = true;

    try {
      final loggedIn = await _storageService.isLoggedIn();
      isLoggedIn.value = loggedIn;

      if (loggedIn) {
        final userData = await _storageService.getUser();
        user.value = userData;
        isPremium.value = userData?.isPremium ?? false;

        // Start auto refresh timer for logged in users
        _startAutoRefresh();
      }
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }

  // Login user
  Future<bool> login(String username, String password) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final data = await _apiService.login(username, password);

      user.value = User.fromJson(data['user']);
      isPremium.value = user.value?.isPremium ?? false;
      isLoggedIn.value = true;

      // Start auto refresh timer after successful login
      _startAutoRefresh();

      return true;
    } catch (e) {
      errorMessage.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Register user
  Future<bool> register(
      String username, String email, String password, String name) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      await _apiService.register(username, email, password, name);
      return true;
    } catch (e) {
      errorMessage.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Logout user
  Future<void> logout() async {
    isLoading.value = true;

    try {
      await _apiService.logout();

      // Stop auto refresh timer
      _profileRefreshTimer?.cancel();
      _profileRefreshTimer = null;

      user.value = null;
      isLoggedIn.value = false;
      isPremium.value = false;
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }

  // Refresh user profile
  Future<void> refreshProfile() async {
    if (!isLoggedIn.value) return;

    try {
      final newUser = await _apiService.getUserProfile();
      final oldPremiumStatus = isPremium.value;

      user.value = newUser;
      isPremium.value = newUser.isPremium;

      // Save updated user data
      await _storageService.saveUser(newUser);

      // Check if premium status changed
      if (oldPremiumStatus != isPremium.value) {
        debugPrint(
            'Premium status changed: $oldPremiumStatus -> ${isPremium.value}');
      }
    } catch (e) {
      debugPrint('Failed to refresh profile: $e');
    }
  }

  // Start auto refresh timer
  void _startAutoRefresh() {
    _profileRefreshTimer?.cancel();
    _profileRefreshTimer = Timer.periodic(_refreshInterval, (timer) {
      if (isLoggedIn.value) {
        refreshProfile();
      } else {
        timer.cancel();
      }
    });
  }

  // Manual refresh with notification
  Future<void> forceRefreshProfile() async {
    await refreshProfile();

    // Show notification if premium status changed
    if (user.value != null) {
      Get.snackbar(
        'Profile Updated',
        'আপনার প্রোফাইল আপডেট হয়েছে',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 2),
      );
    }
  }

  // Check if user can access premium content
  bool canAccessPremium() {
    return isLoggedIn.value && isPremium.value;
  }

  // Check premium status with expiry
  bool isPremiumActive() {
    if (!isPremium.value) return false;

    if (user.value?.premiumExpires != null) {
      try {
        final expiryDate = DateTime.parse(user.value!.premiumExpires!);
        return DateTime.now().isBefore(expiryDate);
      } catch (e) {
        return isPremium.value;
      }
    }

    return isPremium.value;
  }
}
