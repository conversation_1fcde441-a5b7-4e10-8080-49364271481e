<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set page title
$page_title = 'পেজ স্ট্যাটাস চেক';
$current_page = 'page_status_check.php';

// Start session
session_start();

// Set test session data
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';
$_SESSION['user_role'] = 'admin';
$_SESSION['is_premium'] = 1;

// Define all required pages
$required_pages = [
    'index.php' => 'ড্যাশবোর্ড',
    'movies.php' => 'মুভি ম্যানেজমেন্ট',
    'tvshows.php' => 'টিভি শো ম্যানেজমেন্ট',
    'episodes.php' => 'এপিসোড ম্যানেজমেন্ট',
    'categories.php' => 'ক্যাটাগরি ম্যানেজমেন্ট',
    'users.php' => 'ইউজার ম্যানেজমেন্ট',
    'payments.php' => 'পেমেন্ট ম্যানেজমেন্ট',
    'analytics.php' => 'অ্যানালিটিক্স ও রিপোর্ট',
    'live_tracking.php' => 'লাইভ ইউজার ট্র্যাকিং',
    'activity_logs.php' => 'অ্যাক্টিভিটি লগ',
    'settings.php' => 'সেটিংস',
    'mobile_test.php' => 'মোবাইল টেস্ট',
    'test_all_features.php' => 'সব ফিচার টেস্ট'
];

// Check page status
$page_status = [];
foreach ($required_pages as $file => $name) {
    $file_path = __DIR__ . '/' . $file;
    $status = [
        'name' => $name,
        'file' => $file,
        'exists' => file_exists($file_path),
        'size' => file_exists($file_path) ? filesize($file_path) : 0,
        'modified' => file_exists($file_path) ? filemtime($file_path) : 0,
        'mobile_ready' => false,
        'responsive_css' => false,
        'mobile_nav' => false
    ];
    
    if ($status['exists']) {
        $content = file_get_contents($file_path);
        
        // Check for mobile-specific features
        $status['mobile_ready'] = strpos($content, 'responsive.css') !== false;
        $status['responsive_css'] = strpos($content, '@media') !== false || strpos($content, 'responsive.css') !== false;
        $status['mobile_nav'] = strpos($content, 'mobile-nav') !== false || strpos($content, 'mobileMenuToggle') !== false;
    }
    
    $page_status[] = $status;
}

// Check CSS and JS files
$asset_files = [
    'assets/css/admin-style.css' => 'মেইন স্টাইল',
    'assets/css/responsive.css' => 'রেস্পন্সিভ স্টাইল',
    'assets/js/admin-script.js' => 'মেইন স্ক্রিপ্ট'
];

$asset_status = [];
foreach ($asset_files as $file => $name) {
    $file_path = __DIR__ . '/' . $file;
    $asset_status[] = [
        'name' => $name,
        'file' => $file,
        'exists' => file_exists($file_path),
        'size' => file_exists($file_path) ? filesize($file_path) : 0,
        'modified' => file_exists($file_path) ? filemtime($file_path) : 0
    ];
}

// Include header
include 'includes/header.php';
?>

<!-- Include Sidebar -->
<?php include 'includes/sidebar.php'; ?>

<!-- Main Content -->
<div class="main-content">
    <!-- Page Header -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-title">
                    <i class="fas fa-check-circle me-3"></i>পেজ স্ট্যাটাস চেক
                </h1>
                <p class="page-subtitle text-muted">সব পেজের স্ট্যাটাস এবং মোবাইল কম্প্যাটিবিলিটি চেক</p>
            </div>
            <div class="col-auto">
                <div class="page-actions">
                    <button class="btn btn-primary" onclick="runFullCheck()">
                        <i class="fas fa-sync-alt me-2"></i>সম্পূর্ণ চেক
                    </button>
                    <button class="btn btn-outline-success" onclick="toggleMobileDebug()">
                        <i class="fas fa-mobile-alt me-2"></i>মোবাইল ডিবাগ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Overall Status -->
    <div class="row mb-4">
        <?php
        $total_pages = count($page_status);
        $existing_pages = count(array_filter($page_status, function($p) { return $p['exists']; }));
        $mobile_ready_pages = count(array_filter($page_status, function($p) { return $p['mobile_ready']; }));
        $completion_percentage = ($existing_pages / $total_pages) * 100;
        $mobile_percentage = ($mobile_ready_pages / $total_pages) * 100;
        ?>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stat-card bg-gradient-primary">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-file-code"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number"><?php echo $existing_pages; ?>/<?php echo $total_pages; ?></h3>
                                <p class="stat-label">পেজ তৈরি</p>
                                <small class="stat-sublabel">
                                    <?php echo round($completion_percentage, 1); ?>% সম্পন্ন
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stat-card bg-gradient-success">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number"><?php echo $mobile_ready_pages; ?>/<?php echo $total_pages; ?></h3>
                                <p class="stat-label">মোবাইল রেডি</p>
                                <small class="stat-sublabel">
                                    <?php echo round($mobile_percentage, 1); ?>% মোবাইল
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stat-card bg-gradient-warning">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-code"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number"><?php echo count($asset_status); ?></h3>
                                <p class="stat-label">অ্যাসেট ফাইল</p>
                                <small class="stat-sublabel">
                                    CSS + JS
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stat-card bg-gradient-info">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-check-double"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number"><?php echo $completion_percentage >= 100 ? '✅' : '⏳'; ?></h3>
                                <p class="stat-label">স্ট্যাটাস</p>
                                <small class="stat-sublabel">
                                    <?php echo $completion_percentage >= 100 ? 'সম্পন্ন' : 'চলমান'; ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Page Status Table -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>পেজ স্ট্যাটাস
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>পেজ নাম</th>
                                    <th>ফাইল</th>
                                    <th>স্ট্যাটাস</th>
                                    <th>সাইজ</th>
                                    <th>মোবাইল রেডি</th>
                                    <th>শেষ আপডেট</th>
                                    <th>অ্যাকশন</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($page_status as $page): ?>
                                    <tr>
                                        <td data-label="পেজ নাম">
                                            <div class="fw-bold"><?php echo htmlspecialchars($page['name']); ?></div>
                                        </td>
                                        <td data-label="ফাইল">
                                            <code><?php echo htmlspecialchars($page['file']); ?></code>
                                        </td>
                                        <td data-label="স্ট্যাটাস">
                                            <?php if ($page['exists']): ?>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>বিদ্যমান
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times me-1"></i>অনুপস্থিত
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td data-label="সাইজ">
                                            <?php if ($page['exists']): ?>
                                                <?php echo number_format($page['size'] / 1024, 1); ?> KB
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td data-label="মোবাইল রেডি">
                                            <div class="mobile-status">
                                                <?php if ($page['mobile_ready']): ?>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-mobile-alt me-1"></i>রেডি
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">
                                                        <i class="fas fa-exclamation-triangle me-1"></i>প্রয়োজন
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td data-label="শেষ আপডেট">
                                            <?php if ($page['exists']): ?>
                                                <small><?php echo date('d/m/Y H:i', $page['modified']); ?></small>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td data-label="অ্যাকশন">
                                            <div class="btn-group btn-group-sm">
                                                <?php if ($page['exists']): ?>
                                                    <a href="<?php echo $page['file']; ?>" class="btn btn-outline-primary" title="ভিজিট করুন" target="_blank">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                    <button class="btn btn-outline-info" onclick="checkMobileView('<?php echo $page['file']; ?>')" title="মোবাইল ভিউ চেক">
                                                        <i class="fas fa-mobile-alt"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <button class="btn btn-outline-danger" disabled title="ফাইল অনুপস্থিত">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Asset Files Status -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-code me-2"></i>অ্যাসেট ফাইল স্ট্যাটাস
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>ফাইল নাম</th>
                                    <th>পাথ</th>
                                    <th>স্ট্যাটাস</th>
                                    <th>সাইজ</th>
                                    <th>শেষ আপডেট</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($asset_status as $asset): ?>
                                    <tr>
                                        <td data-label="ফাইল নাম">
                                            <div class="fw-bold"><?php echo htmlspecialchars($asset['name']); ?></div>
                                        </td>
                                        <td data-label="পাথ">
                                            <code><?php echo htmlspecialchars($asset['file']); ?></code>
                                        </td>
                                        <td data-label="স্ট্যাটাস">
                                            <?php if ($asset['exists']): ?>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>বিদ্যমান
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times me-1"></i>অনুপস্থিত
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td data-label="সাইজ">
                                            <?php if ($asset['exists']): ?>
                                                <?php echo number_format($asset['size'] / 1024, 1); ?> KB
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td data-label="শেষ আপডেট">
                                            <?php if ($asset['exists']): ?>
                                                <small><?php echo date('d/m/Y H:i', $asset['modified']); ?></small>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Page-specific scripts
$page_scripts = '
<script>
function runFullCheck() {
    cinepixAdmin.showToast("সম্পূর্ণ চেক শুরু হয়েছে...", "info");
    
    // Simulate full check
    setTimeout(() => {
        location.reload();
    }, 2000);
}

function toggleMobileDebug() {
    document.body.classList.toggle("debug-mode");
    
    if (document.body.classList.contains("debug-mode")) {
        cinepixAdmin.showToast("মোবাইল ডিবাগ মোড চালু করা হয়েছে", "success");
    } else {
        cinepixAdmin.showToast("মোবাইল ডিবাগ মোড বন্ধ করা হয়েছে", "info");
    }
}

function checkMobileView(page) {
    // Open page in mobile view
    const mobileUrl = page + "?mobile_debug=1";
    window.open(mobileUrl, "_blank", "width=375,height=667");
    cinepixAdmin.showToast(`${page} মোবাইল ভিউতে খোলা হয়েছে`, "info");
}

// Auto-refresh every 30 seconds
setInterval(() => {
    const lastUpdate = document.querySelector(".page-subtitle");
    if (lastUpdate) {
        lastUpdate.textContent = "শেষ আপডেট: " + new Date().toLocaleTimeString("bn-BD");
    }
}, 30000);

// Show mobile indicator if in mobile view
document.addEventListener("DOMContentLoaded", function() {
    if (window.innerWidth <= 575) {
        document.body.classList.add("debug-mode");
        setTimeout(() => {
            document.body.classList.remove("debug-mode");
        }, 3000);
    }
});
</script>
';

// Include footer
include 'includes/footer.php';
?>
