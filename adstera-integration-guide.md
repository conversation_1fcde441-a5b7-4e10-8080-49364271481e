# Adstera এড ইন্টিগ্রেশন গাইড

## Adstera কি?

Adstera হল একটি জনপ্রিয় এড নেটওয়ার্ক যা ওয়েবসাইট এবং মোবাইল অ্যাপে বিজ্ঞাপন প্রদর্শনের জন্য ব্যবহৃত হয়। এটি Google AdSense এর একটি বিকল্প এবং অনেক সময় বেশি রেভিনিউ দেয়।

## Adstera এড ইন্টিগ্রেশন ধাপসমূহ

### ১. Adstera অ্যাকাউন্ট তৈরি

1. [Adstera.com](https://adstera.com) এ যান
2. "Sign Up" বাটনে ক্লিক করুন
3. আপনার তথ্য দিয়ে অ্যাকাউন্ট তৈরি করুন
4. ইমেইল ভেরিফিকেশন করুন

### ২. সাইট রেজিস্টার

1. Adstera ড্যাশবোর্ডে লগইন করুন
2. "Add Site" বাটনে ক্লিক করুন
3. আপনার সাইটের URL দিন: `https://yourdomain.com`
4. সাইটের ক্যাটাগরি নির্বাচন করুন: "Entertainment" বা "Movies"
5. সাইটের বিবরণ দিন

### ৩. এড ইউনিট তৈরি

#### Banner Ads (728x90, 300x250, 320x50)
```html
<!-- Adstera Banner Ad -->
<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-YOUR_PUBLISHER_ID"></script>
<ins class="adsbygoogle"
     style="display:block"
     data-ad-client="ca-pub-YOUR_PUBLISHER_ID"
     data-ad-slot="YOUR_AD_SLOT"
     data-ad-format="auto"
     data-full-width-responsive="true"></ins>
<script>
     (adsbygoogle = window.adsbygoogle || []).push({});
</script>
```

#### Responsive Ads
```html
<!-- Adstera Responsive Ad -->
<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-YOUR_PUBLISHER_ID"></script>
<ins class="adsbygoogle"
     style="display:block"
     data-ad-client="ca-pub-YOUR_PUBLISHER_ID"
     data-ad-slot="YOUR_AD_SLOT"
     data-ad-format="auto"
     data-full-width-responsive="true"></ins>
<script>
     (adsbygoogle = window.adsbygoogle || []).push({});
</script>
```

### ৪. সাইটে এড প্লেসমেন্ট

#### হোম পেজে এড প্লেসমেন্ট

```php
<!-- Top Banner Ad -->
<div class="ad-banner ad-banner-top">
    <div class="container">
        <div class="ad-placeholder">
            <div class="ad-label">Advertisement</div>
            <div class="ad-content">
                <!-- Adstera Ad Code Here -->
            </div>
        </div>
    </div>
</div>

<!-- Middle Rectangle Ad -->
<div class="ad-banner ad-banner-middle">
    <div class="container">
        <div class="ad-placeholder">
            <div class="ad-label">Advertisement</div>
            <div class="ad-content">
                <!-- Adstera Ad Code Here -->
            </div>
        </div>
    </div>
</div>

<!-- Bottom Banner Ad -->
<div class="ad-banner ad-banner-bottom">
    <div class="container">
        <div class="ad-placeholder">
            <div class="ad-label">Advertisement</div>
            <div class="ad-content">
                <!-- Adstera Ad Code Here -->
            </div>
        </div>
    </div>
</div>
```

#### সাইডবার এড

```php
<!-- Sidebar Ad -->
<div class="sidebar-ad">
    <div class="ad-placeholder">
        <div class="ad-label">Advertisement</div>
        <div class="ad-content">
            <!-- Adstera Ad Code Here -->
        </div>
    </div>
</div>
```

#### কনটেন্ট মাঝে এড

```php
<!-- In-Content Ad -->
<div class="in-content-ad">
    <div class="ad-placeholder">
        <div class="ad-label">Advertisement</div>
        <div class="ad-content">
            <!-- Adstera Ad Code Here -->
        </div>
    </div>
</div>
```

### ৫. এড স্টাইলিং

```css
/* Ad Banner Styling */
.ad-banner {
    padding: 20px 0;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    border-bottom: 1px solid #e9ecef;
    margin: 20px 0;
}

.ad-placeholder {
    background: #fff;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.ad-label {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.ad-content {
    min-height: 90px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Responsive Ad Styling */
@media (max-width: 768px) {
    .ad-banner {
        padding: 15px 0;
    }
    
    .ad-placeholder {
        padding: 15px;
    }
    
    .ad-content {
        min-height: 60px;
    }
}
```

### ৬. এড প্লেসমেন্ট স্ট্র্যাটেজি

#### সর্বোত্তম এড প্লেসমেন্ট:

1. **হেডার এড** - সাইটের শীর্ষে (728x90)
2. **সাইডবার এড** - ডান পাশে (300x250)
3. **কনটেন্ট মাঝে এড** - আর্টিকেল মাঝে (300x250)
4. **ফুটার এড** - সাইটের নিচে (728x90)
5. **মোবাইল এড** - মোবাইল ডিভাইসে (320x50)

#### এড লোডিং অপটিমাইজেশন:

```javascript
// Lazy Loading for Ads
document.addEventListener('DOMContentLoaded', function() {
    const adElements = document.querySelectorAll('.ad-content');
    
    const adObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Load ad when it comes into view
                loadAd(entry.target);
                adObserver.unobserve(entry.target);
            }
        });
    });
    
    adElements.forEach(ad => {
        adObserver.observe(ad);
    });
});

function loadAd(adElement) {
    // Adstera ad loading logic here
    if (typeof adsbygoogle !== 'undefined') {
        (adsbygoogle = window.adsbygoogle || []).push({});
    }
}
```

### ৭. এড ব্লকার ডিটেকশন

```javascript
// Ad Blocker Detection
function detectAdBlocker() {
    let adBlockEnabled = false;
    const testAd = document.createElement('div');
    testAd.innerHTML = '&nbsp;';
    testAd.className = 'adsbox';
    document.body.appendChild(testAd);
    
    window.setTimeout(function() {
        if (testAd.offsetHeight === 0) {
            adBlockEnabled = true;
        }
        testAd.remove();
        
        if (adBlockEnabled) {
            showAdBlockerMessage();
        }
    }, 100);
}

function showAdBlockerMessage() {
    const message = document.createElement('div');
    message.innerHTML = `
        <div class="ad-blocker-message">
            <h3>Ad Blocker Detected</h3>
            <p>Please disable your ad blocker to support our site and continue enjoying free content.</p>
            <button onclick="this.parentElement.remove()">Close</button>
        </div>
    `;
    document.body.appendChild(message);
}

// Run detection on page load
document.addEventListener('DOMContentLoaded', detectAdBlocker);
```

### ৮. এড পারফরম্যান্স মনিটরিং

```javascript
// Ad Performance Tracking
function trackAdPerformance(adSlot) {
    const startTime = performance.now();
    
    // Track ad load time
    window.addEventListener('load', function() {
        const loadTime = performance.now() - startTime;
        console.log(`Ad ${adSlot} loaded in ${loadTime}ms`);
        
        // Send to analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', 'ad_load_time', {
                'ad_slot': adSlot,
                'load_time': loadTime
            });
        }
    });
}
```

### ৯. এড রেভিনিউ অপটিমাইজেশন টিপস

1. **এড প্লেসমেন্ট** - ইউজার এক্সপেরিয়েন্স নষ্ট না করে এড রাখুন
2. **এড সাইজ** - ডিভাইস অনুযায়ী সঠিক সাইজ ব্যবহার করুন
3. **এড ফ্রিকোয়েন্সি** - খুব বেশি এড না দিয়ে ব্যালেন্স রাখুন
4. **এড কনটেন্ট** - কনটেন্টের সাথে মিল রেখে এড দেখান
5. **এড টেস্টিং** - বিভিন্ন ডিভাইসে এড টেস্ট করুন

### ১০. ট্রাবলশুটিং

#### সাধারণ সমস্যা ও সমাধান:

1. **এড লোড হচ্ছে না**
   - Publisher ID সঠিক কিনা চেক করুন
   - Ad Slot ID সঠিক কিনা চেক করুন
   - সাইট approved হয়েছে কিনা চেক করুন

2. **এড সাইজ ঠিক নেই**
   - CSS স্টাইলিং চেক করুন
   - Responsive ad format ব্যবহার করুন

3. **এড ক্লিক হচ্ছে না**
   - Ad blocker চেক করুন
   - Ad code সঠিক কিনা চেক করুন

### ১১. সিকিউরিটি টিপস

1. **HTTPS ব্যবহার করুন** - সব এড HTTPS এর মাধ্যমে লোড করুন
2. **Content Security Policy** - CSP হেডার সেট করুন
3. **Ad Verification** - শুধুমাত্র verified ads accept করুন

### ১২. কন্টাক্ট

Adstera Support: <EMAIL>
Adstera Documentation: https://adstera.com/docs

---

**নোট:** এই গাইড অনুসরণ করে আপনি আপনার সাইটে Adstera এড সফলভাবে ইন্টিগ্রেট করতে পারবেন। মনে রাখবেন, এড প্লেসমেন্টে ইউজার এক্সপেরিয়েন্সের দিকে খেয়াল রাখতে হবে। 