// Service Worker for CinePix PWA
const CACHE_NAME = 'cinepix-pwa-v1';

// Files to cache - using relative paths for localhost compatibility
const urlsToCache = [
  './',
  './index.php',
  './css/style.css',
  './css/design-improvements.css',
  './js/main.js',
  './js/card-improvements.js',
  './manifest.json',
  './pwa-icons/icon-192x192.jpg',
  './pwa-icons/icon-512x512.jpg'
];

// Install event - cache assets
self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Opened cache');
        return cache.addAll(urlsToCache);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  const cacheWhitelist = [CACHE_NAME];
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheWhitelist.indexOf(cacheName) === -1) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// Fetch event - serve from cache or network
self.addEventListener('fetch', event => {
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // Cache hit - return response
        if (response) {
          return response;
        }

        // Clone the request
        const fetchRequest = event.request.clone();

        return fetch(fetchRequest).then(
          response => {
            // Check if valid response
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }

            // Clone the response
            const responseToCache = response.clone();

            // Cache the fetched response
            caches.open(CACHE_NAME)
              .then(cache => {
                // Don't cache API calls or dynamic content
                if (!event.request.url.includes('/includes/') &&
                    !event.request.url.includes('/admin/') &&
                    !event.request.url.includes('?')) {
                  cache.put(event.request, responseToCache);
                }
              });

            return response;
          }
        );
      })
      .catch(() => {
        // If both cache and network fail, show a generic fallback
        if (event.request.url.indexOf('.html') > -1 ||
            event.request.url.indexOf('.php') > -1 ||
            event.request.url === '/') {
          return caches.match('/index.php');
        }
      })
  );
});

// Handle push notifications
self.addEventListener('push', event => {
  const title = 'CinePix';
  const options = {
    body: event.data.text(),
    icon: '/pwa-icons/icon-192x192.png',
    badge: '/pwa-icons/icon-72x72.png'
  };

  event.waitUntil(self.registration.showNotification(title, options));
});

// Handle notification clicks
self.addEventListener('notificationclick', event => {
  event.notification.close();

  event.waitUntil(
    clients.openWindow('/')
  );
});
