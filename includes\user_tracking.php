<?php
/**
 * User Activity Tracking System
 * Include this file in all pages to track user activity
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Generate session ID if not exists
if (!isset($_SESSION['tracking_session_id'])) {
    $_SESSION['tracking_session_id'] = session_id() . '_' . time();
}

// Function to get user's real IP address
function getUserIP() {
    $ip_keys = ['HTTP_CF_CONNECTING_IP', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];
    
    foreach ($ip_keys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

// Function to detect device type
function getDeviceType($user_agent) {
    if (preg_match('/mobile|android|iphone|ipad|phone/i', $user_agent)) {
        return 'Mobile';
    } elseif (preg_match('/tablet|ipad/i', $user_agent)) {
        return 'Tablet';
    } else {
        return 'Desktop';
    }
}

// Function to detect browser
function getBrowser($user_agent) {
    $browsers = [
        'Chrome' => '/chrome/i',
        'Firefox' => '/firefox/i',
        'Safari' => '/safari/i',
        'Edge' => '/edge/i',
        'Opera' => '/opera/i',
        'Internet Explorer' => '/msie/i'
    ];
    
    foreach ($browsers as $browser => $pattern) {
        if (preg_match($pattern, $user_agent)) {
            return $browser;
        }
    }
    
    return 'Unknown';
}

// Function to get location info (basic)
function getLocationInfo($ip) {
    // You can integrate with IP geolocation services like:
    // - ipapi.co
    // - ipinfo.io
    // - freegeoip.app
    
    $location = ['country' => null, 'city' => null];
    
    try {
        // Using ipapi.co (free tier: 1000 requests/day)
        $response = @file_get_contents("http://ipapi.co/{$ip}/json/");
        if ($response) {
            $data = json_decode($response, true);
            if (isset($data['country_name'])) {
                $location['country'] = $data['country_name'];
                $location['city'] = $data['city'] ?? null;
            }
        }
    } catch (Exception $e) {
        // Ignore errors and continue without location
    }
    
    return $location;
}

// Track user activity
function trackUserActivity() {
    global $conn;
    
    if (!isset($conn) || !$conn) {
        return; // No database connection
    }
    
    $session_id = $_SESSION['tracking_session_id'];
    $user_id = isset($_SESSION['user_id']) ? (int)$_SESSION['user_id'] : null;
    $ip_address = getUserIP();
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $current_page = $_SERVER['REQUEST_URI'] ?? '';
    $referrer = $_SERVER['HTTP_REFERER'] ?? '';
    $device_type = getDeviceType($user_agent);
    $browser = getBrowser($user_agent);
    
    // Check if user is premium
    $is_premium = false;
    if ($user_id) {
        $premium_check = mysqli_query($conn, "SELECT premium_status FROM users WHERE id = $user_id");
        if ($premium_check && $row = mysqli_fetch_assoc($premium_check)) {
            $is_premium = ($row['premium_status'] == 'active');
        }
    }
    
    // Check if this session already exists (using simple query for compatibility)
    $check_query = "SELECT id, page_views, user_id, is_premium FROM user_activity WHERE session_id = '" . mysqli_real_escape_string($conn, $session_id) . "'";
    $result = mysqli_query($conn, $check_query);

    if ($result && mysqli_num_rows($result) > 0) {
        $existing = mysqli_fetch_assoc($result);

        // Update existing record using simple query
        $new_page_views = $existing['page_views'] + 1;
        $user_id_safe = $user_id ? (int)$user_id : 'NULL';
        $current_page_safe = mysqli_real_escape_string($conn, $current_page);
        $referrer_safe = mysqli_real_escape_string($conn, $referrer);
        $device_type_safe = mysqli_real_escape_string($conn, $device_type);
        $browser_safe = mysqli_real_escape_string($conn, $browser);
        $is_premium_safe = $is_premium ? 1 : 0;
        $session_id_safe = mysqli_real_escape_string($conn, $session_id);

        $update_query = "UPDATE user_activity SET
                        user_id = $user_id_safe,
                        current_page = '$current_page_safe',
                        referrer = '$referrer_safe',
                        device_type = '$device_type_safe',
                        browser = '$browser_safe',
                        is_premium = $is_premium_safe,
                        last_activity = NOW(),
                        page_views = $new_page_views
                        WHERE session_id = '$session_id_safe'";

        mysqli_query($conn, $update_query);
    } else {
        // Get location info for new users
        $location = getLocationInfo($ip_address);

        // Insert new record using simple query
        $user_id_safe = $user_id ? (int)$user_id : 'NULL';
        $session_id_safe = mysqli_real_escape_string($conn, $session_id);
        $ip_address_safe = mysqli_real_escape_string($conn, $ip_address);
        $user_agent_safe = mysqli_real_escape_string($conn, $user_agent);
        $current_page_safe = mysqli_real_escape_string($conn, $current_page);
        $referrer_safe = mysqli_real_escape_string($conn, $referrer);
        $country_safe = mysqli_real_escape_string($conn, $location['country'] ?? '');
        $city_safe = mysqli_real_escape_string($conn, $location['city'] ?? '');
        $device_type_safe = mysqli_real_escape_string($conn, $device_type);
        $browser_safe = mysqli_real_escape_string($conn, $browser);
        $is_premium_safe = $is_premium ? 1 : 0;

        $insert_query = "INSERT INTO user_activity
                        (user_id, session_id, ip_address, user_agent, current_page,
                         referrer, country, city, device_type, browser, is_premium)
                        VALUES ($user_id_safe, '$session_id_safe', '$ip_address_safe', '$user_agent_safe',
                               '$current_page_safe', '$referrer_safe', '$country_safe', '$city_safe',
                               '$device_type_safe', '$browser_safe', $is_premium_safe)";

        mysqli_query($conn, $insert_query);
    }

    // Clean up old records (older than 24 hours)
    mysqli_query($conn, "DELETE FROM user_activity WHERE last_activity < DATE_SUB(NOW(), INTERVAL 24 HOUR)");
}

// Track the user activity
trackUserActivity();

// JavaScript for real-time tracking
?>
<script>
// Real-time activity tracking
(function() {
    let lastActivity = Date.now();
    let isActive = true;
    
    // Track page visibility
    document.addEventListener('visibilitychange', function() {
        isActive = !document.hidden;
        if (isActive) {
            updateActivity();
        }
    });
    
    // Track mouse movement and clicks
    ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(function(event) {
        document.addEventListener(event, function() {
            lastActivity = Date.now();
            if (!isActive) {
                isActive = true;
                updateActivity();
            }
        }, true);
    });
    
    // Update activity every 30 seconds
    setInterval(function() {
        if (isActive && (Date.now() - lastActivity) < 60000) { // Active in last minute
            updateActivity();
        }
    }, 30000);
    
    function updateActivity() {
        // Send AJAX request to update activity
        fetch('<?php echo SITE_URL; ?>/includes/update_activity.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'session_id=<?php echo $_SESSION['tracking_session_id']; ?>&page=' + encodeURIComponent(window.location.pathname)
        }).catch(function(error) {
            console.log('Activity tracking error:', error);
        });
    }
    
    // Initial activity update
    updateActivity();
})();
</script>
