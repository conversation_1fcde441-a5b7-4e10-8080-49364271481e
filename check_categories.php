<?php
require_once 'includes/config.php';

// Get all categories
$categories_query = "SELECT * FROM categories ORDER BY name";
$categories_result = mysqli_query($conn, $categories_query);

if (!$categories_result) {
    echo "Error in query: " . mysqli_error($conn);
    exit;
}

echo "<h2>Categories in Database:</h2>";
echo "<pre>";

if (mysqli_num_rows($categories_result) > 0) {
    while ($category = mysqli_fetch_assoc($categories_result)) {
        echo "ID: " . $category['id'] . " - Name: " . $category['name'] . "\n";
    }
} else {
    echo "No categories found in the database.";
}

echo "</pre>";

// Check if the category filter works
echo "<h2>Testing Category Filter:</h2>";
echo "<pre>";

$test_category_id = 1; // Test with first category
$test_query = "SELECT COUNT(*) as count FROM movies WHERE category_id = $test_category_id";
$test_result = mysqli_query($conn, $test_query);

if (!$test_result) {
    echo "Error in test query: " . mysqli_error($conn);
} else {
    $count = mysqli_fetch_assoc($test_result)['count'];
    echo "Movies in category ID $test_category_id: $count\n";
}

echo "</pre>";

// Check movies table structure
echo "<h2>Movies Table Structure:</h2>";
echo "<pre>";

$structure_query = "DESCRIBE movies";
$structure_result = mysqli_query($conn, $structure_query);

if (!$structure_result) {
    echo "Error getting table structure: " . mysqli_error($conn);
} else {
    while ($field = mysqli_fetch_assoc($structure_result)) {
        echo $field['Field'] . " - " . $field['Type'] . " - " . ($field['Null'] == 'YES' ? 'NULL' : 'NOT NULL') . "\n";
    }
}

echo "</pre>";
?>
