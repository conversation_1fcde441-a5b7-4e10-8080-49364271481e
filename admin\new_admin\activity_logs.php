<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set page title
$page_title = 'অ্যাক্টিভিটি লগ';
$current_page = 'activity_logs.php';

try {
    // Include configuration and functions
    require_once '../../includes/config.php';
    require_once '../../includes/functions.php';

    // Check if user is logged in and is admin
    if (!isLoggedIn() || !isAdmin()) {
        redirect(SITE_URL . '/login.php');
        exit;
    }
} catch (Exception $e) {
    die("Error: " . $e->getMessage());
}

// Generate mock activity logs (in real implementation, fetch from database)
function generateMockLogs($count = 50) {
    $activities = [
        'ইউজার লগইন করেছে',
        'নতুন মুভি যোগ করা হয়েছে',
        'টিভি শো এডিট করা হয়েছে',
        'পেমেন্ট সম্পন্ন হয়েছে',
        'প্রিমিয়াম প্ল্যান কিনেছে',
        'এপিসোড আপলোড করা হয়েছে',
        'ক্যাটাগরি তৈরি করা হয়েছে',
        'ইউজার রেজিস্ট্রেশন করেছে',
        'পাসওয়ার্ড পরিবর্তন করেছে',
        'প্রোফাইল আপডেট করেছে'
    ];
    
    $users = ['admin', 'user123', 'moviefan', 'viewer01', 'premium_user'];
    $ips = ['***********', '**************', '***************', '************'];
    $types = ['info', 'success', 'warning', 'danger'];
    
    $logs = [];
    for ($i = 0; $i < $count; $i++) {
        $logs[] = [
            'id' => $i + 1,
            'user' => $users[array_rand($users)],
            'activity' => $activities[array_rand($activities)],
            'ip_address' => $ips[array_rand($ips)],
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'type' => $types[array_rand($types)],
            'created_at' => date('Y-m-d H:i:s', strtotime('-' . rand(0, 30) . ' days -' . rand(0, 23) . ' hours'))
        ];
    }
    
    return $logs;
}

// Get filter parameters
$search = $_GET['search'] ?? '';
$type_filter = $_GET['type'] ?? '';
$date_filter = $_GET['date'] ?? '';
$user_filter = $_GET['user'] ?? '';

// Generate logs
$all_logs = generateMockLogs(100);

// Filter logs
$filtered_logs = array_filter($all_logs, function($log) use ($search, $type_filter, $date_filter, $user_filter) {
    if (!empty($search) && stripos($log['activity'], $search) === false && stripos($log['user'], $search) === false) {
        return false;
    }
    if (!empty($type_filter) && $log['type'] !== $type_filter) {
        return false;
    }
    if (!empty($date_filter) && !str_starts_with($log['created_at'], $date_filter)) {
        return false;
    }
    if (!empty($user_filter) && stripos($log['user'], $user_filter) === false) {
        return false;
    }
    return true;
});

// Pagination
$page = $_GET['page'] ?? 1;
$per_page = 20;
$total_logs = count($filtered_logs);
$total_pages = ceil($total_logs / $per_page);
$offset = ($page - 1) * $per_page;
$logs = array_slice($filtered_logs, $offset, $per_page);

// Include header
include 'includes/header.php';
?>

<!-- Include Sidebar -->
<?php include 'includes/sidebar.php'; ?>

<!-- Main Content -->
<div class="main-content">
    <!-- Page Header -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-title">
                    <i class="fas fa-history me-3"></i>অ্যাক্টিভিটি লগ
                </h1>
                <p class="page-subtitle text-muted">সিস্টেম অ্যাক্টিভিটি এবং ইউজার লগ</p>
            </div>
            <div class="col-auto">
                <div class="page-actions">
                    <div class="btn-group">
                        <button class="btn btn-outline-primary" onclick="location.reload()">
                            <i class="fas fa-sync-alt me-2"></i>রিফ্রেশ
                        </button>
                        <button class="btn btn-outline-success" onclick="exportLogs()">
                            <i class="fas fa-download me-2"></i>এক্সপোর্ট
                        </button>
                        <button class="btn btn-outline-danger" onclick="clearOldLogs()">
                            <i class="fas fa-trash me-2"></i>পুরাতন লগ ক্লিয়ার
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">খুঁজুন</label>
                    <input type="text" class="form-control" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="অ্যাক্টিভিটি বা ইউজার...">
                </div>
                <div class="col-md-2">
                    <label class="form-label">টাইপ</label>
                    <select class="form-select" name="type">
                        <option value="">সব টাইপ</option>
                        <option value="info" <?php echo $type_filter === 'info' ? 'selected' : ''; ?>>তথ্য</option>
                        <option value="success" <?php echo $type_filter === 'success' ? 'selected' : ''; ?>>সফল</option>
                        <option value="warning" <?php echo $type_filter === 'warning' ? 'selected' : ''; ?>>সতর্কতা</option>
                        <option value="danger" <?php echo $type_filter === 'danger' ? 'selected' : ''; ?>>বিপদ</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">তারিখ</label>
                    <input type="date" class="form-control" name="date" value="<?php echo htmlspecialchars($date_filter); ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">ইউজার</label>
                    <input type="text" class="form-control" name="user" value="<?php echo htmlspecialchars($user_filter); ?>" placeholder="ইউজারনেম">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i>
                    </button>
                    <a href="activity_logs.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-times"></i>
                    </a>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="setDateFilter('today')">আজ</button>
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="setDateFilter('week')">৭ দিন</button>
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="setDateFilter('month')">৩০ দিন</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Activity Logs -->
    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>অ্যাক্টিভিটি লগ
                        <span class="badge bg-secondary ms-2"><?php echo number_format($total_logs); ?></span>
                    </h5>
                </div>
                <div class="col-auto">
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="toggleAutoRefresh()">
                            <i class="fas fa-play me-1"></i>অটো রিফ্রেশ
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th width="60">টাইপ</th>
                            <th>অ্যাক্টিভিটি</th>
                            <th>ইউজার</th>
                            <th>IP ঠিকানা</th>
                            <th>সময়</th>
                            <th width="80">বিস্তারিত</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($logs)): ?>
                            <?php foreach ($logs as $log): ?>
                                <tr>
                                    <td data-label="টাইপ">
                                        <?php
                                        $type_classes = [
                                            'info' => 'bg-info',
                                            'success' => 'bg-success',
                                            'warning' => 'bg-warning',
                                            'danger' => 'bg-danger'
                                        ];
                                        $type_icons = [
                                            'info' => 'fas fa-info-circle',
                                            'success' => 'fas fa-check-circle',
                                            'warning' => 'fas fa-exclamation-triangle',
                                            'danger' => 'fas fa-times-circle'
                                        ];
                                        ?>
                                        <span class="badge <?php echo $type_classes[$log['type']]; ?>" title="<?php echo ucfirst($log['type']); ?>">
                                            <i class="<?php echo $type_icons[$log['type']]; ?>"></i>
                                        </span>
                                    </td>
                                    <td data-label="অ্যাক্টিভিটি">
                                        <div class="fw-bold"><?php echo htmlspecialchars($log['activity']); ?></div>
                                        <small class="text-muted">ID: <?php echo $log['id']; ?></small>
                                    </td>
                                    <td data-label="ইউজার">
                                        <div class="d-flex align-items-center">
                                            <img src="../../assets/img/default-avatar.png" alt="User" class="rounded-circle me-2" width="24" height="24">
                                            <span class="fw-bold"><?php echo htmlspecialchars($log['user']); ?></span>
                                        </div>
                                    </td>
                                    <td data-label="IP ঠিকানা">
                                        <code class="text-info"><?php echo htmlspecialchars($log['ip_address']); ?></code>
                                    </td>
                                    <td data-label="সময়">
                                        <div><?php echo date('d/m/Y', strtotime($log['created_at'])); ?></div>
                                        <small class="text-muted"><?php echo date('H:i:s', strtotime($log['created_at'])); ?></small>
                                    </td>
                                    <td data-label="বিস্তারিত">
                                        <button class="btn btn-outline-primary btn-sm" onclick="showLogDetails(<?php echo $log['id']; ?>)" title="বিস্তারিত দেখুন">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">কোন অ্যাক্টিভিটি লগ পাওয়া যায়নি।</p>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="card-footer">
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center mb-0">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?>&<?php echo http_build_query($_GET); ?>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query($_GET); ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?>&<?php echo http_build_query($_GET); ?>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Log Details Modal -->
<div class="modal fade" id="logDetailsModal" tabindex="-1" aria-labelledby="logDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark">
            <div class="modal-header border-secondary">
                <h5 class="modal-title" id="logDetailsModalLabel">লগ বিস্তারিত</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="logDetailsContent">
                <!-- Log details will be loaded here -->
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বন্ধ করুন</button>
            </div>
        </div>
    </div>
</div>

<?php
// Page-specific scripts
$page_scripts = '
<script>
let autoRefreshInterval = null;
let autoRefreshActive = false;

function setDateFilter(period) {
    const dateInput = document.querySelector("input[name=date]");
    const today = new Date();
    
    switch(period) {
        case "today":
            dateInput.value = today.toISOString().split("T")[0];
            break;
        case "week":
            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            dateInput.value = weekAgo.toISOString().split("T")[0];
            break;
        case "month":
            const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
            dateInput.value = monthAgo.toISOString().split("T")[0];
            break;
    }
    
    // Submit form
    dateInput.closest("form").submit();
}

function toggleAutoRefresh() {
    const btn = document.querySelector("button[onclick=\"toggleAutoRefresh()\"]");
    
    if (autoRefreshActive) {
        clearInterval(autoRefreshInterval);
        autoRefreshActive = false;
        btn.innerHTML = \'<i class="fas fa-play me-1"></i>অটো রিফ্রেশ\';
        btn.classList.remove("btn-success");
        btn.classList.add("btn-outline-primary");
        cinepixAdmin.showToast("অটো রিফ্রেশ বন্ধ করা হয়েছে", "info");
    } else {
        autoRefreshInterval = setInterval(() => {
            location.reload();
        }, 30000); // Refresh every 30 seconds
        
        autoRefreshActive = true;
        btn.innerHTML = \'<i class="fas fa-stop me-1"></i>স্টপ\';
        btn.classList.remove("btn-outline-primary");
        btn.classList.add("btn-success");
        cinepixAdmin.showToast("অটো রিফ্রেশ চালু করা হয়েছে (৩০ সেকেন্ড)", "success");
    }
}

function showLogDetails(logId) {
    // Mock log details
    const logDetails = `
        <div class="row g-3">
            <div class="col-md-6">
                <strong>লগ ID:</strong> ${logId}
            </div>
            <div class="col-md-6">
                <strong>টাইমস্ট্যাম্প:</strong> ${new Date().toLocaleString("bn-BD")}
            </div>
            <div class="col-12">
                <strong>ইউজার এজেন্ট:</strong><br>
                <code>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36</code>
            </div>
            <div class="col-12">
                <strong>অতিরিক্ত তথ্য:</strong><br>
                <pre class="bg-secondary p-3 rounded">
{
    "session_id": "sess_${Math.random().toString(36).substr(2, 9)}",
    "request_method": "POST",
    "request_uri": "/admin/action",
    "response_code": 200,
    "execution_time": "${(Math.random() * 2).toFixed(3)}s"
}
                </pre>
            </div>
        </div>
    `;
    
    document.getElementById("logDetailsContent").innerHTML = logDetails;
    const modal = new bootstrap.Modal(document.getElementById("logDetailsModal"));
    modal.show();
}

function exportLogs() {
    cinepixAdmin.showToast("লগ এক্সপোর্ট শুরু হয়েছে...", "info");
    
    // Simulate export
    setTimeout(() => {
        cinepixAdmin.showToast("লগ সফলভাবে এক্সপোর্ট করা হয়েছে!", "success");
    }, 2000);
}

function clearOldLogs() {
    cinepixAdmin.showConfirmModal("৩০ দিনের পুরাতন লগ ডিলিট করতে চান?", function() {
        cinepixAdmin.showToast("পুরাতন লগ ক্লিয়ার করা হচ্ছে...", "info");
        
        setTimeout(() => {
            cinepixAdmin.showToast("পুরাতন লগ সফলভাবে ক্লিয়ার করা হয়েছে!", "success");
            location.reload();
        }, 1500);
    });
}

// Real-time log updates (mock)
function addNewLogEntry() {
    const activities = [
        "নতুন ইউজার লগইন করেছে",
        "পেমেন্ট সম্পন্ন হয়েছে", 
        "মুভি আপলোড করা হয়েছে"
    ];
    
    const activity = activities[Math.floor(Math.random() * activities.length)];
    cinepixAdmin.showToast(`নতুন অ্যাক্টিভিটি: ${activity}`, "info");
}

// Simulate new log entries every 2 minutes
setInterval(addNewLogEntry, 120000);
</script>
';

// Include footer
include 'includes/footer.php';
?>
