<?php
require_once 'config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'login_required']);
    exit;
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Get watchlist ID
$watchlist_id = isset($_POST['watchlist_id']) ? (int)$_POST['watchlist_id'] : 0;

// Validate input
if ($watchlist_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid watchlist ID']);
    exit;
}

// Check if watchlist item belongs to user
$user_id = $_SESSION['user_id'];
$check_query = "SELECT id FROM watchlist WHERE id = $watchlist_id AND user_id = $user_id";
$check_result = mysqli_query($conn, $check_query);

if (mysqli_num_rows($check_result) == 0) {
    echo json_encode(['success' => false, 'message' => 'Watchlist item not found or not authorized']);
    exit;
}

// Remove from watchlist
$delete_query = "DELETE FROM watchlist WHERE id = $watchlist_id AND user_id = $user_id";

if (mysqli_query($conn, $delete_query)) {
    echo json_encode(['success' => true]);
} else {
    echo json_encode(['success' => false, 'message' => mysqli_error($conn)]);
}
?>
