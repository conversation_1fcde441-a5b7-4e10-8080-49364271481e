<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CinePix API Documentation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1 {
            color: #e50914;
            border-bottom: 2px solid #e50914;
            padding-bottom: 10px;
        }
        h2 {
            color: #e50914;
            margin-top: 30px;
        }
        h3 {
            margin-top: 20px;
            color: #333;
        }
        pre {
            background-color: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f4f4f4;
        }
        .endpoint {
            background-color: #f9f9f9;
            padding: 15px;
            border-left: 4px solid #e50914;
            margin-bottom: 20px;
        }
        .method {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin-right: 10px;
        }
        .get {
            background-color: #61affe;
            color: white;
        }
        .post {
            background-color: #49cc90;
            color: white;
        }
    </style>
</head>
<body>
    <h1>CinePix API Documentation</h1>
    
    <p>This documentation provides details about the API endpoints for the CinePix mobile app.</p>
    
    <h2>Base URL</h2>
    <pre><code>https://cinepix.top/api/v1</code></pre>
    
    <h2>Authentication</h2>
    <p>Most endpoints require authentication using a JWT token. Include the token in the Authorization header:</p>
    <pre><code>Authorization: Bearer {token}</code></pre>
    
    <h2>Endpoints</h2>
    
    <div class="endpoint">
        <h3><span class="method get">GET</span> /direct_config.php</h3>
        <p>Get app configuration including premium plans and payment methods.</p>
        <h4>Response Example:</h4>
        <pre><code>{
  "success": true,
  "message": "Success",
  "data": {
    "app_version": "1.0.0",
    "min_app_version": "1.0.0",
    "force_update": "false",
    "update_message": "Please update to the latest version for new features and bug fixes.",
    "maintenance_mode": "false",
    "maintenance_message": "We are currently performing maintenance. Please try again later.",
    "api_version": "v1",
    "site_name": "CinePix",
    "site_url": "https://cinepix.top",
    "premium_plans": [
      {
        "id": 1,
        "name": "Basic",
        "price": 30,
        "duration": 30,
        "features": ["Access to premium movies", "Access to premium TV shows", "HD quality"]
      }
    ],
    "payment_methods": [
      {
        "id": "bkash",
        "name": "বিকাশ",
        "merchant_number": "01XXXXXXXXX",
        "merchant_name": "CinePix",
        "is_automatic": true
      }
    ]
  }
}</code></pre>
    </div>
    
    <div class="endpoint">
        <h3><span class="method post">POST</span> /direct_login.php</h3>
        <p>Login with username/email and password.</p>
        <h4>Request Body:</h4>
        <pre><code>{
  "username": "user123",
  "password": "password123"
}</code></pre>
        <h4>Response Example:</h4>
        <pre><code>{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": 1,
      "username": "user123",
      "email": "<EMAIL>",
      "name": "User Name",
      "role": "user",
      "profile_image": null,
      "is_premium": false,
      "premium_expires": null,
      "created_at": "2023-01-01 00:00:00"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}</code></pre>
    </div>
    
    <div class="endpoint">
        <h3><span class="method post">POST</span> /direct_register.php</h3>
        <p>Register a new user.</p>
        <h4>Request Body:</h4>
        <pre><code>{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "password123",
  "name": "New User"
}</code></pre>
        <h4>Response Example:</h4>
        <pre><code>{
  "success": true,
  "message": "Registration successful",
  "data": {
    "user": {
      "id": 2,
      "username": "newuser",
      "email": "<EMAIL>",
      "name": "New User",
      "role": "user",
      "profile_image": null,
      "is_premium": false,
      "premium_expires": null,
      "created_at": "2023-01-01 00:00:00"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}</code></pre>
    </div>
    
    <div class="endpoint">
        <h3><span class="method get">GET</span> /direct_movies.php</h3>
        <p>Get list of movies with pagination.</p>
        <h4>Query Parameters:</h4>
        <table>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>page</td>
                <td>integer</td>
                <td>Page number (default: 1)</td>
            </tr>
            <tr>
                <td>limit</td>
                <td>integer</td>
                <td>Number of items per page (default: 20)</td>
            </tr>
            <tr>
                <td>category_id</td>
                <td>integer</td>
                <td>Filter by category ID (optional)</td>
            </tr>
            <tr>
                <td>featured</td>
                <td>integer</td>
                <td>Filter featured movies (1 = featured only, 0 = all)</td>
            </tr>
        </table>
        <h4>Response Example:</h4>
        <pre><code>{
  "success": true,
  "message": "Success",
  "data": {
    "movies": [
      {
        "id": 1,
        "title": "Movie Title",
        "description": "Movie description...",
        "release_year": 2023,
        "duration": 120,
        "poster": "posters/movie1.jpg",
        "banner": "banners/movie1.jpg",
        "trailer_url": "https://youtube.com/watch?v=abcdef",
        "rating": 8.5,
        "category_id": 1,
        "category_name": "Action",
        "featured": true,
        "premium_only": false,
        "status": "active"
      }
    ],
    "meta": {
      "pagination": {
        "total": 100,
        "per_page": 20,
        "current_page": 1,
        "last_page": 5
      }
    }
  }
}</code></pre>
    </div>
    
    <div class="endpoint">
        <h3><span class="method get">GET</span> /direct_movie_details.php</h3>
        <p>Get details of a specific movie.</p>
        <h4>Query Parameters:</h4>
        <table>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>id</td>
                <td>integer</td>
                <td>Movie ID (required)</td>
            </tr>
        </table>
        <h4>Response Example:</h4>
        <pre><code>{
  "success": true,
  "message": "Success",
  "data": {
    "movie": {
      "id": 1,
      "title": "Movie Title",
      "description": "Movie description...",
      "release_year": 2023,
      "duration": 120,
      "poster": "posters/movie1.jpg",
      "banner": "banners/movie1.jpg",
      "trailer_url": "https://youtube.com/watch?v=abcdef",
      "rating": 8.5,
      "category_id": 1,
      "category_name": "Action",
      "featured": true,
      "premium_only": false,
      "status": "active"
    },
    "download_links": [
      {
        "id": 1,
        "quality": "1080p",
        "url": "https://example.com/movie1_1080p.mp4",
        "server_name": "Server 1",
        "file_size": "2.5 GB",
        "is_premium": false
      }
    ],
    "related_movies": [
      {
        "id": 2,
        "title": "Related Movie",
        "poster": "posters/movie2.jpg",
        "release_year": 2022,
        "rating": 7.8,
        "premium_only": false
      }
    ]
  }
}</code></pre>
    </div>
    
    <div class="endpoint">
        <h3><span class="method get">GET</span> /direct_tvshows.php</h3>
        <p>Get list of TV shows with pagination.</p>
        <h4>Query Parameters:</h4>
        <table>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>page</td>
                <td>integer</td>
                <td>Page number (default: 1)</td>
            </tr>
            <tr>
                <td>limit</td>
                <td>integer</td>
                <td>Number of items per page (default: 20)</td>
            </tr>
            <tr>
                <td>category_id</td>
                <td>integer</td>
                <td>Filter by category ID (optional)</td>
            </tr>
            <tr>
                <td>featured</td>
                <td>integer</td>
                <td>Filter featured TV shows (1 = featured only, 0 = all)</td>
            </tr>
        </table>
        <h4>Response Example:</h4>
        <pre><code>{
  "success": true,
  "message": "Success",
  "data": {
    "tvshows": [
      {
        "id": 1,
        "title": "TV Show Title",
        "description": "TV show description...",
        "start_year": 2020,
        "end_year": 2023,
        "seasons": 3,
        "poster": "posters/tvshow1.jpg",
        "banner": "banners/tvshow1.jpg",
        "trailer_url": "https://youtube.com/watch?v=abcdef",
        "rating": 8.7,
        "category_id": 2,
        "category_name": "Drama",
        "featured": true,
        "premium_only": false,
        "status": "active"
      }
    ],
    "meta": {
      "pagination": {
        "total": 50,
        "per_page": 20,
        "current_page": 1,
        "last_page": 3
      }
    }
  }
}</code></pre>
    </div>
    
    <div class="endpoint">
        <h3><span class="method get">GET</span> /direct_tvshow_details.php</h3>
        <p>Get details of a specific TV show.</p>
        <h4>Query Parameters:</h4>
        <table>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>id</td>
                <td>integer</td>
                <td>TV show ID (required)</td>
            </tr>
        </table>
        <h4>Response Example:</h4>
        <pre><code>{
  "success": true,
  "message": "Success",
  "data": {
    "tvshow": {
      "id": 1,
      "title": "TV Show Title",
      "description": "TV show description...",
      "start_year": 2020,
      "end_year": 2023,
      "seasons": 3,
      "poster": "posters/tvshow1.jpg",
      "banner": "banners/tvshow1.jpg",
      "trailer_url": "https://youtube.com/watch?v=abcdef",
      "rating": 8.7,
      "category_id": 2,
      "category_name": "Drama",
      "featured": true,
      "premium_only": false,
      "status": "active"
    },
    "seasons": [
      {
        "season_number": 1,
        "episodes_count": 10
      },
      {
        "season_number": 2,
        "episodes_count": 8
      },
      {
        "season_number": 3,
        "episodes_count": 6
      }
    ],
    "related_tvshows": [
      {
        "id": 2,
        "title": "Related TV Show",
        "poster": "posters/tvshow2.jpg",
        "start_year": 2019,
        "seasons": 4,
        "rating": 8.2,
        "premium_only": false
      }
    ]
  }
}</code></pre>
    </div>
    
    <div class="endpoint">
        <h3><span class="method get">GET</span> /direct_tvshow_episodes.php</h3>
        <p>Get episodes of a specific TV show season.</p>
        <h4>Query Parameters:</h4>
        <table>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>id</td>
                <td>integer</td>
                <td>TV show ID (required)</td>
            </tr>
            <tr>
                <td>season</td>
                <td>integer</td>
                <td>Season number (required)</td>
            </tr>
        </table>
        <h4>Response Example:</h4>
        <pre><code>{
  "success": true,
  "message": "Success",
  "data": {
    "tvshow": {
      "id": 1,
      "title": "TV Show Title",
      "poster": "posters/tvshow1.jpg",
      "premium_only": false
    },
    "season_number": 1,
    "episodes": [
      {
        "id": 1,
        "tvshow_id": 1,
        "season_number": 1,
        "episode_number": 1,
        "title": "Episode Title",
        "description": "Episode description...",
        "duration": 45,
        "thumbnail": "thumbnails/episode1.jpg",
        "release_date": "2020-01-01",
        "is_premium": false,
        "download_links": [
          {
            "id": 1,
            "quality": "1080p",
            "url": "https://example.com/episode1_1080p.mp4",
            "server_name": "Server 1",
            "file_size": "1.2 GB",
            "is_premium": false
          }
        ]
      }
    ]
  }
}</code></pre>
    </div>
    
    <div class="endpoint">
        <h3><span class="method get">GET</span> /direct_search.php</h3>
        <p>Search for movies and TV shows.</p>
        <h4>Query Parameters:</h4>
        <table>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>q</td>
                <td>string</td>
                <td>Search query (required)</td>
            </tr>
        </table>
        <h4>Response Example:</h4>
        <pre><code>{
  "success": true,
  "message": "Success",
  "data": {
    "query": "action",
    "movies": [
      {
        "id": 1,
        "title": "Action Movie",
        "poster": "posters/movie1.jpg",
        "release_year": 2023,
        "rating": 8.5,
        "category_name": "Action",
        "premium_only": false
      }
    ],
    "tvshows": [
      {
        "id": 1,
        "title": "Action TV Show",
        "poster": "posters/tvshow1.jpg",
        "start_year": 2020,
        "seasons": 3,
        "rating": 8.7,
        "category_name": "Action",
        "premium_only": false
      }
    ]
  }
}</code></pre>
    </div>
    
    <div class="endpoint">
        <h3><span class="method get">GET</span> /direct_categories.php</h3>
        <p>Get list of categories.</p>
        <h4>Response Example:</h4>
        <pre><code>{
  "success": true,
  "message": "Success",
  "data": {
    "categories": [
      {
        "id": 1,
        "name": "Action",
        "movies_count": 25,
        "tvshows_count": 10
      },
      {
        "id": 2,
        "name": "Drama",
        "movies_count": 30,
        "tvshows_count": 15
      }
    ]
  }
}</code></pre>
    </div>
    
    <div class="endpoint">
        <h3><span class="method get">GET</span> /direct_profile.php</h3>
        <p>Get user profile information. Requires authentication.</p>
        <h4>Response Example:</h4>
        <pre><code>{
  "success": true,
  "message": "Success",
  "data": {
    "user": {
      "id": 1,
      "username": "user123",
      "email": "<EMAIL>",
      "name": "User Name",
      "role": "user",
      "profile_image": null,
      "is_premium": false,
      "premium_expires": null,
      "created_at": "2023-01-01 00:00:00",
      "favorites_count": 5,
      "watchlist_count": 10
    },
    "subscription": null
  }
}</code></pre>
    </div>
    
    <h2>Error Responses</h2>
    <p>All endpoints return a standard error format:</p>
    <pre><code>{
  "success": false,
  "message": "Error message",
  "data": null
}</code></pre>
    
    <h2>Common HTTP Status Codes</h2>
    <table>
        <tr>
            <th>Status Code</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>200</td>
            <td>OK - Request successful</td>
        </tr>
        <tr>
            <td>201</td>
            <td>Created - Resource created successfully</td>
        </tr>
        <tr>
            <td>400</td>
            <td>Bad Request - Invalid parameters</td>
        </tr>
        <tr>
            <td>401</td>
            <td>Unauthorized - Authentication required</td>
        </tr>
        <tr>
            <td>403</td>
            <td>Forbidden - Permission denied</td>
        </tr>
        <tr>
            <td>404</td>
            <td>Not Found - Resource not found</td>
        </tr>
        <tr>
            <td>500</td>
            <td>Internal Server Error - Server error</td>
        </tr>
    </table>
    
    <footer>
        <p>© 2023 CinePix. All rights reserved.</p>
    </footer>
</body>
</html>
