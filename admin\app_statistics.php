<?php
// Include config file
require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Set page title
$page_title = 'অ্যাপ স্ট্যাটিসটিক্স';

// Check if api_logs table exists
$check_table = "SHOW TABLES LIKE 'api_logs'";
$result = mysqli_query($conn, $check_table);

if (mysqli_num_rows($result) == 0) {
    // Create api_logs table
    $create_table = "CREATE TABLE IF NOT EXISTS api_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        endpoint VARCHAR(255) NOT NULL,
        method VARCHAR(10) NOT NULL,
        user_id INT NULL,
        ip_address VARCHAR(45) NOT NULL,
        user_agent TEXT NULL,
        status_code INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user (user_id),
        INDEX idx_endpoint (endpoint),
        INDEX idx_created_at (created_at)
    )";

    if (!mysqli_query($conn, $create_table)) {
        $error_message = 'Failed to create api_logs table: ' . mysqli_error($conn);
    }
}

// Check if device_tokens table exists
$check_table = "SHOW TABLES LIKE 'device_tokens'";
$result = mysqli_query($conn, $check_table);

if (mysqli_num_rows($result) == 0) {
    // Create device_tokens table
    $create_table = "CREATE TABLE IF NOT EXISTS device_tokens (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        device_token VARCHAR(255) NOT NULL,
        device_type ENUM('android', 'ios') NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_device_token (device_token)
    )";

    if (!mysqli_query($conn, $create_table)) {
        $error_message = 'Failed to create device_tokens table: ' . mysqli_error($conn);
    }
}

// Get date range for analytics
$date_from = isset($_GET['date_from']) ? sanitize($_GET['date_from']) : date('Y-m-d', strtotime('-30 days'));
$date_to = isset($_GET['date_to']) ? sanitize($_GET['date_to']) : date('Y-m-d');

// Get app usage statistics
$app_usage_stats = [
    'active_users' => 0,
    'unique_devices' => 0,
    'total_requests' => 0
];

// Check if api_logs table exists before querying
$check_table = "SHOW TABLES LIKE 'api_logs'";
$result = mysqli_query($conn, $check_table);
if (mysqli_num_rows($result) > 0) {
    $app_usage_query = "SELECT
                        COUNT(DISTINCT user_id) as active_users,
                        COUNT(DISTINCT ip_address) as unique_devices,
                        COUNT(*) as total_requests
                        FROM api_logs
                        WHERE created_at >= '$date_from' AND created_at <= '$date_to 23:59:59'";
    $app_usage_result = mysqli_query($conn, $app_usage_query);
    if ($app_usage_result) {
        $app_usage_stats = mysqli_fetch_assoc($app_usage_result) ?: [
            'active_users' => 0,
            'unique_devices' => 0,
            'total_requests' => 0
        ];
    }
}

// Get device statistics
$device_stats = [
    'android' => 0,
    'ios' => 0
];

// Check if device_tokens table exists before querying
$check_table = "SHOW TABLES LIKE 'device_tokens'";
$result = mysqli_query($conn, $check_table);
if (mysqli_num_rows($result) > 0) {
    $device_query = "SELECT
                    device_type,
                    COUNT(*) as count
                    FROM device_tokens
                    GROUP BY device_type";
    $device_result = mysqli_query($conn, $device_query);
    if ($device_result) {
        while ($row = mysqli_fetch_assoc($device_result)) {
            $device_stats[$row['device_type']] = $row['count'];
        }
    }
}

// Get most active users
$active_users_result = false;

// Check if api_logs table exists before querying
$check_table = "SHOW TABLES LIKE 'api_logs'";
$result = mysqli_query($conn, $check_table);
if (mysqli_num_rows($result) > 0) {
    $active_users_query = "SELECT u.id, u.username, u.profile_image, COUNT(al.id) as request_count
                          FROM users u
                          JOIN api_logs al ON u.id = al.user_id
                          WHERE al.created_at >= '$date_from' AND al.created_at <= '$date_to 23:59:59'
                          GROUP BY u.id
                          ORDER BY request_count DESC
                          LIMIT 10";
    $active_users_result = mysqli_query($conn, $active_users_query);
}

// Get most used endpoints
$endpoints_result = false;

// Check if api_logs table exists before querying
$check_table = "SHOW TABLES LIKE 'api_logs'";
$result = mysqli_query($conn, $check_table);
if (mysqli_num_rows($result) > 0) {
    $endpoints_query = "SELECT endpoint, COUNT(*) as count
                       FROM api_logs
                       WHERE created_at >= '$date_from' AND created_at <= '$date_to 23:59:59'
                       GROUP BY endpoint
                       ORDER BY count DESC
                       LIMIT 10";
    $endpoints_result = mysqli_query($conn, $endpoints_query);
}

// Get daily API requests for chart
$dates = [];
$request_counts = [];
$daily_requests_result = false;

// Check if api_logs table exists before querying
$check_table = "SHOW TABLES LIKE 'api_logs'";
$result = mysqli_query($conn, $check_table);
if (mysqli_num_rows($result) > 0) {
    $daily_requests_query = "SELECT
                            DATE(created_at) as date,
                            COUNT(*) as count
                            FROM api_logs
                            WHERE created_at >= '$date_from' AND created_at <= '$date_to 23:59:59'
                            GROUP BY DATE(created_at)
                            ORDER BY date";
    $daily_requests_result = mysqli_query($conn, $daily_requests_query);

    if ($daily_requests_result) {
        while ($row = mysqli_fetch_assoc($daily_requests_result)) {
            $dates[] = $row['date'];
            $request_counts[] = $row['count'];
        }
    }
}

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title d-flex align-items-center">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1><?php echo $page_title; ?></h1>
            </div>

            <div class="topbar-actions">
                <div class="topbar-search">
                    <form action="search.php" method="GET">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="সার্চ করুন..." name="q">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>

                <div class="dropdown">
                    <button class="btn btn-link dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <img src="assets/img/user.png" class="rounded-circle" width="32" height="32" alt="User">
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user fa-sm me-2 text-gray-400"></i> প্রোফাইল</a></li>
                        <li><a class="dropdown-item" href="site_settings.php"><i class="fas fa-cogs fa-sm me-2 text-gray-400"></i> সেটিংস</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/logout.php"><i class="fas fa-sign-out-alt fa-sm me-2 text-gray-400"></i> লগআউট</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container-fluid">
        <!-- Date Range Filter -->
        <div class="card shadow mb-4">
            <div class="card-body">
                <form method="GET" action="" class="row g-3 align-items-end">
                    <div class="col-md-4">
                        <label for="date_from" class="form-label">শুরুর তারিখ</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="date_to" class="form-label">শেষের তারিখ</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter me-2"></i>ফিল্টার করুন
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Stats Overview -->
        <div class="row">
            <!-- Active Users -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-primary fw-bold">সক্রিয় ব্যবহারকারী</h6>
                            <div class="icon-circle bg-primary-light">
                                <i class="fas fa-users text-primary"></i>
                            </div>
                        </div>
                        <div class="mb-1">
                            <h2 class="fw-bold mb-0"><?php echo $app_usage_stats['active_users']; ?></h2>
                            <p class="text-muted small">নির্বাচিত সময়ে সক্রিয় ব্যবহারকারী</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Unique Devices -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-success fw-bold">অনন্য ডিভাইস</h6>
                            <div class="icon-circle bg-success-light">
                                <i class="fas fa-mobile-alt text-success"></i>
                            </div>
                        </div>
                        <div class="mb-1">
                            <h2 class="fw-bold mb-0"><?php echo $app_usage_stats['unique_devices']; ?></h2>
                            <p class="text-muted small">নির্বাচিত সময়ে অনন্য ডিভাইস</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Requests -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-info fw-bold">মোট রিকোয়েস্ট</h6>
                            <div class="icon-circle bg-info-light">
                                <i class="fas fa-exchange-alt text-info"></i>
                            </div>
                        </div>
                        <div class="mb-1">
                            <h2 class="fw-bold mb-0"><?php echo $app_usage_stats['total_requests']; ?></h2>
                            <p class="text-muted small">নির্বাচিত সময়ে মোট API রিকোয়েস্ট</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Device Distribution -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-warning fw-bold">ডিভাইস বিতরণ</h6>
                            <div class="icon-circle bg-warning-light">
                                <i class="fas fa-tablet-alt text-warning"></i>
                            </div>
                        </div>
                        <div class="mb-1">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Android</span>
                                <span class="fw-bold"><?php echo $device_stats['android']; ?></span>
                            </div>
                            <div class="progress mb-3" style="height: 10px;">
                                <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo ($device_stats['android'] + $device_stats['ios'] > 0) ? ($device_stats['android'] / ($device_stats['android'] + $device_stats['ios']) * 100) : 0; ?>%" aria-valuenow="<?php echo $device_stats['android']; ?>" aria-valuemin="0" aria-valuemax="<?php echo $device_stats['android'] + $device_stats['ios']; ?>"></div>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>iOS</span>
                                <span class="fw-bold"><?php echo $device_stats['ios']; ?></span>
                            </div>
                            <div class="progress" style="height: 10px;">
                                <div class="progress-bar bg-info" role="progressbar" style="width: <?php echo ($device_stats['android'] + $device_stats['ios'] > 0) ? ($device_stats['ios'] / ($device_stats['android'] + $device_stats['ios']) * 100) : 0; ?>%" aria-valuenow="<?php echo $device_stats['ios']; ?>" aria-valuemin="0" aria-valuemax="<?php echo $device_stats['android'] + $device_stats['ios']; ?>"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="row">
            <!-- API Requests Chart -->
            <div class="col-lg-8">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">দৈনিক API রিকোয়েস্ট</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" style="position: relative; height:300px;">
                            <canvas id="apiRequestsChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Most Used Endpoints -->
            <div class="col-lg-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">সর্বাধিক ব্যবহৃত এন্ডপয়েন্ট</h6>
                    </div>
                    <div class="card-body">
                        <?php if ($endpoints_result && mysqli_num_rows($endpoints_result) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>এন্ডপয়েন্ট</th>
                                            <th class="text-end">রিকোয়েস্ট</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($endpoint = mysqli_fetch_assoc($endpoints_result)): ?>
                                            <tr>
                                                <td>
                                                    <span class="text-truncate d-inline-block" style="max-width: 200px;" title="<?php echo htmlspecialchars($endpoint['endpoint']); ?>">
                                                        <?php echo htmlspecialchars($endpoint['endpoint']); ?>
                                                    </span>
                                                </td>
                                                <td class="text-end"><?php echo $endpoint['count']; ?></td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <p class="text-muted">কোন ডাটা পাওয়া যায়নি</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Most Active Users -->
        <div class="row">
            <div class="col-lg-12">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">সর্বাধিক সক্রিয় ব্যবহারকারী</h6>
                    </div>
                    <div class="card-body">
                        <?php if ($active_users_result && mysqli_num_rows($active_users_result) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>ব্যবহারকারী</th>
                                            <th class="text-end">রিকোয়েস্ট</th>
                                            <th class="text-end">অ্যাকশন</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $i = 1; while ($user = mysqli_fetch_assoc($active_users_result)): ?>
                                            <tr>
                                                <td><?php echo $i++; ?></td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="<?php echo !empty($user['profile_image']) ? SITE_URL . '/uploads/profiles/' . $user['profile_image'] : SITE_URL . '/images/default-user.png'; ?>" class="rounded-circle me-2" width="32" height="32" alt="<?php echo htmlspecialchars($user['username']); ?>">
                                                        <span><?php echo htmlspecialchars($user['username']); ?></span>
                                                    </div>
                                                </td>
                                                <td class="text-end"><?php echo $user['request_count']; ?></td>
                                                <td class="text-end">
                                                    <a href="user_details.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <p class="text-muted">কোন ডাটা পাওয়া যায়নি</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // API Requests Chart
    const apiRequestsCtx = document.getElementById('apiRequestsChart').getContext('2d');
    const apiRequestsChart = new Chart(apiRequestsCtx, {
        type: 'line',
        data: {
            labels: <?php echo json_encode($dates); ?>,
            datasets: [{
                label: 'API রিকোয়েস্ট',
                data: <?php echo json_encode($request_counts); ?>,
                backgroundColor: 'rgba(78, 115, 223, 0.05)',
                borderColor: 'rgba(78, 115, 223, 1)',
                borderWidth: 2,
                pointBackgroundColor: 'rgba(78, 115, 223, 1)',
                pointBorderColor: '#fff',
                pointRadius: 3,
                pointHoverRadius: 5,
                fill: true,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            }
        }
    });
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
