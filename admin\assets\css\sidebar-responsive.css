/* Responsive Sidebar Styles */

/* Mobile-first approach */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -280px;
        width: 280px;
        height: 100vh;
        z-index: 1050;
        transition: left 0.3s ease;
        overflow-y: auto;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        margin-left: 0 !important;
        width: 100%;
    }
    
    /* Sidebar toggle button */
    .sidebar-toggle {
        position: fixed;
        top: 15px;
        left: 15px;
        z-index: 1060;
        background: #007bff;
        border: none;
        color: white;
        padding: 10px;
        border-radius: 5px;
        cursor: pointer;
    }
    
    /* Overlay when sidebar is open */
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1040;
        display: none;
    }
    
    .sidebar-overlay.show {
        display: block;
    }
    
    /* Adjust sidebar header for mobile */
    .sidebar-header {
        padding: 15px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .sidebar-brand-text {
        font-size: 14px;
    }
    
    .admin-text {
        font-size: 12px;
    }
    
    /* Adjust menu items for mobile */
    .components li a {
        padding: 12px 15px;
        font-size: 14px;
    }
    
    .components li ul li a {
        padding: 10px 15px 10px 45px;
        font-size: 13px;
    }
    
    /* Make badges smaller on mobile */
    .badge {
        font-size: 10px;
        padding: 3px 6px;
    }
}

/* Tablet styles */
@media (min-width: 769px) and (max-width: 1024px) {
    .sidebar {
        width: 250px;
    }
    
    .main-content {
        margin-left: 250px;
    }
    
    .sidebar-brand-text {
        font-size: 15px;
    }
    
    .components li a {
        padding: 12px 15px;
        font-size: 14px;
    }
}

/* Large screens */
@media (min-width: 1025px) {
    .sidebar {
        width: 280px;
    }
    
    .main-content {
        margin-left: 280px;
    }
}

/* Hide sidebar toggle on desktop */
@media (min-width: 769px) {
    .sidebar-toggle {
        display: none;
    }
    
    .sidebar-overlay {
        display: none !important;
    }
}

/* Sidebar scrollbar styling */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Animation for sidebar items */
.components li a {
    transition: all 0.3s ease;
}

.components li a:hover {
    transform: translateX(5px);
}

/* Active state improvements */
.components li a.active {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

/* Dropdown improvements */
.components li ul {
    transition: all 0.3s ease;
}

.components li ul li a {
    transition: all 0.3s ease;
}

.components li ul li a:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(3px);
} 