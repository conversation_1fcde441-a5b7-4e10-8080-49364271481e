<?php
/**
 * Subtitle Converter Script
 * Converts SRT subtitles to WebVTT format for HTML5 video
 */

// Set error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Try different possible locations for config file
$possible_config_files = [
    'includes/config.php',
    'config/config.php',
    'config.php'
];

$config_loaded = false;
foreach ($possible_config_files as $config_file) {
    if (file_exists($config_file)) {
        require_once $config_file;
        $config_loaded = true;
        break;
    }
}

if (!$config_loaded) {
    // Define SITE_URL if not defined in config
    if (!defined('SITE_URL')) {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
        $host = $_SERVER['HTTP_HOST'];
        $script_name = dirname($_SERVER['SCRIPT_NAME']);
        $path = $script_name !== '/' ? $script_name : '';
        define('SITE_URL', $protocol . $host . $path);
    }
}

// Set content type to text/vtt
header('Content-Type: text/vtt');
header('Access-Control-Allow-Origin: *'); // Allow cross-origin requests
header('Cache-Control: no-cache, no-store, must-revalidate'); // Prevent caching
header('Pragma: no-cache');
header('Expires: 0');

// Get subtitle URL from query parameter
$subtitle_url = isset($_GET['url']) ? $_GET['url'] : '';

if (empty($subtitle_url)) {
    echo "WEBVTT\n\n";
    echo "00:00:00.000 --> 00:00:05.000\n";
    echo "No subtitle URL provided\n\n";
    exit;
}

// Function to convert ASS/SSA content to WebVTT
function convertAssToWebVtt($content) {
    // Split content into lines
    $lines = explode("\n", $content);
    $output = "WEBVTT\n\n";
    $inEvents = false;
    $formatLine = '';
    $formatColumns = [];

    foreach ($lines as $line) {
        $line = trim($line);

        // Check if we're in the [Events] section
        if ($line === '[Events]') {
            $inEvents = true;
            continue;
        }

        // If we're in the [Events] section, look for the Format line
        if ($inEvents && strpos($line, 'Format:') === 0) {
            $formatLine = $line;
            $formatParts = explode(':', $formatLine, 2);
            if (count($formatParts) > 1) {
                $formatColumns = array_map('trim', explode(',', $formatParts[1]));
            }
            continue;
        }

        // If we're in the [Events] section and have the format, process dialogue lines
        if ($inEvents && !empty($formatColumns) && strpos($line, 'Dialogue:') === 0) {
            $dialogueParts = explode(':', $line, 2);
            if (count($dialogueParts) > 1) {
                $values = array_map('trim', explode(',', $dialogueParts[1], count($formatColumns)));

                // Create an associative array of the values
                $dialogue = [];
                for ($i = 0; $i < count($formatColumns) && $i < count($values); $i++) {
                    $dialogue[$formatColumns[$i]] = $values[$i];
                }

                // Extract start and end times
                if (isset($dialogue['Start']) && isset($dialogue['End']) && isset($dialogue['Text'])) {
                    $startTime = convertAssTimeToWebVtt($dialogue['Start']);
                    $endTime = convertAssTimeToWebVtt($dialogue['End']);
                    $text = cleanAssText($dialogue['Text']);

                    if (!empty($text)) {
                        $output .= $startTime . ' --> ' . $endTime . "\n" . $text . "\n\n";
                    }
                }
            }
        }
    }

    return $output;
}

// Function to convert ASS time format to WebVTT format
function convertAssTimeToWebVtt($time) {
    // ASS format: H:MM:SS.CC (where CC is centiseconds)
    // WebVTT format: HH:MM:SS.MMM
    if (preg_match('/(\d+):(\d{2}):(\d{2})\.(\d{2})/', $time, $matches)) {
        $hours = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
        $minutes = $matches[2];
        $seconds = $matches[3];
        $centiseconds = $matches[4];
        $milliseconds = str_pad($centiseconds * 10, 3, '0', STR_PAD_RIGHT);

        return "$hours:$minutes:$seconds.$milliseconds";
    }

    return $time;
}

// Function to clean ASS text (remove formatting codes)
function cleanAssText($text) {
    // Remove ASS formatting codes like {\an8}, {\b1}, etc.
    $text = preg_replace('/\{\\\\[^}]*\}/', '', $text);

    // Replace ASS line breaks with WebVTT line breaks
    $text = str_replace('\N', "\n", $text);

    return $text;
}

// Get file extension - handle URLs with query parameters
$parsed_url = parse_url($subtitle_url);
$path_parts = pathinfo($parsed_url['path']);
$file_extension = strtolower($path_parts['extension']);

// Debug information
// error_log("Subtitle URL: " . $subtitle_url);
// error_log("File Extension: " . $file_extension);

// Configure context options for file_get_contents
$context_options = [
    'ssl' => [
        'verify_peer' => false,
        'verify_peer_name' => false,
    ],
    'http' => [
        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'timeout' => 30,
        'follow_location' => 1,
        'max_redirects' => 5
    ]
];
$context = stream_context_create($context_options);

// Load subtitle content
$subtitle_content = @file_get_contents($subtitle_url, false, $context);
if ($subtitle_content === false) {
    // Try with cURL as a fallback
    if (function_exists('curl_init')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $subtitle_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 5);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

        $subtitle_content = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($subtitle_content === false || $http_code != 200) {
            echo "WEBVTT\n\n";
            echo "00:00:00.000 --> 00:00:05.000\n";
            echo "Error loading subtitle file (HTTP Code: $http_code)\n\n";
            exit;
        }
    } else {
        echo "WEBVTT\n\n";
        echo "00:00:00.000 --> 00:00:05.000\n";
        echo "Error loading subtitle file\n\n";
        exit;
    }
}

// If it's already a VTT file, just proxy it
if ($file_extension === 'vtt') {
    echo $subtitle_content;
    exit;
}

// If it's an SRT file, convert it to WebVTT
if ($file_extension === 'srt') {
    // Convert SRT to WebVTT
    echo "WEBVTT\n\n";

    // Debug info
    // error_log("Converting SRT file: " . $subtitle_url);
    // error_log("SRT content length: " . strlen($subtitle_content));

    // Check if content is valid
    if (empty($subtitle_content) || !preg_match('/\d+\s+\d\d:\d\d:\d\d,\d\d\d\s+-->\s+\d\d:\d\d:\d\d,\d\d\d/m', $subtitle_content)) {
        echo "00:00:00.000 --> 00:00:05.000\n";
        echo "Error: Invalid SRT format\n\n";
        exit;
    }

    // Fix encoding issues if needed
    if (!mb_check_encoding($subtitle_content, 'UTF-8')) {
        $subtitle_content = mb_convert_encoding($subtitle_content, 'UTF-8', 'auto');
    }

    // Replace comma with dot in timestamps
    $subtitle_content = preg_replace('/(\d\d:\d\d:\d\d),(\d\d\d)/', '$1.$2', $subtitle_content);

    // Process SRT content line by line for better control
    $lines = explode("\n", $subtitle_content);
    $output = "";
    $inCue = false;

    foreach ($lines as $line) {
        $line = trim($line);

        // Skip numeric identifiers (cue numbers)
        if (preg_match('/^\d+$/', $line)) {
            continue;
        }

        // Timestamp lines
        if (preg_match('/(\d\d:\d\d:\d\d\.\d\d\d)\s+-->\s+(\d\d:\d\d:\d\d\.\d\d\d)/', $line)) {
            $inCue = true;
            $output .= $line . "\n";
            continue;
        }

        // Empty line - end of a cue
        if ($line === '' && $inCue) {
            $inCue = false;
            $output .= "\n";
            continue;
        }

        // Text content
        if ($inCue || $line !== '') {
            $output .= $line . "\n";
        }
    }

    // Ensure proper spacing between cues
    $output = preg_replace('/\n{3,}/', "\n\n", $output);

    echo $output;
    exit;
}

// If it's an ASS/SSA file, convert it to WebVTT
if ($file_extension === 'ass' || $file_extension === 'ssa') {
    echo convertAssToWebVtt($subtitle_content);
    exit;
}

// For other formats or if conversion fails
echo "WEBVTT\n\n";
echo "00:00:00.000 --> 00:00:05.000\n";
echo "Unsupported subtitle format: {$file_extension}\n\n";
?>
