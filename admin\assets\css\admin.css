:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #64748b;
    --success-color: #22c55e;
    --info-color: #0ea5e9;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --sidebar-width: 260px;
    --sidebar-collapsed-width: 70px;
    --topbar-height: 70px;

    /* New colors */
    --bg-dark: #0f172a;
    --bg-light: #ffffff;
    --text-dark: #1e293b;
    --text-light: #f8fafc;
    --border-color: #e2e8f0;
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Bootstrap 4 compatibility classes */
.mr-2 { margin-right: 0.5rem !important; }
.mr-3 { margin-right: 1rem !important; }
.ml-auto { margin-left: auto !important; }
.ml-md-3 { margin-left: 1rem !important; }
.mr-md-3 { margin-right: 1rem !important; }
.my-2 { margin-top: 0.5rem !important; margin-bottom: 0.5rem !important; }
.my-md-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.mw-100 { max-width: 100% !important; }
.fa-sm { font-size: 0.875em; }
.fa-fw { width: 1.25em; text-align: center; }
.text-gray-400 { color: #d1d3e2 !important; }
.text-gray-600 { color: #858796 !important; }
.text-gray-800 { color: #5a5c69 !important; }
.animated--grow-in { animation-name: growIn; animation-duration: 200ms; animation-timing-function: transform cubic-bezier(0.18, 1.25, 0.4, 1), opacity cubic-bezier(0, 1, 0.4, 1); }

@keyframes growIn {
    0% { transform: scale(0.9); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
}

/* General Styles */
body {
    font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--light-color);
    overflow-x: hidden;
}

.wrapper {
    display: flex;
    width: 100%;
    align-items: stretch;
}

/* Sidebar Styles */
.sidebar {
    min-width: var(--sidebar-width);
    max-width: var(--sidebar-width);
    background: var(--bg-dark);
    color: var(--text-light);
    transition: all 0.3s;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 999;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(0, 0, 0, 0.25);
}

@media (max-width: 768px) {
    .sidebar {
        margin-left: -250px; /* Hide sidebar by default on mobile */
    }

    .sidebar.active {
        margin-left: 0; /* Show sidebar when active class is added */
    }
}

.sidebar.collapsed {
    min-width: var(--sidebar-collapsed-width);
    max-width: var(--sidebar-collapsed-width);
}

.sidebar .sidebar-header {
    padding: 20px;
    background: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    height: var(--topbar-height);
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.sidebar .sidebar-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
}

.sidebar-brand-icon {
    margin-right: 12px;
    font-size: 1.5rem;
}

.sidebar-brand-text {
    display: flex;
    flex-direction: column;
}

.sidebar-brand-text .site-name {
    font-size: 1.3rem;
    font-weight: 700;
    line-height: 1;
    letter-spacing: 0.5px;
}

.sidebar-brand-text .admin-text {
    font-size: 0.85rem;
    opacity: 0.8;
    margin-top: 3px;
}

.sidebar ul.components {
    padding: 0;
    list-style: none;
    margin-bottom: 0;
}

.sidebar ul li {
    position: relative;
}

.sidebar ul li a {
    padding: 12px 18px;
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s;
    border-radius: 8px;
    margin: 3px 10px;
    font-weight: 500;
    font-size: 0.95rem;
}

.sidebar ul li a:hover {
    color: #fff;
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(3px);
}

.sidebar ul li a.active {
    color: #fff;
    background: var(--primary-color);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    font-weight: 600;
}

.sidebar ul li a i {
    margin-right: 12px;
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.sidebar ul li a .badge {
    margin-left: auto;
}

.sidebar ul li a .menu-text {
    transition: opacity 0.3s;
}

.sidebar.collapsed ul li a .menu-text,
.sidebar.collapsed .sidebar-brand-text,
.sidebar.collapsed .dropdown-toggle::after {
    display: none;
}

.sidebar.collapsed ul li a {
    padding: 15px;
    justify-content: center;
}

.sidebar.collapsed ul li a i {
    margin-right: 0;
    font-size: 1.2rem;
}

.sidebar.collapsed .sidebar-header {
    justify-content: center;
}

.sidebar.collapsed .sidebar-brand-icon {
    margin-right: 0;
}

/* Dropdown Menus */
.sidebar ul li a.dropdown-toggle {
    position: relative;
}

.sidebar ul li a.dropdown-toggle::after {
    display: block;
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    content: "\f105";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    transition: all 0.3s;
}

.sidebar ul li a.dropdown-toggle[aria-expanded="true"]::after {
    transform: translateY(-50%) rotate(90deg);
}

.sidebar ul li ul.collapse {
    margin-left: 0;
    list-style: none;
    padding-left: 0;
    background: rgba(0, 0, 0, 0.1);
}

.sidebar ul li ul.collapse li a {
    padding-left: 56px;
}

.sidebar.collapsed ul li ul.collapse {
    position: absolute;
    left: var(--sidebar-collapsed-width);
    top: 0;
    width: 200px;
    background: var(--primary-dark);
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 0 4px 4px 0;
}

.sidebar.collapsed ul li ul.collapse li a {
    padding-left: 20px;
}

.sidebar.collapsed ul li:hover > ul.collapse {
    display: block !important;
}

/* Main Content */
.content {
    width: calc(100% - var(--sidebar-width));
    min-height: 100vh;
    transition: all 0.3s;
    position: absolute;
    top: 0;
    right: 0;
}

.content.expanded {
    width: calc(100% - var(--sidebar-collapsed-width));
}

@media (max-width: 768px) {
    .content {
        width: 100%;
        margin-left: 0;
    }
}

/* Overlay for mobile sidebar */
.overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 998;
    opacity: 0;
    transition: all 0.3s;
}

.overlay.active {
    display: block;
    opacity: 1;
}

/* Topbar */
.topbar {
    height: var(--topbar-height);
    background-color: var(--bg-light);
    box-shadow: var(--card-shadow);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 25px;
    margin-bottom: 25px;
    border-radius: 12px;
}

.topbar-toggle {
    background: transparent;
    border: none;
    color: var(--primary-color);
    font-size: 1.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 42px;
    height: 42px;
    border-radius: 10px;
    transition: all 0.3s;
}

.topbar-toggle:hover {
    background-color: var(--light-color);
}

.topbar-title h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin: 0;
}

.topbar-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.topbar-search {
    position: relative;
}

.topbar-search input {
    border-radius: 20px;
    padding-left: 40px;
    background-color: var(--light-color);
    border: 1px solid #e3e6f0;
}

.topbar-search i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary-color);
}

.user-dropdown .dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    color: var(--dark-color);
}

.user-dropdown .dropdown-toggle img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--light-color);
}

.user-dropdown .dropdown-toggle::after {
    display: none;
}

.user-dropdown .dropdown-menu {
    min-width: 200px;
    padding: 0;
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 0.35rem;
    overflow: hidden;
}

.user-dropdown .dropdown-item {
    padding: 12px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--dark-color);
    transition: all 0.3s;
}

.user-dropdown .dropdown-item:hover {
    background-color: var(--light-color);
}

.user-dropdown .dropdown-item i {
    width: 20px;
    text-align: center;
    font-size: 1rem;
    color: var(--secondary-color);
}

.user-dropdown .dropdown-divider {
    margin: 0;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    margin-bottom: 25px;
    transition: all 0.3s;
    overflow: hidden;
}

/* Content Poster Styles */
.content-poster {
    width: 50px;
    height: 75px;
    object-fit: cover;
    border-radius: 4px;
    box-shadow: 0 0.15rem 0.5rem 0 rgba(58, 59, 69, 0.15);
}

.content-poster-placeholder {
    width: 50px;
    height: 75px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fc;
    border-radius: 4px;
    border: 1px solid #e3e6f0;
}

.card:hover {
    box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.card-header {
    padding: 1rem 1.35rem;
    border-bottom: 1px solid #e3e6f0;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h5 {
    margin-bottom: 0;
    font-weight: 700;
    font-size: 1rem;
    letter-spacing: 0.05rem;
}

.card-body {
    padding: 1.35rem;
}

/* Card Counter Styles */
.card-counter {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    padding: 25px;
    background-color: #fff;
    border-radius: 0.5rem;
    margin-bottom: 15px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;
    border-left: 4px solid transparent;
    height: 100%;
    min-height: 100px;
}

.card-counter:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.card-counter i {
    font-size: 3rem;
    opacity: 0.3;
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
}

.card-counter .count-numbers {
    position: relative;
    font-size: 2.5rem;
    font-weight: 700;
    display: block;
    margin-bottom: 5px;
    line-height: 1.2;
    left: 0;
    top: 0;
    text-align: left;
}

.card-counter .count-name {
    position: relative;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    display: block;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 0.1rem;
    font-weight: 600;
    left: 0;
    top: 0;
    text-align: left;
    opacity: 1;
}

.card-counter .premium-count {
    position: relative;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.9);
    background-color: rgba(0, 0, 0, 0.2);
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    margin-top: 5px;
    font-weight: 500;
}

.card-counter.primary {
    background: linear-gradient(135deg, var(--primary-color), #2653d4);
    color: #fff;
    border-left-color: #224abe;
}

.card-counter.danger {
    background: linear-gradient(135deg, var(--danger-color), #be2617);
    color: #fff;
    border-left-color: #bd2130;
}

.card-counter.success {
    background: linear-gradient(135deg, var(--success-color), #13855c);
    color: #fff;
    border-left-color: #169b6b;
}

.card-counter.info {
    background: linear-gradient(135deg, var(--info-color), #258391);
    color: #fff;
    border-left-color: #2a96a5;
}

.card-counter.warning {
    background: linear-gradient(135deg, var(--warning-color), #dda20a);
    color: #212529;
    border-left-color: #f4b30d;
}

.card-counter.secondary {
    background: linear-gradient(135deg, var(--secondary-color), #60636f);
    color: #fff;
    border-left-color: #6c757d;
}

.card-counter.dark {
    background: linear-gradient(135deg, var(--dark-color), #42444e);
    color: #fff;
    border-left-color: #343a40;
}

/* Table Styles */
.table-responsive {
    background-color: #fff;
    padding: 0;
    border-radius: 0.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    overflow: hidden;
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background-color: var(--light-color);
    border-top: none;
    font-weight: 700;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
    padding: 1rem;
    vertical-align: middle;
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-color: #e3e6f0;
}

.table-hover tbody tr:hover {
    background-color: var(--light-color);
}

/* Button Styles */
.btn {
    border-radius: 0.5rem;
    padding: 0.6rem 1.2rem;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.3s;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

/* Form Styles */
.form-container {
    background-color: var(--bg-light);
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: var(--card-shadow);
}

.form-control {
    border-radius: 0.5rem;
    padding: 0.625rem 0.75rem;
    border: 1px solid var(--border-color);
    transition: all 0.2s;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(99, 102, 241, 0.25);
}

.form-select {
    border-radius: 0.5rem;
    padding: 0.625rem 2.25rem 0.625rem 0.75rem;
    border: 1px solid var(--border-color);
    transition: all 0.2s;
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(99, 102, 241, 0.25);
}

/* Filter Styles */
.filter-container {
    background-color: var(--bg-light);
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: var(--card-shadow);
    margin-bottom: 1.5rem;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: var(--card-shadow);
    transition: all 0.3s;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.card-header {
    background-color: var(--bg-light);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: var(--bg-light);
    border-top: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
}

/* Table Styles */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: var(--text-dark);
    vertical-align: middle;
}

.table th {
    font-weight: 600;
    padding: 0.75rem 1rem;
    border-bottom: 2px solid var(--border-color);
}

.table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

/* Responsive Styles */
@media (max-width: 1199.98px) {
    .sidebar {
        min-width: var(--sidebar-collapsed-width);
        max-width: var(--sidebar-collapsed-width);
    }

    .sidebar ul li a .menu-text,
    .sidebar .sidebar-brand-text,
    .sidebar .dropdown-toggle::after {
        display: none;
    }

    .sidebar ul li a {
        padding: 15px;
        justify-content: center;
    }

    .sidebar ul li a i {
        margin-right: 0;
        font-size: 1.2rem;
    }

    .sidebar .sidebar-header {
        justify-content: center;
    }

    .sidebar .sidebar-brand-icon {
        margin-right: 0;
    }

    .content {
        width: calc(100% - var(--sidebar-collapsed-width));
    }

    .sidebar.active {
        min-width: var(--sidebar-width);
        max-width: var(--sidebar-width);
        position: fixed;
        z-index: 999;
    }

    .sidebar.active .sidebar-header {
        justify-content: flex-start;
    }

    .sidebar.active .sidebar-brand-icon {
        margin-right: 10px;
    }

    .sidebar.active .sidebar-brand-text,
    .sidebar.active ul li a .menu-text,
    .sidebar.active .dropdown-toggle::after {
        display: block;
    }

    .sidebar.active ul li a {
        padding: 12px 20px;
        justify-content: flex-start;
    }

    .sidebar.active ul li a i {
        margin-right: 12px;
        font-size: 1rem;
    }

    .overlay {
        display: none;
        position: fixed;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.7);
        z-index: 998;
        opacity: 0;
        transition: all 0.5s ease-in-out;
    }

    .overlay.active {
        display: block;
        opacity: 1;
    }
}

@media (max-width: 767.98px) {
    .topbar {
        flex-direction: column;
        height: auto;
        padding: 15px;
    }

    .topbar-title {
        margin-bottom: 15px;
    }

    .topbar-actions {
        width: 100%;
        justify-content: space-between;
    }

    .card-counter {
        margin-bottom: 20px;
    }

    .content {
        padding: 15px;
    }
}

@media (max-width: 575.98px) {
    .topbar-actions {
        flex-direction: column;
        gap: 10px;
    }

    .topbar-search {
        width: 100%;
    }

    .topbar-search input {
        width: 100%;
    }

    .user-dropdown {
        align-self: flex-end;
    }
}

/* Utilities */
.bg-gradient-primary {
    background: linear-gradient(180deg, var(--primary-color) 10%, var(--primary-dark) 100%);
    color: #fff;
}

.text-primary { color: var(--primary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-dark { color: var(--dark-color) !important; }
.text-light { color: var(--light-color) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-info { background-color: var(--info-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-dark { background-color: var(--dark-color) !important; }
.bg-light { background-color: var(--light-color) !important; }

/* Movie/TV Show Poster Styles */
.content-poster {
    width: 80px;
    height: 120px;
    object-fit: cover;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    background-color: #f0f0f0;
    display: block;
}

.content-poster-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f0f0f0;
    width: 80px;
    height: 120px;
    border-radius: 4px;
}

/* User Avatar Styles */
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Loading Spinner */
.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
