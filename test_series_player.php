<?php
// Include necessary files
require_once 'includes/config.php';
require_once 'includes/streaming_helper.php';

// Test Cloudflare Worker URL
$worker_url = "https://odd-darkness-074fsadsafsafasfjlknmmlkaytr9pe8afnhdklnfalskdftgy.bdmovieshub.workers.dev/994/RRR+2022+-+Hindi+-WEBDL+-+720p.mkv?hash=AgADjg";
$title = "টেস্ট এপিসোড";
$poster = "https://image.tmdb.org/t/p/w500/wE0I6efAW4cDDmZQWtwZMOW44EJ.jpg";
$tvshow_title = "টেস্ট সিরিজ";
$season_number = 1;
$episode_number = 5;
$is_premium = true;

// Get streaming URL
$streaming_url = getEpisodeStreamingUrl(
    $worker_url,
    $title,
    $poster,
    $is_premium,
    $tvshow_title,
    $season_number,
    $episode_number
);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>সিরিজ প্লেয়ার টেস্ট</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #111;
            color: #fff;
            padding: 20px;
            font-family: 'SolaimanLipi', Arial, sans-serif;
        }
        .card {
            background-color: #222;
            border: none;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #333;
            color: #fff;
            border-bottom: 1px solid #444;
        }
        .btn-primary {
            background-color: #e50914;
            border-color: #e50914;
        }
        .btn-primary:hover {
            background-color: #b30710;
            border-color: #b30710;
        }
        .feature-list {
            list-style-type: none;
            padding-left: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #333;
            display: flex;
            align-items: center;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list li i {
            color: #e50914;
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        .player-container {
            background-color: #000;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        .player-wrapper {
            position: relative;
            padding-top: 56.25%; /* 16:9 Aspect Ratio */
            height: 0;
            overflow: hidden;
        }
        .player-wrapper iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 0;
        }
        .episode-info {
            background-color: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .episode-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .episode-meta {
            display: flex;
            gap: 15px;
            margin-bottom: 10px;
            color: #aaa;
        }
        .badge-premium {
            background-color: #e50914;
            color: white;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">সিরিজ প্লেয়ার টেস্ট</h1>
        
        <!-- Episode Info -->
        <div class="episode-info">
            <h2 class="episode-title">
                <?php echo $tvshow_title; ?> - S<?php echo $season_number; ?>E<?php echo str_pad($episode_number, 2, '0', STR_PAD_LEFT); ?>: <?php echo $title; ?>
                <?php if($is_premium): ?>
                <span class="badge-premium">PREMIUM</span>
                <?php endif; ?>
            </h2>
            <div class="episode-meta">
                <span><i class="fas fa-clock"></i> 45 min</span>
                <span><i class="fas fa-calendar-alt"></i> January 15, 2023</span>
            </div>
            <p>এটি একটি টেস্ট এপিসোড যা আমাদের উন্নত প্লেয়ার পরীক্ষা করার জন্য ব্যবহার করা হচ্ছে।</p>
        </div>
        
        <!-- Player -->
        <div class="player-container">
            <div class="player-wrapper">
                <iframe src="<?php echo $streaming_url; ?>" allowfullscreen frameborder="0"></iframe>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>সিরিজ প্লেয়ার ফিচারসমূহ</h5>
            </div>
            <div class="card-body">
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> <strong>সিরিজ তথ্য প্রদর্শন:</strong> সিরিজের নাম, সিজন নম্বর, এপিসোড নম্বর এবং টাইটেল প্রদর্শন করা হয়</li>
                    <li><i class="fas fa-check"></i> <strong>প্লেব্যাক পপআপ:</strong> আগে দেখা থাকলে একটি পপআপ আসবে যেখানে আপনি আগের অবস্থান থেকে বা শুরু থেকে দেখার অপশন পাবেন</li>
                    <li><i class="fas fa-check"></i> <strong>১০ সেকেন্ড আগে-পরে যাওয়ার বাটন:</strong> ভিডিও দ্রুত আগে-পিছে করার জন্য বড় বাটন যুক্ত করা হয়েছে</li>
                    <li><i class="fas fa-check"></i> <strong>আগের পেজে ফিরে যাওয়ার বাটন:</strong> "আগের পেজে ফিরে যান" বাটন ক্লিক করলে আপনি আগের পেজে ফিরে যাবেন</li>
                    <li><i class="fas fa-check"></i> <strong>প্রিমিয়াম ব্যাজ:</strong> প্রিমিয়াম কন্টেন্ট হলে প্রিমিয়াম ব্যাজ দেখানো হবে</li>
                </ul>
                
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>নোট:</strong> এই পেজটি শুধুমাত্র টেস্টিং উদ্দেশ্যে তৈরি করা হয়েছে। আসল সিরিজ পেজে এই প্লেয়ার ব্যবহার করতে, আপনাকে Cloudflare Worker লিংক যুক্ত করতে হবে।
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>ব্যবহার নির্দেশিকা</h5>
            </div>
            <div class="card-body">
                <p>সিরিজের জন্য উন্নত প্লেয়ার ব্যবহার করতে:</p>
                <ol>
                    <li>অ্যাডমিন প্যানেলে যান</li>
                    <li>এপিসোড লিংক ম্যানেজমেন্টে যান</li>
                    <li>Cloudflare Worker লিংক যুক্ত করুন (URL-এ "workers.dev" থাকতে হবে)</li>
                    <li>লিংক সেভ করুন</li>
                    <li>এখন যখন ইউজার এপিসোড দেখবে, তখন স্বয়ংক্রিয়ভাবে উন্নত প্লেয়ার ব্যবহার করা হবে</li>
                </ol>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
