<?php
require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize($_POST['username']);
    $email = sanitize($_POST['email']);
    $password = sanitize($_POST['password']);
    $role = sanitize($_POST['role']);
    $premium_plan = isset($_POST['premium_plan']) ? (int)$_POST['premium_plan'] : 0;
    $subscription_duration = isset($_POST['subscription_duration']) ? (int)$_POST['subscription_duration'] : 30;
    $is_premium = ($premium_plan > 0) ? 1 : 0;

    // Validate input
    if (empty($username) || empty($email) || empty($password)) {
        $_SESSION['error_message'] = 'Username, email, and password are required.';
        redirect('users.php');
    }

    // Check if username or email already exists
    $check_query = "SELECT * FROM users WHERE username = '$username' OR email = '$email'";
    $check_result = mysqli_query($conn, $check_query);

    if (mysqli_num_rows($check_result) > 0) {
        $_SESSION['error_message'] = 'Username or email already exists.';
        redirect('users.php');
    }

    // Hash password
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);

    // Begin transaction
    mysqli_begin_transaction($conn);

    try {
        // Insert user
        $insert_query = "INSERT INTO users (username, email, password, role, is_premium) 
                        VALUES ('$username', '$email', '$hashed_password', '$role', $is_premium)";
        
        if (!mysqli_query($conn, $insert_query)) {
            throw new Exception('Error adding user: ' . mysqli_error($conn));
        }

        $user_id = mysqli_insert_id($conn);

        // If premium, create subscription
        if ($is_premium && $premium_plan > 0) {
            // Get plan details
            $plan_query = "SELECT * FROM premium_plans WHERE id = $premium_plan";
            $plan_result = mysqli_query($conn, $plan_query);

            if (!$plan_result || mysqli_num_rows($plan_result) == 0) {
                throw new Exception('Selected premium plan not found');
            }

            $plan = mysqli_fetch_assoc($plan_result);
            $plan_id = $plan['id'];
            $plan_price = $plan['price'];

            // Create subscription
            $start_date = date('Y-m-d H:i:s');
            $end_date = date('Y-m-d H:i:s', strtotime("+{$subscription_duration} days"));

            $create_subscription_query = "INSERT INTO subscriptions (user_id, plan_id, start_date, end_date, status)
                                       VALUES ($user_id, $plan_id, '$start_date', '$end_date', 'active')";
            $create_result = mysqli_query($conn, $create_subscription_query);

            if (!$create_result) {
                throw new Exception('Error creating subscription: ' . mysqli_error($conn));
            }

            // Create payment record
            $subscription_id = mysqli_insert_id($conn);
            $create_payment_query = "INSERT INTO payments (user_id, subscription_id, amount, payment_method, transaction_id, payment_status)
                                   VALUES ($user_id, $subscription_id, $plan_price, 'manual', 'ADMIN-NEW-" . time() . "', 'completed')";
            $create_payment_result = mysqli_query($conn, $create_payment_query);

            if (!$create_payment_result) {
                throw new Exception('Error creating payment record: ' . mysqli_error($conn));
            }
        }

        // Commit transaction
        mysqli_commit($conn);
        $_SESSION['success_message'] = 'User added successfully.';
    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($conn);
        $_SESSION['error_message'] = $e->getMessage();
    }
}

// Redirect back to users page
redirect('users.php');
?>
