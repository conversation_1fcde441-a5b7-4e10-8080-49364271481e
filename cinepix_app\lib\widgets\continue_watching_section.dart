import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cinepix_app/constants/app_constants.dart';
import 'package:cinepix_app/models/watch_history_item.dart';
import 'package:cinepix_app/utils/tv_focus_manager.dart';
import 'package:path/path.dart';

class ContinueWatchingSection extends StatelessWidget {
  final List<WatchHistoryItem> items;
  final bool isLoading;
  final Function(WatchHistoryItem) onItemTap;
  final Function(WatchHistoryItem) onRemoveTap;

  const ContinueWatchingSection({
    super.key,
    required this.items,
    required this.isLoading,
    required this.onItemTap,
    required this.onRemoveTap,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return _buildLoadingList();
    }

    if (items.isEmpty) {
      return const SizedBox.shrink(); // Hide section if empty
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Continue Watching',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 200,
          child: TvFocusManager.createFocusableHorizontalList(
            height: 200,
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            itemCount: items.length,
            onItemTap: (index) => onItemTap(items[index]),
            itemBuilder: (context, index) {
              final item = items[index];
              return _buildContinueWatchingCard(item);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildContinueWatchingCard(WatchHistoryItem item) {
    return TvFocusManager.createFocusableWidget(
      onTap: () => onItemTap(item),
      child: Container(
        width: 280,
        margin: const EdgeInsets.only(right: 12),
        decoration: BoxDecoration(
          color: Colors.grey[850],
          borderRadius: BorderRadius.circular(AppConstants.cardBorderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            // Main content layout
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Poster image
                ClipRRect(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(AppConstants.cardBorderRadius),
                    topRight: Radius.circular(AppConstants.cardBorderRadius),
                  ),
                  child: CachedNetworkImage(
                    // First try backdrop, then poster, then use default image
                    imageUrl: _getImageUrl(item),
                    height: 140,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    errorWidget: (context, url, error) {
                      // If image fails to load, show a default image based on content type
                      return Container(
                        color: Colors.grey[800],
                        child: Center(
                          child: Icon(
                              item.contentType == 'movie'
                                  ? Icons.movie
                                  : Icons.tv,
                              size: 40,
                              color: Colors.white54),
                        ),
                      );
                    },
                    placeholder: (context, url) => Container(
                      color: Colors.grey[800],
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    ),
                  ),
                ),

                // Content info
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.title,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${_formatDuration(item.watchedPosition)} / ${_formatDuration(item.duration)}',
                        style: TextStyle(
                          color: Colors.grey[400],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Overlay elements
            Positioned.fill(
              child: ClipRRect(
                borderRadius:
                    BorderRadius.circular(AppConstants.cardBorderRadius),
                child: Stack(
                  children: [
                    // Semi-transparent overlay for better visibility of play button
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.black.withOpacity(0.1),
                              Colors.black.withOpacity(0.5),
                            ],
                          ),
                        ),
                      ),
                    ),

                    // Play button overlay
                    Positioned.fill(
                      child: Center(
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: AppConstants.primaryColor,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.play_arrow,
                            color: Colors.white,
                            size: 32,
                          ),
                        ),
                      ),
                    ),

                    // Remove button
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Builder(
                        builder: (BuildContext context) => GestureDetector(
                          onTap: () {
                            // এখানে onRemoveTap কল করার আগে একটি ডায়ালগ দেখানো হচ্ছে
                            showDialog(
                              context: context,
                              builder: (context) => AlertDialog(
                                title: const Text('নিশ্চিত করুন'),
                                content: const Text('আপনি কি এই আইটেমটি কন্টিনিউ ওয়াচিং থেকে সরাতে চান?'),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.of(context).pop(),
                                    child: const Text('না'),
                                  ),
                                  TextButton(
                                    onPressed: () {
                                      Navigator.of(context).pop();
                                      onRemoveTap(item);
                                    },
                                    child: const Text('হ্যাঁ'),
                                  ),
                                ],
                              ),
                            );
                          },
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.6),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                      ),
                    ),

                    // Content type badge
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: item.contentType == 'movie'
                              ? Colors.blue
                              : Colors.purple,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          item.contentType == 'movie' ? 'Movie' : 'TV Show',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),

                    // Progress bar
                    Positioned(
                      bottom: 40, // Position above the title text
                      left: 0,
                      right: 0,
                      child: LinearProgressIndicator(
                        value: item.progress / 100,
                        backgroundColor: Colors.grey[800]?.withOpacity(0.5),
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppConstants.primaryColor,
                        ),
                        minHeight: 3,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(
            'Continue Watching',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 200,
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            scrollDirection: Axis.horizontal,
            itemCount: 3,
            itemBuilder: (context, index) {
              return Container(
                width: 280,
                margin: const EdgeInsets.only(right: 12),
                decoration: BoxDecoration(
                  color: Colors.grey[850],
                  borderRadius:
                      BorderRadius.circular(AppConstants.cardBorderRadius),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Poster placeholder
                    Container(
                      height: 140,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.grey[800],
                        borderRadius: BorderRadius.only(
                          topLeft:
                              Radius.circular(AppConstants.cardBorderRadius),
                          topRight:
                              Radius.circular(AppConstants.cardBorderRadius),
                        ),
                      ),
                      child: Center(
                        child: CircularProgressIndicator(
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    ),

                    // Title placeholder
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            height: 14,
                            width: 200,
                            color: Colors.grey[800],
                          ),
                          const SizedBox(height: 4),
                          Container(
                            height: 10,
                            width: 150,
                            color: Colors.grey[800],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  // Helper method to get the best available image URL
  String _getImageUrl(WatchHistoryItem item) {
    // First try backdrop image
    if (item.backdropUrl.isNotEmpty && !item.backdropUrl.contains('null')) {
      return item.backdropUrl;
    }

    // Then try poster image
    if (item.posterUrl.isNotEmpty && !item.posterUrl.contains('null')) {
      return item.posterUrl;
    }

    // If both are empty or contain 'null', return a placeholder URL
    // This will trigger the errorWidget
    return 'https://placeholder.com/wp-content/uploads/2018/10/placeholder.png';
  }
}
