import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import 'package:flutter/foundation.dart';

/// A utility class to manage focus for Android TV navigation
class TvFocusManager {
  /// Check if the app is running on Android TV
  static bool isAndroidTV() {
    if (!kIsWeb && Platform.isAndroid) {
      try {
        final size = PlatformDispatcher.instance.views.first.physicalSize;
        return size.width > size.height;
      } catch (e) {
        debugPrint('Error checking for Android TV: $e');
      }
    }
    return false;
  }

  /// Create a focusable widget for TV navigation
  static Widget createFocusableWidget({
    required Widget child,
    required VoidCallback onTap,
    FocusNode? focusNode,
    bool autofocus = false,
    Color focusColor = Colors.white,
    double focusBorderWidth = 2.0,
    BorderRadius borderRadius = const BorderRadius.all(Radius.circular(8.0)),
  }) {
    // If not on Android TV, return a simple GestureDetector
    if (!isAndroidTV()) {
      return GestureDetector(
        onTap: onTap,
        child: child,
      );
    }

    // On Android TV, use Focus widget for remote control navigation
    return Focus(
      focusNode: focusNode,
      autofocus: autofocus,
      onKey: (FocusNode node, RawKeyEvent event) {
        if (event is RawKeyDownEvent) {
          if (event.logicalKey == LogicalKeyboardKey.select ||
              event.logicalKey == LogicalKeyboardKey.enter) {
            onTap();
            return KeyEventResult.handled;
          }
        }
        return KeyEventResult.ignored;
      },
      child: Builder(
        builder: (BuildContext context) {
          final bool hasFocus = Focus.of(context).hasFocus;
          return GestureDetector(
            onTap: onTap,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: borderRadius,
                border: hasFocus
                    ? Border.all(
                        color: focusColor,
                        width: focusBorderWidth,
                      )
                    : null,
              ),
              child: child,
            ),
          );
        },
      ),
    );
  }

  /// Create a focusable grid for TV navigation
  static Widget createFocusableGrid({
    required int itemCount,
    required IndexedWidgetBuilder itemBuilder,
    required Function(int) onItemTap,
    required SliverGridDelegate gridDelegate,
    EdgeInsetsGeometry? padding,
    ScrollController? controller,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
    bool primary = false,
  }) {
    if (!isAndroidTV()) {
      return GridView.builder(
        gridDelegate: gridDelegate,
        itemCount: itemCount,
        padding: padding,
        controller: controller,
        shrinkWrap: shrinkWrap,
        physics: physics,
        primary: primary,
        itemBuilder: (context, index) {
          return GestureDetector(
            onTap: () => onItemTap(index),
            child: itemBuilder(context, index),
          );
        },
      );
    }

    return GridView.builder(
      gridDelegate: gridDelegate,
      itemCount: itemCount,
      padding: padding,
      controller: controller,
      shrinkWrap: shrinkWrap,
      physics: physics,
      primary: primary,
      itemBuilder: (context, index) {
        return createFocusableWidget(
          onTap: () => onItemTap(index),
          autofocus: index == 0,
          child: itemBuilder(context, index),
        );
      },
    );
  }

  /// Create a focusable horizontal list for TV navigation
  static Widget createFocusableHorizontalList({
    required int itemCount,
    required IndexedWidgetBuilder itemBuilder,
    required Function(int) onItemTap,
    EdgeInsetsGeometry? padding,
    ScrollController? controller,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
    bool primary = false,
    double? itemExtent,
    double? height,
  }) {
    if (!isAndroidTV()) {
      return SizedBox(
        height: height,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: itemCount,
          padding: padding,
          controller: controller,
          shrinkWrap: shrinkWrap,
          physics: physics,
          primary: primary,
          itemExtent: itemExtent,
          itemBuilder: (context, index) {
            return GestureDetector(
              onTap: () => onItemTap(index),
              child: itemBuilder(context, index),
            );
          },
        ),
      );
    }

    return SizedBox(
      height: height,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: itemCount,
        padding: padding,
        controller: controller,
        shrinkWrap: shrinkWrap,
        physics: physics,
        primary: primary,
        itemExtent: itemExtent,
        itemBuilder: (context, index) {
          return createFocusableWidget(
            onTap: () => onItemTap(index),
            autofocus: index == 0,
            child: itemBuilder(context, index),
          );
        },
      ),
    );
  }
}
