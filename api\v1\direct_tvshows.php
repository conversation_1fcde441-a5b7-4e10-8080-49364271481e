<?php
// Include direct config file
require_once '../direct_config.php';

// Get TV shows list
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
$category_id = isset($_GET['category_id']) ? (int)$_GET['category_id'] : 0;
$featured = isset($_GET['featured']) ? (int)$_GET['featured'] : 0;
$offset = ($page - 1) * $limit;

// Build query without status filter
$query = "SELECT t.*, c.name as category_name
          FROM tvshows t
          LEFT JOIN categories c ON t.category_id = c.id
          WHERE 1=1";

if ($category_id > 0) {
    $query .= " AND t.category_id = $category_id";
}

if ($featured > 0) {
    $query .= " AND t.featured = 1";
}

$query .= " ORDER BY t.id DESC LIMIT $limit OFFSET $offset";

$result = mysqli_query($conn, $query);

if (!$result) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to fetch TV shows: ' . mysqli_error($conn),
        'data' => null
    ]);
    exit;
}

$tvshows = [];
while ($tvshow = mysqli_fetch_assoc($result)) {
    $tvshows[] = [
        'id' => (int)$tvshow['id'],
        'title' => $tvshow['title'],
        'description' => $tvshow['description'],
        'start_year' => (int)$tvshow['start_year'],
        'end_year' => $tvshow['end_year'] ? (int)$tvshow['end_year'] : null,
        'seasons' => (int)$tvshow['seasons'],
        'poster' => $tvshow['poster'] ? (strpos($tvshow['poster'], 'http') === 0 ? $tvshow['poster'] : SITE_URL . '/uploads/' . $tvshow['poster']) : '',
        'banner' => $tvshow['banner'] ? (strpos($tvshow['banner'], 'http') === 0 ? $tvshow['banner'] : SITE_URL . '/uploads/' . $tvshow['banner']) : '',
        'trailer_url' => $tvshow['trailer_url'],
        'rating' => (float)$tvshow['rating'],
        'category_id' => (int)$tvshow['category_id'],
        'category_name' => $tvshow['category_name'],
        'featured' => (bool)$tvshow['featured'],
        'premium_only' => (bool)$tvshow['premium_only'],
        'status' => $tvshow['status']
    ];
}

// Get total count for pagination without status filter
$count_query = "SELECT COUNT(*) as total FROM tvshows WHERE 1=1";

if ($category_id > 0) {
    $count_query .= " AND category_id = $category_id";
}

if ($featured > 0) {
    $count_query .= " AND featured = 1";
}

$count_result = mysqli_query($conn, $count_query);
$total = mysqli_fetch_assoc($count_result)['total'];

// Return TV shows list
http_response_code(200);
echo json_encode([
    'success' => true,
    'message' => 'Success',
    'data' => [
        'tvshows' => $tvshows,
        'meta' => [
            'pagination' => [
                'total' => (int)$total,
                'per_page' => $limit,
                'current_page' => $page,
                'last_page' => ceil($total / $limit)
            ]
        ]
    ]
]);
?>
