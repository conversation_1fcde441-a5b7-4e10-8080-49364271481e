<?php
// Include necessary files
require_once 'includes/config.php';
require_once 'includes/streaming_helper.php';

// Test Cloudflare Worker URL
$worker_url = "https://odd-darkness-074fsadsafsafasfjlknmmlkaytr9pe8afnhdklnfalskdftgy.bdmovieshub.workers.dev/994/RRR+2022+-+Hindi+-WEBDL+-+720p.mkv?hash=AgADjg";
$title = "টেস্ট ভিডিও প্লেয়ার";
$poster = "https://image.tmdb.org/t/p/w500/wE0I6efAW4cDDmZQWtwZMOW44EJ.jpg";

// Get streaming URL
$streaming_url = "plyr_player_enhanced.php?url=" . urlencode($worker_url) . "&title=" . urlencode($title) . "&poster=" . urlencode($poster);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>উন্নত প্লেয়ার টেস্ট</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #111;
            color: #fff;
            padding: 20px;
            font-family: 'SolaimanLipi', Arial, sans-serif;
        }
        .card {
            background-color: #222;
            border: none;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #333;
            color: #fff;
            border-bottom: 1px solid #444;
        }
        .btn-primary {
            background-color: #e50914;
            border-color: #e50914;
        }
        .btn-primary:hover {
            background-color: #b30710;
            border-color: #b30710;
        }
        .feature-list {
            list-style-type: none;
            padding-left: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #333;
            display: flex;
            align-items: center;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list li i {
            color: #e50914;
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">উন্নত প্লেয়ার টেস্ট</h1>

        <div class="card">
            <div class="card-header">
                <h5>নতুন ফিচারসমূহ</h5>
            </div>
            <div class="card-body">
                <ul class="feature-list">
                    <li><i class="fas fa-forward"></i> ১০ সেকেন্ড সামনে যাওয়ার বাটন যুক্ত করা হয়েছে</li>
                    <li><i class="fas fa-backward"></i> ১০ সেকেন্ড পিছনে যাওয়ার বাটন যুক্ত করা হয়েছে</li>
                    <li><i class="fas fa-history"></i> প্লেব্যাক পজিশন সংরক্ষণ করা হয়েছে - ভিডিও বন্ধ করে আবার খুললেও সেই পজিশন থেকে চালু হবে</li>
                    <li><i class="fas fa-keyboard"></i> J এবং L কীবোর্ড শর্টকাট যুক্ত করা হয়েছে (YouTube এর মতো)</li>
                    <li><i class="fas fa-mobile-alt"></i> মোবাইলের জন্য বড় বাটন যুক্ত করা হয়েছে</li>
                </ul>

                <div class="mt-4">
                    <a href="<?php echo $streaming_url; ?>" class="btn btn-primary">
                        <i class="fas fa-play-circle me-2"></i>উন্নত প্লেয়ার টেস্ট করুন
                    </a>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5>ব্যবহার নির্দেশিকা</h5>
            </div>
            <div class="card-body">
                <p>১. প্লেয়ারে ১০ সেকেন্ড আগে-পরে যাওয়ার জন্য নিচের পদ্ধতিগুলি ব্যবহার করুন:</p>
                <ul>
                    <li>প্লেয়ারের কন্ট্রোল বারে ১০ সেকেন্ড আগে/পরে বাটন ক্লিক করুন</li>
                    <li>কীবোর্ডের বাম/ডান অ্যারো কী চাপুন</li>
                    <li>কীবোর্ডের J (পিছনে) এবং L (সামনে) কী চাপুন</li>
                </ul>

                <p>২. প্লেব্যাক পজিশন সংরক্ষণ পরীক্ষা করতে:</p>
                <ul>
                    <li>ভিডিও কিছুক্ষণ চালান</li>
                    <li>ব্রাউজার বন্ধ করুন বা পেজ রিফ্রেশ করুন</li>
                    <li>আবার ভিডিও খুলুন - আপনি দেখবেন যে আপনি যেখানে ছিলেন সেখান থেকে ভিডিও চালু হবে</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
