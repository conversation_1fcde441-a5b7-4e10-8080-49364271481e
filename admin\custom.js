document.addEventListener('DOMContentLoaded', function() {
    // Add inline CSS for action buttons on the left
    const style = document.createElement('style');
    style.textContent = `
        .action-buttons-left {
            display: flex;
            justify-content: flex-start;
            gap: 5px;
            margin-bottom: 5px;
        }

        .action-buttons-left .btn {
            min-width: 100px;
        }

        @media (max-width: 768px) {
            .action-buttons-left {
                flex-direction: column;
            }

            .action-buttons-left .btn {
                margin-bottom: 5px;
            }
        }
    `;
    document.head.appendChild(style);

    // Move action buttons to the left side
    setTimeout(function() {
        // Get all tables
        const tables = document.querySelectorAll('.table-striped');

        tables.forEach(table => {
            // Get all rows in the table
            const rows = table.querySelectorAll('tbody tr');

            // Process each row
            rows.forEach(row => {
                // Get all cells in the row
                const cells = row.querySelectorAll('td');

                // Check if there are cells and the last cell contains action buttons
                if (cells.length > 0) {
                    const lastCell = cells[cells.length - 1];
                    const actionButtons = lastCell.querySelector('.action-buttons');

                    if (actionButtons) {
                        // Create a new cell for the action buttons
                        const newCell = document.createElement('td');
                        newCell.innerHTML = lastCell.innerHTML;

                        // Add the left alignment class
                        const newActionButtons = newCell.querySelector('.action-buttons');
                        if (newActionButtons) {
                            newActionButtons.classList.add('action-buttons-left');
                        }

                        // Insert the new cell at the beginning of the row
                        row.insertBefore(newCell, row.firstChild);

                        // Remove the last cell
                        row.removeChild(lastCell);
                    }
                }
            });

            // Update the header
            const headerRow = table.querySelector('thead tr');
            if (headerRow) {
                const headerCells = headerRow.querySelectorAll('th');

                if (headerCells.length > 0) {
                    const lastHeaderCell = headerCells[headerCells.length - 1];

                    // Check if the last header cell is for actions
                    if (lastHeaderCell.textContent.trim() === 'অ্যাকশন') {
                        // Create a new header cell
                        const newHeaderCell = document.createElement('th');
                        newHeaderCell.textContent = 'অ্যাকশন';
                        newHeaderCell.width = lastHeaderCell.width || '30%';

                        // Insert the new header cell at the beginning
                        headerRow.insertBefore(newHeaderCell, headerRow.firstChild);

                        // Remove the last header cell
                        headerRow.removeChild(lastHeaderCell);
                    }
                }
            }
        });
    }, 100);
});
