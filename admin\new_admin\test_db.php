<?php
// Database connection test
require_once '../../includes/config.php';

echo "<h1>Database Connection Test</h1>";

// Check connection
if (!$conn) {
    die("❌ Database connection failed: " . mysqli_connect_error());
}
echo "✅ Database connected successfully<br><br>";

// Check tables
$tables_to_check = ['movies', 'tvshows', 'users', 'categories', 'episodes', 'payments', 'reviews'];

foreach ($tables_to_check as $table) {
    $query = "SHOW TABLES LIKE '$table'";
    $result = mysqli_query($conn, $query);
    
    if ($result && mysqli_num_rows($result) > 0) {
        echo "✅ Table '$table' exists<br>";
        
        // Count records
        $count_query = "SELECT COUNT(*) as count FROM $table";
        $count_result = mysqli_query($conn, $count_query);
        
        if ($count_result) {
            $count_data = mysqli_fetch_assoc($count_result);
            echo "&nbsp;&nbsp;&nbsp;📊 Records: " . $count_data['count'] . "<br>";
        }
    } else {
        echo "❌ Table '$table' does not exist<br>";
    }
}

echo "<br><h2>Sample Data:</h2>";

// Test movies query
$movies_query = "SELECT title, is_premium, is_active FROM movies LIMIT 5";
$movies_result = mysqli_query($conn, $movies_query);

if ($movies_result && mysqli_num_rows($movies_result) > 0) {
    echo "<h3>Movies:</h3>";
    while ($movie = mysqli_fetch_assoc($movies_result)) {
        echo "- " . htmlspecialchars($movie['title']) . " (Premium: " . ($movie['is_premium'] ? 'Yes' : 'No') . ", Active: " . ($movie['is_active'] ? 'Yes' : 'No') . ")<br>";
    }
} else {
    echo "<h3>Movies: No data found</h3>";
}

// Test users query
$users_query = "SELECT username, role, is_premium FROM users LIMIT 5";
$users_result = mysqli_query($conn, $users_query);

if ($users_result && mysqli_num_rows($users_result) > 0) {
    echo "<h3>Users:</h3>";
    while ($user = mysqli_fetch_assoc($users_result)) {
        echo "- " . htmlspecialchars($user['username']) . " (Role: " . $user['role'] . ", Premium: " . ($user['is_premium'] ? 'Yes' : 'No') . ")<br>";
    }
} else {
    echo "<h3>Users: No data found</h3>";
}

echo "<br><a href='index.php'>← Back to Dashboard</a>";
?>
