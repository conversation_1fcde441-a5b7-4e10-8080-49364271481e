<?php
// Set page title
$page_title = "Contact Us - BanglaMobi";

// Include header
require_once 'includes/header.php';

// Process contact form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $name = isset($_POST['name']) ? sanitize($_POST['name']) : '';
    $email = isset($_POST['email']) ? sanitize($_POST['email']) : '';
    $subject = isset($_POST['subject']) ? sanitize($_POST['subject']) : '';
    $message = isset($_POST['message']) ? sanitize($_POST['message']) : '';
    
    // Validate form data
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        $error_message = 'Please fill in all required fields.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = 'Please enter a valid email address.';
    } else {
        // In a real application, you would send an email here
        // For now, we'll just simulate a successful submission
        $success_message = 'Thank you for your message! We will get back to you soon.';
        
        // Clear form data after successful submission
        $name = $email = $subject = $message = '';
    }
}
?>

<!-- Page Header -->
<section class="py-5 bg-dark">
    <div class="container">
        <div class="row">
            <div class="col-md-8 mx-auto text-center">
                <h1 class="display-4 fw-bold">Contact Us</h1>
                <p class="lead text-light">Have questions or feedback? We'd love to hear from you!</p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Content -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <?php if ($success_message): ?>
                <div class="alert alert-success mb-4" role="alert">
                    <?php echo $success_message; ?>
                </div>
                <?php endif; ?>
                
                <?php if ($error_message): ?>
                <div class="alert alert-danger mb-4" role="alert">
                    <?php echo $error_message; ?>
                </div>
                <?php endif; ?>
                
                <div class="card bg-dark shadow-sm border-0 p-4 mb-4">
                    <div class="row">
                        <div class="col-md-6 mb-4 mb-md-0">
                            <h2 class="mb-4">Get In Touch</h2>
                            <p>We're here to help and answer any question you might have. We look forward to hearing from you.</p>
                            
                            <div class="d-flex mb-4">
                                <div class="me-3">
                                    <i class="fas fa-envelope text-danger fa-2x"></i>
                                </div>
                                <div>
                                    <h5>Email Us</h5>
                                    <p class="mb-0"><a href="mailto:<EMAIL>" class="text-light"><EMAIL></a></p>
                                </div>
                            </div>
                            
                            <div class="d-flex mb-4">
                                <div class="me-3">
                                    <i class="fas fa-map-marker-alt text-danger fa-2x"></i>
                                </div>
                                <div>
                                    <h5>Office Location</h5>
                                    <p class="mb-0">123 Review Street, Dhaka 1000, Bangladesh</p>
                                </div>
                            </div>
                            
                            <div class="d-flex mb-4">
                                <div class="me-3">
                                    <i class="fas fa-clock text-danger fa-2x"></i>
                                </div>
                                <div>
                                    <h5>Working Hours</h5>
                                    <p class="mb-0">Monday - Friday: 9:00 AM - 6:00 PM<br>Saturday: 10:00 AM - 4:00 PM</p>
                                </div>
                            </div>
                            
                            <h4 class="mt-5 mb-3">Follow Us</h4>
                            <div class="social-icons">
                                <a href="#" class="me-3 text-light"><i class="fab fa-facebook-f fa-lg"></i></a>
                                <a href="#" class="me-3 text-light"><i class="fab fa-twitter fa-lg"></i></a>
                                <a href="#" class="me-3 text-light"><i class="fab fa-instagram fa-lg"></i></a>
                                <a href="#" class="me-3 text-light"><i class="fab fa-youtube fa-lg"></i></a>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h2 class="mb-4">Send Us a Message</h2>
                            <form method="post" action="">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Your Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control bg-dark text-light border-secondary" id="name" name="name" value="<?php echo isset($name) ? $name : ''; ?>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">Your Email <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control bg-dark text-light border-secondary" id="email" name="email" value="<?php echo isset($email) ? $email : ''; ?>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="subject" class="form-label">Subject <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control bg-dark text-light border-secondary" id="subject" name="subject" value="<?php echo isset($subject) ? $subject : ''; ?>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="message" class="form-label">Your Message <span class="text-danger">*</span></label>
                                    <textarea class="form-control bg-dark text-light border-secondary" id="message" name="message" rows="5" required><?php echo isset($message) ? $message : ''; ?></textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-danger">Send Message</button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="card bg-dark shadow-sm border-0 p-4">
                    <h2 class="mb-4">Frequently Asked Questions</h2>
                    
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item bg-dark text-light border-secondary">
                            <h2 class="accordion-header" id="headingOne">
                                <button class="accordion-button bg-dark text-light" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                    How do you rate movies and TV shows?
                                </button>
                            </h2>
                            <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Our rating system is based on a scale of 1-10, with 10 being the highest. We consider various factors including story, acting, direction, cinematography, sound design, and overall entertainment value. Our team of reviewers watches the entire content before assigning a rating.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item bg-dark text-light border-secondary">
                            <h2 class="accordion-header" id="headingTwo">
                                <button class="accordion-button collapsed bg-dark text-light" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                    Can I submit my own reviews?
                                </button>
                            </h2>
                            <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Yes! Registered users can submit their own ratings and reviews for movies and TV shows. We encourage our community to share their thoughts and perspectives. All user reviews are moderated to ensure they meet our community guidelines.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item bg-dark text-light border-secondary">
                            <h2 class="accordion-header" id="headingThree">
                                <button class="accordion-button collapsed bg-dark text-light" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                    How can I suggest a movie or TV show for review?
                                </button>
                            </h2>
                            <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    You can suggest content for review by using our contact form or by sending an <NAME_EMAIL>. Please include the title, year of release, and why you think we should review it. We appreciate your suggestions and try to cover as many titles as possible.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Include footer
require_once 'includes/footer.php';
?>
