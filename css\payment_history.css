/* Payment History Page Styles */
.subscription-info-card {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.subscription-info-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.8rem;
}

.subscription-info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    font-size: 0.9rem;
    color: #a0a0a0;
    width: 70px;
    font-weight: 500;
}

.info-value {
    font-size: 0.9rem;
    color: #ffffff;
    font-weight: 500;
}

/* Features List */
.features-list {
    list-style: none;
    padding-left: 0;
}

.features-list li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.5rem;
    color: #ffffff;
}

.features-list li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
}

/* Payment Table */
.table-dark {
    background-color: transparent;
    border-color: rgba(255, 255, 255, 0.1);
}

.table-dark th {
    background-color: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.1);
    font-weight: 600;
    color: #ffffff;
}

.table-dark td {
    border-color: rgba(255, 255, 255, 0.05);
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Responsive */
@media (max-width: 767.98px) {
    .subscription-info-card {
        margin-bottom: 1.5rem;
    }
    
    .table-responsive {
        border-radius: 8px;
        overflow: hidden;
    }
}

@media (max-width: 575.98px) {
    .info-label {
        width: 60px;
        font-size: 0.85rem;
    }
    
    .info-value {
        font-size: 0.85rem;
    }
}
