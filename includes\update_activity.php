<?php
/**
 * Activity Update Endpoint
 * Updates user activity in real-time via AJAX
 */

require_once 'config.php';

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if request is POST and has required data
if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['session_id'])) {
    http_response_code(400);
    exit('Invalid request');
}

$session_id = $_POST['session_id'];
$current_page = $_POST['page'] ?? $_SERVER['REQUEST_URI'] ?? '';

// Validate session
if (!isset($_SESSION['tracking_session_id']) || $_SESSION['tracking_session_id'] !== $session_id) {
    http_response_code(403);
    exit('Invalid session');
}

// Update activity
if (isset($conn) && $conn) {
    $update_query = "UPDATE user_activity SET 
                     current_page = ?, 
                     last_activity = NOW() 
                     WHERE session_id = ?";
    
    $stmt = mysqli_prepare($conn, $update_query);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, 'ss', $current_page, $session_id);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);
        
        echo 'OK';
    } else {
        http_response_code(500);
        echo 'Database error';
    }
} else {
    http_response_code(500);
    echo 'No database connection';
}
?>
