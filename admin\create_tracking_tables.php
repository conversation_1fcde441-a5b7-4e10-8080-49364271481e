<?php
// Include configuration file
require_once '../config.php';

// Include database connection
require_once '../db_connect.php';

// Include functions
require_once '../functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

// Create user_sessions table
$create_user_sessions = "CREATE TABLE IF NOT EXISTS user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    session_id VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NOT NULL,
    device_type VARCHAR(50) NOT NULL,
    browser VARCHAR(100) NOT NULL,
    os VARCHAR(100) NOT NULL,
    page_url TEXT NOT NULL,
    referrer_url TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
)";

// Create page_views table
$create_page_views = "CREATE TABLE IF NOT EXISTS page_views (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    user_id INT NULL,
    page_url TEXT NOT NULL,
    referrer_url TEXT NULL,
    time_spent INT DEFAULT 0,
    ip_address VARCHAR(45) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
)";

// Create notifications table
$create_notifications = "CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    message TEXT NOT NULL,
    type ENUM('primary', 'success', 'warning', 'danger', 'info') DEFAULT 'primary',
    link TEXT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
)";

// Execute queries
$success = true;
$error_message = '';

if (!mysqli_query($conn, $create_user_sessions)) {
    $success = false;
    $error_message .= "Error creating user_sessions table: " . mysqli_error($conn) . "<br>";
}

if (!mysqli_query($conn, $create_page_views)) {
    $success = false;
    $error_message .= "Error creating page_views table: " . mysqli_error($conn) . "<br>";
}

if (!mysqli_query($conn, $create_notifications)) {
    $success = false;
    $error_message .= "Error creating notifications table: " . mysqli_error($conn) . "<br>";
}

// Set page title
$page_title = 'Create Tracking Tables';

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content Wrapper -->
<div class="content">
    <!-- Topbar -->
    <?php include 'includes/topbar.php'; ?>

    <!-- Begin Page Content -->
    <div class="container-fluid px-4">
        <!-- Page Heading -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0 text-gray-800">Create Tracking Tables</h1>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Database Tables Setup</h6>
                    </div>
                    <div class="card-body">
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i> All tables were created successfully!
                            </div>
                            <p>The following tables have been created:</p>
                            <ul>
                                <li><strong>user_sessions</strong> - Tracks user sessions and activity</li>
                                <li><strong>page_views</strong> - Records page views and time spent</li>
                                <li><strong>notifications</strong> - Stores user notifications</li>
                            </ul>
                            <div class="mt-4">
                                <a href="live_tracking.php" class="btn btn-primary">
                                    <i class="fas fa-user-clock me-1"></i> Go to Live Tracking
                                </a>
                                <a href="index.php" class="btn btn-secondary ms-2">
                                    <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i> There were errors creating the tables:
                                <div class="mt-2"><?php echo $error_message; ?></div>
                            </div>
                            <div class="mt-4">
                                <button onclick="location.reload()" class="btn btn-primary">
                                    <i class="fas fa-sync me-1"></i> Try Again
                                </button>
                                <a href="index.php" class="btn btn-secondary ms-2">
                                    <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap core JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Custom scripts -->
<script src="assets/js/admin.js"></script>
