<?php
// Include direct config file
require_once 'api/direct_config.php';

// Check if download_links table exists
$check_table = mysqli_query($conn, "SHOW TABLES LIKE 'download_links'");
if (mysqli_num_rows($check_table) == 0) {
    echo "Table 'download_links' does not exist!";
    exit;
}

// Get columns in download_links table
$columns_result = mysqli_query($conn, "SHOW COLUMNS FROM download_links");
$columns = [];
while ($column = mysqli_fetch_assoc($columns_result)) {
    $columns[] = $column['Field'];
}

echo "<h2>Current download_links table structure:</h2>";
echo "<pre>";
print_r($columns);
echo "</pre>";

// Check if required columns exist
$required_columns = ['id', 'content_type', 'content_id', 'quality', 'url', 'link_url', 'server_name', 'file_size', 'is_premium'];
$missing_columns = [];

foreach ($required_columns as $column) {
    if (!in_array($column, $columns)) {
        $missing_columns[] = $column;
    }
}

if (count($missing_columns) > 0) {
    echo "<h2>Missing columns:</h2>";
    echo "<pre>";
    print_r($missing_columns);
    echo "</pre>";
    
    echo "<h2>SQL to add missing columns:</h2>";
    echo "<pre>";
    foreach ($missing_columns as $column) {
        switch ($column) {
            case 'url':
                echo "ALTER TABLE download_links ADD COLUMN url TEXT;\n";
                echo "UPDATE download_links SET url = link_url;\n";
                break;
            case 'link_url':
                echo "ALTER TABLE download_links ADD COLUMN link_url TEXT;\n";
                echo "UPDATE download_links SET link_url = url;\n";
                break;
            case 'server_name':
                echo "ALTER TABLE download_links ADD COLUMN server_name VARCHAR(50) DEFAULT NULL;\n";
                break;
            case 'file_size':
                echo "ALTER TABLE download_links ADD COLUMN file_size VARCHAR(20) DEFAULT NULL;\n";
                break;
            case 'is_premium':
                echo "ALTER TABLE download_links ADD COLUMN is_premium BOOLEAN DEFAULT FALSE;\n";
                break;
            default:
                echo "-- Column $column is missing but no SQL provided\n";
                break;
        }
    }
    echo "</pre>";
} else {
    echo "<h2>All required columns exist!</h2>";
}

// Check for sample data
$data_query = "SELECT * FROM download_links LIMIT 5";
$data_result = mysqli_query($conn, $data_query);

echo "<h2>Sample data:</h2>";
echo "<pre>";
while ($row = mysqli_fetch_assoc($data_result)) {
    print_r($row);
}
echo "</pre>";
?>
