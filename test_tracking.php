<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

$page_title = "ট্র্যাকিং টেস্ট";
include 'includes/header.php';
?>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-vial"></i> ট্র্যাকিং সিস্টেম টেস্ট</h4>
                </div>
                <div class="card-body">
                    <p>এই পেজটি ট্র্যাকিং সিস্টেম টেস্ট করার জন্য তৈরি করা হয়েছে।</p>
                    
                    <div class="alert alert-info">
                        <h5>টেস্ট করার জন্য:</h5>
                        <ol>
                            <li>এই পেজে কিছুক্ষণ থাকুন</li>
                            <li>মাউস নাড়ান, স্ক্রল করুন</li>
                            <li>Admin panel এ গিয়ে "লাইভ ইউজার ট্র্যাকার" দেখুন</li>
                            <li>চ্যাট আইকনে ক্লিক করে মেসেজ পাঠান</li>
                        </ol>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h5>আপনার তথ্য:</h5>
                            <ul class="list-group">
                                <li class="list-group-item">
                                    <strong>IP:</strong> 
                                    <span id="user-ip"><?php echo $_SERVER['REMOTE_ADDR']; ?></span>
                                </li>
                                <li class="list-group-item">
                                    <strong>User Agent:</strong> 
                                    <small><?php echo $_SERVER['HTTP_USER_AGENT']; ?></small>
                                </li>
                                <li class="list-group-item">
                                    <strong>Session ID:</strong> 
                                    <code id="session-id">Loading...</code>
                                </li>
                                <li class="list-group-item">
                                    <strong>Status:</strong> 
                                    <span class="badge bg-<?php echo isLoggedIn() ? 'success' : 'secondary'; ?>">
                                        <?php echo isLoggedIn() ? 'লগইন' : 'অতিথি'; ?>
                                    </span>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>ট্র্যাকিং স্ট্যাটাস:</h5>
                            <div class="alert alert-success" id="tracking-status">
                                <i class="fas fa-spinner fa-spin"></i> ট্র্যাকিং চালু হচ্ছে...
                            </div>
                            
                            <h5>সর্বশেষ আপডেট:</h5>
                            <div id="last-update" class="text-muted">
                                <i class="fas fa-clock"></i> অপেক্ষা করুন...
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h5>টেস্ট অ্যাকশন:</h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-primary" onclick="testPageView()">
                                <i class="fas fa-eye"></i> পেজ ভিউ টেস্ট
                            </button>
                            <button type="button" class="btn btn-success" onclick="testActivity()">
                                <i class="fas fa-mouse-pointer"></i> অ্যাক্টিভিটি টেস্ট
                            </button>
                            <button type="button" class="btn btn-info" onclick="checkTrackingData()">
                                <i class="fas fa-search"></i> ডেটা চেক করুন
                            </button>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h5>লগ:</h5>
                        <div id="test-log" class="border p-3" style="height: 200px; overflow-y: auto; background-color: #f8f9fa;">
                            <div class="text-muted">লগ এখানে দেখানো হবে...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let testLogElement = document.getElementById('test-log');
let sessionIdElement = document.getElementById('session-id');
let trackingStatusElement = document.getElementById('tracking-status');
let lastUpdateElement = document.getElementById('last-update');

function addLog(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.innerHTML = `<small class="text-muted">[${timestamp}]</small> ${message}`;
    testLogElement.appendChild(logEntry);
    testLogElement.scrollTop = testLogElement.scrollHeight;
}

function testPageView() {
    addLog('<span class="text-primary">পেজ ভিউ টেস্ট শুরু...</span>');
    // Simulate page view by updating activity
    if (window.updateUserActivity) {
        window.updateUserActivity();
        addLog('<span class="text-success">পেজ ভিউ আপডেট পাঠানো হয়েছে</span>');
    } else {
        addLog('<span class="text-danger">ট্র্যাকিং স্ক্রিপ্ট লোড হয়নি</span>');
    }
}

function testActivity() {
    addLog('<span class="text-primary">অ্যাক্টিভিটি টেস্ট শুরু...</span>');
    
    // Simulate mouse activity
    const event = new MouseEvent('mousemove', {
        clientX: Math.random() * window.innerWidth,
        clientY: Math.random() * window.innerHeight
    });
    document.dispatchEvent(event);
    
    addLog('<span class="text-success">মাউস মুভমেন্ট সিমুলেট করা হয়েছে</span>');
    
    // Simulate scroll
    window.scrollBy(0, 10);
    setTimeout(() => window.scrollBy(0, -10), 100);
    
    addLog('<span class="text-success">স্ক্রল অ্যাক্টিভিটি সিমুলেট করা হয়েছে</span>');
}

function checkTrackingData() {
    addLog('<span class="text-primary">ট্র্যাকিং ডেটা চেক করা হচ্ছে...</span>');
    
    fetch('includes/update_activity.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'page=' + encodeURIComponent(window.location.pathname)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLog('<span class="text-success">ট্র্যাকিং ডেটা সফলভাবে আপডেট হয়েছে</span>');
            if (data.session_id) {
                sessionIdElement.textContent = data.session_id.substring(0, 16) + '...';
            }
        } else {
            addLog('<span class="text-danger">ট্র্যাকিং ডেটা আপডেট ব্যর্থ: ' + (data.message || 'অজানা ত্রুটি') + '</span>');
        }
    })
    .catch(error => {
        addLog('<span class="text-danger">নেটওয়ার্ক ত্রুটি: ' + error.message + '</span>');
    });
}

// Check if tracking script is loaded
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        if (window.updateUserActivity) {
            trackingStatusElement.innerHTML = '<i class="fas fa-check-circle"></i> ট্র্যাকিং সক্রিয়';
            trackingStatusElement.className = 'alert alert-success';
            addLog('<span class="text-success">ট্র্যাকিং স্ক্রিপ্ট সফলভাবে লোড হয়েছে</span>');
        } else {
            trackingStatusElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i> ট্র্যাকিং স্ক্রিপ্ট লোড হয়নি';
            trackingStatusElement.className = 'alert alert-warning';
            addLog('<span class="text-warning">ট্র্যাকিং স্ক্রিপ্ট লোড হয়নি - header.php এ include করা হয়েছে কিনা চেক করুন</span>');
        }
    }, 2000);
    
    // Update last activity time every 5 seconds
    setInterval(() => {
        lastUpdateElement.innerHTML = '<i class="fas fa-clock"></i> ' + new Date().toLocaleTimeString();
    }, 5000);
    
    addLog('<span class="text-info">টেস্ট পেজ লোড হয়েছে</span>');
});

// Monitor tracking updates
if (window.addEventListener) {
    window.addEventListener('userActivityUpdated', function(e) {
        addLog('<span class="text-info">ইউজার অ্যাক্টিভিটি আপডেট হয়েছে</span>');
    });
}
</script>

<?php include 'includes/footer.php'; ?>
