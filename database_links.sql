-- Add download links table
CREATE TABLE IF NOT EXISTS download_links (
    id INT AUTO_INCREMENT PRIMARY KEY,
    content_type ENUM('movie', 'tvshow') NOT NULL,
    content_id INT NOT NULL,
    quality VARCHAR(20) NOT NULL COMMENT 'e.g. 720p, 1080p, 4K',
    link_type ENUM('direct', 'torrent', 'gdrive', 'mega') NOT NULL,
    link_url TEXT NOT NULL,
    is_premium BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_download_link (content_type, content_id, quality, link_type)
);

-- Add streaming links table
CREATE TABLE IF NOT EXISTS streaming_links (
    id INT AUTO_INCREMENT PRIMARY KEY,
    content_type ENUM('movie', 'tvshow') NOT NULL,
    content_id INT NOT NULL,
    quality VARCHAR(20) NOT NULL COMMENT 'e.g. 720p, 1080p, 4K',
    server_name VARCHAR(50) NOT NULL COMMENT 'e.g. Server 1, Server 2',
    stream_url TEXT NOT NULL,
    is_premium BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_stream_link (content_type, content_id, quality, server_name)
);

-- Add episodes table for TV shows
CREATE TABLE IF NOT EXISTS episodes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tvshow_id INT NOT NULL,
    season_number INT NOT NULL,
    episode_number INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    duration INT COMMENT 'Duration in minutes',
    thumbnail VARCHAR(255),
    release_date DATE,
    is_premium BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tvshow_id) REFERENCES tvshows(id) ON DELETE CASCADE,
    UNIQUE KEY unique_episode (tvshow_id, season_number, episode_number)
);

-- Add episode links table
CREATE TABLE IF NOT EXISTS episode_links (
    id INT AUTO_INCREMENT PRIMARY KEY,
    episode_id INT NOT NULL,
    link_type ENUM('download', 'stream') NOT NULL,
    quality VARCHAR(20) NOT NULL COMMENT 'e.g. 720p, 1080p, 4K',
    server_name VARCHAR(50) COMMENT 'For streaming links',
    link_url TEXT NOT NULL,
    is_premium BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (episode_id) REFERENCES episodes(id) ON DELETE CASCADE,
    UNIQUE KEY unique_episode_link (episode_id, link_type, quality, server_name)
);

-- Insert sample download links for movies
INSERT INTO download_links (content_type, content_id, quality, link_type, link_url, is_premium) VALUES
('movie', 1, '720p', 'direct', 'https://example.com/movies/matrix_720p.mp4', FALSE),
('movie', 1, '1080p', 'direct', 'https://example.com/movies/matrix_1080p.mp4', TRUE),
('movie', 1, '4K', 'direct', 'https://example.com/movies/matrix_4k.mp4', TRUE),
('movie', 2, '720p', 'direct', 'https://example.com/movies/inception_720p.mp4', FALSE),
('movie', 2, '1080p', 'direct', 'https://example.com/movies/inception_1080p.mp4', TRUE),
('movie', 3, '720p', 'direct', 'https://example.com/movies/shawshank_720p.mp4', FALSE),
('movie', 3, '1080p', 'direct', 'https://example.com/movies/shawshank_1080p.mp4', TRUE),
('movie', 4, '720p', 'direct', 'https://example.com/movies/dark_knight_720p.mp4', FALSE),
('movie', 4, '1080p', 'direct', 'https://example.com/movies/dark_knight_1080p.mp4', TRUE),
('movie', 5, '720p', 'direct', 'https://example.com/movies/pulp_fiction_720p.mp4', FALSE),
('movie', 5, '1080p', 'direct', 'https://example.com/movies/pulp_fiction_1080p.mp4', TRUE);

-- Insert sample streaming links for movies
INSERT INTO streaming_links (content_type, content_id, quality, server_name, stream_url, is_premium) VALUES
('movie', 1, '720p', 'Server 1', 'https://example.com/stream/matrix_720p_s1', FALSE),
('movie', 1, '1080p', 'Server 1', 'https://example.com/stream/matrix_1080p_s1', TRUE),
('movie', 1, '720p', 'Server 2', 'https://example.com/stream/matrix_720p_s2', FALSE),
('movie', 2, '720p', 'Server 1', 'https://example.com/stream/inception_720p_s1', FALSE),
('movie', 2, '1080p', 'Server 1', 'https://example.com/stream/inception_1080p_s1', TRUE),
('movie', 3, '720p', 'Server 1', 'https://example.com/stream/shawshank_720p_s1', FALSE),
('movie', 3, '1080p', 'Server 1', 'https://example.com/stream/shawshank_1080p_s1', TRUE),
('movie', 4, '720p', 'Server 1', 'https://example.com/stream/dark_knight_720p_s1', FALSE),
('movie', 4, '1080p', 'Server 1', 'https://example.com/stream/dark_knight_1080p_s1', TRUE),
('movie', 5, '720p', 'Server 1', 'https://example.com/stream/pulp_fiction_720p_s1', FALSE),
('movie', 5, '1080p', 'Server 1', 'https://example.com/stream/pulp_fiction_1080p_s1', TRUE);

-- Insert sample episodes for TV shows
INSERT INTO episodes (tvshow_id, season_number, episode_number, title, description, duration, thumbnail, is_premium) VALUES
(1, 1, 1, 'Pilot', 'Walter White, a chemistry teacher, discovers that he has cancer and decides to get into the meth-making business to repay his medical debts.', 58, 'breaking_bad_s01e01.jpg', FALSE),
(1, 1, 2, 'Cat\'s in the Bag...', 'Walt and Jesse attempt to dispose of the bodies of two rivals, but fail to do so.', 48, 'breaking_bad_s01e02.jpg', FALSE),
(1, 1, 3, '...And the Bag\'s in the River', 'Walter fights with Jesse over his drug use, causing him to leave Walter alone with a very sick Krazy-8.', 48, 'breaking_bad_s01e03.jpg', TRUE),
(2, 1, 1, 'Winter Is Coming', 'Eddard Stark is torn between his family and an old friend when asked to serve at the side of King Robert Baratheon.', 62, 'got_s01e01.jpg', FALSE),
(2, 1, 2, 'The Kingsroad', 'While Bran recovers from his fall, Ned takes only his daughters to King\'s Landing.', 56, 'got_s01e02.jpg', FALSE),
(2, 1, 3, 'Lord Snow', 'Jon begins his training with the Night\'s Watch; Ned confronts his past and future at King\'s Landing.', 58, 'got_s01e03.jpg', TRUE);

-- Insert sample episode links
INSERT INTO episode_links (episode_id, link_type, quality, server_name, link_url, is_premium) VALUES
(1, 'download', '720p', NULL, 'https://example.com/episodes/breaking_bad_s01e01_720p.mp4', FALSE),
(1, 'download', '1080p', NULL, 'https://example.com/episodes/breaking_bad_s01e01_1080p.mp4', TRUE),
(1, 'stream', '720p', 'Server 1', 'https://example.com/stream/breaking_bad_s01e01_720p_s1', FALSE),
(1, 'stream', '1080p', 'Server 1', 'https://example.com/stream/breaking_bad_s01e01_1080p_s1', TRUE),
(2, 'download', '720p', NULL, 'https://example.com/episodes/breaking_bad_s01e02_720p.mp4', FALSE),
(2, 'download', '1080p', NULL, 'https://example.com/episodes/breaking_bad_s01e02_1080p.mp4', TRUE),
(2, 'stream', '720p', 'Server 1', 'https://example.com/stream/breaking_bad_s01e02_720p_s1', FALSE),
(3, 'download', '720p', NULL, 'https://example.com/episodes/breaking_bad_s01e03_720p.mp4', TRUE),
(3, 'stream', '720p', 'Server 1', 'https://example.com/stream/breaking_bad_s01e03_720p_s1', TRUE),
(4, 'download', '720p', NULL, 'https://example.com/episodes/got_s01e01_720p.mp4', FALSE),
(4, 'download', '1080p', NULL, 'https://example.com/episodes/got_s01e01_1080p.mp4', TRUE),
(4, 'stream', '720p', 'Server 1', 'https://example.com/stream/got_s01e01_720p_s1', FALSE),
(5, 'download', '720p', NULL, 'https://example.com/episodes/got_s01e02_720p.mp4', FALSE),
(5, 'stream', '720p', 'Server 1', 'https://example.com/stream/got_s01e02_720p_s1', FALSE),
(6, 'download', '720p', NULL, 'https://example.com/episodes/got_s01e03_720p.mp4', TRUE),
(6, 'stream', '720p', 'Server 1', 'https://example.com/stream/got_s01e03_720p_s1', TRUE);
