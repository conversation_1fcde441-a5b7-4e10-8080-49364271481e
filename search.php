<?php
require_once 'includes/header.php';
require_once 'includes/ad_placeholder.php';

// Add custom CSS for search cards
?>
<link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/search-card.css">

<?php
// Get search parameters
$query = isset($_GET['query']) ? sanitize($_GET['query']) : '';
$type = isset($_GET['type']) ? sanitize($_GET['type']) : '';

// Validate search query
if (empty($query)) {
    redirect(SITE_URL);
}

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$items_per_page = 12;
$offset = ($page - 1) * $items_per_page;

// Build search query
if ($type == 'movie') {
    // Search only movies
    $search_query = "SELECT 'movie' as content_type, m.id, m.title, m.description, m.release_year as year,
                    m.poster, m.rating, c.name as category_name, m.category_id, m.trailer_url
                    FROM movies m
                    LEFT JOIN categories c ON m.category_id = c.id
                    WHERE m.is_active = 1 AND (m.title LIKE '%$query%' OR m.description LIKE '%$query%')";
} elseif ($type == 'tvshow') {
    // Search only TV shows
    $search_query = "SELECT 'tvshow' as content_type, t.id, t.title, t.description, t.start_year as year,
                    t.end_year, t.poster, t.rating, c.name as category_name, t.category_id, t.trailer_url
                    FROM tvshows t
                    LEFT JOIN categories c ON t.category_id = c.id
                    WHERE t.is_active = 1 AND (t.title LIKE '%$query%' OR t.description LIKE '%$query%')";
} else {
    // Search both movies and TV shows
    $search_query = "SELECT 'movie' as content_type, m.id, m.title, m.description, m.release_year as year,
                    NULL as end_year, m.poster, m.rating, c.name as category_name, m.category_id, m.trailer_url
                    FROM movies m
                    LEFT JOIN categories c ON m.category_id = c.id
                    WHERE m.is_active = 1 AND (m.title LIKE '%$query%' OR m.description LIKE '%$query%')
                    UNION
                    SELECT 'tvshow' as content_type, t.id, t.title, t.description, t.start_year as year,
                    t.end_year, t.poster, t.rating, c.name as category_name, t.category_id, t.trailer_url
                    FROM tvshows t
                    LEFT JOIN categories c ON t.category_id = c.id
                    WHERE t.is_active = 1 AND (t.title LIKE '%$query%' OR t.description LIKE '%$query%')";
}

// Add sorting
$search_query .= " ORDER BY rating DESC";

// Get total count for pagination
$count_result = mysqli_query($conn, $search_query);
$total_items = mysqli_num_rows($count_result);
$total_pages = ceil($total_items / $items_per_page);

// Add limit for pagination
$search_query .= " LIMIT $offset, $items_per_page";

// Execute query
$result = mysqli_query($conn, $search_query);
?>

<!-- Page Header -->
<section class="py-5 bg-dark">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="display-4 fw-bold">Search Results</h1>
                <p class="lead text-light">Results for: <span class="text-danger">"<?php echo htmlspecialchars($query); ?>"</span></p>
            </div>
            <div class="col-md-6">
                <form action="<?php echo SITE_URL; ?>/search.php" method="GET" class="d-flex">
                    <?php if($type): ?>
                    <input type="hidden" name="type" value="<?php echo $type; ?>">
                    <?php endif; ?>
                    <input type="search" name="query" class="form-control me-2" placeholder="Search..." value="<?php echo htmlspecialchars($query); ?>">
                    <button type="submit" class="btn btn-danger"><i class="fas fa-search me-1"></i> Search</button>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Filter Tabs -->
<section class="py-4">
    <div class="container">
        <ul class="nav nav-pills">
            <li class="nav-item me-2">
                <a class="nav-link <?php echo empty($type) ? 'active bg-danger' : ''; ?>" href="<?php echo SITE_URL; ?>/search.php?query=<?php echo urlencode($query); ?>">
                    <i class="fas fa-filter me-1"></i> All
                </a>
            </li>
            <li class="nav-item me-2">
                <a class="nav-link <?php echo $type == 'movie' ? 'active bg-danger' : ''; ?>" href="<?php echo SITE_URL; ?>/search.php?query=<?php echo urlencode($query); ?>&type=movie">
                    <i class="fas fa-film me-1"></i> Movies
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $type == 'tvshow' ? 'active bg-danger' : ''; ?>" href="<?php echo SITE_URL; ?>/search.php?query=<?php echo urlencode($query); ?>&type=tvshow">
                    <i class="fas fa-tv me-1"></i> TV Shows
                </a>
            </li>
        </ul>
    </div>
</section>

<!-- Top Ad Section -->
<div class="container my-4">
    <?php echo renderRealAd('banner_728x90'); ?>
</div>

<!-- Search Results -->
<section class="py-4">
    <div class="container-fluid px-4">
        <?php if(mysqli_num_rows($result) > 0): ?>
            <div class="row g-2">
                <?php while($item = mysqli_fetch_assoc($result)): ?>
                <div class="col-6 col-sm-4 col-md-3 col-lg-2 mb-2">
                    <div class="search-card" data-category="<?php echo $item['category_id']; ?>">
                        <?php if($item['content_type'] == 'movie'): ?>
                        <a href="<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $item['id']; ?>" class="search-card-link"></a>
                        <?php else: ?>
                        <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $item['id']; ?>" class="search-card-link"></a>
                        <?php endif; ?>
                        <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $item['poster']; ?>" alt="<?php echo $item['title']; ?>">
                        <div class="search-card-overlay">
                            <h5 class="search-card-title"><?php echo $item['title']; ?></h5>
                            <div class="search-card-info">
                                <?php if($item['content_type'] == 'movie'): ?>
                                <span><i class="far fa-calendar-alt"></i> <?php echo $item['year']; ?></span>
                                <?php else: ?>
                                <span><i class="far fa-calendar-alt"></i> <?php echo $item['year']; ?><?php echo $item['end_year'] ? ' - ' . $item['end_year'] : ' - Present'; ?></span>
                                <?php endif; ?> •
                                <span><i class="fas fa-film"></i> <?php echo $item['category_name']; ?></span>
                            </div>
                            <div class="search-card-rating">
                                <i class="fas fa-star"></i> <?php echo number_format($item['rating'], 1); ?>
                            </div>
                        </div>
                        <div class="search-card-buttons">
                            <?php if($item['content_type'] == 'movie'): ?>
                            <a href="<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $item['id']; ?>" class="search-card-btn" title="More Info">
                            <?php else: ?>
                            <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $item['id']; ?>" class="search-card-btn" title="More Info">
                            <?php endif; ?>
                                <i class="fas fa-info-circle"></i>
                            </a>
                            <a href="#" class="search-card-btn add-to-watchlist" data-id="<?php echo $item['id']; ?>" data-type="<?php echo $item['content_type']; ?>" title="Add to Watchlist">
                                <i class="fas fa-plus"></i>
                            </a>
                            <?php if($item['trailer_url']): ?>
                            <a href="#" class="search-card-btn play-trailer" data-trailer="<?php echo $item['trailer_url']; ?>" title="Play Trailer">
                                <i class="fas fa-play"></i>
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endwhile; ?>
            </div>

            <!-- Middle Ad Section -->
            <div class="container my-4">
                <?php echo renderRealAd('native_2'); ?>
            </div>

            <!-- Pagination -->
            <?php if($total_pages > 1): ?>
            <nav aria-label="Page navigation" class="mt-5">
                <ul class="pagination justify-content-center">
                    <?php if($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?php echo SITE_URL; ?>/search.php?query=<?php echo urlencode($query); ?><?php echo $type ? '&type=' . $type : ''; ?>&page=<?php echo $page - 1; ?>" aria-label="Previous">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php
                    // Show limited page numbers with ellipsis
                    $start_page = max(1, $page - 2);
                    $end_page = min($total_pages, $page + 2);

                    if ($start_page > 1) {
                        echo '<li class="page-item"><a class="page-link" href="' . SITE_URL . '/search.php?query=' . urlencode($query) . ($type ? '&type=' . $type : '') . '&page=1">1</a></li>';
                        if ($start_page > 2) {
                            echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                        }
                    }

                    for($i = $start_page; $i <= $end_page; $i++): ?>
                    <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                        <a class="page-link" href="<?php echo SITE_URL; ?>/search.php?query=<?php echo urlencode($query); ?><?php echo $type ? '&type=' . $type : ''; ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                    </li>
                    <?php endfor;

                    if ($end_page < $total_pages) {
                        if ($end_page < $total_pages - 1) {
                            echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                        }
                        echo '<li class="page-item"><a class="page-link" href="' . SITE_URL . '/search.php?query=' . urlencode($query) . ($type ? '&type=' . $type : '') . '&page=' . $total_pages . '">' . $total_pages . '</a></li>';
                    }
                    ?>

                    <?php if($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?php echo SITE_URL; ?>/search.php?query=<?php echo urlencode($query); ?><?php echo $type ? '&type=' . $type : ''; ?>&page=<?php echo $page + 1; ?>" aria-label="Next">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>

        <?php else: ?>
            <div class="alert alert-dark text-center p-5">
                <i class="fas fa-search fa-3x mb-3 text-danger"></i>
                <h4 class="mb-3">No results found</h4>
                <p>We couldn't find any matches for <span class="text-danger">"<?php echo htmlspecialchars($query); ?>"</span>.</p>
                <p class="mb-0">Please try another search term or browse our categories.</p>
            </div>
        <?php endif; ?>
    </div>
</section>

<?php require_once 'includes/footer.php'; ?>
