<?php
/**
 * Update database tables to add subtitle URL columns
 * 
 * This script adds subtitle URL columns to the streaming_links, download_links, and episode_links tables
 * if they don't already exist.
 */

// Include configuration
require_once 'includes/config.php';

// Function to check if a column exists in a table
function columnExists($table, $column, $conn) {
    $query = "SHOW COLUMNS FROM `$table` LIKE '$column'";
    $result = $conn->query($query);
    return $result && $result->num_rows > 0;
}

// Function to check if a table exists
function tableExists($table, $conn) {
    $query = "SHOW TABLES LIKE '$table'";
    $result = $conn->query($query);
    return $result && $result->num_rows > 0;
}

// Function to execute SQL with error handling
function executeSql($sql, $description, $conn) {
    echo "<p>Executing: $description...</p>";
    
    if ($conn->query($sql)) {
        echo "<p style='color: green;'>Success: $description</p>";
        return true;
    } else {
        echo "<p style='color: red;'>Error: " . $conn->error . "</p>";
        return false;
    }
}

// Start HTML output
echo "<!DOCTYPE html>
<html>
<head>
    <title>Update Subtitle Columns</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        p { margin: 5px 0; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>Update Database for Subtitle Support</h1>";

// Tables to check and update
$tables = [
    'streaming_links' => [
        'subtitle_url_bn' => 'TEXT',
        'subtitle_url_en' => 'TEXT'
    ],
    'download_links' => [
        'subtitle_url_bn' => 'TEXT',
        'subtitle_url_en' => 'TEXT'
    ],
    'episode_links' => [
        'subtitle_url_bn' => 'TEXT',
        'subtitle_url_en' => 'TEXT'
    ]
];

// Check and create subtitles table if it doesn't exist
if (!tableExists('subtitles', $conn)) {
    $create_subtitles_table = "CREATE TABLE IF NOT EXISTS subtitles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        content_type ENUM('movie', 'episode') NOT NULL,
        content_id INT NOT NULL,
        language VARCHAR(50) NOT NULL,
        url VARCHAR(255) NOT NULL,
        is_default BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_content (content_type, content_id)
    )";
    
    executeSql($create_subtitles_table, "Creating subtitles table", $conn);
} else {
    echo "<p class='info'>Subtitles table already exists.</p>";
}

// Check and add columns to each table
foreach ($tables as $table => $columns) {
    if (!tableExists($table, $conn)) {
        echo "<p class='info'>Table '$table' does not exist. Skipping.</p>";
        continue;
    }
    
    echo "<h2>Updating table: $table</h2>";
    
    foreach ($columns as $column => $type) {
        if (!columnExists($table, $column, $conn)) {
            $sql = "ALTER TABLE `$table` ADD COLUMN `$column` $type AFTER `link_url`";
            if ($table === 'episode_links') {
                $sql = "ALTER TABLE `$table` ADD COLUMN `$column` $type AFTER `link_url`";
            }
            
            executeSql($sql, "Adding column '$column' to table '$table'", $conn);
        } else {
            echo "<p class='info'>Column '$column' already exists in table '$table'.</p>";
        }
    }
}

// Check for alternative table names
$alternativeTables = [
    ['links', 'download_links'],
    ['links', 'streaming_links'],
    ['episode_links', 'episode_download_links'],
    ['episode_links', 'episode_streaming_links']
];

foreach ($alternativeTables as $tablePair) {
    $mainTable = $tablePair[0];
    $altTable = $tablePair[1];

    // If main table doesn't exist but alternative does
    if (!tableExists($mainTable, $conn) && tableExists($altTable, $conn)) {
        echo "<p class='info'>Found alternative table '$altTable' instead of '$mainTable'.</p>";

        // Add subtitle columns to alternative table
        if (!columnExists($altTable, 'subtitle_url_bn', $conn)) {
            $sql = "ALTER TABLE `$altTable` ADD COLUMN `subtitle_url_bn` TEXT AFTER `link_url`";
            executeSql($sql, "Adding column 'subtitle_url_bn' to table '$altTable'", $conn);
        }
        
        if (!columnExists($altTable, 'subtitle_url_en', $conn)) {
            $sql = "ALTER TABLE `$altTable` ADD COLUMN `subtitle_url_en` TEXT AFTER `subtitle_url_bn`";
            executeSql($sql, "Adding column 'subtitle_url_en' to table '$altTable'", $conn);
        }
    }
}

echo "<p>Database update completed.</p>";
echo "<p><a href='index.php'>Return to Home</a></p>";
echo "</body></html>";
?>
