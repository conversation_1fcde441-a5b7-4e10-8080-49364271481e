<?php
require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Set current page for sidebar
$current_page = 'episodes.php';

// Process form submissions
$success_message = '';
$error_message = '';

// Delete Episode
if (isset($_GET['delete_episode']) && $_GET['delete_episode'] > 0) {
    $episode_id = (int)$_GET['delete_episode'];

    $delete_query = "DELETE FROM episodes WHERE id = $episode_id";

    if (mysqli_query($conn, $delete_query)) {
        $success_message = 'Episode deleted successfully.';
    } else {
        $error_message = 'Error deleting episode: ' . mysqli_error($conn);
    }
}

// Get all TV shows with episode counts
$tvshows_query = "SELECT t.*, 
                  COUNT(e.id) as episode_count,
                  COUNT(DISTINCT e.season_number) as season_count
                  FROM tvshows t 
                  LEFT JOIN episodes e ON t.id = e.tvshow_id 
                  GROUP BY t.id 
                  ORDER BY t.title ASC";
$tvshows_result = mysqli_query($conn, $tvshows_query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Episodes - <?php echo SITE_NAME; ?> Admin</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Sidebar Responsive CSS -->
    <link rel="stylesheet" href="assets/css/sidebar-responsive.css">
    <!-- Custom CSS -->
    <style>
        body {
            background-color: #f8f9fa;
        }
        .main-content {
            margin-left: 0;
            padding: 20px;
            flex: 1;
        }
        .card {
            border: none;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            border-radius: 10px;
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .card-header {
            border-radius: 10px 10px 0 0 !important;
            border-bottom: none;
        }
        .btn {
            border-radius: 8px;
            font-weight: 500;
        }
        .table {
            border-radius: 8px;
            overflow: hidden;
        }
        .badge {
            border-radius: 6px;
            font-weight: 500;
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .tvshow-poster {
            width: 60px;
            height: 90px;
            object-fit: cover;
            border-radius: 5px;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .col-md-4 {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 15px;
            }
            
            .d-flex.justify-content-between {
                flex-direction: column;
                gap: 15px;
            }
            
            .d-flex.justify-content-between .btn {
                width: 100%;
                margin-bottom: 10px;
            }
            
            .stats-card {
                padding: 15px;
                margin-bottom: 15px;
            }
            
            .stats-card h3 {
                font-size: 1.5rem;
            }
            
            .table-responsive {
                font-size: 14px;
            }
            
            .table th,
            .table td {
                padding: 8px 5px;
            }
            
            .btn-group .btn {
                padding: 4px 8px;
                font-size: 12px;
            }
            
            .card-body {
                padding: 15px;
            }
        }

        @media (max-width: 576px) {
            .main-content {
                padding: 10px;
            }
            
            h1 {
                font-size: 1.5rem;
            }
            
            .card-header h5 {
                font-size: 1rem;
            }
            
            .stats-card {
                padding: 10px;
            }
            
            .stats-card h3 {
                font-size: 1.2rem;
            }
            
            .table {
                font-size: 12px;
            }
            
            .table th,
            .table td {
                padding: 5px 3px;
            }
            
            .btn {
                font-size: 12px;
                padding: 6px 12px;
            }
            
            .badge {
                font-size: 10px;
            }
        }

        /* Mobile-first table improvements */
        @media (max-width: 768px) {
            .table-responsive {
                border: none;
            }
            
            .table thead {
                display: none;
            }
            
            .table tbody tr {
                display: block;
                margin-bottom: 15px;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
                background: white;
            }
            
            .table tbody td {
                display: block;
                text-align: left;
                border: none;
                padding: 5px 0;
            }
            
            .table tbody td:before {
                content: attr(data-label) ": ";
                font-weight: bold;
                color: #495057;
            }
            
            .table tbody td.actions {
                text-align: center;
                margin-top: 10px;
            }
            
            .table tbody td.actions:before {
                content: "";
            }
        }

    </style>
</head>
<body>
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="mb-1">Episodes Management</h1>
                    <p class="text-muted mb-0">Manage all TV show episodes</p>
                </div>
                <div>
                    <a href="tvshows.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to TV Shows
                    </a>
                </div>
            </div>

            <?php if($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <?php if($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <!-- Statistics Cards -->
            <?php
            $total_tvshows_query = "SELECT COUNT(*) as total FROM tvshows";
            $total_tvshows_result = mysqli_query($conn, $total_tvshows_query);
            $total_tvshows = mysqli_fetch_assoc($total_tvshows_result)['total'];

            $total_episodes_query = "SELECT COUNT(*) as total FROM episodes";
            $total_episodes_result = mysqli_query($conn, $total_episodes_query);
            $total_episodes = mysqli_fetch_assoc($total_episodes_result)['total'];

            $total_seasons_query = "SELECT COUNT(DISTINCT CONCAT(tvshow_id, '-', season_number)) as total FROM episodes";
            $total_seasons_result = mysqli_query($conn, $total_seasons_query);
            $total_seasons = mysqli_fetch_assoc($total_seasons_result)['total'];
            ?>

            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-tv fa-2x"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="mb-0"><?php echo $total_tvshows; ?></h3>
                                <p class="mb-0 opacity-75">Total TV Shows</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-list fa-2x"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="mb-0"><?php echo $total_episodes; ?></h3>
                                <p class="mb-0 opacity-75">Total Episodes</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-layer-group fa-2x"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="mb-0"><?php echo $total_seasons; ?></h3>
                                <p class="mb-0 opacity-75">Total Seasons</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- TV Shows with Episodes -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-tv me-2"></i>TV Shows & Episodes
                        </h5>
                        <span class="badge bg-light text-dark">
                            <?php echo mysqli_num_rows($tvshows_result); ?> TV shows
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <?php if(mysqli_num_rows($tvshows_result) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Poster</th>
                                    <th>TV Show</th>
                                    <th>Category</th>
                                    <th>Seasons</th>
                                    <th>Episodes</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while($tvshow = mysqli_fetch_assoc($tvshows_result)): ?>
                                <tr>
                                    <td data-label="Poster">
                                        <?php if($tvshow['poster']): ?>
                                        <img src="../uploads/<?php echo $tvshow['poster']; ?>" alt="TV Show poster" class="tvshow-poster">
                                        <?php else: ?>
                                        <div class="tvshow-poster bg-secondary d-flex align-items-center justify-content-center">
                                            <i class="fas fa-tv text-white"></i>
                                        </div>
                                        <?php endif; ?>
                                    </td>
                                    <td data-label="TV Show">
                                        <div>
                                            <strong><?php echo $tvshow['title']; ?></strong>
                                            <br><small class="text-muted"><?php echo $tvshow['start_year']; ?><?php echo $tvshow['end_year'] ? ' - ' . $tvshow['end_year'] : ''; ?></small>
                                        </div>
                                    </td>
                                    <td data-label="Category">
                                        <?php
                                        $category_query = "SELECT name FROM categories WHERE id = " . $tvshow['category_id'];
                                        $category_result = mysqli_query($conn, $category_query);
                                        $category = mysqli_fetch_assoc($category_result);
                                        ?>
                                        <span class="badge bg-info"><?php echo $category ? $category['name'] : 'Unknown'; ?></span>
                                    </td>
                                    <td data-label="Seasons">
                                        <span class="badge bg-primary"><?php echo $tvshow['season_count']; ?> seasons</span>
                                    </td>
                                    <td data-label="Episodes">
                                        <span class="badge bg-success"><?php echo $tvshow['episode_count']; ?> episodes</span>
                                    </td>
                                    <td data-label="Status">
                                        <?php if($tvshow['premium_only']): ?>
                                        <span class="badge bg-danger">Premium</span>
                                        <?php else: ?>
                                        <span class="badge bg-secondary">Free</span>
                                        <?php endif; ?>
                                    </td>
                                    <td data-label="Actions" class="actions">
                                        <div class="btn-group" role="group">
                                            <a href="manage_episodes.php?tvshow=<?php echo $tvshow['id']; ?>" 
                                               class="btn btn-sm btn-primary" title="Manage Episodes">
                                                <i class="fas fa-list me-1"></i> Episodes
                                            </a>
                                            <a href="manage_links.php?type=tvshow&id=<?php echo $tvshow['id']; ?>" 
                                               class="btn btn-sm btn-info" title="Manage Links">
                                                <i class="fas fa-link me-1"></i> Links
                                            </a>
                                            <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $tvshow['id']; ?>" 
                                               class="btn btn-sm btn-success" title="View TV Show" target="_blank">
                                                <i class="fas fa-eye me-1"></i> View
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-tv fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No TV shows found</h5>
                        <p class="text-muted">Start by adding TV shows first.</p>
                        <a href="tvshows.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add TV Show
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
</body>
</html> 