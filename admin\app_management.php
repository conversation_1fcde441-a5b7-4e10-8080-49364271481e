<?php
// Include config file
require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Set page title
$page_title = 'অ্যাপ সেটিংস';

// Process form submission
$success_message = '';
$error_message = '';

// Check if app_config table exists
$check_table = "SHOW TABLES LIKE 'app_config'";
$result = mysqli_query($conn, $check_table);

if (mysqli_num_rows($result) == 0) {
    // Create app_config table
    $create_table = "CREATE TABLE IF NOT EXISTS app_config (
        id INT AUTO_INCREMENT PRIMARY KEY,
        config_key VARCHAR(100) NOT NULL,
        config_value TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_config_key (config_key)
    )";
    
    if (!mysqli_query($conn, $create_table)) {
        $error_message = 'Failed to create app_config table: ' . mysqli_error($conn);
    } else {
        // Insert default app config values
        $default_configs = [
            ['app_version', '1.0.0'],
            ['min_app_version', '1.0.0'],
            ['force_update', 'false'],
            ['update_message', 'Please update to the latest version for new features and bug fixes.'],
            ['maintenance_mode', 'false'],
            ['maintenance_message', 'We are currently performing maintenance. Please try again later.'],
            ['app_name', 'CinePix'],
            ['app_logo', 'https://cinepix.top/images/logo.png'],
            ['app_theme_color', '#E50914'],
            ['app_accent_color', '#0071EB']
        ];
        
        foreach ($default_configs as $config) {
            $insert_query = "INSERT INTO app_config (config_key, config_value) VALUES ('{$config[0]}', '{$config[1]}')";
            mysqli_query($conn, $insert_query);
        }
    }
}

// Process form submission
if (isset($_POST['save_settings'])) {
    // App settings
    $app_version = sanitize($_POST['app_version']);
    $min_app_version = sanitize($_POST['min_app_version']);
    $force_update = isset($_POST['force_update']) ? 'true' : 'false';
    $update_message = sanitize($_POST['update_message']);
    $maintenance_mode = isset($_POST['maintenance_mode']) ? 'true' : 'false';
    $maintenance_message = sanitize($_POST['maintenance_message']);
    $app_name = sanitize($_POST['app_name']);
    $app_logo = sanitize($_POST['app_logo']);
    $app_theme_color = sanitize($_POST['app_theme_color']);
    $app_accent_color = sanitize($_POST['app_accent_color']);
    
    // Update app_config table
    $configs = [
        ['app_version', $app_version],
        ['min_app_version', $min_app_version],
        ['force_update', $force_update],
        ['update_message', $update_message],
        ['maintenance_mode', $maintenance_mode],
        ['maintenance_message', $maintenance_message],
        ['app_name', $app_name],
        ['app_logo', $app_logo],
        ['app_theme_color', $app_theme_color],
        ['app_accent_color', $app_accent_color]
    ];
    
    $success = true;
    foreach ($configs as $config) {
        $update_query = "UPDATE app_config SET config_value = '{$config[1]}' WHERE config_key = '{$config[0]}'";
        if (!mysqli_query($conn, $update_query)) {
            $success = false;
            $error_message = 'Failed to update app_config: ' . mysqli_error($conn);
            break;
        }
    }
    
    if ($success) {
        $success_message = 'অ্যাপ সেটিংস সফলভাবে আপডেট করা হয়েছে।';
    }
}

// Get current app settings
$app_settings = [];
$app_settings_query = "SELECT * FROM app_config";
$app_settings_result = mysqli_query($conn, $app_settings_query);

if ($app_settings_result) {
    while ($row = mysqli_fetch_assoc($app_settings_result)) {
        $app_settings[$row['config_key']] = $row['config_value'];
    }
}

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title d-flex align-items-center">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1><?php echo $page_title; ?></h1>
            </div>

            <div class="topbar-actions">
                <div class="topbar-search">
                    <form action="search.php" method="GET">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="সার্চ করুন..." name="q">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>

                <div class="dropdown">
                    <button class="btn btn-link dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <img src="assets/img/user.png" class="rounded-circle" width="32" height="32" alt="User">
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user fa-sm me-2 text-gray-400"></i> প্রোফাইল</a></li>
                        <li><a class="dropdown-item" href="site_settings.php"><i class="fas fa-cogs fa-sm me-2 text-gray-400"></i> সেটিংস</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/logout.php"><i class="fas fa-sign-out-alt fa-sm me-2 text-gray-400"></i> লগআউট</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container-fluid">
        <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-lg-12">
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">অ্যাপ সেটিংস</h6>
                        <a href="<?php echo SITE_URL; ?>/api/v1/direct_config.php" target="_blank" class="btn btn-sm btn-info">
                            <i class="fas fa-external-link-alt me-1"></i> API এন্ডপয়েন্ট দেখুন
                        </a>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="">
                            <div class="row">
                                <!-- App Version Settings -->
                                <div class="col-md-6">
                                    <div class="card mb-4">
                                        <div class="card-header">
                                            <h5 class="mb-0"><i class="fas fa-code-branch me-2"></i>ভার্সন সেটিংস</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="app_version" class="form-label">বর্তমান অ্যাপ ভার্সন</label>
                                                <input type="text" class="form-control" id="app_version" name="app_version" value="<?php echo $app_settings['app_version'] ?? '1.0.0'; ?>" required>
                                                <div class="form-text">উদাহরণ: 1.0.0, 1.2.3, ইত্যাদি</div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="min_app_version" class="form-label">সর্বনিম্ন সমর্থিত ভার্সন</label>
                                                <input type="text" class="form-control" id="min_app_version" name="min_app_version" value="<?php echo $app_settings['min_app_version'] ?? '1.0.0'; ?>" required>
                                                <div class="form-text">এর নিচের ভার্সন ব্যবহারকারীদের আপডেট করতে বলা হবে</div>
                                            </div>
                                            <div class="mb-3 form-check">
                                                <input type="checkbox" class="form-check-input" id="force_update" name="force_update" <?php echo (isset($app_settings['force_update']) && $app_settings['force_update'] === 'true') ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="force_update">বাধ্যতামূলক আপডেট</label>
                                                <div class="form-text">চেক করলে, পুরানো ভার্সন ব্যবহারকারীরা অ্যাপ ব্যবহার করতে পারবে না</div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="update_message" class="form-label">আপডেট মেসেজ</label>
                                                <textarea class="form-control" id="update_message" name="update_message" rows="3"><?php echo $app_settings['update_message'] ?? 'Please update to the latest version for new features and bug fixes.'; ?></textarea>
                                                <div class="form-text">আপডেট প্রম্পটে দেখানো মেসেজ</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- App Maintenance Settings -->
                                <div class="col-md-6">
                                    <div class="card mb-4">
                                        <div class="card-header">
                                            <h5 class="mb-0"><i class="fas fa-tools me-2"></i>মেইনটেন্যান্স সেটিংস</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3 form-check">
                                                <input type="checkbox" class="form-check-input" id="maintenance_mode" name="maintenance_mode" <?php echo (isset($app_settings['maintenance_mode']) && $app_settings['maintenance_mode'] === 'true') ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="maintenance_mode">মেইনটেন্যান্স মোড</label>
                                                <div class="form-text">চেক করলে, অ্যাপ মেইনটেন্যান্স মোডে চলে যাবে</div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="maintenance_message" class="form-label">মেইনটেন্যান্স মেসেজ</label>
                                                <textarea class="form-control" id="maintenance_message" name="maintenance_message" rows="3"><?php echo $app_settings['maintenance_message'] ?? 'We are currently performing maintenance. Please try again later.'; ?></textarea>
                                                <div class="form-text">মেইনটেন্যান্স মোডে দেখানো মেসেজ</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- App Appearance Settings -->
                                    <div class="card mb-4">
                                        <div class="card-header">
                                            <h5 class="mb-0"><i class="fas fa-palette me-2"></i>অ্যাপিয়ারেন্স সেটিংস</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="app_name" class="form-label">অ্যাপ নাম</label>
                                                <input type="text" class="form-control" id="app_name" name="app_name" value="<?php echo $app_settings['app_name'] ?? 'CinePix'; ?>" required>
                                            </div>
                                            <div class="mb-3">
                                                <label for="app_logo" class="form-label">অ্যাপ লোগো URL</label>
                                                <input type="text" class="form-control" id="app_logo" name="app_logo" value="<?php echo $app_settings['app_logo'] ?? 'https://cinepix.top/images/logo.png'; ?>">
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="app_theme_color" class="form-label">থিম কালার</label>
                                                        <div class="input-group">
                                                            <input type="color" class="form-control form-control-color" id="app_theme_color" name="app_theme_color" value="<?php echo $app_settings['app_theme_color'] ?? '#E50914'; ?>">
                                                            <input type="text" class="form-control" value="<?php echo $app_settings['app_theme_color'] ?? '#E50914'; ?>" id="app_theme_color_text">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="app_accent_color" class="form-label">অ্যাকসেন্ট কালার</label>
                                                        <div class="input-group">
                                                            <input type="color" class="form-control form-control-color" id="app_accent_color" name="app_accent_color" value="<?php echo $app_settings['app_accent_color'] ?? '#0071EB'; ?>">
                                                            <input type="text" class="form-control" value="<?php echo $app_settings['app_accent_color'] ?? '#0071EB'; ?>" id="app_accent_color_text">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <button type="submit" name="save_settings" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save me-2"></i>সেটিংস সেভ করুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Documentation -->
        <div class="row">
            <div class="col-lg-12">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">API ডকুমেন্টেশন</h6>
                    </div>
                    <div class="card-body">
                        <h5>অ্যাপ কনফিগারেশন API</h5>
                        <p>অ্যাপ কনফিগারেশন পেতে নিম্নলিখিত API এন্ডপয়েন্ট ব্যবহার করুন:</p>
                        <div class="bg-light p-3 rounded mb-3">
                            <code>GET <?php echo SITE_URL; ?>/api/v1/direct_config.php</code>
                        </div>
                        <p>এই API থেকে নিম্নলিখিত তথ্য পাওয়া যাবে:</p>
                        <ul>
                            <li><strong>app_version</strong>: বর্তমান অ্যাপ ভার্সন</li>
                            <li><strong>min_app_version</strong>: সর্বনিম্ন সমর্থিত ভার্সন</li>
                            <li><strong>force_update</strong>: বাধ্যতামূলক আপডেট কিনা</li>
                            <li><strong>update_message</strong>: আপডেট মেসেজ</li>
                            <li><strong>maintenance_mode</strong>: মেইনটেন্যান্স মোড চালু কিনা</li>
                            <li><strong>maintenance_message</strong>: মেইনটেন্যান্স মেসেজ</li>
                            <li><strong>app_name</strong>: অ্যাপ নাম</li>
                            <li><strong>app_logo</strong>: অ্যাপ লোগো URL</li>
                            <li><strong>app_theme_color</strong>: থিম কালার</li>
                            <li><strong>app_accent_color</strong>: অ্যাকসেন্ট কালার</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Sync color inputs
    document.getElementById('app_theme_color').addEventListener('input', function() {
        document.getElementById('app_theme_color_text').value = this.value;
    });
    
    document.getElementById('app_theme_color_text').addEventListener('input', function() {
        document.getElementById('app_theme_color').value = this.value;
    });
    
    document.getElementById('app_accent_color').addEventListener('input', function() {
        document.getElementById('app_accent_color_text').value = this.value;
    });
    
    document.getElementById('app_accent_color_text').addEventListener('input', function() {
        document.getElementById('app_accent_color').value = this.value;
    });
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
