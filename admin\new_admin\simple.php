<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Set some test session data
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';
$_SESSION['user_role'] = 'admin';
$_SESSION['is_premium'] = 1;

$page_title = 'ড্যাশবোর্ড';
$current_page = 'index.php';

// Mock data
$stats = [
    'movies' => 150,
    'premium_movies' => 50,
    'tvshows' => 75,
    'premium_tvshows' => 25,
    'episodes' => 1200,
    'premium_episodes' => 400,
    'users' => 500,
    'premium_users' => 100,
    'pending_payments' => 15,
    'completed_payments' => 85,
    'payments' => 100,
    'categories' => 20,
    'reviews' => 300,
    'revenue' => 15000
];
?>
<!DOCTYPE html>
<html lang="bn" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - CinePix Admin Panel</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/admin-style.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
</head>
<body class="admin-body">
    <!-- Top Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top topbar">
        <div class="container-fluid">
            <button class="btn btn-outline-light d-lg-none me-3" type="button" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
            
            <a class="navbar-brand d-flex align-items-center" href="index.php">
                <i class="fas fa-film text-primary me-2"></i>
                <span class="brand-text">
                    <span class="text-primary">CINE</span><span class="text-light">PIX</span>
                    <small class="d-block text-muted" style="font-size: 0.7rem;">Admin Panel</small>
                </span>
            </a>
            
            <div class="search-container d-none d-md-flex flex-grow-1 mx-4">
                <div class="input-group">
                    <span class="input-group-text bg-dark border-secondary">
                        <i class="fas fa-search text-muted"></i>
                    </span>
                    <input type="text" class="form-control bg-dark border-secondary text-light" 
                           placeholder="Search movies, shows, users...">
                </div>
            </div>
            
            <ul class="navbar-nav ms-auto d-flex flex-row align-items-center">
                <li class="nav-item dropdown me-3">
                    <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">3</span>
                    </a>
                </li>
                
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" 
                       role="button" data-bs-toggle="dropdown">
                        <img src="../../assets/img/admin-avatar.png" class="rounded-circle me-2" width="32" height="32">
                        <span class="d-none d-md-inline"><?php echo $_SESSION['username']; ?></span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
                        <li><a class="dropdown-item text-danger" href="../../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="admin-container">
        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-content">
                <div class="sidebar-header">
                    <div class="sidebar-brand">
                        <i class="fas fa-film text-primary"></i>
                        <span class="brand-text ms-2">
                            <span class="text-primary">CINE</span><span class="text-light">PIX</span>
                        </span>
                    </div>
                </div>

                <div class="sidebar-user-info">
                    <div class="user-avatar">
                        <img src="../../assets/img/admin-avatar.png" alt="Admin" class="rounded-circle">
                        <div class="status-indicator online"></div>
                    </div>
                    <div class="user-details">
                        <div class="user-name"><?php echo $_SESSION['username']; ?></div>
                        <div class="user-role">Administrator</div>
                    </div>
                </div>

                <ul class="sidebar-nav">
                    <li class="nav-item">
                        <a href="index.php" class="nav-link active">
                            <i class="fas fa-tachometer-alt nav-icon"></i>
                            <span class="nav-text">ড্যাশবোর্ড</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="movies.php" class="nav-link">
                            <i class="fas fa-film nav-icon"></i>
                            <span class="nav-text">মুভি</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="tvshows.php" class="nav-link">
                            <i class="fas fa-tv nav-icon"></i>
                            <span class="nav-text">টিভি শো</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="users.php" class="nav-link">
                            <i class="fas fa-users nav-icon"></i>
                            <span class="nav-text">ইউজার</span>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="main-content">
            <div class="page-header mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h1 class="page-title">
                            <i class="fas fa-tachometer-alt me-3"></i>ড্যাশবোর্ড
                        </h1>
                        <p class="page-subtitle text-muted">CinePix Admin Panel এ স্বাগতম</p>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row stats-grid mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stat-card bg-gradient-primary">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col">
                                    <div class="stat-icon">
                                        <i class="fas fa-film"></i>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="stat-content text-end">
                                        <h3 class="stat-number"><?php echo number_format($stats['movies']); ?></h3>
                                        <p class="stat-label">মোট মুভি</p>
                                        <small class="stat-sublabel">
                                            <i class="fas fa-crown me-1"></i><?php echo $stats['premium_movies']; ?> প্রিমিয়াম
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stat-card bg-gradient-success">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col">
                                    <div class="stat-icon">
                                        <i class="fas fa-tv"></i>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="stat-content text-end">
                                        <h3 class="stat-number"><?php echo number_format($stats['tvshows']); ?></h3>
                                        <p class="stat-label">টিভি শো</p>
                                        <small class="stat-sublabel">
                                            <i class="fas fa-crown me-1"></i><?php echo $stats['premium_tvshows']; ?> প্রিমিয়াম
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stat-card bg-gradient-info">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col">
                                    <div class="stat-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="stat-content text-end">
                                        <h3 class="stat-number"><?php echo number_format($stats['users']); ?></h3>
                                        <p class="stat-label">মোট ইউজার</p>
                                        <small class="stat-sublabel">
                                            <i class="fas fa-crown me-1"></i><?php echo $stats['premium_users']; ?> প্রিমিয়াম
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stat-card bg-gradient-warning">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col">
                                    <div class="stat-icon">
                                        <i class="fas fa-money-bill-wave"></i>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="stat-content text-end">
                                        <h3 class="stat-number">৳<?php echo number_format($stats['revenue']); ?></h3>
                                        <p class="stat-label">মোট আয়</p>
                                        <small class="stat-sublabel">
                                            <i class="fas fa-clock me-1"></i><?php echo $stats['pending_payments']; ?> পেন্ডিং
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chart -->
            <div class="row mb-4">
                <div class="col-xl-8 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-line me-2"></i>রেভিনিউ ট্রেন্ড
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="revenueChart" height="100"></canvas>
                        </div>
                    </div>
                </div>

                <div class="col-xl-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>দ্রুত অ্যাকশন
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="#" class="btn btn-outline-primary">
                                    <i class="fas fa-film me-2"></i>নতুন মুভি যোগ করুন
                                </a>
                                <a href="#" class="btn btn-outline-success">
                                    <i class="fas fa-tv me-2"></i>নতুন টিভি শো যোগ করুন
                                </a>
                                <a href="#" class="btn btn-outline-warning">
                                    <i class="fas fa-crown me-2"></i>প্রিমিয়াম ম্যানেজ করুন
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/admin-script.js"></script>
    
    <script>
    document.addEventListener("DOMContentLoaded", function() {
        const ctx = document.getElementById("revenueChart");
        if (ctx) {
            new Chart(ctx, {
                type: "line",
                data: {
                    labels: ["জানুয়ারি", "ফেব্রুয়ারি", "মার্চ", "এপ্রিল", "মে", "জুন"],
                    datasets: [{
                        label: "রেভিনিউ (৳)",
                        data: [1200, 1900, 800, 1500, 2000, 2400],
                        borderColor: "#e50914",
                        backgroundColor: "rgba(229, 9, 20, 0.1)",
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: "#ffffff"
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                color: "#b3b3b3",
                                callback: function(value) {
                                    return "৳" + value;
                                }
                            },
                            grid: {
                                color: "#333333"
                            }
                        },
                        x: {
                            ticks: {
                                color: "#b3b3b3"
                            },
                            grid: {
                                color: "#333333"
                            }
                        }
                    }
                }
            });
        }
    });
    </script>
</body>
</html>
