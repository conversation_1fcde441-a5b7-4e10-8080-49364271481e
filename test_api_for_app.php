<?php
// Include direct config file
require_once 'api/direct_config.php';

// No admin check required for testing
// if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
//     header("Location: login.php");
//     exit;
// }

echo "<h1>API Test for Mobile App</h1>";

// Test API endpoints
$endpoints = [
    'api/v1/direct_movies.php',
    'api/v1/direct_tvshows.php',
    'api/v1/direct_movie_details.php?id=1',
    'api/v1/direct_tvshow_details.php?id=1',
    'api/v1/direct_tvshow_episodes.php?id=1&season=1',
    'api/v1/direct_search.php?q=test'
];

echo "<h2>API Endpoint Tests</h2>";
echo "<table border='1' cellpadding='10'>";
echo "<tr><th>Endpoint</th><th>Status</th><th>Response</th></tr>";

foreach ($endpoints as $endpoint) {
    echo "<tr>";
    echo "<td>$endpoint</td>";

    // Get the contents of the endpoint
    $url = SITE_URL . '/' . $endpoint;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($http_code == 200) {
        $json = json_decode($response, true);
        if ($json && isset($json['success']) && $json['success'] === true) {
            echo "<td style='background-color: #dff0d8;'>✓ Success</td>";

            // Check specific data
            if (strpos($endpoint, 'direct_movies.php') !== false) {
                $count = count($json['data']['movies'] ?? []);
                echo "<td>Found $count movies</td>";
            } elseif (strpos($endpoint, 'direct_tvshows.php') !== false) {
                $count = count($json['data']['tvshows'] ?? []);
                echo "<td>Found $count TV shows</td>";
            } elseif (strpos($endpoint, 'direct_movie_details.php') !== false) {
                $links_count = count($json['data']['download_links'] ?? []);
                echo "<td>Movie has $links_count download links</td>";
            } elseif (strpos($endpoint, 'direct_tvshow_details.php') !== false) {
                $seasons_count = count($json['data']['seasons'] ?? []);
                echo "<td>TV show has $seasons_count seasons</td>";
            } elseif (strpos($endpoint, 'direct_tvshow_episodes.php') !== false) {
                $episodes_count = count($json['data']['episodes'] ?? []);
                echo "<td>Season has $episodes_count episodes</td>";
            } elseif (strpos($endpoint, 'direct_search.php') !== false) {
                $movies_count = count($json['data']['movies'] ?? []);
                $tvshows_count = count($json['data']['tvshows'] ?? []);
                echo "<td>Found $movies_count movies and $tvshows_count TV shows</td>";
            } else {
                echo "<td>Success</td>";
            }
        } else {
            echo "<td style='background-color: #f2dede;'>✗ Failed</td>";
            echo "<td>Invalid response format</td>";
        }
    } else {
        echo "<td style='background-color: #f2dede;'>✗ Failed ($http_code)</td>";
        echo "<td>Request failed</td>";
    }

    echo "</tr>";
}

echo "</table>";

// Check download links
echo "<h2>Download Links Check</h2>";

// Get sample movie
$movie_query = "SELECT id, title FROM movies ORDER BY id DESC LIMIT 1";
$movie_result = mysqli_query($conn, $movie_query);
$movie = mysqli_fetch_assoc($movie_result);

if ($movie) {
    $movie_id = $movie['id'];
    $title = $movie['title'];

    echo "<h3>Movie: $title (ID: $movie_id)</h3>";

    // Get download links
    $links_query = "SELECT * FROM download_links WHERE content_type = 'movie' AND content_id = $movie_id";
    $links_result = mysqli_query($conn, $links_query);

    if (mysqli_num_rows($links_result) > 0) {
        echo "<table border='1' cellpadding='10'>";
        echo "<tr><th>ID</th><th>Quality</th><th>URL</th><th>Premium</th></tr>";

        while ($link = mysqli_fetch_assoc($links_result)) {
            echo "<tr>";
            echo "<td>{$link['id']}</td>";
            echo "<td>{$link['quality']}</td>";
            $url = isset($link['url']) ? $link['url'] : (isset($link['link_url']) ? $link['link_url'] : 'No URL');
            echo "<td>" . substr($url, 0, 50) . "...</td>";
            $is_premium = isset($link['is_premium']) ? ($link['is_premium'] ? 'Yes' : 'No') : 'Unknown';
            echo "<td>$is_premium</td>";
            echo "</tr>";
        }

        echo "</table>";
    } else {
        echo "<p>No download links found for this movie.</p>";
    }
} else {
    echo "<p>No movies found in the database.</p>";
}

// Get sample TV show
$tvshow_query = "SELECT id, title FROM tvshows ORDER BY id DESC LIMIT 1";
$tvshow_result = mysqli_query($conn, $tvshow_query);
$tvshow = mysqli_fetch_assoc($tvshow_result);

if ($tvshow) {
    $tvshow_id = $tvshow['id'];
    $title = $tvshow['title'];

    echo "<h3>TV Show: $title (ID: $tvshow_id)</h3>";

    // Get episodes
    $episodes_query = "SELECT * FROM episodes WHERE tvshow_id = $tvshow_id ORDER BY season_number, episode_number LIMIT 5";
    $episodes_result = mysqli_query($conn, $episodes_query);

    if (mysqli_num_rows($episodes_result) > 0) {
        echo "<table border='1' cellpadding='10'>";
        echo "<tr><th>ID</th><th>Season</th><th>Episode</th><th>Title</th><th>Download Links</th></tr>";

        while ($episode = mysqli_fetch_assoc($episodes_result)) {
            $episode_id = $episode['id'];

            // Get download links for this episode
            $links_query = "SELECT * FROM download_links WHERE content_type = 'episode' AND content_id = $episode_id";
            $links_result = mysqli_query($conn, $links_query);
            $links_count = mysqli_num_rows($links_result);

            echo "<tr>";
            echo "<td>{$episode['id']}</td>";
            echo "<td>{$episode['season_number']}</td>";
            echo "<td>{$episode['episode_number']}</td>";
            echo "<td>{$episode['title']}</td>";
            echo "<td>$links_count links</td>";
            echo "</tr>";
        }

        echo "</table>";
    } else {
        echo "<p>No episodes found for this TV show.</p>";
    }
} else {
    echo "<p>No TV shows found in the database.</p>";
}

// Flutter app API constants
echo "<h2>Flutter App API Constants</h2>";
echo "<pre>";
echo "class ApiConstants {
  // API Base URL
  static const String baseUrl = '" . SITE_URL . "/api/v1';

  // API Endpoints
  static const String configEndpoint = '/direct_config.php';
  static const String loginEndpoint = '/direct_login.php';
  static const String registerEndpoint = '/direct_register.php';
  static const String moviesEndpoint = '/direct_movies.php';
  static const String movieDetailsEndpoint = '/direct_movie_details.php';
  static const String tvShowsEndpoint = '/direct_tvshows.php';
  static const String tvShowDetailsEndpoint = '/direct_tvshow_details.php';
  static const String tvShowEpisodesEndpoint = '/direct_tvshow_episodes.php';
  static const String searchEndpoint = '/direct_search.php';
  static const String categoriesEndpoint = '/direct_categories.php';
  static const String profileEndpoint = '/direct_profile.php';
}";
echo "</pre>";

echo "<p><a href='admin/index.php'>Return to Admin Dashboard</a></p>";
?>
