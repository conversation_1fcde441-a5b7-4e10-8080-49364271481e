<?php
require_once 'includes/header.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect(SITE_URL . '/login.php');
}

// Check if payment_id is provided
if (!isset($_GET['payment_id'])) {
    redirect(SITE_URL . '/premium.php');
}

$payment_id = (int)$_GET['payment_id'];

// Get payment details
$payment_query = "SELECT p.*, s.plan_id, pp.name as plan_name, pp.price
                 FROM payments p
                 JOIN subscriptions s ON p.subscription_id = s.id
                 JOIN premium_plans pp ON s.plan_id = pp.id
                 WHERE p.id = $payment_id AND p.user_id = {$_SESSION['user_id']}";
$payment_result = mysqli_query($conn, $payment_query);

if (mysqli_num_rows($payment_result) == 0) {
    redirect(SITE_URL . '/premium.php');
}

$payment = mysqli_fetch_assoc($payment_result);

// bKash Sandbox API credentials - using constants from config.php
$bkash_app_key = '5tunt4masn6pv2hnvte1sb5n3j';
$bkash_app_secret = '1vggbqd4hqk9g96o9rrrp2jftvek578v7d2bnerim12a87dbrrka';
$bkash_username = 'sandboxTestUser';
$bkash_password = 'hWD@8vtzw0';
$bkash_sandbox_url = 'https://checkout.sandbox.bka.sh/v1.2.0-beta';

// Debug API credentials
error_log("bKash API credentials (direct): APP_KEY=$bkash_app_key, USERNAME=$bkash_username");

// Process bKash payment
$payment_status = '';
$payment_message = '';
$bkash_token = '';
$payment_id_for_bkash = $payment_id; // Store payment ID for bKash API

// Function to get bKash token
function getBkashToken($url, $app_key, $app_secret, $username, $password) {
    // Debug information
    error_log("bKash token request parameters: URL=$url, APP_KEY=$app_key, USERNAME=$username");

    $post_token = array(
        'app_key' => $app_key,
        'app_secret' => $app_secret,
        'username' => $username,
        'password' => $password
    );

    // Debug the request payload
    error_log("bKash token request payload: " . json_encode($post_token));

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url . '/checkout/token/grant');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post_token));
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
        'Accept: application/json'
    ));

    // Add SSL verification options
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    $response = curl_exec($ch);

    // Check for cURL errors
    if(curl_errno($ch)) {
        error_log("bKash cURL Error: " . curl_error($ch));
    }

    curl_close($ch);

    // Debug the response
    error_log("bKash token response: " . $response);

    return json_decode($response, true);
}

// Function to create bKash payment
function createBkashPayment($url, $token, $amount, $invoice, $intent, $currency = 'BDT') {
    $post_data = array(
        'amount' => $amount,
        'currency' => $currency,
        'intent' => $intent,
        'merchantInvoiceNumber' => $invoice,
        'callbackURL' => SITE_URL . '/bkash_callback.php'
    );

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url . '/checkout/payment/create');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
        'Accept: application/json',
        'Authorization: ' . $token,
        'X-APP-Key: ' . BKASH_APP_KEY
    ));

    $response = curl_exec($ch);
    curl_close($ch);

    // Log the response for debugging
    error_log("bKash create payment response: " . $response);

    return json_decode($response, true);
}

// Function to execute bKash payment
function executeBkashPayment($url, $token, $paymentID) {
    $post_data = array(
        'paymentID' => $paymentID
    );

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url . '/checkout/payment/execute');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
        'Accept: application/json',
        'Authorization: ' . $token,
        'X-APP-Key: ' . BKASH_APP_KEY
    ));

    $response = curl_exec($ch);
    curl_close($ch);

    return json_decode($response, true);
}

// Check if we have a bKash payment ID from the callback
if (isset($_GET['bkash_payment_id']) && isset($_GET['status'])) {
    $bkash_payment_id = $_GET['bkash_payment_id'];
    $status = $_GET['status'];

    // Get token
    $token_response = getBkashToken($bkash_sandbox_url, $bkash_app_key, $bkash_app_secret, $bkash_username, $bkash_password);

    if (isset($token_response['id_token'])) {
        $bkash_token = $token_response['id_token'];

        if ($status == 'success') {
            // Execute payment
            $execute_response = executeBkashPayment($bkash_sandbox_url, $bkash_token, $bkash_payment_id);

            if (isset($execute_response['transactionStatus']) && $execute_response['transactionStatus'] == 'Completed') {
                // Update payment status to completed
                $bkash_transaction_id = $execute_response['trxID'];
                $update_query = "UPDATE payments SET payment_status = 'completed', transaction_id = '$bkash_transaction_id' WHERE id = $payment_id_for_bkash";
                mysqli_query($conn, $update_query);

                // Update user to premium
                $update_user_query = "UPDATE users SET is_premium = TRUE WHERE id = {$_SESSION['user_id']}";
                mysqli_query($conn, $update_user_query);

                // Set session variable
                $_SESSION['is_premium'] = true;

                $payment_status = 'success';
                $payment_message = 'Payment successful! Your premium subscription is now active.';

                // Log the successful payment
                error_log("bKash payment successful for payment ID: $payment_id_for_bkash, Transaction ID: $bkash_transaction_id");
            } else {
                // Update payment status to failed
                $update_query = "UPDATE payments SET payment_status = 'failed' WHERE id = $payment_id_for_bkash";
                mysqli_query($conn, $update_query);

                $payment_status = 'failed';
                $payment_message = 'Payment execution failed. Please try again or choose a different payment method.';

                // Log the error
                error_log("bKash payment execution failed: " . json_encode($execute_response));
            }
        } else {
            // Update payment status to failed
            $update_query = "UPDATE payments SET payment_status = 'failed' WHERE id = $payment_id_for_bkash";
            mysqli_query($conn, $update_query);

            $payment_status = 'failed';
            $payment_message = 'Payment failed. Please try again or choose a different payment method.';
        }
    } else {
        $payment_status = 'failed';
        $payment_message = 'Could not authenticate with bKash. Please try again later.';

        // Log the error
        error_log("bKash token error: " . json_encode($token_response));
    }
}

// For demonstration purposes, also keep the simulation option
if (isset($_POST['simulate_payment'])) {
    $action = $_POST['simulate_payment'];

    if ($action == 'success') {
        // Generate a random bKash transaction ID
        $bkash_transaction_id = 'BKASH' . rand(1000000, 9999999);

        // Update payment status to completed
        $update_query = "UPDATE payments SET payment_status = 'completed', transaction_id = '$bkash_transaction_id' WHERE id = $payment_id";
        mysqli_query($conn, $update_query);

        // Update user to premium
        $update_user_query = "UPDATE users SET is_premium = TRUE WHERE id = {$_SESSION['user_id']}";
        mysqli_query($conn, $update_user_query);

        // Set session variable
        $_SESSION['is_premium'] = true;

        $payment_status = 'success';
        $payment_message = 'Payment successful! Your premium subscription is now active.';

        // Log the successful payment
        error_log("bKash payment successful for payment ID: $payment_id, Transaction ID: $bkash_transaction_id");
    } else {
        // Update payment status to failed
        $update_query = "UPDATE payments SET payment_status = 'failed' WHERE id = $payment_id";
        mysqli_query($conn, $update_query);

        $payment_status = 'failed';
        $payment_message = 'Payment failed. Please try again or choose a different payment method.';
    }
}
?>

<!-- bKash Payment Section -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <?php if ($payment_status == 'success'): ?>
                <div class="card bg-dark">
                    <div class="card-body text-center">
                        <div class="mb-4">
                            <i class="fas fa-check-circle text-success fa-5x"></i>
                        </div>
                        <h3 class="mb-3">Payment Successful!</h3>
                        <p>Your premium subscription is now active.</p>
                        <div class="mt-4">
                            <a href="<?php echo SITE_URL; ?>/premium_content.php" class="btn btn-success btn-lg">View Premium Content</a>
                        </div>
                    </div>
                </div>
                <?php elseif ($payment_status == 'failed'): ?>
                <div class="card bg-dark">
                    <div class="card-body text-center">
                        <div class="mb-4">
                            <i class="fas fa-times-circle text-danger fa-5x"></i>
                        </div>
                        <h3 class="mb-3">Payment Failed</h3>
                        <p>Your payment could not be processed. Please try again or choose a different payment method.</p>
                        <div class="mt-4">
                            <a href="<?php echo SITE_URL; ?>/payment.php?plan=<?php echo $payment['plan_id']; ?>" class="btn btn-danger btn-lg">Try Again</a>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="card bg-dark">
                    <div class="card-header bg-danger text-white">
                        <h3 class="mb-0">bKash Payment</h3>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <img src="<?php echo SITE_URL; ?>/images/bkash.png" alt="bKash" class="img-fluid" style="max-width: 150px;">
                        </div>

                        <div class="mb-4">
                            <h5>Payment Details:</h5>
                            <ul class="list-group list-group-flush bg-dark">
                                <li class="list-group-item bg-dark text-white border-secondary d-flex justify-content-between">
                                    <span>Plan:</span>
                                    <span><?php echo $payment['plan_name']; ?></span>
                                </li>
                                <li class="list-group-item bg-dark text-white border-secondary d-flex justify-content-between">
                                    <span>Amount:</span>
                                    <span>৳<?php echo number_format($payment['price'], 0); ?></span>
                                </li>
                            </ul>
                        </div>

                        <div class="alert alert-info">
                            <p><strong>Note:</strong> You will be redirected to the bKash payment gateway to complete your payment securely.</p>
                        </div>

                        <?php if (!$bkash_token): ?>
                        <!-- bKash Payment Initiation -->
                        <div class="card mb-4">
                            <div class="card-header bg-light text-dark">
                                <h5 class="mb-0">bKash Payment</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="bkashAmount" class="form-label">Amount</label>
                                    <input type="text" class="form-control" id="bkashAmount" value="৳<?php echo number_format($payment['price'], 0); ?>" readonly>
                                </div>

                                <div class="text-center">
                                    <?php
                                    // Get token for creating payment
                                    // Debug the API credentials
                                    error_log("bKash API credentials: URL=$bkash_sandbox_url, APP_KEY=$bkash_app_key, USERNAME=$bkash_username");

                                    $token_response = getBkashToken($bkash_sandbox_url, $bkash_app_key, $bkash_app_secret, $bkash_username, $bkash_password);

                                    if (isset($token_response['id_token'])) {
                                        $bkash_token = $token_response['id_token'];

                                        // Create payment
                                        $invoice_number = 'INV-' . $payment_id . '-' . time();
                                        $create_payment_response = createBkashPayment(
                                            $bkash_sandbox_url,
                                            $bkash_token,
                                            $payment['price'],
                                            $invoice_number,
                                            'sale'
                                        );

                                        if (isset($create_payment_response['paymentID'])) {
                                            $bkash_payment_id = $create_payment_response['paymentID'];
                                            $bkash_create_time = $create_payment_response['createTime'];
                                            $bkash_org_logo = $create_payment_response['orgLogo'];
                                            $bkash_org_name = $create_payment_response['orgName'];

                                            // Store bKash payment ID in session for later use
                                            $_SESSION['bkash_payment_id'] = $bkash_payment_id;
                                            $_SESSION['payment_id'] = $payment_id;

                                            // Update payment record with bKash payment ID
                                            $update_payment_query = "UPDATE payments SET transaction_id = '$bkash_payment_id' WHERE id = $payment_id";
                                            mysqli_query($conn, $update_payment_query);

                                            // Show bKash payment button
                                            echo '<a href="' . $create_payment_response['bkashURL'] . '" class="btn btn-danger btn-lg">Pay with bKash</a>';
                                        } else {
                                            echo '<div class="alert alert-danger">Failed to create bKash payment. Please try again later.</div>';
                                            error_log("bKash create payment error: " . json_encode($create_payment_response));
                                        }
                                    } else {
                                        echo '<div class="alert alert-danger">Could not connect to bKash. Please try again later.</div>';
                                        error_log("bKash token error: " . json_encode($token_response));
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- For demonstration purposes, also keep the simulation option -->
                        <div class="card mb-4">
                            <div class="card-header bg-light text-dark">
                                <h5 class="mb-0">Simulation Mode (For Testing)</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <p><strong>Alternative:</strong> If the bKash API integration doesn't work, you can use this simulation mode for testing purposes.</p>
                                    <p>The real bKash integration might require proper SSL configuration which might not be available in your local environment.</p>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <form method="POST" action="">
                                        <button type="submit" name="simulate_payment" value="failed" class="btn btn-outline-danger">Simulate Failed Payment</button>
                                    </form>
                                    <form method="POST" action="">
                                        <button type="submit" name="simulate_payment" value="success" class="btn btn-success">Simulate Successful Payment</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                            </div>
                        </div>

                        <div class="text-center">
                            <a href="<?php echo SITE_URL; ?>/payment.php?plan=<?php echo $payment['plan_id']; ?>" class="btn btn-secondary">Choose Another Payment Method</a>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<?php require_once 'includes/footer.php'; ?>
