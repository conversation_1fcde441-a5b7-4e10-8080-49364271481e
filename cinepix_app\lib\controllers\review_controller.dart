import 'package:get/get.dart';
import 'package:cinepix_app/models/review.dart';
import 'package:cinepix_app/services/api_service.dart';
import 'package:cinepix_app/controllers/auth_controller.dart';

class ReviewController extends GetxController {
  final ApiService _apiService = ApiService();
  final AuthController _authController = Get.find<AuthController>();
  
  final RxList<Review> reviews = <Review>[].obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxBool isSubmitting = false.obs;
  
  // Load reviews for a specific content
  Future<void> loadReviews(String contentType, int contentId) async {
    isLoading.value = true;
    errorMessage.value = '';
    
    try {
      final data = await _apiService.getReviews(contentType, contentId);
      final List<Review> loadedReviews = (data)
          .map((reviewJson) => Review.fromJson(reviewJson))
          .toList();
      
      reviews.value = loadedReviews;
    } catch (e) {
      errorMessage.value = e.toString();
      reviews.value = [];
    } finally {
      isLoading.value = false;
    }
  }
  
  // Submit a new review
  Future<bool> submitReview(String contentType, int contentId, int rating, String comment) async {
    if (!_authController.isLoggedIn.value) {
      errorMessage.value = 'You must be logged in to submit a review';
      return false;
    }
    
    isSubmitting.value = true;
    errorMessage.value = '';
    
    try {
      await _apiService.submitReview(contentType, contentId, rating, comment);
      
      // Reload reviews to show the new one
      await loadReviews(contentType, contentId);
      
      return true;
    } catch (e) {
      errorMessage.value = e.toString();
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }
  
  // Clear reviews when no longer needed
  void clearReviews() {
    reviews.clear();
    errorMessage.value = '';
  }
}
