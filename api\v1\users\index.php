<?php
// API Users Endpoints

// Get the request
global $request;

// Check if user is authenticated
if (!is_authenticated()) {
    api_error('Authentication required', 401);
}

// Get authenticated user
$current_user = get_authenticated_user();

// Handle different endpoints based on the path parts
$action = $request['parts'][0] ?? 'profile';

switch ($action) {
    case 'profile':
        if ($request['method'] === 'GET') {
            handle_get_profile($current_user);
        } elseif ($request['method'] === 'PUT') {
            handle_update_profile($current_user, $request);
        } else {
            api_error('Method not allowed', 405);
        }
        break;

    case 'change-password':
        if ($request['method'] === 'POST') {
            handle_change_password($current_user, $request);
        } else {
            api_error('Method not allowed', 405);
        }
        break;

    case 'upload-profile-image':
        if ($request['method'] === 'POST') {
            handle_upload_profile_image($current_user, $request);
        } else {
            api_error('Method not allowed', 405);
        }
        break;

    case 'watchlist':
        if ($request['method'] === 'GET') {
            handle_get_watchlist($current_user, $request);
        } elseif ($request['method'] === 'POST') {
            handle_add_to_watchlist($current_user, $request);
        } elseif ($request['method'] === 'DELETE' && isset($request['parts'][1]) && is_numeric($request['parts'][1])) {
            handle_remove_from_watchlist($current_user, $request['parts'][1]);
        } else {
            api_error('Invalid request', 400);
        }
        break;

    case 'favorites':
        if ($request['method'] === 'GET') {
            handle_get_favorites($current_user, $request);
        } elseif ($request['method'] === 'POST') {
            handle_add_to_favorites($current_user, $request);
        } elseif ($request['method'] === 'DELETE' && isset($request['parts'][1]) && is_numeric($request['parts'][1])) {
            handle_remove_from_favorites($current_user, $request['parts'][1]);
        } else {
            api_error('Invalid request', 400);
        }
        break;

    case 'reviews':
        handle_get_reviews($current_user, $request);
        break;

    case 'subscriptions':
        handle_get_subscriptions($current_user, $request);
        break;

    case 'payments':
        handle_get_payments($current_user, $request);
        break;

    case 'notifications':
        if ($request['method'] === 'GET') {
            handle_get_notifications($current_user, $request);
        } elseif ($request['method'] === 'PUT') {
            handle_update_notification_settings($current_user, $request);
        } else {
            api_error('Method not allowed', 405);
        }
        break;

    default:
        api_error('Invalid users endpoint', 404);
}

/**
 * Handle getting user profile
 */
function handle_get_profile($user) {
    global $conn;

    // Get user details from database
    $query = "SELECT id, username, email, name, profile_image, role, is_premium, premium_expires, created_at, last_login
              FROM users WHERE id = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $user['user_id']);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if (mysqli_num_rows($result) === 0) {
        api_error('User not found', 404);
    }

    $user_data = mysqli_fetch_assoc($result);

    // Format response with Bengali translations
    $response = [
        'id' => $user_data['id'],
        'username' => $user_data['username'],
        'email' => $user_data['email'],
        'name' => $user_data['name'] ?? '',
        'profile_image' => $user_data['profile_image'],
        'role' => $user_data['role'],
        'is_premium' => (bool)$user_data['is_premium'],
        'premium_expires' => $user_data['premium_expires'],
        'created_at' => $user_data['created_at'],
        'last_login' => $user_data['last_login'] ?? null,
        'translations' => [
            'bn' => [
                'role' => $user_data['role'] === 'admin' ? 'অ্যাডমিন' : 'ব্যবহারকারী',
                'premium_status' => (bool)$user_data['is_premium'] ? 'প্রিমিয়াম সদস্য' : 'সাধারণ সদস্য',
                'created_at_label' => 'যোগদানের তারিখ',
                'last_login_label' => 'সর্বশেষ লগইন'
            ]
        ]
    ];

    api_response($response);
}

/**
 * Handle updating user profile
 */
function handle_update_profile($user, $request) {
    global $conn;

    // Validate request body
    if (empty($request['body'])) {
        api_error('No data provided', 400);
    }

    // Fields that can be updated
    $allowed_fields = ['name', 'email', 'password', 'profile_image'];
    $update_fields = [];
    $params = [];
    $types = '';

    // Build update query
    foreach ($allowed_fields as $field) {
        if (isset($request['body'][$field])) {
            // Special handling for password
            if ($field === 'password') {
                if (strlen($request['body'][$field]) < 6) {
                    api_error('Password must be at least 6 characters', 400);
                }
                $update_fields[] = "$field = ?";
                $params[] = password_hash($request['body'][$field], PASSWORD_DEFAULT);
                $types .= 's';
            }
            // Special handling for email
            elseif ($field === 'email') {
                if (!filter_var($request['body'][$field], FILTER_VALIDATE_EMAIL)) {
                    api_error('Invalid email format', 400);
                }

                // Check if email is already in use by another user
                $check_query = "SELECT id FROM users WHERE email = ? AND id != ?";
                $check_stmt = mysqli_prepare($conn, $check_query);
                mysqli_stmt_bind_param($check_stmt, 'si', $request['body'][$field], $user['user_id']);
                mysqli_stmt_execute($check_stmt);
                $check_result = mysqli_stmt_get_result($check_stmt);

                if (mysqli_num_rows($check_result) > 0) {
                    api_error('Email already in use', 409);
                }

                $update_fields[] = "$field = ?";
                $params[] = $request['body'][$field];
                $types .= 's';
            }
            // Handle other fields
            else {
                $update_fields[] = "$field = ?";
                $params[] = $request['body'][$field];
                $types .= 's';
            }
        }
    }

    // If no valid fields to update
    if (empty($update_fields)) {
        api_error('No valid fields to update', 400);
    }

    // Add user ID to params
    $params[] = $user['user_id'];
    $types .= 'i';

    // Update user
    $query = "UPDATE users SET " . implode(', ', $update_fields) . " WHERE id = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, $types, ...$params);

    if (!mysqli_stmt_execute($stmt)) {
        api_error('Failed to update profile: ' . mysqli_error($conn), 500);
    }

    // Get updated user data
    $get_query = "SELECT id, username, email, name, profile_image, role, is_premium, premium_expires, created_at
                  FROM users WHERE id = ?";
    $get_stmt = mysqli_prepare($conn, $get_query);
    mysqli_stmt_bind_param($get_stmt, 'i', $user['user_id']);
    mysqli_stmt_execute($get_stmt);
    $result = mysqli_stmt_get_result($get_stmt);
    $user_data = mysqli_fetch_assoc($result);

    // Format response
    $response = [
        'success' => true,
        'message' => 'Profile updated successfully',
        'user' => [
            'id' => $user_data['id'],
            'username' => $user_data['username'],
            'email' => $user_data['email'],
            'name' => $user_data['name'] ?? '',
            'profile_image' => $user_data['profile_image'],
            'role' => $user_data['role'],
            'is_premium' => (bool)$user_data['is_premium'],
            'premium_expires' => $user_data['premium_expires'],
            'created_at' => $user_data['created_at']
        ]
    ];

    api_response($response);
}

/**
 * Handle getting user's watchlist
 */
function handle_get_watchlist($user, $request) {
    global $conn;

    // Get pagination parameters
    $page = isset($request['params']['page']) ? (int)$request['params']['page'] : 1;
    $limit = isset($request['params']['limit']) ? (int)$request['params']['limit'] : 10;
    $offset = ($page - 1) * $limit;

    // Get content type filter
    $content_type = isset($request['params']['type']) ? $request['params']['type'] : null;

    // Build query
    $query = "SELECT w.id, w.content_type, w.content_id, w.added_at";
    $count_query = "SELECT COUNT(*) as total";

    $from_clause = " FROM watchlist w WHERE w.user_id = ?";

    // Add content type filter if provided
    $params = [$user['user_id']];
    $types = 'i';

    if ($content_type) {
        if ($content_type !== 'movie' && $content_type !== 'tvshow') {
            api_error('Invalid content type', 400);
        }

        $from_clause .= " AND w.content_type = ?";
        $params[] = $content_type;
        $types .= 's';
    }

    // Add pagination
    $query .= $from_clause . " ORDER BY w.added_at DESC LIMIT ? OFFSET ?";
    $count_query .= $from_clause;

    $params[] = $limit;
    $params[] = $offset;
    $types .= 'ii';

    // Get total count
    $count_stmt = mysqli_prepare($conn, $count_query);
    mysqli_stmt_bind_param($count_stmt, substr($types, 0, -2), ...array_slice($params, 0, -2));
    mysqli_stmt_execute($count_stmt);
    $count_result = mysqli_stmt_get_result($count_stmt);
    $total = mysqli_fetch_assoc($count_result)['total'];

    // Get watchlist items
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, $types, ...$params);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    $items = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $item = [
            'id' => $row['id'],
            'content_type' => $row['content_type'],
            'content_id' => $row['content_id'],
            'added_at' => $row['added_at']
        ];

        // Get content details
        if ($row['content_type'] === 'movie') {
            $movie_query = "SELECT title, release_year, poster FROM movies WHERE id = ?";
            $movie_stmt = mysqli_prepare($conn, $movie_query);
            mysqli_stmt_bind_param($movie_stmt, 'i', $row['content_id']);
            mysqli_stmt_execute($movie_stmt);
            $movie_result = mysqli_stmt_get_result($movie_stmt);

            if ($movie_data = mysqli_fetch_assoc($movie_result)) {
                $item['title'] = $movie_data['title'];
                $item['year'] = $movie_data['release_year'];
                $item['poster'] = $movie_data['poster'];
            }
        } else {
            $tvshow_query = "SELECT title, start_year, poster FROM tvshows WHERE id = ?";
            $tvshow_stmt = mysqli_prepare($conn, $tvshow_query);
            mysqli_stmt_bind_param($tvshow_stmt, 'i', $row['content_id']);
            mysqli_stmt_execute($tvshow_stmt);
            $tvshow_result = mysqli_stmt_get_result($tvshow_stmt);

            if ($tvshow_data = mysqli_fetch_assoc($tvshow_result)) {
                $item['title'] = $tvshow_data['title'];
                $item['year'] = $tvshow_data['start_year'];
                $item['poster'] = $tvshow_data['poster'];
            }
        }

        $items[] = $item;
    }

    // Format response
    $response = [
        'items' => $items,
        'pagination' => [
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ]
    ];

    api_response($response);
}

/**
 * Handle adding item to watchlist
 */
function handle_add_to_watchlist($user, $request) {
    global $conn;

    // Validate request body
    if (empty($request['body']['content_type']) || empty($request['body']['content_id'])) {
        api_error('Content type and content ID are required', 400);
    }

    $content_type = $request['body']['content_type'];
    $content_id = (int)$request['body']['content_id'];

    // Validate content type
    if ($content_type !== 'movie' && $content_type !== 'tvshow') {
        api_error('Invalid content type', 400);
    }

    // Check if content exists
    if ($content_type === 'movie') {
        $check_query = "SELECT id FROM movies WHERE id = ?";
    } else {
        $check_query = "SELECT id FROM tvshows WHERE id = ?";
    }

    $check_stmt = mysqli_prepare($conn, $check_query);
    mysqli_stmt_bind_param($check_stmt, 'i', $content_id);
    mysqli_stmt_execute($check_stmt);
    $check_result = mysqli_stmt_get_result($check_stmt);

    if (mysqli_num_rows($check_result) === 0) {
        api_error('Content not found', 404);
    }

    // Check if already in watchlist
    $exists_query = "SELECT id FROM watchlist WHERE user_id = ? AND content_type = ? AND content_id = ?";
    $exists_stmt = mysqli_prepare($conn, $exists_query);
    mysqli_stmt_bind_param($exists_stmt, 'isi', $user['user_id'], $content_type, $content_id);
    mysqli_stmt_execute($exists_stmt);
    $exists_result = mysqli_stmt_get_result($exists_stmt);

    if (mysqli_num_rows($exists_result) > 0) {
        // Already in watchlist, return success
        api_response([
            'success' => true,
            'message' => 'Item already in watchlist',
            'watchlist_id' => mysqli_fetch_assoc($exists_result)['id']
        ]);
    }

    // Add to watchlist
    $insert_query = "INSERT INTO watchlist (user_id, content_type, content_id) VALUES (?, ?, ?)";
    $insert_stmt = mysqli_prepare($conn, $insert_query);
    mysqli_stmt_bind_param($insert_stmt, 'isi', $user['user_id'], $content_type, $content_id);

    if (!mysqli_stmt_execute($insert_stmt)) {
        api_error('Failed to add to watchlist: ' . mysqli_error($conn), 500);
    }

    $watchlist_id = mysqli_insert_id($conn);

    // Return success response
    api_response([
        'success' => true,
        'message' => 'Added to watchlist',
        'watchlist_id' => $watchlist_id
    ], 201);
}

/**
 * Handle removing item from watchlist
 */
function handle_remove_from_watchlist($user, $watchlist_id) {
    global $conn;

    // Check if watchlist item exists and belongs to user
    $check_query = "SELECT id FROM watchlist WHERE id = ? AND user_id = ?";
    $check_stmt = mysqli_prepare($conn, $check_query);
    mysqli_stmt_bind_param($check_stmt, 'ii', $watchlist_id, $user['user_id']);
    mysqli_stmt_execute($check_stmt);
    $check_result = mysqli_stmt_get_result($check_stmt);

    if (mysqli_num_rows($check_result) === 0) {
        api_error('Watchlist item not found', 404);
    }

    // Remove from watchlist
    $delete_query = "DELETE FROM watchlist WHERE id = ?";
    $delete_stmt = mysqli_prepare($conn, $delete_query);
    mysqli_stmt_bind_param($delete_stmt, 'i', $watchlist_id);

    if (!mysqli_stmt_execute($delete_stmt)) {
        api_error('Failed to remove from watchlist: ' . mysqli_error($conn), 500);
    }

    // Return success response
    api_response([
        'success' => true,
        'message' => 'Removed from watchlist'
    ]);
}

/**
 * Handle getting user's reviews
 */
function handle_get_reviews($user, $request) {
    global $conn;

    // Get pagination parameters
    $page = isset($request['params']['page']) ? (int)$request['params']['page'] : 1;
    $limit = isset($request['params']['limit']) ? (int)$request['params']['limit'] : 10;
    $offset = ($page - 1) * $limit;

    // Get content type filter
    $content_type = isset($request['params']['type']) ? $request['params']['type'] : null;

    // Build query
    $query = "SELECT r.id, r.content_type, r.content_id, r.rating, r.comment, r.created_at";
    $count_query = "SELECT COUNT(*) as total";

    $from_clause = " FROM reviews r WHERE r.user_id = ?";

    // Add content type filter if provided
    $params = [$user['user_id']];
    $types = 'i';

    if ($content_type) {
        if ($content_type !== 'movie' && $content_type !== 'tvshow') {
            api_error('Invalid content type', 400);
        }

        $from_clause .= " AND r.content_type = ?";
        $params[] = $content_type;
        $types .= 's';
    }

    // Add pagination
    $query .= $from_clause . " ORDER BY r.created_at DESC LIMIT ? OFFSET ?";
    $count_query .= $from_clause;

    $params[] = $limit;
    $params[] = $offset;
    $types .= 'ii';

    // Get total count
    $count_stmt = mysqli_prepare($conn, $count_query);
    mysqli_stmt_bind_param($count_stmt, substr($types, 0, -2), ...array_slice($params, 0, -2));
    mysqli_stmt_execute($count_stmt);
    $count_result = mysqli_stmt_get_result($count_stmt);
    $total = mysqli_fetch_assoc($count_result)['total'];

    // Get reviews
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, $types, ...$params);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    $reviews = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $review = [
            'id' => $row['id'],
            'content_type' => $row['content_type'],
            'content_id' => $row['content_id'],
            'rating' => (int)$row['rating'],
            'comment' => $row['comment'],
            'created_at' => $row['created_at']
        ];

        // Get content details
        if ($row['content_type'] === 'movie') {
            $movie_query = "SELECT title, release_year, poster FROM movies WHERE id = ?";
            $movie_stmt = mysqli_prepare($conn, $movie_query);
            mysqli_stmt_bind_param($movie_stmt, 'i', $row['content_id']);
            mysqli_stmt_execute($movie_stmt);
            $movie_result = mysqli_stmt_get_result($movie_stmt);

            if ($movie_data = mysqli_fetch_assoc($movie_result)) {
                $review['title'] = $movie_data['title'];
                $review['year'] = $movie_data['release_year'];
                $review['poster'] = $movie_data['poster'];
            }
        } else {
            $tvshow_query = "SELECT title, start_year, poster FROM tvshows WHERE id = ?";
            $tvshow_stmt = mysqli_prepare($conn, $tvshow_query);
            mysqli_stmt_bind_param($tvshow_stmt, 'i', $row['content_id']);
            mysqli_stmt_execute($tvshow_stmt);
            $tvshow_result = mysqli_stmt_get_result($tvshow_stmt);

            if ($tvshow_data = mysqli_fetch_assoc($tvshow_result)) {
                $review['title'] = $tvshow_data['title'];
                $review['year'] = $tvshow_data['start_year'];
                $review['poster'] = $tvshow_data['poster'];
            }
        }

        $reviews[] = $review;
    }

    // Format response
    $response = [
        'reviews' => $reviews,
        'pagination' => [
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ]
    ];

    api_response($response);
}

/**
 * Handle getting user's subscriptions
 */
function handle_get_subscriptions($user, $request) {
    global $conn;

    // Get pagination parameters
    $page = isset($request['params']['page']) ? (int)$request['params']['page'] : 1;
    $limit = isset($request['params']['limit']) ? (int)$request['params']['limit'] : 10;
    $offset = ($page - 1) * $limit;

    // Build query
    $query = "SELECT s.id, s.plan_id, s.start_date, s.end_date, s.status, s.created_at,
                     p.name as plan_name, p.price as plan_price, p.duration as plan_duration
              FROM subscriptions s
              JOIN premium_plans p ON s.plan_id = p.id
              WHERE s.user_id = ?
              ORDER BY s.created_at DESC
              LIMIT ? OFFSET ?";

    $count_query = "SELECT COUNT(*) as total FROM subscriptions WHERE user_id = ?";

    // Get total count
    $count_stmt = mysqli_prepare($conn, $count_query);
    mysqli_stmt_bind_param($count_stmt, 'i', $user['user_id']);
    mysqli_stmt_execute($count_stmt);
    $count_result = mysqli_stmt_get_result($count_stmt);
    $total = mysqli_fetch_assoc($count_result)['total'];

    // Get subscriptions
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'iii', $user['user_id'], $limit, $offset);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    $subscriptions = [];

    while ($row = mysqli_fetch_assoc($result)) {
        // Get payment status
        $payment_query = "SELECT payment_status FROM payments WHERE subscription_id = ? ORDER BY created_at DESC LIMIT 1";
        $payment_stmt = mysqli_prepare($conn, $payment_query);
        mysqli_stmt_bind_param($payment_stmt, 'i', $row['id']);
        mysqli_stmt_execute($payment_stmt);
        $payment_result = mysqli_stmt_get_result($payment_stmt);
        $payment_status = mysqli_num_rows($payment_result) > 0 ? mysqli_fetch_assoc($payment_result)['payment_status'] : 'pending';

        $subscriptions[] = [
            'id' => $row['id'],
            'plan' => [
                'id' => $row['plan_id'],
                'name' => $row['plan_name'],
                'price' => $row['plan_price'],
                'duration' => $row['plan_duration']
            ],
            'start_date' => $row['start_date'],
            'end_date' => $row['end_date'],
            'status' => $row['status'],
            'payment_status' => $payment_status,
            'created_at' => $row['created_at']
        ];
    }

    // Format response
    $response = [
        'subscriptions' => $subscriptions,
        'pagination' => [
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ]
    ];

    api_response($response);
}

/**
 * Handle getting user's payments
 */
function handle_get_payments($user, $request) {
    global $conn;

    // Get pagination parameters
    $page = isset($request['params']['page']) ? (int)$request['params']['page'] : 1;
    $limit = isset($request['params']['limit']) ? (int)$request['params']['limit'] : 10;
    $offset = ($page - 1) * $limit;

    // Build query
    $query = "SELECT p.id, p.subscription_id, p.amount, p.payment_method, p.transaction_id,
                     p.payment_status, p.payment_date, p.notes, p.created_at,
                     pp.name as plan_name
              FROM payments p
              JOIN subscriptions s ON p.subscription_id = s.id
              JOIN premium_plans pp ON s.plan_id = pp.id
              WHERE p.user_id = ?
              ORDER BY p.created_at DESC
              LIMIT ? OFFSET ?";

    $count_query = "SELECT COUNT(*) as total FROM payments WHERE user_id = ?";

    // Get total count
    $count_stmt = mysqli_prepare($conn, $count_query);
    mysqli_stmt_bind_param($count_stmt, 'i', $user['user_id']);
    mysqli_stmt_execute($count_stmt);
    $count_result = mysqli_stmt_get_result($count_stmt);
    $total = mysqli_fetch_assoc($count_result)['total'];

    // Get payments
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'iii', $user['user_id'], $limit, $offset);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    $payments = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $payments[] = [
            'id' => $row['id'],
            'subscription_id' => $row['subscription_id'],
            'plan_name' => $row['plan_name'],
            'amount' => $row['amount'],
            'payment_method' => $row['payment_method'],
            'transaction_id' => $row['transaction_id'],
            'payment_status' => $row['payment_status'],
            'payment_date' => $row['payment_date'],
            'notes' => $row['notes'],
            'created_at' => $row['created_at']
        ];
    }

    // Format response
    $response = [
        'payments' => $payments,
        'pagination' => [
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ]
    ];

    api_response($response);
}

/**
 * Handle changing user password
 */
function handle_change_password($user, $request) {
    global $conn;

    // Validate request body
    if (empty($request['body']['current_password']) || empty($request['body']['new_password'])) {
        api_error('Current password and new password are required', 400);
    }

    $current_password = $request['body']['current_password'];
    $new_password = $request['body']['new_password'];

    // Validate new password
    if (strlen($new_password) < 6) {
        api_error('New password must be at least 6 characters', 400);
    }

    // Get user's current password
    $query = "SELECT password FROM users WHERE id = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $user['user_id']);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if (mysqli_num_rows($result) === 0) {
        api_error('User not found', 404);
    }

    $user_data = mysqli_fetch_assoc($result);

    // Verify current password
    if (!password_verify($current_password, $user_data['password'])) {
        api_error('Current password is incorrect', 401);
    }

    // Hash new password
    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);

    // Update password
    $update_query = "UPDATE users SET password = ? WHERE id = ?";
    $update_stmt = mysqli_prepare($conn, $update_query);
    mysqli_stmt_bind_param($update_stmt, 'si', $hashed_password, $user['user_id']);

    if (!mysqli_stmt_execute($update_stmt)) {
        api_error('Failed to update password: ' . mysqli_error($conn), 500);
    }

    // Return success response with Bengali translation
    api_response([
        'success' => true,
        'message' => 'Password updated successfully',
        'translations' => [
            'bn' => [
                'message' => 'পাসওয়ার্ড সফলভাবে আপডেট করা হয়েছে'
            ]
        ]
    ]);
}

/**
 * Handle uploading profile image
 */
function handle_upload_profile_image($user, $request) {
    global $conn;

    // Check if image data is provided
    if (empty($request['body']['image_data'])) {
        api_error('Image data is required', 400);
    }

    $image_data = $request['body']['image_data'];

    // Validate base64 image data
    if (!preg_match('/^data:image\/(jpeg|jpg|png|gif);base64,/', $image_data)) {
        api_error('Invalid image format. Only JPEG, PNG and GIF are supported', 400);
    }

    // Extract image data from base64 string
    $image_parts = explode(';base64,', $image_data);
    $image_type_aux = explode('image/', $image_parts[0]);
    $image_type = $image_type_aux[1];
    $image_base64 = base64_decode($image_parts[1]);

    // Generate unique filename
    $filename = 'user_' . $user['user_id'] . '_' . time() . '.' . $image_type;
    $upload_dir = '../../../uploads/profile_images/';

    // Create directory if it doesn't exist
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }

    $file_path = $upload_dir . $filename;

    // Save image file
    if (!file_put_contents($file_path, $image_base64)) {
        api_error('Failed to save image', 500);
    }

    // Update user profile image in database
    $relative_path = 'uploads/profile_images/' . $filename;
    $update_query = "UPDATE users SET profile_image = ? WHERE id = ?";
    $update_stmt = mysqli_prepare($conn, $update_query);
    mysqli_stmt_bind_param($update_stmt, 'si', $relative_path, $user['user_id']);

    if (!mysqli_stmt_execute($update_stmt)) {
        // Delete uploaded file if database update fails
        unlink($file_path);
        api_error('Failed to update profile image: ' . mysqli_error($conn), 500);
    }

    // Return success response with Bengali translation
    api_response([
        'success' => true,
        'message' => 'Profile image updated successfully',
        'profile_image' => $relative_path,
        'translations' => [
            'bn' => [
                'message' => 'প্রোফাইল ছবি সফলভাবে আপডেট করা হয়েছে'
            ]
        ]
    ]);
}