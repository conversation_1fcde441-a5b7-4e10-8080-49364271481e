<?php
// Set page title
$page_title = 'Reviews';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Process form submissions
$success_message = '';
$error_message = '';

// Function to update content rating
function updateContentRating($content_type, $content_id, $conn) {
    // Calculate average rating
    $rating_query = "SELECT AVG(rating) as avg_rating FROM reviews
                    WHERE content_type = '$content_type' AND content_id = $content_id";
    $rating_result = mysqli_query($conn, $rating_query);
    $avg_rating = mysqli_fetch_assoc($rating_result)['avg_rating'];

    // Update content rating
    if ($content_type == 'movie') {
        $update_query = "UPDATE movies SET rating = $avg_rating WHERE id = $content_id";
    } else {
        $update_query = "UPDATE tvshows SET rating = $avg_rating WHERE id = $content_id";
    }

    mysqli_query($conn, $update_query);
}

// Update Review
if (isset($_POST['update_review'])) {
    $review_id = (int)$_POST['review_id'];
    $rating = (int)$_POST['rating'];
    $comment = sanitize($_POST['comment']);

    if ($rating < 1 || $rating > 10) {
        $error_message = 'Rating must be between 1 and 10.';
    } else {
        // Get content type and content id before updating
        $get_review_query = "SELECT content_type, content_id FROM reviews WHERE id = $review_id";
        $review_result = mysqli_query($conn, $get_review_query);
        $review_data = mysqli_fetch_assoc($review_result);

        // Update review
        $update_query = "UPDATE reviews SET
                        rating = $rating,
                        comment = '$comment'
                        WHERE id = $review_id";

        if (mysqli_query($conn, $update_query)) {
            // Update content rating
            updateContentRating($review_data['content_type'], $review_data['content_id'], $conn);
            $success_message = 'Review updated successfully.';
        } else {
            $error_message = 'Error updating review: ' . mysqli_error($conn);
        }
    }
}

// Delete Review
if (isset($_GET['delete']) && $_GET['delete'] > 0) {
    $review_id = (int)$_GET['delete'];

    // Get content type and content id before deleting
    $get_review_query = "SELECT content_type, content_id FROM reviews WHERE id = $review_id";
    $review_result = mysqli_query($conn, $get_review_query);
    $review_data = mysqli_fetch_assoc($review_result);

    // Delete review
    $delete_query = "DELETE FROM reviews WHERE id = $review_id";

    if (mysqli_query($conn, $delete_query)) {
        // Update content rating after deletion
        updateContentRating($review_data['content_type'], $review_data['content_id'], $conn);
        $success_message = 'Review deleted successfully.';
    } else {
        $error_message = 'Error deleting review: ' . mysqli_error($conn);
    }
}

// Get review to edit
$edit_review = null;
if (isset($_GET['edit']) && $_GET['edit'] > 0) {
    $review_id = (int)$_GET['edit'];

    $edit_query = "SELECT r.*, u.username,
                  CASE
                      WHEN r.content_type = 'movie' THEN m.title
                      ELSE t.title
                  END as content_title
                  FROM reviews r
                  JOIN users u ON r.user_id = u.id
                  LEFT JOIN movies m ON r.content_type = 'movie' AND r.content_id = m.id
                  LEFT JOIN tvshows t ON r.content_type = 'tvshow' AND r.content_id = t.id
                  WHERE r.id = $review_id";
    $edit_result = mysqli_query($conn, $edit_query);

    if (mysqli_num_rows($edit_result) > 0) {
        $edit_review = mysqli_fetch_assoc($edit_result);
    }
}

// Filter parameters
$filter_content_type = isset($_GET['content_type']) ? sanitize($_GET['content_type']) : '';
$filter_rating = isset($_GET['rating']) ? (int)$_GET['rating'] : 0;
$search_term = isset($_GET['search']) ? sanitize($_GET['search']) : '';

// Build query with filters
$reviews_query = "SELECT r.*, u.username,
                CASE
                    WHEN r.content_type = 'movie' THEN m.title
                    ELSE t.title
                END as content_title
                FROM reviews r
                JOIN users u ON r.user_id = u.id
                LEFT JOIN movies m ON r.content_type = 'movie' AND r.content_id = m.id
                LEFT JOIN tvshows t ON r.content_type = 'tvshow' AND r.content_id = t.id
                WHERE 1=1";

if (!empty($filter_content_type)) {
    $reviews_query .= " AND r.content_type = '$filter_content_type'";
}

if ($filter_rating > 0) {
    $reviews_query .= " AND r.rating = $filter_rating";
}

if (!empty($search_term)) {
    $reviews_query .= " AND (u.username LIKE '%$search_term%'
                      OR m.title LIKE '%$search_term%'
                      OR t.title LIKE '%$search_term%'
                      OR r.comment LIKE '%$search_term%')";
}

$reviews_query .= " ORDER BY r.created_at DESC";
$reviews_result = mysqli_query($conn, $reviews_query);

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Reviews</h1>
            </div>
            <div class="topbar-actions">
                <div class="topbar-search">
                    <form action="reviews.php" method="GET">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="Search reviews..." name="search" value="<?php echo $search_term; ?>">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <?php if($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <!-- Filter Section -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Filter Reviews</h5>
            </div>
            <div class="card-body">
                <form method="GET" action="" class="row g-3">
                    <div class="col-md-4">
                        <label for="content_type" class="form-label">Content Type</label>
                        <select class="form-select" id="content_type" name="content_type">
                            <option value="">All</option>
                            <option value="movie" <?php echo $filter_content_type == 'movie' ? 'selected' : ''; ?>>Movies</option>
                            <option value="tvshow" <?php echo $filter_content_type == 'tvshow' ? 'selected' : ''; ?>>TV Shows</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="rating" class="form-label">Rating</label>
                        <select class="form-select" id="rating" name="rating">
                            <option value="0">All</option>
                            <?php for($i = 1; $i <= 10; $i++): ?>
                            <option value="<?php echo $i; ?>" <?php echo $filter_rating == $i ? 'selected' : ''; ?>><?php echo $i; ?>/10</option>
                            <?php endfor; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" value="<?php echo $search_term; ?>" placeholder="Search by user, title, or comment">
                    </div>
                    <div class="col-12 text-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter me-2"></i>Apply Filters
                        </button>
                        <a href="reviews.php" class="btn btn-secondary">
                            <i class="fas fa-undo me-2"></i>Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <div class="row">
            <?php if($edit_review): ?>
            <!-- Edit Review Form -->
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Edit Review</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="" class="needs-validation" novalidate>
                            <input type="hidden" name="review_id" value="<?php echo $edit_review['id']; ?>">

                            <div class="mb-3">
                                <label class="form-label">User</label>
                                <input type="text" class="form-control" value="<?php echo $edit_review['username']; ?>" disabled>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Content</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" value="<?php echo $edit_review['content_title']; ?>" disabled>
                                    <span class="input-group-text">
                                        <?php echo $edit_review['content_type'] == 'movie' ? 'Movie' : 'TV Show'; ?>
                                    </span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="rating" class="form-label">Rating <span class="text-danger">*</span></label>
                                <select class="form-select" id="rating" name="rating" required>
                                    <?php for($i = 1; $i <= 10; $i++): ?>
                                    <option value="<?php echo $i; ?>" <?php echo $edit_review['rating'] == $i ? 'selected' : ''; ?>><?php echo $i; ?>/10</option>
                                    <?php endfor; ?>
                                </select>
                                <div class="invalid-feedback">
                                    Please select a rating.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="comment" class="form-label">Comment</label>
                                <textarea class="form-control" id="comment" name="comment" rows="4"><?php echo $edit_review['comment']; ?></textarea>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" name="update_review" class="btn btn-primary">Update Review</button>
                                <a href="reviews.php" class="btn btn-secondary">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Reviews List -->
            <div class="col-lg-<?php echo $edit_review ? '8' : '12'; ?> mb-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">All Reviews</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover datatable">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>User</th>
                                        <th>Content</th>
                                        <th>Rating</th>
                                        <th>Comment</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if(mysqli_num_rows($reviews_result) > 0): ?>
                                        <?php while($review = mysqli_fetch_assoc($reviews_result)): ?>
                                        <tr>
                                            <td><?php echo $review['id']; ?></td>
                                            <td><?php echo $review['username']; ?></td>
                                            <td>
                                                <?php echo $review['content_title']; ?>
                                                <span class="badge <?php echo $review['content_type'] == 'movie' ? 'bg-primary' : 'bg-danger'; ?>">
                                                    <?php echo $review['content_type'] == 'movie' ? 'Movie' : 'TV Show'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-star text-warning me-1"></i>
                                                    <span class="fw-bold"><?php echo $review['rating']; ?>/10</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="text-truncate" style="max-width: 200px;" data-bs-toggle="tooltip" title="<?php echo htmlspecialchars($review['comment']); ?>">
                                                    <?php echo $review['comment']; ?>
                                                </div>
                                            </td>
                                            <td><?php echo date('M d, Y', strtotime($review['created_at'])); ?></td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="reviews.php?edit=<?php echo $review['id']; ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="reviews.php?delete=<?php echo $review['id']; ?>" class="btn btn-sm btn-danger btn-delete" data-bs-toggle="tooltip" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="7" class="text-center">No reviews found.</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
