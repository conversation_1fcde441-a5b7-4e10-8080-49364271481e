<?php
// API Index File - Handles routing to appropriate endpoints

// Include API configuration
require_once 'config.php';

// Get request method and URI
$request_method = $_SERVER['REQUEST_METHOD'];
$request_uri = $_SERVER['REQUEST_URI'];

// Extract the path from the URI
$uri_parts = parse_url($request_uri);
$path = $uri_parts['path'];

// Remove base path to get the API endpoint
$base_path = '/moviesite/api/';
$endpoint = substr($path, strlen($base_path));

// Parse query parameters
$query_params = [];
if (isset($uri_parts['query'])) {
    parse_str($uri_parts['query'], $query_params);
}

// Parse JSON body for POST, PUT requests
$body = [];
if ($request_method === 'POST' || $request_method === 'PUT') {
    $input = file_get_contents('php://input');
    if (!empty($input)) {
        $body = json_decode($input, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            api_error('Invalid JSON in request body', 400);
        }
    }
}

// Route the request to the appropriate endpoint
$endpoint_parts = explode('/', $endpoint);

if (count($endpoint_parts) < 2) {
    api_error('Invalid API endpoint', 404);
}

$version = $endpoint_parts[0];
$resource = $endpoint_parts[1];

// Check API version
if ($version !== API_VERSION) {
    api_error('Unsupported API version', 400);
}

// Route to the appropriate resource file
$resource_file = __DIR__ . "/$version/$resource/index.php";

if (file_exists($resource_file)) {
    // Pass request data to the resource file
    $request = [
        'method' => $request_method,
        'endpoint' => $endpoint,
        'params' => $query_params,
        'body' => $body,
        'parts' => array_slice($endpoint_parts, 2) // Additional path parts
    ];
    
    // Include the resource file
    require_once $resource_file;
} else {
    api_error('Resource not found', 404);
}

// Log the API request
$response_code = http_response_code();
log_api_request($api_key, $endpoint, $request_method, array_merge($query_params, $body), $response_code, $api_request_start_time);
