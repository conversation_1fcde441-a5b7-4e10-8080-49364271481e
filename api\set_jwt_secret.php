<?php
// Set JWT Secret Key

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Check if config.php exists
$config_file = 'config.php';
if (!file_exists($config_file)) {
    die("Config file not found!");
}

// Read config file
$config_content = file_get_contents($config_file);

// Check if JWT_SECRET is already defined
if (strpos($config_content, 'JWT_SECRET') !== false) {
    echo "JWT_SECRET is already defined in config.php";
} else {
    // Generate a random secret
    $jwt_secret = bin2hex(random_bytes(32));
    
    // Add JWT_SECRET to config.php
    $config_content .= "\n\n// JWT Secret Key for API Authentication\ndefine('JWT_SECRET', '$jwt_secret');\n";
    
    // Write back to config.php
    if (file_put_contents($config_file, $config_content)) {
        echo "Successfully added JWT_SECRET to config.php";
    } else {
        echo "Failed to write to config.php. Check file permissions.";
    }
}
?>
