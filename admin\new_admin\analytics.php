<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set page title
$page_title = 'অ্যানালিটিক্স ও রিপোর্ট';
$current_page = 'analytics.php';

try {
    // Include configuration and functions
    require_once '../../includes/config.php';
    require_once '../../includes/functions.php';

    // Check if user is logged in and is admin
    if (!isLoggedIn() || !isAdmin()) {
        redirect(SITE_URL . '/login.php');
        exit;
    }
} catch (Exception $e) {
    die("Error: " . $e->getMessage());
}

// Get date range
$start_date = $_GET['start_date'] ?? date('Y-m-01'); // First day of current month
$end_date = $_GET['end_date'] ?? date('Y-m-d'); // Today

// Get analytics data
$analytics = [
    'total_users' => 0,
    'new_users' => 0,
    'premium_users' => 0,
    'total_revenue' => 0,
    'total_movies' => 0,
    'total_tvshows' => 0,
    'total_episodes' => 0,
    'pending_payments' => 0
];

try {
    // Total users
    $user_query = "SELECT COUNT(*) as total FROM users WHERE role = 'user'";
    $result = mysqli_query($conn, $user_query);
    if ($result) {
        $analytics['total_users'] = mysqli_fetch_assoc($result)['total'];
    }

    // New users in date range
    $new_users_query = "SELECT COUNT(*) as total FROM users WHERE role = 'user' AND DATE(created_at) BETWEEN ? AND ?";
    $stmt = mysqli_prepare($conn, $new_users_query);
    mysqli_stmt_bind_param($stmt, 'ss', $start_date, $end_date);
    mysqli_stmt_execute($stmt);
    $analytics['new_users'] = mysqli_fetch_assoc(mysqli_stmt_get_result($stmt))['total'];

    // Premium users
    $premium_query = "SELECT COUNT(*) as total FROM users WHERE role = 'user' AND is_premium = 1";
    $result = mysqli_query($conn, $premium_query);
    if ($result) {
        $analytics['premium_users'] = mysqli_fetch_assoc($result)['total'];
    }

    // Revenue in date range
    $revenue_query = "SELECT SUM(amount) as total FROM payments WHERE status = 'completed' AND DATE(created_at) BETWEEN ? AND ?";
    $stmt = mysqli_prepare($conn, $revenue_query);
    mysqli_stmt_bind_param($stmt, 'ss', $start_date, $end_date);
    mysqli_stmt_execute($stmt);
    $analytics['total_revenue'] = mysqli_fetch_assoc(mysqli_stmt_get_result($stmt))['total'] ?? 0;

    // Content statistics
    $analytics['total_movies'] = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as total FROM movies"))['total'];
    $analytics['total_tvshows'] = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as total FROM tvshows"))['total'];
    $analytics['total_episodes'] = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as total FROM episodes"))['total'];
    $analytics['pending_payments'] = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as total FROM payments WHERE status = 'pending'"))['total'];

} catch (Exception $e) {
    error_log("Analytics error: " . $e->getMessage());
}

// Get chart data
$chart_data = [
    'daily_users' => [],
    'daily_revenue' => [],
    'content_distribution' => [],
    'payment_methods' => []
];

try {
    // Daily new users for last 30 days
    $daily_users_query = "SELECT DATE(created_at) as date, COUNT(*) as count 
                         FROM users 
                         WHERE role = 'user' AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                         GROUP BY DATE(created_at) 
                         ORDER BY date";
    $result = mysqli_query($conn, $daily_users_query);
    while ($row = mysqli_fetch_assoc($result)) {
        $chart_data['daily_users'][] = [
            'date' => $row['date'],
            'count' => (int)$row['count']
        ];
    }

    // Daily revenue for last 30 days
    $daily_revenue_query = "SELECT DATE(created_at) as date, SUM(amount) as revenue 
                           FROM payments 
                           WHERE status = 'completed' AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                           GROUP BY DATE(created_at) 
                           ORDER BY date";
    $result = mysqli_query($conn, $daily_revenue_query);
    while ($row = mysqli_fetch_assoc($result)) {
        $chart_data['daily_revenue'][] = [
            'date' => $row['date'],
            'revenue' => (float)$row['revenue']
        ];
    }

    // Content distribution
    $content_query = "SELECT 'Movies' as type, COUNT(*) as count FROM movies
                     UNION ALL
                     SELECT 'TV Shows' as type, COUNT(*) as count FROM tvshows
                     UNION ALL
                     SELECT 'Episodes' as type, COUNT(*) as count FROM episodes";
    $result = mysqli_query($conn, $content_query);
    while ($row = mysqli_fetch_assoc($result)) {
        $chart_data['content_distribution'][] = [
            'type' => $row['type'],
            'count' => (int)$row['count']
        ];
    }

    // Payment methods
    $payment_methods_query = "SELECT payment_method, COUNT(*) as count 
                             FROM payments 
                             WHERE status = 'completed'
                             GROUP BY payment_method";
    $result = mysqli_query($conn, $payment_methods_query);
    while ($row = mysqli_fetch_assoc($result)) {
        $method_names = [
            'bkash' => 'বিকাশ',
            'nagad' => 'নগদ',
            'rocket' => 'রকেট'
        ];
        $chart_data['payment_methods'][] = [
            'method' => $method_names[$row['payment_method']] ?? $row['payment_method'],
            'count' => (int)$row['count']
        ];
    }

} catch (Exception $e) {
    error_log("Chart data error: " . $e->getMessage());
}

// Include header
include 'includes/header.php';
?>

<!-- Include Sidebar -->
<?php include 'includes/sidebar.php'; ?>

<!-- Main Content -->
<div class="main-content">
    <!-- Page Header -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-title">
                    <i class="fas fa-chart-line me-3"></i>অ্যানালিটিক্স ও রিপোর্ট
                </h1>
                <p class="page-subtitle text-muted">ডেটা বিশ্লেষণ এবং পারফরমেন্স রিপোর্ট</p>
            </div>
            <div class="col-auto">
                <div class="page-actions">
                    <button class="btn btn-outline-primary" onclick="exportReport()">
                        <i class="fas fa-download me-2"></i>রিপোর্ট এক্সপোর্ট
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label class="form-label">শুরুর তারিখ</label>
                    <input type="date" class="form-control" name="start_date" value="<?php echo $start_date; ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">শেষ তারিখ</label>
                    <input type="date" class="form-control" name="end_date" value="<?php echo $end_date; ?>">
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-2"></i>ফিল্টার করুন
                    </button>
                </div>
                <div class="col-md-4 text-end">
                    <div class="btn-group">
                        <a href="?start_date=<?php echo date('Y-m-d'); ?>&end_date=<?php echo date('Y-m-d'); ?>" class="btn btn-outline-secondary btn-sm">আজ</a>
                        <a href="?start_date=<?php echo date('Y-m-d', strtotime('-7 days')); ?>&end_date=<?php echo date('Y-m-d'); ?>" class="btn btn-outline-secondary btn-sm">৭ দিন</a>
                        <a href="?start_date=<?php echo date('Y-m-01'); ?>&end_date=<?php echo date('Y-m-d'); ?>" class="btn btn-outline-secondary btn-sm">এই মাস</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Analytics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stat-card bg-gradient-primary">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number"><?php echo number_format($analytics['total_users']); ?></h3>
                                <p class="stat-label">মোট ইউজার</p>
                                <small class="stat-sublabel">
                                    <i class="fas fa-plus me-1"></i><?php echo $analytics['new_users']; ?> নতুন
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stat-card bg-gradient-warning">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-crown"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number"><?php echo number_format($analytics['premium_users']); ?></h3>
                                <p class="stat-label">প্রিমিয়াম ইউজার</p>
                                <small class="stat-sublabel">
                                    <?php echo $analytics['total_users'] > 0 ? round(($analytics['premium_users'] / $analytics['total_users']) * 100, 1) : 0; ?>% কনভার্শন
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stat-card bg-gradient-success">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number">৳<?php echo number_format($analytics['total_revenue']); ?></h3>
                                <p class="stat-label">রেভিনিউ</p>
                                <small class="stat-sublabel">
                                    <?php echo date('d M', strtotime($start_date)); ?> - <?php echo date('d M', strtotime($end_date)); ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stat-card bg-gradient-info">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number"><?php echo number_format($analytics['pending_payments']); ?></h3>
                                <p class="stat-label">পেন্ডিং পেমেন্ট</p>
                                <small class="stat-sublabel">
                                    <i class="fas fa-exclamation-triangle me-1"></i>অ্যাকশন প্রয়োজন
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Daily Users Chart -->
        <div class="col-xl-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-plus me-2"></i>দৈনিক নতুন ইউজার
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="dailyUsersChart" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Daily Revenue Chart -->
        <div class="col-xl-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>দৈনিক রেভিনিউ
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="dailyRevenueChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Second Charts Row -->
    <div class="row mb-4">
        <!-- Content Distribution -->
        <div class="col-xl-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>কনটেন্ট বিতরণ
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="contentChart" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Payment Methods -->
        <div class="col-xl-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-credit-card me-2"></i>পেমেন্ট মেথড
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="paymentMethodsChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-table me-2"></i>দ্রুত পরিসংখ্যান
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><i class="fas fa-film text-primary me-2"></i>মোট মুভি</td>
                                    <td class="text-end fw-bold"><?php echo number_format($analytics['total_movies']); ?></td>
                                </tr>
                                <tr>
                                    <td><i class="fas fa-tv text-success me-2"></i>মোট টিভি শো</td>
                                    <td class="text-end fw-bold"><?php echo number_format($analytics['total_tvshows']); ?></td>
                                </tr>
                                <tr>
                                    <td><i class="fas fa-list text-info me-2"></i>মোট এপিসোড</td>
                                    <td class="text-end fw-bold"><?php echo number_format($analytics['total_episodes']); ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><i class="fas fa-percentage text-warning me-2"></i>প্রিমিয়াম রেট</td>
                                    <td class="text-end fw-bold">
                                        <?php echo $analytics['total_users'] > 0 ? round(($analytics['premium_users'] / $analytics['total_users']) * 100, 1) : 0; ?>%
                                    </td>
                                </tr>
                                <tr>
                                    <td><i class="fas fa-calculator text-danger me-2"></i>গড় রেভিনিউ/ইউজার</td>
                                    <td class="text-end fw-bold">
                                        ৳<?php echo $analytics['premium_users'] > 0 ? number_format($analytics['total_revenue'] / $analytics['premium_users'], 2) : 0; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><i class="fas fa-clock text-secondary me-2"></i>পেন্ডিং পেমেন্ট</td>
                                    <td class="text-end fw-bold"><?php echo number_format($analytics['pending_payments']); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Page-specific scripts
$page_scripts = '
<script>
// Chart data from PHP
const chartData = ' . json_encode($chart_data) . ';

document.addEventListener("DOMContentLoaded", function() {
    // Daily Users Chart
    const dailyUsersCtx = document.getElementById("dailyUsersChart");
    if (dailyUsersCtx && chartData.daily_users) {
        new Chart(dailyUsersCtx, {
            type: "line",
            data: {
                labels: chartData.daily_users.map(item => new Date(item.date).toLocaleDateString("bn-BD")),
                datasets: [{
                    label: "নতুন ইউজার",
                    data: chartData.daily_users.map(item => item.count),
                    borderColor: "#e50914",
                    backgroundColor: "rgba(229, 9, 20, 0.1)",
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: "#ffffff" }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: { color: "#b3b3b3" },
                        grid: { color: "#333333" }
                    },
                    x: {
                        ticks: { color: "#b3b3b3" },
                        grid: { color: "#333333" }
                    }
                }
            }
        });
    }

    // Daily Revenue Chart
    const dailyRevenueCtx = document.getElementById("dailyRevenueChart");
    if (dailyRevenueCtx && chartData.daily_revenue) {
        new Chart(dailyRevenueCtx, {
            type: "bar",
            data: {
                labels: chartData.daily_revenue.map(item => new Date(item.date).toLocaleDateString("bn-BD")),
                datasets: [{
                    label: "রেভিনিউ (৳)",
                    data: chartData.daily_revenue.map(item => item.revenue),
                    backgroundColor: "rgba(40, 167, 69, 0.8)",
                    borderColor: "#28a745",
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: "#ffffff" }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: { 
                            color: "#b3b3b3",
                            callback: function(value) {
                                return "৳" + value;
                            }
                        },
                        grid: { color: "#333333" }
                    },
                    x: {
                        ticks: { color: "#b3b3b3" },
                        grid: { color: "#333333" }
                    }
                }
            }
        });
    }

    // Content Distribution Chart
    const contentCtx = document.getElementById("contentChart");
    if (contentCtx && chartData.content_distribution) {
        new Chart(contentCtx, {
            type: "doughnut",
            data: {
                labels: chartData.content_distribution.map(item => item.type),
                datasets: [{
                    data: chartData.content_distribution.map(item => item.count),
                    backgroundColor: [
                        "#e50914",
                        "#28a745", 
                        "#17a2b8"
                    ],
                    borderWidth: 2,
                    borderColor: "#1a1a1a"
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: "bottom",
                        labels: { color: "#ffffff" }
                    }
                }
            }
        });
    }

    // Payment Methods Chart
    const paymentMethodsCtx = document.getElementById("paymentMethodsChart");
    if (paymentMethodsCtx && chartData.payment_methods) {
        new Chart(paymentMethodsCtx, {
            type: "pie",
            data: {
                labels: chartData.payment_methods.map(item => item.method),
                datasets: [{
                    data: chartData.payment_methods.map(item => item.count),
                    backgroundColor: [
                        "#ffc107",
                        "#6f42c1",
                        "#fd7e14"
                    ],
                    borderWidth: 2,
                    borderColor: "#1a1a1a"
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: "bottom",
                        labels: { color: "#ffffff" }
                    }
                }
            }
        });
    }
});

function exportReport() {
    // Implement export functionality
    cinepixAdmin.showToast("রিপোর্ট এক্সপোর্ট ফিচার শীঘ্রই আসছে!", "info");
}
</script>
';

// Include footer
include 'includes/footer.php';
?>
