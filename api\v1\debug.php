<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include config file
require_once '../config.php';
require_once '../functions.php';

// Test API functions
echo "<h1>API Functions Debug</h1>";
echo "<pre>";

echo "PHP Version: " . phpversion() . "\n\n";

// Check if api_response function exists
if (function_exists('api_response')) {
    echo "api_response function: EXISTS\n";
} else {
    echo "api_response function: MISSING\n";
}

// Check if api_error function exists
if (function_exists('api_error')) {
    echo "api_error function: EXISTS\n";
} else {
    echo "api_error function: MISSING\n";
}

// Check if is_authenticated function exists
if (function_exists('is_authenticated')) {
    echo "is_authenticated function: EXISTS\n";
} else {
    echo "is_authenticated function: MISSING\n";
}

// Check if JWT_SECRET is defined
if (defined('JWT_SECRET')) {
    echo "JWT_SECRET: DEFINED\n";
} else {
    echo "JWT_SECRET: NOT DEFINED\n";
    
    // Add JWT_SECRET to config.php
    $jwt_secret = bin2hex(random_bytes(32)); // Generate a random secret
    $config_file = file_get_contents('../config.php');
    
    if (strpos($config_file, 'JWT_SECRET') === false) {
        $config_file .= "\n\n// JWT Secret Key for API Authentication\ndefine('JWT_SECRET', '$jwt_secret');\n";
        file_put_contents('../config.php', $config_file);
        echo "Added JWT_SECRET to config.php\n";
    }
}

// Test database connection
if (isset($conn) && $conn) {
    echo "Database connection: SUCCESS\n";
    
    // Check if premium_plans table exists
    $result = mysqli_query($conn, "SHOW TABLES LIKE 'premium_plans'");
    if (mysqli_num_rows($result) > 0) {
        echo "Table premium_plans: EXISTS\n";
    } else {
        echo "Table premium_plans: MISSING\n";
        
        // Create premium_plans table
        $sql = "CREATE TABLE IF NOT EXISTS premium_plans (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            duration INT NOT NULL,
            features TEXT NULL,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        if (mysqli_query($conn, $sql)) {
            echo "Created premium_plans table\n";
            
            // Insert default plans
            $sql = "INSERT INTO premium_plans (name, price, duration, features, status) VALUES 
                ('Basic', 30, 30, 'Access to premium movies\nAccess to premium TV shows\nHD quality', 'active'),
                ('Standard', 60, 60, 'Access to premium movies\nAccess to premium TV shows\nFull HD quality\nPriority support', 'active'),
                ('Premium', 90, 90, 'Access to premium movies\nAccess to premium TV shows\n4K quality\nPriority support\nEarly access to new content', 'active')";
            
            if (mysqli_query($conn, $sql)) {
                echo "Inserted default premium plans\n";
            } else {
                echo "Error inserting default premium plans: " . mysqli_error($conn) . "\n";
            }
        } else {
            echo "Error creating premium_plans table: " . mysqli_error($conn) . "\n";
        }
    }
} else {
    echo "Database connection: FAILED\n";
    if (isset($conn)) {
        echo "Error: " . mysqli_connect_error() . "\n";
    } else {
        echo "Error: \$conn variable not set\n";
    }
}

// Create a simple test response
echo "\n\nTest API Response:\n";
echo json_encode([
    'success' => true,
    'message' => 'Test successful',
    'data' => [
        'test' => 'value',
        'timestamp' => time()
    ]
]);

echo "</pre>";
?>
