<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Get video URL from query parameter
$video_url = isset($_GET['url']) ? $_GET['url'] : '';
$video_title = isset($_GET['title']) ? $_GET['title'] : 'Video Player';
$poster = isset($_GET['poster']) ? $_GET['poster'] : '';
$subtitle_url_bn = isset($_GET['subtitle_bn']) ? $_GET['subtitle_bn'] : '';
$subtitle_url_en = isset($_GET['subtitle_en']) ? $_GET['subtitle_en'] : '';

// Determine video type
$video_type = 'video/mp4';
if (strpos($video_url, '.m3u8') !== false) {
    $video_type = 'application/x-mpegURL';
} elseif (strpos($video_url, '.mpd') !== false) {
    $video_type = 'application/dash+xml';
} elseif (strpos($video_url, '.mkv') !== false) {
    $video_type = 'video/x-matroska';
}

// Validate URL
if (empty($video_url)) {
    header('Location: ' . SITE_URL);
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $video_title; ?> - Shaka Player</title>

    <!-- Preload critical resources -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" as="style">
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/shaka-player@4.3.6/dist/shaka-player.ui.min.js" as="script">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/shaka-player@4.3.6/dist/controls.min.css" as="style">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Shaka Player CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/shaka-player@4.3.6/dist/controls.min.css">

    <style>
        body {
            background-color: #0f0f0f;
            color: #fff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .btn-back {
            margin-bottom: 15px;
            border-radius: 20px;
            padding: 8px 15px;
            font-size: 14px;
            background-color: rgba(255, 255, 255, 0.1);
            border: none;
            transition: all 0.3s;
        }

        .btn-back:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .player-container {
            background-color: #1a1a1a;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            margin-bottom: 20px;
        }

        .video-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            overflow: hidden;
        }

        #video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        #video-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .video-info {
            padding: 20px;
        }

        .video-info h1 {
            font-size: 24px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .badge {
            font-weight: 500;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 12px;
        }

        .alert {
            margin-top: 15px;
            border-radius: 6px;
            font-size: 14px;
        }

        .controls-panel {
            margin-top: 20px;
            background-color: #252525;
            border-radius: 6px;
            padding: 15px;
        }

        .controls-panel h5 {
            font-size: 16px;
            margin-bottom: 15px;
            color: #e5e5e5;
        }

        .keyboard-shortcuts {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .keyboard-shortcuts ul {
            list-style: none;
            padding: 0;
            margin: 0;
            flex: 1;
            min-width: 200px;
        }

        .keyboard-shortcuts li {
            margin-bottom: 8px;
            font-size: 13px;
            color: #b0b0b0;
        }

        .keyboard-shortcuts i {
            width: 20px;
            text-align: center;
            color: #0d6efd;
        }

        /* Mobile optimizations */
        @media (max-width: 768px) {
            .video-info h1 {
                font-size: 20px;
            }

            .keyboard-shortcuts {
                flex-direction: column;
            }
        }

        /* Landscape mode optimizations for mobile */
        @media (max-height: 500px) and (orientation: landscape) {
            .player-container {
                margin-bottom: 0;
            }

            .video-info {
                padding: 10px;
            }

            .video-info h1 {
                font-size: 18px;
                margin-bottom: 5px;
            }

            .controls-panel {
                display: none;
            }
        }

        /* Custom Shaka UI styling */
        .shaka-video-container .shaka-controls-button-panel {
            font-size: 1.2em;
        }

        .shaka-overflow-menu, .shaka-settings-menu {
            border-radius: 8px;
            background-color: rgba(28, 28, 28, 0.9);
        }

        .shaka-settings-menu button {
            font-size: 14px;
        }

        .shaka-range-element {
            height: 5px;
        }

        .shaka-small-play-button, .shaka-current-time, .shaka-seek-bar-container {
            margin-left: 10px;
        }

        .shaka-volume-bar-container {
            margin-right: 10px;
        }

        /* Audio track notification styling */
        .audio-track-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            z-index: 9999;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            transition: opacity 0.5s ease;
        }

        .audio-track-notification i {
            margin-right: 8px;
            color: #0d6efd;
        }

        .audio-track-notification.fade-out {
            opacity: 0;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <a href="<?php echo SITE_URL; ?>" class="btn btn-outline-light btn-back">
            <i class="fas fa-arrow-left"></i> Back to Home
        </a>

        <div class="player-container">
            <div class="video-container">
                <!-- Shaka Player -->
                <div id="video-container" data-shaka-player-container>
                    <video id="video" data-shaka-player autoplay poster="<?php echo $poster; ?>">
                        <?php if (!empty($subtitle_url_bn)): ?>
                        <track kind="subtitles" src="<?php echo SITE_URL; ?>/subtitle_converter.php?url=<?php echo urlencode($subtitle_url_bn); ?>" srclang="bn" label="Bangla" default>
                        <?php endif; ?>
                        <?php if (!empty($subtitle_url_en)): ?>
                        <track kind="subtitles" src="<?php echo SITE_URL; ?>/subtitle_converter.php?url=<?php echo urlencode($subtitle_url_en); ?>" srclang="en" label="English" <?php echo empty($subtitle_url_bn) ? 'default' : ''; ?>>
                        <?php endif; ?>
                    </video>
                </div>
            </div>

            <div class="video-info">
                <h1><?php echo $video_title; ?></h1>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <span class="badge bg-danger me-2">720p</span>
                        <?php if (!empty($subtitle_url_bn) || !empty($subtitle_url_en)): ?>
                        <span class="badge bg-success me-2">Subtitles</span>
                        <?php endif; ?>
                        <?php if (!empty($subtitle_url_bn)): ?>
                        <span class="badge bg-secondary me-2">Bangla</span>
                        <?php endif; ?>
                        <?php if (!empty($subtitle_url_en)): ?>
                        <span class="badge bg-info me-2">English</span>
                        <?php endif; ?>
                    </div>
                    <div>
                        <button id="copy-url" class="btn btn-sm btn-outline-light" data-url="<?php echo SITE_URL; ?>/shaka_player.php?url=<?php echo urlencode($video_url); ?>&title=<?php echo urlencode($video_title); ?>&poster=<?php echo urlencode($poster); ?><?php echo !empty($subtitle_url_bn) ? '&subtitle_bn=' . urlencode($subtitle_url_bn) : ''; ?><?php echo !empty($subtitle_url_en) ? '&subtitle_en=' . urlencode($subtitle_url_en) : ''; ?>">
                            <i class="fas fa-copy"></i> Copy Player URL
                        </button>
                    </div>
                </div>

                <div class="alert alert-info">
                    <strong>Note:</strong> Shaka Player supports DASH, HLS, and MKV formats with advanced features like adaptive bitrate streaming, multiple audio tracks, and subtitles. Press 'A' key to cycle through audio tracks if available. Press 'C' key to toggle subtitles.
                    <?php if (!empty($subtitle_url_bn) || !empty($subtitle_url_en)): ?>
                    <br><br><strong>Subtitles:</strong>
                    <?php if (!empty($subtitle_url_bn)): ?>
                    <span class="badge bg-secondary me-2">Bangla</span>
                    <?php endif; ?>
                    <?php if (!empty($subtitle_url_en)): ?>
                    <span class="badge bg-info me-2">English</span>
                    <?php endif; ?>
                    <?php endif; ?>
                </div>

                <!-- Debug info for MKV files -->
                <?php if (strpos($video_type, 'matroska') !== false): ?>
                <div class="alert alert-secondary debug-info">
                    <strong>MKV Debug Info:</strong> <span id="mkv-debug-info">Loading MKV file information...</span>
                </div>
                <?php endif; ?>

                <div class="controls-panel">
                    <h5>Keyboard Shortcuts</h5>
                    <div class="keyboard-shortcuts">
                        <ul>
                            <li><i class="fas fa-play"></i> Space - Play/Pause</li>
                            <li><i class="fas fa-volume-up"></i> M - Mute/Unmute</li>
                            <li><i class="fas fa-arrows-alt"></i> F - Fullscreen</li>
                            <li><i class="fas fa-undo"></i> Left Arrow - Rewind 10s</li>
                            <li><i class="fas fa-undo"></i> J - Rewind 10s</li>
                        </ul>
                        <ul>
                            <li><i class="fas fa-redo"></i> Right Arrow - Forward 10s</li>
                            <li><i class="fas fa-redo"></i> L - Forward 10s</li>
                            <li><i class="fas fa-volume-down"></i> Down Arrow - Volume Down</li>
                            <li><i class="fas fa-volume-up"></i> Up Arrow - Volume Up</li>
                            <li><i class="fas fa-language"></i> A - Cycle Audio Tracks</li>
                            <li><i class="fas fa-closed-captioning"></i> C - Toggle Subtitles</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Shaka Player -->
    <script src="https://cdn.jsdelivr.net/npm/shaka-player@4.3.6/dist/shaka-player.ui.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Shaka Player
            const video = document.getElementById('video');
            const videoContainer = document.getElementById('video-container');

            // Register MKV support
            shaka.polyfill.installAll();

            // Check if MKV is supported
            if (videoType === 'video/x-matroska') {
                console.log('MKV file detected, enabling enhanced MKV support');
            }

            const player = new shaka.Player(video);

            // Add event listener for audio tracks loaded
            video.addEventListener('loadedmetadata', function() {
                if (video.audioTracks && video.audioTracks.length > 0) {
                    console.log('Audio tracks detected in video element:', video.audioTracks.length);

                    // Update debug info for MKV files
                    const debugInfoElement = document.getElementById('mkv-debug-info');
                    if (debugInfoElement) {
                        let audioTracksInfo = `Found ${video.audioTracks.length} audio tracks:<br>`;

                        for (let i = 0; i < video.audioTracks.length; i++) {
                            const track = video.audioTracks[i];
                            const trackName = track.label || track.language || `Track ${i+1}`;
                            audioTracksInfo += `- Track ${i+1}: ${trackName} ${track.enabled ? '(active)' : ''}<br>`;
                            console.log(`Audio track ${i+1}:`, trackName);
                        }

                        // Add video info
                        audioTracksInfo += `<br>Video Info:<br>`;
                        audioTracksInfo += `- Duration: ${Math.floor(video.duration / 60)}m ${Math.floor(video.duration % 60)}s<br>`;
                        audioTracksInfo += `- Resolution: ${video.videoWidth}x${video.videoHeight}<br>`;

                        debugInfoElement.innerHTML = audioTracksInfo;
                    }
                } else {
                    // Update debug info if no audio tracks found
                    const debugInfoElement = document.getElementById('mkv-debug-info');
                    if (debugInfoElement) {
                        debugInfoElement.innerHTML = 'No multiple audio tracks detected in this MKV file.';
                    }
                }
            });

            // Configure Shaka Player
            player.configure({
                streaming: {
                    bufferingGoal: 60,         // Buffer up to 60 seconds
                    rebufferingGoal: 15,       // Have at least 15 seconds buffered before starting playback after a stall
                    bufferBehind: 30,          // Keep 30 seconds of content behind the playhead
                    retryParameters: {
                        maxAttempts: 6,        // Try more times before failing
                        baseDelay: 1000,       // Start with a 1-second delay before retrying
                        backoffFactor: 2,      // Double the delay each retry
                        fuzzFactor: 0.5        // Randomize delay by +/- 50%
                    },
                    alwaysStreamText: true,    // Always stream text tracks, even when not selected
                    useNativeHlsOnSafari: true, // Use native HLS on Safari
                    ignoreTextStreamFailures: true, // Don't fail if text streams fail to load
                    startAtSegmentBoundary: true, // Start at segment boundaries for smoother playback
                    preferNativeHls: true,     // Prefer native HLS when available
                    // Enhanced MKV support
                    transmuxer: {
                        keepOriginalTimestamps: true
                    }
                },
                abr: {
                    enabled: true,             // Enable adaptive bitrate streaming
                    defaultBandwidthEstimate: 1000000, // Start with a 1Mbps estimate
                    switchInterval: 8,         // Switch streams every 8 seconds if needed
                    bandwidthDowngradeTarget: 0.95, // Downgrade when bandwidth is 95% utilized
                    bandwidthUpgradeTarget: 0.85    // Upgrade when bandwidth is 85% utilized
                },
                manifest: {
                    disableAudio: false,       // Enable audio
                    disableVideo: false,       // Enable video
                    disableText: false,        // Enable text
                    defaultAudioCodec: 'mp4a', // Default audio codec
                    defaultVideoCodec: 'avc1', // Default video codec
                    dash: {
                        ignoreMinBufferTime: true, // Ignore min buffer time in DASH manifests
                        autoCorrectDrift: true, // Auto-correct drift in DASH manifests
                        ignoreSuggestedPresentationDelay: true // Ignore suggested presentation delay
                    },
                    hls: {
                        ignoreTextStreamFailures: true, // Don't fail if text streams fail to load
                        useFullSegmentsForStartTime: true // Use full segments for start time
                    }
                },
                // Add support for multiple audio tracks in MKV files
                mediaSource: {
                    forceTransmux: true  // Force transmuxing for better MKV support
                }
            });

            // Initialize UI
            const ui = new shaka.ui.Overlay(player, videoContainer, video);
            const controls = ui.getControls();

            // Configure UI
            const config = {
                addBigPlayButton: true,        // Add big play button
                addSeekBar: true,              // Add seek bar
                controlPanelElements: [
                    'play_pause',
                    'rewind',
                    'fast_forward',
                    'time_and_duration',
                    'spacer',
                    'mute',
                    'volume',
                    'quality',
                    'language',                // Add language selection for multiple audio tracks
                    'captions',
                    'fullscreen',
                    'overflow_menu'
                ],
                overflowMenuButtons: [
                    'captions',
                    'quality',
                    'language',                // Add language selection in overflow menu
                    'playback_rate',
                    'picture_in_picture'
                ],
                seekBarColors: {
                    base: 'rgba(255, 255, 255, 0.3)',
                    buffered: 'rgba(255, 255, 255, 0.54)',
                    played: 'rgb(255, 0, 0)'
                },
                enableTooltips: true,          // Enable tooltips
                enableKeyboardPlaybackControls: true, // Enable keyboard controls
                doubleClickForFullscreen: true, // Double-click for fullscreen
                singleClickForPlayAndPause: true // Single-click for play/pause
            };
            ui.configure(config);

            // Load the video
            const videoUrl = '<?php echo $video_url; ?>';
            const videoType = '<?php echo $video_type; ?>';

            // Error handling
            player.addEventListener('error', (event) => {
                console.error('Error code', event.detail.code, 'object', event.detail);
                document.querySelector('.alert-info').classList.remove('alert-info');
                document.querySelector('.alert-info').classList.add('alert-danger');
                document.querySelector('.alert-info').innerHTML = '<strong>Error:</strong> ' + event.detail.message;
            });

            // Track audio language changes
            player.addEventListener('variantchanged', () => {
                const tracks = player.getVariantTracks();
                const activeTracks = tracks.filter(track => track.active);
                if (activeTracks.length > 0) {
                    console.log('Active audio track:', activeTracks[0].language, activeTracks[0].label);
                }
            });

            // Load the content
            player.load(videoUrl).then(() => {
                console.log('The video has been loaded successfully');

                // Check for multiple audio tracks
                const tracks = player.getVariantTracks();
                const audioLanguages = new Set();
                const audioLabels = new Map();

                tracks.forEach(track => {
                    if (track.language) {
                        audioLanguages.add(track.language);
                        // Store language and label mapping
                        if (track.label) {
                            audioLabels.set(track.language, track.label);
                        }
                    }
                });

                // For MKV files, try to detect audio tracks from the video element
                if (videoType === 'video/x-matroska' && video.audioTracks && video.audioTracks.length > 1) {
                    console.log('MKV audio tracks detected via video element:', video.audioTracks.length);

                    // Add notification about MKV audio tracks
                    document.querySelector('.alert-info').innerHTML =
                        '<strong>Note:</strong> This MKV video has multiple audio tracks. ' +
                        'Press the "A" key to cycle through audio tracks, or use the ' +
                        '<i class="fas fa-language"></i> button in player controls.';
                } else if (audioLanguages.size > 1) {
                    // For other formats with multiple audio tracks
                    console.log('Multiple audio tracks detected:', audioLanguages);

                    let languageInfo = '';
                    audioLanguages.forEach(lang => {
                        const label = audioLabels.get(lang) || lang;
                        languageInfo += `<span class="badge bg-secondary me-1">${label}</span>`;
                    });

                    document.querySelector('.alert-info').innerHTML =
                        '<strong>Note:</strong> This video has multiple audio tracks: ' + languageInfo + '<br>' +
                        'Click the language button <i class="fas fa-language"></i> in the player controls or press "A" key to switch between them.';
                }

                // Register for audio track change events from the video element
                if (video.audioTracks) {
                    for (let i = 0; i < video.audioTracks.length; i++) {
                        video.audioTracks[i].addEventListener('change', function() {
                            console.log('Audio track changed:', this.label || this.language);
                        });
                    }
                }
            }).catch((error) => {
                console.error('Error loading video:', error);
            });

            // Copy URL button
            document.getElementById('copy-url').addEventListener('click', function() {
                const url = this.getAttribute('data-url');
                navigator.clipboard.writeText(url).then(() => {
                    this.innerHTML = '<i class="fas fa-check"></i> Copied!';
                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-copy"></i> Copy Player URL';
                    }, 2000);
                });
            });

            // Add event listener for keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Only if video player is in focus or we're in fullscreen
                if (document.activeElement === video || document.fullscreenElement) {
                    // J key - seek backward 10 seconds
                    if (e.key === 'j' || e.key === 'J') {
                        video.currentTime -= 10;
                    }

                    // L key - seek forward 10 seconds
                    if (e.key === 'l' || e.key === 'L') {
                        video.currentTime += 10;
                    }

                    // A key - cycle through audio tracks
                    if (e.key === 'a' || e.key === 'A') {
                        cycleAudioTracks(player);
                    }

                    // C key - toggle subtitles
                    if (e.key === 'c' || e.key === 'C') {
                        toggleSubtitles(player);
                    }
                }
            });

            // Function to cycle through audio tracks
            function cycleAudioTracks(player) {
                // First try to handle MKV audio tracks via the video element
                if (video.audioTracks && video.audioTracks.length > 1) {
                    // Find the current enabled track
                    let currentTrackIndex = -1;
                    for (let i = 0; i < video.audioTracks.length; i++) {
                        if (video.audioTracks[i].enabled) {
                            currentTrackIndex = i;
                            break;
                        }
                    }

                    // Calculate next track index
                    const nextTrackIndex = (currentTrackIndex + 1) % video.audioTracks.length;

                    // Disable all tracks
                    for (let i = 0; i < video.audioTracks.length; i++) {
                        video.audioTracks[i].enabled = false;
                    }

                    // Enable the next track
                    video.audioTracks[nextTrackIndex].enabled = true;

                    // Get track info for notification
                    const trackInfo = video.audioTracks[nextTrackIndex].label ||
                                     video.audioTracks[nextTrackIndex].language ||
                                     `Track ${nextTrackIndex + 1}`;

                    // Show notification
                    showAudioTrackNotification(trackInfo);

                    // Update debug info if available
                    updateAudioTrackDebugInfo();
                    return;
                }

                // Fall back to Shaka Player's variant tracks
                const tracks = player.getVariantTracks();
                if (tracks.length <= 1) return; // No need to cycle if only one track

                // Get unique languages
                const languages = new Set();
                tracks.forEach(track => {
                    if (track.language) {
                        languages.add(track.language);
                    }
                });

                if (languages.size <= 1) return; // No need to cycle if only one language

                // Get current active track
                const activeTracks = tracks.filter(track => track.active);
                if (activeTracks.length === 0) return;

                const currentLang = activeTracks[0].language;

                // Convert Set to Array for indexing
                const languagesArray = Array.from(languages);
                const currentIndex = languagesArray.indexOf(currentLang);
                const nextIndex = (currentIndex + 1) % languagesArray.length;
                const nextLang = languagesArray[nextIndex];

                // Select the next language
                player.selectAudioLanguage(nextLang);

                // Get track label if available
                let trackLabel = nextLang;
                for (const track of tracks) {
                    if (track.language === nextLang && track.label) {
                        trackLabel = track.label;
                        break;
                    }
                }

                // Show notification
                showAudioTrackNotification(trackLabel);
            }

            // Helper function to show audio track notification
            function showAudioTrackNotification(trackInfo) {
                const notification = document.createElement('div');
                notification.className = 'audio-track-notification';
                notification.innerHTML = `<i class="fas fa-language"></i> Audio: ${trackInfo}`;
                document.body.appendChild(notification);

                // Remove notification after 2 seconds
                setTimeout(() => {
                    notification.classList.add('fade-out');
                    setTimeout(() => {
                        document.body.removeChild(notification);
                    }, 500);
                }, 2000);
            }

            // Function to toggle subtitles
            function toggleSubtitles(player) {
                const tracks = player.getTextTracks();
                if (tracks.length === 0) {
                    // Check if there are HTML track elements
                    const textTracks = video.textTracks;
                    if (textTracks.length === 0) {
                        showSubtitleNotification('No subtitles available');
                        return;
                    }

                    // Toggle HTML track elements
                    let activeFound = false;
                    for (let i = 0; i < textTracks.length; i++) {
                        if (textTracks[i].mode === 'showing') {
                            textTracks[i].mode = 'hidden';
                            activeFound = true;
                        }
                    }

                    if (!activeFound && textTracks.length > 0) {
                        // No active track found, enable the first one
                        textTracks[0].mode = 'showing';
                        showSubtitleNotification(`Subtitles: ${textTracks[0].label || textTracks[0].language || 'On'}`);
                    } else if (activeFound) {
                        showSubtitleNotification('Subtitles: Off');
                    }
                    return;
                }

                // Handle Shaka Player text tracks
                const activeTrack = tracks.find(track => track.active);

                if (activeTrack) {
                    // If there's an active track, disable it
                    player.setTextTrackVisibility(false);
                    showSubtitleNotification('Subtitles: Off');
                } else {
                    // If no active track, enable the first one
                    player.setTextTrackVisibility(true);
                    if (tracks.length > 0) {
                        player.selectTextTrack(tracks[0]);
                        showSubtitleNotification(`Subtitles: ${tracks[0].label || tracks[0].language || 'On'}`);
                    }
                }
            }

            // Helper function to show subtitle notification
            function showSubtitleNotification(message) {
                const notification = document.createElement('div');
                notification.className = 'audio-track-notification';
                notification.innerHTML = `<i class="fas fa-closed-captioning"></i> ${message}`;
                document.body.appendChild(notification);

                // Remove notification after 2 seconds
                setTimeout(() => {
                    notification.classList.add('fade-out');
                    setTimeout(() => {
                        document.body.removeChild(notification);
                    }, 500);
                }, 2000);
            }

            // Helper function to update audio track debug info
            function updateAudioTrackDebugInfo() {
                const debugInfoElement = document.getElementById('mkv-debug-info');
                if (!debugInfoElement || !video.audioTracks) return;

                let audioTracksInfo = `Found ${video.audioTracks.length} audio tracks:<br>`;

                for (let i = 0; i < video.audioTracks.length; i++) {
                    const track = video.audioTracks[i];
                    const trackName = track.label || track.language || `Track ${i+1}`;
                    audioTracksInfo += `- Track ${i+1}: ${trackName} ${track.enabled ? '<strong>(active)</strong>' : ''}<br>`;
                }

                // Add video info
                audioTracksInfo += `<br>Video Info:<br>`;
                audioTracksInfo += `- Duration: ${Math.floor(video.duration / 60)}m ${Math.floor(video.duration % 60)}s<br>`;
                audioTracksInfo += `- Resolution: ${video.videoWidth}x${video.videoHeight}<br>`;
                audioTracksInfo += `- Current Time: ${Math.floor(video.currentTime / 60)}m ${Math.floor(video.currentTime % 60)}s<br>`;

                debugInfoElement.innerHTML = audioTracksInfo;
            }
        });
    </script>
</body>
</html>
