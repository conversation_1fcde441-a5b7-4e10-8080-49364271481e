<?php
// Include database connection
require_once 'includes/config.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || !isAdmin()) {
    header("Location: login.php");
    exit;
}

$messages = [];
$success = false;

if (isset($_POST['update_database'])) {
    // Function to execute SQL and handle errors
    function executeSql($conn, $sql, $description) {
        try {
            if (mysqli_query($conn, $sql)) {
                return ["success" => true, "message" => "<div class='alert alert-success'>$description সফলভাবে সম্পন্ন হয়েছে।</div>"];
            } else {
                return ["success" => false, "message" => "<div class='alert alert-danger'>$description ব্যর্থ হয়েছে: " . mysqli_error($conn) . "</div>"];
            }
        } catch (Exception $e) {
            return ["success" => false, "message" => "<div class='alert alert-danger'>$description ব্যর্থ হয়েছে: " . $e->getMessage() . "</div>"];
        }
    }

    // Update download_links table
    try {
        $sql_drop_download_constraint = "ALTER TABLE download_links DROP INDEX unique_download_link";
        $result = executeSql($conn, $sql_drop_download_constraint, "ডাউনলোড লিংক টেবিল থেকে UNIQUE KEY অপসারণ");
        $messages[] = $result["message"];
    } catch (Exception $e) {
        $messages[] = "<div class='alert alert-warning'>ডাউনলোড লিংক টেবিলে unique_download_link কনস্ট্রেইন্ট নেই বা অপসারণ করা যায়নি।</div>";
    }

    try {
        $sql_add_download_constraint = "ALTER TABLE download_links ADD CONSTRAINT unique_download_content UNIQUE(content_type, content_id, id)";
        $result = executeSql($conn, $sql_add_download_constraint, "ডাউনলোড লিংক টেবিলে নতুন UNIQUE KEY যোগ");
        $messages[] = $result["message"];
    } catch (Exception $e) {
        $messages[] = "<div class='alert alert-warning'>ডাউনলোড লিংক টেবিলে নতুন কনস্ট্রেইন্ট যোগ করা যায়নি।</div>";
    }

    // Update streaming_links table
    try {
        $sql_drop_streaming_constraint = "ALTER TABLE streaming_links DROP INDEX unique_stream_link";
        $result = executeSql($conn, $sql_drop_streaming_constraint, "স্ট্রিমিং লিংক টেবিল থেকে UNIQUE KEY অপসারণ");
        $messages[] = $result["message"];
    } catch (Exception $e) {
        $messages[] = "<div class='alert alert-warning'>স্ট্রিমিং লিংক টেবিলে unique_stream_link কনস্ট্রেইন্ট নেই বা অপসারণ করা যায়নি।</div>";
    }

    try {
        $sql_add_streaming_constraint = "ALTER TABLE streaming_links ADD CONSTRAINT unique_stream_content UNIQUE(content_type, content_id, id)";
        $result = executeSql($conn, $sql_add_streaming_constraint, "স্ট্রিমিং লিংক টেবিলে নতুন UNIQUE KEY যোগ");
        $messages[] = $result["message"];
    } catch (Exception $e) {
        $messages[] = "<div class='alert alert-warning'>স্ট্রিমিং লিংক টেবিলে নতুন কনস্ট্রেইন্ট যোগ করা যায়নি।</div>";
    }

    // Update episode_links table
    try {
        $sql_drop_episode_constraint = "ALTER TABLE episode_links DROP INDEX unique_episode_link";
        $result = executeSql($conn, $sql_drop_episode_constraint, "এপিসোড লিংক টেবিল থেকে UNIQUE KEY অপসারণ");
        $messages[] = $result["message"];
    } catch (Exception $e) {
        $messages[] = "<div class='alert alert-warning'>এপিসোড লিংক টেবিলে unique_episode_link কনস্ট্রেইন্ট নেই বা অপসারণ করা যায়নি।</div>";
    }

    try {
        $sql_add_episode_constraint = "ALTER TABLE episode_links ADD CONSTRAINT unique_episode_content UNIQUE(episode_id, id)";
        $result = executeSql($conn, $sql_add_episode_constraint, "এপিসোড লিংক টেবিলে নতুন UNIQUE KEY যোগ");
        $messages[] = $result["message"];
    } catch (Exception $e) {
        $messages[] = "<div class='alert alert-warning'>এপিসোড লিংক টেবিলে নতুন কনস্ট্রেইন্ট যোগ করা যায়নি।</div>";
    }

    // Add server_name and file_size to download_links if they don't exist
    $check_server_name = "SHOW COLUMNS FROM download_links LIKE 'server_name'";
    $server_name_result = mysqli_query($conn, $check_server_name);

    if (mysqli_num_rows($server_name_result) == 0) {
        $add_server_name = "ALTER TABLE download_links ADD COLUMN server_name VARCHAR(50) AFTER link_type";
        $result = executeSql($conn, $add_server_name, "ডাউনলোড লিংক টেবিলে server_name কলাম যোগ");
        $messages[] = $result["message"];
    }

    $check_file_size = "SHOW COLUMNS FROM download_links LIKE 'file_size'";
    $file_size_result = mysqli_query($conn, $check_file_size);

    if (mysqli_num_rows($file_size_result) == 0) {
        $add_file_size = "ALTER TABLE download_links ADD COLUMN file_size VARCHAR(50) AFTER server_name";
        $result = executeSql($conn, $add_file_size, "ডাউনলোড লিংক টেবিলে file_size কলাম যোগ");
        $messages[] = $result["message"];
    }

    $success = true;
}

// HTML output
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>আপডেট লিংক টেবিল</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 offset-md-2">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">ডাটাবেস লিংক টেবিল আপডেট</h4>
                    </div>
                    <div class="card-body">
                        <?php if (!$success): ?>
                        <div class="alert alert-info">
                            <p><strong>এই আপডেট কি করবে?</strong></p>
                            <p>এই আপডেটের মাধ্যমে আপনি একই কোয়ালিটির জন্য একাধিক ডাউনলোড এবং স্ট্রিমিং লিংক যোগ করতে পারবেন।</p>
                            <p>বর্তমানে, আপনি একই কোয়ালিটির জন্য শুধুমাত্র একটি লিংক যোগ করতে পারেন। এই আপডেট সেই সীমাবদ্ধতা দূর করবে।</p>
                        </div>

                        <form method="POST" action="">
                            <div class="alert alert-warning">
                                <p><strong>সতর্কতা:</strong> এই আপডেট আপনার ডাটাবেস স্ট্রাকচার পরিবর্তন করবে। আপডেট করার আগে ডাটাবেসের ব্যাকআপ নিয়ে রাখুন।</p>
                            </div>
                            <button type="submit" name="update_database" class="btn btn-primary">ডাটাবেস আপডেট করুন</button>
                            <a href="admin/index.php" class="btn btn-secondary">বাতিল করুন</a>
                        </form>
                        <?php else: ?>
                        <h5>আপডেট স্ট্যাটাস:</h5>
                        <?php foreach ($messages as $message): ?>
                            <?php echo $message; ?>
                        <?php endforeach; ?>

                        <div class="alert alert-success mt-3">
                            <p><strong>আপডেট সম্পন্ন!</strong></p>
                            <p>এখন আপনি একই কোয়ালিটির জন্য একাধিক ডাউনলোড এবং স্ট্রিমিং লিংক যোগ করতে পারবেন।</p>
                        </div>

                        <div class="mt-3">
                            <a href="admin/index.php" class="btn btn-primary">অ্যাডমিন প্যানেলে ফিরে যান</a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
