<?php
// Set page title
$page_title = 'Backup & Restore';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Process form submissions
$success_message = '';
$error_message = '';

// Backup database
if (isset($_POST['backup_database'])) {
    // Set backup filename
    $backup_file = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
    $backup_path = '../backups/';
    
    // Create backups directory if it doesn't exist
    if (!file_exists($backup_path)) {
        mkdir($backup_path, 0777, true);
    }
    
    // Get database credentials from config
    $db_host = DB_HOST;
    $db_user = DB_USER;
    $db_pass = DB_PASS;
    $db_name = DB_NAME;
    
    // Command to backup database
    $command = "mysqldump --host=$db_host --user=$db_user --password=$db_pass $db_name > $backup_path$backup_file";
    
    // Execute command
    $output = [];
    $return_var = 0;
    exec($command, $output, $return_var);
    
    if ($return_var === 0) {
        $success_message = 'Database backup created successfully: ' . $backup_file;
    } else {
        $error_message = 'Error creating database backup. Please check if mysqldump is available on the server.';
    }
}

// Restore database
if (isset($_POST['restore_database']) && isset($_FILES['backup_file'])) {
    $file = $_FILES['backup_file'];
    
    // Check for errors
    if ($file['error'] === UPLOAD_ERR_OK) {
        // Check file extension
        $file_ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if ($file_ext === 'sql') {
            // Get database credentials from config
            $db_host = DB_HOST;
            $db_user = DB_USER;
            $db_pass = DB_PASS;
            $db_name = DB_NAME;
            
            // Command to restore database
            $command = "mysql --host=$db_host --user=$db_user --password=$db_pass $db_name < {$file['tmp_name']}";
            
            // Execute command
            $output = [];
            $return_var = 0;
            exec($command, $output, $return_var);
            
            if ($return_var === 0) {
                $success_message = 'Database restored successfully from: ' . $file['name'];
            } else {
                $error_message = 'Error restoring database. Please check if mysql client is available on the server.';
            }
        } else {
            $error_message = 'Invalid file format. Only SQL files are allowed.';
        }
    } else {
        $error_message = 'Error uploading file: ' . $file['error'];
    }
}

// Get list of existing backups
$backups = [];
$backup_path = '../backups/';
if (file_exists($backup_path)) {
    $files = scandir($backup_path);
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..' && pathinfo($file, PATHINFO_EXTENSION) === 'sql') {
            $backups[] = [
                'name' => $file,
                'size' => filesize($backup_path . $file),
                'date' => filemtime($backup_path . $file)
            ];
        }
    }
    
    // Sort backups by date (newest first)
    usort($backups, function($a, $b) {
        return $b['date'] - $a['date'];
    });
}

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title d-flex align-items-center">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>ব্যাকআপ এবং রিস্টোর</h1>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <?php if($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>
        
        <?php if($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="row">
            <!-- Backup Database -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="fw-bold text-primary mb-0">ডাটাবেস ব্যাকআপ</h6>
                    </div>
                    <div class="card-body">
                        <p>ডাটাবেস ব্যাকআপ করুন যাতে আপনি প্রয়োজনে এটি পুনরুদ্ধার করতে পারেন। ব্যাকআপ ফাইলটি <code>backups</code> ফোল্ডারে সংরক্ষণ করা হবে।</p>
                        <form method="POST" action="">
                            <div class="d-grid">
                                <button type="submit" name="backup_database" class="btn btn-primary">
                                    <i class="fas fa-database me-2"></i>ডাটাবেস ব্যাকআপ করুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Restore Database -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="fw-bold text-primary mb-0">ডাটাবেস রিস্টোর</h6>
                    </div>
                    <div class="card-body">
                        <p class="text-danger fw-bold">সতর্কতা: ডাটাবেস রিস্টোর করলে বর্তমান ডাটা মুছে যাবে এবং ব্যাকআপ ফাইল থেকে ডাটা পুনরুদ্ধার করা হবে।</p>
                        <form method="POST" action="" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="backup_file" class="form-label">ব্যাকআপ ফাইল নির্বাচন করুন</label>
                                <input type="file" class="form-control" id="backup_file" name="backup_file" accept=".sql" required>
                                <div class="form-text">শুধুমাত্র .sql ফাইল আপলোড করুন।</div>
                            </div>
                            <div class="d-grid">
                                <button type="submit" name="restore_database" class="btn btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি ডাটাবেস রিস্টোর করতে চান? এটি বর্তমান ডাটা মুছে ফেলবে।');">
                                    <i class="fas fa-undo-alt me-2"></i>ডাটাবেস রিস্টোর করুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Existing Backups -->
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="fw-bold text-primary mb-0">বিদ্যমান ব্যাকআপ</h6>
                    </div>
                    <div class="card-body">
                        <?php if(count($backups) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead>
                                    <tr>
                                        <th>ফাইল নাম</th>
                                        <th>সাইজ</th>
                                        <th>তারিখ</th>
                                        <th>অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($backups as $backup): ?>
                                    <tr>
                                        <td><?php echo $backup['name']; ?></td>
                                        <td><?php echo formatFileSize($backup['size']); ?></td>
                                        <td><?php echo date('Y-m-d H:i:s', $backup['date']); ?></td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="<?php echo SITE_URL; ?>/backups/<?php echo $backup['name']; ?>" class="btn btn-sm btn-primary" download>
                                                    <i class="fas fa-download"></i>
                                                </a>
                                                <a href="backup.php?delete=<?php echo $backup['name']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই ব্যাকআপ ফাইলটি মুছতে চান?');">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="text-center p-4">
                            <i class="fas fa-database fa-3x text-muted mb-3"></i>
                            <p>কোন ব্যাকআপ ফাইল পাওয়া যায়নি।</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Helper function to format file size
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $i = 0;
    while ($bytes >= 1024 && $i < count($units) - 1) {
        $bytes /= 1024;
        $i++;
    }
    return round($bytes, 2) . ' ' . $units[$i];
}

// Include footer
require_once 'includes/footer.php';
?>
