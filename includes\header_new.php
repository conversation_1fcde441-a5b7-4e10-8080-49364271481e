<?php
require_once 'config.php';

// Check if user is premium
$is_premium = false;
if (isLoggedIn()) {
    // Check from session first
    if (isset($_SESSION['is_premium'])) {
        $is_premium = $_SESSION['is_premium'];
    } else {
        // Check from database
        $user_id = $_SESSION['user_id'];
        $premium_query = "SELECT COUNT(*) as count FROM subscriptions
                         WHERE user_id = $user_id AND status = 'active' AND end_date > NOW()";
        $premium_result = mysqli_query($conn, $premium_query);
        $premium_count = mysqli_fetch_assoc($premium_result)['count'];

        $is_premium = $premium_count > 0;
        $_SESSION['is_premium'] = $is_premium;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="site-url" content="<?php echo SITE_URL; ?>">
    <title><?php echo SITE_NAME; ?> - Stream Movies & TV Shows</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Owl Carousel -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="<?php echo SITE_URL; ?>">
                <span class="text-danger fw-bold">MOVIE</span><span class="text-light">FLIX</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>/movies.php">Movies</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>/tvshows.php">TV Shows</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>/premium.php">
                            Premium
                            <?php if(!$is_premium): ?>
                            <span class="badge bg-danger">New</span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <?php if(isLoggedIn()): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>/watchlist.php">My List</a>
                    </li>
                    <?php if($is_premium): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>/premium_content.php">
                            <i class="fas fa-crown text-warning"></i> Premium Content
                        </a>
                    </li>
                    <?php endif; ?>
                    <?php endif; ?>
                </ul>
                <div class="d-flex align-items-center">
                    <form class="d-flex me-3" action="<?php echo SITE_URL; ?>/search.php" method="GET">
                        <input class="form-control me-2 search-input" type="search" name="query" placeholder="Search" aria-label="Search">
                        <button class="btn btn-outline-danger" type="submit"><i class="fas fa-search"></i></button>
                    </form>
                    <?php if(isLoggedIn()): ?>
                        <div class="dropdown">
                            <a class="nav-link dropdown-toggle text-white" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $_SESSION['profile_image']; ?>" alt="Profile" class="rounded-circle profile-img-small me-2">
                                <?php echo $_SESSION['username']; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/profile.php">Profile</a></li>
                                <?php if($is_premium): ?>
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/premium_content.php">
                                    <i class="fas fa-crown text-warning"></i> Premium Content
                                </a></li>
                                <?php else: ?>
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/premium.php">
                                    <span class="badge bg-danger me-1">New</span> Upgrade to Premium
                                </a></li>
                                <?php endif; ?>
                                <?php if(isAdmin()): ?>
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/admin/">Admin Panel</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/logout.php">Logout</a></li>
                            </ul>
                        </div>
                    <?php else: ?>
                        <a href="<?php echo SITE_URL; ?>/login.php" class="btn btn-outline-light me-2">Login</a>
                        <a href="<?php echo SITE_URL; ?>/register.php" class="btn btn-danger">Sign Up</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>
    <!-- Main Content -->
    <main>
