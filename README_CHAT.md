# লাইভ চ্যাট সিস্টেম ইনস্টলেশন নির্দেশনা

## ডাটাবেস সেটআপ

নিম্নলিখিত SQL কোড phpMyAdmin বা MySQL কমান্ড লাইন ব্যবহার করে চালান:

```sql
-- Chat sessions table
CREATE TABLE IF NOT EXISTS chat_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    admin_id INT DEFAULT NULL,
    status ENUM('active', 'closed') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Chat messages table
CREATE TABLE IF NOT EXISTS chat_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id INT NOT NULL,
    sender_id INT NOT NULL,
    receiver_id INT NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE
);
```

## ফাইল সেটআপ

সমস্ত ফাইল আপনার সার্ভারে আপলোড করুন। নিম্নলিখিত ফাইলগুলি যুক্ত করা হয়েছে:

1. `chat_server.php` - চ্যাট সার্ভার ফাংশনালিটি
2. `admin/live_chat.php` - এডমিনদের চ্যাট ম্যানেজ করার জন্য

## ব্যবহার নির্দেশনা

### ইউজারদের জন্য:
1. হোম পেজের নিচে ডান দিকে একটি ফ্লোটিং চ্যাট আইকন দেখতে পাবেন
2. আইকনে ক্লিক করে চ্যাট বক্স ওপেন করুন
3. মেসেজ লিখে পাঠান
4. এডমিনের উত্তর রিয়েল-টাইমে দেখতে পাবেন

### এডমিনদের জন্য:
1. এডমিন প্যানেলে "Live Chat" মেনুতে ক্লিক করুন
2. সমস্ত অ্যাকটিভ চ্যাট সেশন দেখুন
3. ইউজারদের সাথে রিয়েল-টাইমে চ্যাট করুন
4. অপঠিত মেসেজের জন্য একটি লাল ব্যাজ দেখাবে

## সীমাবদ্ধতা

- এই চ্যাট সিস্টেম AJAX পোলিং ব্যবহার করে, যা WebSocket এর মতো সম্পূর্ণ রিয়েল-টাইম নয়
- প্রতি 3 সেকেন্ডে নতুন মেসেজের জন্য চেক করে
- শুধুমাত্র লগইন করা ইউজাররা চ্যাট করতে পারবেন
