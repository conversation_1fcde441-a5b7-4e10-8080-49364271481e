<?php
// Setup script for configuring the site on a live server
// This file should be deleted after use for security reasons

// Start session
session_start();

// Define constants
define('CONFIG_FILE', 'includes/config.php');

// Check if form is submitted
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $db_server = isset($_POST['db_server']) ? trim($_POST['db_server']) : '';
    $db_username = isset($_POST['db_username']) ? trim($_POST['db_username']) : '';
    $db_password = isset($_POST['db_password']) ? trim($_POST['db_password']) : '';
    $db_name = isset($_POST['db_name']) ? trim($_POST['db_name']) : '';
    $site_name = isset($_POST['site_name']) ? trim($_POST['site_name']) : 'MovieFlix';
    
    // Validate form data
    if (empty($db_server) || empty($db_username) || empty($db_name)) {
        $error_message = 'Please fill in all required fields.';
    } else {
        // Test database connection
        $conn = @mysqli_connect($db_server, $db_username, $db_password, $db_name);
        
        if (!$conn) {
            $error_message = 'Database connection failed: ' . mysqli_connect_error();
        } else {
            // Connection successful, update config file
            $config_content = file_get_contents(CONFIG_FILE);
            
            // Update database settings
            $config_content = preg_replace('/define\(\'DB_SERVER\',\s*\'.*?\'\);/', "define('DB_SERVER', '$db_server');", $config_content);
            $config_content = preg_replace('/define\(\'DB_USERNAME\',\s*\'.*?\'\);/', "define('DB_USERNAME', '$db_username');", $config_content);
            $config_content = preg_replace('/define\(\'DB_PASSWORD\',\s*\'.*?\'\);/', "define('DB_PASSWORD', '$db_password');", $config_content);
            $config_content = preg_replace('/define\(\'DB_NAME\',\s*\'.*?\'\);/', "define('DB_NAME', '$db_name');", $config_content);
            
            // Update site name if provided
            if (!empty($site_name)) {
                $config_content = preg_replace('/define\(\'SITE_NAME\',\s*\'.*?\'\);/', "define('SITE_NAME', '$site_name');", $config_content);
            }
            
            // Write updated content back to file
            if (file_put_contents(CONFIG_FILE, $config_content)) {
                $success_message = 'Configuration updated successfully. Please delete this setup file for security reasons.';
                
                // Test if the site is working with the new configuration
                $test_conn = @mysqli_connect($db_server, $db_username, $db_password, $db_name);
                if (!$test_conn) {
                    $error_message = 'Warning: Configuration saved but database connection test failed. Please check your settings.';
                }
            } else {
                $error_message = 'Error updating configuration file. Make sure the file is writable.';
            }
            
            // Close connection
            mysqli_close($conn);
        }
    }
}

// Get current settings
$current_settings = array(
    'db_server' => '',
    'db_username' => '',
    'db_password' => '',
    'db_name' => '',
    'site_name' => ''
);

if (file_exists(CONFIG_FILE)) {
    $config_content = file_get_contents(CONFIG_FILE);
    
    // Extract current settings using regex
    if (preg_match('/define\(\'DB_SERVER\',\s*\'(.*?)\'\);/', $config_content, $matches)) {
        $current_settings['db_server'] = $matches[1];
    }
    
    if (preg_match('/define\(\'DB_USERNAME\',\s*\'(.*?)\'\);/', $config_content, $matches)) {
        $current_settings['db_username'] = $matches[1];
    }
    
    if (preg_match('/define\(\'DB_PASSWORD\',\s*\'(.*?)\'\);/', $config_content, $matches)) {
        $current_settings['db_password'] = $matches[1];
    }
    
    if (preg_match('/define\(\'DB_NAME\',\s*\'(.*?)\'\);/', $config_content, $matches)) {
        $current_settings['db_name'] = $matches[1];
    }
    
    if (preg_match('/define\(\'SITE_NAME\',\s*\'(.*?)\'\);/', $config_content, $matches)) {
        $current_settings['site_name'] = $matches[1];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MovieFlix Setup</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #141414;
            color: #ffffff;
            font-family: 'Helvetica Neue', Arial, sans-serif;
            padding-top: 50px;
        }
        .setup-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #1f1f1f;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        .setup-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .setup-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
        }
        .setup-header h1 .text-danger {
            color: #e50914 !important;
        }
        .form-label {
            color: #e5e5e5;
            font-weight: 500;
        }
        .form-control {
            background-color: #333;
            border: 1px solid #444;
            color: #fff;
            padding: 10px 15px;
        }
        .form-control:focus {
            background-color: #444;
            color: #fff;
            border-color: #e50914;
            box-shadow: 0 0 0 0.25rem rgba(229, 9, 20, 0.25);
        }
        .btn-primary {
            background-color: #e50914;
            border-color: #e50914;
            padding: 10px 20px;
            font-weight: 600;
        }
        .btn-primary:hover, .btn-primary:focus {
            background-color: #b30710;
            border-color: #b30710;
        }
        .text-muted {
            color: #aaa !important;
        }
        .alert-success {
            background-color: #28a745;
            color: #fff;
            border: none;
        }
        .alert-danger {
            background-color: #dc3545;
            color: #fff;
            border: none;
        }
        .setup-footer {
            text-align: center;
            margin-top: 30px;
            color: #aaa;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="setup-container">
            <div class="setup-header">
                <h1><span class="text-danger">MOVIE</span>FLIX</h1>
                <p class="lead">Setup Configuration</p>
            </div>
            
            <?php if (!empty($success_message)): ?>
            <div class="alert alert-success mb-4">
                <?php echo $success_message; ?>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($error_message)): ?>
            <div class="alert alert-danger mb-4">
                <?php echo $error_message; ?>
            </div>
            <?php endif; ?>
            
            <form method="post" action="">
                <div class="mb-4">
                    <h4>Database Configuration</h4>
                    <p class="text-muted">Enter your database connection details below.</p>
                    
                    <div class="mb-3">
                        <label for="db_server" class="form-label">Database Server <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="db_server" name="db_server" value="<?php echo htmlspecialchars($current_settings['db_server']); ?>" required>
                        <small class="text-muted">Usually 'localhost'</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="db_username" class="form-label">Database Username <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="db_username" name="db_username" value="<?php echo htmlspecialchars($current_settings['db_username']); ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="db_password" class="form-label">Database Password</label>
                        <input type="password" class="form-control" id="db_password" name="db_password" value="<?php echo htmlspecialchars($current_settings['db_password']); ?>">
                    </div>
                    
                    <div class="mb-3">
                        <label for="db_name" class="form-label">Database Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="db_name" name="db_name" value="<?php echo htmlspecialchars($current_settings['db_name']); ?>" required>
                    </div>
                </div>
                
                <div class="mb-4">
                    <h4>Site Configuration</h4>
                    <p class="text-muted">Customize your site settings.</p>
                    
                    <div class="mb-3">
                        <label for="site_name" class="form-label">Site Name</label>
                        <input type="text" class="form-control" id="site_name" name="site_name" value="<?php echo htmlspecialchars($current_settings['site_name']); ?>">
                    </div>
                </div>
                
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-lg">Save Configuration</button>
                </div>
            </form>
            
            <div class="setup-footer">
                <p>After setup is complete, please delete this file for security reasons.</p>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
