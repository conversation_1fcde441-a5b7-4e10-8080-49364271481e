<?php
require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Set current page for sidebar
$current_page = 'manage_episodes.php';

// Get TV show ID from URL
$tvshow_id = isset($_GET['tvshow']) ? (int)$_GET['tvshow'] : 0;
$season_number = isset($_GET['season']) ? (int)$_GET['season'] : 0;

// Get TV show details
$tvshow_query = "SELECT * FROM tvshows WHERE id = $tvshow_id";
$tvshow_result = mysqli_query($conn, $tvshow_query);

if (mysqli_num_rows($tvshow_result) == 0) {
    redirect(SITE_URL . '/admin/tvshows.php');
}

$tvshow = mysqli_fetch_assoc($tvshow_result);

// Process form submissions
$success_message = '';
$error_message = '';

// Add Season
if (isset($_POST['add_season'])) {
    $new_season_number = (int)$_POST['new_season_number'];
    
    if ($new_season_number <= 0) {
        $error_message = 'Please enter a valid season number.';
    } else {
        // Check if season already exists
        $check_season_query = "SELECT COUNT(*) as count FROM episodes WHERE tvshow_id = $tvshow_id AND season_number = $new_season_number";
        $check_season_result = mysqli_query($conn, $check_season_query);
        $season_exists = mysqli_fetch_assoc($check_season_result)['count'] > 0;
        
        if ($season_exists) {
            $error_message = 'Season ' . $new_season_number . ' already exists.';
        } else {
            // Create a placeholder episode for the season
            $placeholder_query = "INSERT INTO episodes (tvshow_id, season_number, episode_number, title, description, is_premium) 
                                 VALUES ($tvshow_id, $new_season_number, 1, 'Episode 1', 'Placeholder episode for Season $new_season_number', 0)";
            
            if (mysqli_query($conn, $placeholder_query)) {
                $success_message = 'Season ' . $new_season_number . ' created successfully with a placeholder episode.';
            } else {
                $error_message = 'Error creating season: ' . mysqli_error($conn);
            }
        }
    }
}

// Add/Edit Episode
if (isset($_POST['add_episode'])) {
    $season_number = (int)$_POST['season_number'];
    $episode_number = (int)$_POST['episode_number'];
    $title = sanitize($_POST['title']);
    $description = sanitize($_POST['description']);
    $duration = (int)$_POST['duration'];
    $release_date = sanitize($_POST['release_date']);
    $is_premium = isset($_POST['is_premium']) ? 1 : 0;
    $episode_id = isset($_POST['episode_id']) ? (int)$_POST['episode_id'] : 0;

    // Handle thumbnail upload
    $thumbnail = '';
    if (isset($_FILES['thumbnail']) && $_FILES['thumbnail']['error'] == 0) {
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        $file_type = $_FILES['thumbnail']['type'];
        
        if (in_array($file_type, $allowed_types)) {
            $file_extension = pathinfo($_FILES['thumbnail']['name'], PATHINFO_EXTENSION);
            $thumbnail = 'episode_' . $tvshow_id . '_s' . $season_number . 'e' . $episode_number . '_' . time() . '.' . $file_extension;
            $upload_path = '../uploads/' . $thumbnail;
            
            if (move_uploaded_file($_FILES['thumbnail']['tmp_name'], $upload_path)) {
                // Success
            } else {
                $error_message = 'Error uploading thumbnail.';
            }
        } else {
            $error_message = 'Invalid file type. Only JPG, PNG, and WebP are allowed.';
        }
    }

    if (empty($title) || $season_number <= 0 || $episode_number <= 0) {
        $error_message = 'Please fill in all required fields.';
    } else {
        if ($episode_id > 0) {
            // Update existing episode
            $update_query = "UPDATE episodes SET
                            season_number = $season_number,
                            episode_number = $episode_number,
                            title = '$title',
                            description = '$description',
                            duration = $duration,
                            release_date = '$release_date',
                            is_premium = $is_premium";
            
            if (!empty($thumbnail)) {
                $update_query .= ", thumbnail = '$thumbnail'";
            }
            
            $update_query .= " WHERE id = $episode_id AND tvshow_id = $tvshow_id";

            if (mysqli_query($conn, $update_query)) {
                $success_message = 'Episode updated successfully.';
            } else {
                $error_message = 'Error updating episode: ' . mysqli_error($conn);
            }
        } else {
            // Add new episode
            $insert_query = "INSERT INTO episodes (tvshow_id, season_number, episode_number, title, description, duration, thumbnail, release_date, is_premium)
                            VALUES ($tvshow_id, $season_number, $episode_number, '$title', '$description', $duration, '$thumbnail', '$release_date', $is_premium)";

            if (mysqli_query($conn, $insert_query)) {
                $success_message = 'Episode added successfully.';
            } else {
                $error_message = 'Error adding episode: ' . mysqli_error($conn);
            }
        }
    }
}

// Delete Episode
if (isset($_GET['delete_episode']) && $_GET['delete_episode'] > 0) {
    $episode_id = (int)$_GET['delete_episode'];

    $delete_query = "DELETE FROM episodes WHERE id = $episode_id AND tvshow_id = $tvshow_id";

    if (mysqli_query($conn, $delete_query)) {
        $success_message = 'Episode deleted successfully.';
    } else {
        $error_message = 'Error deleting episode: ' . mysqli_error($conn);
    }
}

// Get episodes
$episodes_query = "SELECT * FROM episodes WHERE tvshow_id = $tvshow_id";
if ($season_number > 0) {
    $episodes_query .= " AND season_number = $season_number";
}
$episodes_query .= " ORDER BY season_number ASC, episode_number ASC";
$episodes_result = mysqli_query($conn, $episodes_query);

// Get episode to edit if specified
$edit_episode = null;

if (isset($_GET['edit_episode']) && $_GET['edit_episode'] > 0) {
    $episode_id = (int)$_GET['edit_episode'];

    $edit_query = "SELECT * FROM episodes WHERE id = $episode_id AND tvshow_id = $tvshow_id";
    $edit_result = mysqli_query($conn, $edit_query);

    if (mysqli_num_rows($edit_result) > 0) {
        $edit_episode = mysqli_fetch_assoc($edit_result);
    }
}

// Get seasons for this TV show
$seasons_query = "SELECT DISTINCT season_number FROM episodes WHERE tvshow_id = $tvshow_id ORDER BY season_number ASC";
$seasons_result = mysqli_query($conn, $seasons_query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Episodes - <?php echo SITE_NAME; ?> Admin</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Sidebar Responsive CSS -->
    <link rel="stylesheet" href="assets/css/sidebar-responsive.css">
    <!-- Custom CSS -->
    <style>
        body {
            background-color: #f8f9fa;
        }
        .main-content {
            margin-left: 0;
            padding: 20px;
            flex: 1;
        }
        .card {
            border: none;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            border-radius: 10px;
        }
        .card-header {
            border-radius: 10px 10px 0 0 !important;
            border-bottom: none;
        }
        .btn {
            border-radius: 8px;
            font-weight: 500;
        }
        .table {
            border-radius: 8px;
            overflow: hidden;
        }
        .episode-thumbnail {
            width: 60px;
            height: 40px;
            object-fit: cover;
            border-radius: 5px;
        }
        .season-filter {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .badge {
            border-radius: 6px;
            font-weight: 500;
        }
        .alert {
            border-radius: 10px;
            border: none;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .col-lg-4 {
                flex: 0 0 100%;
                max-width: 100%;
            }
            .col-lg-8 {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 15px;
            }
            
            .d-flex.justify-content-between {
                flex-direction: column;
                gap: 15px;
            }
            
            .d-flex.justify-content-between .btn {
                width: 100%;
                margin-bottom: 10px;
            }
            
            .season-filter .row {
                flex-direction: column;
                gap: 15px;
            }
            
            .season-filter .col-md-4,
            .season-filter .col-md-5,
            .season-filter .col-md-3 {
                flex: 0 0 100%;
                max-width: 100%;
            }
            
            .season-filter .d-flex.gap-2 {
                flex-wrap: wrap;
                gap: 5px !important;
            }
            
            .season-filter .btn {
                font-size: 12px;
                padding: 5px 10px;
            }
            
            .table-responsive {
                font-size: 14px;
            }
            
            .table th,
            .table td {
                padding: 8px 5px;
            }
            
            .btn-group .btn {
                padding: 4px 8px;
                font-size: 12px;
            }
            
            .episode-thumbnail {
                width: 50px;
                height: 30px;
            }
            
            .card-body {
                padding: 15px;
            }
            
            .form-control,
            .form-select {
                font-size: 14px;
            }
        }

        @media (max-width: 576px) {
            .main-content {
                padding: 10px;
            }
            
            h1 {
                font-size: 1.5rem;
            }
            
            .card-header h5 {
                font-size: 1rem;
            }
            
            .table {
                font-size: 12px;
            }
            
            .table th,
            .table td {
                padding: 5px 3px;
            }
            
            .btn {
                font-size: 12px;
                padding: 6px 12px;
            }
            
            .badge {
                font-size: 10px;
            }
            
            .modal-dialog {
                margin: 10px;
            }
            
            .modal-body {
                padding: 15px;
            }
            
            .season-filter {
                padding: 10px;
            }
            
            .season-filter h5 {
                font-size: 1rem;
            }
        }

        /* Mobile-first table improvements */
        @media (max-width: 768px) {
            .table-responsive {
                border: none;
            }
            
            .table thead {
                display: none;
            }
            
            .table tbody tr {
                display: block;
                margin-bottom: 15px;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
                background: white;
            }
            
            .table tbody td {
                display: block;
                text-align: left;
                border: none;
                padding: 5px 0;
            }
            
            .table tbody td:before {
                content: attr(data-label) ": ";
                font-weight: bold;
                color: #495057;
            }
            
            .table tbody td.actions {
                text-align: center;
                margin-top: 10px;
            }
            
            .table tbody td.actions:before {
                content: "";
            }
        }

    </style>
</head>
<body>
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="mb-1">Manage Episodes</h1>
                    <p class="text-muted mb-0"><?php echo $tvshow['title']; ?></p>
                </div>
                <div>
                    <a href="tvshows.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to TV Shows
                    </a>
                    <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $tvshow_id; ?>" class="btn btn-primary" target="_blank">
                        <i class="fas fa-eye me-2"></i>View TV Show
                    </a>
                </div>
            </div>

            <?php if($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <?php if($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <!-- Season Filter and Add Season -->
            <div class="season-filter">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>Filter by Season
                        </h5>
                    </div>
                    <div class="col-md-5">
                        <div class="d-flex gap-2">
                            <a href="manage_episodes.php?tvshow=<?php echo $tvshow_id; ?>" class="btn btn-light btn-sm <?php echo $season_number == 0 ? 'active' : ''; ?>">
                                All Seasons
                            </a>
                            <?php while($season = mysqli_fetch_assoc($seasons_result)): ?>
                            <a href="manage_episodes.php?tvshow=<?php echo $tvshow_id; ?>&season=<?php echo $season['season_number']; ?>" 
                               class="btn btn-light btn-sm <?php echo $season_number == $season['season_number'] ? 'active' : ''; ?>">
                                Season <?php echo $season['season_number']; ?>
                            </a>
                            <?php endwhile; ?>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-warning btn-sm" data-bs-toggle="modal" data-bs-target="#addSeasonModal">
                            <i class="fas fa-plus me-1"></i>Add New Season
                        </button>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Add/Edit Episode Form -->
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-plus me-2"></i>
                                <?php echo $edit_episode ? 'Edit Episode' : 'Add New Episode'; ?>
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="" enctype="multipart/form-data">
                                <input type="hidden" name="episode_id" value="<?php echo $edit_episode ? $edit_episode['id'] : ''; ?>">
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="season_number" class="form-label">Season Number</label>
                                        <input type="number" class="form-control" id="season_number" name="season_number" 
                                               value="<?php echo $edit_episode ? $edit_episode['season_number'] : ($season_number > 0 ? $season_number : ''); ?>" 
                                               min="1" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="episode_number" class="form-label">Episode Number</label>
                                        <input type="number" class="form-control" id="episode_number" name="episode_number" 
                                               value="<?php echo $edit_episode ? $edit_episode['episode_number'] : ''; ?>" 
                                               min="1" required>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="title" class="form-label">Episode Title</label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="<?php echo $edit_episode ? $edit_episode['title'] : ''; ?>" required>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control" id="description" name="description" rows="3"><?php echo $edit_episode ? $edit_episode['description'] : ''; ?></textarea>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="duration" class="form-label">Duration (minutes)</label>
                                        <input type="number" class="form-control" id="duration" name="duration" 
                                               value="<?php echo $edit_episode ? $edit_episode['duration'] : ''; ?>" 
                                               min="1" placeholder="e.g. 45">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="release_date" class="form-label">Release Date</label>
                                        <input type="date" class="form-control" id="release_date" name="release_date" 
                                               value="<?php echo $edit_episode ? $edit_episode['release_date'] : ''; ?>">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="thumbnail" class="form-label">Thumbnail</label>
                                    <input type="file" class="form-control" id="thumbnail" name="thumbnail" accept="image/*">
                                    <small class="form-text text-muted">JPG, PNG, or WebP format. Recommended size: 400x225px</small>
                                    <?php if($edit_episode && $edit_episode['thumbnail']): ?>
                                    <div class="mt-2">
                                        <img src="../uploads/<?php echo $edit_episode['thumbnail']; ?>" alt="Current thumbnail" 
                                             class="episode-thumbnail" style="width: 100px; height: 60px;">
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="is_premium" name="is_premium" 
                                           <?php echo ($edit_episode && $edit_episode['is_premium']) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_premium">Premium Only</label>
                                </div>

                                <div class="d-grid gap-2">
                                    <button type="submit" name="add_episode" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        <?php echo $edit_episode ? 'Update Episode' : 'Add Episode'; ?>
                                    </button>
                                    <?php if($edit_episode): ?>
                                    <a href="manage_episodes.php?tvshow=<?php echo $tvshow_id; ?><?php echo $season_number > 0 ? '&season=' . $season_number : ''; ?>" 
                                       class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Episodes List -->
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2"></i>Episodes List
                                </h5>
                                <span class="badge bg-light text-dark">
                                    <?php echo mysqli_num_rows($episodes_result); ?> episodes
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if(mysqli_num_rows($episodes_result) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Thumbnail</th>
                                            <th>Episode</th>
                                            <th>Title</th>
                                            <th>Duration</th>
                                            <th>Release Date</th>
                                            <th>Premium</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while($episode = mysqli_fetch_assoc($episodes_result)): ?>
                                        <tr>
                                            <td data-label="Thumbnail">
                                                <?php if($episode['thumbnail']): ?>
                                                <img src="../uploads/<?php echo $episode['thumbnail']; ?>" alt="Episode thumbnail" class="episode-thumbnail">
                                                <?php else: ?>
                                                <div class="episode-thumbnail bg-secondary d-flex align-items-center justify-content-center">
                                                    <i class="fas fa-image text-white"></i>
                                                </div>
                                                <?php endif; ?>
                                            </td>
                                            <td data-label="Episode">
                                                <strong>S<?php echo $episode['season_number']; ?>E<?php echo $episode['episode_number']; ?></strong>
                                            </td>
                                            <td data-label="Title">
                                                <div>
                                                    <strong><?php echo $episode['title']; ?></strong>
                                                    <?php if($episode['description']): ?>
                                                    <br><small class="text-muted"><?php echo substr($episode['description'], 0, 50); ?>...</small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td data-label="Duration">
                                                <?php if($episode['duration']): ?>
                                                <span class="badge bg-info"><?php echo $episode['duration']; ?> min</span>
                                                <?php else: ?>
                                                <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td data-label="Release Date">
                                                <?php if($episode['release_date']): ?>
                                                <small><?php echo date('M d, Y', strtotime($episode['release_date'])); ?></small>
                                                <?php else: ?>
                                                <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td data-label="Premium">
                                                <?php if($episode['is_premium']): ?>
                                                <span class="badge bg-danger">Premium</span>
                                                <?php else: ?>
                                                <span class="badge bg-secondary">Free</span>
                                                <?php endif; ?>
                                            </td>
                                            <td data-label="Actions" class="actions">
                                                <div class="btn-group" role="group">
                                                    <a href="manage_episodes.php?tvshow=<?php echo $tvshow_id; ?>&edit_episode=<?php echo $episode['id']; ?><?php echo $season_number > 0 ? '&season=' . $season_number : ''; ?>" 
                                                       class="btn btn-sm btn-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="manage_episode_links.php?episode=<?php echo $episode['id']; ?>" 
                                                       class="btn btn-sm btn-info" title="Manage Links">
                                                        <i class="fas fa-link"></i>
                                                    </a>
                                                    <a href="manage_episodes.php?tvshow=<?php echo $tvshow_id; ?>&delete_episode=<?php echo $episode['id']; ?><?php echo $season_number > 0 ? '&season=' . $season_number : ''; ?>" 
                                                       class="btn btn-sm btn-danger" 
                                                       onclick="return confirm('Are you sure you want to delete this episode? This action cannot be undone.')" 
                                                       title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-tv fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No episodes found</h5>
                                <p class="text-muted">Start by adding episodes using the form on the left.</p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Seasons Overview Section -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-layer-group me-2"></i>Seasons Overview
                        </h5>
                        <span class="badge bg-light text-dark">
                            <?php 
                            $total_episodes_query = "SELECT COUNT(*) as total FROM episodes WHERE tvshow_id = $tvshow_id";
                            $total_episodes_result = mysqli_query($conn, $total_episodes_query);
                            $total_episodes = mysqli_fetch_assoc($total_episodes_result)['total'];
                            echo $total_episodes . ' total episodes';
                            ?>
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <?php
                    // Reset seasons result for reuse
                    mysqli_data_seek($seasons_result, 0);
                    
                    if(mysqli_num_rows($seasons_result) > 0):
                    ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Season</th>
                                    <th>Episodes</th>
                                    <th>Duration</th>
                                    <th>Premium Episodes</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while($season = mysqli_fetch_assoc($seasons_result)): 
                                    // Get episode count for this season
                                    $season_episodes_query = "SELECT COUNT(*) as episode_count, 
                                                             SUM(duration) as total_duration,
                                                             SUM(CASE WHEN is_premium = 1 THEN 1 ELSE 0 END) as premium_count
                                                             FROM episodes 
                                                             WHERE tvshow_id = $tvshow_id AND season_number = " . $season['season_number'];
                                    $season_episodes_result = mysqli_query($conn, $season_episodes_query);
                                    $season_stats = mysqli_fetch_assoc($season_episodes_result);
                                ?>
                                <tr>
                                    <td data-label="Season">
                                        <strong>Season <?php echo $season['season_number']; ?></strong>
                                    </td>
                                    <td data-label="Episodes">
                                        <span class="badge bg-primary"><?php echo $season_stats['episode_count']; ?> episodes</span>
                                    </td>
                                    <td data-label="Duration">
                                        <?php if($season_stats['total_duration']): ?>
                                        <span class="badge bg-info"><?php echo $season_stats['total_duration']; ?> min</span>
                                        <?php else: ?>
                                        <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td data-label="Premium Episodes">
                                        <?php if($season_stats['premium_count'] > 0): ?>
                                        <span class="badge bg-danger"><?php echo $season_stats['premium_count']; ?> premium</span>
                                        <?php else: ?>
                                        <span class="badge bg-success">All free</span>
                                        <?php endif; ?>
                                    </td>
                                    <td data-label="Actions" class="actions">
                                        <div class="btn-group" role="group">
                                            <a href="manage_episodes.php?tvshow=<?php echo $tvshow_id; ?>&season=<?php echo $season['season_number']; ?>" 
                                               class="btn btn-sm btn-primary" title="View Episodes">
                                                <i class="fas fa-eye me-1"></i> View
                                            </a>
                                            <a href="manage_episodes.php?tvshow=<?php echo $tvshow_id; ?>&season=<?php echo $season['season_number']; ?>" 
                                               class="btn btn-sm btn-success" title="Add Episode">
                                                <i class="fas fa-plus me-1"></i> Add Episode
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No seasons found</h5>
                        <p class="text-muted">Start by adding episodes to create seasons.</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Season Modal -->
    <div class="modal fade" id="addSeasonModal" tabindex="-1" aria-labelledby="addSeasonModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="addSeasonModalLabel">
                        <i class="fas fa-plus me-2"></i>Add New Season
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="new_season_number" class="form-label">Season Number</label>
                            <input type="number" class="form-control" id="new_season_number" name="new_season_number" 
                                   min="1" required placeholder="e.g. 2, 3, 4...">
                            <small class="form-text text-muted">Enter the season number you want to create.</small>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Note:</strong> A placeholder episode will be created automatically for the new season.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="add_season" class="btn btn-warning">
                            <i class="fas fa-plus me-2"></i>Create Season
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
</body>
</html>
