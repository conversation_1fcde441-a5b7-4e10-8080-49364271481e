<?php
// Set page title
$page_title = 'Dashboard';

// Define constants for testing
define('SITE_NAME', 'MovieFlix');
define('SITE_URL', 'http://localhost:8000');

// Mock session data
$_SESSION['username'] = 'admin';
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['is_premium'] = 1;

// Mock statistics
$stats = [
    'movies' => 150,
    'premium_movies' => 50,
    'tvshows' => 75,
    'premium_tvshows' => 25,
    'episodes' => 1200,
    'premium_episodes' => 400,
    'users' => 500,
    'premium_users' => 100,
    'pending_payments' => 15,
    'completed_payments' => 85,
    'payments' => 100,
    'categories' => 20,
    'reviews' => 300,
    'revenue' => 15000
];

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/simple_sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">
            <!-- Sidebar Toggle (Topbar) -->
            <button id="sidebarToggle" class="btn btn-link d-md-none rounded-circle mr-3">
                <i class="fa fa-bars"></i>
            </button>

            <!-- Topbar Title -->
            <div class="d-none d-sm-inline-block mr-auto ml-md-3 my-2 my-md-0 mw-100">
                <h1 class="h3 mb-0 text-gray-800">Dashboard</h1>
            </div>

            <!-- Topbar Search -->
            <form class="d-none d-sm-inline-block form-inline ml-auto mr-md-3 my-2 my-md-0 mw-100 navbar-search">
                <div class="input-group">
                    <input type="text" class="form-control bg-light border-0 small" placeholder="Search for..." aria-label="Search" aria-describedby="basic-addon2">
                    <div class="input-group-append">
                        <button class="btn btn-primary" type="button">
                            <i class="fas fa-search fa-sm"></i>
                        </button>
                    </div>
                </div>
            </form>

            <!-- Topbar Navbar -->
            <ul class="navbar-nav ml-auto">
                <!-- Nav Item - User Information -->
                <li class="nav-item dropdown no-arrow">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <span class="mr-2 d-none d-lg-inline text-gray-600 small">admin</span>
                        <img class="img-profile rounded-circle" src="assets/img/default-avatar.png" style="width: 40px; height: 40px;">
                    </a>
                    <!-- Dropdown - User Information -->
                    <div class="dropdown-menu dropdown-menu-end shadow animated--grow-in" aria-labelledby="userDropdown">
                        <a class="dropdown-item" href="#">
                            <i class="fas fa-user fa-sm fa-fw mr-2 text-gray-400"></i>
                            Profile
                        </a>
                        <a class="dropdown-item" href="site_settings.php">
                            <i class="fas fa-cog fa-sm fa-fw mr-2 text-gray-400"></i>
                            Settings
                        </a>
                        <a class="dropdown-item" href="activity_logs.php">
                            <i class="fas fa-list fa-sm fa-fw mr-2 text-gray-400"></i>
                            Activity Log
                        </a>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="#">
                            <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                            Logout
                        </a>
                    </div>
                </li>
            </ul>
        </nav>
    </div>

    <div class="container-fluid">
        <!-- Welcome Card -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-gradient-primary text-white">
                    <div class="card-body p-4">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h2 class="mb-1">Welcome back, admin!</h2>
                                <p class="mb-0">Here's what's happening with your movie site today.</p>
                            </div>
                            <div class="text-end">
                                <h4><?php echo date('l, F j, Y'); ?></h4>
                                <p class="mb-0"><?php echo date('h:i A'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Movies</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['movies']; ?></div>
                                <div class="mt-2 text-xs text-muted">
                                    <span class="text-danger"><?php echo $stats['premium_movies']; ?> Premium</span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-film fa-2x text-primary"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-light py-2">
                        <a href="movies.php" class="text-primary text-decoration-none small">
                            View Details <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">TV Shows</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['tvshows']; ?></div>
                                <div class="mt-2 text-xs text-muted">
                                    <span class="text-danger"><?php echo $stats['premium_tvshows']; ?> Premium</span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-tv fa-2x text-danger"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-light py-2">
                        <a href="tvshows.php" class="text-danger text-decoration-none small">
                            View Details <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Users</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['users']; ?></div>
                                <div class="mt-2 text-xs text-muted">
                                    <span class="text-success"><?php echo $stats['premium_users']; ?> Premium</span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-light py-2">
                        <a href="users.php" class="text-success text-decoration-none small">
                            View Details <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Reviews</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['reviews']; ?></div>
                                <div class="mt-2 text-xs text-muted">
                                    <span class="text-info">User Feedback</span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-star fa-2x text-info"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-light py-2">
                        <a href="reviews.php" class="text-info text-decoration-none small">
                            View Details <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue & Payments Row -->
        <div class="row mb-4">
            <div class="col-xl-4 col-md-6 mb-4">
                <div class="card shadow border-left-warning h-100">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Total Revenue</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">৳<?php echo number_format($stats['revenue'], 0); ?></div>
                                <div class="mt-2 text-xs text-muted">
                                    <span class="text-warning"><?php echo $stats['completed_payments']; ?> Completed Payments</span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-dollar-sign fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-light py-2">
                        <a href="payments.php" class="text-warning text-decoration-none small">
                            View Details <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-xl-4 col-md-6 mb-4">
                <div class="card shadow border-left-danger h-100">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Pending Payments</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['pending_payments']; ?></div>
                                <div class="mt-2 text-xs text-muted">
                                    <span class="text-danger">Require Approval</span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-danger"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-light py-2">
                        <a href="payments.php?status=pending" class="text-danger text-decoration-none small">
                            View Details <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-xl-4 col-md-6 mb-4">
                <div class="card shadow border-left-primary h-100">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Premium Content</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['premium_movies'] + $stats['premium_tvshows']; ?></div>
                                <div class="mt-2 text-xs text-muted">
                                    <span class="text-primary"><?php echo $stats['premium_movies']; ?> Movies, <?php echo $stats['premium_tvshows']; ?> TV Shows</span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-crown fa-2x text-primary"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-light py-2">
                        <a href="manage_premium.php" class="text-primary text-decoration-none small">
                            Manage Premium <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card shadow">
                    <div class="card-header bg-white py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <!-- Content Management -->
                            <div class="col-lg-3 col-md-6">
                                <div class="card bg-gradient-primary text-white shadow">
                                    <div class="card-body">
                                        <div class="text-center">
                                            <i class="fas fa-photo-video fa-3x mb-3"></i>
                                            <h5 class="mb-3">Content Management</h5>
                                            <div class="d-grid gap-2">
                                                <a href="add_movie.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-plus-circle me-1"></i> Add Movie
                                                </a>
                                                <a href="add_tvshow.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-plus-circle me-1"></i> Add TV Show
                                                </a>
                                                <a href="categories.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-tags me-1"></i> Categories
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Import from TMDB -->
                            <div class="col-lg-3 col-md-6">
                                <div class="card bg-gradient-warning text-dark shadow">
                                    <div class="card-body">
                                        <div class="text-center">
                                            <i class="fas fa-file-import fa-3x mb-3"></i>
                                            <h5 class="mb-3">Import from TMDB</h5>
                                            <div class="d-grid gap-2">
                                                <a href="import_tmdb.php" class="btn btn-dark btn-sm">
                                                    <i class="fas fa-film me-1"></i> Import Movies
                                                </a>
                                                <a href="import_tmdb_tvshow.php" class="btn btn-dark btn-sm">
                                                    <i class="fas fa-tv me-1"></i> Import TV Shows
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Premium Management -->
                            <div class="col-lg-3 col-md-6">
                                <div class="card bg-gradient-success text-white shadow">
                                    <div class="card-body">
                                        <div class="text-center">
                                            <i class="fas fa-crown fa-3x mb-3"></i>
                                            <h5 class="mb-3">Premium Management</h5>
                                            <div class="d-grid gap-2">
                                                <a href="premium_plans.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-list-alt me-1"></i> Premium Plans
                                                </a>
                                                <a href="manage_premium.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-gem me-1"></i> Manage Premium
                                                </a>
                                                <a href="payments.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-money-bill-wave me-1"></i> Payments
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- System Management -->
                            <div class="col-lg-3 col-md-6">
                                <div class="card bg-gradient-danger text-white shadow">
                                    <div class="card-body">
                                        <div class="text-center">
                                            <i class="fas fa-cogs fa-3x mb-3"></i>
                                            <h5 class="mb-3">System Management</h5>
                                            <div class="d-grid gap-2">
                                                <a href="site_settings.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-cog me-1"></i> Site Settings
                                                </a>
                                                <a href="backup.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-database me-1"></i> Backup & Restore
                                                </a>
                                                <a href="logs.php" class="btn btn-light btn-sm">
                                                    <i class="fas fa-list me-1"></i> System Logs
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
