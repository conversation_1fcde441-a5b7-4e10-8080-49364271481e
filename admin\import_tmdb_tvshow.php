<?php
// Set page title
$page_title = 'Import TV Shows from TMDB';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Check if TMDB API key is set
if (!defined('TMDB_API_KEY') || empty(TMDB_API_KEY)) {
    $error_message = 'TMDB API key is not set. Please set it in the site settings.';
}

// Process form submissions
$success_message = '';
$error_message = '';
$search_results = [];
$tvshow_details = null;

// Search TV Shows
if (isset($_POST['search_tvshows']) && !empty($_POST['search_query'])) {
    $search_query = sanitize($_POST['search_query']);
    // Replace placeholder values with actual constants
    $api_key = defined('TMDB_API_KEY') ? constant('TMDB_API_KEY') : '';
    $language = defined('TMDB_LANGUAGE') ? constant('TMDB_LANGUAGE') : 'en-US';
    $api_url = "https://api.themoviedb.org/3/search/tv?api_key=" . $api_key . "&language=" . $language . "&query=" . urlencode($search_query) . "&page=1&include_adult=false";

    // Enable error reporting for debugging
    $old_error_reporting = error_reporting(E_ALL);
    $old_display_errors = ini_get('display_errors');
    ini_set('display_errors', 1);

    // Create a stream context with timeout
    $context = stream_context_create([
        'http' => [
            'timeout' => 10, // Timeout in seconds
        ]
    ]);

    try {
        $response = @file_get_contents($api_url, false, $context);

        if ($response !== false) {
            $data = json_decode($response, true);
            if (isset($data['results']) && !empty($data['results'])) {
                $search_results = $data['results'];
            } else {
                $error_message = 'No TV shows found for the search query.';
            }
        } else {
            $error_message = 'Error connecting to TMDB API. URL: ' . $api_url;
        }
    } catch (Exception $e) {
        $error_message = 'Exception: ' . $e->getMessage();
    }

    // Restore error reporting settings
    error_reporting($old_error_reporting);
    ini_set('display_errors', $old_display_errors);
}

// Get TV Show Details
if (isset($_GET['tvshow_id']) && is_numeric($_GET['tvshow_id'])) {
    $tmdb_id = (int)$_GET['tvshow_id'];
    // Replace placeholder values with actual constants
    $api_key = defined('TMDB_API_KEY') ? constant('TMDB_API_KEY') : '';
    $language = defined('TMDB_LANGUAGE') ? constant('TMDB_LANGUAGE') : 'en-US';
    $api_url = "https://api.themoviedb.org/3/tv/$tmdb_id?api_key=" . $api_key . "&language=" . $language . "&append_to_response=credits,videos";

    // Create a stream context with timeout
    $context = stream_context_create([
        'http' => [
            'timeout' => 10, // Timeout in seconds
        ]
    ]);

    try {
        $response = @file_get_contents($api_url, false, $context);

        if ($response !== false) {
            $tvshow_details = json_decode($response, true);
            if (!$tvshow_details) {
                $error_message = 'Error parsing TV show details from TMDB API.';
            }
        } else {
            $error_message = 'Error fetching TV show details from TMDB API. URL: ' . $api_url;
        }
    } catch (Exception $e) {
        $error_message = 'Exception: ' . $e->getMessage();
    }
}

// Create tvshow_genres table if it doesn't exist
$check_tvshow_genres_table = "SHOW TABLES LIKE 'tvshow_genres'";
$tvshow_genres_table_exists = mysqli_query($conn, $check_tvshow_genres_table);
if (mysqli_num_rows($tvshow_genres_table_exists) == 0) {
    $create_tvshow_genres_table = "CREATE TABLE IF NOT EXISTS tvshow_genres (
        id INT AUTO_INCREMENT PRIMARY KEY,
        tvshow_id INT NOT NULL,
        genre_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (tvshow_id) REFERENCES tvshows(id) ON DELETE CASCADE,
        FOREIGN KEY (genre_id) REFERENCES categories(id) ON DELETE CASCADE,
        UNIQUE KEY unique_tvshow_genre (tvshow_id, genre_id)
    )";
    mysqli_query($conn, $create_tvshow_genres_table);
}

// Direct import from search results
if (isset($_POST['direct_import_from_search']) && !empty($_POST['tmdb_id'])) {
    $tmdb_id = (int)$_POST['tmdb_id'];

    // Check if TV show already exists
    $check_query = "SELECT * FROM tvshows WHERE tmdb_id = $tmdb_id";
    $check_result = mysqli_query($conn, $check_query);

    if (mysqli_num_rows($check_result) > 0) {
        $error_message = 'TV show already exists in the database.';
    } else {
        // Get TV show details from TMDB API
        $api_key = defined('TMDB_API_KEY') ? constant('TMDB_API_KEY') : '';
        $language = defined('TMDB_LANGUAGE') ? constant('TMDB_LANGUAGE') : 'en-US';
        $api_url = "https://api.themoviedb.org/3/tv/$tmdb_id?api_key=" . $api_key . "&language=" . $language . "&append_to_response=credits,videos,content_ratings";

        $context = stream_context_create([
            'http' => [
                'timeout' => 10, // Timeout in seconds
            ]
        ]);

        try {
            $response = @file_get_contents($api_url, false, $context);

            if ($response !== false) {
                $tvshow_details = json_decode($response, true);
                if (!$tvshow_details) {
                    $error_message = 'Error parsing TV show details from TMDB API.';
                } else {
                    // Get default category
                    $category_query = "SELECT id FROM categories ORDER BY id LIMIT 1";
                    $category_result = mysqli_query($conn, $category_query);
                    $default_category_id = mysqli_num_rows($category_result) > 0 ? mysqli_fetch_assoc($category_result)['id'] : 1;

                    $title = sanitize($tvshow_details['name']);

                    // Get category based on TMDB genres
                    $category_id = $default_category_id;
                    if (!empty($tvshow_details['genres'])) {
                        // Get the first genre from TMDB
                        $tmdb_genre = $tvshow_details['genres'][0]['name'];

                        // Try to find a matching category in our database
                        $category_query = "SELECT id FROM categories WHERE name LIKE '%$tmdb_genre%' AND (type = 'both' OR type = 'tvshow') LIMIT 1";
                        $category_result = mysqli_query($conn, $category_query);

                        if (mysqli_num_rows($category_result) > 0) {
                            $category = mysqli_fetch_assoc($category_result);
                            $category_id = $category['id'];
                        }
                    }

                    // Get start and end years
                    $start_year = !empty($tvshow_details['first_air_date']) ? (int)date('Y', strtotime($tvshow_details['first_air_date'])) : 0;
                    $end_year = !empty($tvshow_details['last_air_date']) ? (int)date('Y', strtotime($tvshow_details['last_air_date'])) : NULL;
                    if ($tvshow_details['status'] == 'Ended' || $tvshow_details['status'] == 'Canceled') {
                        $end_year = $end_year ?: $start_year;
                    } else {
                        $end_year = NULL;
                    }

                    // Get number of seasons
                    $seasons = !empty($tvshow_details['number_of_seasons']) ? (int)$tvshow_details['number_of_seasons'] : 1;

                    $quality = 'HD';
                    $rating = (float)$tvshow_details['vote_average'];
                    $description = sanitize($tvshow_details['overview']);

                    // Get trailer URL
                    $trailer_url = '';
                    if (!empty($tvshow_details['videos']['results'])) {
                        foreach ($tvshow_details['videos']['results'] as $video) {
                            if ($video['site'] == 'YouTube' && ($video['type'] == 'Trailer' || $video['type'] == 'Teaser')) {
                                $trailer_url = 'https://www.youtube.com/watch?v=' . $video['key'];
                                break;
                            }
                        }
                    }

                    $poster_path = !empty($tvshow_details['poster_path']) ? $tvshow_details['poster_path'] : '';
                    $backdrop_path = !empty($tvshow_details['backdrop_path']) ? $tvshow_details['backdrop_path'] : '';
                    $premium_only = 0;
                    $featured = 0;

                    // Download poster image from TMDB
                    $poster_filename = '';
                    if (!empty($poster_path)) {
                        $poster_url = 'https://image.tmdb.org/t/p/w500' . $poster_path;
                        $poster_filename = 'tvshow_poster_' . $tmdb_id . '_' . time() . '.jpg';
                        $poster_path_local = '../uploads/' . $poster_filename;

                        // Create uploads directory if it doesn't exist
                        if (!file_exists('../uploads')) {
                            mkdir('../uploads', 0777, true);
                        }

                        // Download the image
                        $image_content = @file_get_contents($poster_url);
                        if ($image_content !== false) {
                            file_put_contents($poster_path_local, $image_content);
                        } else {
                            // If download fails, use the TMDB path directly
                            $poster_filename = $poster_path;
                        }
                    }

                    // Download banner image from TMDB
                    $banner_filename = '';
                    if (!empty($backdrop_path)) {
                        $banner_url = 'https://image.tmdb.org/t/p/original' . $backdrop_path;
                        $banner_filename = 'tvshow_banner_' . $tmdb_id . '_' . time() . '.jpg';
                        $banner_path_local = '../uploads/' . $banner_filename;

                        // Download the image
                        $banner_content = @file_get_contents($banner_url);
                        if ($banner_content !== false) {
                            file_put_contents($banner_path_local, $banner_content);
                        } else {
                            // If download fails, use the poster as banner
                            $banner_filename = $poster_filename;
                        }
                    } else {
                        // If no backdrop, use poster as banner
                        $banner_filename = $poster_filename;
                    }

                    // Begin transaction
                    mysqli_begin_transaction($conn);

                    try {
                        // Insert TV show
                        $end_year_value = $end_year ? $end_year : 'NULL';
                        $query = "INSERT INTO tvshows (tmdb_id, title, category_id, start_year, end_year, seasons, quality, rating, description, trailer_url, poster, banner, premium_only, featured, created_at)
                                 VALUES ($tmdb_id, '$title', $category_id, $start_year, " . ($end_year ? $end_year : 'NULL') . ", $seasons, '$quality', $rating, '$description', '$trailer_url', '$poster_filename', '$banner_filename', $premium_only, $featured, NOW())";

                        if (!mysqli_query($conn, $query)) {
                            throw new Exception('Error importing TV show: ' . mysqli_error($conn));
                        }

                        $tvshow_id = mysqli_insert_id($conn);

                        // Add all genres to tvshow_genres table
                        if (!empty($tvshow_details['genres'])) {
                            foreach ($tvshow_details['genres'] as $genre) {
                                $tmdb_genre = sanitize($genre['name']);

                                // Try to find a matching category in our database
                                $genre_query = "SELECT id FROM categories WHERE name LIKE '%$tmdb_genre%' AND (type = 'both' OR type = 'tvshow') LIMIT 1";
                                $genre_result = mysqli_query($conn, $genre_query);

                                if (mysqli_num_rows($genre_result) > 0) {
                                    $db_genre = mysqli_fetch_assoc($genre_result);
                                    $genre_id = $db_genre['id'];

                                    // Insert into tvshow_genres table
                                    $insert_genre_query = "INSERT IGNORE INTO tvshow_genres (tvshow_id, genre_id) VALUES ($tvshow_id, $genre_id)";
                                    mysqli_query($conn, $insert_genre_query);
                                }
                            }
                        }

                        // Import seasons and episodes
                        $seasons_imported = 0;
                        $episodes_imported = 0;

                        for ($season = 1; $season <= $seasons; $season++) {
                            // Get season details from TMDB API
                            $season_api_url = "https://api.themoviedb.org/3/tv/$tmdb_id/season/$season?api_key=" . $api_key . "&language=" . $language;
                            $season_response = @file_get_contents($season_api_url, false, $context);

                            if ($season_response !== false) {
                                $season_details = json_decode($season_response, true);

                                if ($season_details && !empty($season_details['episodes'])) {
                                    $seasons_imported++;

                                    foreach ($season_details['episodes'] as $episode) {
                                        $episode_number = (int)$episode['episode_number'];
                                        $episode_title = sanitize($episode['name']);
                                        $episode_description = sanitize($episode['overview']);
                                        $episode_air_date = !empty($episode['air_date']) ? $episode['air_date'] : NULL;

                                        // Insert episode
                                        $episode_query = "INSERT INTO episodes (tvshow_id, season_number, episode_number, title, description, release_date, is_premium, created_at)
                                                       VALUES ($tvshow_id, $season, $episode_number, '$episode_title', '$episode_description', " . ($episode_air_date ? "'$episode_air_date'" : 'NULL') . ", $premium_only, NOW())";

                                        if (mysqli_query($conn, $episode_query)) {
                                            $episodes_imported++;

                                            // Add automatic streaming link for vidzee.wtf
                                            $episode_id = mysqli_insert_id($conn);
                                            $stream_url = "https://vidzee.wtf/tv/{$tmdb_id}/{$season}/{$episode_number}";
                                            $stream_query = "INSERT INTO streaming_links (content_type, content_id, quality, server_name, stream_url, is_premium)
                                                           VALUES ('episode', $episode_id, 'HD', 'Vidzee', '$stream_url', $premium_only)";
                                            mysqli_query($conn, $stream_query);
                                        }
                                    }
                                }
                            }
                        }

                        // Commit transaction
                        mysqli_commit($conn);

                        $success_message = "TV show imported successfully with $seasons_imported seasons and $episodes_imported episodes.";

                        // Redirect to edit page
                        redirect("edit_tvshow.php?id=$tvshow_id&success=imported");
                    } catch (Exception $e) {
                        // Rollback transaction on error
                        mysqli_rollback($conn);
                        $error_message = $e->getMessage();
                    }
                }
            } else {
                $error_message = 'Error fetching TV show details from TMDB API. URL: ' . $api_url;
            }
        } catch (Exception $e) {
            $error_message = 'Exception: ' . $e->getMessage();
        }
    }
}

// Import TV Show
if (isset($_POST['import_tvshow']) || isset($_POST['direct_import'])) {
    $tmdb_id = (int)$_POST['tmdb_id'];

    // Check if TV show already exists
    $check_query = "SELECT * FROM tvshows WHERE tmdb_id = $tmdb_id";
    $check_result = mysqli_query($conn, $check_query);

    if (mysqli_num_rows($check_result) > 0) {
        $error_message = 'TV show already exists in the database.';
    } else {
        if (isset($_POST['direct_import'])) {
            // Direct import with default values
            // Get default category
            $category_query = "SELECT id FROM categories ORDER BY id LIMIT 1";
            $category_result = mysqli_query($conn, $category_query);
            $default_category_id = mysqli_num_rows($category_result) > 0 ? mysqli_fetch_assoc($category_result)['id'] : 1;

            $title = sanitize($tvshow_details['name']);

            // Get category based on TMDB genres
            $category_id = $default_category_id;
            if (!empty($tvshow_details['genres'])) {
                // Get the first genre from TMDB
                $tmdb_genre = $tvshow_details['genres'][0]['name'];

                // Try to find a matching category in our database
                $category_query = "SELECT id FROM categories WHERE name LIKE '%$tmdb_genre%' AND (type = 'both' OR type = 'tvshow') LIMIT 1";
                $category_result = mysqli_query($conn, $category_query);

                if (mysqli_num_rows($category_result) > 0) {
                    $category = mysqli_fetch_assoc($category_result);
                    $category_id = $category['id'];
                }
            }

            $start_year = !empty($tvshow_details['first_air_date']) ? (int)date('Y', strtotime($tvshow_details['first_air_date'])) : 0;
            $end_year = !empty($tvshow_details['last_air_date']) && $tvshow_details['status'] == 'Ended' ? (int)date('Y', strtotime($tvshow_details['last_air_date'])) : null;
            $seasons = count($tvshow_details['seasons']);
            $quality = 'HD';
            $language = !empty($tvshow_details['original_language']) ? strtoupper($tvshow_details['original_language']) : '';
            $rating = (float)$tvshow_details['vote_average'];
            $description = sanitize($tvshow_details['overview']);

            // Get trailer URL
            $trailer_url = '';
            if (!empty($tvshow_details['videos']['results'])) {
                foreach ($tvshow_details['videos']['results'] as $video) {
                    if ($video['site'] == 'YouTube' && ($video['type'] == 'Trailer' || $video['type'] == 'Teaser')) {
                        $trailer_url = 'https://www.youtube.com/watch?v=' . $video['key'];
                        break;
                    }
                }
            }

            $poster_path = sanitize($tvshow_details['poster_path']);
            $premium_only = 0;
            $featured = 0;
            $import_seasons = 1; // Always import seasons with direct import
        } else {
            // Regular import with user-specified values
            $title = sanitize($_POST['title']);
            $category_id = (int)$_POST['category_id'];
            $start_year = (int)$_POST['start_year'];
            $end_year = !empty($_POST['end_year']) ? (int)$_POST['end_year'] : null;
            $seasons = (int)$_POST['seasons'];
            $quality = sanitize($_POST['quality']);
            $language = sanitize($_POST['language']);
            $rating = (float)$_POST['rating'];
            $description = sanitize($_POST['description']);
            $trailer_url = sanitize($_POST['trailer_url']);
            $poster_path = sanitize($_POST['poster_path']);
            $premium_only = isset($_POST['premium_only']) ? 1 : 0;
            $featured = isset($_POST['featured']) ? 1 : 0;
            $import_seasons = isset($_POST['import_seasons']) ? 1 : 0;
        }

        // Validate required fields
        if (empty($title) || empty($category_id) || empty($start_year)) {
            $error_message = 'Title, category, and start year are required.';
        } else {
            // Download poster image from TMDB
            $poster_filename = '';
            if (!empty($poster_path)) {
                $poster_url = 'https://image.tmdb.org/t/p/w500' . $poster_path;
                $poster_filename = 'tvshow_poster_' . $tmdb_id . '_' . time() . '.jpg';
                $poster_path_local = '../uploads/' . $poster_filename;

                // Create uploads directory if it doesn't exist
                if (!file_exists('../uploads')) {
                    mkdir('../uploads', 0777, true);
                }

                // Download the image
                $image_content = @file_get_contents($poster_url);
                if ($image_content !== false) {
                    file_put_contents($poster_path_local, $image_content);
                } else {
                    // If download fails, use the TMDB path directly
                    $poster_filename = $poster_path;
                }
            }

            // Download banner image from TMDB
            $banner_filename = '';
            $backdrop_path = isset($_POST['backdrop_path']) ? sanitize($_POST['backdrop_path']) : '';
            if (!empty($backdrop_path)) {
                $banner_url = 'https://image.tmdb.org/t/p/original' . $backdrop_path;
                $banner_filename = 'tvshow_banner_' . $tmdb_id . '_' . time() . '.jpg';
                $banner_path_local = '../uploads/' . $banner_filename;

                // Download the image
                $banner_content = @file_get_contents($banner_url);
                if ($banner_content !== false) {
                    file_put_contents($banner_path_local, $banner_content);
                } else {
                    // If download fails, use the poster as banner
                    $banner_filename = $poster_filename;
                }
            } else {
                // If no backdrop, use poster as banner
                $banner_filename = $poster_filename;
            }

            // Begin transaction
            mysqli_begin_transaction($conn);

            try {
                // Insert TV show
                $end_year_value = $end_year ? $end_year : 'NULL';
                $query = "INSERT INTO tvshows (tmdb_id, title, category_id, start_year, end_year, seasons, quality, rating, description, trailer_url, poster, banner, premium_only, featured, created_at)
                         VALUES ($tmdb_id, '$title', $category_id, $start_year, " . ($end_year ? $end_year : 'NULL') . ", $seasons, '$quality', $rating, '$description', '$trailer_url', '$poster_filename', '$banner_filename', $premium_only, $featured, NOW())";

                if (!mysqli_query($conn, $query)) {
                    throw new Exception('Error importing TV show: ' . mysqli_error($conn));
                }

                $tvshow_id = mysqli_insert_id($conn);

                // Add all genres to tvshow_genres table
                if (!empty($tvshow_details['genres'])) {
                    foreach ($tvshow_details['genres'] as $genre) {
                        $tmdb_genre = sanitize($genre['name']);

                        // Try to find a matching category in our database
                        $genre_query = "SELECT id FROM categories WHERE name LIKE '%$tmdb_genre%' AND (type = 'both' OR type = 'tvshow') LIMIT 1";
                        $genre_result = mysqli_query($conn, $genre_query);

                        if (mysqli_num_rows($genre_result) > 0) {
                            $db_genre = mysqli_fetch_assoc($genre_result);
                            $genre_id = $db_genre['id'];

                            // Insert into tvshow_genres table
                            $insert_genre_query = "INSERT IGNORE INTO tvshow_genres (tvshow_id, genre_id) VALUES ($tvshow_id, $genre_id)";
                            mysqli_query($conn, $insert_genre_query);
                        }
                    }
                }

                // Import seasons if requested
                if ($import_seasons && !empty($tvshow_details['seasons'])) {
                    $episodes_imported = 0;
                    $episodes_error = 0;
                    $api_key = defined('TMDB_API_KEY') ? constant('TMDB_API_KEY') : '';
                    $language = defined('TMDB_LANGUAGE') ? constant('TMDB_LANGUAGE') : 'en-US';

                    foreach ($tvshow_details['seasons'] as $season) {
                        // Skip specials (season 0)
                        if ($season['season_number'] == 0) {
                            continue;
                        }

                        // Insert season
                        $season_query = "INSERT INTO seasons (tvshow_id, season_number, created_at)
                                        VALUES ($tvshow_id, {$season['season_number']}, NOW())";

                        if (!mysqli_query($conn, $season_query)) {
                            throw new Exception('Error importing season: ' . mysqli_error($conn));
                        }

                        // Get season details with episodes from TMDB API
                        $season_number = $season['season_number'];
                        $season_api_url = "https://api.themoviedb.org/3/tv/$tmdb_id/season/$season_number?api_key=$api_key&language=$language";

                        // Create a stream context with timeout
                        $context = stream_context_create([
                            'http' => [
                                'timeout' => 15, // Longer timeout for season details
                            ]
                        ]);

                        $season_response = @file_get_contents($season_api_url, false, $context);

                        if ($season_response !== false) {
                            $season_data = json_decode($season_response, true);

                            if (isset($season_data['episodes']) && !empty($season_data['episodes'])) {
                                foreach ($season_data['episodes'] as $episode_data) {
                                    // Extract episode details
                                    $episode_number = $episode_data['episode_number'];
                                    $episode_title = sanitize($episode_data['name']);
                                    $episode_description = sanitize($episode_data['overview']);
                                    $episode_air_date = !empty($episode_data['air_date']) ? "'" . $episode_data['air_date'] . "'" : 'NULL';

                                    // Download episode thumbnail if available
                                    $episode_thumbnail = '';
                                    if (!empty($episode_data['still_path'])) {
                                        $thumbnail_url = 'https://image.tmdb.org/t/p/w300' . $episode_data['still_path'];
                                        $thumbnail_filename = 'episode_' . $tmdb_id . '_s' . $season_number . 'e' . $episode_number . '_' . time() . '.jpg';
                                        $thumbnail_path_local = '../uploads/' . $thumbnail_filename;

                                        // Download the image
                                        $image_content = @file_get_contents($thumbnail_url);
                                        if ($image_content !== false) {
                                            file_put_contents($thumbnail_path_local, $image_content);
                                            $episode_thumbnail = $thumbnail_filename;
                                        } else {
                                            // If download fails, use the TMDB path directly
                                            $episode_thumbnail = $episode_data['still_path'];
                                        }
                                    }

                                    // Insert episode into database
                                    $episode_query = "INSERT INTO episodes (tvshow_id, season_number, episode_number, title, description, thumbnail, release_date, is_premium, created_at)
                                                    VALUES ($tvshow_id, $season_number, $episode_number, '$episode_title', '$episode_description', '$episode_thumbnail', $episode_air_date, $premium_only, NOW())";

                                    if (mysqli_query($conn, $episode_query)) {
                                        $episode_id = mysqli_insert_id($conn);

                                        // Add automatic streaming link for vidzee.wtf
                                        $stream_url = "https://vidzee.wtf/tv/{$tmdb_id}/{$season_number}/{$episode_number}";
                                        $stream_query = "INSERT INTO episode_links (episode_id, link_type, quality, server_name, link_url, is_premium)
                                                       VALUES ($episode_id, 'stream', '$quality', 'Vidzee', '$stream_url', $premium_only)";
                                        mysqli_query($conn, $stream_query);

                                        $episodes_imported++;
                                    } else {
                                        $episodes_error++;
                                    }
                                }
                            }
                        }
                    }
                }

                // Commit transaction
                mysqli_commit($conn);

                if (isset($episodes_imported) && $episodes_imported > 0) {
                    $success_message = 'TV show imported successfully with ' . $episodes_imported . ' episodes!';
                    if (isset($episodes_error) && $episodes_error > 0) {
                        $success_message .= ' ' . $episodes_error . ' episodes failed to import.';
                    }
                } else {
                    $success_message = 'TV show imported successfully.';
                }

                // Redirect to edit page
                redirect("edit_tvshow.php?id=$tvshow_id&success=imported");
            } catch (Exception $e) {
                // Rollback transaction on error
                mysqli_rollback($conn);
                $error_message = $e->getMessage();
            }
        }
    }
}

// Get categories
$categories_query = "SELECT * FROM categories ORDER BY name";
$categories_result = mysqli_query($conn, $categories_query);

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title d-flex align-items-center">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Import TV Shows from TMDB</h1>
            </div>

            <div class="topbar-actions">
                <div class="dropdown">
                    <button class="btn btn-link dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <img src="assets/img/user.png" class="rounded-circle" width="32" height="32" alt="User">
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user fa-sm me-2 text-gray-400"></i> Profile</a></li>
                        <li><a class="dropdown-item" href="site_settings.php"><i class="fas fa-cogs fa-sm me-2 text-gray-400"></i> Settings</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/logout.php"><i class="fas fa-sign-out-alt fa-sm me-2 text-gray-400"></i> Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <?php if($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if(!isset($error_message) || (isset($error_message) && $error_message != 'TMDB API key is not set. Please set it in the site settings.')): ?>
        <!-- Search and Direct Import Forms -->
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="fw-bold text-primary mb-0">Search TV Shows</h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="" class="needs-validation" novalidate>
                            <div class="input-group">
                                <input type="text" class="form-control" name="search_query" placeholder="Enter TV show title..." required>
                                <button type="submit" name="search_tvshows" class="btn btn-primary d-flex align-items-center">
                                    <i class="fas fa-search me-2"></i><span>Search</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="fw-bold text-primary mb-0">Direct Import by TMDB ID</h6>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="" class="needs-validation" novalidate>
                            <div class="input-group">
                                <input type="number" class="form-control" name="tvshow_id" placeholder="Enter TMDB ID..." required>
                                <button type="submit" class="btn btn-success d-flex align-items-center">
                                    <i class="fas fa-file-import me-2"></i><span>Get Details</span>
                                </button>
                            </div>
                            <div class="form-text mt-2">
                                <i class="fas fa-info-circle me-1"></i> Find TMDB ID from <a href="https://www.themoviedb.org" target="_blank">themoviedb.org</a> TV show URLs (e.g., 1396 for Breaking Bad)
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <?php if(!empty($search_results)): ?>
        <!-- Search Results -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="fw-bold text-primary mb-0">Search Results</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="table-light d-none d-md-table-header-group">
                            <tr>
                                <th>Poster</th>
                                <th>Title</th>
                                <th>First Air Date</th>
                                <th>Rating</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($search_results as $tvshow): ?>
                            <tr class="d-block d-md-table-row mb-3 mb-md-0">
                                <td class="d-flex d-md-table-cell align-items-center">
                                    <?php if(!empty($tvshow['poster_path'])): ?>
                                    <img src="https://image.tmdb.org/t/p/w92<?php echo $tvshow['poster_path']; ?>" alt="<?php echo $tvshow['name']; ?>" class="img-thumbnail" style="width: 50px;">
                                    <?php else: ?>
                                    <div style="width: 50px; height: 75px; background-color: #f8f9fc; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-tv fa-2x text-muted"></i>
                                    </div>
                                    <?php endif; ?>
                                    <div class="ms-3 d-block d-md-none">
                                        <h6 class="mb-0"><?php echo $tvshow['name']; ?></h6>
                                        <small class="text-muted">
                                            <?php echo !empty($tvshow['first_air_date']) ? date('M j, Y', strtotime($tvshow['first_air_date'])) : 'N/A'; ?> •
                                            <?php echo $tvshow['vote_average']; ?>/10
                                        </small>
                                    </div>
                                </td>
                                <td class="d-none d-md-table-cell"><?php echo $tvshow['name']; ?></td>
                                <td class="d-none d-md-table-cell"><?php echo !empty($tvshow['first_air_date']) ? date('M j, Y', strtotime($tvshow['first_air_date'])) : 'N/A'; ?></td>
                                <td class="d-none d-md-table-cell"><?php echo $tvshow['vote_average']; ?>/10</td>
                                <td class="d-block d-md-table-cell mt-2 mt-md-0">
                                    <div class="d-flex flex-column flex-md-row gap-2">
                                        <a href="import_tmdb_tvshow.php?tvshow_id=<?php echo $tvshow['id']; ?>" class="btn btn-primary btn-sm d-flex align-items-center justify-content-center">
                                            <i class="fas fa-info-circle me-2"></i><span>View Details</span>
                                        </a>
                                        <form method="POST" action="" class="m-0">
                                            <input type="hidden" name="direct_import_from_search" value="1">
                                            <input type="hidden" name="tmdb_id" value="<?php echo $tvshow['id']; ?>">
                                            <button type="submit" class="btn btn-success btn-sm d-flex align-items-center justify-content-center w-100">
                                                <i class="fas fa-file-import me-2"></i><span>Import</span>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php if($tvshow_details): ?>
        <!-- TV Show Details -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="fw-bold text-primary mb-0">TV Show Details</h6>
            </div>
            <div class="card-body">
                <form method="POST" action="" class="needs-validation" novalidate>
                    <input type="hidden" name="tmdb_id" value="<?php echo $tvshow_details['id']; ?>">

                    <div class="row">
                        <div class="col-md-3 mb-4">
                            <?php if(!empty($tvshow_details['poster_path'])): ?>
                            <img src="https://image.tmdb.org/t/p/w300<?php echo $tvshow_details['poster_path']; ?>" alt="<?php echo $tvshow_details['name']; ?>" class="img-thumbnail w-100">
                            <?php else: ?>
                            <div style="width: 100%; height: 300px; background-color: #f8f9fc; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-tv fa-4x text-muted"></i>
                            </div>
                            <?php endif; ?>

                            <div class="mt-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="premium_only" name="premium_only">
                                    <label class="form-check-label" for="premium_only">Premium Only</label>
                                </div>

                                <div class="form-check form-switch mt-2">
                                    <input class="form-check-input" type="checkbox" id="featured" name="featured">
                                    <label class="form-check-label" for="featured">Featured</label>
                                </div>

                                <div class="form-check form-switch mt-2">
                                    <input class="form-check-input" type="checkbox" id="import_seasons" name="import_seasons" checked>
                                    <label class="form-check-label" for="import_seasons">Import Seasons</label>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-9">
                            <div class="mb-3">
                                <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" value="<?php echo $tvshow_details['name']; ?>" required>
                                <div class="invalid-feedback">
                                    Please enter a title.
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="category_id" class="form-label">Category <span class="text-danger">*</span></label>
                                        <select class="form-select" id="category_id" name="category_id" required>
                                            <option value="">Select Category</option>
                                            <?php
                                            mysqli_data_seek($categories_result, 0);
                                            while($category = mysqli_fetch_assoc($categories_result)):
                                            ?>
                                            <option value="<?php echo $category['id']; ?>"><?php echo $category['name']; ?></option>
                                            <?php endwhile; ?>
                                        </select>
                                        <div class="invalid-feedback">
                                            Please select a category.
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="start_year" class="form-label">Start Year <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="start_year" name="start_year" value="<?php echo !empty($tvshow_details['first_air_date']) ? date('Y', strtotime($tvshow_details['first_air_date'])) : ''; ?>" required>
                                        <div class="invalid-feedback">
                                            Please enter a valid start year.
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="end_year" class="form-label">End Year</label>
                                        <input type="number" class="form-control" id="end_year" name="end_year" value="<?php echo !empty($tvshow_details['last_air_date']) && $tvshow_details['status'] == 'Ended' ? date('Y', strtotime($tvshow_details['last_air_date'])) : ''; ?>">
                                        <div class="form-text">Leave empty if still running.</div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="seasons" class="form-label">Seasons</label>
                                        <input type="number" class="form-control" id="seasons" name="seasons" min="1" value="<?php echo count($tvshow_details['seasons']); ?>">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="quality" class="form-label">Quality</label>
                                        <select class="form-select" id="quality" name="quality">
                                            <option value="">Select Quality</option>
                                            <option value="SD">SD</option>
                                            <option value="HD" selected>HD</option>
                                            <option value="Full HD">Full HD</option>
                                            <option value="4K">4K</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-5">
                                    <div class="mb-3">
                                        <label for="language" class="form-label">Language</label>
                                        <input type="text" class="form-control" id="language" name="language" value="<?php echo !empty($tvshow_details['original_language']) ? strtoupper($tvshow_details['original_language']) : ''; ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="rating" class="form-label">Rating (0-10)</label>
                                <input type="number" class="form-control" id="rating" name="rating" min="0" max="10" step="0.1" value="<?php echo $tvshow_details['vote_average']; ?>">
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="5"><?php echo $tvshow_details['overview']; ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="trailer_url" class="form-label">Trailer URL (YouTube)</label>
                                <?php
                                $trailer_url = '';
                                if (!empty($tvshow_details['videos']['results'])) {
                                    foreach ($tvshow_details['videos']['results'] as $video) {
                                        if ($video['site'] == 'YouTube' && ($video['type'] == 'Trailer' || $video['type'] == 'Teaser')) {
                                            $trailer_url = 'https://www.youtube.com/watch?v=' . $video['key'];
                                            break;
                                        }
                                    }
                                }
                                ?>
                                <input type="url" class="form-control" id="trailer_url" name="trailer_url" value="<?php echo $trailer_url; ?>">
                            </div>

                            <input type="hidden" name="poster_path" value="<?php echo $tvshow_details['poster_path']; ?>">
                            <input type="hidden" name="backdrop_path" value="<?php echo $tvshow_details['backdrop_path']; ?>">

                            <div class="d-flex flex-column flex-md-row gap-2 mb-3">
                                <button type="submit" name="import_tvshow" class="btn btn-primary w-100 mb-2 mb-md-0">
                                    <i class="fas fa-file-import me-2"></i>Import TV Show
                                </button>
                                <button type="submit" name="direct_import" class="btn btn-success w-100">
                                    <i class="fas fa-bolt me-2"></i>Direct Import
                                </button>
                            </div>
                            <div class="form-text mt-2 text-center">
                                <i class="fas fa-info-circle me-1"></i> Direct Import will use default category and settings
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<style>
@media (max-width: 576px) {
    .input-group .btn span {
        display: inline-block !important;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        width: 100%;
    }

    .table td {
        vertical-align: middle;
    }

    .table td .btn {
        width: 100%;
        margin-top: 5px;
        margin-bottom: 5px;
        white-space: normal;
    }
}
</style>

<?php
// Include footer
require_once 'includes/footer.php';
?>
