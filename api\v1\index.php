<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// API Router
require_once '../config.php';
require_once '../functions.php';

// Define API version
define('API_VERSION', 'v1');

// Set headers for API responses
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization, API-Key');
header('Content-Type: application/json; charset=UTF-8');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Parse request
$request = parse_request();

// Route request to appropriate endpoint
route_request($request);

// Parse request data
function parse_request() {
    $method = $_SERVER['REQUEST_METHOD'];
    $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $uri = str_replace('/api/' . API_VERSION . '/', '', $uri);
    $uri = rtrim($uri, '/');
    $parts = explode('/', $uri);

    $params = [];
    if ($method === 'GET') {
        $params = $_GET;
    }

    $body = [];
    if ($method === 'POST' || $method === 'PUT') {
        $input = file_get_contents('php://input');
        if (!empty($input)) {
            $body = json_decode($input, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                api_error('Invalid JSON in request body', 400);
            }
        } else {
            $body = $_POST;
        }
    }

    // Get authorization header
    $headers = getallheaders();
    $auth = isset($headers['Authorization']) ? $headers['Authorization'] : '';

    return [
        'method' => $method,
        'uri' => $uri,
        'parts' => $parts,
        'params' => $params,
        'body' => $body,
        'auth' => $auth
    ];
}

// Route request to appropriate endpoint
function route_request($request) {
    global $request;

    if (empty($request['parts'][0])) {
        // API root
        api_response([
            'name' => 'CinePix API',
            'version' => API_VERSION,
            'status' => 'active'
        ]);
    }

    $endpoint = $request['parts'][0];
    array_shift($request['parts']);

    switch ($endpoint) {
        case 'auth':
            require_once 'auth/index.php';
            break;

        case 'movies':
            require_once 'movies/index.php';
            break;

        case 'tvshows':
            require_once 'tvshows/index.php';
            break;

        case 'users':
            require_once 'users/profile.php';
            break;

        case 'search':
            require_once 'search/index.php';
            break;

        case 'stream':
            require_once 'stream/index.php';
            break;

        case 'subtitles':
            require_once 'subtitles/index.php';
            break;

        case 'payments':
            require_once 'payments/index.php';
            break;

        case 'config':
            require_once 'config/index.php';
            break;

        case 'notifications':
            require_once 'notifications/index.php';
            break;

        default:
            api_error('Endpoint not found', 404);
    }
}
