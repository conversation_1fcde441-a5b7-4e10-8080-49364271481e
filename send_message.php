<?php
require_once 'includes/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'login_required']);
    exit;
}

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $user_id = $_SESSION['user_id'];
    $message = sanitize($_POST['message']);

    // Check if this is a reply to an existing message
    if (isset($_POST['reply_to']) && !empty($_POST['reply_to'])) {
        $reply_to = (int)$_POST['reply_to'];

        // Validate input
        if (empty($message)) {
            echo json_encode(['success' => false, 'message' => 'মেসেজ লিখুন।']);
            exit;
        }

        // Check if the message exists and belongs to the user
        $check_message_query = "SELECT * FROM messages WHERE id = $reply_to AND user_id = $user_id";
        $check_message_result = mysqli_query($conn, $check_message_query);

        if (mysqli_num_rows($check_message_result) === 0) {
            echo json_encode(['success' => false, 'message' => 'মেসেজ খুঁজে পাওয়া যায়নি।']);
            exit;
        }

        $original_message = mysqli_fetch_assoc($check_message_result);
        $subject = 'RE: ' . $original_message['subject'];

        // Insert new message as a reply
        $insert_query = "INSERT INTO messages (user_id, subject, message, reply_to) VALUES ($user_id, '$subject', '$message', $reply_to)";

        if (mysqli_query($conn, $insert_query)) {
            // Redirect to messages page
            if (!headers_sent()) {
                redirect(SITE_URL . '/messages.php');
            } else {
                echo json_encode(['success' => true, 'redirect' => SITE_URL . '/messages.php']);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'ডাটাবেস ত্রুটি: ' . mysqli_error($conn)]);
        }
    } else {
        // This is a new message
        $subject = sanitize($_POST['subject']);

        // Validate input
        if (empty($subject) || empty($message)) {
            echo json_encode(['success' => false, 'message' => 'বিষয় এবং মেসেজ উভয়ই প্রয়োজন।']);
            exit;
        }

        // Check if user has sent a message in the last 24 hours
        $check_query = "SELECT COUNT(*) as count FROM messages WHERE user_id = $user_id AND created_at > DATE_SUB(NOW(), INTERVAL 1 DAY) AND reply_to IS NULL";
        $check_result = mysqli_query($conn, $check_query);
        $check_data = mysqli_fetch_assoc($check_result);

        if ($check_data['count'] > 0) {
            echo json_encode(['success' => false, 'message' => 'আপনি ইতিমধ্যে গত ২৪ ঘন্টায় একটি মেসেজ পাঠিয়েছেন। দয়া করে আবার চেষ্টা করার আগে অপেক্ষা করুন।']);
            exit;
        }

        // Insert message into database
        $insert_query = "INSERT INTO messages (user_id, subject, message) VALUES ($user_id, '$subject', '$message')";

        if (mysqli_query($conn, $insert_query)) {
            // Check if request is from messages page
            $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
            if (strpos($referer, 'messages.php') !== false) {
                // Redirect to messages page
                if (!headers_sent()) {
                    redirect(SITE_URL . '/messages.php');
                } else {
                    echo json_encode(['success' => true, 'redirect' => SITE_URL . '/messages.php']);
                }
            } else {
                echo json_encode(['success' => true]);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'ডাটাবেস ত্রুটি: ' . mysqli_error($conn)]);
        }
    }
} else {
    echo json_encode(['success' => false, 'message' => 'অবৈধ অনুরোধ।']);
}
?>
