<?php
require_once 'includes/header.php';

// Check if stream ID is provided
if (!isset($_GET['id'])) {
    redirect(SITE_URL);
}

$stream_id = (int)$_GET['id'];

// Get stream details
$stream_query = "SELECT s.*, 
                CASE 
                    WHEN s.content_type = 'movie' THEN m.title
                    ELSE t.title
                END as content_title,
                CASE 
                    WHEN s.content_type = 'movie' THEN m.id
                    ELSE t.id
                END as content_id
                FROM streaming_links s
                LEFT JOIN movies m ON s.content_type = 'movie' AND s.content_id = m.id
                LEFT JOIN tvshows t ON s.content_type = 'tvshow' AND s.content_id = t.id
                WHERE s.id = $stream_id";
$stream_result = mysqli_query($conn, $stream_query);

if (mysqli_num_rows($stream_result) == 0) {
    redirect(SITE_URL);
}

$stream = mysqli_fetch_assoc($stream_result);

// Check if premium content and user is not premium
if ($stream['is_premium'] && !$is_premium) {
    redirect(SITE_URL . '/premium.php');
}

// Get related content
if ($stream['content_type'] == 'movie') {
    $related_query = "SELECT 'movie' as type, m.id, m.title, m.poster, m.premium_only 
                     FROM movies m 
                     WHERE m.id != {$stream['content_id']}
                     ORDER BY RAND() 
                     LIMIT 6";
} else {
    $related_query = "SELECT 'tvshow' as type, t.id, t.title, t.poster, t.premium_only 
                     FROM tvshows t 
                     WHERE t.id != {$stream['content_id']}
                     ORDER BY RAND() 
                     LIMIT 6";
}
$related_result = mysqli_query($conn, $related_query);
?>

<!-- Video Player Section -->
<section class="py-4 bg-black">
    <div class="container">
        <div class="row">
            <div class="col-lg-9">
                <div class="card bg-dark border-0">
                    <div class="card-header bg-dark d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><?php echo $stream['content_title']; ?> - <?php echo $stream['quality']; ?> (<?php echo $stream['server_name']; ?>)</h5>
                        <a href="<?php echo SITE_URL; ?>/details.php?type=<?php echo $stream['content_type']; ?>&id=<?php echo $stream['content_id']; ?>" class="btn btn-sm btn-outline-light">
                            <i class="fas fa-arrow-left me-1"></i> Back to Details
                        </a>
                    </div>
                    <div class="card-body p-0">
                        <div class="ratio ratio-16x9">
                            <!-- Video Player (using an iframe for demonstration) -->
                            <iframe src="<?php echo $stream['stream_url']; ?>" allowfullscreen></iframe>
                            
                            <!-- For a real implementation, you might use a video player like this: -->
                            <!--
                            <video id="videoPlayer" class="video-js vjs-default-skin vjs-big-play-centered" controls preload="auto" data-setup='{}'>
                                <source src="<?php echo $stream['stream_url']; ?>" type="video/mp4">
                                <p class="vjs-no-js">
                                    To view this video please enable JavaScript, and consider upgrading to a web browser that
                                    <a href="https://videojs.com/html5-video-support/" target="_blank">supports HTML5 video</a>
                                </p>
                            </video>
                            -->
                        </div>
                    </div>
                </div>
                
                <!-- Server Selection -->
                <div class="card bg-dark mt-3">
                    <div class="card-header bg-dark">
                        <h5 class="mb-0">Available Servers</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        // Get all servers for this content
                        $servers_query = "SELECT * FROM streaming_links 
                                         WHERE content_type = '{$stream['content_type']}' 
                                         AND content_id = {$stream['content_id']} 
                                         ORDER BY quality DESC, server_name ASC";
                        $servers_result = mysqli_query($conn, $servers_query);
                        
                        if(mysqli_num_rows($servers_result) > 0):
                        ?>
                        <div class="d-flex flex-wrap">
                            <?php while($server = mysqli_fetch_assoc($servers_result)): ?>
                            <a href="<?php echo SITE_URL; ?>/watch.php?id=<?php echo $server['id']; ?>" class="btn <?php echo $server['id'] == $stream_id ? 'btn-danger' : 'btn-outline-light'; ?> me-2 mb-2">
                                <?php echo $server['server_name']; ?> (<?php echo $server['quality']; ?>)
                                <?php if($server['is_premium']): ?>
                                <i class="fas fa-crown text-warning ms-1"></i>
                                <?php endif; ?>
                            </a>
                            <?php endwhile; ?>
                        </div>
                        <?php else: ?>
                        <p class="mb-0">No alternative servers available.</p>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Download Options -->
                <?php
                // Get download links for this content
                $download_query = "SELECT * FROM download_links 
                                  WHERE content_type = '{$stream['content_type']}' 
                                  AND content_id = {$stream['content_id']} 
                                  ORDER BY quality DESC, link_type ASC";
                $download_result = mysqli_query($conn, $download_query);
                
                if(mysqli_num_rows($download_result) > 0):
                ?>
                <div class="card bg-dark mt-3">
                    <div class="card-header bg-dark">
                        <h5 class="mb-0">Download Options</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-dark table-hover">
                                <thead>
                                    <tr>
                                        <th>Quality</th>
                                        <th>Type</th>
                                        <th>Size</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while($download = mysqli_fetch_assoc($download_result)): ?>
                                    <tr>
                                        <td><?php echo $download['quality']; ?></td>
                                        <td><?php echo ucfirst($download['link_type']); ?></td>
                                        <td>
                                            <?php 
                                            // Simulate file size based on quality
                                            if($download['quality'] == '720p') echo '1.2 GB';
                                            elseif($download['quality'] == '1080p') echo '2.5 GB';
                                            elseif($download['quality'] == '4K') echo '8.7 GB';
                                            else echo '800 MB';
                                            ?>
                                        </td>
                                        <td>
                                            <?php if($download['is_premium'] && !$is_premium): ?>
                                            <a href="<?php echo SITE_URL; ?>/premium.php" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-crown me-1"></i> Premium
                                            </a>
                                            <?php else: ?>
                                            <a href="<?php echo $download['link_url']; ?>" class="btn btn-sm btn-success" target="_blank">
                                                <i class="fas fa-download me-1"></i> Download
                                            </a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-3 mt-4 mt-lg-0">
                <!-- Related Content -->
                <div class="card bg-dark">
                    <div class="card-header bg-dark">
                        <h5 class="mb-0">You May Also Like</h5>
                    </div>
                    <div class="card-body p-2">
                        <?php if(mysqli_num_rows($related_result) > 0): ?>
                        <div class="row g-2">
                            <?php while($related = mysqli_fetch_assoc($related_result)): ?>
                            <div class="col-6 col-lg-12 col-xl-6">
                                <div class="position-relative">
                                    <a href="<?php echo SITE_URL; ?>/details.php?type=<?php echo $related['type']; ?>&id=<?php echo $related['id']; ?>">
                                        <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $related['poster']; ?>" alt="<?php echo $related['title']; ?>" class="img-fluid rounded">
                                        <?php if($related['premium_only']): ?>
                                        <span class="badge bg-danger position-absolute top-0 start-0 m-2">PREMIUM</span>
                                        <?php endif; ?>
                                    </a>
                                </div>
                            </div>
                            <?php endwhile; ?>
                        </div>
                        <?php else: ?>
                        <p class="mb-0">No related content available.</p>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Premium Ad -->
                <?php if(!$is_premium): ?>
                <div class="card bg-dark mt-3">
                    <div class="card-body text-center">
                        <h5 class="text-warning"><i class="fas fa-crown me-2"></i>Go Premium</h5>
                        <p>Unlock all premium content, HD quality, and ad-free experience.</p>
                        <a href="<?php echo SITE_URL; ?>/premium.php" class="btn btn-danger">Upgrade Now</a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<?php require_once 'includes/footer.php'; ?>
