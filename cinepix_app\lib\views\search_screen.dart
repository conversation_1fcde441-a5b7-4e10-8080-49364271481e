import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cinepix_app/constants/app_constants.dart';
import 'package:cinepix_app/controllers/search_controller.dart' as app_search;
import 'package:cinepix_app/models/movie.dart';
import 'package:cinepix_app/models/tv_show.dart';
import 'package:cinepix_app/views/movie_details_screen.dart';
import 'package:cinepix_app/views/tv_show_details_screen.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final app_search.SearchController _searchController =
      Get.find<app_search.SearchController>();
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  // Debounce timer for live search
  Timer? _debounce;

  // Track if we've attempted a search
  bool _hasAttemptedSearch = false;

  @override
  void initState() {
    super.initState();
    _searchController.clearSearch();

    // Focus the search field when the screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  // Perform search immediately
  void _performSearch() {
    final query = _textController.text.trim();
    if (query.isNotEmpty) {
      setState(() {
        _hasAttemptedSearch = true;
      });

      try {
        debugPrint('Performing search for: $query');
        _searchController.updateQuery(query);
        _searchController.search();
        FocusScope.of(context).unfocus();
      } catch (e) {
        debugPrint('Error in search: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('সার্চ করতে সমস্যা হয়েছে: $e')),
        );
      }
    }
  }

  // Perform search with debounce
  void _performLiveSearch(String text) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();

    _debounce = Timer(const Duration(milliseconds: 500), () {
      final query = text.trim();
      if (query.isNotEmpty) {
        setState(() {
          _hasAttemptedSearch = true;
        });

        try {
          debugPrint('Live search for: $query');
          _searchController.updateQuery(query);
          _searchController.search();
        } catch (e) {
          debugPrint('Error in live search: $e');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('সার্চ করতে সমস্যা হয়েছে: $e')),
          );
        }
      } else {
        _searchController.clearSearch();
        setState(() {
          _hasAttemptedSearch = false;
        });
      }
    });
  }

  // Clear search
  void _clearSearch() {
    _textController.clear();
    _searchController.clearSearch();
    setState(() {
      _hasAttemptedSearch = false;
    });
    _focusNode.requestFocus();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: _buildSearchField(),
        actions: _buildAppBarActions(),
        elevation: 0,
      ),
      body: Obx(() => _buildBody()),
    );
  }

  // Search field widget
  Widget _buildSearchField() {
    return TextField(
      controller: _textController,
      focusNode: _focusNode,
      decoration: InputDecoration(
        hintText: 'মুভি এবং টিভি সিরিজ খুঁজুন',
        border: InputBorder.none,
        hintStyle: TextStyle(color: Colors.grey[400]),
        contentPadding: const EdgeInsets.symmetric(vertical: 12),
      ),
      style: const TextStyle(color: Colors.white),
      textInputAction: TextInputAction.search,
      onSubmitted: (_) => _performSearch(),
      onChanged: _performLiveSearch,
    );
  }

  // App bar actions
  List<Widget> _buildAppBarActions() {
    return [
      // Search button
      IconButton(
        icon: const Icon(Icons.search),
        onPressed: _performSearch,
        tooltip: 'খুঁজুন',
      ),

      // Clear button
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: _clearSearch,
        tooltip: 'মুছুন',
      ),
    ];
  }

  // Main body content
  Widget _buildBody() {
    debugPrint(
        'Building search body: isLoading=${_searchController.isLoading.value}, '
        'hasAttemptedSearch=$_hasAttemptedSearch, '
        'hasSearched=${_searchController.hasSearched.value}, '
        'hasResults=${_searchController.hasResults}, '
        'totalResults=${_searchController.totalResults}, '
        'movies=${_searchController.movies.length}, '
        'tvShows=${_searchController.tvShows.length}');

    // Show loading indicator
    if (_searchController.isLoading.value) {
      return _buildLoadingIndicator();
    }

    // Show initial search screen
    if (!_hasAttemptedSearch || !_searchController.hasSearched.value) {
      return _buildInitialSearchScreen();
    }

    // Show no results screen
    if (!_searchController.hasResults) {
      return _buildNoResultsScreen();
    }

    // Show search results
    return _buildSearchResults();
  }

  // Loading indicator
  Widget _buildLoadingIndicator() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: AppConstants.primaryColor,
          ),
          const SizedBox(height: 16),
          Text(
            'খোঁজা হচ্ছে...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[400],
            ),
          ),
        ],
      ),
    );
  }

  // Initial search screen
  Widget _buildInitialSearchScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search,
            size: 80,
            color: Colors.grey[600],
          ),
          const SizedBox(height: 24),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              'আপনি যে মুভি বা টিভি সিরিজ খুঁজছেন তার নাম লিখুন',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[500],
              ),
            ),
          ),
          const SizedBox(height: 32),
          _buildSearchSuggestions(),
        ],
      ),
    );
  }

  // Search suggestions
  Widget _buildSearchSuggestions() {
    final suggestions = [
      'Naruto',
      'Avengers',
      'Game of Thrones',
      'Breaking Bad',
      'Spider-Man',
      'Attack on Titan',
    ];

    return Wrap(
      alignment: WrapAlignment.center,
      spacing: 8,
      runSpacing: 8,
      children: suggestions.map((suggestion) {
        return ActionChip(
          label: Text(suggestion),
          backgroundColor: Colors.grey[800],
          labelStyle: const TextStyle(color: Colors.white),
          onPressed: () {
            _textController.text = suggestion;
            _performSearch();
          },
        );
      }).toList(),
    );
  }

  // No results screen
  Widget _buildNoResultsScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 80,
            color: Colors.grey[600],
          ),
          const SizedBox(height: 24),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              '"${_searchController.query.value}" এর জন্য কোন ফলাফল পাওয়া যায়নি',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[500],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'অন্য কিছু খুঁজে দেখুন',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[400],
            ),
          ),
        ],
      ),
    );
  }

  // Search results
  Widget _buildSearchResults() {
    debugPrint(
        'Building search results: movies=${_searchController.movies.length}, tvShows=${_searchController.tvShows.length}');

    // Check if we have any movies or TV shows
    if (_searchController.movies.isEmpty && _searchController.tvShows.isEmpty) {
      debugPrint('No results to display, showing no results screen instead');
      return _buildNoResultsScreen();
    }

    return CustomScrollView(
      slivers: [
        // Search info
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              '"${_searchController.query.value}" এর জন্য ${_searchController.totalResults} টি ফলাফল পাওয়া গেছে',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[400],
              ),
            ),
          ),
        ),

        // Movies section
        if (_searchController.movies.isNotEmpty) ...[
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  const Icon(Icons.movie, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'মুভি (${_searchController.movies.length})',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
          _buildMoviesGrid(),
        ],

        // TV Shows section
        if (_searchController.tvShows.isNotEmpty) ...[
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  const Icon(Icons.tv, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'টিভি সিরিজ (${_searchController.tvShows.length})',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
          _buildTvShowsGrid(),
        ],

        // Bottom padding
        const SliverToBoxAdapter(
          child: SizedBox(height: 24),
        ),
      ],
    );
  }

  // Movies grid
  Widget _buildMoviesGrid() {
    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      sliver: SliverGrid(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 0.7,
          crossAxisSpacing: 12,
          mainAxisSpacing: 16,
        ),
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            final movie = _searchController.movies[index];
            return _buildMovieItem(movie);
          },
          childCount: _searchController.movies.length,
        ),
      ),
    );
  }

  // TV Shows grid
  Widget _buildTvShowsGrid() {
    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      sliver: SliverGrid(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 0.7,
          crossAxisSpacing: 12,
          mainAxisSpacing: 16,
        ),
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            final tvShow = _searchController.tvShows[index];
            return _buildTvShowItem(tvShow);
          },
          childCount: _searchController.tvShows.length,
        ),
      ),
    );
  }

  // Movie item
  Widget _buildMovieItem(Movie movie) {
    return GestureDetector(
      onTap: () => Get.to(() => MovieDetailsScreen(movieId: movie.id)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Poster
          Expanded(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Stack(
                fit: StackFit.expand,
                children: [
                  // Poster image
                  CachedNetworkImage(
                    imageUrl: movie.poster,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Shimmer.fromColors(
                      baseColor: Colors.grey[850]!,
                      highlightColor: Colors.grey[800]!,
                      child: Container(color: Colors.grey[850]),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey[850],
                      child: const Icon(Icons.movie, color: Colors.grey),
                    ),
                  ),

                  // Rating badge
                  if (movie.rating > 0)
                    Positioned(
                      top: 4,
                      right: 4,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withAlpha(179),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.star,
                              color: Colors.amber,
                              size: 12,
                            ),
                            const SizedBox(width: 2),
                            Text(
                              movie.rating.toString(),
                              style: const TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),

          // Title
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              movie.title,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // Year
          Text(
            movie.releaseYear.toString(),
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[400],
            ),
          ),
        ],
      ),
    );
  }

  // TV Show item
  Widget _buildTvShowItem(TvShow tvShow) {
    return GestureDetector(
      onTap: () => Get.to(() => TvShowDetailsScreen(tvShowId: tvShow.id)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Poster
          Expanded(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Stack(
                fit: StackFit.expand,
                children: [
                  // Poster image
                  CachedNetworkImage(
                    imageUrl: tvShow.poster,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Shimmer.fromColors(
                      baseColor: Colors.grey[850]!,
                      highlightColor: Colors.grey[800]!,
                      child: Container(color: Colors.grey[850]),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey[850],
                      child: const Icon(Icons.tv, color: Colors.grey),
                    ),
                  ),

                  // Rating badge
                  if (tvShow.rating > 0)
                    Positioned(
                      top: 4,
                      right: 4,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withAlpha(179),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.star,
                              color: Colors.amber,
                              size: 12,
                            ),
                            const SizedBox(width: 2),
                            Text(
                              tvShow.rating.toString(),
                              style: const TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),

          // Title
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              tvShow.title,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // Year
          Text(
            tvShow.startYear.toString(),
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[400],
            ),
          ),
        ],
      ),
    );
  }
}
