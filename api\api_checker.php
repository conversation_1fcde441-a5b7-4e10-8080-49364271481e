<?php
// API Checker Script
// This script checks if all API endpoints are working correctly

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Database connection
$db_host = 'localhost';
$db_user = 'tipsbdxy_4525';
$db_pass = 'tipsbdxy_4525';
$db_name = 'tipsbdxy_4525';

// Create connection
$conn = mysqli_connect($db_host, $db_user, $db_pass, $db_name);

// Check connection
if (!$conn) {
    die("<div class='error'>Database connection failed: " . mysqli_connect_error() . "</div>");
}

// Site URL
$site_url = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http";
$site_url .= "://" . $_SERVER['HTTP_HOST'];
$site_url .= str_replace('/api_checker.php', '', $_SERVER['REQUEST_URI']);

// API base URL
$api_base_url = $site_url;

// CSS styles
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CinePix API Checker</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .success {
            color: #27ae60;
            background-color: #e8f5e9;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .error {
            color: #e74c3c;
            background-color: #fdecea;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .warning {
            color: #f39c12;
            background-color: #fef5e7;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .info {
            color: #3498db;
            background-color: #e3f2fd;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .endpoint {
            font-family: monospace;
            font-weight: bold;
        }
        .method {
            display: inline-block;
            padding: 3px 6px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .get {
            background-color: #e3f2fd;
            color: #0d47a1;
        }
        .post {
            background-color: #e8f5e9;
            color: #1b5e20;
        }
        .btn {
            display: inline-block;
            padding: 8px 16px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .status-code {
            font-weight: bold;
        }
        .status-200 {
            color: #27ae60;
        }
        .status-error {
            color: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CinePix API Checker</h1>
        <p>This tool checks if your API endpoints are working correctly.</p>
';

// Function to check if a table exists
function tableExists($conn, $tableName) {
    $result = mysqli_query($conn, "SHOW TABLES LIKE '$tableName'");
    return mysqli_num_rows($result) > 0;
}

// Function to check if a column exists in a table
function columnExists($conn, $tableName, $columnName) {
    $result = mysqli_query($conn, "SHOW COLUMNS FROM $tableName LIKE '$columnName'");
    return mysqli_num_rows($result) > 0;
}

// Function to make API request
function makeRequest($url, $method = 'GET', $data = null) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }
    
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    return [
        'code' => $httpCode,
        'response' => $response ? json_decode($response, true) : null,
        'error' => $error
    ];
}

// Check database tables
echo '<h2>Database Check</h2>';
echo '<table>';
echo '<tr><th>Table</th><th>Status</th></tr>';

$requiredTables = [
    'users' => 'Users table',
    'movies' => 'Movies table',
    'tvshows' => 'TV Shows table',
    'episodes' => 'Episodes table',
    'categories' => 'Categories table',
    'download_links' => 'Download Links table',
    'app_config' => 'App Configuration table',
    'device_tokens' => 'Device Tokens table',
    'notifications' => 'Notifications table',
    'subtitles' => 'Subtitles table',
    'api_logs' => 'API Logs table',
    'payment_settings' => 'Payment Settings table',
    'payment_requests' => 'Payment Requests table',
    'subscriptions' => 'Subscriptions table',
    'favorites' => 'Favorites table'
];

$missingTables = [];
foreach ($requiredTables as $table => $description) {
    $exists = tableExists($conn, $table);
    echo '<tr>';
    echo '<td>' . $description . ' (' . $table . ')</td>';
    if ($exists) {
        echo '<td><span class="success">✓ Found</span></td>';
    } else {
        echo '<td><span class="error">✗ Missing</span></td>';
        $missingTables[] = $table;
    }
    echo '</tr>';
}
echo '</table>';

if (!empty($missingTables)) {
    echo '<div class="warning">';
    echo '<strong>Warning:</strong> Some required tables are missing. Please run the database update script:';
    echo '<br><a href="simple_update.php" class="btn">Run Database Update</a>';
    echo '</div>';
}

// Check required columns
echo '<h2>Required Columns Check</h2>';
echo '<table>';
echo '<tr><th>Table</th><th>Column</th><th>Status</th></tr>';

$requiredColumns = [
    'users' => ['profile_image'],
    'download_links' => ['server_name', 'file_size'],
    'watchlist' => ['last_watched_position', 'last_watched_at']
];

$missingColumns = [];
foreach ($requiredColumns as $table => $columns) {
    if (tableExists($conn, $table)) {
        foreach ($columns as $column) {
            $exists = columnExists($conn, $table, $column);
            echo '<tr>';
            echo '<td>' . $table . '</td>';
            echo '<td>' . $column . '</td>';
            if ($exists) {
                echo '<td><span class="success">✓ Found</span></td>';
            } else {
                echo '<td><span class="error">✗ Missing</span></td>';
                $missingColumns[] = "$table.$column";
            }
            echo '</tr>';
        }
    }
}
echo '</table>';

if (!empty($missingColumns)) {
    echo '<div class="warning">';
    echo '<strong>Warning:</strong> Some required columns are missing. Please run the database update script:';
    echo '<br><a href="simple_update.php" class="btn">Run Database Update</a>';
    echo '</div>';
}

// Check API endpoints
echo '<h2>API Endpoints Check</h2>';
echo '<table>';
echo '<tr><th>Endpoint</th><th>Method</th><th>Status</th><th>Response</th></tr>';

$endpoints = [
    ['v1/direct_config.php', 'GET', null],
    ['v1/direct_movies.php', 'GET', null],
    ['v1/direct_tvshows.php', 'GET', null],
    ['v1/direct_categories.php', 'GET', null],
    ['v1/direct_search.php?q=test', 'GET', null],
    ['v1/direct_movie_details.php?id=1', 'GET', null],
    ['v1/direct_tvshow_details.php?id=1', 'GET', null]
];

$failedEndpoints = [];
foreach ($endpoints as $endpoint) {
    $url = $api_base_url . '/' . $endpoint[0];
    $method = $endpoint[1];
    $data = $endpoint[2];
    
    $result = makeRequest($url, $method, $data);
    
    echo '<tr>';
    echo '<td class="endpoint">' . $endpoint[0] . '</td>';
    echo '<td><span class="method ' . strtolower($method) . '">' . $method . '</span></td>';
    
    if ($result['code'] >= 200 && $result['code'] < 300) {
        echo '<td><span class="status-code status-200">' . $result['code'] . ' OK</span></td>';
    } else {
        echo '<td><span class="status-code status-error">' . $result['code'] . ' Error</span></td>';
        $failedEndpoints[] = $endpoint[0];
    }
    
    echo '<td>';
    if ($result['error']) {
        echo '<div class="error">Error: ' . $result['error'] . '</div>';
    } elseif ($result['response']) {
        echo '<pre>' . json_encode($result['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
    } else {
        echo '<div class="warning">No response</div>';
    }
    echo '</td>';
    echo '</tr>';
}
echo '</table>';

if (!empty($failedEndpoints)) {
    echo '<div class="error">';
    echo '<strong>Error:</strong> Some API endpoints failed. Please check your API configuration.';
    echo '</div>';
}

// Check config.php file
echo '<h2>Configuration Check</h2>';

$configFile = '../config.php';
$directConfigFile = 'direct_config.php';

echo '<table>';
echo '<tr><th>Configuration</th><th>Status</th></tr>';

// Check if config.php exists
if (file_exists($configFile)) {
    echo '<tr><td>config.php file</td><td><span class="success">✓ Found</span></td></tr>';
    
    // Check if JWT_SECRET is defined in config.php
    $configContent = file_get_contents($configFile);
    if (strpos($configContent, 'JWT_SECRET') !== false) {
        echo '<tr><td>JWT_SECRET in config.php</td><td><span class="success">✓ Found</span></td></tr>';
    } else {
        echo '<tr><td>JWT_SECRET in config.php</td><td><span class="error">✗ Missing</span></td></tr>';
    }
} else {
    echo '<tr><td>config.php file</td><td><span class="error">✗ Missing</span></td></tr>';
}

// Check if direct_config.php exists
if (file_exists($directConfigFile)) {
    echo '<tr><td>direct_config.php file</td><td><span class="success">✓ Found</span></td></tr>';
} else {
    echo '<tr><td>direct_config.php file</td><td><span class="error">✗ Missing</span></td></tr>';
}

// Check if .htaccess exists
if (file_exists('.htaccess')) {
    echo '<tr><td>.htaccess file</td><td><span class="success">✓ Found</span></td></tr>';
} else {
    echo '<tr><td>.htaccess file</td><td><span class="error">✗ Missing</span></td></tr>';
}

echo '</table>';

// Summary and recommendations
echo '<h2>Summary</h2>';

if (empty($missingTables) && empty($missingColumns) && empty($failedEndpoints)) {
    echo '<div class="success">';
    echo '<strong>Success!</strong> Your API is properly configured and ready to use.';
    echo '</div>';
} else {
    echo '<div class="warning">';
    echo '<strong>Warning:</strong> There are some issues with your API configuration. Please fix them before using the API.';
    echo '</div>';
    
    echo '<h3>Recommendations:</h3>';
    echo '<ol>';
    
    if (!empty($missingTables) || !empty($missingColumns)) {
        echo '<li>Run the database update script to create missing tables and columns: <a href="simple_update.php" class="btn">Run Database Update</a></li>';
    }
    
    if (!file_exists($configFile) || strpos(file_get_contents($configFile), 'JWT_SECRET') === false) {
        echo '<li>Make sure your config.php file exists and contains the JWT_SECRET constant.</li>';
    }
    
    if (!file_exists('.htaccess')) {
        echo '<li>Create an .htaccess file in the API directory with the following content:
        <pre>RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^v1/(.*)$ v1/index.php [QSA,L]</pre></li>';
    }
    
    echo '</ol>';
}

// API Documentation link
echo '<h2>API Documentation</h2>';
echo '<p>View the API documentation to learn how to use the API endpoints:</p>';
echo '<a href="v1/api_documentation.html" class="btn" target="_blank">View API Documentation</a>';

// Test API Connection
echo '<h2>Test API Connection</h2>';
echo '<p>Click the button below to test the API connection:</p>';
echo '<a href="v1/direct_config.php" class="btn" target="_blank">Test API Connection</a>';

// Close database connection
mysqli_close($conn);

echo '</div>
</body>
</html>';
?>
