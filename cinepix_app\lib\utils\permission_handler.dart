import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';

class PermissionManager {
  /// Request all required permissions at app startup
  static Future<void> requestAllPermissions(BuildContext context) async {
    // For Android 11+ (API 30+), we need different permissions
    if (Platform.isAndroid) {
      // Get Android SDK version
      final deviceInfoPlugin = DeviceInfoPlugin();
      final androidInfo = await deviceInfoPlugin.androidInfo;
      final sdkVersion = androidInfo.version.sdkInt;

      if (sdkVersion >= 30) {
        // Android 11+
        // Request media permissions for Android 11+
        final permissions = [
          Permission.storage,
          Permission.manageExternalStorage,
          Permission.photos,
          Permission.mediaLibrary,
        ];

        // Request all permissions
        Map<Permission, PermissionStatus> statuses =
            await permissions.request();

        // Check if manage external storage is permanently denied
        if (statuses[Permission.manageExternalStorage]?.isPermanentlyDenied ==
            true) {
          if (context.mounted) {
            _showManageStorageDialog(context);
            return;
          }
        }

        // Check if any permission was denied
        bool anyDenied = statuses.values.any((status) => status.isDenied);
        if (anyDenied && context.mounted) {
          _showPermissionDialog(context);
        }
      } else {
        // For Android 10 and below, request storage permission
        final permissions = [
          Permission.storage,
          Permission.photos,
          Permission.mediaLibrary,
        ];

        // Request all permissions
        Map<Permission, PermissionStatus> statuses =
            await permissions.request();

        // Check if any permission was denied
        bool anyDenied = statuses.values.any((status) => status.isDenied);
        if (anyDenied && context.mounted) {
          _showPermissionDialog(context);
        }
      }
    }
  }

  /// Show a dialog explaining why permissions are needed
  static void _showPermissionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('পারমিশন প্রয়োজন'),
        content: const Text(
          'সিনেপিক্স অ্যাপটি সঠিকভাবে কাজ করার জন্য স্টোরেজ পারমিশন প্রয়োজন। '
          'এটি ছাড়া আপনি ভিডিও দেখতে বা ডাউনলোড করতে পারবেন না।',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('বাতিল'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
            ),
            child: const Text(
              'সেটিংস খুলুন',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  /// Show a dialog specifically for Manage External Storage permission
  static void _showManageStorageDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('বিশেষ পারমিশন প্রয়োজন'),
        content: const Text(
          'সিনেপিক্স অ্যাপটি ফাইল ডাউনলোড করার জন্য "সমস্ত ফাইল অ্যাক্সেস" পারমিশন প্রয়োজন। '
          'দয়া করে সেটিংসে গিয়ে "সমস্ত ফাইল অ্যাক্সেস" পারমিশন এনাবল করুন।',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('বাতিল'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
            ),
            child: const Text(
              'সেটিংস খুলুন',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  /// Check and request storage permissions for download
  static Future<bool> checkAndRequestStoragePermission(
      BuildContext context) async {
    if (Platform.isAndroid) {
      // Get Android SDK version
      final deviceInfoPlugin = DeviceInfoPlugin();
      final androidInfo = await deviceInfoPlugin.androidInfo;
      final sdkVersion = androidInfo.version.sdkInt;

      if (sdkVersion >= 30) {
        // Android 11+
        // Check if we have manage external storage permission
        final status = await Permission.manageExternalStorage.status;

        if (status.isGranted) {
          return true;
        } else {
          // Request permission
          final result = await Permission.manageExternalStorage.request();

          if (result.isGranted) {
            return true;
          } else if (result.isPermanentlyDenied && context.mounted) {
            _showManageStorageDialog(context);
            return false;
          } else if (context.mounted) {
            _showPermissionDialog(context);
            return false;
          }
        }
      } else {
        // For Android 10 and below
        final status = await Permission.storage.status;

        if (status.isGranted) {
          return true;
        } else {
          // Request permission
          final result = await Permission.storage.request();

          if (result.isGranted) {
            return true;
          } else if (context.mounted) {
            _showPermissionDialog(context);
            return false;
          }
        }
      }
    }

    return false;
  }
}
