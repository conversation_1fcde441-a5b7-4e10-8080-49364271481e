# CinePix API সেটআপ গাইড

এই ডকুমেন্টটি আপনাকে CinePix API লাইভ সাইটে আপলোড করতে এবং সেটআপ করতে সাহায্য করবে।

## ১. API ফোল্ডার আপলোড করা

প্রথমে, আপনার সম্পূর্ণ API ফোল্ডার লাইভ সাইটে আপলোড করুন:

1. আপনার FTP ক্লায়েন্ট (FileZilla বা অন্য কোন FTP সফটওয়্যার) ব্যবহার করে লাইভ সাইটে কানেক্ট করুন
2. সাইটের রুট ডিরেক্টরিতে `api` ফোল্ডার আপলোড করুন
3. সমস্ত ফাইল আপলোড হয়েছে কিনা নিশ্চিত করুন

## ২. ডাটাবেস আপডেট করা

API-এর জন্য প্রয়োজনীয় টেবিল এবং কলাম তৈরি করতে:

1. আপনার ব্রাউজারে `https://cinepix.top/api/simple_update.php` ভিজিট করুন
2. স্ক্রিপ্টটি স্বয়ংক্রিয়ভাবে প্রয়োজনীয় টেবিল এবং কলাম তৈরি করবে
3. সমস্ত আপডেট সফলভাবে সম্পন্ন হয়েছে কিনা নিশ্চিত করুন

## ৩. API চেক করা

API সঠিকভাবে কাজ করছে কিনা তা পরীক্ষা করতে:

1. আপনার ব্রাউজারে `https://cinepix.top/api/api_checker.php` ভিজিট করুন
2. এই টুলটি আপনার API এন্ডপয়েন্টগুলি পরীক্ষা করবে এবং কোন সমস্যা থাকলে তা রিপোর্ট করবে
3. যদি কোন সমস্যা থাকে, তাহলে সেগুলি সমাধান করুন

## ৪. কনফিগারেশন চেক করা

API কনফিগারেশন সঠিক আছে কিনা নিশ্চিত করতে:

1. `api/direct_config.php` ফাইলে ডাটাবেস কানেকশন সেটিংস সঠিক আছে কিনা চেক করুন:
   ```php
   $db_host = 'localhost';
   $db_user = 'tipsbdxy_4525'; // আপনার ডাটাবেস ইউজারনেম
   $db_pass = 'tipsbdxy_4525'; // আপনার ডাটাবেস পাসওয়ার্ড
   $db_name = 'tipsbdxy_4525'; // আপনার ডাটাবেস নাম
   ```

2. `api/direct_config.php` ফাইলে সাইট সেটিংস সঠিক আছে কিনা চেক করুন:
   ```php
   define('SITE_NAME', 'CinePix');
   define('SITE_URL', 'https://cinepix.top');
   define('SITE_EMAIL', '<EMAIL>');
   ```

## ৫. API টেস্ট করা

API সঠিকভাবে কাজ করছে কিনা পরীক্ষা করতে:

1. আপনার ব্রাউজারে `https://cinepix.top/api/v1/direct_config.php` ভিজিট করুন
2. আপনার JSON রেসপন্স দেখতে পাওয়া উচিত
3. অন্যান্য API এন্ডপয়েন্ট পরীক্ষা করুন:
   - `https://cinepix.top/api/v1/direct_movies.php`
   - `https://cinepix.top/api/v1/direct_tvshows.php`
   - `https://cinepix.top/api/v1/direct_categories.php`

## ৬. সম্ভাব্য সমস্যা এবং সমাধান

### ডাটাবেস কানেকশন এরর

যদি ডাটাবেস কানেকশন এরর দেখায়:

1. `api/direct_config.php` ফাইলে ডাটাবেস কানেকশন সেটিংস চেক করুন
2. সঠিক ডাটাবেস ইউজারনেম, পাসওয়ার্ড এবং ডাটাবেস নাম ব্যবহার করছেন কিনা নিশ্চিত করুন

### 404 এরর

যদি API এন্ডপয়েন্টগুলি 404 এরর দেখায়:

1. `.htaccess` ফাইল সঠিকভাবে আপলোড হয়েছে কিনা চেক করুন
2. আপনার সার্ভারে mod_rewrite এনাবল আছে কিনা নিশ্চিত করুন

### 500 এরর

যদি API এন্ডপয়েন্টগুলি 500 এরর দেখায়:

1. PHP এরর লগ চেক করুন
2. `api/direct_config.php` ফাইলে ডাটাবেস কানেকশন সেটিংস চেক করুন
3. প্রয়োজনীয় টেবিল এবং কলাম আছে কিনা নিশ্চিত করুন

## ৭. মোবাইল অ্যাপ ইন্টিগ্রেশন

মোবাইল অ্যাপে API ব্যবহার করতে:

1. অ্যাপের API বেস URL সেট করুন: `https://cinepix.top/api/v1`
2. API এন্ডপয়েন্টগুলি ব্যবহার করুন:
   - `direct_config.php` - অ্যাপ কনফিগারেশন পেতে
   - `direct_login.php` - ইউজার লগইন করাতে
   - `direct_register.php` - নতুন ইউজার রেজিস্ট্রেশন করাতে
   - `direct_movies.php` - মুভি লিস্ট পেতে
   - `direct_movie_details.php?id={movie_id}` - মুভি ডিটেইলস পেতে
   - `direct_tvshows.php` - টিভি শো লিস্ট পেতে
   - `direct_tvshow_details.php?id={tvshow_id}` - টিভি শো ডিটেইলস পেতে
   - `direct_tvshow_episodes.php?id={tvshow_id}&season={season_number}` - টিভি শো এপিসোড লিস্ট পেতে
   - `direct_search.php?q={search_query}` - সার্চ করতে
   - `direct_categories.php` - ক্যাটাগরি লিস্ট পেতে
   - `direct_profile.php` - ইউজার প্রোফাইল পেতে

## ৮. সিকিউরিটি নোট

API সিকিউরিটি নিশ্চিত করতে:

1. `JWT_SECRET` কনস্ট্যান্ট একটি শক্তিশালী র‍্যান্ডম স্ট্রিং দিয়ে সেট করুন
2. সমস্ত সেনসিটিভ API এন্ডপয়েন্টে JWT অথেনটিকেশন ব্যবহার করুন
3. API লগিং এনাবল করুন সমস্ত রিকোয়েস্ট ট্র্যাক করার জন্য

## ৯. ট্রাবলশুটিং

যদি API চেকার টুল কোন সমস্যা দেখায়:

1. সমস্যাগুলি সমাধান করুন
2. `simple_update.php` স্ক্রিপ্ট আবার রান করুন
3. API চেকার টুল আবার রান করুন সমস্যা সমাধান হয়েছে কিনা দেখতে

## ১০. সাপোর্ট

যদি আপনার কোন প্রশ্ন বা সমস্যা থাকে, তাহলে যোগাযোগ করুন:

- ইমেইল: <EMAIL>
