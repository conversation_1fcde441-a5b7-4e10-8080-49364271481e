<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include config file
require_once '../config.php';

// Test database connection
echo "<h1>API Test</h1>";
echo "<pre>";

echo "PHP Version: " . phpversion() . "\n";

// Check if database connection works
if (isset($conn) && $conn) {
    echo "Database connection: SUCCESS\n";
    
    // Check if tables exist
    $tables = ['users', 'movies', 'tvshows', 'episodes', 'download_links', 'app_config', 'device_tokens', 'notifications', 'subtitles'];
    
    foreach ($tables as $table) {
        $result = mysqli_query($conn, "SHOW TABLES LIKE '$table'");
        if (mysqli_num_rows($result) > 0) {
            echo "Table $table: EXISTS\n";
        } else {
            echo "Table $table: MISSING\n";
        }
    }
    
    // Check if JWT_SECRET is defined
    if (defined('JWT_SECRET')) {
        echo "JWT_SECRET: DEFINED\n";
    } else {
        echo "JWT_SECRET: NOT DEFINED\n";
    }
    
    // Check if app_config table has data
    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM app_config");
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        echo "app_config entries: " . $row['count'] . "\n";
    } else {
        echo "app_config query failed: " . mysqli_error($conn) . "\n";
    }
} else {
    echo "Database connection: FAILED\n";
    if (isset($conn)) {
        echo "Error: " . mysqli_connect_error() . "\n";
    } else {
        echo "Error: \$conn variable not set\n";
    }
}

echo "</pre>";
?>
