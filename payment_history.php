<?php
require_once 'includes/header.php';

// Check if user is logged in
if (!isLoggedIn()) {
    // Save current page as redirect destination after login
    $_SESSION['redirect_to'] = SITE_URL . '/payment_history.php';
    redirect(SITE_URL . '/login.php');
}

// Get user data
$user_id = $_SESSION['user_id'];
$user_query = "SELECT * FROM users WHERE id = $user_id";
$user_result = mysqli_query($conn, $user_query);
$user = mysqli_fetch_assoc($user_result);

// Get active subscription details if premium
$active_subscription = null;
if (isPremium()) {
    $subscription_query = "SELECT s.*, p.name as plan_name, p.price, p.features
                          FROM subscriptions s
                          JOIN premium_plans p ON s.plan_id = p.id
                          WHERE s.user_id = $user_id AND s.status = 'active' AND s.end_date > NOW()
                          ORDER BY s.end_date DESC LIMIT 1";
    $subscription_result = mysqli_query($conn, $subscription_query);

    if (mysqli_num_rows($subscription_result) > 0) {
        $active_subscription = mysqli_fetch_assoc($subscription_result);
    }
}

// Get all payment history
$payment_query = "SELECT p.*, s.plan_id, pp.name as plan_name
                FROM payments p
                JOIN subscriptions s ON p.subscription_id = s.id
                JOIN premium_plans pp ON s.plan_id = pp.id
                WHERE p.user_id = $user_id
                ORDER BY p.payment_date DESC";
$payment_result = mysqli_query($conn, $payment_query);
?>

<!-- Page Header -->
<section class="py-5 bg-dark">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="text-light mb-0">Payment History</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>" class="text-decoration-none">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>/profile.php" class="text-decoration-none">Profile</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Payment History</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-6 text-md-end mt-3 mt-md-0">
                <?php if(!isPremium()): ?>
                <a href="<?php echo SITE_URL; ?>/payment.php" class="btn btn-warning">
                    <i class="fas fa-crown me-1"></i> Upgrade to Premium
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Payment History Content -->
<section class="py-5">
    <div class="container">
        <?php if($active_subscription): ?>
        <div class="card bg-dark mb-4">
            <div class="card-header bg-dark border-secondary">
                <h5 class="mb-0 text-warning"><i class="fas fa-crown me-2"></i>Active Subscription</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-light"><?php echo $active_subscription['plan_name']; ?> Plan</h6>
                        <div class="subscription-info-card mt-3">
                            <div class="subscription-info-item">
                                <span class="info-label">Price:</span>
                                <span class="info-value">৳<?php echo $active_subscription['price']; ?>/month</span>
                            </div>
                            <div class="subscription-info-item">
                                <span class="info-label">Started:</span>
                                <span class="info-value"><?php echo date('d M Y', strtotime($active_subscription['start_date'])); ?></span>
                            </div>
                            <div class="subscription-info-item">
                                <span class="info-label">Expires:</span>
                                <span class="info-value"><?php echo date('d M Y', strtotime($active_subscription['end_date'])); ?></span>
                            </div>
                            <div class="subscription-info-item">
                                <span class="info-label">Status:</span>
                                <span class="badge bg-success">Active</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-light">Features:</h6>
                        <ul class="features-list">
                            <?php foreach(explode("\n", $active_subscription['features']) as $feature): ?>
                            <li><?php echo $feature; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <div class="card bg-dark">
            <div class="card-header bg-dark border-secondary d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>Payment History</h5>
            </div>
            <div class="card-body">
                <?php if(mysqli_num_rows($payment_result) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-dark table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Transaction ID</th>
                                <th>Plan</th>
                                <th>Amount</th>
                                <th>Method</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while($payment = mysqli_fetch_assoc($payment_result)): ?>
                            <tr>
                                <td><?php echo date('d M Y', strtotime($payment['payment_date'])); ?></td>
                                <td><?php echo $payment['transaction_id'] ? $payment['transaction_id'] : 'N/A'; ?></td>
                                <td><?php echo $payment['plan_name']; ?></td>
                                <td>৳<?php echo $payment['amount']; ?></td>
                                <td>
                                    <?php
                                    $method_icon = '';
                                    switch($payment['payment_method']) {
                                        case 'bkash':
                                            $method_icon = 'text-danger';
                                            break;
                                        case 'nagad':
                                            $method_icon = 'text-warning';
                                            break;
                                        case 'rocket':
                                            $method_icon = 'text-primary';
                                            break;
                                        default:
                                            $method_icon = 'text-secondary';
                                    }
                                    ?>
                                    <span class="<?php echo $method_icon; ?>">
                                        <?php echo ucfirst($payment['payment_method']); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php
                                    $status_class = '';
                                    switch($payment['payment_status']) {
                                        case 'completed':
                                            $status_class = 'bg-success';
                                            break;
                                        case 'pending':
                                            $status_class = 'bg-warning text-dark';
                                            break;
                                        case 'failed':
                                            $status_class = 'bg-danger';
                                            break;
                                        case 'refunded':
                                            $status_class = 'bg-info';
                                            break;
                                    }
                                    ?>
                                    <span class="badge <?php echo $status_class; ?>">
                                        <?php echo ucfirst($payment['payment_status']); ?>
                                    </span>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No payment history found</h5>
                    <?php if(!isPremium()): ?>
                    <a href="<?php echo SITE_URL; ?>/payment.php" class="btn btn-warning mt-3">
                        <i class="fas fa-crown me-1"></i> Upgrade to Premium
                    </a>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<?php require_once 'includes/footer.php'; ?>
