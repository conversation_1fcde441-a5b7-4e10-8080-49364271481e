<?php
// API Notifications Endpoints

// Get the request
global $request;

// Check if user is authenticated
if (!is_authenticated() && $request['parts'][0] !== 'public') {
    api_error('Authentication required', 401);
}

// Get authenticated user
$current_user = get_authenticated_user();

// Handle different endpoints based on the path parts
$action = $request['parts'][0] ?? 'list';

switch ($action) {
    case 'list':
        handle_list_notifications($current_user, $request);
        break;
    
    case 'register_device':
        handle_register_device($current_user, $request);
        break;
    
    case 'mark_read':
        handle_mark_notification_read($current_user, $request);
        break;
    
    case 'mark_all_read':
        handle_mark_all_notifications_read($current_user, $request);
        break;
    
    case 'send':
        if (is_admin()) {
            handle_send_notification($request);
        } else {
            api_error('Unauthorized access', 403);
        }
        break;
    
    case 'public':
        handle_public_notifications($request);
        break;
    
    default:
        api_error('Invalid notifications endpoint', 404);
}

// Handle list notifications
function handle_list_notifications($user, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get pagination parameters
    $page = isset($request['params']['page']) ? (int)$request['params']['page'] : 1;
    $limit = isset($request['params']['limit']) ? (int)$request['params']['limit'] : 20;
    $offset = ($page - 1) * $limit;
    
    // Get notifications for the user
    $query = "SELECT * FROM notifications 
              WHERE user_id = ? OR user_id IS NULL 
              ORDER BY created_at DESC 
              LIMIT ? OFFSET ?";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'iii', $user['user_id'], $limit, $offset);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $notifications = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $notifications[] = [
            'id' => (int)$row['id'],
            'title' => $row['title'],
            'message' => $row['message'],
            'image_url' => $row['image_url'],
            'action_type' => $row['action_type'],
            'action_id' => $row['action_id'],
            'is_read' => (bool)$row['is_read'],
            'created_at' => $row['created_at']
        ];
    }
    
    // Get total count for pagination
    $count_query = "SELECT COUNT(*) as total FROM notifications 
                   WHERE user_id = ? OR user_id IS NULL";
    
    $count_stmt = mysqli_prepare($conn, $count_query);
    mysqli_stmt_bind_param($count_stmt, 'i', $user['user_id']);
    mysqli_stmt_execute($count_stmt);
    $count_result = mysqli_stmt_get_result($count_stmt);
    $total = mysqli_fetch_assoc($count_result)['total'];
    
    // Get unread count
    $unread_query = "SELECT COUNT(*) as unread FROM notifications 
                    WHERE (user_id = ? OR user_id IS NULL) AND is_read = 0";
    
    $unread_stmt = mysqli_prepare($conn, $unread_query);
    mysqli_stmt_bind_param($unread_stmt, 'i', $user['user_id']);
    mysqli_stmt_execute($unread_stmt);
    $unread_result = mysqli_stmt_get_result($unread_stmt);
    $unread_count = mysqli_fetch_assoc($unread_result)['unread'];
    
    // Return notifications with pagination
    api_response([
        'notifications' => $notifications,
        'unread_count' => (int)$unread_count,
        'meta' => [
            'pagination' => [
                'total' => (int)$total,
                'per_page' => $limit,
                'current_page' => $page,
                'last_page' => ceil($total / $limit)
            ]
        ]
    ]);
}

// Handle register device token
function handle_register_device($user, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'POST') {
        api_error('Method not allowed', 405);
    }
    
    // Validate required fields
    if (empty($request['body']['device_token']) || empty($request['body']['device_type'])) {
        api_error('Device token and type are required', 400);
    }
    
    $device_token = $request['body']['device_token'];
    $device_type = $request['body']['device_type'];
    
    // Validate device type
    if (!in_array($device_type, ['android', 'ios'])) {
        api_error('Invalid device type. Must be android or ios', 400);
    }
    
    // Insert or update device token
    $query = "INSERT INTO device_tokens (user_id, device_token, device_type) 
              VALUES (?, ?, ?) 
              ON DUPLICATE KEY UPDATE user_id = ?, device_type = ?, updated_at = NOW()";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'isssi', $user['user_id'], $device_token, $device_type, $user['user_id'], $device_type);
    
    if (!mysqli_stmt_execute($stmt)) {
        api_error('Failed to register device: ' . mysqli_error($conn), 500);
    }
    
    // Return success response
    api_response([
        'device_token' => $device_token,
        'device_type' => $device_type
    ], 200, 'Device registered successfully');
}

// Handle mark notification as read
function handle_mark_notification_read($user, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'POST') {
        api_error('Method not allowed', 405);
    }
    
    // Validate required fields
    if (empty($request['body']['notification_id'])) {
        api_error('Notification ID is required', 400);
    }
    
    $notification_id = (int)$request['body']['notification_id'];
    
    // Update notification
    $query = "UPDATE notifications SET is_read = 1 
              WHERE id = ? AND (user_id = ? OR user_id IS NULL)";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'ii', $notification_id, $user['user_id']);
    
    if (!mysqli_stmt_execute($stmt)) {
        api_error('Failed to mark notification as read: ' . mysqli_error($conn), 500);
    }
    
    // Check if notification was found and updated
    if (mysqli_stmt_affected_rows($stmt) === 0) {
        api_error('Notification not found or not accessible', 404);
    }
    
    // Return success response
    api_response([
        'notification_id' => $notification_id
    ], 200, 'Notification marked as read');
}

// Handle mark all notifications as read
function handle_mark_all_notifications_read($user, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'POST') {
        api_error('Method not allowed', 405);
    }
    
    // Update all notifications
    $query = "UPDATE notifications SET is_read = 1 
              WHERE (user_id = ? OR user_id IS NULL) AND is_read = 0";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $user['user_id']);
    
    if (!mysqli_stmt_execute($stmt)) {
        api_error('Failed to mark all notifications as read: ' . mysqli_error($conn), 500);
    }
    
    // Return success response
    api_response([
        'affected_rows' => mysqli_stmt_affected_rows($stmt)
    ], 200, 'All notifications marked as read');
}

// Handle send notification (admin only)
function handle_send_notification($request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'POST') {
        api_error('Method not allowed', 405);
    }
    
    // Validate required fields
    if (empty($request['body']['title']) || empty($request['body']['message'])) {
        api_error('Title and message are required', 400);
    }
    
    $title = $request['body']['title'];
    $message = $request['body']['message'];
    $image_url = $request['body']['image_url'] ?? null;
    $action_type = $request['body']['action_type'] ?? 'none';
    $action_id = $request['body']['action_id'] ?? null;
    $user_id = $request['body']['user_id'] ?? null; // Optional, if null, send to all users
    
    // Validate action type
    if (!in_array($action_type, ['none', 'movie', 'tvshow', 'url'])) {
        api_error('Invalid action type', 400);
    }
    
    // Insert notification
    $query = "INSERT INTO notifications (user_id, title, message, image_url, action_type, action_id) 
              VALUES (?, ?, ?, ?, ?, ?)";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'isssss', $user_id, $title, $message, $image_url, $action_type, $action_id);
    
    if (!mysqli_stmt_execute($stmt)) {
        api_error('Failed to create notification: ' . mysqli_error($conn), 500);
    }
    
    $notification_id = mysqli_insert_id($conn);
    
    // TODO: Send push notification to device tokens
    // This would require integration with Firebase Cloud Messaging (FCM) or other push notification service
    
    // Return success response
    api_response([
        'notification_id' => $notification_id,
        'title' => $title,
        'message' => $message
    ], 201, 'Notification sent successfully');
}

// Handle public notifications (no authentication required)
function handle_public_notifications($request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get pagination parameters
    $page = isset($request['params']['page']) ? (int)$request['params']['page'] : 1;
    $limit = isset($request['params']['limit']) ? (int)$request['params']['limit'] : 10;
    $offset = ($page - 1) * $limit;
    
    // Get public notifications (where user_id is NULL)
    $query = "SELECT * FROM notifications 
              WHERE user_id IS NULL 
              ORDER BY created_at DESC 
              LIMIT ? OFFSET ?";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'ii', $limit, $offset);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $notifications = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $notifications[] = [
            'id' => (int)$row['id'],
            'title' => $row['title'],
            'message' => $row['message'],
            'image_url' => $row['image_url'],
            'action_type' => $row['action_type'],
            'action_id' => $row['action_id'],
            'created_at' => $row['created_at']
        ];
    }
    
    // Get total count for pagination
    $count_query = "SELECT COUNT(*) as total FROM notifications WHERE user_id IS NULL";
    $count_result = mysqli_query($conn, $count_query);
    $total = mysqli_fetch_assoc($count_result)['total'];
    
    // Return notifications with pagination
    api_response([
        'notifications' => $notifications,
        'meta' => [
            'pagination' => [
                'total' => (int)$total,
                'per_page' => $limit,
                'current_page' => $page,
                'last_page' => ceil($total / $limit)
            ]
        ]
    ]);
}
