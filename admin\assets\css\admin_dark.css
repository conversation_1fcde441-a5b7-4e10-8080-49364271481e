:root {
    /* Main colors */
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #64748b;
    --success-color: #22c55e;
    --info-color: #0ea5e9;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    
    /* Dark theme colors */
    --bg-dark: #0f172a;
    --bg-darker: #0a0f1d;
    --bg-card: #1e293b;
    --bg-card-hover: #2d3a50;
    --bg-input: #1e293b;
    --text-light: #f8fafc;
    --text-muted: #94a3b8;
    --text-dark: #e2e8f0;
    --border-color: #334155;
    
    /* Layout */
    --sidebar-width: 260px;
    --sidebar-collapsed-width: 70px;
    --topbar-height: 70px;
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
    --card-border-radius: 0.75rem;
}

/* Base styles */
body {
    font-family: 'Nunito', sans-serif;
    background-color: var(--bg-dark);
    color: var(--text-light);
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

a {
    text-decoration: none;
    transition: all 0.3s;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-darker);
}

::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Wrapper */
.wrapper {
    display: flex;
    width: 100%;
    align-items: stretch;
}

/* Sidebar */
.sidebar {
    min-width: var(--sidebar-width);
    max-width: var(--sidebar-width);
    background: linear-gradient(180deg, var(--bg-darker) 0%, var(--bg-dark) 100%);
    color: var(--text-light);
    transition: all 0.3s;
    z-index: 999;
    height: 100vh;
    position: fixed;
    overflow-y: auto;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
}

.sidebar.active {
    min-width: var(--sidebar-collapsed-width);
    max-width: var(--sidebar-collapsed-width);
}

.sidebar .sidebar-header {
    padding: 20px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.sidebar .sidebar-brand-icon {
    font-size: 1.5rem;
    margin-right: 10px;
}

.sidebar .sidebar-brand-text {
    display: flex;
    flex-direction: column;
}

.sidebar .site-name {
    font-size: 1.2rem;
    font-weight: 700;
    line-height: 1;
}

.sidebar .admin-text {
    font-size: 0.8rem;
    opacity: 0.7;
}

.sidebar ul.components {
    padding: 20px 0;
}

.sidebar ul li {
    position: relative;
}

.sidebar ul li a {
    padding: 12px 20px;
    display: flex;
    align-items: center;
    color: var(--text-light);
    border-left: 3px solid transparent;
}

.sidebar ul li a:hover {
    background: var(--bg-card);
    border-left-color: var(--primary-color);
}

.sidebar ul li a.active {
    background: var(--bg-card);
    border-left-color: var(--primary-color);
    color: var(--primary-color);
}

.sidebar ul li a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.sidebar ul li a .badge {
    margin-left: auto;
}

.sidebar .dropdown-toggle::after {
    display: block;
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
}

.sidebar ul ul a {
    padding-left: 50px;
    background: var(--bg-darker);
}

/* Main Content */
.content {
    width: calc(100% - var(--sidebar-width));
    min-height: 100vh;
    transition: all 0.3s;
    position: absolute;
    top: 0;
    right: 0;
    padding-top: var(--topbar-height);
    background-color: var(--bg-dark);
}

.content.expanded {
    width: calc(100% - var(--sidebar-collapsed-width));
}

/* Topbar */
.topbar {
    height: var(--topbar-height);
    background-color: var(--bg-darker);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    right: 0;
    left: var(--sidebar-width);
    z-index: 998;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    padding: 0 25px;
}

.content.expanded .topbar {
    left: var(--sidebar-collapsed-width);
}

.topbar-toggle {
    color: var(--text-light);
    background: transparent;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    margin-right: 15px;
}

.topbar-search {
    flex-grow: 1;
    max-width: 400px;
    margin: 0 15px;
}

.topbar-search .input-group {
    background-color: var(--bg-input);
    border-radius: 50px;
    overflow: hidden;
}

.topbar-search .form-control {
    background-color: transparent;
    border: none;
    color: var(--text-light);
    padding-left: 20px;
}

.topbar-search .form-control:focus {
    box-shadow: none;
}

.topbar-search .input-group-text {
    background-color: transparent;
    border: none;
    color: var(--text-muted);
}

.topbar-divider {
    width: 1px;
    background-color: var(--border-color);
    height: 40px;
    margin: 0 15px;
}

.topbar-nav {
    display: flex;
    align-items: center;
}

.topbar-nav .nav-item {
    position: relative;
}

.topbar-nav .nav-link {
    color: var(--text-light);
    padding: 0.5rem 0.75rem;
    font-size: 1.1rem;
    position: relative;
}

.topbar-nav .nav-link:hover {
    color: var(--primary-color);
}

.topbar-nav .badge-counter {
    position: absolute;
    top: 0;
    right: 0;
    transform: translate(25%, -25%);
    font-size: 0.6rem;
}

.topbar-nav .dropdown-menu {
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    box-shadow: var(--card-shadow);
    min-width: 15rem;
    padding: 0.5rem 0;
    margin-top: 0.5rem;
}

.topbar-nav .dropdown-item {
    color: var(--text-light);
    padding: 0.5rem 1.5rem;
}

.topbar-nav .dropdown-item:hover {
    background-color: var(--bg-card-hover);
}

.topbar-nav .dropdown-header {
    color: var(--text-muted);
    font-weight: 700;
    font-size: 0.65rem;
    padding: 0.5rem 1.5rem;
    text-transform: uppercase;
}

.topbar-nav .dropdown-divider {
    border-color: var(--border-color);
}

/* Cards */
.card {
    background-color: var(--bg-card);
    border: none;
    border-radius: var(--card-border-radius);
    box-shadow: var(--card-shadow);
    transition: all 0.3s;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.card-header {
    background-color: rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: rgba(0, 0, 0, 0.1);
    border-top: 1px solid var(--border-color);
    padding: 0.75rem 1.5rem;
}

/* Buttons */
.btn {
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    font-weight: 600;
    transition: all 0.3s;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

/* Tables */
.table {
    color: var(--text-light);
}

.table thead th {
    border-bottom: 1px solid var(--border-color);
    border-top: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.table td {
    border-color: var(--border-color);
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: var(--bg-card-hover);
}

/* Forms */
.form-control, .form-select {
    background-color: var(--bg-input);
    border: 1px solid var(--border-color);
    color: var(--text-light);
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
}

.form-control:focus, .form-select:focus {
    background-color: var(--bg-input);
    border-color: var(--primary-color);
    color: var(--text-light);
    box-shadow: 0 0 0 0.25rem rgba(99, 102, 241, 0.25);
}

.form-label {
    color: var(--text-light);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-text {
    color: var(--text-muted);
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        min-width: var(--sidebar-collapsed-width);
        max-width: var(--sidebar-collapsed-width);
    }

    .sidebar ul li a .menu-text,
    .sidebar .sidebar-brand-text,
    .sidebar .dropdown-toggle::after {
        display: none;
    }

    .sidebar ul li a {
        padding: 15px;
        justify-content: center;
    }

    .sidebar ul li a i {
        margin-right: 0;
        font-size: 1.2rem;
    }

    .sidebar .sidebar-header {
        justify-content: center;
    }

    .sidebar .sidebar-brand-icon {
        margin-right: 0;
    }

    .content {
        width: calc(100% - var(--sidebar-collapsed-width));
    }

    .topbar {
        left: var(--sidebar-collapsed-width);
    }

    .sidebar.active {
        min-width: var(--sidebar-width);
        max-width: var(--sidebar-width);
        position: fixed;
        z-index: 999;
    }

    .sidebar.active .sidebar-header {
        justify-content: flex-start;
    }

    .sidebar.active .sidebar-brand-icon {
        margin-right: 10px;
    }

    .sidebar.active .sidebar-brand-text,
    .sidebar.active ul li a .menu-text,
    .sidebar.active .dropdown-toggle::after {
        display: block;
    }

    .sidebar.active ul li a {
        padding: 12px 20px;
        justify-content: flex-start;
    }

    .sidebar.active ul li a i {
        margin-right: 10px;
    }

    .overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.7);
        z-index: 998;
        opacity: 0;
        transition: all 0.3s;
    }

    .overlay.active {
        display: block;
        opacity: 1;
    }
}
