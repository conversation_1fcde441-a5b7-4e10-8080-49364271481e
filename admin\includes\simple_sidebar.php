<!-- Wrapper -->
<div class="d-flex">
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-brand-icon">
                <i class="fas fa-film text-danger"></i>
            </div>
            <div class="sidebar-brand-text">
                <span class="site-name"><span class="text-danger">MOVIE</span><span class="text-light">FLIX</span></span>
                <span class="admin-text">Admin Panel</span>
            </div>
        </div>

        <ul class="components list-unstyled">
            <!-- Dashboard -->
            <li>
                <a href="index.php" class="<?php echo $current_page == 'index.php' ? 'active' : ''; ?>">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="menu-text">Dashboard</span>
                </a>
            </li>

            <!-- Content Management -->
            <li>
                <a href="#contentSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle <?php echo in_array($current_page, ['movies.php', 'add_movie.php', 'edit_movie.php', 'tvshows.php', 'add_tvshow.php', 'edit_tvshow.php', 'manage_episodes.php', 'manage_episode_links.php', 'categories.php']) ? 'active' : ''; ?>">
                    <i class="fas fa-photo-video"></i>
                    <span class="menu-text">Content</span>
                </a>
                <ul class="collapse <?php echo in_array($current_page, ['movies.php', 'add_movie.php', 'edit_movie.php', 'tvshows.php', 'add_tvshow.php', 'edit_tvshow.php', 'manage_episodes.php', 'manage_episode_links.php', 'categories.php']) ? 'show' : ''; ?>" id="contentSubmenu">
                    <li>
                        <a href="movies.php" class="<?php echo in_array($current_page, ['movies.php', 'add_movie.php', 'edit_movie.php']) ? 'active' : ''; ?>">
                            <i class="fas fa-film"></i>
                            <span class="menu-text">Movies</span>
                        </a>
                    </li>
                    <li>
                        <a href="tvshows.php" class="<?php echo in_array($current_page, ['tvshows.php', 'add_tvshow.php', 'edit_tvshow.php', 'manage_episodes.php', 'manage_episode_links.php']) ? 'active' : ''; ?>">
                            <i class="fas fa-tv"></i>
                            <span class="menu-text">TV Shows</span>
                        </a>
                    </li>
                    <li>
                        <a href="categories.php" class="<?php echo $current_page == 'categories.php' ? 'active' : ''; ?>">
                            <i class="fas fa-tags"></i>
                            <span class="menu-text">Categories</span>
                        </a>
                    </li>
                    <li>
                        <a href="manage_links.php" class="<?php echo $current_page == 'manage_links.php' ? 'active' : ''; ?>">
                            <i class="fas fa-link"></i>
                            <span class="menu-text">Manage Links</span>
                        </a>
                    </li>
                </ul>
            </li>

            <!-- Import from TMDB -->
            <li>
                <a href="#importSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle <?php echo in_array($current_page, ['import_tmdb.php', 'import_tmdb_tvshow.php']) ? 'active' : ''; ?>">
                    <i class="fas fa-file-import"></i>
                    <span class="menu-text">Import</span>
                </a>
                <ul class="collapse <?php echo in_array($current_page, ['import_tmdb.php', 'import_tmdb_tvshow.php']) ? 'show' : ''; ?>" id="importSubmenu">
                    <li>
                        <a href="import_tmdb.php" class="<?php echo $current_page == 'import_tmdb.php' ? 'active' : ''; ?>">
                            <i class="fas fa-film"></i>
                            <span class="menu-text">Import Movies</span>
                        </a>
                    </li>
                    <li>
                        <a href="import_tmdb_tvshow.php" class="<?php echo $current_page == 'import_tmdb_tvshow.php' ? 'active' : ''; ?>">
                            <i class="fas fa-tv"></i>
                            <span class="menu-text">Import TV Shows</span>
                        </a>
                    </li>
                </ul>
            </li>

            <!-- User Management -->
            <li>
                <a href="#userSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle <?php echo in_array($current_page, ['users.php', 'reviews.php']) ? 'active' : ''; ?>">
                    <i class="fas fa-users"></i>
                    <span class="menu-text">Users</span>
                </a>
                <ul class="collapse <?php echo in_array($current_page, ['users.php', 'reviews.php']) ? 'show' : ''; ?>" id="userSubmenu">
                    <li>
                        <a href="users.php" class="<?php echo $current_page == 'users.php' ? 'active' : ''; ?>">
                            <i class="fas fa-user"></i>
                            <span class="menu-text">Manage Users</span>
                        </a>
                    </li>
                    <li>
                        <a href="reviews.php" class="<?php echo $current_page == 'reviews.php' ? 'active' : ''; ?>">
                            <i class="fas fa-star"></i>
                            <span class="menu-text">Reviews</span>
                        </a>
                    </li>
                </ul>
            </li>

            <!-- Premium Features -->
            <li>
                <a href="#premiumSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle <?php echo in_array($current_page, ['payments.php', 'premium_plans.php', 'manage_premium.php', 'payment_settings.php']) ? 'active' : ''; ?>">
                    <i class="fas fa-crown"></i>
                    <span class="menu-text">Premium</span>
                </a>
                <ul class="collapse <?php echo in_array($current_page, ['payments.php', 'premium_plans.php', 'manage_premium.php', 'payment_settings.php']) ? 'show' : ''; ?>" id="premiumSubmenu">
                    <li>
                        <a href="payments.php" class="<?php echo $current_page == 'payments.php' ? 'active' : ''; ?>">
                            <i class="fas fa-credit-card"></i>
                            <span class="menu-text">Payments</span>
                            <span class="badge bg-danger rounded-pill">5</span>
                        </a>
                    </li>
                    <li>
                        <a href="premium_plans.php" class="<?php echo $current_page == 'premium_plans.php' ? 'active' : ''; ?>">
                            <i class="fas fa-list-alt"></i>
                            <span class="menu-text">Premium Plans</span>
                        </a>
                    </li>
                    <li>
                        <a href="manage_premium.php" class="<?php echo $current_page == 'manage_premium.php' ? 'active' : ''; ?>">
                            <i class="fas fa-gem"></i>
                            <span class="menu-text">Manage Premium</span>
                        </a>
                    </li>
                    <li>
                        <a href="payment_settings.php" class="<?php echo $current_page == 'payment_settings.php' ? 'active' : ''; ?>">
                            <i class="fas fa-cog"></i>
                            <span class="menu-text">Payment Settings</span>
                        </a>
                    </li>
                </ul>
            </li>

            <!-- Communication -->
            <li>
                <a href="#communicationSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle <?php echo in_array($current_page, ['messages.php', 'live_chat.php']) ? 'active' : ''; ?>">
                    <i class="fas fa-comments"></i>
                    <span class="menu-text">Communication</span>
                    <span class="badge bg-danger rounded-pill">3</span>
                </a>
                <ul class="collapse <?php echo in_array($current_page, ['messages.php', 'live_chat.php']) ? 'show' : ''; ?>" id="communicationSubmenu">
                    <li>
                        <a href="messages.php" class="<?php echo $current_page == 'messages.php' ? 'active' : ''; ?>">
                            <i class="fas fa-envelope"></i>
                            <span class="menu-text">Messages</span>
                            <span class="badge bg-danger rounded-pill">2</span>
                        </a>
                    </li>
                    <li>
                        <a href="live_chat.php" class="<?php echo $current_page == 'live_chat.php' ? 'active' : ''; ?>">
                            <i class="fas fa-comment-dots"></i>
                            <span class="menu-text">Live Chat</span>
                            <span class="badge bg-danger rounded-pill">1</span>
                        </a>
                    </li>
                </ul>
            </li>

            <!-- System Management -->
            <li>
                <a href="#systemSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle <?php echo in_array($current_page, ['site_settings.php', 'backup.php', 'logs.php', 'activity_logs.php']) ? 'active' : ''; ?>">
                    <i class="fas fa-cogs"></i>
                    <span class="menu-text">System</span>
                </a>
                <ul class="collapse <?php echo in_array($current_page, ['site_settings.php', 'backup.php', 'logs.php', 'activity_logs.php']) ? 'show' : ''; ?>" id="systemSubmenu">
                    <li>
                        <a href="site_settings.php" class="<?php echo $current_page == 'site_settings.php' ? 'active' : ''; ?>">
                            <i class="fas fa-cog"></i>
                            <span class="menu-text">Site Settings</span>
                        </a>
                    </li>
                    <li>
                        <a href="backup.php" class="<?php echo $current_page == 'backup.php' ? 'active' : ''; ?>">
                            <i class="fas fa-database"></i>
                            <span class="menu-text">Backup & Restore</span>
                        </a>
                    </li>
                    <li>
                        <a href="logs.php" class="<?php echo $current_page == 'logs.php' ? 'active' : ''; ?>">
                            <i class="fas fa-list"></i>
                            <span class="menu-text">System Logs</span>
                        </a>
                    </li>
                    <li>
                        <a href="activity_logs.php" class="<?php echo $current_page == 'activity_logs.php' ? 'active' : ''; ?>">
                            <i class="fas fa-history"></i>
                            <span class="menu-text">Activity Logs</span>
                        </a>
                    </li>
                </ul>
            </li>

            <!-- Utilities -->
            <li>
                <a href="#utilsSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle <?php echo in_array($current_page, ['analytics.php', 'debug_images.php', 'fix_images.php', 'update_database.php']) ? 'active' : ''; ?>">
                    <i class="fas fa-tools"></i>
                    <span class="menu-text">Utilities</span>
                </a>
                <ul class="collapse <?php echo in_array($current_page, ['analytics.php', 'debug_images.php', 'fix_images.php', 'update_database.php']) ? 'show' : ''; ?>" id="utilsSubmenu">
                    <li>
                        <a href="analytics.php" class="<?php echo $current_page == 'analytics.php' ? 'active' : ''; ?>">
                            <i class="fas fa-chart-line"></i>
                            <span class="menu-text">Analytics</span>
                        </a>
                    </li>
                    <li>
                        <a href="debug_images.php" class="<?php echo $current_page == 'debug_images.php' ? 'active' : ''; ?>">
                            <i class="fas fa-bug"></i>
                            <span class="menu-text">Debug Images</span>
                        </a>
                    </li>
                    <li>
                        <a href="fix_images.php" class="<?php echo $current_page == 'fix_images.php' ? 'active' : ''; ?>">
                            <i class="fas fa-wrench"></i>
                            <span class="menu-text">Fix Images</span>
                        </a>
                    </li>
                    <li>
                        <a href="update_database.php" class="<?php echo $current_page == 'update_database.php' ? 'active' : ''; ?>">
                            <i class="fas fa-sync"></i>
                            <span class="menu-text">Update Database</span>
                        </a>
                    </li>
                </ul>
            </li>

            <!-- View Site -->
            <li>
                <a href="<?php echo SITE_URL; ?>" target="_blank">
                    <i class="fas fa-external-link-alt"></i>
                    <span class="menu-text">View Site</span>
                </a>
            </li>

            <!-- Logout -->
            <li>
                <a href="<?php echo SITE_URL; ?>/logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    <span class="menu-text">Logout</span>
                </a>
            </li>
        </ul>
    </nav>
