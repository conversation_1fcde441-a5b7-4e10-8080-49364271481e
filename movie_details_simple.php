<?php
// Simple movie details page without complex SEO
require_once 'includes/config.php';

// Check if id is provided
if (!isset($_GET['id'])) {
    header('Location: ' . SITE_URL);
    exit;
}

$id = (int)$_GET['id'];

// Get movie details
$query = "SELECT m.*, c.name as category_name FROM movies m
          LEFT JOIN categories c ON m.category_id = c.id
          WHERE m.id = $id";
$result = mysqli_query($conn, $query);

// Check if movie exists
if (mysqli_num_rows($result) == 0) {
    header('Location: ' . SITE_URL);
    exit;
}

$movie = mysqli_fetch_assoc($result);

// Simple SEO Meta Tags
$page_title = $movie['title'] . " (" . $movie['release_year'] . ") - Download Full Movie | " . SITE_NAME;
$page_description = "Download " . $movie['title'] . " (" . $movie['release_year'] . ") full movie in HD quality. " . substr($movie['description'], 0, 120) . "...";
$page_keywords = $movie['title'] . ", " . $movie['title'] . " download, " . $movie['title'] . " full movie, " . $movie['release_year'] . ", HD movie";
$page_image = SITE_URL . "/uploads/" . $movie['poster'];
$canonical_url = SITE_URL . "/movie_details.php?id=" . $movie['id'];

// Include header
require_once 'includes/header.php';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    <meta name="keywords" content="<?php echo $page_keywords; ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $page_title; ?>">
    <meta property="og:description" content="<?php echo $page_description; ?>">
    <meta property="og:image" content="<?php echo $page_image; ?>">
    <meta property="og:url" content="<?php echo $canonical_url; ?>">
    <meta property="og:type" content="video.movie">
    
    <!-- JSON-LD Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Movie",
      "name": "<?php echo addslashes($movie['title']); ?>",
      "description": "<?php echo addslashes(substr($movie['description'], 0, 200)); ?>",
      "image": "<?php echo $page_image; ?>",
      "datePublished": "<?php echo $movie['release_year']; ?>-01-01",
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "<?php echo $movie['rating']; ?>",
        "bestRating": "10",
        "worstRating": "1"
      },
      "url": "<?php echo $canonical_url; ?>"
    }
    </script>
</head>
<body>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-4">
            <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $movie['poster']; ?>" 
                 alt="<?php echo $movie['title']; ?>" 
                 class="img-fluid rounded">
        </div>
        <div class="col-md-8">
            <h1><?php echo $movie['title']; ?> (<?php echo $movie['release_year']; ?>)</h1>
            <p><strong>Category:</strong> <?php echo $movie['category_name']; ?></p>
            <p><strong>Rating:</strong> <?php echo $movie['rating']; ?>/10</p>
            <p><strong>Duration:</strong> <?php echo $movie['duration']; ?></p>
            <p><strong>Language:</strong> <?php echo $movie['language']; ?></p>
            
            <h3>Description</h3>
            <p><?php echo $movie['description']; ?></p>
            
            <?php if ($movie['premium_only'] && !$is_premium): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-crown"></i> This movie is available for premium members only.
                    <a href="<?php echo SITE_URL; ?>/premium.php" class="btn btn-warning btn-sm">Get Premium</a>
                </div>
            <?php else: ?>
                <div class="mt-3">
                    <h4>Download Links</h4>
                    <?php
                    // Get download links
                    $download_query = "SELECT * FROM download_links 
                                      WHERE content_type = 'movie' AND content_id = $id 
                                      ORDER BY quality DESC";
                    $download_result = mysqli_query($conn, $download_query);
                    
                    if (mysqli_num_rows($download_result) > 0):
                        while ($link = mysqli_fetch_assoc($download_result)):
                    ?>
                        <div class="mb-2">
                            <a href="<?php echo $link['download_url']; ?>" 
                               class="btn btn-success" 
                               target="_blank">
                                <i class="fas fa-download"></i> 
                                Download <?php echo $link['quality']; ?> 
                                (<?php echo $link['file_size']; ?>)
                            </a>
                        </div>
                    <?php 
                        endwhile;
                    else:
                    ?>
                        <p>No download links available yet.</p>
                    <?php endif; ?>
                </div>
                
                <div class="mt-3">
                    <h4>Watch Online</h4>
                    <?php
                    // Get streaming links
                    $stream_query = "SELECT * FROM streaming_links 
                                    WHERE content_type = 'movie' AND content_id = $id 
                                    ORDER BY quality DESC";
                    $stream_result = mysqli_query($conn, $stream_query);
                    
                    if (mysqli_num_rows($stream_result) > 0):
                        while ($stream = mysqli_fetch_assoc($stream_result)):
                    ?>
                        <div class="mb-2">
                            <a href="<?php echo $stream['stream_url']; ?>" 
                               class="btn btn-primary" 
                               target="_blank">
                                <i class="fas fa-play"></i> 
                                Watch <?php echo $stream['quality']; ?> 
                                - <?php echo $stream['server_name']; ?>
                            </a>
                        </div>
                    <?php 
                        endwhile;
                    else:
                    ?>
                        <p>No streaming links available yet.</p>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Similar Movies -->
    <div class="row mt-5">
        <div class="col-12">
            <h3>Similar Movies</h3>
            <div class="row">
                <?php
                // Get similar movies
                $similar_query = "SELECT * FROM movies 
                                 WHERE category_id = {$movie['category_id']} AND id != $id 
                                 ORDER BY RAND() LIMIT 4";
                $similar_result = mysqli_query($conn, $similar_query);
                
                while ($similar = mysqli_fetch_assoc($similar_result)):
                ?>
                    <div class="col-md-3 mb-3">
                        <div class="card">
                            <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $similar['poster']; ?>" 
                                 class="card-img-top" 
                                 alt="<?php echo $similar['title']; ?>">
                            <div class="card-body">
                                <h6 class="card-title"><?php echo $similar['title']; ?></h6>
                                <p class="card-text small"><?php echo $similar['release_year']; ?></p>
                                <a href="movie_details.php?id=<?php echo $similar['id']; ?>" 
                                   class="btn btn-sm btn-primary">View Details</a>
                            </div>
                        </div>
                    </div>
                <?php endwhile; ?>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>

</body>
</html>
