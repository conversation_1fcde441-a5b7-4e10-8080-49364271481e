<?php
// Start output buffering to prevent 'headers already sent' errors
ob_start();

// Video Proxy Script
// This script acts as a proxy between the client and the video source

// Set unlimited execution time for large files
set_time_limit(0);

// Increase memory limit
ini_set('memory_limit', '512M');

// Get video URL from query parameter
if (!isset($_GET['url']) || empty($_GET['url'])) {
    header('HTTP/1.1 400 Bad Request');
    exit('URL parameter is required');
}

$url = urldecode($_GET['url']);

// Validate URL
if (!filter_var($url, FILTER_VALIDATE_URL)) {
    header('HTTP/1.1 400 Bad Request');
    exit('Invalid URL');
}

// Get file information
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_NOBODY, true);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
$headers = curl_exec($ch);
$fileSize = curl_getinfo($ch, CURLINFO_CONTENT_LENGTH_DOWNLOAD);
$contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
curl_close($ch);

// Default content type if not detected
if (empty($contentType)) {
    // Check file extension to determine video type
    $file_extension = strtolower(pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION));

    // Set appropriate MIME type based on file extension
    if ($file_extension === 'mkv') {
        $contentType = 'video/x-matroska';
    } elseif ($file_extension === 'webm') {
        $contentType = 'video/webm';
    } elseif ($file_extension === 'ogg' || $file_extension === 'ogv') {
        $contentType = 'video/ogg';
    } elseif ($file_extension === 'mov') {
        $contentType = 'video/quicktime';
    } elseif ($file_extension === 'avi') {
        $contentType = 'video/x-msvideo';
    } elseif ($file_extension === 'flv') {
        $contentType = 'video/x-flv';
    } elseif ($file_extension === '3gp') {
        $contentType = 'video/3gpp';
    } else {
        $contentType = 'video/mp4'; // Default to MP4
    }
}

// Check if range is requested (for seeking)
$range = isset($_SERVER['HTTP_RANGE']) ? $_SERVER['HTTP_RANGE'] : null;

// Set headers for streaming
header('Content-Type: ' . $contentType);
header('Accept-Ranges: bytes');
header('Cache-Control: max-age=86400');
header('Access-Control-Allow-Origin: *');

// Handle range requests
if ($range !== null) {
    // Parse range header
    list($unit, $range) = explode('=', $range, 2);
    if ($unit == 'bytes') {
        // Multiple ranges could be specified at the same time, but we only support a single range
        if (strpos($range, ',') !== false) {
            header('HTTP/1.1 416 Requested Range Not Satisfiable');
            header("Content-Range: bytes */$fileSize");
            exit;
        }

        // If the range starts with a minus sign (e.g. -500), it means the last 500 bytes
        if (strpos($range, '-') === 0) {
            $end = $fileSize - 1;
            $start = $end + (int)$range + 1;
        } else {
            list($start, $end) = explode('-', $range, 2);
            $start = (int)$start;
            $end = (empty($end)) ? ($fileSize - 1) : (int)$end;
        }

        // Check if the requested range is valid
        if ($start > $end || $start >= $fileSize || $end >= $fileSize) {
            header('HTTP/1.1 416 Requested Range Not Satisfiable');
            header("Content-Range: bytes */$fileSize");
            exit;
        }

        // Set headers for partial content
        header('HTTP/1.1 206 Partial Content');
        header("Content-Range: bytes $start-$end/$fileSize");
        header('Content-Length: ' . ($end - $start + 1));
    } else {
        header('HTTP/1.1 416 Requested Range Not Satisfiable');
        header("Content-Range: bytes */$fileSize");
        exit;
    }
} else {
    // Full content
    header('Content-Length: ' . $fileSize);
}

// Stream the file
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, false);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($curl, $data) {
    echo $data;
    return strlen($data);
});

// Set range if requested
if ($range !== null) {
    curl_setopt($ch, CURLOPT_RANGE, "$start-$end");
}

// Execute and close
curl_exec($ch);
curl_close($ch);
exit;
?>
