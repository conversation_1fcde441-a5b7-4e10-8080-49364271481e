import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:cinepix_app/models/download_link.dart';

class VideoService {
  // Initialize downloader
  static Future<void> initializeDownloader() async {
    // Downloader initialization disabled
  }

  // Request storage permission
  static Future<bool> requestStoragePermission() async {
    if (Platform.isAndroid) {
      // Get Android SDK version
      final deviceInfoPlugin = DeviceInfoPlugin();
      final androidInfo = await deviceInfoPlugin.androidInfo;
      final sdkVersion = androidInfo.version.sdkInt;

      if (sdkVersion >= 30) {
        // Android 11+
        // For Android 11+, we need MANAGE_EXTERNAL_STORAGE permission
        final status = await Permission.manageExternalStorage.status;

        if (status.isGranted) {
          return true;
        } else {
          // Request permission
          final result = await Permission.manageExternalStorage.request();
          return result.isGranted;
        }
      } else {
        // For Android 10 and below
        final status = await Permission.storage.request();
        return status.isGranted;
      }
    } else if (Platform.isIOS) {
      return true; // iOS doesn't need explicit permission for downloads
    }
    return false;
  }

  // Download video
  static Future<String?> downloadVideo(DownloadLink link, String title) async {
    final hasPermission = await requestStoragePermission();

    if (!hasPermission) {
      return 'Storage permission denied';
    }

    try {
      // Get download directory
      final directory = await getExternalStorageDirectory();
      final downloadPath =
          directory?.path ?? (await getApplicationDocumentsDirectory()).path;

      // Create CinePix directory if it doesn't exist
      final cinepixDir = Directory('$downloadPath/CinePix');
      if (!await cinepixDir.exists()) {
        await cinepixDir.create();
      }

      // Sanitize filename for future use
      // final sanitizedTitle =
      //     title.replaceAll(RegExp(r'[^\w\s]+'), '').replaceAll(' ', '_');
      // final filename = '${sanitizedTitle}_${link.quality}.mp4';

      // Download functionality disabled for now
      return 'Download functionality will be available in the next update';
    } catch (e) {
      return 'Download error: $e';
    }
  }

  // Get download tasks
  static Future<List<dynamic>> getDownloadTasks() async {
    return [];
  }

  // Cancel download
  static Future<bool> cancelDownload(String taskId) async {
    return false;
  }

  // Pause download
  static Future<bool> pauseDownload(String taskId) async {
    return false;
  }

  // Resume download
  static Future<String?> resumeDownload(String taskId) async {
    return null;
  }

  // Retry download
  static Future<String?> retryDownload(String taskId) async {
    return null;
  }

  // Remove download
  static Future<bool> removeDownload(String taskId) async {
    return false;
  }

  // Get video mime type
  static String getMimeType(String url) {
    if (url.contains('.mp4')) {
      return 'video/mp4';
    } else if (url.contains('.mkv')) {
      return 'video/x-matroska';
    } else if (url.contains('.webm')) {
      return 'video/webm';
    } else if (url.contains('.m3u8')) {
      return 'application/x-mpegURL';
    } else {
      return 'video/mp4'; // Default
    }
  }

  // Check if URL is HLS stream
  static bool isHlsStream(String url) {
    return url.contains('.m3u8');
  }

  // Format duration
  static String formatDuration(int seconds) {
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final remainingSeconds = seconds % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m ${remainingSeconds}s';
    }
  }

  // Open download link in external browser
  static Future<bool> openDownloadLinkInBrowser(DownloadLink link) async {
    final Uri url = Uri.parse(link.url);
    try {
      return await launchUrl(url, mode: LaunchMode.externalApplication);
    } catch (e) {
      debugPrint('Error launching URL: $e');
      return false;
    }
  }
}
