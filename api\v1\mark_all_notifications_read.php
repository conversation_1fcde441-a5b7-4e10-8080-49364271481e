<?php
// Mark All Notifications as Read Endpoint
require_once __DIR__ . '/../config.php';

// <PERSON>le preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed. Use POST.',
        'data' => null
    ]);
    exit;
}

// Get request body
$request_body = file_get_contents('php://input');
$data = json_decode($request_body, true);

// Extract user ID from request
$user_id = $data['user_id'] ?? null;

// Validate user ID
if ($user_id === null) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Missing required field: user_id',
        'data' => null
    ]);
    exit;
}

// Check if notifications table exists
$check_table = "SHOW TABLES LIKE 'notifications'";
$result = mysqli_query($conn, $check_table);

if (mysqli_num_rows($result) == 0) {
    http_response_code(404);
    echo json_encode([
        'success' => false,
        'message' => 'Notifications table does not exist',
        'data' => null
    ]);
    exit;
}

// Update all notifications for the user to mark as read
// Include both user-specific notifications and broadcast notifications (user_id IS NULL)
$query = "UPDATE notifications SET is_read = TRUE WHERE user_id = ? OR user_id IS NULL";
$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, 'i', $user_id);

if (mysqli_stmt_execute($stmt)) {
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'message' => 'All notifications marked as read',
        'data' => [
            'affected_rows' => mysqli_stmt_affected_rows($stmt)
        ]
    ]);
} else {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to mark notifications as read: ' . mysqli_error($conn),
        'data' => null
    ]);
}

mysqli_stmt_close($stmt);
mysqli_close($conn);
