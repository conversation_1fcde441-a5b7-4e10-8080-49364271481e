/**
 * Ad Enhancements JavaScript
 * Enhances ad placeholder functionality and user experience
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize ad enhancements
    initAdEnhancements();
    
    // Add loading animations
    addLoadingAnimations();
    
    // Setup ad rotation
    setupAdRotation();
    
    // Add click tracking
    addClickTracking();
    
    // Setup responsive ad handling
    setupResponsiveAds();
});

/**
 * Initialize ad enhancements
 */
function initAdEnhancements() {
    const adContainers = document.querySelectorAll('.ad-container');
    
    adContainers.forEach((container, index) => {
        // Add unique ID if not present
        if (!container.id) {
            container.id = `ad-container-${index + 1}`;
        }
        
        // Add hover effects
        addHoverEffects(container);
        
        // Add loading state
        addLoadingState(container);
        
        // Add click handler
        addClickHandler(container);
        
        // Add visibility tracking
        addVisibilityTracking(container);
    });
}

/**
 * Add hover effects to ad containers
 */
function addHoverEffects(container) {
    const placeholder = container.querySelector('.ad-placeholder');
    
    container.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-2px)';
        this.style.boxShadow = '0 8px 25px rgba(229, 9, 20, 0.2)';
        
        if (placeholder) {
            const icon = placeholder.querySelector('i');
            if (icon) {
                icon.style.transform = 'scale(1.2)';
                icon.style.color = '#ffc107';
            }
        }
    });
    
    container.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = '';
        
        if (placeholder) {
            const icon = placeholder.querySelector('i');
            if (icon) {
                icon.style.transform = 'scale(1)';
                icon.style.color = '#e50914';
            }
        }
    });
}

/**
 * Add loading state to ad containers
 */
function addLoadingState(container) {
    // Simulate loading delay
    const loadingDelay = Math.random() * 2000 + 1000; // 1-3 seconds
    
    container.classList.add('ad-loading');
    
    setTimeout(() => {
        container.classList.remove('ad-loading');
        container.classList.add('ad-loaded');
        
        // Add loaded animation
        container.style.animation = 'fadeInUp 0.5s ease-out';
    }, loadingDelay);
}

/**
 * Add click handler to ad containers
 */
function addClickHandler(container) {
    container.addEventListener('click', function() {
        // Track click
        trackAdClick(this.id);
        
        // Add click animation
        this.style.transform = 'scale(0.98)';
        setTimeout(() => {
            this.style.transform = '';
        }, 150);
        
        // Show info message
        showAdInfo();
    });
}

/**
 * Add visibility tracking
 */
function addVisibilityTracking(container) {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Track ad impression
                trackAdImpression(entry.target.id);
                
                // Add visible class
                entry.target.classList.add('ad-visible');
            }
        });
    }, {
        threshold: 0.5 // 50% visible
    });
    
    observer.observe(container);
}

/**
 * Add loading animations
 */
function addLoadingAnimations() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ad-loading::before {
            animation: shimmer 2s infinite;
        }
        
        .ad-loaded {
            opacity: 1;
        }
        
        .ad-visible {
            border-color: rgba(229, 9, 20, 0.5);
        }
    `;
    document.head.appendChild(style);
}

/**
 * Setup ad rotation
 */
function setupAdRotation() {
    const rotatableAds = document.querySelectorAll('.ad-container[data-rotate="true"]');
    
    rotatableAds.forEach(container => {
        const messages = [
            'Advertisement Space Available',
            'Your Ad Could Be Here',
            'Promote Your Business',
            'Reach Your Audience',
            'Advertise With Us'
        ];
        
        let currentIndex = 0;
        const placeholder = container.querySelector('.ad-placeholder span');
        
        if (placeholder) {
            setInterval(() => {
                placeholder.style.opacity = '0';
                setTimeout(() => {
                    currentIndex = (currentIndex + 1) % messages.length;
                    placeholder.textContent = messages[currentIndex];
                    placeholder.style.opacity = '1';
                }, 300);
            }, 5000); // Rotate every 5 seconds
        }
    });
}

/**
 * Add click tracking
 */
function addClickTracking() {
    // This would integrate with your analytics system
    window.trackAdClick = function(adId) {
        console.log('Ad clicked:', adId);
        
        // Example: Send to analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', 'ad_click', {
                'ad_id': adId,
                'event_category': 'advertisement',
                'event_label': 'placeholder_click'
            });
        }
    };
    
    window.trackAdImpression = function(adId) {
        console.log('Ad impression:', adId);
        
        // Example: Send to analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', 'ad_impression', {
                'ad_id': adId,
                'event_category': 'advertisement',
                'event_label': 'placeholder_view'
            });
        }
    };
}

/**
 * Setup responsive ad handling
 */
function setupResponsiveAds() {
    function updateAdSizes() {
        const adContainers = document.querySelectorAll('.ad-container');
        const isMobile = window.innerWidth <= 768;
        
        adContainers.forEach(container => {
            const placeholder = container.querySelector('.ad-placeholder span');
            if (placeholder) {
                const originalText = placeholder.textContent;
                if (isMobile && originalText.includes('728x90')) {
                    placeholder.textContent = originalText.replace('728x90', '320x50');
                } else if (!isMobile && originalText.includes('320x50')) {
                    placeholder.textContent = originalText.replace('320x50', '728x90');
                }
            }
        });
    }
    
    // Update on load
    updateAdSizes();
    
    // Update on resize
    window.addEventListener('resize', debounce(updateAdSizes, 250));
}

/**
 * Show ad info message
 */
function showAdInfo() {
    // Create or show existing modal
    let modal = document.getElementById('ad-info-modal');
    
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'ad-info-modal';
        modal.innerHTML = `
            <div class="ad-info-overlay">
                <div class="ad-info-content">
                    <h3>বিজ্ঞাপন স্থান</h3>
                    <p>এই স্থানে আপনার বিজ্ঞাপন দেখানো হবে। আমাদের সাথে যোগাযোগ করুন বিজ্ঞাপনের জন্য।</p>
                    <div class="ad-info-buttons">
                        <button onclick="closeAdInfo()" class="btn btn-secondary">বন্ধ করুন</button>
                        <a href="mailto:<EMAIL>" class="btn btn-primary">যোগাযোগ করুন</a>
                    </div>
                </div>
            </div>
        `;
        
        // Add styles
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        document.body.appendChild(modal);
    }
    
    modal.style.display = 'flex';
    setTimeout(() => modal.style.opacity = '1', 10);
}

/**
 * Close ad info modal
 */
window.closeAdInfo = function() {
    const modal = document.getElementById('ad-info-modal');
    if (modal) {
        modal.style.opacity = '0';
        setTimeout(() => modal.style.display = 'none', 300);
    }
};

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
