<?php
require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Check if episode ID is provided
if (!isset($_GET['episode']) || empty($_GET['episode'])) {
    redirect('tvshows.php');
}

$episode_id = (int)$_GET['episode'];

// Get episode details
$episode_query = "SELECT e.*, t.title as tvshow_title, t.poster as tvshow_poster
                 FROM episodes e
                 JOIN tvshows t ON e.tvshow_id = t.id
                 WHERE e.id = $episode_id";
$episode_result = mysqli_query($conn, $episode_query);

if (mysqli_num_rows($episode_result) == 0) {
    redirect('tvshows.php');
}

$episode = mysqli_fetch_assoc($episode_result);

// Delete link
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $link_id = (int)$_GET['delete'];

    $delete_query = "DELETE FROM episode_links WHERE id = $link_id AND episode_id = $episode_id";

    if (mysqli_query($conn, $delete_query)) {
        $success_message = 'Link deleted successfully.';
    } else {
        $error_message = 'Error deleting link: ' . mysqli_error($conn);
    }
}

// Add/Edit Download Link
if (isset($_POST['add_download_link'])) {
    $quality = sanitize($_POST['quality']);
    $link_url = sanitize($_POST['link_url']);
    $server_name = isset($_POST['server_name']) ? sanitize($_POST['server_name']) : NULL;
    $file_size = isset($_POST['file_size']) ? sanitize($_POST['file_size']) : NULL;
    $subtitle_url_bn = isset($_POST['subtitle_url_bn']) ? sanitize($_POST['subtitle_url_bn']) : NULL;
    $subtitle_url_en = isset($_POST['subtitle_url_en']) ? sanitize($_POST['subtitle_url_en']) : NULL;
    $is_premium = isset($_POST['is_premium']) ? 1 : 0;
    $link_id = isset($_POST['link_id']) ? (int)$_POST['link_id'] : 0;

    if (empty($quality) || empty($link_url)) {
        $error_message = 'Please fill in all required fields.';
    } else {
        if ($link_id > 0) {
            // Update existing link
            $update_query = "UPDATE episode_links SET
                            quality = '$quality',
                            server_name = " . ($server_name ? "'$server_name'" : "NULL") . ",
                            file_size = " . ($file_size ? "'$file_size'" : "NULL") . ",
                            link_url = '$link_url',
                            subtitle_url_bn = " . ($subtitle_url_bn ? "'$subtitle_url_bn'" : "NULL") . ",
                            subtitle_url_en = " . ($subtitle_url_en ? "'$subtitle_url_en'" : "NULL") . ",
                            is_premium = $is_premium
                            WHERE id = $link_id AND episode_id = $episode_id AND link_type = 'download'";

            if (mysqli_query($conn, $update_query)) {
                $success_message = 'Download link updated successfully.';
            } else {
                $error_message = 'Error updating download link: ' . mysqli_error($conn);
            }
        } else {
            // Check if file_size column exists
            $check_column_query = "SHOW COLUMNS FROM episode_links LIKE 'file_size'";
            $column_result = mysqli_query($conn, $check_column_query);

            if (mysqli_num_rows($column_result) > 0) {
                // Column exists, include it in the query
                $insert_query = "INSERT INTO episode_links (episode_id, link_type, quality, server_name, file_size, link_url, subtitle_url_bn, subtitle_url_en, is_premium)
                                VALUES ($episode_id, 'download', '$quality', " . ($server_name ? "'$server_name'" : "NULL") . ", " . ($file_size ? "'$file_size'" : "NULL") . ", '$link_url', " . ($subtitle_url_bn ? "'$subtitle_url_bn'" : "NULL") . ", " . ($subtitle_url_en ? "'$subtitle_url_en'" : "NULL") . ", $is_premium)";
            } else {
                // Column doesn't exist, exclude it from the query
                $insert_query = "INSERT INTO episode_links (episode_id, link_type, quality, server_name, link_url, subtitle_url_bn, subtitle_url_en, is_premium)
                                VALUES ($episode_id, 'download', '$quality', " . ($server_name ? "'$server_name'" : "NULL") . ", '$link_url', " . ($subtitle_url_bn ? "'$subtitle_url_bn'" : "NULL") . ", " . ($subtitle_url_en ? "'$subtitle_url_en'" : "NULL") . ", $is_premium)";
            }

            if (mysqli_query($conn, $insert_query)) {
                $success_message = 'Download link added successfully.';

                // Add file_size column if it doesn't exist
                if (mysqli_num_rows($column_result) == 0) {
                    $add_column_query = "ALTER TABLE episode_links ADD COLUMN file_size VARCHAR(50) NULL AFTER server_name";
                    mysqli_query($conn, $add_column_query);
                }
            } else {
                $error_message = 'Error adding download link: ' . mysqli_error($conn);
            }
        }
    }
}

// Add/Edit Streaming Link
if (isset($_POST['add_streaming_link'])) {
    $quality = sanitize($_POST['quality']);
    $server_name = isset($_POST['server_name']) ? sanitize($_POST['server_name']) : NULL;
    $stream_url = sanitize($_POST['stream_url']);
    $subtitle_url_bn = isset($_POST['subtitle_url_bn']) ? sanitize($_POST['subtitle_url_bn']) : NULL;
    $subtitle_url_en = isset($_POST['subtitle_url_en']) ? sanitize($_POST['subtitle_url_en']) : NULL;
    $is_premium = isset($_POST['is_premium']) ? 1 : 0;
    $link_id = isset($_POST['link_id']) ? (int)$_POST['link_id'] : 0;

    if (empty($quality) || empty($stream_url)) {
        $error_message = 'Please fill in all required fields.';
    } else {
        if ($link_id > 0) {
            // Update existing link
            $update_query = "UPDATE episode_links SET
                            quality = '$quality',
                            server_name = " . ($server_name ? "'$server_name'" : "NULL") . ",
                            link_url = '$stream_url',
                            subtitle_url_bn = " . ($subtitle_url_bn ? "'$subtitle_url_bn'" : "NULL") . ",
                            subtitle_url_en = " . ($subtitle_url_en ? "'$subtitle_url_en'" : "NULL") . ",
                            is_premium = $is_premium
                            WHERE id = $link_id AND episode_id = $episode_id AND link_type = 'stream'";

            if (mysqli_query($conn, $update_query)) {
                $success_message = 'Streaming link updated successfully.';
            } else {
                $error_message = 'Error updating streaming link: ' . mysqli_error($conn);
            }
        } else {
            // Add new link
            $insert_query = "INSERT INTO episode_links (episode_id, link_type, quality, server_name, link_url, subtitle_url_bn, subtitle_url_en, is_premium)
                            VALUES ($episode_id, 'stream', '$quality', " . ($server_name ? "'$server_name'" : "NULL") . ", '$stream_url', " . ($subtitle_url_bn ? "'$subtitle_url_bn'" : "NULL") . ", " . ($subtitle_url_en ? "'$subtitle_url_en'" : "NULL") . ", $is_premium)";

            if (mysqli_query($conn, $insert_query)) {
                $success_message = 'Streaming link added successfully.';
            } else {
                $error_message = 'Error adding streaming link: ' . mysqli_error($conn);
            }
        }
    }
}

// Get existing links
$download_query = "SELECT * FROM episode_links WHERE episode_id = $episode_id AND link_type = 'download' ORDER BY quality DESC";
$download_result = mysqli_query($conn, $download_query);

$streaming_query = "SELECT * FROM episode_links WHERE episode_id = $episode_id AND link_type = 'stream' ORDER BY quality DESC";
$streaming_result = mysqli_query($conn, $streaming_query);

// Edit download link
$edit_download_link = null;
if (isset($_GET['edit_download']) && !empty($_GET['edit_download'])) {
    $link_id = (int)$_GET['edit_download'];

    $edit_query = "SELECT * FROM episode_links WHERE id = $link_id AND episode_id = $episode_id AND link_type = 'download'";
    $edit_result = mysqli_query($conn, $edit_query);

    if (mysqli_num_rows($edit_result) > 0) {
        $edit_download_link = mysqli_fetch_assoc($edit_result);
    }
}

// Edit streaming link
$edit_streaming_link = null;
if (isset($_GET['edit_streaming']) && !empty($_GET['edit_streaming'])) {
    $link_id = (int)$_GET['edit_streaming'];

    $edit_query = "SELECT * FROM episode_links WHERE id = $link_id AND episode_id = $episode_id AND link_type = 'stream'";
    $edit_result = mysqli_query($conn, $edit_query);

    if (mysqli_num_rows($edit_result) > 0) {
        $edit_streaming_link = mysqli_fetch_assoc($edit_result);
    }
}
?>

<?php
// Set page title
$page_title = "Manage Episode Links";

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<style>
    .action-buttons .btn {
        min-width: 100px;
        margin-bottom: 5px;
    }

    .action-buttons-left {
        display: flex;
        justify-content: flex-start;
        gap: 5px;
    }

    @media (max-width: 768px) {
        .action-buttons, .action-buttons-left {
            display: flex;
            flex-direction: column;
        }

        .action-buttons .btn, .action-buttons-left .btn {
            margin-bottom: 5px;
        }
    }
</style>

<!-- Main Content -->
<div class="content">
    <div class="container-fluid p-4">
        <!-- Page Heading -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <h1 class="h3 mb-0 text-gray-800">এপিসোড লিংক ম্যানেজমেন্ট</h1>
            <a href="manage_episodes.php?tvshow=<?php echo $episode['tvshow_id']; ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>এপিসোড লিস্টে ফিরে যান
            </a>
        </div>

        <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="episode-info">
            <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $episode['thumbnail'] ? $episode['thumbnail'] : $episode['tvshow_poster']; ?>" alt="<?php echo $episode['title']; ?>" class="episode-poster">
            <div class="episode-details">
                <h4><?php echo $episode['tvshow_title']; ?></h4>
                <h5>সিজন <?php echo $episode['season_number']; ?>, এপিসোড <?php echo $episode['episode_number']; ?>: <?php echo $episode['title']; ?></h5>
                <p><i class="fas fa-clock me-2"></i><?php echo $episode['duration']; ?> মিনিট</p>
                <?php if($episode['is_premium']): ?>
                <span class="badge bg-danger">প্রিমিয়াম</span>
                <?php endif; ?>
            </div>
        </div>

        <div class="row">
            <!-- Download Links -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">ডাউনলোড লিংকসমূহ</h5>
                    </div>
                    <div class="card-body">
                        <!-- Add/Edit Download Link Form -->
                        <form method="POST" action="" class="mb-4">
                            <input type="hidden" name="link_id" value="<?php echo $edit_download_link ? $edit_download_link['id'] : ''; ?>">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="quality" class="form-label">কোয়ালিটি</label>
                                    <select class="form-select" id="quality" name="quality" required>
                                        <option value="">কোয়ালিটি নির্বাচন করুন</option>
                                        <option value="480p" <?php echo ($edit_download_link && $edit_download_link['quality'] == '480p') ? 'selected' : ''; ?>>480p</option>
                                        <option value="720p" <?php echo ($edit_download_link && $edit_download_link['quality'] == '720p') ? 'selected' : ''; ?>>720p</option>
                                        <option value="1080p" <?php echo ($edit_download_link && $edit_download_link['quality'] == '1080p') ? 'selected' : ''; ?>>1080p</option>
                                        <option value="4K" <?php echo ($edit_download_link && $edit_download_link['quality'] == '4K') ? 'selected' : ''; ?>>4K</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="server_name_dl" class="form-label">সার্ভার নাম</label>
                                    <input type="text" class="form-control" id="server_name_dl" name="server_name" value="<?php echo $edit_download_link ? $edit_download_link['server_name'] : ''; ?>" placeholder="যেমন: Google Drive, MEGA, ইত্যাদি">
                                </div>
                                <div class="col-md-4">
                                    <label for="file_size" class="form-label">ফাইল সাইজ</label>
                                    <input type="text" class="form-control" id="file_size" name="file_size" value="<?php echo $edit_download_link ? $edit_download_link['file_size'] : ''; ?>" placeholder="যেমন: 700MB, 1.5GB">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="link_url" class="form-label">ডাউনলোড URL</label>
                                <input type="url" class="form-control" id="link_url" name="link_url" value="<?php echo $edit_download_link ? $edit_download_link['link_url'] : ''; ?>" required>
                            </div>
                            <div class="mb-3">
                                <label for="subtitle_url_bn" class="form-label">বাংলা সাবটাইটেল URL (ঐচ্ছিক)</label>
                                <input type="url" class="form-control" id="subtitle_url_bn" name="subtitle_url_bn" value="<?php echo $edit_download_link ? $edit_download_link['subtitle_url_bn'] : ''; ?>">
                                <small class="form-text text-muted">SRT বা VTT ফরম্যাট</small>
                            </div>
                            <div class="mb-3">
                                <label for="subtitle_url_en" class="form-label">ইংরেজি সাবটাইটেল URL (ঐচ্ছিক)</label>
                                <input type="url" class="form-control" id="subtitle_url_en" name="subtitle_url_en" value="<?php echo $edit_download_link ? $edit_download_link['subtitle_url_en'] : ''; ?>">
                                <small class="form-text text-muted">SRT বা VTT ফরম্যাট</small>
                            </div>
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="is_premium" name="is_premium" <?php echo ($edit_download_link && $edit_download_link['is_premium']) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_premium">শুধুমাত্র প্রিমিয়াম</label>
                            </div>
                            <button type="submit" name="add_download_link" class="btn btn-primary">
                                <?php echo $edit_download_link ? 'ডাউনলোড লিংক আপডেট করুন' : 'ডাউনলোড লিংক যোগ করুন'; ?>
                            </button>
                            <?php if($edit_download_link): ?>
                            <a href="manage_episode_links.php?episode=<?php echo $episode_id; ?>" class="btn btn-secondary">বাতিল</a>
                            <?php endif; ?>
                        </form>
                        <!-- Download Links Table -->
                        <?php if(mysqli_num_rows($download_result) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th width="10%">কোয়ালিটি</th>
                                        <th width="15%">সার্ভার</th>
                                        <th width="10%">সাইজ</th>
                                        <th width="15%">সাবটাইটেল</th>
                                        <th width="20%">URL</th>
                                        <th width="10%">প্রিমিয়াম</th>
                                        <th width="20%">অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while($link = mysqli_fetch_assoc($download_result)): ?>
                                    <tr>
                                        <td><?php echo $link['quality']; ?></td>
                                        <td>
                                            <?php if(!empty($link['server_name'])): ?>
                                            <?php echo $link['server_name']; ?>
                                            <?php else: ?>
                                            <span class="text-muted">নির্দিষ্ট করা হয়নি</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if(!empty($link['file_size'])): ?>
                                            <?php echo $link['file_size']; ?>
                                            <?php else: ?>
                                            <span class="text-muted">নির্দিষ্ট করা হয়নি</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if(!empty($link['subtitle_url_bn'])): ?>
                                            <span class="badge bg-success">BN</span>
                                            <?php endif; ?>
                                            <?php if(!empty($link['subtitle_url_en'])): ?>
                                            <span class="badge bg-info">EN</span>
                                            <?php endif; ?>
                                            <?php if(empty($link['subtitle_url_bn']) && empty($link['subtitle_url_en'])): ?>
                                            <span class="badge bg-secondary">নেই</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="text-truncate">
                                                <a href="<?php echo $link['link_url']; ?>" target="_blank" title="<?php echo $link['link_url']; ?>" class="text-primary">
                                                    <i class="fas fa-external-link-alt me-1"></i> <?php echo substr($link['link_url'], 0, 20); ?>...
                                                </a>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if($link['is_premium']): ?>
                                            <span class="badge bg-danger">হ্যাঁ</span>
                                            <?php else: ?>
                                            <span class="badge bg-secondary">না</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-2 action-buttons justify-content-start">
                                                <a href="manage_episode_links.php?episode=<?php echo $episode_id; ?>&edit_download=<?php echo $link['id']; ?>" class="btn btn-primary">
                                                    <i class="fas fa-edit"></i> এডিট
                                                </a>
                                                <a href="manage_episode_links.php?episode=<?php echo $episode_id; ?>&delete=<?php echo $link['id']; ?>" class="btn btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে এই ডাউনলোড লিংকটি মুছে ফেলতে চান?')">
                                                    <i class="fas fa-trash"></i> ডিলিট
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="alert alert-info">
                            <p class="mb-0">এখনো কোনো ডাউনলোড লিংক যোগ করা হয়নি।</p>
                        </div>
                        <?php endif; ?>
                        </div>
                    </div>
                </div>

            <!-- Streaming Links -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">স্ট্রিমিং লিংকসমূহ</h5>
                    </div>
                    <div class="card-body">
                        <!-- Add/Edit Streaming Link Form -->
                        <form method="POST" action="" class="mb-4">
                            <input type="hidden" name="link_id" value="<?php echo $edit_streaming_link ? $edit_streaming_link['id'] : ''; ?>">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="quality" class="form-label">কোয়ালিটি</label>
                                    <select class="form-select" id="quality" name="quality" required>
                                        <option value="">কোয়ালিটি নির্বাচন করুন</option>
                                        <option value="480p" <?php echo ($edit_streaming_link && $edit_streaming_link['quality'] == '480p') ? 'selected' : ''; ?>>480p</option>
                                        <option value="720p" <?php echo ($edit_streaming_link && $edit_streaming_link['quality'] == '720p') ? 'selected' : ''; ?>>720p</option>
                                        <option value="1080p" <?php echo ($edit_streaming_link && $edit_streaming_link['quality'] == '1080p') ? 'selected' : ''; ?>>1080p</option>
                                        <option value="4K" <?php echo ($edit_streaming_link && $edit_streaming_link['quality'] == '4K') ? 'selected' : ''; ?>>4K</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="server_name_stream" class="form-label">সার্ভার নাম</label>
                                    <input type="text" class="form-control" id="server_name_stream" name="server_name" value="<?php echo $edit_streaming_link ? $edit_streaming_link['server_name'] : ''; ?>" placeholder="যেমন: VidCloud, Streamtape, ইত্যাদি">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="stream_url" class="form-label">স্ট্রিমিং URL</label>
                                <input type="url" class="form-control" id="stream_url" name="stream_url" value="<?php echo $edit_streaming_link ? $edit_streaming_link['link_url'] : ''; ?>" required>
                                <small class="text-muted">টিভি শোর জন্য, আপনি এই ফরম্যাট ব্যবহার করতে পারেন: https://vidzee.wtf/tv/{id}/{season}/{episode}</small>
                            </div>
                            <div class="mb-3">
                                <label for="subtitle_url_bn" class="form-label">বাংলা সাবটাইটেল URL (ঐচ্ছিক)</label>
                                <input type="url" class="form-control" id="subtitle_url_bn" name="subtitle_url_bn" value="<?php echo $edit_streaming_link ? $edit_streaming_link['subtitle_url_bn'] : ''; ?>">
                                <small class="form-text text-muted">SRT বা VTT ফরম্যাট</small>
                            </div>
                            <div class="mb-3">
                                <label for="subtitle_url_en" class="form-label">ইংরেজি সাবটাইটেল URL (ঐচ্ছিক)</label>
                                <input type="url" class="form-control" id="subtitle_url_en" name="subtitle_url_en" value="<?php echo $edit_streaming_link ? $edit_streaming_link['subtitle_url_en'] : ''; ?>">
                                <small class="form-text text-muted">SRT বা VTT ফরম্যাট</small>
                            </div>
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="stream_is_premium" name="is_premium" <?php echo ($edit_streaming_link && $edit_streaming_link['is_premium']) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="stream_is_premium">শুধুমাত্র প্রিমিয়াম</label>
                            </div>
                            <button type="submit" name="add_streaming_link" class="btn btn-success">
                                <?php echo $edit_streaming_link ? 'স্ট্রিমিং লিংক আপডেট করুন' : 'স্ট্রিমিং লিংক যোগ করুন'; ?>
                            </button>
                            <?php if($edit_streaming_link): ?>
                            <a href="manage_episode_links.php?episode=<?php echo $episode_id; ?>" class="btn btn-secondary">বাতিল</a>
                            <?php endif; ?>
                        </form>
                        <!-- Streaming Links Table -->
                        <?php if(mysqli_num_rows($streaming_result) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th width="10%">কোয়ালিটি</th>
                                        <th width="15%">সার্ভার</th>
                                        <th width="15%">সাবটাইটেল</th>
                                        <th width="20%">URL</th>
                                        <th width="10%">প্রিমিয়াম</th>
                                        <th width="30%">অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while($link = mysqli_fetch_assoc($streaming_result)): ?>
                                    <tr>
                                        <td><?php echo $link['quality']; ?></td>
                                        <td>
                                            <?php if(!empty($link['server_name'])): ?>
                                            <?php echo $link['server_name']; ?>
                                            <?php else: ?>
                                            <span class="text-muted">নির্দিষ্ট করা হয়নি</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if(!empty($link['subtitle_url_bn'])): ?>
                                            <span class="badge bg-success">BN</span>
                                            <?php endif; ?>
                                            <?php if(!empty($link['subtitle_url_en'])): ?>
                                            <span class="badge bg-info">EN</span>
                                            <?php endif; ?>
                                            <?php if(empty($link['subtitle_url_bn']) && empty($link['subtitle_url_en'])): ?>
                                            <span class="badge bg-secondary">নেই</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="text-truncate">
                                                <a href="<?php echo $link['link_url']; ?>" target="_blank" title="<?php echo $link['link_url']; ?>" class="text-primary">
                                                    <i class="fas fa-external-link-alt me-1"></i> <?php echo substr($link['link_url'], 0, 20); ?>...
                                                </a>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if($link['is_premium']): ?>
                                            <span class="badge bg-danger">হ্যাঁ</span>
                                            <?php else: ?>
                                            <span class="badge bg-secondary">না</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-2 action-buttons justify-content-start">
                                                <a href="manage_episode_links.php?episode=<?php echo $episode_id; ?>&edit_streaming=<?php echo $link['id']; ?>" class="btn btn-primary">
                                                    <i class="fas fa-edit"></i> এডিট
                                                </a>
                                                <a href="manage_episode_links.php?episode=<?php echo $episode_id; ?>&delete=<?php echo $link['id']; ?>" class="btn btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে এই স্ট্রিমিং লিংকটি মুছে ফেলতে চান?')">
                                                    <i class="fas fa-trash"></i> ডিলিট
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="alert alert-info">
                            <p class="mb-0">এখনো কোনো স্ট্রিমিং লিংক যোগ করা হয়নি।</p>
                        </div>
                        <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</div>

<?php require_once 'includes/footer.php'; ?>

<script>
// This script will run after the page loads
document.addEventListener('DOMContentLoaded', function() {
    // Add a small delay to ensure the DOM is fully loaded
    setTimeout(function() {
        // Get all action buttons
        const actionButtons = document.querySelectorAll('.action-buttons');

        // Add the left alignment class to all action buttons
        actionButtons.forEach(function(buttons) {
            buttons.classList.add('action-buttons-left');
        });

        // Move action buttons to the left side of the table
        const tables = document.querySelectorAll('.table-striped');

        tables.forEach(function(table) {
            const rows = table.querySelectorAll('tbody tr');

            rows.forEach(function(row) {
                // Get the last cell (action buttons)
                const lastCell = row.querySelector('td:last-child');

                if (lastCell && lastCell.querySelector('.action-buttons')) {
                    // Create a new cell
                    const newCell = document.createElement('td');
                    newCell.innerHTML = lastCell.innerHTML;

                    // Insert the new cell at the beginning
                    row.insertBefore(newCell, row.firstChild);

                    // Remove the last cell
                    row.removeChild(lastCell);
                }
            });

            // Update the header
            const headerRow = table.querySelector('thead tr');
            if (headerRow) {
                const lastHeader = headerRow.querySelector('th:last-child');

                if (lastHeader && lastHeader.textContent.trim() === 'অ্যাকশন') {
                    // Create a new header
                    const newHeader = document.createElement('th');
                    newHeader.textContent = 'অ্যাকশন';
                    newHeader.width = '30%';

                    // Insert the new header at the beginning
                    headerRow.insertBefore(newHeader, headerRow.firstChild);

                    // Remove the last header
                    headerRow.removeChild(lastHeader);
                }
            }
        });
    }, 500);
});
</script>
