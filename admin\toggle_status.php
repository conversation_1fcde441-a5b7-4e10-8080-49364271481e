<?php
require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Check if required parameters are provided
if (!isset($_POST['id']) || !isset($_POST['type']) || !isset($_POST['status'])) {
    echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
    exit;
}

$id = (int)$_POST['id'];
$type = $_POST['type']; // 'movie' or 'tvshow'
$status = (int)$_POST['status']; // 1 for active, 0 for inactive

// Validate type
if ($type !== 'movie' && $type !== 'tvshow') {
    echo json_encode(['success' => false, 'message' => 'Invalid content type']);
    exit;
}

// Validate id
if ($id <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid ID']);
    exit;
}

// Validate status
if ($status !== 0 && $status !== 1) {
    echo json_encode(['success' => false, 'message' => 'Invalid status']);
    exit;
}

// Determine table name based on type
$table = ($type === 'movie') ? 'movies' : 'tvshows';

// Update status
$query = "UPDATE $table SET is_active = $status WHERE id = $id";
$result = mysqli_query($conn, $query);

if ($result) {
    // Log the action
    $action = $status ? 'activated' : 'deactivated';
    $content_type = ($type === 'movie') ? 'Movie' : 'TV Show';
    
    // Get the title of the content
    $title_query = "SELECT title FROM $table WHERE id = $id";
    $title_result = mysqli_query($conn, $title_query);
    $title_row = mysqli_fetch_assoc($title_result);
    $title = $title_row['title'];
    
    // Insert into activity_logs if the table exists
    $check_table_query = "SHOW TABLES LIKE 'activity_logs'";
    $check_table_result = mysqli_query($conn, $check_table_query);
    
    if (mysqli_num_rows($check_table_result) > 0) {
        $user_id = $_SESSION['user_id'];
        $description = "$content_type '$title' (ID: $id) has been $action";
        $ip_address = $_SERVER['REMOTE_ADDR'];
        $user_agent = $_SERVER['HTTP_USER_AGENT'];
        
        $log_query = "INSERT INTO activity_logs (user_id, activity_type, description, ip_address, user_agent) 
                     VALUES ($user_id, 'status_change', '$description', '$ip_address', '$user_agent')";
        mysqli_query($conn, $log_query);
    }
    
    echo json_encode([
        'success' => true, 
        'message' => "$content_type has been " . ($status ? 'activated' : 'deactivated'),
        'newStatus' => $status
    ]);
} else {
    echo json_encode(['success' => false, 'message' => 'Error updating status: ' . mysqli_error($conn)]);
}
?>
