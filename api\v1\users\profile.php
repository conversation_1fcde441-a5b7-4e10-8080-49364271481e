<?php
// API User Profile Endpoints

// Get the request
global $request;

// Check if user is authenticated
if (!is_authenticated()) {
    api_error('Authentication required', 401);
}

// Get authenticated user
$current_user = get_authenticated_user();

// Handle different endpoints based on the path parts
$action = $request['parts'][0] ?? 'get';

switch ($action) {
    case 'get':
        handle_get_profile($current_user, $request);
        break;
    
    case 'update':
        handle_update_profile($current_user, $request);
        break;
    
    case 'change_password':
        handle_change_password($current_user, $request);
        break;
    
    case 'upload_avatar':
        handle_upload_avatar($current_user, $request);
        break;
    
    case 'favorites':
        handle_favorites($current_user, $request);
        break;
    
    case 'watchlist':
        handle_watchlist($current_user, $request);
        break;
    
    case 'subscription':
        handle_subscription($current_user, $request);
        break;
    
    default:
        api_error('Invalid profile endpoint', 404);
}

// Handle get user profile
function handle_get_profile($user, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get user profile
    $query = "SELECT u.id, u.username, u.email, u.name, u.role, u.profile_image, 
              u.is_premium, u.premium_expires, u.created_at, 
              COUNT(DISTINCT f.id) as favorites_count,
              COUNT(DISTINCT w.id) as watchlist_count
              FROM users u
              LEFT JOIN favorites f ON u.id = f.user_id
              LEFT JOIN watchlist w ON u.id = w.user_id
              WHERE u.id = ?
              GROUP BY u.id";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $user['user_id']);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($result) === 0) {
        api_error('User not found', 404);
    }
    
    $profile = mysqli_fetch_assoc($result);
    
    // Get active subscription if any
    $subscription_query = "SELECT s.*, p.name as plan_name, p.price as plan_price, p.duration as plan_duration 
                          FROM subscriptions s
                          JOIN premium_plans p ON s.plan_id = p.id
                          WHERE s.user_id = ? AND s.status = 'active'
                          ORDER BY s.created_at DESC
                          LIMIT 1";
    
    $subscription_stmt = mysqli_prepare($conn, $subscription_query);
    mysqli_stmt_bind_param($subscription_stmt, 'i', $user['user_id']);
    mysqli_stmt_execute($subscription_stmt);
    $subscription_result = mysqli_stmt_get_result($subscription_stmt);
    
    $subscription = null;
    if (mysqli_num_rows($subscription_result) > 0) {
        $sub = mysqli_fetch_assoc($subscription_result);
        $subscription = [
            'id' => (int)$sub['id'],
            'plan_id' => (int)$sub['plan_id'],
            'plan_name' => $sub['plan_name'],
            'plan_price' => (float)$sub['plan_price'],
            'plan_duration' => (int)$sub['plan_duration'],
            'start_date' => $sub['start_date'],
            'end_date' => $sub['end_date'],
            'status' => $sub['status'],
            'payment_method' => $sub['payment_method'],
            'created_at' => $sub['created_at']
        ];
    }
    
    // Return user profile
    api_response([
        'id' => (int)$profile['id'],
        'username' => $profile['username'],
        'email' => $profile['email'],
        'name' => $profile['name'],
        'role' => $profile['role'],
        'profile_image' => $profile['profile_image'],
        'is_premium' => (bool)$profile['is_premium'],
        'premium_expires' => $profile['premium_expires'],
        'favorites_count' => (int)$profile['favorites_count'],
        'watchlist_count' => (int)$profile['watchlist_count'],
        'created_at' => $profile['created_at'],
        'subscription' => $subscription
    ]);
}

// Handle update user profile
function handle_update_profile($user, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'POST') {
        api_error('Method not allowed', 405);
    }
    
    // Validate required fields
    if (empty($request['body']['name']) && empty($request['body']['email'])) {
        api_error('Name or email is required', 400);
    }
    
    // Get current user data
    $query = "SELECT * FROM users WHERE id = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $user['user_id']);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($result) === 0) {
        api_error('User not found', 404);
    }
    
    $current_data = mysqli_fetch_assoc($result);
    
    // Update fields
    $name = isset($request['body']['name']) ? $request['body']['name'] : $current_data['name'];
    $email = isset($request['body']['email']) ? $request['body']['email'] : $current_data['email'];
    
    // Check if email is already taken
    if ($email !== $current_data['email']) {
        $email_check_query = "SELECT id FROM users WHERE email = ? AND id != ?";
        $email_check_stmt = mysqli_prepare($conn, $email_check_query);
        mysqli_stmt_bind_param($email_check_stmt, 'si', $email, $user['user_id']);
        mysqli_stmt_execute($email_check_stmt);
        $email_check_result = mysqli_stmt_get_result($email_check_stmt);
        
        if (mysqli_num_rows($email_check_result) > 0) {
            api_error('Email is already taken', 400);
        }
    }
    
    // Update user profile
    $update_query = "UPDATE users SET name = ?, email = ? WHERE id = ?";
    $update_stmt = mysqli_prepare($conn, $update_query);
    mysqli_stmt_bind_param($update_stmt, 'ssi', $name, $email, $user['user_id']);
    
    if (!mysqli_stmt_execute($update_stmt)) {
        api_error('Failed to update profile: ' . mysqli_error($conn), 500);
    }
    
    // Return updated profile
    api_response([
        'id' => (int)$user['user_id'],
        'username' => $current_data['username'],
        'email' => $email,
        'name' => $name,
        'profile_image' => $current_data['profile_image']
    ], 200, 'Profile updated successfully');
}

// Handle change password
function handle_change_password($user, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'POST') {
        api_error('Method not allowed', 405);
    }
    
    // Validate required fields
    if (empty($request['body']['current_password']) || 
        empty($request['body']['new_password']) || 
        empty($request['body']['confirm_password'])) {
        api_error('Current password, new password, and confirm password are required', 400);
    }
    
    $current_password = $request['body']['current_password'];
    $new_password = $request['body']['new_password'];
    $confirm_password = $request['body']['confirm_password'];
    
    // Check if new password and confirm password match
    if ($new_password !== $confirm_password) {
        api_error('New password and confirm password do not match', 400);
    }
    
    // Check if new password is at least 6 characters
    if (strlen($new_password) < 6) {
        api_error('New password must be at least 6 characters', 400);
    }
    
    // Get current user data
    $query = "SELECT password FROM users WHERE id = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $user['user_id']);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($result) === 0) {
        api_error('User not found', 404);
    }
    
    $current_data = mysqli_fetch_assoc($result);
    
    // Verify current password
    if (!password_verify($current_password, $current_data['password'])) {
        api_error('Current password is incorrect', 400);
    }
    
    // Hash new password
    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
    
    // Update password
    $update_query = "UPDATE users SET password = ? WHERE id = ?";
    $update_stmt = mysqli_prepare($conn, $update_query);
    mysqli_stmt_bind_param($update_stmt, 'si', $hashed_password, $user['user_id']);
    
    if (!mysqli_stmt_execute($update_stmt)) {
        api_error('Failed to change password: ' . mysqli_error($conn), 500);
    }
    
    // Return success response
    api_response([], 200, 'Password changed successfully');
}

// Handle upload avatar
function handle_upload_avatar($user, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'POST') {
        api_error('Method not allowed', 405);
    }
    
    // Validate required fields
    if (empty($request['body']['profile_image'])) {
        api_error('Profile image URL is required', 400);
    }
    
    $profile_image = $request['body']['profile_image'];
    
    // Update profile image
    $update_query = "UPDATE users SET profile_image = ? WHERE id = ?";
    $update_stmt = mysqli_prepare($conn, $update_query);
    mysqli_stmt_bind_param($update_stmt, 'si', $profile_image, $user['user_id']);
    
    if (!mysqli_stmt_execute($update_stmt)) {
        api_error('Failed to upload avatar: ' . mysqli_error($conn), 500);
    }
    
    // Return success response
    api_response([
        'profile_image' => $profile_image
    ], 200, 'Avatar uploaded successfully');
}

// Handle favorites
function handle_favorites($user, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] === 'GET') {
        // Get favorites
        $page = isset($request['params']['page']) ? (int)$request['params']['page'] : 1;
        $limit = isset($request['params']['limit']) ? (int)$request['params']['limit'] : 20;
        $offset = ($page - 1) * $limit;
        
        $query = "SELECT f.*, 
                 CASE 
                    WHEN f.content_type = 'movie' THEN m.title 
                    WHEN f.content_type = 'tvshow' THEN t.title
                 END as title,
                 CASE 
                    WHEN f.content_type = 'movie' THEN m.poster 
                    WHEN f.content_type = 'tvshow' THEN t.poster
                 END as poster,
                 CASE 
                    WHEN f.content_type = 'movie' THEN m.release_year 
                    WHEN f.content_type = 'tvshow' THEN t.start_year
                 END as year,
                 CASE 
                    WHEN f.content_type = 'movie' THEN m.rating 
                    WHEN f.content_type = 'tvshow' THEN t.rating
                 END as rating,
                 CASE 
                    WHEN f.content_type = 'movie' THEN m.premium_only 
                    WHEN f.content_type = 'tvshow' THEN t.premium_only
                 END as premium_only
                 FROM favorites f
                 LEFT JOIN movies m ON f.content_type = 'movie' AND f.content_id = m.id
                 LEFT JOIN tvshows t ON f.content_type = 'tvshow' AND f.content_id = t.id
                 WHERE f.user_id = ?
                 ORDER BY f.created_at DESC
                 LIMIT ? OFFSET ?";
        
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, 'iii', $user['user_id'], $limit, $offset);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        
        $favorites = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $favorites[] = [
                'id' => (int)$row['id'],
                'content_type' => $row['content_type'],
                'content_id' => (int)$row['content_id'],
                'title' => $row['title'],
                'poster' => $row['poster'],
                'year' => (int)$row['year'],
                'rating' => (float)$row['rating'],
                'premium_only' => (bool)$row['premium_only'],
                'created_at' => $row['created_at']
            ];
        }
        
        // Get total count for pagination
        $count_query = "SELECT COUNT(*) as total FROM favorites WHERE user_id = ?";
        $count_stmt = mysqli_prepare($conn, $count_query);
        mysqli_stmt_bind_param($count_stmt, 'i', $user['user_id']);
        mysqli_stmt_execute($count_stmt);
        $count_result = mysqli_stmt_get_result($count_stmt);
        $total = mysqli_fetch_assoc($count_result)['total'];
        
        // Return favorites with pagination
        api_response([
            'favorites' => $favorites,
            'meta' => [
                'pagination' => [
                    'total' => (int)$total,
                    'per_page' => $limit,
                    'current_page' => $page,
                    'last_page' => ceil($total / $limit)
                ]
            ]
        ]);
    } elseif ($request['method'] === 'POST') {
        // Add or remove favorite
        
        // Validate required fields
        if (empty($request['body']['content_type']) || 
            empty($request['body']['content_id']) || 
            !isset($request['body']['action'])) {
            api_error('Content type, content ID, and action are required', 400);
        }
        
        $content_type = $request['body']['content_type'];
        $content_id = (int)$request['body']['content_id'];
        $action = $request['body']['action']; // 'add' or 'remove'
        
        // Validate content type
        if (!in_array($content_type, ['movie', 'tvshow'])) {
            api_error('Invalid content type. Must be movie or tvshow', 400);
        }
        
        // Validate action
        if (!in_array($action, ['add', 'remove'])) {
            api_error('Invalid action. Must be add or remove', 400);
        }
        
        // Check if content exists
        if ($content_type === 'movie') {
            $content_query = "SELECT id FROM movies WHERE id = ?";
        } else {
            $content_query = "SELECT id FROM tvshows WHERE id = ?";
        }
        
        $content_stmt = mysqli_prepare($conn, $content_query);
        mysqli_stmt_bind_param($content_stmt, 'i', $content_id);
        mysqli_stmt_execute($content_stmt);
        $content_result = mysqli_stmt_get_result($content_stmt);
        
        if (mysqli_num_rows($content_result) === 0) {
            api_error("$content_type with ID $content_id not found", 404);
        }
        
        if ($action === 'add') {
            // Check if already in favorites
            $check_query = "SELECT id FROM favorites 
                           WHERE user_id = ? AND content_type = ? AND content_id = ?";
            
            $check_stmt = mysqli_prepare($conn, $check_query);
            mysqli_stmt_bind_param($check_stmt, 'isi', $user['user_id'], $content_type, $content_id);
            mysqli_stmt_execute($check_stmt);
            $check_result = mysqli_stmt_get_result($check_stmt);
            
            if (mysqli_num_rows($check_result) > 0) {
                api_error("$content_type is already in favorites", 400);
            }
            
            // Add to favorites
            $add_query = "INSERT INTO favorites (user_id, content_type, content_id) 
                         VALUES (?, ?, ?)";
            
            $add_stmt = mysqli_prepare($conn, $add_query);
            mysqli_stmt_bind_param($add_stmt, 'isi', $user['user_id'], $content_type, $content_id);
            
            if (!mysqli_stmt_execute($add_stmt)) {
                api_error('Failed to add to favorites: ' . mysqli_error($conn), 500);
            }
            
            // Return success response
            api_response([
                'content_type' => $content_type,
                'content_id' => $content_id
            ], 201, 'Added to favorites');
        } else {
            // Remove from favorites
            $remove_query = "DELETE FROM favorites 
                           WHERE user_id = ? AND content_type = ? AND content_id = ?";
            
            $remove_stmt = mysqli_prepare($conn, $remove_query);
            mysqli_stmt_bind_param($remove_stmt, 'isi', $user['user_id'], $content_type, $content_id);
            
            if (!mysqli_stmt_execute($remove_stmt)) {
                api_error('Failed to remove from favorites: ' . mysqli_error($conn), 500);
            }
            
            // Check if favorite was found and removed
            if (mysqli_stmt_affected_rows($remove_stmt) === 0) {
                api_error("$content_type is not in favorites", 404);
            }
            
            // Return success response
            api_response([
                'content_type' => $content_type,
                'content_id' => $content_id
            ], 200, 'Removed from favorites');
        }
    } else {
        api_error('Method not allowed', 405);
    }
}

// Handle watchlist
function handle_watchlist($user, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get pagination parameters
    $page = isset($request['params']['page']) ? (int)$request['params']['page'] : 1;
    $limit = isset($request['params']['limit']) ? (int)$request['params']['limit'] : 20;
    $offset = ($page - 1) * $limit;
    
    // Get watchlist
    $query = "SELECT w.*, 
             CASE 
                WHEN w.content_type = 'movie' THEN m.title 
                WHEN w.content_type = 'episode' THEN CONCAT(t.title, ' S', e.season_number, 'E', e.episode_number, ' - ', e.title)
             END as title,
             CASE 
                WHEN w.content_type = 'movie' THEN m.poster 
                WHEN w.content_type = 'episode' THEN COALESCE(e.thumbnail, t.poster)
             END as poster,
             CASE 
                WHEN w.content_type = 'movie' THEN m.duration 
                WHEN w.content_type = 'episode' THEN e.duration
             END as duration,
             CASE 
                WHEN w.content_type = 'movie' THEN m.premium_only 
                WHEN w.content_type = 'episode' THEN (e.is_premium OR t.premium_only)
             END as premium_only
             FROM watchlist w
             LEFT JOIN movies m ON w.content_type = 'movie' AND w.content_id = m.id
             LEFT JOIN episodes e ON w.content_type = 'episode' AND w.content_id = e.id
             LEFT JOIN tvshows t ON e.tvshow_id = t.id
             WHERE w.user_id = ?
             ORDER BY w.last_watched_at DESC
             LIMIT ? OFFSET ?";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'iii', $user['user_id'], $limit, $offset);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $watchlist = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $progress = 0;
        if ($row['duration'] > 0 && $row['last_watched_position'] > 0) {
            $progress = min(100, round(($row['last_watched_position'] / ($row['duration'] * 60)) * 100));
        }
        
        $watchlist[] = [
            'id' => (int)$row['id'],
            'content_type' => $row['content_type'],
            'content_id' => (int)$row['content_id'],
            'title' => $row['title'],
            'poster' => $row['poster'],
            'duration' => (int)$row['duration'],
            'last_watched_position' => (int)$row['last_watched_position'],
            'progress' => $progress,
            'premium_only' => (bool)$row['premium_only'],
            'last_watched_at' => $row['last_watched_at']
        ];
    }
    
    // Get total count for pagination
    $count_query = "SELECT COUNT(*) as total FROM watchlist WHERE user_id = ?";
    $count_stmt = mysqli_prepare($conn, $count_query);
    mysqli_stmt_bind_param($count_stmt, 'i', $user['user_id']);
    mysqli_stmt_execute($count_stmt);
    $count_result = mysqli_stmt_get_result($count_stmt);
    $total = mysqli_fetch_assoc($count_result)['total'];
    
    // Return watchlist with pagination
    api_response([
        'watchlist' => $watchlist,
        'meta' => [
            'pagination' => [
                'total' => (int)$total,
                'per_page' => $limit,
                'current_page' => $page,
                'last_page' => ceil($total / $limit)
            ]
        ]
    ]);
}

// Handle subscription
function handle_subscription($user, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get active subscription
    $query = "SELECT s.*, p.name as plan_name, p.price as plan_price, p.duration as plan_duration, 
             p.features as plan_features
             FROM subscriptions s
             JOIN premium_plans p ON s.plan_id = p.id
             WHERE s.user_id = ? AND s.status = 'active'
             ORDER BY s.created_at DESC
             LIMIT 1";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $user['user_id']);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($result) === 0) {
        // Get subscription history
        $history_query = "SELECT s.*, p.name as plan_name, p.price as plan_price 
                         FROM subscriptions s
                         JOIN premium_plans p ON s.plan_id = p.id
                         WHERE s.user_id = ?
                         ORDER BY s.created_at DESC
                         LIMIT 5";
        
        $history_stmt = mysqli_prepare($conn, $history_query);
        mysqli_stmt_bind_param($history_stmt, 'i', $user['user_id']);
        mysqli_stmt_execute($history_stmt);
        $history_result = mysqli_stmt_get_result($history_stmt);
        
        $history = [];
        while ($sub = mysqli_fetch_assoc($history_result)) {
            $history[] = [
                'id' => (int)$sub['id'],
                'plan_id' => (int)$sub['plan_id'],
                'plan_name' => $sub['plan_name'],
                'plan_price' => (float)$sub['plan_price'],
                'start_date' => $sub['start_date'],
                'end_date' => $sub['end_date'],
                'status' => $sub['status'],
                'payment_method' => $sub['payment_method'],
                'created_at' => $sub['created_at']
            ];
        }
        
        // Get available plans
        $plans_query = "SELECT * FROM premium_plans WHERE status = 'active' ORDER BY price ASC";
        $plans_result = mysqli_query($conn, $plans_query);
        
        $plans = [];
        while ($plan = mysqli_fetch_assoc($plans_result)) {
            $plans[] = [
                'id' => (int)$plan['id'],
                'name' => $plan['name'],
                'price' => (float)$plan['price'],
                'duration' => (int)$plan['duration'],
                'features' => explode("\n", $plan['features'])
            ];
        }
        
        // Return no active subscription
        api_response([
            'active_subscription' => null,
            'subscription_history' => $history,
            'available_plans' => $plans
        ]);
    } else {
        $subscription = mysqli_fetch_assoc($result);
        
        // Get subscription history
        $history_query = "SELECT s.*, p.name as plan_name, p.price as plan_price 
                         FROM subscriptions s
                         JOIN premium_plans p ON s.plan_id = p.id
                         WHERE s.user_id = ? AND s.id != ?
                         ORDER BY s.created_at DESC
                         LIMIT 5";
        
        $history_stmt = mysqli_prepare($conn, $history_query);
        mysqli_stmt_bind_param($history_stmt, 'ii', $user['user_id'], $subscription['id']);
        mysqli_stmt_execute($history_stmt);
        $history_result = mysqli_stmt_get_result($history_stmt);
        
        $history = [];
        while ($sub = mysqli_fetch_assoc($history_result)) {
            $history[] = [
                'id' => (int)$sub['id'],
                'plan_id' => (int)$sub['plan_id'],
                'plan_name' => $sub['plan_name'],
                'plan_price' => (float)$sub['plan_price'],
                'start_date' => $sub['start_date'],
                'end_date' => $sub['end_date'],
                'status' => $sub['status'],
                'payment_method' => $sub['payment_method'],
                'created_at' => $sub['created_at']
            ];
        }
        
        // Return active subscription
        api_response([
            'active_subscription' => [
                'id' => (int)$subscription['id'],
                'plan_id' => (int)$subscription['plan_id'],
                'plan_name' => $subscription['plan_name'],
                'plan_price' => (float)$subscription['plan_price'],
                'plan_duration' => (int)$subscription['plan_duration'],
                'plan_features' => explode("\n", $subscription['plan_features']),
                'start_date' => $subscription['start_date'],
                'end_date' => $subscription['end_date'],
                'status' => $subscription['status'],
                'payment_method' => $subscription['payment_method'],
                'transaction_id' => $subscription['transaction_id'],
                'created_at' => $subscription['created_at']
            ],
            'subscription_history' => $history
        ]);
    }
}
