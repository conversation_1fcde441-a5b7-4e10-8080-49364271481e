<?php
// Set page title
$page_title = 'Payment Settings';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Process form submissions
$success_message = '';
$error_message = '';

// Check if payment_settings table exists
$check_table_query = "SHOW TABLES LIKE 'payment_settings'";
$check_table_result = mysqli_query($conn, $check_table_query);

if (mysqli_num_rows($check_table_result) == 0) {
    // Create payment_settings table if it doesn't exist
    $create_table_query = "CREATE TABLE IF NOT EXISTS payment_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        bkash_merchant_number VARCHAR(20),
        bkash_merchant_name VARCHAR(100),
        nagad_merchant_number VARCHAR(20),
        nagad_merchant_name VARCHAR(100),
        rocket_merchant_number VARCHAR(20),
        rocket_merchant_name VARCHAR(100),
        bkash_enabled BOOLEAN DEFAULT TRUE,
        nagad_enabled BOOLEAN DEFAULT TRUE,
        rocket_enabled BOOLEAN DEFAULT TRUE,
        manual_payment_enabled BOOLEAN DEFAULT TRUE,
        payment_instructions TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";

    mysqli_query($conn, $create_table_query);

    // Insert default values
    $insert_default_query = "INSERT INTO payment_settings (
        bkash_merchant_number,
        bkash_merchant_name,
        nagad_merchant_number,
        nagad_merchant_name,
        rocket_merchant_number,
        rocket_merchant_name,
        payment_instructions
    ) VALUES (
        '01XXXXXXXXX',
        'MovieFlix',
        '01XXXXXXXXX',
        'MovieFlix',
        '01XXXXXXXXX',
        'MovieFlix',
        'Please make the payment to our merchant number and enter the Transaction ID below.'
    )";

    mysqli_query($conn, $insert_default_query);
}

// Get current payment settings
$settings_query = "SELECT * FROM payment_settings WHERE id = 1";
$settings_result = mysqli_query($conn, $settings_query);

// Check if settings exist
if (mysqli_num_rows($settings_result) == 0) {
    // Insert default values if no settings exist
    $insert_default_query = "INSERT INTO payment_settings (
        bkash_merchant_number,
        bkash_merchant_name,
        nagad_merchant_number,
        nagad_merchant_name,
        rocket_merchant_number,
        rocket_merchant_name,
        payment_instructions
    ) VALUES (
        '01XXXXXXXXX',
        'MovieFlix',
        '01XXXXXXXXX',
        'MovieFlix',
        '01XXXXXXXXX',
        'MovieFlix',
        'Please make the payment to our merchant number and enter the Transaction ID below.'
    )";

    mysqli_query($conn, $insert_default_query);

    // Get settings again
    $settings_result = mysqli_query($conn, "SELECT * FROM payment_settings WHERE id = 1");
}

// Set default values in case the database query fails
$default_settings = [
    'bkash_merchant_number' => '01XXXXXXXXX',
    'bkash_merchant_name' => 'MovieFlix',
    'nagad_merchant_number' => '01XXXXXXXXX',
    'nagad_merchant_name' => 'MovieFlix',
    'rocket_merchant_number' => '01XXXXXXXXX',
    'rocket_merchant_name' => 'MovieFlix',
    'bkash_enabled' => true,
    'nagad_enabled' => true,
    'rocket_enabled' => true,
    'manual_payment_enabled' => true,
    'payment_instructions' => 'Please make the payment to our merchant number and enter the Transaction ID below.'
];

$settings = mysqli_fetch_assoc($settings_result) ?: $default_settings;

// Update payment settings
if (isset($_POST['update_settings'])) {
    $bkash_merchant_number = sanitize($_POST['bkash_merchant_number']);
    $bkash_merchant_name = sanitize($_POST['bkash_merchant_name']);
    $nagad_merchant_number = sanitize($_POST['nagad_merchant_number']);
    $nagad_merchant_name = sanitize($_POST['nagad_merchant_name']);
    $rocket_merchant_number = sanitize($_POST['rocket_merchant_number']);
    $rocket_merchant_name = sanitize($_POST['rocket_merchant_name']);

    $bkash_enabled = isset($_POST['bkash_enabled']) ? 1 : 0;
    $nagad_enabled = isset($_POST['nagad_enabled']) ? 1 : 0;
    $rocket_enabled = isset($_POST['rocket_enabled']) ? 1 : 0;
    $manual_payment_enabled = isset($_POST['manual_payment_enabled']) ? 1 : 0;

    $payment_instructions = sanitize($_POST['payment_instructions']);

    $update_query = "UPDATE payment_settings SET
                    bkash_merchant_number = '$bkash_merchant_number',
                    bkash_merchant_name = '$bkash_merchant_name',
                    nagad_merchant_number = '$nagad_merchant_number',
                    nagad_merchant_name = '$nagad_merchant_name',
                    rocket_merchant_number = '$rocket_merchant_number',
                    rocket_merchant_name = '$rocket_merchant_name',
                    bkash_enabled = $bkash_enabled,
                    nagad_enabled = $nagad_enabled,
                    rocket_enabled = $rocket_enabled,
                    manual_payment_enabled = $manual_payment_enabled,
                    payment_instructions = '$payment_instructions',
                    updated_at = NOW()
                    WHERE id = 1";

    if (mysqli_query($conn, $update_query)) {
        $success_message = 'Payment settings updated successfully.';

        // Refresh settings
        $settings_result = mysqli_query($conn, "SELECT * FROM payment_settings WHERE id = 1");
        $settings = mysqli_fetch_assoc($settings_result) ?: $default_settings;
    } else {
        $error_message = 'Error updating payment settings: ' . mysqli_error($conn);
    }
}

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title d-flex align-items-center">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>পেমেন্ট সেটিংস</h1>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <?php if($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="fw-bold text-primary mb-0">পেমেন্ট সেটিংস আপডেট করুন</h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="" class="needs-validation" novalidate>
                            <div class="row">
                                <!-- bKash Settings -->
                                <div class="col-md-6 mb-4">
                                    <div class="card h-100">
                                        <div class="card-header bg-white">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0 fw-bold">বিকাশ সেটিংস</h6>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="bkash_enabled" name="bkash_enabled" <?php echo isset($settings['bkash_enabled']) && $settings['bkash_enabled'] ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="bkash_enabled">এনাবল</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="bkash_merchant_number" class="form-label">বিকাশ মার্চেন্ট নাম্বার</label>
                                                <input type="text" class="form-control" id="bkash_merchant_number" name="bkash_merchant_number" value="<?php echo isset($settings['bkash_merchant_number']) ? $settings['bkash_merchant_number'] : ''; ?>">
                                            </div>
                                            <div class="mb-3">
                                                <label for="bkash_merchant_name" class="form-label">বিকাশ মার্চেন্ট নাম</label>
                                                <input type="text" class="form-control" id="bkash_merchant_name" name="bkash_merchant_name" value="<?php echo isset($settings['bkash_merchant_name']) ? $settings['bkash_merchant_name'] : ''; ?>">
                                            </div>
                                            <div class="text-center mt-3">
                                                <img src="<?php echo SITE_URL; ?>/assets/img/payment/bkash.png" alt="bKash" class="img-fluid" style="max-height: 50px;">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Nagad Settings -->
                                <div class="col-md-6 mb-4">
                                    <div class="card h-100">
                                        <div class="card-header bg-white">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0 fw-bold">নগদ সেটিংস</h6>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="nagad_enabled" name="nagad_enabled" <?php echo isset($settings['nagad_enabled']) && $settings['nagad_enabled'] ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="nagad_enabled">এনাবল</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="nagad_merchant_number" class="form-label">নগদ মার্চেন্ট নাম্বার</label>
                                                <input type="text" class="form-control" id="nagad_merchant_number" name="nagad_merchant_number" value="<?php echo isset($settings['nagad_merchant_number']) ? $settings['nagad_merchant_number'] : ''; ?>">
                                            </div>
                                            <div class="mb-3">
                                                <label for="nagad_merchant_name" class="form-label">নগদ মার্চেন্ট নাম</label>
                                                <input type="text" class="form-control" id="nagad_merchant_name" name="nagad_merchant_name" value="<?php echo isset($settings['nagad_merchant_name']) ? $settings['nagad_merchant_name'] : ''; ?>">
                                            </div>
                                            <div class="text-center mt-3">
                                                <img src="<?php echo SITE_URL; ?>/assets/img/payment/nagad.png" alt="Nagad" class="img-fluid" style="max-height: 50px;">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Rocket Settings -->
                                <div class="col-md-6 mb-4">
                                    <div class="card h-100">
                                        <div class="card-header bg-white">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0 fw-bold">রকেট সেটিংস</h6>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="rocket_enabled" name="rocket_enabled" <?php echo isset($settings['rocket_enabled']) && $settings['rocket_enabled'] ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="rocket_enabled">এনাবল</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="rocket_merchant_number" class="form-label">রকেট মার্চেন্ট নাম্বার</label>
                                                <input type="text" class="form-control" id="rocket_merchant_number" name="rocket_merchant_number" value="<?php echo isset($settings['rocket_merchant_number']) ? $settings['rocket_merchant_number'] : ''; ?>">
                                            </div>
                                            <div class="mb-3">
                                                <label for="rocket_merchant_name" class="form-label">রকেট মার্চেন্ট নাম</label>
                                                <input type="text" class="form-control" id="rocket_merchant_name" name="rocket_merchant_name" value="<?php echo isset($settings['rocket_merchant_name']) ? $settings['rocket_merchant_name'] : ''; ?>">
                                            </div>
                                            <div class="text-center mt-3">
                                                <img src="<?php echo SITE_URL; ?>/assets/img/payment/rocket.png" alt="Rocket" class="img-fluid" style="max-height: 50px;">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Manual Payment Settings -->
                                <div class="col-md-6 mb-4">
                                    <div class="card h-100">
                                        <div class="card-header bg-white">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0 fw-bold">ম্যানুয়াল পেমেন্ট সেটিংস</h6>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="manual_payment_enabled" name="manual_payment_enabled" <?php echo isset($settings['manual_payment_enabled']) && $settings['manual_payment_enabled'] ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="manual_payment_enabled">এনাবল</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="payment_instructions" class="form-label">পেমেন্ট নির্দেশনা</label>
                                                <textarea class="form-control" id="payment_instructions" name="payment_instructions" rows="5"><?php echo isset($settings['payment_instructions']) ? $settings['payment_instructions'] : ''; ?></textarea>
                                                <div class="form-text">ম্যানুয়াল পেমেন্টের জন্য নির্দেশনা লিখুন। এটি পেমেন্ট পেজে দেখানো হবে।</div>
                                            </div>
                                            <div class="text-center mt-3">
                                                <img src="<?php echo SITE_URL; ?>/assets/img/payment/bkash.png" alt="Manual Payment" class="img-fluid" style="max-height: 50px;">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <button type="submit" name="update_settings" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>সেটিংস আপডেট করুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
