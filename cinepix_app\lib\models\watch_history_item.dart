class WatchHistoryItem {
  final int id;
  final String contentType; // 'movie' or 'episode'
  final int contentId;
  final int parentId; // Parent ID (tvshow_id for episodes)
  final String title;
  final String posterUrl;
  final String backdropUrl; // Added backdrop image URL
  final int duration; // Total duration in seconds
  final int watchedPosition; // Watched position in seconds
  final double progress; // Progress percentage (0-100)
  final DateTime lastWatchedAt;

  WatchHistoryItem({
    required this.id,
    required this.contentType,
    required this.contentId,
    this.parentId = 0, // Default to 0 if not provided
    required this.title,
    required this.posterUrl,
    this.backdropUrl = '',
    required this.duration,
    required this.watchedPosition,
    required this.progress,
    required this.lastWatchedAt,
  });

  factory WatchHistoryItem.fromJson(Map<String, dynamic> json) {
    return WatchHistoryItem(
      id: json['id'] ?? 0,
      contentType: json['content_type'] ?? '',
      contentId: json['content_id'] ?? 0,
      parentId: json['parent_id'] ?? 0,
      title: json['title'] ?? '',
      posterUrl: json['poster_url'] ?? '',
      backdropUrl: json['backdrop_url'] ?? '',
      duration: json['duration'] ?? 0,
      watchedPosition: json['watched_position'] ?? 0,
      progress: json['progress'] ?? 0.0,
      lastWatchedAt: json['last_watched_at'] != null
          ? DateTime.parse(json['last_watched_at'])
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content_type': contentType,
      'content_id': contentId,
      'parent_id': parentId, // Added parentId field
      'title': title,
      'poster_url': posterUrl,
      'backdrop_url': backdropUrl,
      'duration': duration,
      'watched_position': watchedPosition,
      'progress': progress,
      'last_watched_at': lastWatchedAt.toIso8601String(),
    };
  }
}
