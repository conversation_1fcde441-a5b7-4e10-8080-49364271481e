class User {
  final int id;
  final String username;
  final String email;
  final String name;
  final String role;
  final String? profileImage;
  final bool isPremium;
  final String? premiumExpires;
  final String createdAt;
  final String? phone;

  User({
    required this.id,
    required this.username,
    required this.email,
    required this.name,
    required this.role,
    this.profileImage,
    required this.isPremium,
    this.premiumExpires,
    required this.createdAt,
    this.phone,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? 0,
      username: json['username'] ?? '',
      email: json['email'] ?? '',
      name: json['name'] ?? '',
      role: json['role'] ?? 'user',
      profileImage: json['profile_image'],
      isPremium: json['is_premium'] ?? false,
      premiumExpires: json['premium_expires'],
      createdAt: json['created_at'] ?? '',
      phone: json['phone'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'name': name,
      'role': role,
      'profile_image': profileImage,
      'is_premium': isPremium,
      'premium_expires': premiumExpires,
      'created_at': createdAt,
      'phone': phone,
    };
  }
}
