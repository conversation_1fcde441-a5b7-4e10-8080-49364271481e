<?php
require_once 'includes/config.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: login.php");
    exit;
}

echo "<h1>API Download Links Fix</h1>";

// Check if download_links table exists
$check_table = mysqli_query($conn, "SHOW TABLES LIKE 'download_links'");
if (mysqli_num_rows($check_table) == 0) {
    echo "<p style='color: red;'>Error: download_links table does not exist!</p>";
    exit;
}

// Check columns in download_links table
$columns_result = mysqli_query($conn, "SHOW COLUMNS FROM download_links");
$columns = [];
while ($column = mysqli_fetch_assoc($columns_result)) {
    $columns[] = $column['Field'];
}

echo "<h2>Current download_links table structure:</h2>";
echo "<pre>";
print_r($columns);
echo "</pre>";

// Check if url column exists
$url_exists = in_array('url', $columns);
$link_url_exists = in_array('link_url', $columns);

if (!$url_exists && $link_url_exists) {
    // Need to add url column
    echo "<p>Adding 'url' column to match API expectations...</p>";
    
    // First add the column
    $add_column = mysqli_query($conn, "ALTER TABLE download_links ADD COLUMN url TEXT");
    
    if ($add_column) {
        // Then copy data from link_url to url
        $update_data = mysqli_query($conn, "UPDATE download_links SET url = link_url");
        
        if ($update_data) {
            echo "<p style='color: green;'>Successfully added 'url' column and copied data from 'link_url'.</p>";
        } else {
            echo "<p style='color: red;'>Error updating data: " . mysqli_error($conn) . "</p>";
        }
    } else {
        echo "<p style='color: red;'>Error adding column: " . mysqli_error($conn) . "</p>";
    }
} elseif ($url_exists && !$link_url_exists) {
    // Need to add link_url column
    echo "<p>Adding 'link_url' column to match database schema...</p>";
    
    // First add the column
    $add_column = mysqli_query($conn, "ALTER TABLE download_links ADD COLUMN link_url TEXT");
    
    if ($add_column) {
        // Then copy data from url to link_url
        $update_data = mysqli_query($conn, "UPDATE download_links SET link_url = url");
        
        if ($update_data) {
            echo "<p style='color: green;'>Successfully added 'link_url' column and copied data from 'url'.</p>";
        } else {
            echo "<p style='color: red;'>Error updating data: " . mysqli_error($conn) . "</p>";
        }
    } else {
        echo "<p style='color: red;'>Error adding column: " . mysqli_error($conn) . "</p>";
    }
} elseif ($url_exists && $link_url_exists) {
    // Both columns exist, make sure they have the same data
    echo "<p>Both 'url' and 'link_url' columns exist. Synchronizing data...</p>";
    
    $sync_data = mysqli_query($conn, "UPDATE download_links SET url = link_url WHERE url != link_url OR url IS NULL");
    
    if ($sync_data) {
        echo "<p style='color: green;'>Successfully synchronized data between 'url' and 'link_url' columns.</p>";
    } else {
        echo "<p style='color: red;'>Error synchronizing data: " . mysqli_error($conn) . "</p>";
    }
} else {
    // Neither column exists, something is wrong
    echo "<p style='color: red;'>Error: Neither 'url' nor 'link_url' column exists in the download_links table!</p>";
}

// Check if server_name and file_size columns exist
$server_name_exists = in_array('server_name', $columns);
$file_size_exists = in_array('file_size', $columns);

if (!$server_name_exists) {
    echo "<p>Adding 'server_name' column...</p>";
    $add_server_name = mysqli_query($conn, "ALTER TABLE download_links ADD COLUMN server_name VARCHAR(50) DEFAULT NULL");
    
    if ($add_server_name) {
        echo "<p style='color: green;'>Successfully added 'server_name' column.</p>";
    } else {
        echo "<p style='color: red;'>Error adding 'server_name' column: " . mysqli_error($conn) . "</p>";
    }
}

if (!$file_size_exists) {
    echo "<p>Adding 'file_size' column...</p>";
    $add_file_size = mysqli_query($conn, "ALTER TABLE download_links ADD COLUMN file_size VARCHAR(20) DEFAULT NULL");
    
    if ($add_file_size) {
        echo "<p style='color: green;'>Successfully added 'file_size' column.</p>";
    } else {
        echo "<p style='color: red;'>Error adding 'file_size' column: " . mysqli_error($conn) . "</p>";
    }
}

// Check if is_premium column exists
$is_premium_exists = in_array('is_premium', $columns);

if (!$is_premium_exists) {
    echo "<p>Adding 'is_premium' column...</p>";
    $add_is_premium = mysqli_query($conn, "ALTER TABLE download_links ADD COLUMN is_premium BOOLEAN DEFAULT FALSE");
    
    if ($add_is_premium) {
        echo "<p style='color: green;'>Successfully added 'is_premium' column.</p>";
    } else {
        echo "<p style='color: red;'>Error adding 'is_premium' column: " . mysqli_error($conn) . "</p>";
    }
}

// Update API files to handle both column names
echo "<h2>Updating API files...</h2>";

// Update direct_movie_details.php
$movie_details_file = 'api/v1/direct_movie_details.php';
if (file_exists($movie_details_file)) {
    $content = file_get_contents($movie_details_file);
    
    // Check if we need to update the file
    if (strpos($content, "url' => \$link['url']") !== false && strpos($content, "url' => \$link['link_url']") === false) {
        // Replace the line that gets the url
        $content = str_replace(
            "'url' => \$link['url'],", 
            "'url' => \$link['url'] ?? \$link['link_url'],", 
            $content
        );
        
        // Save the updated file
        if (file_put_contents($movie_details_file, $content)) {
            echo "<p style='color: green;'>Successfully updated $movie_details_file to handle both column names.</p>";
        } else {
            echo "<p style='color: red;'>Error updating $movie_details_file.</p>";
        }
    } else {
        echo "<p>No update needed for $movie_details_file.</p>";
    }
} else {
    echo "<p style='color: red;'>File $movie_details_file does not exist!</p>";
}

// Update direct_tvshow_episodes.php
$episodes_file = 'api/v1/direct_tvshow_episodes.php';
if (file_exists($episodes_file)) {
    $content = file_get_contents($episodes_file);
    
    // Check if we need to update the file
    if (strpos($content, "url' => \$link['url']") !== false && strpos($content, "url' => \$link['link_url']") === false) {
        // Replace the line that gets the url
        $content = str_replace(
            "'url' => \$link['url'],", 
            "'url' => \$link['url'] ?? \$link['link_url'],", 
            $content
        );
        
        // Save the updated file
        if (file_put_contents($episodes_file, $content)) {
            echo "<p style='color: green;'>Successfully updated $episodes_file to handle both column names.</p>";
        } else {
            echo "<p style='color: red;'>Error updating $episodes_file.</p>";
        }
    } else {
        echo "<p>No update needed for $episodes_file.</p>";
    }
} else {
    echo "<p style='color: red;'>File $episodes_file does not exist!</p>";
}

echo "<h2>Fix Complete</h2>";
echo "<p>The API should now be able to correctly handle download links.</p>";
echo "<p><a href='admin/index.php'>Return to Admin Dashboard</a></p>";
?>
