/* Profile Page Styles */
:root {
    --profile-card-bg: rgba(20, 20, 20, 0.8);
    --profile-card-border: rgba(255, 255, 255, 0.1);
    --profile-card-shadow: rgba(0, 0, 0, 0.3);
    --profile-accent: #e50914;
    --profile-text: #ffffff;
    --profile-text-muted: #a0a0a0;
}

/* Profile Hero Section */
.profile-hero {
    background-color: #0a0a0a;
    background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.8), rgba(20, 20, 20, 0.9)),
                      url('../images/profile-bg.jpg');
    background-size: cover;
    background-position: center;
    position: relative;
    padding: 3rem 0;
}

.profile-header-card {
    background-color: var(--profile-card-bg);
    border-radius: 12px;
    border: 1px solid var(--profile-card-border);
    box-shadow: 0 10px 30px var(--profile-card-shadow);
    overflow: hidden;
    padding: 2rem;
    transition: all 0.3s ease;
}

/* Profile Image */
.profile-image-container {
    position: relative;
    width: 180px;
    height: 180px;
    margin: 0 auto;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid var(--profile-accent);
    box-shadow: 0 5px 15px rgba(229, 9, 20, 0.3);
    transition: all 0.3s ease;
}

.profile-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.profile-image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(229, 9, 20, 0.7);
    overflow: hidden;
    width: 100%;
    height: 0;
    transition: .5s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-image-container:hover .profile-image-overlay {
    height: 40px;
}

.edit-profile-btn {
    color: white;
    font-size: 20px;
    cursor: pointer;
    margin: 0;
    padding: 0;
}

/* Profile Info */
.profile-info {
    padding: 1rem 0;
}

.profile-name {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--profile-text);
}

.profile-email {
    color: var(--profile-text-muted);
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.member-since {
    color: var(--profile-text-muted);
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

/* Profile Stats */
.profile-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 1rem;
}

.profile-stat {
    text-align: center;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 0.8rem 1.2rem;
    min-width: 100px;
    transition: all 0.3s ease;
}

.profile-stat:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.profile-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.3rem;
    color: var(--profile-text);
}

.profile-stat-label {
    color: var(--profile-text-muted);
    font-size: 0.9rem;
}

.premium-stat {
    background-color: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
}

/* Active Package */
.active-package {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid rgba(255, 193, 7, 0.3);
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.1);
}

.package-info {
    display: flex;
    flex-direction: column;
}

.package-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--profile-text);
}

.package-details {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
}

.package-info-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    background-color: rgba(0, 0, 0, 0.2);
    padding: 0.8rem;
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.info-item {
    display: flex;
    align-items: center;
}

.info-label {
    font-size: 0.9rem;
    color: var(--profile-text-muted);
    width: 70px;
    font-weight: 500;
}

.package-price, .package-date, .package-expiry {
    font-size: 0.9rem;
    color: #ffffff;
    margin: 0;
    font-weight: 500;
}

/* Features List */
.package-features {
    padding: 0.5rem;
}

.features-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.8rem;
    color: var(--profile-text);
}

.features-list {
    list-style: none;
    padding-left: 0;
    margin-bottom: 0;
}

.features-list li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.5rem;
    color: #ffffff;
    font-size: 0.9rem;
}

.features-list li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
}

/* Profile Content Section */
.profile-content {
    padding: 3rem 0;
}

/* Profile Cards */
.profile-card {
    background-color: var(--profile-card-bg);
    border-radius: 12px;
    border: 1px solid var(--profile-card-border);
    box-shadow: 0 5px 15px var(--profile-card-shadow);
    overflow: hidden;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.profile-card-header {
    background-color: rgba(0, 0, 0, 0.3);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--profile-card-border);
}

.profile-card-body {
    padding: 1.5rem;
}

/* Activity Card */
.activity-card {
    height: 100%;
}

.activity-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.activity-item {
    padding: 0.8rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-content {
    display: flex;
    align-items: flex-start;
}

.activity-icon {
    font-size: 1.2rem;
    margin-right: 1rem;
    padding-top: 0.2rem;
}

.activity-details {
    flex: 1;
}

.activity-action {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.3rem;
    font-size: 0.9rem;
    color: var(--profile-text-muted);
}

.activity-date {
    font-size: 0.8rem;
    color: var(--profile-text-muted);
}

.activity-title {
    color: var(--profile-text);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    display: block;
    font-size: 0.95rem;
}

.activity-title:hover {
    color: var(--profile-accent);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 2rem 1rem;
}

.empty-icon {
    font-size: 3rem;
    color: var(--profile-text-muted);
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Profile Tabs */
.profile-tabs {
    margin-bottom: 1.5rem;
}

.profile-nav-tabs {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.profile-nav-tabs .nav-link {
    color: var(--profile-text-muted);
    border: none;
    padding: 1rem 1.5rem;
    border-radius: 0;
    transition: all 0.3s ease;
}

.profile-nav-tabs .nav-link:hover {
    color: var(--profile-text);
    background-color: rgba(255, 255, 255, 0.05);
}

.profile-nav-tabs .nav-link.active {
    color: var(--profile-text);
    background-color: transparent;
    border-bottom: 3px solid var(--profile-accent);
}

.profile-tab-content {
    padding-top: 1.5rem;
}

/* Profile Forms */
.profile-form .form-label {
    color: var(--profile-text);
    font-weight: 500;
}

.profile-form .form-control {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--profile-text);
    padding: 0.8rem 1rem;
}

.profile-form .form-control:focus {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: var(--profile-accent);
    box-shadow: 0 0 0 0.25rem rgba(229, 9, 20, 0.25);
}

.profile-form .input-group-text {
    background-color: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--profile-text-muted);
}

.profile-form .form-text {
    color: var(--profile-text-muted);
    font-size: 0.85rem;
}

/* Subscription Details */
.active-subscription-details {
    padding: 0.5rem;
}

.subscription-info {
    margin-bottom: 1.5rem;
}

.subscription-plan {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--profile-text);
}

.subscription-price {
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.subscription-dates {
    font-size: 0.9rem;
    color: var(--profile-text-muted);
    line-height: 1.6;
}

.subscription-status {
    margin-top: 1rem;
}

.subscription-features h6 {
    font-size: 1rem;
    margin-bottom: 1rem;
    color: var(--profile-text);
}

.features-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.features-list li {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

/* Payment History */
.payment-history-table {
    margin-bottom: 0;
    font-size: 0.9rem;
}

.payment-history-table th {
    font-weight: 600;
    border-top: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.payment-history-table td {
    vertical-align: middle;
    border-color: rgba(255, 255, 255, 0.05);
}

.payment-history-table tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Profile Preview */
.profile-preview-container {
    width: 150px;
    height: 150px;
    margin: 0 auto;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid rgba(255, 255, 255, 0.1);
}

.profile-preview-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Custom File Input */
.custom-file-input::-webkit-file-upload-button {
    visibility: hidden;
    width: 0;
}

.custom-file-input::before {
    content: 'Select file';
    display: inline-block;
    background: linear-gradient(to bottom, #e50914, #b20710);
    color: white;
    border-radius: 4px;
    padding: 8px 16px;
    outline: none;
    white-space: nowrap;
    cursor: pointer;
    font-weight: 500;
    font-size: 0.9rem;
    margin-right: 10px;
}

.custom-file-input:hover::before {
    background: linear-gradient(to bottom, #f52631, #c41016);
}

/* Responsive Styles */
@media (max-width: 991.98px) {
    .profile-header-card {
        padding: 1.5rem;
    }

    .profile-image-container {
        width: 150px;
        height: 150px;
    }

    .profile-name {
        font-size: 1.8rem;
    }

    .profile-stats {
        gap: 1rem;
    }

    .profile-stat {
        padding: 0.6rem 1rem;
        min-width: 90px;
    }

    .profile-stat-value {
        font-size: 1.3rem;
    }
}

@media (max-width: 767.98px) {
    .profile-header-card {
        padding: 1.2rem;
    }

    .profile-image-container {
        width: 130px;
        height: 130px;
        margin-bottom: 1rem;
    }

    .profile-name {
        font-size: 1.6rem;
        text-align: center;
    }

    .profile-email, .member-since {
        text-align: center;
    }

    .profile-stats {
        justify-content: center;
    }

    .profile-actions {
        text-align: center;
    }

    .profile-nav-tabs .nav-link {
        padding: 0.8rem 1rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 575.98px) {
    .profile-hero {
        padding: 2rem 0;
    }

    .profile-header-card {
        padding: 1rem;
    }

    .profile-image-container {
        width: 120px;
        height: 120px;
    }

    .profile-name {
        font-size: 1.4rem;
    }

    .profile-stats {
        gap: 0.8rem;
    }

    .profile-stat {
        padding: 0.5rem 0.8rem;
        min-width: 80px;
    }

    .profile-stat-value {
        font-size: 1.1rem;
    }

    .profile-stat-label {
        font-size: 0.8rem;
    }

    .profile-card-header {
        padding: 0.8rem 1rem;
    }

    .profile-card-body {
        padding: 1rem;
    }

    .profile-nav-tabs .nav-link {
        padding: 0.6rem 0.8rem;
        font-size: 0.8rem;
    }
}
