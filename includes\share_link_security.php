<?php
/**
 * Share Link Security and Validation Functions
 * Handles security checks, rate limiting, and abuse prevention
 */

class ShareLinkSecurity {
    private $conn;
    private $max_requests_per_hour = 100;
    private $max_requests_per_day = 500;
    private $use_get_result = false;
    private $blocked_ips = [];
    
    public function __construct($database_connection) {
        $this->conn = $database_connection;
        $this->use_get_result = function_exists('mysqli_stmt_get_result');
        $this->loadBlockedIPs();
    }
    
    /**
     * Validate share link token
     */
    public function validateToken($token) {
        // Basic token format validation
        if (empty($token) || strlen($token) < 10 || strlen($token) > 255) {
            return [
                'valid' => false,
                'error' => 'Invalid token format'
            ];
        }
        
        // Check for malicious patterns
        if (preg_match('/[<>"\']|script|javascript|vbscript/i', $token)) {
            $this->logSecurityEvent('malicious_token', $token);
            return [
                'valid' => false,
                'error' => 'Invalid token format'
            ];
        }
        
        return [
            'valid' => true,
            'error' => null
        ];
    }
    
    /**
     * Check rate limiting for IP address
     */
    public function checkRateLimit($ip_address) {
        // Check if IP is blocked
        if (in_array($ip_address, $this->blocked_ips)) {
            return [
                'allowed' => false,
                'error' => 'IP address is blocked'
            ];
        }
        
        // Check hourly limit
        $hourly_count = $this->getRequestCount($ip_address, 'hour');
        if ($hourly_count >= $this->max_requests_per_hour) {
            $this->logSecurityEvent('rate_limit_exceeded', $ip_address, 'hourly');
            return [
                'allowed' => false,
                'error' => 'Too many requests per hour'
            ];
        }
        
        // Check daily limit
        $daily_count = $this->getRequestCount($ip_address, 'day');
        if ($daily_count >= $this->max_requests_per_day) {
            $this->logSecurityEvent('rate_limit_exceeded', $ip_address, 'daily');
            return [
                'allowed' => false,
                'error' => 'Too many requests per day'
            ];
        }
        
        return [
            'allowed' => true,
            'hourly_count' => $hourly_count,
            'daily_count' => $daily_count
        ];
    }
    

    
    /**
     * Get request count for IP address
     */
    private function getRequestCount($ip_address, $period) {
        $time_condition = '';
        
        switch ($period) {
            case 'hour':
                $time_condition = "access_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)";
                break;
            case 'day':
                $time_condition = "access_time >= DATE_SUB(NOW(), INTERVAL 1 DAY)";
                break;
            default:
                return 0;
        }
        
        $query = "SELECT COUNT(*) as count FROM shared_link_access_logs 
                  WHERE ip_address = ? AND $time_condition";
        $stmt = mysqli_prepare($this->conn, $query);
        mysqli_stmt_bind_param($stmt, 's', $ip_address);
        mysqli_stmt_execute($stmt);
        
        if ($this->use_get_result) {
            $result = mysqli_stmt_get_result($stmt);
            $row = mysqli_fetch_assoc($result);
            return (int)$row['count'];
        } else {
            mysqli_stmt_bind_result($stmt, $count);
            mysqli_stmt_fetch($stmt);
            mysqli_stmt_close($stmt);
            return (int)$count;
        }
    }
    
    /**
     * Check for suspicious activity patterns
     */
    public function checkSuspiciousActivity($ip_address, $user_agent) {
        $suspicious_score = 0;
        $reasons = [];
        
        // Check for bot patterns in user agent
        $bot_patterns = [
            '/bot/i', '/crawler/i', '/spider/i', '/scraper/i',
            '/curl/i', '/wget/i', '/python/i', '/php/i'
        ];
        
        foreach ($bot_patterns as $pattern) {
            if (preg_match($pattern, $user_agent)) {
                $suspicious_score += 30;
                $reasons[] = 'Bot-like user agent';
                break;
            }
        }
        
        // Check for rapid sequential requests
        $recent_requests = $this->getRecentRequestCount($ip_address, 60); // Last 1 minute
        if ($recent_requests > 10) {
            $suspicious_score += 40;
            $reasons[] = 'Too many rapid requests';
        }
        
        // Check for empty or suspicious user agent
        if (empty($user_agent) || strlen($user_agent) < 10) {
            $suspicious_score += 20;
            $reasons[] = 'Empty or suspicious user agent';
        }
        
        // Check for known malicious IP ranges (you can expand this)
        if ($this->isKnownMaliciousIP($ip_address)) {
            $suspicious_score += 50;
            $reasons[] = 'Known malicious IP range';
        }
        
        $is_suspicious = $suspicious_score >= 50;
        
        if ($is_suspicious) {
            $this->logSecurityEvent('suspicious_activity', $ip_address, implode(', ', $reasons));
        }
        
        return [
            'suspicious' => $is_suspicious,
            'score' => $suspicious_score,
            'reasons' => $reasons
        ];
    }
    
    /**
     * Get recent request count for IP
     */
    private function getRecentRequestCount($ip_address, $seconds) {
        $query = "SELECT COUNT(*) as count FROM shared_link_access_logs 
                  WHERE ip_address = ? AND access_time >= DATE_SUB(NOW(), INTERVAL ? SECOND)";
        $stmt = mysqli_prepare($this->conn, $query);
        mysqli_stmt_bind_param($stmt, 'si', $ip_address, $seconds);
        mysqli_stmt_execute($stmt);
        
        if ($this->use_get_result) {
            $result = mysqli_stmt_get_result($stmt);
            $row = mysqli_fetch_assoc($result);
            return (int)$row['count'];
        } else {
            mysqli_stmt_bind_result($stmt, $count);
            mysqli_stmt_fetch($stmt);
            mysqli_stmt_close($stmt);
            return (int)$count;
        }
    }
    
    /**
     * Check if IP is in known malicious ranges
     */
    private function isKnownMaliciousIP($ip_address) {
        // Add your known malicious IP ranges here
        $malicious_ranges = [
            // Example: '***********/24'
        ];
        
        foreach ($malicious_ranges as $range) {
            if ($this->ipInRange($ip_address, $range)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check if IP is in CIDR range
     */
    private function ipInRange($ip, $range) {
        if (strpos($range, '/') === false) {
            return $ip === $range;
        }
        
        list($subnet, $bits) = explode('/', $range);
        $ip_long = ip2long($ip);
        $subnet_long = ip2long($subnet);
        $mask = -1 << (32 - $bits);
        $subnet_long &= $mask;
        
        return ($ip_long & $mask) === $subnet_long;
    }
    
    /**
     * Log security events
     */
    private function logSecurityEvent($event_type, $ip_address, $details = '') {
        // Create security log table if not exists
        $create_table = "CREATE TABLE IF NOT EXISTS share_security_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event_type VARCHAR(50) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            details TEXT,
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_event_type (event_type),
            INDEX idx_ip_address (ip_address),
            INDEX idx_created_at (created_at)
        )";
        
        mysqli_query($this->conn, $create_table);
        
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        $query = "INSERT INTO share_security_logs (event_type, ip_address, details, user_agent) 
                  VALUES (?, ?, ?, ?)";
        $stmt = mysqli_prepare($this->conn, $query);
        mysqli_stmt_bind_param($stmt, 'ssss', $event_type, $ip_address, $details, $user_agent);
        mysqli_stmt_execute($stmt);
    }
    
    /**
     * Load blocked IPs from database
     */
    private function loadBlockedIPs() {
        // Create blocked IPs table if not exists
        $create_table = "CREATE TABLE IF NOT EXISTS blocked_ips (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip_address VARCHAR(45) NOT NULL UNIQUE,
            reason TEXT,
            blocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NULL,
            is_active BOOLEAN DEFAULT TRUE,
            INDEX idx_ip_address (ip_address),
            INDEX idx_is_active (is_active)
        )";
        
        mysqli_query($this->conn, $create_table);
        
        $query = "SELECT ip_address FROM blocked_ips 
                  WHERE is_active = 1 AND (expires_at IS NULL OR expires_at > NOW())";
        $result = mysqli_query($this->conn, $query);
        
        while ($row = mysqli_fetch_assoc($result)) {
            $this->blocked_ips[] = $row['ip_address'];
        }
    }
    
    /**
     * Block IP address
     */
    public function blockIP($ip_address, $reason = '', $duration_hours = null) {
        $expires_at = null;
        if ($duration_hours) {
            $expires_at = date('Y-m-d H:i:s', time() + ($duration_hours * 3600));
        }
        
        $query = "INSERT INTO blocked_ips (ip_address, reason, expires_at) 
                  VALUES (?, ?, ?) 
                  ON DUPLICATE KEY UPDATE 
                  reason = VALUES(reason), 
                  expires_at = VALUES(expires_at), 
                  is_active = 1";
        $stmt = mysqli_prepare($this->conn, $query);
        mysqli_stmt_bind_param($stmt, 'sss', $ip_address, $reason, $expires_at);
        
        if (mysqli_stmt_execute($stmt)) {
            $this->blocked_ips[] = $ip_address;
            $this->logSecurityEvent('ip_blocked', $ip_address, $reason);
            return true;
        }
        
        return false;
    }
    
    /**
     * Auto-block IP based on suspicious activity
     */
    public function autoBlockIfNeeded($ip_address) {
        // Check recent security events for this IP
        $query = "SELECT COUNT(*) as count FROM share_security_logs 
                  WHERE ip_address = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)";
        $stmt = mysqli_prepare($this->conn, $query);
        mysqli_stmt_bind_param($stmt, 's', $ip_address);
        mysqli_stmt_execute($stmt);
        
        if ($this->use_get_result) {
            $result = mysqli_stmt_get_result($stmt);
            $row = mysqli_fetch_assoc($result);
            $recent_violations = (int)$row['count'];
        } else {
            mysqli_stmt_bind_result($stmt, $count);
            mysqli_stmt_fetch($stmt);
            mysqli_stmt_close($stmt);
            $recent_violations = (int)$count;
        }
        
        // Auto-block if too many violations
        if ($recent_violations >= 5) {
            $this->blockIP($ip_address, 'Auto-blocked due to multiple security violations', 24);
            return true;
        }
        
        return false;
    }
    
    /**
     * Clean up old logs
     */
    public function cleanupOldLogs($days = 30) {
        $queries = [
            "DELETE FROM shared_link_access_logs WHERE access_time < DATE_SUB(NOW(), INTERVAL ? DAY)",
            "DELETE FROM share_security_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)",
            "UPDATE blocked_ips SET is_active = 0 WHERE expires_at IS NOT NULL AND expires_at < NOW()"
        ];
        
        foreach ($queries as $query) {
            $stmt = mysqli_prepare($this->conn, $query);
            if (strpos($query, 'UPDATE') === false) {
                mysqli_stmt_bind_param($stmt, 'i', $days);
            }
            mysqli_stmt_execute($stmt);
        }
    }
}

/**
 * Helper function to get security instance
 */
function getShareLinkSecurity() {
    global $conn;
    static $security = null;
    
    if ($security === null) {
        $security = new ShareLinkSecurity($conn);
    }
    
    return $security;
}
?>
