import 'package:flutter/material.dart';
import 'package:cinepix_app/constants/app_constants.dart';
import 'package:cinepix_app/models/movie.dart';
import 'package:cinepix_app/models/tv_show.dart';
import 'package:cinepix_app/widgets/movie_card.dart';
import 'package:cinepix_app/widgets/tv_show_card.dart';
import 'package:cinepix_app/utils/tv_focus_manager.dart';
import 'package:shimmer/shimmer.dart';

class ContentSection extends StatelessWidget {
  final String title;
  final List<dynamic> items;
  final bool isLoading;
  final Function(dynamic) onItemTap;
  final VoidCallback? onSeeAllTap;

  const ContentSection({
    super.key,
    required this.title,
    required this.items,
    required this.isLoading,
    required this.onItemTap,
    this.onSeeAllTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (onSeeAllTap != null)
                TextButton(
                  onPressed: onSeeAllTap,
                  child: const Text('See All'),
                ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 220, // Changed from 280 to 220 to match GenreSection
          child: isLoading
              ? _buildLoadingList()
              : items.isEmpty
                  ? _buildEmptyList()
                  : _buildContentList(),
        ),
      ],
    );
  }

  Widget _buildContentList() {
    return TvFocusManager.createFocusableHorizontalList(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      height: 220,
      itemCount: items.length,
      onItemTap: (index) => onItemTap(items[index]),
      itemBuilder: (context, index) {
        final item = items[index];

        if (item is Movie) {
          return MovieCard(
            movie: item,
            onTap: () => onItemTap(item),
          );
        } else if (item is TvShow) {
          return TvShowCard(
            tvShow: item,
            onTap: () => onItemTap(item),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildLoadingList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      scrollDirection: Axis.horizontal,
      itemCount: 5,
      itemBuilder: (context, index) {
        return Shimmer.fromColors(
          baseColor: Colors.grey[850]!,
          highlightColor: Colors.grey[700]!,
          child: Container(
            width: 140,
            height: 200,
            margin: const EdgeInsets.only(right: 10),
            decoration: BoxDecoration(
              color: Colors.grey[850],
              borderRadius:
                  BorderRadius.circular(AppConstants.cardBorderRadius),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyList() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.movie_outlined,
            size: 48,
            color: Colors.grey[600],
          ),
          const SizedBox(height: 16),
          Text(
            'No content available',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}
