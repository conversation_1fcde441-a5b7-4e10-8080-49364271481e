# MovieFlix - নেটফ্লিক্স স্টাইল মুভি সাইট

এটি একটি নেটফ্লিক্স স্টাইলের মুভি সাইট যা PHP, MySQL, Bootstrap, এবং JavaScript দিয়ে তৈরি করা হয়েছে। এই সাইটে ইউজাররা মুভি ও টিভি শো দেখতে, সার<PERSON>চ করতে, রিভিউ দিতে এবং ওয়াচলিস্টে যোগ করতে পারবে।

## বৈশিষ্ট্যসমূহ

- **রেসপন্সিভ ডিজাইন**: সকল ডিভাইসে (মোবাইল, ট্যাবলেট, ডেস্কটপ) সুন্দরভাবে প্রদর্শিত হবে
- **ইউজার অ্যাকাউন্ট**: রেজিস্ট্রেশন, লগইন, প্রোফাইল ম্যানেজমেন্ট
- **মুভি ও টিভি শো ক্যাটালগ**: ক্যাটাগরি অনুযায়ী ব্রাউজ করার সুবিধা
- **ওয়াচলিস্ট**: ইউজাররা পছন্দের মুভি/টিভি শো সেভ করতে পারবে
- **রিভিউ সিস্টেম**: ইউজাররা রেটিং ও কমেন্ট দিতে পারবে
- **সার্চ ফাংশন**: মুভি ও টিভি শো খুঁজে পাওয়ার সুবিধা
- **অ্যাডমিন প্যানেল**: সা<PERSON><PERSON> ম্যানেজমেন্টের জন্য পূর্ণাঙ্গ অ্যাডমিন ইন্টারফেস
- **প্রিমিয়াম সাবস্ক্রিপশন**: প্রিমিয়াম কন্টেন্ট অ্যাক্সেসের জন্য সাবস্ক্রিপশন সিস্টেম
- **পেমেন্ট গেটওয়ে**: বিকাশ (অটো পেমেন্ট), নগদ ও রকেট (মেনুয়াল পেমেন্ট) ইন্টিগ্রেশন
- **ডাউনলোড/স্ট্রিম লিংক**: বিভিন্ন কোয়ালিটির ডাউনলোড ও স্ট্রিমিং লিংক
- **প্রিমিয়াম সিম্বল**: হোম পেজে প্রিমিয়াম কন্টেন্টে প্রিমিয়াম সিম্বল প্রদর্শন

## ইনস্টলেশন নির্দেশনা

### প্রয়োজনীয় সফটওয়্যার
- PHP 7.4 বা উচ্চতর
- MySQL 5.7 বা উচ্চতর
- Apache/Nginx ওয়েব সার্ভার

### ইনস্টলেশন স্টেপস

1. **ফাইলগুলি আপলোড করুন**: সকল ফাইল ও ফোল্ডার cPanel-এর public_html ডিরেক্টরিতে আপলোড করুন

2. **ডাটাবেস তৈরি করুন**: cPanel-এ MySQL ডাটাবেস তৈরি করুন

3. **ডাটাবেস কনফিগারেশন**: `includes/config.php` ফাইলে ডাটাবেস কানেকশন সেটিংস আপডেট করুন
   ```php
   define('DB_SERVER', 'localhost');
   define('DB_USERNAME', 'your_db_username');
   define('DB_PASSWORD', 'your_db_password');
   define('DB_NAME', 'your_db_name');
   ```

4. **পেমেন্ট গেটওয়ে কনফিগারেশন**: `includes/config.php` ফাইলে পেমেন্ট গেটওয়ে সেটিংস আপডেট করুন
   ```php
   define('BKASH_APP_KEY', 'your_bkash_app_key');
   define('BKASH_APP_SECRET', 'your_bkash_app_secret');
   define('BKASH_USERNAME', 'your_bkash_username');
   define('BKASH_PASSWORD', 'your_bkash_password');

   define('NAGAD_MERCHANT_ID', 'your_nagad_merchant_id');
   define('NAGAD_MERCHANT_NUMBER', 'your_nagad_number');

   define('ROCKET_MERCHANT_NUMBER', 'your_rocket_number');
   ```

5. **ডাটাবেস স্কিমা ইম্পোর্ট করুন**: phpMyAdmin ব্যবহার করে `database.sql` ফাইল ইম্পোর্ট করুন

6. **ডিফল্ট ইমেজ আপলোড করুন**: `uploads` ফোল্ডারে একটি `default.jpg` ইমেজ আপলোড করুন

7. **পেমেন্ট লোগো আপলোড করুন**: `images` ফোল্ডারে `bkash.png`, `nagad.png`, এবং `rocket.png` ইমেজ আপলোড করুন

### অ্যাডমিন লগইন তথ্য
- **ইউজারনেম**: admin
- **পাসওয়ার্ড**: admin123

## ব্যবহার নির্দেশিকা

### ইউজার ফাংশন
- **রেজিস্ট্রেশন/লগইন**: ইউজাররা অ্যাকাউন্ট তৈরি করতে ও লগইন করতে পারবে
- **মুভি/টিভি শো ব্রাউজ**: ক্যাটাগরি অনুযায়ী মুভি ও টিভি শো ব্রাউজ করতে পারবে
- **সার্চ**: টাইটেল দিয়ে মুভি ও টিভি শো খুঁজতে পারবে
- **ওয়াচলিস্ট**: পছন্দের মুভি/টিভি শো ওয়াচলিস্টে যোগ করতে পারবে
- **রিভিউ**: মুভি/টিভি শো এর রিভিউ ও রেটিং দিতে পারবে
- **প্রিমিয়াম সাবস্ক্রিপশন**: প্রিমিয়াম কন্টেন্ট দেখার জন্য সাবস্ক্রাইব করতে পারবে

### অ্যাডমিন ফাংশন
- **কন্টেন্ট ম্যানেজমেন্ট**: মুভি/টিভি শো যোগ, এডিট, ডিলিট করতে পারবে
- **ক্যাটাগরি ম্যানেজমেন্ট**: ক্যাটাগরি যোগ, এডিট, ডিলিট করতে পারবে
- **ইউজার ম্যানেজমেন্ট**: ইউজার তথ্য দেখতে ও ম্যানেজ করতে পারবে
- **রিভিউ ম্যানেজমেন্ট**: রিভিউ দেখতে ও ম্যানেজ করতে পারবে
- **প্রিমিয়াম প্ল্যান ম্যানেজমেন্ট**: প্রিমিয়াম প্ল্যান যোগ, এডিট, ডিলিট করতে পারবে
- **পেমেন্ট ম্যানেজমেন্ট**: পেমেন্ট ট্র্যাক ও ম্যানেজ করতে পারবে

## পেমেন্ট সিস্টেম

### বিকাশ (অটো পেমেন্ট)
- বিকাশ সেন্ডবক্স API ব্যবহার করে অটোমেটিক পেমেন্ট সিস্টেম
- পেমেন্ট গেটওয়েতে রিডাইরেক্ট করে পেমেন্ট প্রসেস করা হয়
- পেমেন্ট সফল হলে অটোমেটিক প্রিমিয়াম অ্যাকাউন্ট অ্যাক্টিভেট হয়

### নগদ/রকেট (মেনুয়াল পেমেন্ট)
- ইউজার মার্চেন্ট নাম্বারে পেমেন্ট করে ট্রানজেকশন আইডি দিবে
- অ্যাডমিন ম্যানুয়ালি ভেরিফাই করে প্রিমিয়াম অ্যাকাউন্ট অ্যাক্টিভেট করবে

## কাস্টমাইজেশন

এই সাইটটি আপনার প্রয়োজন অনুযায়ী কাস্টমাইজ করতে পারেন:

- **থিম কালার**: `css/style.css` ফাইলে `:root` ভেরিয়েবল পরিবর্তন করে থিম কালার পরিবর্তন করতে পারেন
- **লোগো**: `includes/header.php` ফাইলে লোগো পরিবর্তন করতে পারেন
- **সাইট নাম**: `includes/config.php` ফাইলে `SITE_NAME` পরিবর্তন করতে পারেন
- **প্রিমিয়াম প্ল্যান**: `database_premium.sql` ফাইলে প্রিমিয়াম প্ল্যান পরিবর্তন করতে পারেন

## সাপোর্ট

যেকোনো সমস্যা বা প্রশ্নের জন্য যোগাযোগ করুন: <EMAIL>
