<?php
// Set page title
$page_title = 'Messages';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Process form submissions
$success_message = '';
$error_message = '';

// Reply to Message
if (isset($_POST['reply_message'])) {
    $message_id = (int)$_POST['message_id'];
    $reply = sanitize($_POST['reply']);
    
    if (empty($reply)) {
        $error_message = 'Reply message cannot be empty.';
    } else {
        // Update message with reply
        $update_query = "UPDATE messages SET 
                        admin_reply = '$reply', 
                        status = 'replied', 
                        replied_at = NOW() 
                        WHERE id = $message_id";
        
        if (mysqli_query($conn, $update_query)) {
            $success_message = 'Reply sent successfully.';
        } else {
            $error_message = 'Error sending reply: ' . mysqli_error($conn);
        }
    }
}

// Mark as Read
if (isset($_GET['mark_read']) && is_numeric($_GET['mark_read'])) {
    $message_id = (int)$_GET['mark_read'];
    
    $update_query = "UPDATE messages SET status = 'read' WHERE id = $message_id AND status = 'unread'";
    
    if (mysqli_query($conn, $update_query)) {
        $success_message = 'Message marked as read.';
    } else {
        $error_message = 'Error updating message status: ' . mysqli_error($conn);
    }
}

// Delete Message
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $message_id = (int)$_GET['delete'];
    
    $delete_query = "DELETE FROM messages WHERE id = $message_id";
    
    if (mysqli_query($conn, $delete_query)) {
        $success_message = 'Message deleted successfully.';
    } else {
        $error_message = 'Error deleting message: ' . mysqli_error($conn);
    }
}

// Get message to view/reply
$view_message = null;
if (isset($_GET['view']) && is_numeric($_GET['view'])) {
    $message_id = (int)$_GET['view'];
    
    $view_query = "SELECT m.*, u.username, u.email 
                  FROM messages m 
                  LEFT JOIN users u ON m.user_id = u.id 
                  WHERE m.id = $message_id";
    $view_result = mysqli_query($conn, $view_query);
    
    if (mysqli_num_rows($view_result) > 0) {
        $view_message = mysqli_fetch_assoc($view_result);
        
        // Mark as read if unread
        if ($view_message['status'] == 'unread') {
            $update_query = "UPDATE messages SET status = 'read' WHERE id = $message_id";
            mysqli_query($conn, $update_query);
            $view_message['status'] = 'read';
        }
    }
}

// Filter parameters
$status = isset($_GET['status']) ? sanitize($_GET['status']) : '';
$search_term = isset($_GET['search']) ? sanitize($_GET['search']) : '';

// Get messages
$messages_query = "SELECT m.*, u.username, u.email 
                  FROM messages m 
                  LEFT JOIN users u ON m.user_id = u.id 
                  WHERE 1=1";

if (!empty($status)) {
    $messages_query .= " AND m.status = '$status'";
}

if (!empty($search_term)) {
    $messages_query .= " AND (m.subject LIKE '%$search_term%' OR m.message LIKE '%$search_term%' OR u.username LIKE '%$search_term%' OR u.email LIKE '%$search_term%')";
}

$messages_query .= " ORDER BY m.created_at DESC";
$messages_result = mysqli_query($conn, $messages_query);

// Get message counts
$unread_query = "SELECT COUNT(*) as count FROM messages WHERE status = 'unread'";
$unread_result = mysqli_query($conn, $unread_query);
$unread_count = mysqli_fetch_assoc($unread_result)['count'];

$replied_query = "SELECT COUNT(*) as count FROM messages WHERE status = 'replied'";
$replied_result = mysqli_query($conn, $replied_query);
$replied_count = mysqli_fetch_assoc($replied_result)['count'];

$total_query = "SELECT COUNT(*) as count FROM messages";
$total_result = mysqli_query($conn, $total_query);
$total_count = mysqli_fetch_assoc($total_result)['count'];

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">
            <!-- Sidebar Toggle (Topbar) -->
            <button id="sidebarToggle" class="btn btn-link d-md-none rounded-circle mr-3">
                <i class="fa fa-bars"></i>
            </button>

            <!-- Topbar Title -->
            <div class="d-none d-sm-inline-block mr-auto ml-md-3 my-2 my-md-0 mw-100">
                <h1 class="h3 mb-0 text-gray-800">Messages</h1>
            </div>
        </nav>
    </div>

    <div class="container-fluid">
        <?php if($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>
        
        <?php if($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>
        
        <div class="row">
            <?php if($view_message): ?>
            <!-- Message View/Reply -->
            <div class="col-lg-8 mb-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">Message Details</h6>
                        <div>
                            <a href="messages.php" class="btn btn-sm btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i> Back to Messages
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h5 class="mb-0"><?php echo $view_message['subject']; ?></h5>
                                <span class="badge <?php echo $view_message['status'] == 'unread' ? 'bg-danger' : ($view_message['status'] == 'replied' ? 'bg-success' : 'bg-secondary'); ?>">
                                    <?php echo ucfirst($view_message['status']); ?>
                                </span>
                            </div>
                            <div class="d-flex justify-content-between text-muted small mb-3">
                                <div>
                                    <span>From: </span>
                                    <?php if($view_message['user_id']): ?>
                                    <a href="users.php?view=<?php echo $view_message['user_id']; ?>" class="text-decoration-none">
                                        <?php echo $view_message['username']; ?> (<?php echo $view_message['email']; ?>)
                                    </a>
                                    <?php else: ?>
                                    <span>Guest (<?php echo $view_message['email']; ?>)</span>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <span>Date: <?php echo date('M j, Y g:i A', strtotime($view_message['created_at'])); ?></span>
                                </div>
                            </div>
                            <div class="card bg-light">
                                <div class="card-body">
                                    <p class="mb-0"><?php echo nl2br($view_message['message']); ?></p>
                                </div>
                            </div>
                        </div>
                        
                        <?php if($view_message['admin_reply']): ?>
                        <div class="mb-4">
                            <h6 class="mb-2">Your Reply</h6>
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <p class="mb-0"><?php echo nl2br($view_message['admin_reply']); ?></p>
                                </div>
                            </div>
                            <div class="text-end text-muted small mt-1">
                                <span>Replied: <?php echo date('M j, Y g:i A', strtotime($view_message['replied_at'])); ?></span>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if($view_message['status'] != 'replied'): ?>
                        <form method="POST" action="" class="needs-validation" novalidate>
                            <input type="hidden" name="message_id" value="<?php echo $view_message['id']; ?>">
                            
                            <div class="mb-3">
                                <label for="reply" class="form-label">Reply</label>
                                <textarea class="form-control" id="reply" name="reply" rows="5" required></textarea>
                                <div class="invalid-feedback">
                                    Please enter a reply message.
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" name="reply_message" class="btn btn-primary">
                                    <i class="fas fa-reply me-2"></i>Send Reply
                                </button>
                            </div>
                        </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Message Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <?php if($view_message['status'] == 'unread'): ?>
                            <a href="messages.php?mark_read=<?php echo $view_message['id']; ?>" class="btn btn-info">
                                <i class="fas fa-check me-2"></i>Mark as Read
                            </a>
                            <?php endif; ?>
                            
                            <a href="messages.php?delete=<?php echo $view_message['id']; ?>" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this message?')">
                                <i class="fas fa-trash me-2"></i>Delete Message
                            </a>
                            
                            <?php if($view_message['user_id']): ?>
                            <a href="users.php?view=<?php echo $view_message['user_id']; ?>" class="btn btn-secondary">
                                <i class="fas fa-user me-2"></i>View User Profile
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <!-- Messages List -->
            <div class="col-12 mb-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">All Messages</h6>
                        <div>
                            <span class="badge bg-danger me-2">
                                <?php echo $unread_count; ?> Unread
                            </span>
                            <span class="badge bg-success me-2">
                                <?php echo $replied_count; ?> Replied
                            </span>
                            <span class="badge bg-primary">
                                <?php echo $total_count; ?> Total
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Filter Form -->
                        <form method="GET" action="" class="mb-4">
                            <div class="row g-2">
                                <div class="col-md-4">
                                    <select class="form-select" name="status" onchange="this.form.submit()">
                                        <option value="">All Messages</option>
                                        <option value="unread" <?php echo $status == 'unread' ? 'selected' : ''; ?>>Unread</option>
                                        <option value="read" <?php echo $status == 'read' ? 'selected' : ''; ?>>Read</option>
                                        <option value="replied" <?php echo $status == 'replied' ? 'selected' : ''; ?>>Replied</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="search" placeholder="Search messages..." value="<?php echo $search_term; ?>">
                                        <button class="btn btn-primary" type="submit">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <a href="messages.php" class="btn btn-secondary w-100">
                                        <i class="fas fa-undo me-2"></i>Reset
                                    </a>
                                </div>
                            </div>
                        </form>
                        
                        <?php if(mysqli_num_rows($messages_result) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover align-middle">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Status</th>
                                            <th>From</th>
                                            <th>Subject</th>
                                            <th>Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while($message = mysqli_fetch_assoc($messages_result)): ?>
                                        <tr class="<?php echo $message['status'] == 'unread' ? 'table-primary' : ''; ?>">
                                            <td>
                                                <span class="badge <?php echo $message['status'] == 'unread' ? 'bg-danger' : ($message['status'] == 'replied' ? 'bg-success' : 'bg-secondary'); ?>">
                                                    <?php echo ucfirst($message['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if($message['user_id']): ?>
                                                <a href="users.php?view=<?php echo $message['user_id']; ?>" class="text-decoration-none">
                                                    <?php echo $message['username']; ?>
                                                </a>
                                                <?php else: ?>
                                                <span>Guest</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <a href="messages.php?view=<?php echo $message['id']; ?>" class="text-decoration-none">
                                                    <?php echo $message['subject']; ?>
                                                </a>
                                            </td>
                                            <td><?php echo date('M j, Y g:i A', strtotime($message['created_at'])); ?></td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="messages.php?view=<?php echo $message['id']; ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <?php if($message['status'] == 'unread'): ?>
                                                    <a href="messages.php?mark_read=<?php echo $message['id']; ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="Mark as Read">
                                                        <i class="fas fa-check"></i>
                                                    </a>
                                                    <?php endif; ?>
                                                    <a href="messages.php?delete=<?php echo $message['id']; ?>" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="Delete" onclick="return confirm('Are you sure you want to delete this message?')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                No messages found.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
