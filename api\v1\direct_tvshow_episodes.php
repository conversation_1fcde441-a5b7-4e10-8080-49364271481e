<?php
// Include direct config file
require_once '../direct_config.php';

// Get TV show ID and season number from query parameters
$tvshow_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$season_number = isset($_GET['season']) ? (int)$_GET['season'] : 0;

if ($tvshow_id <= 0) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'TV show ID is required',
        'data' => null
    ]);
    exit;
}

if ($season_number <= 0) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Season number is required',
        'data' => null
    ]);
    exit;
}

// Check if TV show exists without status filter
$tvshow_query = "SELECT * FROM tvshows WHERE id = $tvshow_id";
$tvshow_result = mysqli_query($conn, $tvshow_query);

if (!$tvshow_result || mysqli_num_rows($tvshow_result) === 0) {
    http_response_code(404);
    echo json_encode([
        'success' => false,
        'message' => 'TV show not found',
        'data' => null
    ]);
    exit;
}

$tvshow = mysqli_fetch_assoc($tvshow_result);

// Get episodes for the specified season
$episodes_query = "SELECT * FROM episodes
                  WHERE tvshow_id = $tvshow_id AND season_number = $season_number
                  ORDER BY episode_number ASC";

$episodes_result = mysqli_query($conn, $episodes_query);

if (!$episodes_result) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to fetch episodes: ' . mysqli_error($conn),
        'data' => null
    ]);
    exit;
}

$episodes = [];
while ($episode = mysqli_fetch_assoc($episodes_result)) {
    $episode_id = (int)$episode['id'];

    // Get download links for this episode
    // First try episode_links table
    $links_query = "SELECT * FROM episode_links
                   WHERE episode_id = $episode_id
                   ORDER BY quality DESC";

    $links_result = mysqli_query($conn, $links_query);

    // If no results, try download_links table
    if (!$links_result || mysqli_num_rows($links_result) === 0) {
        $links_query = "SELECT * FROM download_links
                       WHERE content_type = 'episode' AND content_id = $episode_id
                       ORDER BY quality DESC";
    }

    $links_result = mysqli_query($conn, $links_query);
    $download_links = [];

    if ($links_result) {
        while ($link = mysqli_fetch_assoc($links_result)) {
            $download_links[] = [
                'id' => (int)$link['id'],
                'quality' => $link['quality'],
                'url' => $link['url'] ?? $link['link_url'] ?? '',
                'server_name' => $link['server_name'] ?? '',
                'file_size' => $link['file_size'] ?? '',
                'is_premium' => (bool)($link['is_premium'] ?? false)
            ];
        }
    }

    $episodes[] = [
        'id' => $episode_id,
        'tvshow_id' => (int)$episode['tvshow_id'],
        'season_number' => (int)$episode['season_number'],
        'episode_number' => (int)$episode['episode_number'],
        'title' => $episode['title'],
        'description' => $episode['description'],
        'duration' => (int)$episode['duration'],
        'thumbnail' => $episode['thumbnail'] ? (strpos($episode['thumbnail'], 'http') === 0 ? $episode['thumbnail'] : SITE_URL . '/uploads/' . $episode['thumbnail']) : '',
        'release_date' => $episode['release_date'],
        'is_premium' => (bool)$episode['is_premium'],
        'download_links' => $download_links
    ];
}

// Return episodes
http_response_code(200);
echo json_encode([
    'success' => true,
    'message' => 'Success',
    'data' => [
        'tvshow' => [
            'id' => (int)$tvshow['id'],
            'title' => $tvshow['title'],
            'poster' => $tvshow['poster'] ? (strpos($tvshow['poster'], 'http') === 0 ? $tvshow['poster'] : SITE_URL . '/uploads/' . $tvshow['poster']) : '',
            'premium_only' => (bool)$tvshow['premium_only']
        ],
        'season_number' => $season_number,
        'episodes' => $episodes
    ]
]);
?>
