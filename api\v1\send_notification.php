<?php
// Include direct config file
require_once '../direct_config.php';

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed. Use POST.',
        'data' => null
    ]);
    exit;
}

// Check if user is admin
session_start();
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || $_SESSION['is_admin'] != 1) {
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized. Admin access required.',
        'data' => null
    ]);
    exit;
}

// Get JSON data from request body
$json_data = file_get_contents('php://input');
$data = json_decode($json_data, true);

// Check if data is valid JSON
if ($data === null) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Invalid JSON data.',
        'data' => null
    ]);
    exit;
}

// Check if notifications table exists
$check_table = "SHOW TABLES LIKE 'notifications'";
$result = mysqli_query($conn, $check_table);

if (mysqli_num_rows($result) == 0) {
    // Create notifications table
    $create_table = "CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        image_url VARCHAR(255) NULL,
        action_type ENUM('none', 'movie', 'tvshow', 'url') DEFAULT 'none',
        action_id VARCHAR(100) NULL,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    
    if (!mysqli_query($conn, $create_table)) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to create notifications table: ' . mysqli_error($conn),
            'data' => null
        ]);
        exit;
    }
}

// Extract data from request
$title = isset($data['title']) ? $data['title'] : '';
$message = isset($data['message']) ? $data['message'] : '';
$image_url = isset($data['image_url']) ? $data['image_url'] : null;
$action_type = isset($data['action_type']) ? $data['action_type'] : 'none';
$action_id = isset($data['action_id']) ? $data['action_id'] : null;
$recipient_type = isset($data['recipient_type']) ? $data['recipient_type'] : 'all';
$user_id = isset($data['user_id']) ? (int)$data['user_id'] : null;

// Validate required fields
if (empty($title) || empty($message)) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Title and message are required.',
        'data' => null
    ]);
    exit;
}

// Validate action type
if (!in_array($action_type, ['none', 'movie', 'tvshow', 'url'])) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Invalid action type. Must be "none", "movie", "tvshow", or "url".',
        'data' => null
    ]);
    exit;
}

// Validate recipient type
if (!in_array($recipient_type, ['all', 'premium', 'specific'])) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Invalid recipient type. Must be "all", "premium", or "specific".',
        'data' => null
    ]);
    exit;
}

// Validate user_id for specific recipient
if ($recipient_type === 'specific' && ($user_id === null || $user_id <= 0)) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'User ID is required for specific recipient.',
        'data' => null
    ]);
    exit;
}

// Prepare SQL based on recipient type
if ($recipient_type === 'all') {
    // Send to all users (user_id = NULL means broadcast)
    $sql = "INSERT INTO notifications (user_id, title, message, image_url, action_type, action_id) 
            VALUES (NULL, ?, ?, ?, ?, ?)";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, 'sssss', $title, $message, $image_url, $action_type, $action_id);
    
    if (mysqli_stmt_execute($stmt)) {
        $notification_id = mysqli_insert_id($conn);
        http_response_code(201);
        echo json_encode([
            'success' => true,
            'message' => 'Notification sent to all users.',
            'data' => [
                'notification_id' => $notification_id
            ]
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to send notification: ' . mysqli_error($conn),
            'data' => null
        ]);
    }
    
    mysqli_stmt_close($stmt);
} elseif ($recipient_type === 'premium') {
    // Get all premium users and send individually
    $premium_users_query = "SELECT id FROM users WHERE is_premium = TRUE";
    $premium_users_result = mysqli_query($conn, $premium_users_query);
    
    if (mysqli_num_rows($premium_users_result) > 0) {
        $sql = "INSERT INTO notifications (user_id, title, message, image_url, action_type, action_id) 
                VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = mysqli_prepare($conn, $sql);
        
        $success_count = 0;
        while ($user = mysqli_fetch_assoc($premium_users_result)) {
            mysqli_stmt_bind_param($stmt, 'isssss', $user['id'], $title, $message, $image_url, $action_type, $action_id);
            if (mysqli_stmt_execute($stmt)) {
                $success_count++;
            }
        }
        
        http_response_code(201);
        echo json_encode([
            'success' => true,
            'message' => "Notification sent to $success_count premium users.",
            'data' => [
                'success_count' => $success_count
            ]
        ]);
        
        mysqli_stmt_close($stmt);
    } else {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'No premium users found.',
            'data' => null
        ]);
    }
} elseif ($recipient_type === 'specific' && $user_id > 0) {
    // Check if user exists
    $user_check = "SELECT id FROM users WHERE id = ?";
    $user_stmt = mysqli_prepare($conn, $user_check);
    mysqli_stmt_bind_param($user_stmt, 'i', $user_id);
    mysqli_stmt_execute($user_stmt);
    mysqli_stmt_store_result($user_stmt);
    
    if (mysqli_stmt_num_rows($user_stmt) == 0) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'User not found.',
            'data' => null
        ]);
        mysqli_stmt_close($user_stmt);
        exit;
    }
    mysqli_stmt_close($user_stmt);
    
    // Send to specific user
    $sql = "INSERT INTO notifications (user_id, title, message, image_url, action_type, action_id) 
            VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, 'isssss', $user_id, $title, $message, $image_url, $action_type, $action_id);
    
    if (mysqli_stmt_execute($stmt)) {
        $notification_id = mysqli_insert_id($conn);
        http_response_code(201);
        echo json_encode([
            'success' => true,
            'message' => 'Notification sent to user.',
            'data' => [
                'notification_id' => $notification_id
            ]
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to send notification: ' . mysqli_error($conn),
            'data' => null
        ]);
    }
    
    mysqli_stmt_close($stmt);
} else {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Invalid recipient configuration.',
        'data' => null
    ]);
}

// Close database connection
mysqli_close($conn);
?>
