<?php
require_once '../../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(403);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

header('Content-Type: application/json');

if (!isset($_GET['type']) || empty($_GET['type'])) {
    echo json_encode([]);
    exit;
}

$type = mysqli_real_escape_string($conn, $_GET['type']);
$data = [];

if ($type === 'movie') {
    $query = "SELECT id, title FROM movies WHERE premium_only = 1 ORDER BY title";
    $result = mysqli_query($conn, $query);
    
    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = [
            'id' => $row['id'],
            'title' => $row['title']
        ];
    }
} elseif ($type === 'tvshow') {
    $query = "SELECT id, title FROM tvshows WHERE premium_only = 1 ORDER BY title";
    $result = mysqli_query($conn, $query);
    
    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = [
            'id' => $row['id'],
            'title' => $row['title']
        ];
    }
}

echo json_encode($data);
?>
