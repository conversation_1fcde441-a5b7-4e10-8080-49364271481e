<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: login.php');
    exit();
}

/**
 * Live User Tracking System
 * Real-time monitoring of active users on the website
 */

// Create user_activity table if not exists
$create_table_query = "CREATE TABLE IF NOT EXISTS user_activity (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    session_id VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    current_page VARCHAR(500) NULL,
    referrer VARCHAR(500) NULL,
    country VARCHAR(100) NULL,
    city VARCHAR(100) NULL,
    device_type VARCHAR(50) NULL,
    browser VARCHAR(100) NULL,
    is_premium BOOLEAN DEFAULT FALSE,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    first_visit TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    page_views INT DEFAULT 1,
    INDEX idx_session (session_id),
    INDEX idx_last_activity (last_activity),
    INDEX idx_user_id (user_id)
)";

mysqli_query($conn, $create_table_query);

// Create user_messages table for chat system
$create_messages_table = "CREATE TABLE IF NOT EXISTS user_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    session_id VARCHAR(255) NOT NULL,
    admin_id INT NULL,
    message TEXT NOT NULL,
    message_type ENUM('user', 'admin') NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_session (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
)";

mysqli_query($conn, $create_messages_table);

// Get live users (active in last 5 minutes)
function getLiveUsers() {
    global $conn;
    
    $query = "SELECT ua.*, u.username, u.email, u.premium_status,
                     COUNT(DISTINCT um.id) as unread_messages
              FROM user_activity ua
              LEFT JOIN users u ON ua.user_id = u.id
              LEFT JOIN user_messages um ON ua.session_id = um.session_id 
                       AND um.message_type = 'user' AND um.is_read = FALSE
              WHERE ua.last_activity >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
              GROUP BY ua.session_id
              ORDER BY ua.last_activity DESC";
    
    $result = mysqli_query($conn, $query);
    $users = [];
    
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $users[] = $row;
        }
    }
    
    return $users;
}

// Get user statistics
function getUserStats() {
    global $conn;
    
    $stats = [];
    
    // Total online users (last 5 minutes)
    $online_query = "SELECT COUNT(DISTINCT session_id) as count FROM user_activity 
                     WHERE last_activity >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)";
    $result = mysqli_query($conn, $online_query);
    $stats['online_now'] = mysqli_fetch_assoc($result)['count'] ?? 0;
    
    // Today's visitors
    $today_query = "SELECT COUNT(DISTINCT session_id) as count FROM user_activity 
                    WHERE DATE(first_visit) = CURDATE()";
    $result = mysqli_query($conn, $today_query);
    $stats['today_visitors'] = mysqli_fetch_assoc($result)['count'] ?? 0;
    
    // Premium users online
    $premium_query = "SELECT COUNT(DISTINCT ua.session_id) as count FROM user_activity ua
                      LEFT JOIN users u ON ua.user_id = u.id
                      WHERE ua.last_activity >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
                      AND (ua.is_premium = TRUE OR u.premium_status = 'active')";
    $result = mysqli_query($conn, $premium_query);
    $stats['premium_online'] = mysqli_fetch_assoc($result)['count'] ?? 0;
    
    // Unread messages
    $messages_query = "SELECT COUNT(*) as count FROM user_messages 
                       WHERE message_type = 'user' AND is_read = FALSE";
    $result = mysqli_query($conn, $messages_query);
    $stats['unread_messages'] = mysqli_fetch_assoc($result)['count'] ?? 0;
    
    return $stats;
}

// Handle AJAX requests
if (isset($_GET['action']) && $_GET['action'] == 'get_live_data') {
    header('Content-Type: application/json');
    
    $data = [
        'users' => getLiveUsers(),
        'stats' => getUserStats(),
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    echo json_encode($data);
    exit();
}

// Handle message sending
if ($_POST && isset($_POST['action']) && $_POST['action'] == 'send_message') {
    $session_id = $_POST['session_id'] ?? '';
    $message = trim($_POST['message'] ?? '');
    $admin_id = $_SESSION['user_id'] ?? 0;
    
    if ($session_id && $message) {
        $insert_query = "INSERT INTO user_messages (session_id, admin_id, message, message_type) 
                         VALUES (?, ?, ?, 'admin')";
        $stmt = mysqli_prepare($conn, $insert_query);
        mysqli_stmt_bind_param($stmt, 'sis', $session_id, $admin_id, $message);
        
        if (mysqli_stmt_execute($stmt)) {
            echo json_encode(['success' => true, 'message' => 'Message sent successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to send message']);
        }
        exit();
    }
}

$live_users = getLiveUsers();
$stats = getUserStats();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live User Tracker - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .user-card {
            border-left: 4px solid #28a745;
            transition: all 0.3s ease;
        }
        .user-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .online-indicator {
            width: 10px;
            height: 10px;
            background: #28a745;
            border-radius: 50%;
            display: inline-block;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .premium-badge {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #000;
            font-weight: bold;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .message-badge {
            background: #dc3545;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 0.8em;
        }
        .chat-modal .modal-body {
            max-height: 400px;
            overflow-y: auto;
        }
        .message-bubble {
            max-width: 80%;
            margin: 5px 0;
            padding: 8px 12px;
            border-radius: 15px;
        }
        .message-user {
            background: #e9ecef;
            margin-left: auto;
            text-align: right;
        }
        .message-admin {
            background: #007bff;
            color: white;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card stats-card">
                    <div class="card-body">
                        <h2><i class="fas fa-users"></i> Live User Tracker</h2>
                        <div class="row mt-3">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h3 id="online-count"><?php echo $stats['online_now']; ?></h3>
                                    <small>Online Now</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h3 id="today-visitors"><?php echo $stats['today_visitors']; ?></h3>
                                    <small>Today's Visitors</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h3 id="premium-online"><?php echo $stats['premium_online']; ?></h3>
                                    <small>Premium Online</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h3 id="unread-messages"><?php echo $stats['unread_messages']; ?></h3>
                                    <small>Unread Messages</small>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small>Last updated: <span id="last-updated"><?php echo date('H:i:s'); ?></span></small>
                            <div class="float-end">
                                <button class="btn btn-light btn-sm" onclick="toggleAutoRefresh()">
                                    <i class="fas fa-sync" id="refresh-icon"></i> Auto Refresh: <span id="refresh-status">ON</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Live Users -->
        <div class="row" id="users-container">
            <?php foreach ($live_users as $user): ?>
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card user-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="card-title">
                                    <span class="online-indicator"></span>
                                    <?php echo $user['username'] ? htmlspecialchars($user['username']) : 'Guest User'; ?>
                                    <?php if ($user['premium_status'] == 'active' || $user['is_premium']): ?>
                                        <span class="badge premium-badge">Premium</span>
                                    <?php endif; ?>
                                </h6>
                                <small class="text-muted">
                                    <i class="fas fa-globe"></i> <?php echo htmlspecialchars($user['ip_address']); ?>
                                    <?php if ($user['country']): ?>
                                        | <?php echo htmlspecialchars($user['country']); ?>
                                    <?php endif; ?>
                                </small>
                            </div>
                            <?php if ($user['unread_messages'] > 0): ?>
                                <span class="message-badge"><?php echo $user['unread_messages']; ?></span>
                            <?php endif; ?>
                        </div>
                        
                        <div class="mt-2">
                            <small>
                                <i class="fas fa-eye"></i> <?php echo $user['page_views']; ?> views |
                                <i class="fas fa-clock"></i> <?php echo date('H:i', strtotime($user['last_activity'])); ?>
                            </small>
                        </div>
                        
                        <?php if ($user['current_page']): ?>
                        <div class="mt-2">
                            <small class="text-primary">
                                <i class="fas fa-link"></i> <?php echo htmlspecialchars(basename($user['current_page'])); ?>
                            </small>
                        </div>
                        <?php endif; ?>
                        
                        <div class="mt-3">
                            <button class="btn btn-primary btn-sm" onclick="openChat('<?php echo $user['session_id']; ?>', '<?php echo htmlspecialchars($user['username'] ?: 'Guest'); ?>')">
                                <i class="fas fa-comment"></i> Chat
                            </button>
                            <?php if ($user['device_type']): ?>
                                <span class="badge bg-secondary"><?php echo htmlspecialchars($user['device_type']); ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- Chat Modal -->
    <div class="modal fade" id="chatModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Chat with <span id="chat-username"></span></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body chat-modal" id="chat-messages">
                    <!-- Messages will be loaded here -->
                </div>
                <div class="modal-footer">
                    <div class="input-group">
                        <input type="text" class="form-control" id="message-input" placeholder="Type your message...">
                        <button class="btn btn-primary" onclick="sendMessage()">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let autoRefresh = true;
        let refreshInterval;
        let currentChatSession = '';

        // Auto refresh functionality
        function startAutoRefresh() {
            refreshInterval = setInterval(loadLiveData, 5000); // Refresh every 5 seconds
        }

        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        }

        function toggleAutoRefresh() {
            autoRefresh = !autoRefresh;
            const status = document.getElementById('refresh-status');
            const icon = document.getElementById('refresh-icon');
            
            if (autoRefresh) {
                status.textContent = 'ON';
                icon.classList.add('fa-spin');
                startAutoRefresh();
            } else {
                status.textContent = 'OFF';
                icon.classList.remove('fa-spin');
                stopAutoRefresh();
            }
        }

        // Load live data via AJAX
        function loadLiveData() {
            fetch('?action=get_live_data')
                .then(response => response.json())
                .then(data => {
                    updateStats(data.stats);
                    updateUsers(data.users);
                    document.getElementById('last-updated').textContent = new Date().toLocaleTimeString();
                })
                .catch(error => console.error('Error loading live data:', error));
        }

        function updateStats(stats) {
            document.getElementById('online-count').textContent = stats.online_now;
            document.getElementById('today-visitors').textContent = stats.today_visitors;
            document.getElementById('premium-online').textContent = stats.premium_online;
            document.getElementById('unread-messages').textContent = stats.unread_messages;
        }

        function updateUsers(users) {
            // This would require more complex DOM manipulation
            // For now, we'll just reload the page periodically
        }

        // Chat functionality
        function openChat(sessionId, username) {
            currentChatSession = sessionId;
            document.getElementById('chat-username').textContent = username;
            
            // Load chat messages
            loadChatMessages(sessionId);
            
            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('chatModal'));
            modal.show();
        }

        function loadChatMessages(sessionId) {
            // This would load messages via AJAX
            // For now, showing placeholder
            document.getElementById('chat-messages').innerHTML = '<p class="text-center text-muted">Chat messages will appear here</p>';
        }

        function sendMessage() {
            const messageInput = document.getElementById('message-input');
            const message = messageInput.value.trim();
            
            if (!message || !currentChatSession) return;
            
            const formData = new FormData();
            formData.append('action', 'send_message');
            formData.append('session_id', currentChatSession);
            formData.append('message', message);
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    messageInput.value = '';
                    loadChatMessages(currentChatSession);
                } else {
                    alert('Failed to send message: ' + data.message);
                }
            })
            .catch(error => console.error('Error sending message:', error));
        }

        // Enter key to send message
        document.getElementById('message-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Start auto refresh on page load
        startAutoRefresh();
    </script>
</body>
</html>
