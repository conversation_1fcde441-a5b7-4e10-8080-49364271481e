<?php
// Set page title
$page_title = 'Update Tags Tables';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Process form submission
$success_message = '';
$error_message = '';

if (isset($_POST['update_tables'])) {
    // SQL commands to execute
    $sql_commands = [
        // Create tags table
        "CREATE TABLE IF NOT EXISTS tags (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) NOT NULL UNIQUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",

        // Create movie_tags junction table
        "CREATE TABLE IF NOT EXISTS movie_tags (
            id INT AUTO_INCREMENT PRIMARY KEY,
            movie_id INT NOT NULL,
            tag_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIG<PERSON> KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
            FOREIG<PERSON> KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE,
            UNIQUE KEY unique_movie_tag (movie_id, tag_id)
        )",

        // Create tvshow_tags junction table
        "CREATE TABLE IF NOT EXISTS tvshow_tags (
            id INT AUTO_INCREMENT PRIMARY KEY,
            tvshow_id INT NOT NULL,
            tag_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (tvshow_id) REFERENCES tvshows(id) ON DELETE CASCADE,
            FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE,
            UNIQUE KEY unique_tvshow_tag (tvshow_id, tag_id)
        )"
    ];

    // Execute each SQL command
    $success = true;
    foreach ($sql_commands as $sql) {
        if (!mysqli_query($conn, $sql)) {
            $success = false;
            $error_message .= "Error: " . mysqli_error($conn) . "<br>";
        }
    }

    // Insert common language tags if they don't exist
    $common_tags = [
        'Bangla Dubbed',
        'Hindi',
        'Dual Audio',
        'Telugu',
        'English',
        'Tamil',
        'Malayalam',
        'Punjabi',
        'Marathi',
        'Korean',
        'Japanese',
        'Chinese',
        'Spanish',
        'French',
        'German',
        'Italian',
        'Russian'
    ];

    foreach ($common_tags as $tag) {
        $tag = sanitize($tag);
        $check_query = "SELECT id FROM tags WHERE name = '$tag'";
        $check_result = mysqli_query($conn, $check_query);

        if (mysqli_num_rows($check_result) == 0) {
            $insert_query = "INSERT INTO tags (name) VALUES ('$tag')";
            if (!mysqli_query($conn, $insert_query)) {
                $success = false;
                $error_message .= "Error inserting tag '$tag': " . mysqli_error($conn) . "<br>";
            }
        }
    }

    if ($success) {
        $success_message = "Tags tables created successfully and common tags added.";
    }
}

// Check if tables exist
$tags_table_exists = mysqli_query($conn, "SHOW TABLES LIKE 'tags'");
$movie_tags_table_exists = mysqli_query($conn, "SHOW TABLES LIKE 'movie_tags'");
$tvshow_tags_table_exists = mysqli_query($conn, "SHOW TABLES LIKE 'tvshow_tags'");

$all_tables_exist = mysqli_num_rows($tags_table_exists) > 0 &&
                    mysqli_num_rows($movie_tags_table_exists) > 0 &&
                    mysqli_num_rows($tvshow_tags_table_exists) > 0;

// Get existing tags
$tags = [];
if (mysqli_num_rows($tags_table_exists) > 0) {
    $tags_query = "SELECT * FROM tags ORDER BY name";
    $tags_result = mysqli_query($conn, $tags_query);
    while ($tag = mysqli_fetch_assoc($tags_result)) {
        $tags[] = $tag;
    }
}

// Add new tag
if (isset($_POST['add_tag'])) {
    $tag_name = sanitize($_POST['tag_name']);

    if (empty($tag_name)) {
        $error_message = "Tag name cannot be empty.";
    } else {
        $check_query = "SELECT id FROM tags WHERE name = '$tag_name'";
        $check_result = mysqli_query($conn, $check_query);

        if (mysqli_num_rows($check_result) > 0) {
            $error_message = "Tag '$tag_name' already exists.";
        } else {
            $insert_query = "INSERT INTO tags (name) VALUES ('$tag_name')";
            if (mysqli_query($conn, $insert_query)) {
                $success_message = "Tag '$tag_name' added successfully.";
                // Refresh tags list
                $tags_query = "SELECT * FROM tags ORDER BY name";
                $tags_result = mysqli_query($conn, $tags_query);
                $tags = [];
                while ($tag = mysqli_fetch_assoc($tags_result)) {
                    $tags[] = $tag;
                }
            } else {
                $error_message = "Error adding tag: " . mysqli_error($conn);
            }
        }
    }
}

// Delete tag
if (isset($_GET['delete_tag']) && is_numeric($_GET['delete_tag'])) {
    $tag_id = (int)$_GET['delete_tag'];

    // Get tag name for confirmation message
    $tag_query = "SELECT name FROM tags WHERE id = $tag_id";
    $tag_result = mysqli_query($conn, $tag_query);
    $tag = mysqli_fetch_assoc($tag_result);

    if ($tag) {
        $delete_query = "DELETE FROM tags WHERE id = $tag_id";
        if (mysqli_query($conn, $delete_query)) {
            $success_message = "Tag '" . $tag['name'] . "' deleted successfully.";
            // Refresh tags list
            $tags_query = "SELECT * FROM tags ORDER BY name";
            $tags_result = mysqli_query($conn, $tags_query);
            $tags = [];
            while ($tag = mysqli_fetch_assoc($tags_result)) {
                $tags[] = $tag;
            }
        } else {
            $error_message = "Error deleting tag: " . mysqli_error($conn);
        }
    } else {
        $error_message = "Tag not found.";
    }
}

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Tags Management</h1>
            </div>
        </div>
    </div>

    <div class="container-fluid">

    <?php if (!empty($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo $success_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo $error_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header bg-white py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Create Tags Tables</h6>
                </div>
                <div class="card-body">
                    <?php if ($all_tables_exist): ?>
                        <div class="alert alert-info">
                            All tags tables already exist.
                        </div>
                    <?php else: ?>
                        <p>This will create the following tables:</p>
                        <ul>
                            <li><code>tags</code> - Stores tag information</li>
                            <li><code>movie_tags</code> - Links tags to movies</li>
                            <li><code>tvshow_tags</code> - Links tags to TV shows</li>
                        </ul>
                        <form method="POST" action="">
                            <button type="submit" name="update_tables" class="btn btn-primary">Create Tables</button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header bg-white py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Add New Tag</h6>
                </div>
                <div class="card-body">
                    <?php if (!$all_tables_exist): ?>
                        <div class="alert alert-warning">
                            Please create the tags tables first.
                        </div>
                    <?php else: ?>
                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="tag_name" class="form-label">Tag Name</label>
                                <input type="text" class="form-control" id="tag_name" name="tag_name" required>
                            </div>
                            <button type="submit" name="add_tag" class="btn btn-success">Add Tag</button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <?php if (count($tags) > 0): ?>
    <div class="card shadow mb-4">
        <div class="card-header bg-white py-3">
            <h6 class="m-0 font-weight-bold text-primary">Existing Tags</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle datatable" id="tagsTable" width="100%" cellspacing="0">
                    <thead class="table-light">
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Created At</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($tags as $tag): ?>
                        <tr>
                            <td><?php echo $tag['id']; ?></td>
                            <td><?php echo $tag['name']; ?></td>
                            <td><?php echo $tag['created_at']; ?></td>
                            <td>
                                <div class="btn-group">
                                    <a href="?delete_tag=<?php echo $tag['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to delete this tag?');" data-bs-toggle="tooltip" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Initialize DataTable
        $('#tagsTable').DataTable({
            order: [[1, 'asc']],
            language: {
                search: "_INPUT_",
                searchPlaceholder: "Search tags..."
            },
            lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]]
        });
    });
</script>

<?php require_once 'includes/footer.php'; ?>
