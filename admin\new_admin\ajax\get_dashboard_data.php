<?php
header('Content-Type: application/json');

// Include configuration and functions
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(403);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

try {
    // Initialize response data
    $response = [
        'stats' => [],
        'recent_activity' => [],
        'chart_data' => []
    ];

    // Get statistics
    $stats = [
        'movies' => 0,
        'premium_movies' => 0,
        'tvshows' => 0,
        'premium_tvshows' => 0,
        'episodes' => 0,
        'premium_episodes' => 0,
        'users' => 0,
        'premium_users' => 0,
        'pending_payments' => 0,
        'completed_payments' => 0,
        'payments' => 0,
        'categories' => 0,
        'reviews' => 0,
        'revenue' => 0
    ];

    // Get movie statistics
    $movie_query = "SELECT 
        COUNT(*) as total_movies,
        COUNT(CASE WHEN is_premium = 1 THEN 1 END) as premium_movies
        FROM movies";
    $movie_result = mysqli_query($conn, $movie_query);
    if ($movie_result && $movie_row = mysqli_fetch_assoc($movie_result)) {
        $stats['movies'] = (int)$movie_row['total_movies'];
        $stats['premium_movies'] = (int)$movie_row['premium_movies'];
    }

    // Get TV show statistics
    $tvshow_query = "SELECT 
        COUNT(*) as total_tvshows,
        COUNT(CASE WHEN is_premium = 1 THEN 1 END) as premium_tvshows
        FROM tvshows";
    $tvshow_result = mysqli_query($conn, $tvshow_query);
    if ($tvshow_result && $tvshow_row = mysqli_fetch_assoc($tvshow_result)) {
        $stats['tvshows'] = (int)$tvshow_row['total_tvshows'];
        $stats['premium_tvshows'] = (int)$tvshow_row['premium_tvshows'];
    }

    // Get episode statistics
    $episode_query = "SELECT 
        COUNT(*) as total_episodes,
        COUNT(CASE WHEN is_premium = 1 THEN 1 END) as premium_episodes
        FROM episodes";
    $episode_result = mysqli_query($conn, $episode_query);
    if ($episode_result && $episode_row = mysqli_fetch_assoc($episode_result)) {
        $stats['episodes'] = (int)$episode_row['total_episodes'];
        $stats['premium_episodes'] = (int)$episode_row['premium_episodes'];
    }

    // Get user statistics
    $user_query = "SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN is_premium = 1 THEN 1 END) as premium_users
        FROM users WHERE role = 'user'";
    $user_result = mysqli_query($conn, $user_query);
    if ($user_result && $user_row = mysqli_fetch_assoc($user_result)) {
        $stats['users'] = (int)$user_row['total_users'];
        $stats['premium_users'] = (int)$user_row['premium_users'];
    }

    // Get payment statistics
    $payment_query = "SELECT 
        COUNT(*) as total_payments,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_payments,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_payments,
        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_revenue
        FROM payments";
    $payment_result = mysqli_query($conn, $payment_query);
    if ($payment_result && $payment_row = mysqli_fetch_assoc($payment_result)) {
        $stats['payments'] = (int)$payment_row['total_payments'];
        $stats['pending_payments'] = (int)$payment_row['pending_payments'];
        $stats['completed_payments'] = (int)$payment_row['completed_payments'];
        $stats['revenue'] = (float)($payment_row['total_revenue'] ?? 0);
    }

    // Get category count
    $category_query = "SELECT COUNT(*) as total_categories FROM categories";
    $category_result = mysqli_query($conn, $category_query);
    if ($category_result && $category_row = mysqli_fetch_assoc($category_result)) {
        $stats['categories'] = (int)$category_row['total_categories'];
    }

    // Get review count
    $review_query = "SELECT COUNT(*) as total_reviews FROM reviews";
    $review_result = mysqli_query($conn, $review_query);
    if ($review_result && $review_row = mysqli_fetch_assoc($review_result)) {
        $stats['reviews'] = (int)$review_row['total_reviews'];
    }

    $response['stats'] = $stats;

    // Get recent activity
    $recent_activity = [];
    
    // Recent movies
    $recent_movies_query = "SELECT title, created_at FROM movies ORDER BY created_at DESC LIMIT 3";
    $recent_movies_result = mysqli_query($conn, $recent_movies_query);
    if ($recent_movies_result) {
        while ($row = mysqli_fetch_assoc($recent_movies_result)) {
            $recent_activity[] = [
                'icon' => 'fas fa-film text-primary',
                'title' => 'নতুন মুভি যোগ করা হয়েছে',
                'description' => $row['title'] . ' মুভি সফলভাবে যোগ করা হয়েছে',
                'time_ago' => time_elapsed_string($row['created_at'])
            ];
        }
    }

    // Recent users
    $recent_users_query = "SELECT username, created_at FROM users WHERE role = 'user' ORDER BY created_at DESC LIMIT 3";
    $recent_users_result = mysqli_query($conn, $recent_users_query);
    if ($recent_users_result) {
        while ($row = mysqli_fetch_assoc($recent_users_result)) {
            $recent_activity[] = [
                'icon' => 'fas fa-user-plus text-success',
                'title' => 'নতুন ইউজার রেজিস্ট্রেশন',
                'description' => $row['username'] . ' নামে নতুন ইউজার রেজিস্ট্রেশন করেছে',
                'time_ago' => time_elapsed_string($row['created_at'])
            ];
        }
    }

    // Recent payments
    $recent_payments_query = "SELECT amount, status, created_at FROM payments ORDER BY created_at DESC LIMIT 3";
    $recent_payments_result = mysqli_query($conn, $recent_payments_query);
    if ($recent_payments_result) {
        while ($row = mysqli_fetch_assoc($recent_payments_result)) {
            $status_text = $row['status'] == 'completed' ? 'সম্পন্ন' : 'পেন্ডিং';
            $icon_color = $row['status'] == 'completed' ? 'text-success' : 'text-warning';
            
            $recent_activity[] = [
                'icon' => 'fas fa-credit-card ' . $icon_color,
                'title' => 'পেমেন্ট ' . $status_text,
                'description' => '৳' . number_format($row['amount']) . ' পেমেন্ট ' . $status_text,
                'time_ago' => time_elapsed_string($row['created_at'])
            ];
        }
    }

    // Sort recent activity by time
    usort($recent_activity, function($a, $b) {
        return strtotime($b['time_ago']) - strtotime($a['time_ago']);
    });

    $response['recent_activity'] = array_slice($recent_activity, 0, 10);

    // Get chart data for revenue
    $chart_data = [];
    $revenue_query = "SELECT 
        DATE_FORMAT(created_at, '%Y-%m') as month,
        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as revenue
        FROM payments 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month";
    
    $revenue_result = mysqli_query($conn, $revenue_query);
    if ($revenue_result) {
        $months = [];
        $revenues = [];
        
        while ($row = mysqli_fetch_assoc($revenue_result)) {
            $months[] = date('M Y', strtotime($row['month'] . '-01'));
            $revenues[] = (float)$row['revenue'];
        }
        
        $chart_data['revenue'] = [
            'labels' => $months,
            'data' => $revenues
        ];
    }

    $response['chart_data'] = $chart_data;

    echo json_encode($response);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error: ' . $e->getMessage()]);
}

// Helper function to calculate time elapsed
function time_elapsed_string($datetime, $full = false) {
    $now = new DateTime;
    $ago = new DateTime($datetime);
    $diff = $now->diff($ago);

    $diff->w = floor($diff->d / 7);
    $diff->d -= $diff->w * 7;

    $string = array(
        'y' => 'বছর',
        'm' => 'মাস',
        'w' => 'সপ্তাহ',
        'd' => 'দিন',
        'h' => 'ঘন্টা',
        'i' => 'মিনিট',
        's' => 'সেকেন্ড',
    );
    
    foreach ($string as $k => &$v) {
        if ($diff->$k) {
            $v = $diff->$k . ' ' . $v;
        } else {
            unset($string[$k]);
        }
    }

    if (!$full) $string = array_slice($string, 0, 1);
    return $string ? implode(', ', $string) . ' আগে' : 'এখনই';
}
?>
