<?php
require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Get content type and ID from URL
$content_type = isset($_GET['type']) ? sanitize($_GET['type']) : '';
$content_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Validate content type
if ($content_type != 'movie' && $content_type != 'tvshow') {
    redirect(SITE_URL . '/admin');
}

// Get content details
if ($content_type == 'movie') {
    $content_query = "SELECT * FROM movies WHERE id = $content_id";
} else {
    $content_query = "SELECT * FROM tvshows WHERE id = $content_id";
}

$content_result = mysqli_query($conn, $content_query);

if (mysqli_num_rows($content_result) == 0) {
    redirect(SITE_URL . '/admin');
}

$content = mysqli_fetch_assoc($content_result);

// Process form submissions
$success_message = '';
$error_message = '';

// Add/Edit Download Link
if (isset($_POST['add_download_link'])) {
    $quality = sanitize($_POST['quality']);
    $link_type = sanitize($_POST['link_type']);
    $link_url = sanitize($_POST['link_url']);
    $server_name = sanitize($_POST['server_name']);
    $file_size = sanitize($_POST['file_size']);
    $subtitle_url_bn = sanitize($_POST['subtitle_url_bn']);
    $subtitle_url_en = sanitize($_POST['subtitle_url_en']);
    $is_premium = isset($_POST['is_premium']) ? 1 : 0;
    $link_id = isset($_POST['link_id']) ? (int)$_POST['link_id'] : 0;

    if (empty($quality) || empty($link_type) || empty($link_url)) {
        $error_message = 'Please fill in all required fields.';
    } else {
        if ($link_id > 0) {
            // Update existing link
            $update_query = "UPDATE download_links SET
                            quality = '$quality',
                            link_type = '$link_type',
                            link_url = '$link_url',
                            subtitle_url_bn = '$subtitle_url_bn',
                            subtitle_url_en = '$subtitle_url_en',
                            server_name = '$server_name',
                            file_size = '$file_size',
                            is_premium = $is_premium
                            WHERE id = $link_id";

            if (mysqli_query($conn, $update_query)) {
                $success_message = 'Download link updated successfully.';
            } else {
                $error_message = 'Error updating download link: ' . mysqli_error($conn);
            }
        } else {
            // Add new link
            $insert_query = "INSERT INTO download_links (content_type, content_id, quality, link_type, link_url, subtitle_url_bn, subtitle_url_en, server_name, file_size, is_premium)
                            VALUES ('$content_type', $content_id, '$quality', '$link_type', '$link_url', '$subtitle_url_bn', '$subtitle_url_en', '$server_name', '$file_size', $is_premium)";

            if (mysqli_query($conn, $insert_query)) {
                $success_message = 'Download link added successfully.';
            } else {
                $error_message = 'Error adding download link: ' . mysqli_error($conn);
            }
        }
    }
}

// Add/Edit Streaming Link
if (isset($_POST['add_streaming_link'])) {
    $quality = sanitize($_POST['quality']);
    $server_name = sanitize($_POST['server_name']);
    $stream_url = sanitize($_POST['stream_url']);
    $subtitle_url_bn = sanitize($_POST['subtitle_url_bn']);
    $subtitle_url_en = sanitize($_POST['subtitle_url_en']);
    $is_premium = isset($_POST['is_premium']) ? 1 : 0;
    $link_id = isset($_POST['link_id']) ? (int)$_POST['link_id'] : 0;

    if (empty($quality) || empty($server_name) || empty($stream_url)) {
        $error_message = 'Please fill in all fields.';
    } else {
        if ($link_id > 0) {
            // Update existing link
            $update_query = "UPDATE streaming_links SET
                            quality = '$quality',
                            server_name = '$server_name',
                            stream_url = '$stream_url',
                            subtitle_url_bn = '$subtitle_url_bn',
                            subtitle_url_en = '$subtitle_url_en',
                            is_premium = $is_premium
                            WHERE id = $link_id";

            if (mysqli_query($conn, $update_query)) {
                $success_message = 'Streaming link updated successfully.';
            } else {
                $error_message = 'Error updating streaming link: ' . mysqli_error($conn);
            }
        } else {
            // Add new link
            $insert_query = "INSERT INTO streaming_links (content_type, content_id, quality, server_name, stream_url, subtitle_url_bn, subtitle_url_en, is_premium)
                            VALUES ('$content_type', $content_id, '$quality', '$server_name', '$stream_url', '$subtitle_url_bn', '$subtitle_url_en', $is_premium)";

            if (mysqli_query($conn, $insert_query)) {
                $success_message = 'Streaming link added successfully.';
            } else {
                $error_message = 'Error adding streaming link: ' . mysqli_error($conn);
            }
        }
    }
}

// Delete Download Link
if (isset($_GET['delete_download']) && $_GET['delete_download'] > 0) {
    $link_id = (int)$_GET['delete_download'];

    $delete_query = "DELETE FROM download_links WHERE id = $link_id AND content_type = '$content_type' AND content_id = $content_id";

    if (mysqli_query($conn, $delete_query)) {
        $success_message = 'Download link deleted successfully.';
    } else {
        $error_message = 'Error deleting download link: ' . mysqli_error($conn);
    }
}

// Delete Streaming Link
if (isset($_GET['delete_streaming']) && $_GET['delete_streaming'] > 0) {
    $link_id = (int)$_GET['delete_streaming'];

    $delete_query = "DELETE FROM streaming_links WHERE id = $link_id AND content_type = '$content_type' AND content_id = $content_id";

    if (mysqli_query($conn, $delete_query)) {
        $success_message = 'Streaming link deleted successfully.';
    } else {
        $error_message = 'Error deleting streaming link: ' . mysqli_error($conn);
    }
}

// Get download links
$download_query = "SELECT * FROM download_links WHERE content_type = '$content_type' AND content_id = $content_id ORDER BY quality DESC, link_type ASC";
$download_result = mysqli_query($conn, $download_query);

// Get streaming links
$streaming_query = "SELECT * FROM streaming_links WHERE content_type = '$content_type' AND content_id = $content_id ORDER BY quality DESC, server_name ASC";
$streaming_result = mysqli_query($conn, $streaming_query);

// Get link to edit if specified
$edit_download_link = null;
$edit_streaming_link = null;

if (isset($_GET['edit_download']) && $_GET['edit_download'] > 0) {
    $link_id = (int)$_GET['edit_download'];

    $edit_query = "SELECT * FROM download_links WHERE id = $link_id AND content_type = '$content_type' AND content_id = $content_id";
    $edit_result = mysqli_query($conn, $edit_query);

    if (mysqli_num_rows($edit_result) > 0) {
        $edit_download_link = mysqli_fetch_assoc($edit_result);
    }
}

if (isset($_GET['edit_streaming']) && $_GET['edit_streaming'] > 0) {
    $link_id = (int)$_GET['edit_streaming'];

    $edit_query = "SELECT * FROM streaming_links WHERE id = $link_id AND content_type = '$content_type' AND content_id = $content_id";
    $edit_result = mysqli_query($conn, $edit_query);

    if (mysqli_num_rows($edit_result) > 0) {
        $edit_streaming_link = mysqli_fetch_assoc($edit_result);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Links - <?php echo SITE_NAME; ?> Admin</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <style>
        body {
            background-color: #f8f9fa;
        }
        .sidebar {
            background-color: #212529;
            min-height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            padding-top: 20px;
        }
        .sidebar-brand {
            padding: 15px 20px;
            margin-bottom: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .sidebar-nav {
            padding: 0;
            list-style: none;
        }
        .sidebar-nav li a {
            display: block;
            padding: 10px 20px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s;
        }
        .sidebar-nav li a:hover, .sidebar-nav li a.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
        }
        .sidebar-nav li a i {
            margin-right: 10px;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .table-responsive {
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-brand">
            <h4 class="text-light">
                <span class="text-danger">MOVIE</span><span class="text-light">FLIX</span>
                <span class="text-muted fs-6">Admin</span>
            </h4>
        </div>
        <ul class="sidebar-nav">
            <li>
                <a href="index.php">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
            </li>
            <li>
                <a href="movies.php">
                    <i class="fas fa-film"></i> Movies
                </a>
            </li>
            <li>
                <a href="tvshows.php">
                    <i class="fas fa-tv"></i> TV Shows
                </a>
            </li>
            <li>
                <a href="categories.php">
                    <i class="fas fa-tags"></i> Categories
                </a>
            </li>
            <li>
                <a href="users.php">
                    <i class="fas fa-users"></i> Users
                </a>
            </li>
            <li>
                <a href="reviews.php">
                    <i class="fas fa-star"></i> Reviews
                </a>
            </li>
            <li>
                <a href="<?php echo SITE_URL; ?>" target="_blank">
                    <i class="fas fa-external-link-alt"></i> View Site
                </a>
            </li>
            <li>
                <a href="<?php echo SITE_URL; ?>/logout.php">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Manage Links: <?php echo $content['title']; ?></h1>
                <div>
                    <?php if ($content_type == 'movie'): ?>
                    <a href="movies.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Movies
                    </a>
                    <?php else: ?>
                    <a href="tvshows.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to TV Shows
                    </a>
                    <?php endif; ?>
                    <?php if ($content_type == 'movie'): ?>
                    <a href="<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $content_id; ?>" class="btn btn-primary" target="_blank">
                        <i class="fas fa-eye me-2"></i>View Movie
                    </a>
                    <?php else: ?>
                    <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $content_id; ?>" class="btn btn-primary" target="_blank">
                        <i class="fas fa-eye me-2"></i>View TV Show
                    </a>
                    <?php endif; ?>
                </div>
            </div>

            <?php if($success_message): ?>
            <div class="alert alert-success">
                <?php echo $success_message; ?>
            </div>
            <?php endif; ?>

            <?php if($error_message): ?>
            <div class="alert alert-danger">
                <?php echo $error_message; ?>
            </div>
            <?php endif; ?>

            <div class="row">
                <!-- Download Links Section -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Download Links</h5>
                        </div>
                        <div class="card-body">
                            <!-- Add/Edit Download Link Form -->
                            <form method="POST" action="" class="mb-4">
                                <input type="hidden" name="link_id" value="<?php echo $edit_download_link ? $edit_download_link['id'] : ''; ?>">
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <label for="quality" class="form-label">Quality</label>
                                        <select class="form-select" id="quality" name="quality" required>
                                            <option value="">Select Quality</option>
                                            <option value="480p" <?php echo ($edit_download_link && $edit_download_link['quality'] == '480p') ? 'selected' : ''; ?>>480p</option>
                                            <option value="720p" <?php echo ($edit_download_link && $edit_download_link['quality'] == '720p') ? 'selected' : ''; ?>>720p</option>
                                            <option value="1080p" <?php echo ($edit_download_link && $edit_download_link['quality'] == '1080p') ? 'selected' : ''; ?>>1080p</option>
                                            <option value="4K" <?php echo ($edit_download_link && $edit_download_link['quality'] == '4K') ? 'selected' : ''; ?>>4K</option>
                                        </select>
                                        <input type="hidden" name="link_type" value="direct">
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="server_name" class="form-label">Server Name</label>
                                        <input type="text" class="form-control" id="server_name" name="server_name" value="<?php echo $edit_download_link ? $edit_download_link['server_name'] : ''; ?>" placeholder="e.g. Server 1, GDrive, Mega">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="file_size" class="form-label">File Size</label>
                                        <input type="text" class="form-control" id="file_size" name="file_size" value="<?php echo $edit_download_link ? $edit_download_link['file_size'] : ''; ?>" placeholder="e.g. 700MB, 1.5GB">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="link_url" class="form-label">Download URL</label>
                                    <input type="url" class="form-control" id="link_url" name="link_url" value="<?php echo $edit_download_link ? $edit_download_link['link_url'] : ''; ?>" required>
                                </div>
                                <div class="mb-3">
                                    <label for="subtitle_url_bn" class="form-label">Bangla Subtitle URL (Optional)</label>
                                    <input type="url" class="form-control" id="subtitle_url_bn" name="subtitle_url_bn" value="<?php echo $edit_download_link ? $edit_download_link['subtitle_url_bn'] : ''; ?>">
                                    <small class="form-text text-muted">SRT or VTT format</small>
                                </div>
                                <div class="mb-3">
                                    <label for="subtitle_url_en" class="form-label">English Subtitle URL (Optional)</label>
                                    <input type="url" class="form-control" id="subtitle_url_en" name="subtitle_url_en" value="<?php echo $edit_download_link ? $edit_download_link['subtitle_url_en'] : ''; ?>">
                                    <small class="form-text text-muted">SRT or VTT format</small>
                                </div>
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="is_premium" name="is_premium" <?php echo ($edit_download_link && $edit_download_link['is_premium']) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_premium">Premium Only</label>
                                </div>
                                <button type="submit" name="add_download_link" class="btn btn-primary">
                                    <?php echo $edit_download_link ? 'Update Download Link' : 'Add Download Link'; ?>
                                </button>
                                <?php if($edit_download_link): ?>
                                <a href="manage_links.php?type=<?php echo $content_type; ?>&id=<?php echo $content_id; ?>" class="btn btn-secondary">Cancel</a>
                                <?php endif; ?>
                            </form>

                            <!-- Download Links Table -->
                            <?php if(mysqli_num_rows($download_result) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Quality</th>
                                            <th>Server</th>
                                            <th>Size</th>
                                            <th>Subtitles</th>
                                            <th>Premium</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while($link = mysqli_fetch_assoc($download_result)): ?>
                                        <tr>
                                            <td><?php echo $link['quality']; ?></td>
                                            <td><?php echo $link['server_name'] ? $link['server_name'] : '-'; ?></td>
                                            <td><?php echo $link['file_size'] ? $link['file_size'] : '-'; ?></td>
                                            <td>
                                                <?php if(!empty($link['subtitle_url_bn'])): ?>
                                                <span class="badge bg-success">BN</span>
                                                <?php endif; ?>
                                                <?php if(!empty($link['subtitle_url_en'])): ?>
                                                <span class="badge bg-info">EN</span>
                                                <?php endif; ?>
                                                <?php if(empty($link['subtitle_url_bn']) && empty($link['subtitle_url_en'])): ?>
                                                <span class="badge bg-secondary">None</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($link['is_premium']): ?>
                                                <span class="badge bg-danger">Yes</span>
                                                <?php else: ?>
                                                <span class="badge bg-secondary">No</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <a href="manage_links.php?type=<?php echo $content_type; ?>&id=<?php echo $content_id; ?>&edit_download=<?php echo $link['id']; ?>" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="manage_links.php?type=<?php echo $content_type; ?>&id=<?php echo $content_id; ?>&delete_download=<?php echo $link['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this download link?')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="alert alert-info">
                                <p class="mb-0">No download links added yet.</p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Streaming Links Section -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">Streaming Links</h5>
                        </div>
                        <div class="card-body">
                            <!-- Add/Edit Streaming Link Form -->
                            <form method="POST" action="" class="mb-4">
                                <input type="hidden" name="link_id" value="<?php echo $edit_streaming_link ? $edit_streaming_link['id'] : ''; ?>">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="quality" class="form-label">Quality</label>
                                        <select class="form-select" id="quality" name="quality" required>
                                            <option value="">Select Quality</option>
                                            <option value="480p" <?php echo ($edit_streaming_link && $edit_streaming_link['quality'] == '480p') ? 'selected' : ''; ?>>480p</option>
                                            <option value="720p" <?php echo ($edit_streaming_link && $edit_streaming_link['quality'] == '720p') ? 'selected' : ''; ?>>720p</option>
                                            <option value="1080p" <?php echo ($edit_streaming_link && $edit_streaming_link['quality'] == '1080p') ? 'selected' : ''; ?>>1080p</option>
                                            <option value="4K" <?php echo ($edit_streaming_link && $edit_streaming_link['quality'] == '4K') ? 'selected' : ''; ?>>4K</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="server_name" class="form-label">Server Name</label>
                                        <input type="text" class="form-control" id="server_name" name="server_name" value="<?php echo $edit_streaming_link ? $edit_streaming_link['server_name'] : ''; ?>" required>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="stream_url" class="form-label">Stream URL</label>
                                    <input type="url" class="form-control" id="stream_url" name="stream_url" value="<?php echo $edit_streaming_link ? $edit_streaming_link['stream_url'] : ''; ?>" required>
                                </div>
                                <div class="mb-3">
                                    <label for="subtitle_url_bn" class="form-label">Bangla Subtitle URL (Optional)</label>
                                    <input type="url" class="form-control" id="subtitle_url_bn" name="subtitle_url_bn" value="<?php echo $edit_streaming_link ? $edit_streaming_link['subtitle_url_bn'] : ''; ?>">
                                    <small class="form-text text-muted">SRT or VTT format</small>
                                </div>
                                <div class="mb-3">
                                    <label for="subtitle_url_en" class="form-label">English Subtitle URL (Optional)</label>
                                    <input type="url" class="form-control" id="subtitle_url_en" name="subtitle_url_en" value="<?php echo $edit_streaming_link ? $edit_streaming_link['subtitle_url_en'] : ''; ?>">
                                    <small class="form-text text-muted">SRT or VTT format</small>
                                </div>
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="is_premium_stream" name="is_premium" <?php echo ($edit_streaming_link && $edit_streaming_link['is_premium']) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_premium_stream">Premium Only</label>
                                </div>
                                <button type="submit" name="add_streaming_link" class="btn btn-success">
                                    <?php echo $edit_streaming_link ? 'Update Streaming Link' : 'Add Streaming Link'; ?>
                                </button>
                                <?php if($edit_streaming_link): ?>
                                <a href="manage_links.php?type=<?php echo $content_type; ?>&id=<?php echo $content_id; ?>" class="btn btn-secondary">Cancel</a>
                                <?php endif; ?>
                            </form>

                            <!-- Streaming Links Table -->
                            <?php if(mysqli_num_rows($streaming_result) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Quality</th>
                                            <th>Server</th>
                                            <th>Subtitles</th>
                                            <th>Premium</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while($link = mysqli_fetch_assoc($streaming_result)): ?>
                                        <tr>
                                            <td><?php echo $link['quality']; ?></td>
                                            <td><?php echo $link['server_name']; ?></td>
                                            <td>
                                                <?php if(!empty($link['subtitle_url_bn'])): ?>
                                                <span class="badge bg-success">BN</span>
                                                <?php endif; ?>
                                                <?php if(!empty($link['subtitle_url_en'])): ?>
                                                <span class="badge bg-info">EN</span>
                                                <?php endif; ?>
                                                <?php if(empty($link['subtitle_url_bn']) && empty($link['subtitle_url_en'])): ?>
                                                <span class="badge bg-secondary">None</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($link['is_premium']): ?>
                                                <span class="badge bg-danger">Yes</span>
                                                <?php else: ?>
                                                <span class="badge bg-secondary">No</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <a href="manage_links.php?type=<?php echo $content_type; ?>&id=<?php echo $content_id; ?>&edit_streaming=<?php echo $link['id']; ?>" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="manage_links.php?type=<?php echo $content_type; ?>&id=<?php echo $content_id; ?>&delete_streaming=<?php echo $link['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this streaming link?')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="alert alert-info">
                                <p class="mb-0">No streaming links added yet.</p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Episodes Section (for TV Shows) -->
            <?php if($content_type == 'tvshow'): ?>
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Episodes</h5>
                        <a href="manage_episodes.php?tvshow=<?php echo $content_id; ?>" class="btn btn-light btn-sm">
                            <i class="fas fa-list me-1"></i> Manage Episodes
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php
                    // Get episodes count by season
                    $episodes_query = "SELECT season_number, COUNT(*) as episode_count FROM episodes WHERE tvshow_id = $content_id GROUP BY season_number ORDER BY season_number ASC";
                    $episodes_result = mysqli_query($conn, $episodes_query);

                    if(mysqli_num_rows($episodes_result) > 0):
                    ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Season</th>
                                    <th>Episodes</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while($season = mysqli_fetch_assoc($episodes_result)): ?>
                                <tr>
                                    <td>Season <?php echo $season['season_number']; ?></td>
                                    <td><?php echo $season['episode_count']; ?> episodes</td>
                                    <td>
                                        <a href="manage_episodes.php?tvshow=<?php echo $content_id; ?>&season=<?php echo $season['season_number']; ?>" class="btn btn-sm btn-info">
                                            <i class="fas fa-edit me-1"></i> Manage Episodes
                                        </a>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-info">
                        <p class="mb-0">No episodes added yet. <a href="manage_episodes.php?tvshow=<?php echo $content_id; ?>" class="alert-link">Add episodes</a>.</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</body>
</html>
