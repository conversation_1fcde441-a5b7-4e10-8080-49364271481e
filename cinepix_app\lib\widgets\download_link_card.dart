import 'package:flutter/material.dart';
import 'package:cinepix_app/constants/app_constants.dart';
import 'package:cinepix_app/models/download_link.dart';
import 'package:cinepix_app/services/video_service.dart';
import 'package:cinepix_app/utils/device_utils.dart';
import 'package:cinepix_app/utils/tv_focus_utils.dart';

class DownloadLinkCard extends StatefulWidget {
  final DownloadLink link;
  final String title;
  final VoidCallback onPlayTap;
  final bool isPremiumUser;

  const DownloadLinkCard({
    super.key,
    required this.link,
    required this.title,
    required this.onPlayTap,
    required this.isPremiumUser,
  });

  @override
  State<DownloadLinkCard> createState() => _DownloadLinkCardState();
}

class _DownloadLinkCardState extends State<DownloadLinkCard> {
  // Focus nodes for TV remote navigation
  late final FocusNode _playFocusNode;
  late final FocusNode _downloadFocusNode;

  @override
  void initState() {
    super.initState();
    _playFocusNode = FocusNode();
    _downloadFocusNode = FocusNode();
  }

  @override
  void dispose() {
    _playFocusNode.dispose();
    _downloadFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bool canAccess = !widget.link.isPremium || widget.isPremiumUser;
    final bool isTV = DeviceUtils.isAndroidTV(context);

    // Initialize focus nodes for TV
    if (isTV) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        TvFocusUtils.initFocusNodes(context, [_playFocusNode]);
      });
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).cardTheme.color,
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        border: Border.all(
          color: widget.link.isPremium
              ? AppConstants.primaryColor.withOpacity(0.5)
              : Colors.transparent,
          width: widget.link.isPremium ? 1 : 0,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Quality and Premium badge
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppConstants.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Icon(
                      Icons.hd,
                      color: AppConstants.primaryColor,
                      size: 24,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  widget.link.quality,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(width: 8),
                if (widget.link.isPremium)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: AppConstants.primaryColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Text(
                      'PREMIUM',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),

            const SizedBox(height: 8),

            // Server and file size info
            Row(
              children: [
                Icon(
                  Icons.storage,
                  size: 14,
                  color: Theme.of(context)
                      .textTheme
                      .bodyMedium
                      ?.color
                      ?.withOpacity(0.7),
                ),
                const SizedBox(width: 4),
                Text(
                  widget.link.serverName,
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context)
                        .textTheme
                        .bodyMedium
                        ?.color
                        ?.withOpacity(0.7),
                  ),
                ),
                const SizedBox(width: 12),
                Icon(
                  Icons.sd_card,
                  size: 14,
                  color: Theme.of(context)
                      .textTheme
                      .bodyMedium
                      ?.color
                      ?.withOpacity(0.7),
                ),
                const SizedBox(width: 4),
                Text(
                  widget.link.fileSize,
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context)
                        .textTheme
                        .bodyMedium
                        ?.color
                        ?.withOpacity(0.7),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Action buttons in a row
            Builder(
              builder: (context) {
                final bool isTV = DeviceUtils.isAndroidTV(context);

                return Row(
                  children: [
                    // Play button
                    Expanded(
                      child: TvFocusUtils.createFocusScope(
                        context: context,
                        focusNode: _playFocusNode,
                        nextFocus: isTV ? null : _downloadFocusNode,
                        child: Container(
                          decoration: BoxDecoration(
                            color: canAccess
                                ? AppConstants.primaryColor
                                : Colors.grey.withAlpha(100),
                            borderRadius: BorderRadius.circular(8),
                            // Add a focus highlight for TV
                            border: Border.all(
                              color: isTV && _playFocusNode.hasFocus
                                  ? Colors.white
                                  : Colors.transparent,
                              width: isTV && _playFocusNode.hasFocus ? 2 : 0,
                            ),
                          ),
                          child: InkWell(
                            onTap: canAccess
                                ? widget.onPlayTap
                                : _showPremiumDialog,
                            borderRadius: BorderRadius.circular(8),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.play_circle_fill,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'দেখুন',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),

                    // Only show download button on mobile devices, not on TV
                    if (!isTV) ...[
                      const SizedBox(width: 12),

                      // Download button
                      Expanded(
                        child: TvFocusUtils.createFocusScope(
                          context: context,
                          focusNode: _downloadFocusNode,
                          previousFocus: _playFocusNode,
                          child: Container(
                            decoration: BoxDecoration(
                              color: canAccess
                                  ? Colors.blue
                                  : Colors.grey.withAlpha(100),
                              borderRadius: BorderRadius.circular(8),
                              // Add a focus highlight for TV
                              border: Border.all(
                                color: isTV && _downloadFocusNode.hasFocus
                                    ? Colors.white
                                    : Colors.transparent,
                                width:
                                    isTV && _downloadFocusNode.hasFocus ? 2 : 0,
                              ),
                            ),
                            child: InkWell(
                              onTap: canAccess
                                  ? () => _downloadVideo(context)
                                  : _showPremiumDialog,
                              borderRadius: BorderRadius.circular(8),
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 10),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.download,
                                      color: Colors.white,
                                      size: 16,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      'ডাউনলোড',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _downloadVideo(BuildContext context) async {
    // Open the download link in external browser
    final success = await VideoService.openDownloadLinkInBrowser(widget.link);

    if (!context.mounted) return;

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              '${widget.title} (${widget.link.quality}) ডাউনলোড লিংক ব্রাউজারে খোলা হয়েছে'),
          duration: const Duration(seconds: 3),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('ডাউনলোড লিংক খুলতে সমস্যা হয়েছে। আবার চেষ্টা করুন।'),
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  void _showPremiumDialog() {
    showDialog(
      context: navigatorKey.currentContext!,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(100),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Premium icon
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppConstants.primaryColor.withAlpha(50),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.workspace_premium,
                  color: AppConstants.primaryColor,
                  size: 40,
                ),
              ),
              const SizedBox(height: 20),

              // Title
              const Text(
                'প্রিমিয়াম কনটেন্ট',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 12),

              // Content
              const Text(
                'এই কনটেন্ট শুধুমাত্র প্রিমিয়াম ব্যবহারকারীদের জন্য উপলব্ধ। প্রিমিয়াম কনটেন্ট অ্যাক্সেস করতে আপগ্রেড করুন।',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.white70,
                ),
              ),
              const SizedBox(height: 24),

              // Buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: Colors.grey),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                    ),
                    child: const Text(
                      'বাতিল',
                      style: TextStyle(color: Colors.white70),
                    ),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      // Open website to purchase premium
                      _launchPremiumWebsite();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.primaryColor,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                    ),
                    child: const Text(
                      'আপগ্রেড করুন',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _launchPremiumWebsite() {
    // For now, just show a snackbar
    ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
      const SnackBar(
        content: Text('Opening premium website: cinepix.top/premium.php'),
        duration: Duration(seconds: 2),
      ),
    );

    // In a real implementation, we would use url_launcher package:
    // final Uri url = Uri.parse('https://cinepix.top/premium.php');
    // launchUrl(url, mode: LaunchMode.externalApplication);
  }
}

// Global navigator key for accessing context
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
