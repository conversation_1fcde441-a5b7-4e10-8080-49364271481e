<?php
// Include header file which contains the site's design
require_once 'includes/config.php';

// Don't include the regular header as we need a custom one for the player
// We'll manually add the necessary styles and scripts

// Get video URL from query parameter or use default
$video_url = isset($_GET['url']) ? $_GET['url'] : 'https://odd-darkness-074fsadsafsafasfjlknmmlkaytr9pe8afnhdklnfalskdftgy.bdmovieshub.workers.dev/994/RRR+2022+-+Hindi+-WEBDL+-+720p.mkv?hash=AgADjg';
$video_title = isset($_GET['title']) ? $_GET['title'] : 'RRR (2022) - Hindi - WEBDL - 720p';
$poster = isset($_GET['poster']) ? $_GET['poster'] : '';
$quality = isset($_GET['quality']) ? $_GET['quality'] : '720p';
$language = isset($_GET['language']) ? $_GET['language'] : 'Hindi';
$source = isset($_GET['source']) ? $_GET['source'] : 'WEBDL';
$is_premium = isset($_GET['premium']) && $_GET['premium'] == '1';

// Check file extension to determine video type
$file_extension = strtolower(pathinfo(parse_url($video_url, PHP_URL_PATH), PATHINFO_EXTENSION));
$video_type = 'video/mp4'; // Default video type

// Set appropriate MIME type based on file extension
if ($file_extension === 'mkv') {
    $video_type = 'video/x-matroska';
} elseif ($file_extension === 'webm') {
    $video_type = 'video/webm';
} elseif ($file_extension === 'ogg' || $file_extension === 'ogv') {
    $video_type = 'video/ogg';
} elseif ($file_extension === 'mov') {
    $video_type = 'video/quicktime';
} elseif ($file_extension === 'avi') {
    $video_type = 'video/x-msvideo';
} elseif ($file_extension === 'flv') {
    $video_type = 'video/x-flv';
} elseif ($file_extension === '3gp') {
    $video_type = 'video/3gpp';
}

// Generate a unique cache key for this video
$cache_key = md5($video_url);

// Get the referrer URL for the back button
$referrer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : SITE_URL;

// Check if referrer is from the same site to prevent security issues
if (strpos($referrer, SITE_URL) === false) {
    $referrer = SITE_URL; // If referrer is external, default to home page
}

// Set back button text
$back_button_text = 'আগের পেজে ফিরে যান';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="site-url" content="<?php echo SITE_URL; ?>">
    <title><?php echo $video_title; ?> - <?php echo SITE_NAME; ?></title>

    <!-- Preload critical resources -->
    <link rel="preload" href="https://cdn.plyr.io/3.7.8/plyr.css" as="style">
    <link rel="preload" href="https://cdn.plyr.io/3.7.8/plyr.polyfilled.js" as="script">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/hls.js@latest" as="script">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" as="style">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Plyr.io CSS -->
    <link rel="stylesheet" href="https://cdn.plyr.io/3.7.8/plyr.css" />

    <!-- Site's Custom CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/style.css?v=<?php echo filemtime(__DIR__ . '/css/style.css'); ?>">

    <!-- Owl Carousel (if needed for related videos) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css">

    <style>
        :root {
            --plyr-color-main: #e50914;
            --plyr-video-control-color: #fff;
            --plyr-video-control-color-hover: #e50914;
            --plyr-video-control-background-hover: rgba(0, 0, 0, 0.5);
            --plyr-audio-control-background-hover: rgba(0, 0, 0, 0.5);
            --plyr-video-control-radius: 3px;
            --plyr-progress-loading-background: rgba(229, 9, 20, 0.5);
            --plyr-progress-loading-size: 5px;
            --plyr-range-thumb-background: #e50914;
            --plyr-range-fill-background: #e50914;
        }

        /* Player page specific styles */
        body.player-page {
            background-color: var(--dark-color);
            color: var(--light-color);
            padding-top: 76px; /* Adjusted for navbar */
            margin: 0;
            overflow-x: hidden;
        }

        /* Override some site styles for the player page */
        .player-page .navbar {
            background-color: rgba(0, 0, 0, 0.9);
        }

        .player-page main {
            padding-top: 0;
        }

        .content-wrapper {
            padding: 20px 0;
        }

        .player-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #000;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }

        .video-wrapper {
            position: relative;
            background-color: #000;
            border-radius: 8px 8px 0 0;
            overflow: hidden;
        }

        .plyr {
            --plyr-video-controls-background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.7));
            height: auto !important;
            width: 100% !important;
        }

        /* Larger control buttons */
        .plyr__control--overlaid {
            padding: 20px !important;
        }

        .plyr__control[data-plyr="rewind"],
        .plyr__control[data-plyr="fast-forward"] {
            font-size: 1.2em;
            padding: 8px !important;
        }

        /* Highlight rewind/forward buttons */
        .plyr__control[data-plyr="rewind"],
        .plyr__control[data-plyr="fast-forward"] {
            background-color: rgba(229, 9, 20, 0.2);
            border-radius: 50%;
            transition: background-color 0.3s ease;
        }

        .plyr__control[data-plyr="rewind"]:hover,
        .plyr__control[data-plyr="fast-forward"]:hover {
            background-color: rgba(229, 9, 20, 0.5);
        }

        .video-info {
            padding: 20px;
            background-color: #222;
            border-radius: 0 0 8px 8px;
        }

        h1 {
            font-size: 24px;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .btn-back {
            margin-bottom: 20px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 4px;
            background-color: var(--primary-color);
            color: #fff;
            border: none;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .btn-back:hover {
            background-color: #c50812;
            color: #fff;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 10;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #e50914;
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 15px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .loading-text {
            color: #fff;
            font-size: 16px;
            text-align: center;
        }

        .loading-progress {
            width: 200px;
            height: 4px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            margin-top: 10px;
            overflow: hidden;
        }

        .loading-progress-bar {
            height: 100%;
            background-color: #e50914;
            width: 0%;
            transition: width 0.3s ease;
        }

        .quality-selector {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 5;
            background-color: rgba(0, 0, 0, 0.7);
            border-radius: 4px;
            padding: 5px;
            display: flex;
            gap: 5px;
        }

        .quality-btn {
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
            border: none;
            border-radius: 3px;
            padding: 3px 8px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quality-btn.active {
            background-color: #e50914;
        }

        .quality-btn:hover {
            background-color: rgba(229, 9, 20, 0.7);
        }

        .video-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }

        .video-meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
            background-color: rgba(255, 255, 255, 0.1);
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 14px;
        }

        .video-meta-item i {
            color: #e50914;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .action-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 4px;
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
            border: none;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .action-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .action-btn.primary {
            background-color: #e50914;
        }

        .action-btn.primary:hover {
            background-color: #c50812;
        }

        .shortcuts-container {
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }

        .shortcuts-title {
            font-size: 18px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .shortcuts-title i {
            color: #e50914;
        }

        .shortcut-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .shortcut-key {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 3px 8px;
            border-radius: 3px;
            margin-right: 10px;
            font-family: monospace;
            font-size: 14px;
            min-width: 30px;
            text-align: center;
        }

        .shortcut-description {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            .video-wrapper {
                border-radius: 0;
            }

            .player-container {
                border-radius: 0;
                max-width: 100%;
            }

            .video-info {
                border-radius: 0;
            }

            h1 {
                font-size: 20px;
            }

            .shortcuts-container {
                display: none;
            }

            /* Improve fullscreen on mobile */
            .plyr--fullscreen-active video {
                height: 100% !important;
            }

            /* Force landscape orientation in fullscreen */
            video::-webkit-media-controls {
                display: flex !important;
                opacity: 1 !important;
            }
        }

        /* Fullscreen improvements */
        .plyr--fullscreen-active {
            background: #000;
            height: 100% !important;
            width: 100% !important;
            max-width: none !important;
            max-height: none !important;
            margin: 0 !important;
            padding: 0 !important;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Tooltip styles */
        .tooltip {
            position: relative;
            display: inline-block;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 120px;
            background-color: rgba(0, 0, 0, 0.8);
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -60px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        /* Premium badge */
        .badge {
            font-size: 12px;
            padding: 5px 8px;
            border-radius: 4px;
            margin-left: 10px;
            vertical-align: middle;
        }

        /* Resume notification */
        .resume-notification {
            position: absolute;
            bottom: 70px;
            left: 0;
            right: 0;
            display: flex;
            justify-content: center;
            z-index: 5;
            animation: fadeInUp 0.5s ease;
        }

        .resume-notification-content {
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            display: flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
            max-width: 90%;
        }

        .resume-notification i {
            color: #e50914;
        }

        .resume-close {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            padding: 0;
            margin-left: 10px;
            font-size: 14px;
        }

        .resume-close:hover {
            color: white;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Resume Playback Modal */
        .resume-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.85);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .resume-modal.active {
            opacity: 1;
            visibility: visible;
        }

        .resume-modal-content {
            background-color: #222;
            border-radius: 8px;
            max-width: 90%;
            width: 500px;
            padding: 0;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
            overflow: hidden;
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }

        .resume-modal.active .resume-modal-content {
            transform: scale(1);
        }

        .resume-modal-header {
            background-color: #333;
            padding: 15px 20px;
            border-bottom: 1px solid #444;
        }

        .resume-modal-header h3 {
            margin: 0;
            font-size: 18px;
            color: white;
        }

        .resume-modal-body {
            padding: 20px;
            color: #ddd;
        }

        .resume-modal-footer {
            display: flex;
            padding: 15px 20px;
            border-top: 1px solid #444;
            gap: 10px;
        }

        .resume-modal-btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .resume-modal-btn-primary {
            background-color: #e50914;
            color: white;
        }

        .resume-modal-btn-primary:hover {
            background-color: #f6121d;
        }

        .resume-modal-btn-secondary {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .resume-modal-btn-secondary:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .resume-time {
            font-weight: bold;
            color: #e50914;
        }

        /* Improved loading overlay */
        .loading-overlay {
            background-color: rgba(0, 0, 0, 0.9);
        }

        .spinner {
            width: 60px;
            height: 60px;
            border-width: 6px;
        }
    </style>
</head>
<body class="player-page">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="<?php echo SITE_URL; ?>">
                <span class="text-danger fw-bold">BANGLA</span><span class="text-light">MOBI</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>/movies.php">Movies</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>/tvshows.php">TV Shows</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <div class="content-wrapper">
        <div class="container">
            <a href="<?php echo $referrer; ?>" class="btn-back">
                <i class="fas fa-arrow-left"></i> <?php echo $back_button_text; ?>
            </a>

            <div class="player-container">
                <div class="video-wrapper">
                    <!-- Loading overlay -->
                    <div class="loading-overlay" id="loading-overlay">
                        <div class="spinner"></div>
                        <div class="loading-text">Loading video...</div>
                        <div class="loading-progress">
                            <div class="loading-progress-bar" id="loading-progress-bar"></div>
                        </div>
                    </div>

                    <!-- Quality selector -->
                    <div class="quality-selector" id="quality-selector" style="display: none;">
                        <button class="quality-btn active" data-quality="auto">AUTO</button>
                        <button class="quality-btn" data-quality="1080">1080p</button>
                        <button class="quality-btn" data-quality="720">720p</button>
                        <button class="quality-btn" data-quality="480">480p</button>
                        <button class="quality-btn" data-quality="360">360p</button>
                    </div>

                    <!-- Plyr.js Player -->
                    <video id="player" playsinline controls crossorigin data-poster="<?php echo $poster; ?>" preload="metadata">
                        <source src="<?php echo $video_url; ?>" type="<?php echo $video_type; ?>" size="720" />
                        <!-- Fallback for browsers that don't support the video element -->
                        <a href="<?php echo $video_url; ?>" download>Download</a>
                    </video>
                </div>

                <div class="video-info">
                    <h1>
                        <?php echo $video_title; ?>
                        <?php if($is_premium): ?>
                        <span class="badge bg-warning text-dark">PREMIUM</span>
                        <?php endif; ?>
                    </h1>

                    <div class="video-meta">
                        <div class="video-meta-item">
                            <i class="fas fa-film"></i>
                            <span><?php echo $quality; ?></span>
                        </div>
                        <div class="video-meta-item">
                            <i class="fas fa-language"></i>
                            <span><?php echo $language; ?></span>
                        </div>
                        <div class="video-meta-item">
                            <i class="fas fa-server"></i>
                            <span><?php echo $source; ?></span>
                        </div>
                        <div class="video-meta-item" id="video-size">
                            <i class="fas fa-hdd"></i>
                            <span>Calculating size...</span>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button id="restart-player" class="action-btn">
                            <i class="fas fa-redo"></i> Restart Player
                        </button>
                        <button id="copy-url" class="action-btn" data-url="<?php echo SITE_URL; ?>/plyr_player_enhanced.php?url=<?php echo urlencode($video_url); ?>&title=<?php echo urlencode($video_title); ?>">
                            <i class="fas fa-copy"></i> Copy Player URL
                        </button>
                        <a href="<?php echo $video_url; ?>" class="action-btn primary" download target="_blank">
                            <i class="fas fa-download"></i> Download Video
                        </a>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Playback Tips:</strong> If the video stutters, try pausing for a few seconds to allow buffering. You can also try a lower quality setting if available.
                    </div>

                    <div class="shortcuts-container">
                        <div class="shortcuts-title">
                            <i class="fas fa-keyboard"></i> Keyboard Shortcuts
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="shortcut-item">
                                    <span class="shortcut-key">Space</span>
                                    <span class="shortcut-description">Play/Pause</span>
                                </div>
                                <div class="shortcut-item">
                                    <span class="shortcut-key">M</span>
                                    <span class="shortcut-description">Mute/Unmute</span>
                                </div>
                                <div class="shortcut-item">
                                    <span class="shortcut-key">F</span>
                                    <span class="shortcut-description">Fullscreen</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="shortcut-item">
                                    <span class="shortcut-key">→</span>
                                    <span class="shortcut-description">১০ সেকেন্ড সামনে</span>
                                </div>
                                <div class="shortcut-item">
                                    <span class="shortcut-key">←</span>
                                    <span class="shortcut-description">১০ সেকেন্ড পিছনে</span>
                                </div>
                                <div class="shortcut-item">
                                    <span class="shortcut-key">J</span>
                                    <span class="shortcut-description">১০ সেকেন্ড পিছনে</span>
                                </div>
                                <div class="shortcut-item">
                                    <span class="shortcut-key">L</span>
                                    <span class="shortcut-description">১০ সেকেন্ড সামনে</span>
                                </div>
                                <div class="shortcut-item">
                                    <span class="shortcut-key">↑</span>
                                    <span class="shortcut-description">Volume Up</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="shortcut-item">
                                    <span class="shortcut-key">↓</span>
                                    <span class="shortcut-description">Volume Down</span>
                                </div>
                                <div class="shortcut-item">
                                    <span class="shortcut-key">></span>
                                    <span class="shortcut-description">Increase Speed</span>
                                </div>
                                <div class="shortcut-item">
                                    <span class="shortcut-key"><</span>
                                    <span class="shortcut-description">Decrease Speed</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </main>

    <!-- Resume Playback Modal -->
    <div class="resume-modal" id="resume-modal">
        <div class="resume-modal-content">
            <div class="resume-modal-header">
                <h3><i class="fas fa-history"></i> প্লেব্যাক পুনরায় শুরু করুন</h3>
            </div>
            <div class="resume-modal-body">
                <p>আপনি আগে এই ভিডিওটি <span class="resume-time" id="resume-time">00:00</span> পর্যন্ত দেখেছেন। আপনি কি সেখান থেকে দেখা চালিয়ে যেতে চান?</p>
            </div>
            <div class="resume-modal-footer">
                <button id="resume-from-beginning" class="resume-modal-btn resume-modal-btn-secondary">শুরু থেকে দেখুন</button>
                <button id="resume-from-last" class="resume-modal-btn resume-modal-btn-primary">আগের অবস্থান থেকে দেখুন</button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- HLS.js for HLS support -->
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>

    <!-- Plyr.js -->
    <script src="https://cdn.plyr.io/3.7.8/plyr.polyfilled.js"></script>

    <!-- Owl Carousel (if needed) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>

    <!-- Custom JS for the player -->
    <script>
        // Add a class to the body to indicate this is the player page
        document.body.classList.add('player-page');

        document.addEventListener('DOMContentLoaded', function() {
            const videoElement = document.getElementById('player');
            const loadingOverlay = document.getElementById('loading-overlay');
            const loadingProgressBar = document.getElementById('loading-progress-bar');
            const qualitySelector = document.getElementById('quality-selector');
            const videoSizeElement = document.getElementById('video-size');
            const videoUrl = '<?php echo $video_url; ?>';
            const cacheKey = '<?php echo $cache_key; ?>';

            // Check if video is cached
            const cachedTime = localStorage.getItem('video_time_' + cacheKey);
            const cachedSize = localStorage.getItem('video_size_' + cacheKey);
            const resumeModal = document.getElementById('resume-modal');
            const resumeTimeElement = document.getElementById('resume-time');
            const resumeFromBeginningBtn = document.getElementById('resume-from-beginning');
            const resumeFromLastBtn = document.getElementById('resume-from-last');
            let shouldAutoPlay = false;

            if (cachedSize) {
                videoSizeElement.innerHTML = '<i class="fas fa-hdd"></i> <span>' + cachedSize + '</span>';
            }

            // Initialize Plyr with advanced options
            const player = new Plyr(videoElement, {
                controls: [
                    'play-large', 'play', 'rewind', 'fast-forward', 'progress', 'current-time', 'duration',
                    'mute', 'volume', 'captions', 'settings', 'pip', 'airplay', 'fullscreen'
                ],
                settings: ['captions', 'quality', 'speed', 'loop'],
                seekTime: 10,
                keyboard: { focused: true, global: true },
                tooltips: { controls: true, seek: true },
                captions: { active: true, language: 'auto', update: true },
                speed: { selected: 1, options: [0.5, 0.75, 1, 1.25, 1.5, 1.75, 2] },
                quality: {
                    default: 720,
                    options: [360, 480, 720, 1080],
                    forced: true,
                    onChange: null,
                },
                // Improved buffering settings
                buffer: {
                    fastSeek: true
                },
                previewThumbnails: { enabled: false },
                blankVideo: 'data:video/mp4;base64,AAAAHGZ0eXBpc29tAAACAGlzb21pc28yYXZjMW1wNDEAAAAIZnJlZQAAAFttZGF0AAAAMmWIhD///8PAnFAAFPf3333331111111111111111111111111111111111111111114TTEtTKmEIZU4Q11AUXghBQ==',
                autoplay: false,
                clickToPlay: true,
                hideControls: true,
                resetOnEnd: false,
                disableContextMenu: false,
                loadSprite: true,
                iconUrl: 'https://cdn.plyr.io/3.7.8/plyr.svg',
                iconPrefix: 'plyr',
                debug: false,
            });

            // Function to format bytes to human-readable size
            function formatBytes(bytes, decimals = 2) {
                if (bytes === 0) return '0 Bytes';

                const k = 1024;
                const dm = decimals < 0 ? 0 : decimals;
                const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

                const i = Math.floor(Math.log(bytes) / Math.log(k));

                return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
            }

            // Function to get video file size
            function getVideoSize() {
                if (cachedSize) return;

                const xhr = new XMLHttpRequest();
                xhr.open('HEAD', videoUrl, true);
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        if (xhr.status === 200) {
                            const size = xhr.getResponseHeader('Content-Length');
                            if (size) {
                                const formattedSize = formatBytes(size);
                                videoSizeElement.innerHTML = '<i class="fas fa-hdd"></i> <span>' + formattedSize + '</span>';
                                localStorage.setItem('video_size_' + cacheKey, formattedSize);
                            } else {
                                videoSizeElement.innerHTML = '<i class="fas fa-hdd"></i> <span>Unknown size</span>';
                            }
                        } else {
                            videoSizeElement.innerHTML = '<i class="fas fa-hdd"></i> <span>Unknown size</span>';
                        }
                    }
                };
                xhr.send();
            }

            // Get video size
            getVideoSize();

            // HLS support
            function setupHLS() {
                // Check if the URL is an HLS stream (.m3u8)
                if (videoUrl.includes('.m3u8') && Hls.isSupported()) {
                    const hls = new Hls({
                        maxBufferLength: 90,                   // Increased buffer length for smoother playback
                        maxMaxBufferLength: 180,               // Increased max buffer length
                        maxBufferSize: 120 * 1000 * 1000,      // Increased to 120MB for better buffering
                        maxBufferHole: 0.5,
                        lowLatencyMode: false,
                        startLevel: -1,                        // Auto quality selection
                        abrEwmaDefaultEstimate: 1000000,       // Start with a higher bandwidth estimate (1Mbps)
                        abrBandWidthFactor: 0.95,              // Be slightly conservative with ABR
                        abrBandWidthUpFactor: 0.7,             // Be more conservative when switching up
                        abrMaxWithRealBitrate: true,           // Use real bitrate for ABR calculations
                        manifestLoadingTimeOut: 15000,         // Increased timeout
                        manifestLoadingMaxRetry: 6,            // More retries
                        manifestLoadingRetryDelay: 1000,       // Start retry delay
                        manifestLoadingMaxRetryTimeout: 8000,  // Max retry delay
                        levelLoadingTimeOut: 15000,            // Increased timeout
                        levelLoadingMaxRetry: 6,               // More retries
                        fragLoadingTimeOut: 30000,             // Increased timeout for fragment loading
                        fragLoadingMaxRetry: 8                 // More retries for fragments
                    });

                    hls.loadSource(videoUrl);
                    hls.attachMedia(videoElement);

                    // Show quality selector for HLS
                    qualitySelector.style.display = 'flex';

                    // Handle quality selection
                    document.querySelectorAll('.quality-btn').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const quality = this.getAttribute('data-quality');

                            // Remove active class from all buttons
                            document.querySelectorAll('.quality-btn').forEach(b => {
                                b.classList.remove('active');
                            });

                            // Add active class to clicked button
                            this.classList.add('active');

                            if (quality === 'auto') {
                                hls.currentLevel = -1; // Auto
                            } else {
                                // Find the level that matches the selected quality
                                const levels = hls.levels;
                                for (let i = 0; i < levels.length; i++) {
                                    if (levels[i].height <= parseInt(quality)) {
                                        hls.currentLevel = i;
                                        break;
                                    }
                                }
                            }
                        });
                    });

                    // Handle HLS events
                    hls.on(Hls.Events.MANIFEST_PARSED, function(event, data) {
                        console.log('Manifest parsed, found ' + data.levels.length + ' quality levels');

                        // Update quality selector with available qualities
                        const qualityBtns = document.querySelectorAll('.quality-btn:not([data-quality="auto"])');
                        qualityBtns.forEach(btn => {
                            btn.style.display = 'none';
                        });

                        data.levels.forEach(level => {
                            const qualityBtn = document.querySelector(`.quality-btn[data-quality="${level.height}"]`);
                            if (qualityBtn) {
                                qualityBtn.style.display = 'block';
                            }
                        });
                    });

                    hls.on(Hls.Events.ERROR, function(event, data) {
                        if (data.fatal) {
                            switch(data.type) {
                                case Hls.ErrorTypes.NETWORK_ERROR:
                                    console.log('Fatal network error encountered, trying to recover');
                                    hls.startLoad();
                                    break;
                                case Hls.ErrorTypes.MEDIA_ERROR:
                                    console.log('Fatal media error encountered, trying to recover');
                                    hls.recoverMediaError();
                                    break;
                                default:
                                    console.log('Fatal error, cannot recover');
                                    hls.destroy();
                                    break;
                            }
                        }
                    });
                }
            }

            // Setup HLS if needed
            setupHLS();

            // Preload video metadata for faster loading
            videoElement.preload = 'metadata';

            // Add keyboard shortcut for Escape key to close resume modal
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && resumeModal.classList.contains('active')) {
                    resumeModal.classList.remove('active');
                    player.play();
                }
            });

            // Optimize video loading
            if ('connection' in navigator) {
                if (navigator.connection.saveData) {
                    // If save data mode is enabled, use lower quality
                    videoElement.preload = 'metadata';
                } else if (navigator.connection.effectiveType.includes('4g')) {
                    // If on fast connection, preload more
                    videoElement.preload = 'auto';
                    shouldAutoPlay = true;
                }
            }

            // Handle loading progress
            let loadingProgress = 0;
            const loadingInterval = setInterval(() => {
                loadingProgress += 5;
                if (loadingProgress > 100) {
                    loadingProgress = 100;
                    clearInterval(loadingInterval);
                }
                loadingProgressBar.style.width = loadingProgress + '%';
            }, 200);

            // Function to format time in minutes and seconds
            function formatTime(seconds) {
                const minutes = Math.floor(seconds / 60);
                const remainingSeconds = Math.floor(seconds % 60);
                return minutes + ':' + (remainingSeconds < 10 ? '0' : '') + remainingSeconds;
            }

            // Function to show resume modal
            function showResumeModal(time) {
                if (time > 30) { // Only show if more than 30 seconds watched
                    resumeTimeElement.textContent = formatTime(time);
                    resumeModal.classList.add('active');

                    // Pause video while modal is showing
                    videoElement.pause();
                }
            }

            // Resume from beginning button
            resumeFromBeginningBtn.addEventListener('click', function() {
                videoElement.currentTime = 0;
                resumeModal.classList.remove('active');
                player.play();
            });

            // Resume from last position button
            resumeFromLastBtn.addEventListener('click', function() {
                if (cachedTime) {
                    const time = parseFloat(cachedTime);
                    if (time > 0 && time < videoElement.duration - 10) {
                        videoElement.currentTime = time;
                    }
                }
                resumeModal.classList.remove('active');
                player.play();
            });

            // Hide loading overlay when video can play
            videoElement.addEventListener('canplay', function() {
                loadingOverlay.style.display = 'none';
                clearInterval(loadingInterval);

                // Check if we have a cached time and show resume modal
                if (cachedTime && !shouldAutoPlay) {
                    const time = parseFloat(cachedTime);
                    if (time > 0 && time < videoElement.duration - 10) {
                        // Show resume modal
                        showResumeModal(time);
                    }
                }

                // If autoplay is enabled, start playing
                if (shouldAutoPlay) {
                    player.play();
                }
            });

            // Show loading overlay if video stalls
            videoElement.addEventListener('waiting', function() {
                loadingOverlay.style.display = 'flex';
                loadingProgress = 0;
            });

            // Hide loading overlay when video plays
            videoElement.addEventListener('playing', function() {
                loadingOverlay.style.display = 'none';
            });

            // Save playback position periodically (more frequently)
            setInterval(() => {
                if (videoElement.currentTime > 0) {
                    localStorage.setItem('video_time_' + cacheKey, videoElement.currentTime.toString());
                }
            }, 3000);

            // Save position on pause
            videoElement.addEventListener('pause', function() {
                if (videoElement.currentTime > 0) {
                    localStorage.setItem('video_time_' + cacheKey, videoElement.currentTime.toString());
                }
            });

            // Save position before page unload
            window.addEventListener('beforeunload', function() {
                if (videoElement.currentTime > 0) {
                    localStorage.setItem('video_time_' + cacheKey, videoElement.currentTime.toString());
                }
            });

            // Restart player button
            document.getElementById('restart-player').addEventListener('click', function() {
                // Reload the video
                videoElement.load();
                player.restart();

                // Show loading overlay
                loadingOverlay.style.display = 'flex';
                loadingProgress = 0;
            });

            // Copy URL button
            document.getElementById('copy-url').addEventListener('click', function() {
                const url = this.getAttribute('data-url');
                navigator.clipboard.writeText(url).then(() => {
                    this.innerHTML = '<i class="fas fa-check"></i> Copied!';
                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-copy"></i> Copy Player URL';
                    }, 2000);
                });
            });

            // Optimize video loading based on network conditions
            function optimizeVideoLoading() {
                // Check if the video is already playing or if we're on a fast connection
                if (!videoElement.paused || (window.navigator.connection && window.navigator.connection.effectiveType.includes('4g'))) {
                    // On fast connections or if already playing, preload more aggressively
                    videoElement.preload = 'auto';

                    // Use fetch() to prime the browser cache
                    if ('fetch' in window) {
                        fetch(videoUrl, { method: 'HEAD', mode: 'no-cors' })
                            .catch(() => {}); // Ignore errors, this is just to prime the cache
                    }

                    // If the video is an MP4, try to preload a range to speed up initial loading
                    if (videoUrl.includes('.mp4') && 'fetch' in window) {
                        fetch(videoUrl, {
                            method: 'GET',
                            headers: { Range: 'bytes=0-1000000' }, // Request first ~1MB
                            mode: 'no-cors'
                        }).catch(() => {});
                    }
                } else {
                    // On slower connections, be more conservative
                    videoElement.preload = 'metadata';
                }
            }

            // Call optimize function after a short delay
            setTimeout(optimizeVideoLoading, 1000);

            // Add error handling
            videoElement.addEventListener('error', function(e) {
                console.error('Video error:', e);
                loadingOverlay.style.display = 'flex';
                document.querySelector('.loading-text').textContent = 'Error loading video. Retrying...';

                // Try to recover
                setTimeout(() => {
                    videoElement.load();
                }, 3000);
            });

            // Add YouTube-style J and L keyboard shortcuts for seeking
            document.addEventListener('keydown', function(e) {
                // Only if video player is in focus or we're in fullscreen
                if (document.activeElement === videoElement || document.fullscreenElement) {
                    // J key - seek backward 10 seconds
                    if (e.key === 'j' || e.key === 'J') {
                        player.rewind();
                    }

                    // L key - seek forward 10 seconds
                    if (e.key === 'l' || e.key === 'L') {
                        player.forward();
                    }
                }
            });

            // Optimize for mobile
            if (window.matchMedia('(max-width: 768px)').matches) {
                player.config.controls = ['play-large', 'play', 'rewind', 'fast-forward', 'progress', 'current-time', 'mute', 'volume', 'fullscreen'];
            }

            // Handle fullscreen changes for better mobile experience
            player.on('enterfullscreen', function() {
                // Try to force landscape orientation on mobile
                if (screen.orientation && screen.orientation.lock) {
                    screen.orientation.lock('landscape').catch(function(error) {
                        // Silently fail if orientation locking is not supported or permission denied
                        console.log('Orientation lock failed: ', error);
                    });
                }

                // Add a class to the body for fullscreen styling
                document.body.classList.add('plyr-fullscreen-active');

                // Make sure controls are visible in fullscreen
                setTimeout(function() {
                    player.toggleControls(true);
                }, 100);
            });

            // Handle exit from fullscreen
            player.on('exitfullscreen', function() {
                // Remove the fullscreen class
                document.body.classList.remove('plyr-fullscreen-active');

                // Release orientation lock if it was set
                if (screen.orientation && screen.orientation.unlock) {
                    screen.orientation.unlock();
                }
            });
        });
    </script>

    <!-- Simple Footer -->
    <footer class="footer py-3 mt-4">
        <div class="container text-center">
            <p class="text-muted mb-0">&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
