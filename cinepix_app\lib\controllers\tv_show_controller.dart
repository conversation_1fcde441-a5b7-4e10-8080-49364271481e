import 'package:get/get.dart';
import 'package:cinepix_app/models/tv_show.dart';
import 'package:cinepix_app/models/episode.dart';
import 'package:cinepix_app/models/download_link.dart';
import 'package:cinepix_app/services/api_service.dart';

class TvShowController extends GetxController {
  final ApiService _apiService = ApiService();

  // TV shows lists
  final RxList<TvShow> featuredTvShows = <TvShow>[].obs;
  final RxList<TvShow> latestTvShows = <TvShow>[].obs;
  final RxList<TvShow> popularTvShows = <TvShow>[].obs;
  final RxList<TvShow> topRatedTvShows = <TvShow>[].obs;
  final RxList<TvShow> categoryTvShows = <TvShow>[].obs;

  // Current TV show details
  final Rx<TvShow?> currentTvShow = Rx<TvShow?>(null);
  final RxList<dynamic> seasons = <dynamic>[].obs;
  final RxList<TvShow> relatedTvShows = <TvShow>[].obs;

  // Current season and episodes
  final RxInt currentSeason = 1.obs;
  final RxList<Episode> episodes = <Episode>[].obs;
  final Rx<Episode?> currentEpisode = Rx<Episode?>(null);
  final RxList<DownloadLink> downloadLinks = <DownloadLink>[].obs;

  // Loading states
  final RxBool isLoadingFeatured = false.obs;
  final RxBool isLoadingLatest = false.obs;
  final RxBool isLoadingPopular = false.obs;
  final RxBool isLoadingTopRated = false.obs;
  final RxBool isLoadingCategory = false.obs;
  final RxBool isLoadingDetails = false.obs;
  final RxBool isLoadingEpisodes = false.obs;
  final RxBool isLoadingEpisodeDetails = false.obs;

  // Pagination
  final RxInt currentPage = 1.obs;
  final RxInt totalPages = 1.obs;
  final RxBool hasMoreTvShows = true.obs;

  // Error handling
  final RxString errorMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();
    loadInitialData();
  }

  // Load initial data
  Future<void> loadInitialData() async {
    await Future.wait([
      loadFeaturedTvShows(),
      loadLatestTvShows(),
      loadPopularTvShows(),
      loadTopRatedTvShows(),
    ]);
  }

  // Load featured TV shows
  Future<void> loadFeaturedTvShows() async {
    isLoadingFeatured.value = true;
    errorMessage.value = '';

    try {
      final tvShows = await _apiService.getTvShows(featured: true, limit: 10);
      featuredTvShows.value = tvShows;
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoadingFeatured.value = false;
    }
  }

  // Load latest TV shows
  Future<void> loadLatestTvShows() async {
    isLoadingLatest.value = true;
    errorMessage.value = '';

    try {
      final tvShows = await _apiService.getTvShows(page: 1, limit: 20);
      latestTvShows.value = tvShows;
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoadingLatest.value = false;
    }
  }

  // Load popular TV shows
  Future<void> loadPopularTvShows() async {
    isLoadingPopular.value = true;
    errorMessage.value = '';

    try {
      // Assuming popular TV shows are sorted by rating on the server
      final tvShows = await _apiService.getTvShows(page: 1, limit: 20);
      popularTvShows.value = tvShows;
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoadingPopular.value = false;
    }
  }

  // Load top rated TV shows
  Future<void> loadTopRatedTvShows() async {
    isLoadingTopRated.value = true;
    errorMessage.value = '';

    try {
      // Get TV shows sorted by rating
      final tvShows =
          await _apiService.getTvShows(page: 1, limit: 10, sortBy: 'rating');

      // Sort by rating in descending order (highest first)
      tvShows.sort((a, b) => b.rating.compareTo(a.rating));

      // Take only TV shows with rating >= 7.0
      final highRatedTvShows =
          tvShows.where((tvShow) => tvShow.rating >= 7.0).toList();

      topRatedTvShows.value = highRatedTvShows;
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoadingTopRated.value = false;
    }
  }

  // Load TV shows by category
  Future<void> loadTvShowsByCategory(int categoryId) async {
    isLoadingCategory.value = true;
    errorMessage.value = '';
    currentPage.value = 1;

    try {
      final tvShows = await _apiService.getTvShows(
        page: currentPage.value,
        limit: 20,
        categoryId: categoryId,
      );

      categoryTvShows.value = tvShows;
      hasMoreTvShows.value = tvShows.length >= 20;
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoadingCategory.value = false;
    }
  }

  // Load more TV shows by category
  Future<void> loadMoreTvShowsByCategory(int categoryId) async {
    if (!hasMoreTvShows.value || isLoadingCategory.value) return;

    isLoadingCategory.value = true;
    errorMessage.value = '';

    try {
      currentPage.value++;

      final tvShows = await _apiService.getTvShows(
        page: currentPage.value,
        limit: 20,
        categoryId: categoryId,
      );

      if (tvShows.isNotEmpty) {
        categoryTvShows.addAll(tvShows);
      }

      hasMoreTvShows.value = tvShows.length >= 20;
    } catch (e) {
      errorMessage.value = e.toString();
      currentPage.value--;
    } finally {
      isLoadingCategory.value = false;
    }
  }

  // Load TV show details
  Future<void> loadTvShowDetails(int tvShowId) async {
    isLoadingDetails.value = true;
    errorMessage.value = '';

    try {
      final details = await _apiService.getTvShowDetails(tvShowId);

      currentTvShow.value = details['tvshow'];
      seasons.value = details['seasons'];
      relatedTvShows.value = details['related_tvshows'];

      // Load first season episodes if available
      if (seasons.isNotEmpty) {
        currentSeason.value = seasons[0]['season_number'] ?? 1;
        await loadEpisodes(tvShowId, currentSeason.value);
      }
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoadingDetails.value = false;
    }
  }

  // Load episodes for a season
  Future<void> loadEpisodes(int tvShowId, int seasonNumber) async {
    isLoadingEpisodes.value = true;
    errorMessage.value = '';

    // Update current season before loading episodes
    // This will trigger UI updates for any widgets observing this value
    currentSeason.value = seasonNumber;

    try {
      final episodesList =
          await _apiService.getTvShowEpisodes(tvShowId, seasonNumber);
      episodes.value = episodesList;
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoadingEpisodes.value = false;
    }
  }

  // Load episode details
  Future<void> loadEpisodeDetails(Episode episode) async {
    isLoadingEpisodeDetails.value = true;
    errorMessage.value = '';
    currentEpisode.value = episode;

    try {
      final links = await _apiService.getEpisodeDownloadLinks(episode.id);
      downloadLinks.value = links;
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoadingEpisodeDetails.value = false;
    }
  }

  // Search TV shows
  Future<List<TvShow>> searchTvShows(String query) async {
    if (query.isEmpty) return [];

    try {
      final results = await _apiService.search(query);
      final tvShowsList = results['tvshows'] ?? [];
      return tvShowsList
          .map((tvShowJson) => TvShow.fromJson(tvShowJson))
          .toList();
    } catch (e) {
      errorMessage.value = e.toString();
      return [];
    }
  }

  // Get download links for current episode
  List<DownloadLink> getDownloadLinks() {
    return downloadLinks;
  }

  // Clear current TV show
  void clearCurrentTvShow() {
    currentTvShow.value = null;
    seasons.clear();
    relatedTvShows.clear();
    episodes.clear();
    currentEpisode.value = null;
    downloadLinks.clear();
  }

  // Get next episode
  Episode? getNextEpisode() {
    if (currentEpisode.value == null || episodes.isEmpty) {
      return null;
    }

    final currentEpisodeIndex = episodes
        .indexWhere((episode) => episode.id == currentEpisode.value!.id);

    if (currentEpisodeIndex == -1 ||
        currentEpisodeIndex >= episodes.length - 1) {
      // Current episode not found or is the last episode in the current season
      return null;
    }

    // Return the next episode in the current season
    return episodes[currentEpisodeIndex + 1];
  }

  // Load next episode details
  Future<DownloadLink?> loadNextEpisodeDetails() async {
    final nextEpisode = getNextEpisode();
    if (nextEpisode == null) {
      return null;
    }

    await loadEpisodeDetails(nextEpisode);

    if (downloadLinks.isEmpty) {
      return null;
    }

    return downloadLinks.first;
  }

  // Load episode links directly by episode ID
  Future<List<DownloadLink>> loadEpisodeLinks(int episodeId) async {
    isLoadingEpisodeDetails.value = true;
    errorMessage.value = '';

    try {
      // Find the episode in the current episodes list
      final episode = episodes.firstWhere(
        (e) => e.id == episodeId,
        orElse: () => Episode(
          id: episodeId,
          tvshowId: currentTvShow.value?.id ?? 0,
          title: '',
          description: '',
          seasonNumber: currentSeason.value,
          episodeNumber: 0,
          duration: 0,
          thumbnail: '',
          releaseDate: '',
          isPremium: false,
        ),
      );

      // Set as current episode
      currentEpisode.value = episode;

      // Get download links
      final links = await _apiService.getEpisodeDownloadLinks(episodeId);
      downloadLinks.value = links;
      return links;
    } catch (e) {
      errorMessage.value = e.toString();
      return [];
    } finally {
      isLoadingEpisodeDetails.value = false;
    }
  }
}
