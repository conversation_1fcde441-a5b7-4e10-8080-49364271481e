<?php
$page_title = 'শেয়ার লিংক সিকিউরিটি';
$current_page = 'share_security.php';

require_once '../includes/config.php';
require_once '../includes/share_link_security.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

$security = getShareLinkSecurity();

// Handle actions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'block_ip':
                $ip = mysqli_real_escape_string($conn, $_POST['ip_address']);
                $reason = mysqli_real_escape_string($conn, $_POST['reason']);
                $duration = (int)$_POST['duration'];
                
                if ($security->blockIP($ip, $reason, $duration)) {
                    $success_message = "IP ব্লক করা হয়েছে!";
                } else {
                    $error_message = "IP ব্লক করতে সমস্যা হয়েছে।";
                }
                break;
                
            case 'unblock_ip':
                $ip = mysqli_real_escape_string($conn, $_POST['ip_address']);
                
                $query = "UPDATE blocked_ips SET is_active = 0 WHERE ip_address = ?";
                $stmt = mysqli_prepare($conn, $query);
                mysqli_stmt_bind_param($stmt, 's', $ip);
                
                if (mysqli_stmt_execute($stmt)) {
                    $success_message = "IP আনব্লক করা হয়েছে!";
                } else {
                    $error_message = "IP আনব্লক করতে সমস্যা হয়েছে।";
                }
                break;
                
            case 'cleanup_logs':
                $days = (int)$_POST['days'];
                $security->cleanupOldLogs($days);
                $success_message = "পুরানো লগ পরিষ্কার করা হয়েছে!";
                break;
        }
    }
}

// Get security statistics
$stats_query = "SELECT
    (SELECT COUNT(*) FROM share_security_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as events_24h,
    (SELECT COUNT(*) FROM share_security_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)) as events_7d,
    (SELECT COUNT(*) FROM blocked_ips WHERE is_active = 1) as blocked_ips,
    (SELECT COUNT(DISTINCT ip_address) FROM shared_link_access_logs WHERE access_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as unique_visitors_24h";
$stats_result = mysqli_query($conn, $stats_query);

// Initialize default stats
$stats = [
    'events_24h' => 0,
    'events_7d' => 0,
    'blocked_ips' => 0,
    'unique_visitors_24h' => 0
];

if ($stats_result) {
    $stats = mysqli_fetch_assoc($stats_result);
}

// Get recent security events
$events_query = "SELECT * FROM share_security_logs ORDER BY created_at DESC LIMIT 50";
$events_result = mysqli_query($conn, $events_query);
if (!$events_result) {
    $events_result = false;
}

// Get blocked IPs
$blocked_query = "SELECT * FROM blocked_ips WHERE is_active = 1 ORDER BY blocked_at DESC";
$blocked_result = mysqli_query($conn, $blocked_query);
if (!$blocked_result) {
    $blocked_result = false;
}

// Get top accessing IPs
$top_ips_query = "SELECT ip_address, COUNT(*) as access_count,
                         MAX(access_time) as last_access,
                         COUNT(DISTINCT shared_link_id) as unique_links
                  FROM shared_link_access_logs
                  WHERE access_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                  GROUP BY ip_address
                  ORDER BY access_count DESC
                  LIMIT 20";
$top_ips_result = mysqli_query($conn, $top_ips_query);
if (!$top_ips_result) {
    $top_ips_result = false;
}

require_once 'includes/header.php';
require_once 'includes/sidebar.php';
?>

<div class="main-content" style="margin-left: 250px; width: calc(100% - 250px); padding: 20px; min-height: 100vh;"">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="page-header">
                    <h1><i class="fas fa-shield-alt"></i> শেয়ার লিংক সিকিউরিটি</h1>
                    <p class="text-muted">সিকিউরিটি ইভেন্ট, ব্লকড IP এবং এক্সেস পর্যবেক্ষণ</p>
                </div>

                <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Security Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo $stats['events_24h']; ?></h4>
                                        <p class="mb-0">২৪ ঘন্টার ইভেন্ট</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo $stats['events_7d']; ?></h4>
                                        <p class="mb-0">৭ দিনের ইভেন্ট</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-calendar-week fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo $stats['blocked_ips']; ?></h4>
                                        <p class="mb-0">ব্লকড IP</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-ban fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo $stats['unique_visitors_24h']; ?></h4>
                                        <p class="mb-0">২ৄ ঘন্টার ভিজিটর</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-ban"></i> IP ব্লক করুন</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="block_ip">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">IP Address</label>
                                                <input type="text" name="ip_address" class="form-control" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Duration (Hours)</label>
                                                <select name="duration" class="form-select">
                                                    <option value="1">১ ঘন্টা</option>
                                                    <option value="24">২৪ ঘন্টা</option>
                                                    <option value="168">৭ দিন</option>
                                                    <option value="">স্থায়ী</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">কারণ</label>
                                        <input type="text" name="reason" class="form-control" required>
                                    </div>
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-ban"></i> ব্লক করুন
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-trash"></i> লগ পরিষ্কার করুন</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="cleanup_logs">
                                    <div class="mb-3">
                                        <label class="form-label">কত দিনের পুরানো লগ মুছবেন?</label>
                                        <select name="days" class="form-select">
                                            <option value="7">৭ দিন</option>
                                            <option value="30" selected>৩০ দিন</option>
                                            <option value="90">৯০ দিন</option>
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-warning" onclick="return confirm('আপনি কি নিশ্চিত?')">
                                        <i class="fas fa-trash"></i> পরিষ্কার করুন
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Security Events -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> সাম্প্রতিক সিকিউরিটি ইভেন্ট</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($events_result && mysqli_num_rows($events_result) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>সময়</th>
                                        <th>ইভেন্ট টাইপ</th>
                                        <th>IP Address</th>
                                        <th>বিবরণ</th>
                                        <th>User Agent</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($event = mysqli_fetch_assoc($events_result)): ?>
                                    <tr>
                                        <td><?php echo date('d/m/Y H:i', strtotime($event['created_at'])); ?></td>
                                        <td>
                                            <?php
                                            $badge_class = 'bg-secondary';
                                            switch ($event['event_type']) {
                                                case 'malicious_token':
                                                    $badge_class = 'bg-danger';
                                                    break;
                                                case 'rate_limit_exceeded':
                                                    $badge_class = 'bg-warning';
                                                    break;
                                                case 'suspicious_activity':
                                                    $badge_class = 'bg-info';
                                                    break;
                                                case 'ip_blocked':
                                                    $badge_class = 'bg-danger';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge <?php echo $badge_class; ?>"><?php echo $event['event_type']; ?></span>
                                        </td>
                                        <td>
                                            <?php echo $event['ip_address']; ?>
                                            <button class="btn btn-sm btn-outline-danger ms-2" onclick="blockIP('<?php echo $event['ip_address']; ?>')">
                                                <i class="fas fa-ban"></i>
                                            </button>
                                        </td>
                                        <td><?php echo htmlspecialchars($event['details']); ?></td>
                                        <td>
                                            <small><?php echo htmlspecialchars(substr($event['user_agent'], 0, 50)); ?>...</small>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-shield-alt fa-3x text-muted mb-3"></i>
                            <h5>কোনো সিকিউরিটি ইভেন্ট নেই</h5>
                            <p class="text-muted">এটি একটি ভালো খবর!</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Blocked IPs -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-ban"></i> ব্লকড IP সমূহ</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($blocked_result && mysqli_num_rows($blocked_result) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>IP Address</th>
                                        <th>কারণ</th>
                                        <th>ব্লক করার সময়</th>
                                        <th>মেয়াদ শেষ</th>
                                        <th>অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($blocked = mysqli_fetch_assoc($blocked_result)): ?>
                                    <tr>
                                        <td><?php echo $blocked['ip_address']; ?></td>
                                        <td><?php echo htmlspecialchars($blocked['reason']); ?></td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($blocked['blocked_at'])); ?></td>
                                        <td>
                                            <?php if ($blocked['expires_at']): ?>
                                                <?php echo date('d/m/Y H:i', strtotime($blocked['expires_at'])); ?>
                                            <?php else: ?>
                                                <span class="badge bg-danger">স্থায়ী</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="unblock_ip">
                                                <input type="hidden" name="ip_address" value="<?php echo $blocked['ip_address']; ?>">
                                                <button type="submit" class="btn btn-sm btn-success" onclick="return confirm('আপনি কি নিশ্চিত?')">
                                                    <i class="fas fa-unlock"></i> আনব্লক
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-unlock fa-3x text-muted mb-3"></i>
                            <h5>কোনো ব্লকড IP নেই</h5>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Top Accessing IPs -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar"></i> সর্বোচ্চ এক্সেসকারী IP (২৪ ঘন্টা)</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($top_ips_result && mysqli_num_rows($top_ips_result) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>IP Address</th>
                                        <th>এক্সেস সংখ্যা</th>
                                        <th>ইউনিক লিংক</th>
                                        <th>শেষ এক্সেস</th>
                                        <th>অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($ip_data = mysqli_fetch_assoc($top_ips_result)): ?>
                                    <tr>
                                        <td><?php echo $ip_data['ip_address']; ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $ip_data['access_count'] > 50 ? 'danger' : ($ip_data['access_count'] > 20 ? 'warning' : 'success'); ?>">
                                                <?php echo $ip_data['access_count']; ?>
                                            </span>
                                        </td>
                                        <td><?php echo $ip_data['unique_links']; ?></td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($ip_data['last_access'])); ?></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-danger" onclick="blockIP('<?php echo $ip_data['ip_address']; ?>')">
                                                <i class="fas fa-ban"></i> ব্লক
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                            <h5>কোনো ডেটা নেই</h5>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function blockIP(ip) {
    if (confirm('আপনি কি ' + ip + ' IP টি ব্লক করতে চান?')) {
        // Fill the block form with the IP
        document.querySelector('input[name="ip_address"]').value = ip;
        document.querySelector('input[name="reason"]').value = 'Suspicious activity detected';
        document.querySelector('input[name="reason"]').focus();
    }
}
</script>

<?php require_once 'includes/footer.php'; ?>
