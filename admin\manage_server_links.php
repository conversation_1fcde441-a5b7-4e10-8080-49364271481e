<?php
// Set page title
$page_title = 'Manage Server Links';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Initialize variables
$success_message = '';
$error_message = '';
$search_domain = '';
$search_results = [];
$total_links = 0;
$download_links_count = 0;
$streaming_links_count = 0;
$episode_links_count = 0;

// Process search form
if (isset($_POST['search_links'])) {
    $search_domain = sanitize($_POST['search_domain']);

    if (empty($search_domain)) {
        $error_message = 'Please enter a domain to search for.';
    } else {
        // Search in download_links table
        $download_query = "SELECT 'download_links' as table_name, dl.id, dl.content_type, dl.content_id, dl.quality,
                          dl.server_name, dl.link_url, dl.is_premium,
                          CASE
                            WHEN dl.content_type = 'movie' THEN m.title
                            WHEN dl.content_type = 'tvshow' THEN t.title
                          END as content_title
                          FROM download_links dl
                          LEFT JOIN movies m ON dl.content_type = 'movie' AND dl.content_id = m.id
                          LEFT JOIN tvshows t ON dl.content_type = 'tvshow' AND dl.content_id = t.id
                          WHERE dl.link_url LIKE '%$search_domain%'
                          ORDER BY dl.content_type, content_title";

        $download_result = mysqli_query($conn, $download_query);
        $download_links_count = mysqli_num_rows($download_result);

        // Search in streaming_links table
        $streaming_query = "SELECT 'streaming_links' as table_name, sl.id, sl.content_type, sl.content_id, sl.quality,
                           sl.server_name, sl.stream_url as link_url, sl.is_premium,
                           CASE
                             WHEN sl.content_type = 'movie' THEN m.title
                             WHEN sl.content_type = 'tvshow' THEN t.title
                           END as content_title
                           FROM streaming_links sl
                           LEFT JOIN movies m ON sl.content_type = 'movie' AND sl.content_id = m.id
                           LEFT JOIN tvshows t ON sl.content_type = 'tvshow' AND sl.content_id = t.id
                           WHERE sl.stream_url LIKE '%$search_domain%'
                           ORDER BY sl.content_type, content_title";

        $streaming_result = mysqli_query($conn, $streaming_query);
        $streaming_links_count = mysqli_num_rows($streaming_result);

        // Search in episode_links table
        $episode_query = "SELECT 'episode_links' as table_name, el.id, 'episode' as content_type, el.episode_id as content_id,
                         el.quality, el.server_name, el.link_url, el.is_premium, el.link_type,
                         CONCAT(t.title, ' - S', e.season_number, 'E', LPAD(e.episode_number, 2, '0'), ': ', e.title) as content_title
                         FROM episode_links el
                         JOIN episodes e ON el.episode_id = e.id
                         JOIN tvshows t ON e.tvshow_id = t.id
                         WHERE el.link_url LIKE '%$search_domain%'
                         ORDER BY t.title, e.season_number, e.episode_number";

        $episode_result = mysqli_query($conn, $episode_query);
        $episode_links_count = mysqli_num_rows($episode_result);

        // Combine results
        $total_links = $download_links_count + $streaming_links_count + $episode_links_count;

        if ($total_links > 0) {
            $success_message = "Found $total_links links containing '$search_domain'.";
        } else {
            $error_message = "No links found containing '$search_domain'.";
        }
    }
}

// Process bulk actions
if (isset($_POST['bulk_action'])) {
    $action = sanitize($_POST['bulk_action']);
    $selected_links = isset($_POST['selected_links']) ? $_POST['selected_links'] : [];
    $new_domain = isset($_POST['new_domain']) ? sanitize($_POST['new_domain']) : '';

    if (empty($selected_links)) {
        $error_message = 'Please select at least one link.';
    } else if ($action == 'update_domain' && empty($new_domain)) {
        $error_message = 'Please enter a new domain.';
    } else {
        $success_count = 0;
        $error_count = 0;

        foreach ($selected_links as $link_info) {
            list($table, $id) = explode(':', $link_info);
            $id = (int)$id;

            if ($table == 'download_links') {
                // Get current link URL
                $get_link_query = "SELECT link_url FROM download_links WHERE id = $id";
                $link_result = mysqli_query($conn, $get_link_query);
                $link_data = mysqli_fetch_assoc($link_result);
                $current_url = $link_data['link_url'];

                if ($action == 'deactivate') {
                    // Add "_inactive" to server_name
                    $update_query = "UPDATE download_links SET server_name = CONCAT(server_name, '_inactive') WHERE id = $id";
                } else if ($action == 'activate') {
                    // Remove "_inactive" from server_name
                    $update_query = "UPDATE download_links SET server_name = REPLACE(server_name, '_inactive', '') WHERE id = $id";
                } else if ($action == 'update_domain') {
                    // Replace domain in URL
                    $old_domain = sanitize($_POST['search_domain']);
                    $new_url = str_replace($old_domain, $new_domain, $current_url);
                    $update_query = "UPDATE download_links SET link_url = '$new_url' WHERE id = $id";
                }
            } else if ($table == 'streaming_links') {
                // Get current stream URL
                $get_link_query = "SELECT stream_url FROM streaming_links WHERE id = $id";
                $link_result = mysqli_query($conn, $get_link_query);
                $link_data = mysqli_fetch_assoc($link_result);
                $current_url = $link_data['stream_url'];

                if ($action == 'deactivate') {
                    // Add "_inactive" to server_name
                    $update_query = "UPDATE streaming_links SET server_name = CONCAT(server_name, '_inactive') WHERE id = $id";
                } else if ($action == 'activate') {
                    // Remove "_inactive" from server_name
                    $update_query = "UPDATE streaming_links SET server_name = REPLACE(server_name, '_inactive', '') WHERE id = $id";
                } else if ($action == 'update_domain') {
                    // Replace domain in URL
                    $old_domain = sanitize($_POST['search_domain']);
                    $new_url = str_replace($old_domain, $new_domain, $current_url);
                    $update_query = "UPDATE streaming_links SET stream_url = '$new_url' WHERE id = $id";
                }
            } else if ($table == 'episode_links') {
                // Get current link URL
                $get_link_query = "SELECT link_url, link_type FROM episode_links WHERE id = $id";
                $link_result = mysqli_query($conn, $get_link_query);
                $link_data = mysqli_fetch_assoc($link_result);
                $current_url = $link_data['link_url'];

                if ($action == 'deactivate') {
                    // Add "_inactive" to server_name
                    $update_query = "UPDATE episode_links SET server_name = CONCAT(server_name, '_inactive') WHERE id = $id";
                } else if ($action == 'activate') {
                    // Remove "_inactive" from server_name
                    $update_query = "UPDATE episode_links SET server_name = REPLACE(server_name, '_inactive', '') WHERE id = $id";
                } else if ($action == 'update_domain') {
                    // Replace domain in URL
                    $old_domain = sanitize($_POST['search_domain']);
                    $new_url = str_replace($old_domain, $new_domain, $current_url);
                    $update_query = "UPDATE episode_links SET link_url = '$new_url' WHERE id = $id";
                }
            }

            if (isset($update_query) && mysqli_query($conn, $update_query)) {
                $success_count++;
            } else {
                $error_count++;
            }
        }

        if ($success_count > 0) {
            $action_text = ($action == 'deactivate') ? 'deactivated' : (($action == 'activate') ? 'activated' : 'updated');
            $success_message = "$success_count links successfully $action_text.";

            if ($error_count > 0) {
                $error_message = "$error_count links could not be updated.";
            }

            // Refresh search results
            $_POST['search_links'] = true;
        } else {
            $error_message = "No links were updated. Please try again.";
        }
    }
}

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title d-flex align-items-center">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>সার্ভার লিংক ম্যানেজমেন্ট</h1>
            </div>

            <div class="topbar-actions">
                <div class="topbar-search">
                    <form action="search.php" method="GET">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="সার্চ করুন..." name="q">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Begin Page Content -->
    <div class="container-fluid">

            <?php if (!empty($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php endif; ?>

            <?php if (!empty($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php endif; ?>

            <!-- Search Form -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">সার্ভার ডোমেইন খুঁজুন</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <div class="row align-items-end">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="search_domain" class="form-label">ডোমেইন নাম</label>
                                    <input type="text" class="form-control" id="search_domain" name="search_domain"
                                           value="<?php echo htmlspecialchars($search_domain); ?>"
                                           placeholder="উদাহরণ: example.com, workers.dev" required>
                                    <div class="form-text">সার্ভার ডোমেইন নাম দিয়ে সার্চ করুন</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <button type="submit" name="search_links" class="btn btn-primary w-100">
                                        <i class="fas fa-search me-1"></i> খুঁজুন
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <?php if (isset($_POST['search_links']) && $total_links > 0): ?>
            <!-- Search Results -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">সার্চ রেজাল্ট (<?php echo $total_links; ?> লিংক)</h6>
                    <div>
                        <span class="badge bg-primary me-2"><?php echo $download_links_count; ?> ডাউনলোড লিংক</span>
                        <span class="badge bg-success me-2"><?php echo $streaming_links_count; ?> স্ট্রিমিং লিংক</span>
                        <span class="badge bg-info"><?php echo $episode_links_count; ?> এপিসোড লিংক</span>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="" id="bulkActionForm">
                        <input type="hidden" name="search_domain" value="<?php echo htmlspecialchars($search_domain); ?>">

                        <!-- Bulk Actions -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <select class="form-select" name="bulk_action" id="bulk_action" required>
                                    <option value="">বাল্ক একশন নির্বাচন করুন</option>
                                    <option value="deactivate">লিংক ইনএক্টিভ করুন</option>
                                    <option value="activate">লিংক এক্টিভ করুন</option>
                                    <option value="update_domain">ডোমেইন আপডেট করুন</option>
                                </select>
                            </div>
                            <div class="col-md-4" id="new_domain_container" style="display: none;">
                                <input type="text" class="form-control" name="new_domain" id="new_domain"
                                       placeholder="নতুন ডোমেইন নাম">
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary" id="apply_action">
                                    <i class="fas fa-check me-1"></i> এপ্লাই করুন
                                </button>
                            </div>
                            <div class="col-md-3 text-end">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="select_all">
                                    <label class="form-check-label" for="select_all">
                                        সব সিলেক্ট করুন
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Results Table -->
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th style="width: 30px;"><i class="fas fa-check-square"></i></th>
                                        <th>কনটেন্ট</th>
                                        <th>টাইপ</th>
                                        <th>কোয়ালিটি</th>
                                        <th>সার্ভার</th>
                                        <th>লিংক</th>
                                        <th>প্রিমিয়াম</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    // Reset result pointers
                                    if ($download_links_count > 0) mysqli_data_seek($download_result, 0);
                                    if ($streaming_links_count > 0) mysqli_data_seek($streaming_result, 0);
                                    if ($episode_links_count > 0) mysqli_data_seek($episode_result, 0);

                                    // Display download links
                                    if ($download_links_count > 0):
                                        while ($link = mysqli_fetch_assoc($download_result)):
                                    ?>
                                    <tr>
                                        <td>
                                            <input class="form-check-input link-checkbox" type="checkbox"
                                                   name="selected_links[]" value="download_links:<?php echo $link['id']; ?>">
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($link['content_title']); ?>
                                            <span class="badge bg-primary"><?php echo $link['content_type']; ?></span>
                                        </td>
                                        <td>Download</td>
                                        <td><?php echo htmlspecialchars($link['quality']); ?></td>
                                        <td>
                                            <?php
                                            $server_name = htmlspecialchars($link['server_name']);
                                            $is_inactive = strpos($server_name, '_inactive') !== false;
                                            if ($is_inactive) {
                                                echo '<span class="text-danger"><i class="fas fa-times-circle me-1"></i>' .
                                                     str_replace('_inactive', '', $server_name) . '</span>';
                                            } else {
                                                echo '<span class="text-success"><i class="fas fa-check-circle me-1"></i>' .
                                                     $server_name . '</span>';
                                            }
                                            ?>
                                        </td>
                                        <td>
                                            <div class="text-truncate" style="max-width: 250px;" title="<?php echo htmlspecialchars($link['link_url']); ?>">
                                                <?php echo htmlspecialchars($link['link_url']); ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($link['is_premium']): ?>
                                            <span class="badge bg-warning">প্রিমিয়াম</span>
                                            <?php else: ?>
                                            <span class="badge bg-secondary">ফ্রি</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php
                                        endwhile;
                                    endif;

                                    // Display streaming links
                                    if ($streaming_links_count > 0):
                                        while ($link = mysqli_fetch_assoc($streaming_result)):
                                    ?>
                                    <tr>
                                        <td>
                                            <input class="form-check-input link-checkbox" type="checkbox"
                                                   name="selected_links[]" value="streaming_links:<?php echo $link['id']; ?>">
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($link['content_title']); ?>
                                            <span class="badge bg-primary"><?php echo $link['content_type']; ?></span>
                                        </td>
                                        <td>Stream</td>
                                        <td><?php echo htmlspecialchars($link['quality']); ?></td>
                                        <td>
                                            <?php
                                            $server_name = htmlspecialchars($link['server_name']);
                                            $is_inactive = strpos($server_name, '_inactive') !== false;
                                            if ($is_inactive) {
                                                echo '<span class="text-danger"><i class="fas fa-times-circle me-1"></i>' .
                                                     str_replace('_inactive', '', $server_name) . '</span>';
                                            } else {
                                                echo '<span class="text-success"><i class="fas fa-check-circle me-1"></i>' .
                                                     $server_name . '</span>';
                                            }
                                            ?>
                                        </td>
                                        <td>
                                            <div class="text-truncate" style="max-width: 250px;" title="<?php echo htmlspecialchars($link['link_url']); ?>">
                                                <?php echo htmlspecialchars($link['link_url']); ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($link['is_premium']): ?>
                                            <span class="badge bg-warning">প্রিমিয়াম</span>
                                            <?php else: ?>
                                            <span class="badge bg-secondary">ফ্রি</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php
                                        endwhile;
                                    endif;

                                    // Display episode links
                                    if ($episode_links_count > 0):
                                        while ($link = mysqli_fetch_assoc($episode_result)):
                                    ?>
                                    <tr>
                                        <td>
                                            <input class="form-check-input link-checkbox" type="checkbox"
                                                   name="selected_links[]" value="episode_links:<?php echo $link['id']; ?>">
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($link['content_title']); ?>
                                            <span class="badge bg-info">episode</span>
                                        </td>
                                        <td><?php echo ucfirst($link['link_type']); ?></td>
                                        <td><?php echo htmlspecialchars($link['quality']); ?></td>
                                        <td>
                                            <?php
                                            $server_name = htmlspecialchars($link['server_name']);
                                            $is_inactive = strpos($server_name, '_inactive') !== false;
                                            if ($is_inactive) {
                                                echo '<span class="text-danger"><i class="fas fa-times-circle me-1"></i>' .
                                                     str_replace('_inactive', '', $server_name) . '</span>';
                                            } else {
                                                echo '<span class="text-success"><i class="fas fa-check-circle me-1"></i>' .
                                                     $server_name . '</span>';
                                            }
                                            ?>
                                        </td>
                                        <td>
                                            <div class="text-truncate" style="max-width: 250px;" title="<?php echo htmlspecialchars($link['link_url']); ?>">
                                                <?php echo htmlspecialchars($link['link_url']); ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($link['is_premium']): ?>
                                            <span class="badge bg-warning">প্রিমিয়াম</span>
                                            <?php else: ?>
                                            <span class="badge bg-secondary">ফ্রি</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php
                                        endwhile;
                                    endif;
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </form>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <!-- /.container-fluid -->
    </div>
</div>
<!-- End of Content -->

<?php require_once 'includes/footer.php'; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle "Select All" checkbox
    const selectAllCheckbox = document.getElementById('select_all');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.link-checkbox');
            checkboxes.forEach(function(checkbox) {
                checkbox.checked = selectAllCheckbox.checked;
            });
        });
    }

    // Show/hide new domain input based on selected action
    const bulkActionSelect = document.getElementById('bulk_action');
    const newDomainContainer = document.getElementById('new_domain_container');

    if (bulkActionSelect && newDomainContainer) {
        bulkActionSelect.addEventListener('change', function() {
            if (this.value === 'update_domain') {
                newDomainContainer.style.display = 'block';
            } else {
                newDomainContainer.style.display = 'none';
            }
        });
    }

    // Form validation
    const bulkActionForm = document.getElementById('bulkActionForm');
    if (bulkActionForm) {
        bulkActionForm.addEventListener('submit', function(e) {
            const action = bulkActionSelect.value;
            const selectedLinks = document.querySelectorAll('.link-checkbox:checked');
            const newDomain = document.getElementById('new_domain');

            if (!action) {
                e.preventDefault();
                alert('দয়া করে একটি বাল্ক একশন নির্বাচন করুন।');
                return;
            }

            if (selectedLinks.length === 0) {
                e.preventDefault();
                alert('দয়া করে কমপক্ষে একটি লিংক নির্বাচন করুন।');
                return;
            }

            if (action === 'update_domain' && !newDomain.value.trim()) {
                e.preventDefault();
                alert('দয়া করে নতুন ডোমেইন নাম দিন।');
                return;
            }

            if (action === 'deactivate' && !confirm('আপনি কি নিশ্চিত যে আপনি নির্বাচিত লিংকগুলি ইনএক্টিভ করতে চান?')) {
                e.preventDefault();
                return;
            }

            if (action === 'update_domain' && !confirm('আপনি কি নিশ্চিত যে আপনি নির্বাচিত লিংকগুলির ডোমেইন আপডেট করতে চান?')) {
                e.preventDefault();
                return;
            }
        });
    }
});
</script>
