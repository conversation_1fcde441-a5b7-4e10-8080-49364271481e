/* Homepage Modern Design - Enhanced */
:root {
    --primary-red: #e50914;
    --dark-bg: #141414;
    --darker-bg: #000000;
    --card-bg: #1a1a1a;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --premium-gold: #ffc107;
    --gradient-overlay: linear-gradient(135deg, rgba(229, 9, 20, 0.8) 0%, rgba(0, 0, 0, 0.9) 100%);
}

/* Enhanced Hero Slider */
.main-slider {
    position: relative;
    height: 90vh;
    min-height: 700px;
    overflow: hidden;
    background: var(--darker-bg);
}

.slider-wrapper {
    position: relative;
    height: 100%;
    width: 100%;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: all 1.2s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
}

.slide.active {
    opacity: 1;
    z-index: 2;
}

.slide-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    transform: scale(1);
    transition: transform 12s ease-out;
    filter: brightness(0.7);
}

.slide.active .slide-image {
    transform: scale(1.05);
}

.slide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        45deg,
        rgba(0, 0, 0, 0.9) 0%,
        rgba(0, 0, 0, 0.6) 40%,
        rgba(229, 9, 20, 0.1) 70%,
        transparent 100%
    );
    z-index: 3;
}

.slide-content {
    position: relative;
    z-index: 4;
    max-width: 600px;
    padding: 2rem;
    animation: slideInUp 1s ease-out;
}

.slide.active .slide-content {
    animation: slideInUp 1.2s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-title {
    font-size: 3.5rem;
    font-weight: 900;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.8);
    line-height: 1.1;
    background: linear-gradient(135deg, #ffffff 0%, #e50914 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.slide-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    font-size: 1rem;
    color: var(--text-secondary);
}

.slide-meta span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.slide-meta i {
    color: var(--primary-red);
}

.slide-description {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: var(--text-secondary);
    text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.8);
}

.slide-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.slide-buttons .btn {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 160px;
    justify-content: center;
}

.slide-buttons .btn-danger {
    background: linear-gradient(135deg, var(--primary-red) 0%, #b8070f 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(229, 9, 20, 0.4);
}

.slide-buttons .btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(229, 9, 20, 0.6);
}

.slide-buttons .btn-outline-light {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.slide-buttons .btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Premium Badge Enhancement */
.premium-tag {
    background: linear-gradient(135deg, var(--premium-gold) 0%, #ff8f00 100%);
    color: var(--darker-bg);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 700;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Content Type Badge */
.content-type-badge .badge {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
    border-radius: 20px;
    font-weight: 600;
}

/* Slider Controls Enhancement */
.slider-controls {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 2rem;
    z-index: 5;
}

.slider-prev, .slider-next {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.slider-prev:hover, .slider-next:hover {
    background: rgba(229, 9, 20, 0.8);
    border-color: var(--primary-red);
    transform: scale(1.1);
}

.slider-dots {
    display: flex;
    gap: 0.8rem;
}

.slider-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.slider-dot.active {
    background: var(--primary-red);
    transform: scale(1.2);
}

/* Section Enhancements */
.section-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 2rem;
    position: relative;
    display: inline-block;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-red) 0%, #ff6b6b 100%);
    border-radius: 2px;
}

/* Top 10 Section Enhancement */
.top-10-carousel {
    display: flex;
    gap: 1.5rem;
    overflow-x: auto;
    padding: 1rem 0;
    scroll-behavior: smooth;
}

.top-10-carousel::-webkit-scrollbar {
    height: 8px;
}

.top-10-carousel::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.top-10-carousel::-webkit-scrollbar-thumb {
    background: var(--primary-red);
    border-radius: 4px;
}

.top-10-item {
    position: relative;
    min-width: 200px;
    flex-shrink: 0;
}

.top-10-rank {
    position: absolute;
    top: -10px;
    left: -10px;
    z-index: 10;
    background: linear-gradient(135deg, var(--primary-red) 0%, #b8070f 100%);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(229, 9, 20, 0.5);
}

.rank-number {
    color: white;
    font-weight: 900;
    font-size: 1.2rem;
}

/* Movie Card Enhancements */
.movie-card {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: var(--card-bg);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.movie-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.5);
}

.movie-card img {
    width: 100%;
    height: auto;
    aspect-ratio: 2/3;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.movie-card:hover img {
    transform: scale(1.1);
}

.movie-card-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
    padding: 2rem 1rem 1rem;
    transform: translateY(100%);
    transition: transform 0.4s ease;
}

.movie-card:hover .movie-card-overlay {
    transform: translateY(0);
}

.movie-card-title {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: white;
}

.movie-card-info {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.movie-card-rating {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    color: var(--premium-gold);
    font-weight: 600;
    margin-bottom: 1rem;
}

/* Premium Badge */
.premium-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(135deg, var(--premium-gold) 0%, #ff8f00 100%);
    color: var(--darker-bg);
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 700;
    z-index: 5;
    display: flex;
    align-items: center;
    gap: 0.3rem;
    box-shadow: 0 2px 10px rgba(255, 193, 7, 0.4);
}

/* Featured Carousel Enhancement */
.featured-carousel.owl-carousel .owl-item {
    padding: 0 10px;
}

.featured-carousel.owl-carousel .owl-nav {
    position: absolute;
    top: 50%;
    width: 100%;
    transform: translateY(-50%);
}

.featured-carousel.owl-carousel .owl-nav button {
    position: absolute;
    background: rgba(229, 9, 20, 0.8) !important;
    color: white !important;
    border: none !important;
    border-radius: 50% !important;
    width: 50px !important;
    height: 50px !important;
    font-size: 1.2rem !important;
    transition: all 0.3s ease !important;
}

.featured-carousel.owl-carousel .owl-nav .owl-prev {
    left: -25px;
}

.featured-carousel.owl-carousel .owl-nav .owl-next {
    right: -25px;
}

.featured-carousel.owl-carousel .owl-nav button:hover {
    background: var(--primary-red) !important;
    transform: scale(1.1) !important;
}

/* Premium Section Enhancement */
.premium-section {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(0, 0, 0, 0.9) 100%);
    border-top: 2px solid rgba(255, 193, 7, 0.3);
    border-bottom: 2px solid rgba(255, 193, 7, 0.3);
}

.premium-header h2 {
    background: linear-gradient(135deg, var(--premium-gold) 0%, #ff8f00 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.premium-card {
    border: 2px solid rgba(255, 193, 7, 0.3);
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.05) 0%, var(--card-bg) 100%);
}

.premium-card:hover {
    border-color: var(--premium-gold);
    box-shadow: 0 15px 40px rgba(255, 193, 7, 0.2);
}

/* Ad Placeholder Styles */
.ad-container {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 2px dashed rgba(229, 9, 20, 0.3);
    border-radius: 12px;
    padding: 2rem;
    margin: 2rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.ad-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(229, 9, 20, 0.1), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.ad-placeholder {
    color: var(--text-secondary);
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    min-height: 120px;
}

.ad-placeholder i {
    color: var(--primary-red);
    font-size: 1.5rem;
}

.ad-banner {
    min-height: 90px;
}

.ad-sidebar {
    min-height: 250px;
}

.ad-inline {
    min-height: 120px;
    margin: 1.5rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .slide-title {
        font-size: 2.5rem;
    }

    .slide-content {
        padding: 1rem;
    }

    .slide-buttons {
        flex-direction: column;
    }

    .slide-buttons .btn {
        width: 100%;
    }

    .section-title {
        font-size: 2rem;
    }

    .top-10-carousel {
        gap: 1rem;
    }

    .top-10-item {
        min-width: 160px;
    }

    .ad-container {
        padding: 1rem;
        margin: 1rem 0;
    }

    .ad-placeholder {
        font-size: 1rem;
        min-height: 80px;
    }
}

@media (max-width: 576px) {
    .main-slider {
        height: 70vh;
        min-height: 500px;
    }

    .slide-title {
        font-size: 2rem;
    }

    .slide-meta {
        gap: 0.5rem;
        font-size: 0.9rem;
    }

    .slide-description {
        font-size: 1rem;
    }

    .slider-controls {
        bottom: 1rem;
        gap: 1rem;
    }

    .slider-prev, .slider-next {
        width: 40px;
        height: 40px;
    }

    .ad-banner {
        min-height: 60px;
    }

    .ad-sidebar {
        min-height: 200px;
    }
}
