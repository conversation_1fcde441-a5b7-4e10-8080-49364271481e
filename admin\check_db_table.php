<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include config file
require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
    exit;
}

// Get table structure
$table_name = isset($_GET['table']) ? $_GET['table'] : 'tvshows';
$result = mysqli_query($conn, "DESCRIBE $table_name");

echo "<h1>Table Structure: $table_name</h1>";
echo "<p>Switch to: <a href='?table=tvshows'>tvshows</a> | <a href='?table=movies'>movies</a></p>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";

if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$row['Key']}</td>";
        echo "<td>{$row['Default']}</td>";
        echo "<td>{$row['Extra']}</td>";
        echo "</tr>";
    }
} else {
    echo "<tr><td colspan='6'>Error: " . mysqli_error($conn) . "</td></tr>";
}

echo "</table>";

// Show create table statement
$result = mysqli_query($conn, "SHOW CREATE TABLE $table_name");
if ($result) {
    $row = mysqli_fetch_assoc($result);
    
    echo "<h2>Create Table Statement</h2>";
    echo "<pre>";
    print_r($row['Create Table']);
    echo "</pre>";
} else {
    echo "<p>Error: " . mysqli_error($conn) . "</p>";
}
?>
