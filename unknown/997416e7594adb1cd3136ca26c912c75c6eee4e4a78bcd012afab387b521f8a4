import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:cinepix_app/constants/app_constants.dart';
import 'package:cinepix_app/models/download_link.dart';
import 'package:cinepix_app/services/download_manager.dart';
import 'package:screen_brightness/screen_brightness.dart';

class VideoPlayerWidget extends StatefulWidget {
  final DownloadLink downloadLink;
  final String title;
  final Function(Duration) onPositionChanged;
  final Duration? initialPosition;
  final int? contentId;
  final String? contentType;

  const VideoPlayerWidget({
    super.key,
    required this.downloadLink,
    required this.title,
    required this.onPositionChanged,
    this.initialPosition,
    this.contentId,
    this.contentType,
  });

  @override
  State<VideoPlayerWidget> createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  late Player _player;
  late VideoController _videoController;
  bool _isInitialized = false;
  bool _showControls = true;
  bool _isBuffering = false;
  bool _showResumeDialog = false;
  List<AudioTrack> _audioTracks = [];
  int _currentAudioTrack = 0;
  final double _playbackSpeed = 1.0;

  // Brightness and volume control
  double _brightness = 0.5;
  double _volume = 1.0;
  bool _showBrightnessControl = false;
  bool _showVolumeControl = false;

  // Timer for position updates
  Timer? _positionUpdateTimer;

  // Download manager
  final DownloadManager _downloadManager = DownloadManager();
  bool _isDownloading = false;
  bool _isDownloaded = false;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
    _initializeBrightness();
    _checkDownloadStatus();
  }

  Future<void> _initializeBrightness() async {
    try {
      // Get current screen brightness
      _brightness = await ScreenBrightness().current;
    } catch (e) {
      // Fallback to default value if we can't get current brightness
      _brightness = 0.5;
    }
  }

  Future<void> _checkDownloadStatus() async {
    if (widget.contentId != null && widget.contentType != null) {
      _isDownloaded = await _downloadManager.isDownloaded(
        widget.contentId!,
        widget.contentType!,
      );

      if (mounted) {
        setState(() {});
      }

      // Start auto download if not already downloaded
      if (!_isDownloaded) {
        _startAutoDownload();
      }
    }
  }

  Future<void> _startAutoDownload() async {
    if (_isDownloading ||
        widget.contentId == null ||
        widget.contentType == null) {
      return;
    }

    setState(() {
      _isDownloading = true;
    });

    try {
      final taskId = await _downloadManager.startDownload(
        downloadLink: widget.downloadLink,
        title: widget.title,
        contentId: widget.contentId!,
        contentType: widget.contentType!,
      );

      if (taskId != null) {
        debugPrint('Started auto download: $taskId');

        // Show subtle notification
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${widget.title} ডাউনলোড শুরু হয়েছে'),
              duration: const Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
              margin: const EdgeInsets.only(bottom: 70, left: 20, right: 20),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Auto download failed: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isDownloading = false;
        });
      }
    }
  }

  Future<void> _initializePlayer() async {
    setState(() {
      _isBuffering = true;
    });

    // Create a new player instance
    _player = Player();

    // Create a video controller for the player
    _videoController = VideoController(_player);

    try {
      // Check if offline version exists and use it if available
      String mediaUrl = widget.downloadLink.url;
      Map<String, String> headers = {
        "User-Agent": "CinePix/1.0",
      };

      if (widget.contentId != null && widget.contentType != null) {
        final offlineItem = await _downloadManager.getDownloadedItem(
          widget.contentId!,
          widget.contentType!,
        );

        if (offlineItem != null) {
          final file = File(offlineItem.filePath);
          if (await file.exists()) {
            mediaUrl = 'file://${offlineItem.filePath}';
            headers = {}; // No headers needed for local files

            // Show offline indicator
            if (mounted && context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('অফলাইন ভার্সন চালানো হচ্ছে'),
                  duration: const Duration(seconds: 2),
                  behavior: SnackBarBehavior.floating,
                  margin:
                      const EdgeInsets.only(bottom: 70, left: 20, right: 20),
                  backgroundColor: Colors.green,
                ),
              );
            }
          }
        }
      }

      // Open the media with headers
      await _player.open(
        Media(
          mediaUrl,
          httpHeaders: headers,
        ),
      );

      // Set up position listener for watch history
      _positionUpdateTimer = Timer.periodic(const Duration(seconds: 1), (_) {
        if (_player.state.playing && mounted) {
          widget.onPositionChanged(_player.state.position);
          // Force UI update for time display
          setState(() {});
        }
      });

      // Get initial volume
      _volume = _player.state.volume / 100;

      // Get audio tracks after a short delay to ensure they're loaded
      Future.delayed(const Duration(seconds: 1), () {
        _detectAudioTracks();
      });

      setState(() {
        _isInitialized = true;
        _isBuffering = false;
      });

      // Handle initial position and auto-play after UI is updated
      if (widget.initialPosition != null) {
        // Check if we should show resume dialog
        if (widget.initialPosition!.inSeconds > 30 &&
            widget.initialPosition! <
                _player.state.duration - const Duration(seconds: 30)) {
          setState(() {
            _showResumeDialog = true;
          });
        } else {
          _player.seek(widget.initialPosition!);
          _player.play();
        }
      } else {
        // Auto-play
        _player.play();
      }
    } catch (e) {
      // Log error without using print
      debugPrint('Error initializing video player: $e');
      setState(() {
        _isBuffering = false;
      });

      // Show error message to user
      if (mounted && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('ভিডিও লোড করতে সমস্যা হয়েছে। আবার চেষ্টা করুন।'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    // Cancel position update timer
    _positionUpdateTimer?.cancel();

    // Dispose player
    _player.dispose();

    super.dispose();
  }

  // Detect audio tracks from player
  void _detectAudioTracks() {
    // Get audio tracks from media_kit player
    _audioTracks = _player.state.tracks.audio;

    // If no audio tracks detected but URL suggests multiple audio
    if (_audioTracks.isEmpty) {
      final url = widget.downloadLink.url.toLowerCase();

      // Check for audio track information in the URL
      if (url.contains('audiotrack=') || url.contains('audio_track=')) {
        // Extract audio track information from URL
        final regex = RegExp(r'audio(?:_)?track=([^&]+)');
        final match = regex.firstMatch(url);
        if (match != null && match.groupCount >= 1) {
          final tracksInfo = match.group(1)!;
          final trackNames = tracksInfo.split(',');
          _audioTracks = trackNames
              .asMap()
              .entries
              .map((entry) =>
                  AudioTrack(entry.key.toString(), entry.value, null))
              .toList();
          return;
        }
      }

      // If no explicit track info, use URL keywords to guess available tracks
      if (url.contains('hindi') || url.contains('multi')) {
        _audioTracks = [
          AudioTrack('0', 'Default', null),
          AudioTrack('1', 'Hindi', null),
          AudioTrack('2', 'English', null),
          AudioTrack('3', 'Bengali', null),
        ];
      } else if (url.contains('dual')) {
        _audioTracks = [
          AudioTrack('0', 'Default', null),
          AudioTrack('1', 'English', null),
          AudioTrack('2', 'Bengali', null),
        ];
      } else if (url.contains('bangla') || url.contains('bengali')) {
        _audioTracks = [
          AudioTrack('0', 'Default', null),
          AudioTrack('1', 'Bengali', null),
        ];
      } else if (url.contains('english')) {
        _audioTracks = [
          AudioTrack('0', 'Default', null),
          AudioTrack('1', 'English', null),
        ];
      } else if (url.contains('japanese')) {
        _audioTracks = [
          AudioTrack('0', 'Default', null),
          AudioTrack('1', 'Japanese', null),
        ];
      } else if (url.contains('korean')) {
        _audioTracks = [
          AudioTrack('0', 'Default', null),
          AudioTrack('1', 'Korean', null),
        ];
      } else {
        // Check file extension for MKV which often has multiple audio tracks
        if (url.endsWith('.mkv')) {
          _audioTracks = [
            AudioTrack('0', 'Track 1', null),
            AudioTrack('1', 'Track 2', null),
          ];
        } else {
          _audioTracks = [AudioTrack('0', 'Default', null)];
        }
      }
    }

    // Update UI if needed
    if (mounted) {
      setState(() {});
    }
  }

  void _togglePlayPause() {
    setState(() {
      if (_player.state.playing) {
        _player.pause();
      } else {
        _player.play();
      }
    });
  }

  void _skipForward() {
    final newPosition = _player.state.position + const Duration(seconds: 10);
    _seekWithLoading(newPosition);
  }

  void _skipBackward() {
    final newPosition = _player.state.position - const Duration(seconds: 10);
    _seekWithLoading(newPosition);
  }

  // Helper method to show loading indicator during seek
  Future<void> _seekWithLoading(Duration position) async {
    // Show buffering indicator
    setState(() {
      _isBuffering = true;
    });

    // Perform the seek operation
    await _player.seek(position);

    // Wait a short time to allow buffering to start
    await Future.delayed(const Duration(milliseconds: 300));

    // If video was playing before seek, resume playback
    if (!_player.state.playing) {
      await _player.play();
    }

    // Hide buffering indicator if it's still showing after a delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted && _isBuffering) {
        setState(() {
          _isBuffering = false;
        });
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
      _showBrightnessControl = false;
      _showVolumeControl = false;
    });
  }

  void _updateBrightness(double delta) async {
    final newBrightness = (_brightness + delta).clamp(0.0, 1.0);

    // Only update if there's a significant change
    if ((newBrightness - _brightness).abs() > 0.01) {
      setState(() {
        _brightness = newBrightness;
        _showBrightnessControl = true;
        _showVolumeControl = false;
      });

      try {
        // Actually change the screen brightness
        await ScreenBrightness().setScreenBrightness(_brightness);
      } catch (e) {
        debugPrint('Failed to set brightness: $e');
      }

      // Auto-hide brightness control after 2 seconds
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            _showBrightnessControl = false;
          });
        }
      });
    }
  }

  void _updateVolume(double delta) {
    // Only update if there's a significant change
    final newVolume = (_volume + delta).clamp(0.0, 1.0);
    if ((newVolume - _volume).abs() > 0.01) {
      setState(() {
        _volume = newVolume;
        _showVolumeControl = true;
        _showBrightnessControl = false;

        // Set player volume (media_kit uses 0-100 scale)
        _player.setVolume(_volume * 100);
      });

      // Auto-hide volume control after 2 seconds
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            _showVolumeControl = false;
          });
        }
      });
    }
  }

  void _showAudioTrackMenu(BuildContext context) {
    if (_audioTracks.isEmpty || _audioTracks.length <= 1) {
      // If only one track, show a message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('কোন অতিরিক্ত অডিও ট্র্যাক নেই'),
          duration: Duration(seconds: 1),
          behavior: SnackBarBehavior.floating,
          margin: EdgeInsets.only(bottom: 70, left: 20, right: 20),
        ),
      );
      return;
    }

    // Show a dialog with all available audio tracks
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.symmetric(horizontal: 20),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.grey[900],
              borderRadius: BorderRadius.circular(12),
            ),
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Padding(
                  padding: EdgeInsets.only(bottom: 16),
                  child: Text(
                    'অডিও ট্র্যাক নির্বাচন করুন',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.5,
                  ),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: _audioTracks.length,
                    itemBuilder: (context, index) {
                      final isSelected = index == _currentAudioTrack;
                      final track = _audioTracks[index];
                      return ListTile(
                        title: Text(
                          track.title ?? 'Track ${index + 1}',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                        leading: isSelected
                            ? Icon(
                                Icons.check_circle,
                                color: AppConstants.primaryColor,
                              )
                            : const Icon(
                                Icons.audiotrack,
                                color: Colors.grey,
                              ),
                        trailing: isSelected
                            ? Icon(
                                Icons.music_note,
                                color: AppConstants.primaryColor,
                              )
                            : null,
                        onTap: () {
                          // Close dialog first to prevent UI lag
                          Navigator.of(context).pop();

                          // Only change if different track selected
                          if (index != _currentAudioTrack) {
                            setState(() {
                              _currentAudioTrack = index;
                            });

                            // Actually change the audio track
                            _changeAudioTrack(index);

                            // Show a toast to indicate audio track change
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                    'অডিও ট্র্যাক: ${_audioTracks[index]}'),
                                duration: const Duration(seconds: 1),
                                behavior: SnackBarBehavior.floating,
                                margin: const EdgeInsets.only(
                                    bottom: 70, left: 20, right: 20),
                              ),
                            );
                          }
                        },
                      );
                    },
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 16),
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(
                      'বাতিল',
                      style: TextStyle(
                        color: AppConstants.primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Method to actually change the audio track
  void _changeAudioTrack(int trackIndex) async {
    if (trackIndex < 0 || trackIndex >= _audioTracks.length) return;

    // First, get current position to resume from same point
    final currentPosition = _player.state.position;
    final wasPlaying = _player.state.playing;

    // Pause the current playback
    await _player.pause();

    // Show buffering indicator
    setState(() {
      _isBuffering = true;
    });

    try {
      // Update the current audio track index
      setState(() {
        _currentAudioTrack = trackIndex;
      });

      // Select the audio track using media_kit
      await _player.setAudioTrack(_audioTracks[trackIndex]);

      // Seek to the previous position
      await _player.seek(currentPosition);

      // Resume playback if it was playing before
      if (wasPlaying) {
        await _player.play();
      }

      // Show a message about the track change
      if (mounted && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'অডিও ট্র্যাক পরিবর্তন করা হয়েছে: ${_audioTracks[trackIndex].title ?? 'Track ${trackIndex + 1}'}'),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.only(bottom: 70, left: 20, right: 20),
          ),
        );
      }

      // Hide buffering indicator
      setState(() {
        _isBuffering = false;
      });
    } catch (e) {
      // Log error
      debugPrint('Error changing audio track: $e');
      // Show error message
      if (mounted && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('অডিও ট্র্যাক পরিবর্তন করতে সমস্যা হয়েছে: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
      debugPrint('Error changing audio track: $e');
    } finally {
      // Hide buffering indicator
      if (mounted) {
        setState(() {
          _isBuffering = false;
        });
      }
    }
  }

  void _resumeFromLastPosition() {
    if (widget.initialPosition != null) {
      _player.seek(widget.initialPosition!);
    }
    setState(() {
      _showResumeDialog = false;
    });
    _player.play();
  }

  void _startFromBeginning() {
    _player.seek(Duration.zero);
    setState(() {
      _showResumeDialog = false;
    });
    _player.play();
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));

    return hours == '00' ? '$minutes:$seconds' : '$hours:$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              'ভিডিও লোড হচ্ছে...',
              style: TextStyle(color: Colors.white.withAlpha(204)),
            ),
            if (_isBuffering)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  'বাফারিং হচ্ছে...',
                  style: TextStyle(
                      color: Colors.white.withAlpha(153), fontSize: 12),
                ),
              ),
          ],
        ),
      );
    }

    return Stack(
      children: [
        // Resume dialog
        if (_showResumeDialog)
          Container(
            color: Colors.black.withAlpha(200),
            child: Center(
              child: Card(
                color: Colors.grey[900],
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        'প্লেব্যাক চালিয়ে যাবেন?',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'আপনি যেখানে থেমেছিলেন সেখান থেকে চালিয়ে যেতে চান?',
                        style: TextStyle(color: Colors.white.withAlpha(204)),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ElevatedButton(
                            onPressed: _startFromBeginning,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.grey[700],
                            ),
                            child: const Text('শুরু থেকে দেখুন'),
                          ),
                          const SizedBox(width: 16),
                          ElevatedButton(
                            onPressed: _resumeFromLastPosition,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppConstants.primaryColor,
                            ),
                            child: const Text('চালিয়ে যান'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

        // Main player UI
        GestureDetector(
          onTap: _toggleControls,
          onVerticalDragUpdate: (details) {
            final screenWidth = MediaQuery.of(context).size.width;
            // Left side of screen - brightness control
            if (details.globalPosition.dx < screenWidth / 2) {
              _updateBrightness(-details.delta.dy * 0.01);
            }
            // Right side of screen - volume control
            else {
              _updateVolume(-details.delta.dy * 0.01);
            }
          },
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Video player
              Video(
                controller: _videoController,
              ),

              // Buffering indicator
              if (_isBuffering)
                Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                        AppConstants.primaryColor),
                    strokeWidth: 3.0,
                  ),
                ),

              // Brightness control indicator
              if (_showBrightnessControl)
                Positioned(
                  left: 20,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.black.withAlpha(153), // 0.6 opacity
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.brightness_6,
                            color: Colors.white,
                            size: 32,
                          ),
                          const SizedBox(height: 8),
                          Container(
                            height: 100,
                            width: 8,
                            decoration: BoxDecoration(
                              color: Colors.grey[700],
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Stack(
                              children: [
                                Positioned(
                                  bottom: 0,
                                  left: 0,
                                  right: 0,
                                  height: _brightness * 100,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '${(_brightness * 100).toInt()}%',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

              // Volume control indicator
              if (_showVolumeControl)
                Positioned(
                  right: 20,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.black.withAlpha(153), // 0.6 opacity
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _volume > 0 ? Icons.volume_up : Icons.volume_off,
                            color: Colors.white,
                            size: 32,
                          ),
                          const SizedBox(height: 8),
                          Container(
                            height: 100,
                            width: 8,
                            decoration: BoxDecoration(
                              color: Colors.grey[700],
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Stack(
                              children: [
                                Positioned(
                                  bottom: 0,
                                  left: 0,
                                  right: 0,
                                  height: _volume * 100,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: AppConstants.primaryColor,
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '${(_volume * 100).toInt()}%',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

              // Controls overlay
              if (_showControls)
                Container(
                  color: Colors.black.withAlpha(128),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Top bar with title and controls
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Row(
                          children: [
                            IconButton(
                              icon: const Icon(Icons.arrow_back,
                                  color: Colors.white),
                              onPressed: () => Navigator.pop(context),
                            ),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    widget.title,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  if (_isDownloaded)
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.offline_pin,
                                          color: Colors.green[400],
                                          size: 14,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          'অফলাইন',
                                          style: TextStyle(
                                            color: Colors.green[400],
                                            fontSize: 12,
                                          ),
                                        ),
                                      ],
                                    ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Center play/pause button
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          IconButton(
                            iconSize: 60,
                            icon: const Icon(Icons.replay_10,
                                color: Colors.white),
                            onPressed: _skipBackward,
                            tooltip: '10 সেকেন্ড পিছনে',
                            padding: const EdgeInsets.all(16),
                          ),
                          IconButton(
                            iconSize: 80,
                            icon: Icon(
                              _player.state.playing
                                  ? Icons.pause_circle_filled
                                  : Icons.play_circle_filled,
                              color: Colors.white,
                            ),
                            onPressed: _togglePlayPause,
                            tooltip: 'প্লে/পজ',
                            padding: const EdgeInsets.all(16),
                          ),
                          IconButton(
                            iconSize: 60,
                            icon: const Icon(Icons.forward_10,
                                color: Colors.white),
                            onPressed: _skipForward,
                            tooltip: '10 সেকেন্ড সামনে',
                            padding: const EdgeInsets.all(16),
                          ),
                        ],
                      ),

                      // Bottom progress bar and controls
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            // Playback speed indicator
                            if (_playbackSpeed != 1.0)
                              Padding(
                                padding: const EdgeInsets.only(bottom: 8.0),
                                child: Text(
                                  '${_playbackSpeed}x',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),

                            // Progress bar
                            GestureDetector(
                              onHorizontalDragEnd: (details) {
                                // When user finishes dragging, show loading indicator
                                setState(() {
                                  _isBuffering = true;
                                });

                                // Hide buffering indicator after a delay if it's still showing
                                Future.delayed(
                                    const Duration(milliseconds: 800), () {
                                  if (mounted && _isBuffering) {
                                    setState(() {
                                      _isBuffering = false;
                                    });
                                  }
                                });
                              },
                              child: SliderTheme(
                                data: SliderThemeData(
                                  trackHeight: 4,
                                  activeTrackColor: AppConstants.primaryColor,
                                  inactiveTrackColor: Colors.grey.shade800,
                                  thumbColor: AppConstants.primaryColor,
                                  thumbShape: const RoundSliderThumbShape(
                                      enabledThumbRadius: 6),
                                  overlayShape: const RoundSliderOverlayShape(
                                      overlayRadius: 12),
                                ),
                                child: Slider(
                                  value: _player.state.position.inMilliseconds
                                      .toDouble(),
                                  min: 0,
                                  max: _player.state.duration.inMilliseconds
                                      .toDouble(),
                                  onChanged: (value) {
                                    _player.seek(
                                        Duration(milliseconds: value.toInt()));
                                  },
                                ),
                              ),
                            ),

                            // Time indicators and volume
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // Current position
                                Text(
                                  _formatDuration(_player.state.position),
                                  style: const TextStyle(color: Colors.white),
                                ),

                                // Audio track button (only show if multiple tracks available)
                                if (_audioTracks.length > 1)
                                  GestureDetector(
                                    onTap: () => _showAudioTrackMenu(context),
                                    child: Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: AppConstants.primaryColor
                                            .withAlpha(100),
                                        shape: BoxShape.circle,
                                      ),
                                      child: const Icon(
                                        Icons.audiotrack,
                                        color: Colors.white,
                                        size: 24,
                                      ),
                                    ),
                                  ),

                                // Total duration
                                Text(
                                  _formatDuration(_player.state.duration),
                                  style: const TextStyle(color: Colors.white),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }
}
