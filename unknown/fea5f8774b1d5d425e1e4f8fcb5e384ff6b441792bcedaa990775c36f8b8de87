import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';
import 'package:get/get.dart';
// import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:cinepix_app/constants/app_constants.dart';
import 'package:cinepix_app/controllers/auth_controller.dart';
import 'package:cinepix_app/controllers/movie_controller.dart';
import 'package:cinepix_app/controllers/tv_show_controller.dart';
import 'package:cinepix_app/controllers/search_controller.dart' as app_search;
import 'package:cinepix_app/controllers/category_controller.dart';
import 'package:cinepix_app/controllers/theme_controller.dart';
import 'package:cinepix_app/controllers/watch_history_controller.dart';
import 'package:cinepix_app/controllers/review_controller.dart';
// import 'package:cinepix_app/services/video_service.dart';
import 'package:cinepix_app/services/download_manager.dart';
import 'package:cinepix_app/services/storage_manager.dart';
import 'package:cinepix_app/views/splash_screen.dart';
import 'package:cinepix_app/views/account_settings_screen.dart';
import 'package:cinepix_app/views/notifications_settings_screen.dart';
import 'package:cinepix_app/views/downloads_screen.dart';
import 'package:cinepix_app/views/download_settings_screen.dart';
import 'package:cinepix_app/views/storage_management_screen.dart';
import 'package:cinepix_app/widgets/download_link_card.dart';
import 'package:media_kit/media_kit.dart'; // Import MediaKit

// Check if the app is running on Android TV
bool isAndroidTV() {
  if (!kIsWeb && Platform.isAndroid) {
    try {
      // Check for leanback intent filter in AndroidManifest.xml
      // This is a simple check, but in a real app you might want to use
      // a plugin like device_info_plus to get more accurate information
      final size = PlatformDispatcher.instance.views.first.physicalSize;
      return size.width > size.height;
    } catch (e) {
      debugPrint('Error checking for Android TV: $e');
    }
  }
  return false;
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize MediaKit for ExoPlayer support
  MediaKit.ensureInitialized();

  // Initialize Download Manager
  await DownloadManager().initialize();

  // Initialize Storage Manager
  await StorageManager().initialize();

  // Register controllers
  Get.put(AuthController());
  Get.put(MovieController());
  Get.put(TvShowController());
  Get.put(app_search.SearchController());
  Get.put(CategoryController());
  Get.put(ThemeController());
  Get.put(WatchHistoryController());
  Get.put(ReviewController());

  // Set preferred orientations based on platform
  if (!isAndroidTV()) {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final ThemeController themeController = Get.find<ThemeController>();

    return Obx(() => GetMaterialApp(
          title: AppConstants.appName,
          debugShowCheckedModeBanner: false,
          theme: themeController.lightTheme,
          darkTheme: themeController.darkTheme,
          themeMode: themeController.themeMode.value,
          navigatorKey: navigatorKey,
          defaultTransition: Transition.cupertino,
          home: const SplashScreen(),
          getPages: [
            GetPage(
              name: '/account_settings',
              page: () => const AccountSettingsScreen(),
            ),
            GetPage(
              name: '/notifications_settings',
              page: () => const NotificationsSettingsScreen(),
            ),
            GetPage(
              name: '/downloads',
              page: () => const DownloadsScreen(),
            ),
            GetPage(
              name: '/download_settings',
              page: () => const DownloadSettingsScreen(),
            ),
            GetPage(
              name: '/storage_management',
              page: () => const StorageManagementScreen(),
            ),
          ],
        ));
  }
}
