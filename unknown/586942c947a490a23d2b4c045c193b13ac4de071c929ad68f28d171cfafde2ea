import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cinepix_app/constants/app_constants.dart';
import 'package:cinepix_app/models/user.dart';

class StorageService {
  // Save auth token
  Future<void> saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.tokenKey, token);
  }

  // Get auth token
  Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(AppConstants.tokenKey);
  }

  // Save user data
  Future<void> saveUser(User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(AppConstants.userIdKey, user.id);
    await prefs.setString(AppConstants.userNameKey, user.username);
    await prefs.setString(AppConstants.userEmail<PERSON>ey, user.email);
    await prefs.setBool(AppConstants.isPremium<PERSON>ey, user.isPremium);

    // Save full user object as JSON
    await prefs.setString('user_data', json.encode(user.toJson()));
  }

  // Get user data
  Future<User?> getUser() async {
    final prefs = await SharedPreferences.getInstance();
    final userData = prefs.getString('user_data');

    if (userData != null) {
      return User.fromJson(json.decode(userData));
    }

    return null;
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  // Check if user is premium
  Future<bool> isPremium() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(AppConstants.isPremiumKey) ?? false;
  }

  // Save dark mode preference
  Future<void> setDarkMode(bool isDarkMode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(AppConstants.isDarkModeKey, isDarkMode);
  }

  // Get dark mode preference
  Future<bool> getDarkMode() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(AppConstants.isDarkModeKey) ??
        true; // Default to dark mode
  }

  // Save data with expiry
  Future<void> saveWithExpiry(
      String key, String value, int expiryMinutes) async {
    final prefs = await SharedPreferences.getInstance();
    final expiryTime = DateTime.now()
        .add(Duration(minutes: expiryMinutes))
        .millisecondsSinceEpoch;

    final data = {
      'value': value,
      'expiry': expiryTime,
    };

    await prefs.setString(key, json.encode(data));
  }

  // Get data with expiry check
  Future<String?> getWithExpiry(String key) async {
    final prefs = await SharedPreferences.getInstance();
    final data = prefs.getString(key);

    if (data != null) {
      final decoded = json.decode(data);
      final expiry = decoded['expiry'];

      if (expiry > DateTime.now().millisecondsSinceEpoch) {
        return decoded['value'];
      } else {
        // Data expired, remove it
        await prefs.remove(key);
      }
    }

    return null;
  }

  // Save boolean value
  Future<void> saveBool(String key, bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(key, value);
  }

  // Get boolean value
  Future<bool?> getBool(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(key);
  }

  // Save integer value
  Future<void> saveInt(String key, int value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(key, value);
  }

  // Get integer value
  Future<int?> getInt(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(key);
  }

  // Save string value
  Future<void> saveString(String key, String value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(key, value);
  }

  // Get string value
  Future<String?> getString(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(key);
  }

  // Clear all stored data
  Future<void> clearAll() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }
}
