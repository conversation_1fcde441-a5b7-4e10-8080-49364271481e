<?php
// Database connection
$host = 'localhost';
$username = 'tipsbdxy_4525';
$password = '@mdsrabon13';
$database = 'tipsbdxy_4525';

$conn = mysqli_connect($host, $username, $password, $database);

if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// Define the new plan details
$name = "Premium Plus";  // Change this to your desired plan name
$price = 499;            // Change this to your desired price
$duration = 90;          // Change this to your desired duration in days (e.g., 30, 60, 90)
$features = "Access to all premium movies and TV shows\nWatch on multiple devices\nHD quality streaming\nPriority support\nEarly access to new releases";

// Check if description column exists
$check_column_query = "SHOW COLUMNS FROM premium_plans LIKE 'description'";
$check_column_result = mysqli_query($conn, $check_column_query);
$has_description = mysqli_num_rows($check_column_result) > 0;

// Insert the new plan
if ($has_description) {
    $description = "Premium Plus plan with extended features and longer duration";
    $query = "INSERT INTO premium_plans (name, description, price, duration, features) 
              VALUES ('$name', '$description', $price, $duration, '$features')";
} else {
    $query = "INSERT INTO premium_plans (name, price, duration, features) 
              VALUES ('$name', $price, $duration, '$features')";
}

if (mysqli_query($conn, $query)) {
    echo "<h2>New premium plan added successfully!</h2>";
    echo "<p><strong>Plan Details:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Name:</strong> $name</li>";
    echo "<li><strong>Price:</strong> ৳$price</li>";
    echo "<li><strong>Duration:</strong> $duration days</li>";
    echo "<li><strong>Features:</strong><br><pre>$features</pre></li>";
    echo "</ul>";
    
    // Show all plans
    echo "<h2>All Premium Plans:</h2>";
    $plans_query = "SELECT * FROM premium_plans ORDER BY price";
    $plans_result = mysqli_query($conn, $plans_query);
    
    if (mysqli_num_rows($plans_result) > 0) {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>Name</th><th>Price</th><th>Duration (days)</th><th>Features</th></tr>";
        
        while ($plan = mysqli_fetch_assoc($plans_result)) {
            echo "<tr>";
            echo "<td>" . $plan['id'] . "</td>";
            echo "<td>" . $plan['name'] . "</td>";
            echo "<td>৳" . $plan['price'] . "</td>";
            echo "<td>" . $plan['duration'] . "</td>";
            echo "<td><pre>" . $plan['features'] . "</pre></td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
} else {
    echo "<h2>Error adding premium plan:</h2>";
    echo "<p>" . mysqli_error($conn) . "</p>";
    
    // Show table structure
    echo "<h3>Premium Plans Table Structure:</h3>";
    $structure_query = "DESCRIBE premium_plans";
    $structure_result = mysqli_query($conn, $structure_query);
    
    if ($structure_result) {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        while ($row = mysqli_fetch_assoc($structure_result)) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
}

mysqli_close($conn);
?>
