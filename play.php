<?php
// Simple standalone player without database connection

// Start output buffering to prevent 'headers already sent' errors
ob_start();

// Start session
session_start();

// Site configuration
define('SITE_NAME', 'MovieFlix');

// Automatically detect site URL
$site_url = 'http://' . $_SERVER['HTTP_HOST'];
$script_name = dirname($_SERVER['SCRIPT_NAME']);

// If script is in root directory, use empty string
if ($script_name === '/' || $script_name === '\\') {
    $script_name = '';
}

// Define site URL
define('SITE_URL', $site_url . $script_name);

// Function to redirect
function redirect($url) {
    // Clean any existing output buffers
    while (ob_get_level()) {
        ob_end_clean();
    }

    // Set the header and exit
    header("Location: $url");
    exit();
}

// Get video URL from query parameter
if (!isset($_GET['url']) || empty($_GET['url'])) {
    redirect(SITE_URL);
}

$video_url = urldecode($_GET['url']);
$title = isset($_GET['title']) ? urldecode($_GET['title']) : 'Video Player';
$poster = isset($_GET['poster']) ? urldecode($_GET['poster']) : '';
$is_premium = isset($_GET['premium']) && $_GET['premium'] == '1';

// Check file extension to determine video type
$file_extension = strtolower(pathinfo(parse_url($video_url, PHP_URL_PATH), PATHINFO_EXTENSION));
$video_type = 'video/mp4'; // Default video type

// Set appropriate MIME type based on file extension
if ($file_extension === 'mkv') {
    $video_type = 'video/x-matroska';
} elseif ($file_extension === 'webm') {
    $video_type = 'video/webm';
} elseif ($file_extension === 'ogg' || $file_extension === 'ogv') {
    $video_type = 'video/ogg';
} elseif ($file_extension === 'mov') {
    $video_type = 'video/quicktime';
} elseif ($file_extension === 'avi') {
    $video_type = 'video/x-msvideo';
} elseif ($file_extension === 'flv') {
    $video_type = 'video/x-flv';
} elseif ($file_extension === '3gp') {
    $video_type = 'video/3gpp';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="site-url" content="<?php echo SITE_URL; ?>">
    <title><?php echo $title; ?> - <?php echo SITE_NAME; ?></title>
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo SITE_URL; ?>/assets/img/favicon.png" type="image/x-icon">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Plyr CSS -->
    <link rel="stylesheet" href="https://cdn.plyr.io/3.7.8/plyr.css">
    <style>
        :root {
            --primary-color: #e50914;
            --secondary-color: #141414;
            --text-color: #ffffff;
            --card-bg: #1f1f1f;
            --body-bg: #0c0c0c;
        }

        body {
            background-color: var(--body-bg);
            color: var(--text-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background-color: rgba(0, 0, 0, 0.9);
        }

        .player-container {
            background-color: #000;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .plyr {
            --plyr-color-main: var(--primary-color);
            --plyr-video-control-color: var(--text-color);
            --plyr-video-control-background-hover: var(--primary-color);
            --plyr-audio-control-background-hover: var(--primary-color);
            --plyr-audio-control-color: var(--text-color);
            --plyr-menu-background: var(--secondary-color);
            --plyr-menu-color: var(--text-color);
            border-radius: 8px;
            height: 100%;
        }

        .video-info {
            background-color: var(--card-bg);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .video-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--text-color);
        }

        .player-options {
            background-color: var(--card-bg);
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .player-options h4 {
            margin-bottom: 1rem;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .player-options .btn {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            border-radius: 4px;
        }

        .badge-premium {
            background-color: var(--primary-color);
            color: white;
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            margin-left: 0.5rem;
            vertical-align: middle;
        }
    </style>
</head>
<body>
    <!-- Simple Header -->
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
            <div class="container">
                <a class="navbar-brand" href="<?php echo SITE_URL; ?>">
                    <span class="text-danger fw-bold">MOVIE</span><span class="text-light">FLIX</span>
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo SITE_URL; ?>">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo SITE_URL; ?>/movies.php">Movies</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo SITE_URL; ?>/tvshows.php">TV Shows</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="py-4">
        <div class="container">
            <!-- Video Title -->
            <div class="video-info">
                <h1 class="video-title">
                    <?php echo $title; ?>
                    <?php if($is_premium): ?>
                    <span class="badge-premium">PREMIUM</span>
                    <?php endif; ?>
                </h1>
            </div>

            <!-- Video Player -->
            <div class="player-container ratio ratio-16x9">
                <video id="player" playsinline controls>
                    <source src="video_proxy.php?url=<?php echo urlencode($video_url); ?>" type="<?php echo $video_type; ?>">
                </video>
            </div>

            <!-- Player Options -->
            <div class="player-options">
                <h4>External Players</h4>
                <div class="d-flex flex-wrap">
                    <button onclick="openVLC('<?php echo $video_url; ?>')" class="btn btn-outline-light">
                        <i class="fas fa-play-circle me-2"></i>VLC Player
                    </button>
                    <button onclick="openMX('<?php echo $video_url; ?>')" class="btn btn-outline-light">
                        <i class="fas fa-play-circle me-2"></i>MX Player
                    </button>
                    <button onclick="openPlayit('<?php echo $video_url; ?>')" class="btn btn-outline-light">
                        <i class="fas fa-play-circle me-2"></i>Playit Player
                    </button>
                    <button onclick="openKMPlayer('<?php echo $video_url; ?>')" class="btn btn-outline-light">
                        <i class="fas fa-play-circle me-2"></i>KM Player
                    </button>
                    <a href="<?php echo $video_url; ?>" class="btn btn-primary" target="_blank">
                        <i class="fas fa-download me-2"></i>Download
                    </a>
                    <a href="video_proxy.php?url=<?php echo urlencode($video_url); ?>" class="btn btn-success" target="_blank">
                        <i class="fas fa-play-circle me-2"></i>Direct Stream
                    </a>
                </div>
            </div>
        </div>
    </main>

    <!-- Simple Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><span class="text-danger">MOVIE</span><span class="text-light">FLIX</span></h5>
                    <p class="text-muted">Stream and download movies and TV shows.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-muted">&copy; <?php echo date('Y'); ?> MovieFlix. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Plyr JS -->
    <script src="https://cdn.plyr.io/3.7.8/plyr.polyfilled.js"></script>

    <script>
        // Initialize Plyr player
        const player = new Plyr('#player', {
            controls: [
                'play-large', // The large play button in the center
                'restart', // Restart playback
                'rewind', // Rewind by the seek time (default 10 seconds)
                'play', // Play/pause playback
                'fast-forward', // Fast forward by the seek time (default 10 seconds)
                'progress', // The progress bar and scrubber for playback and buffering
                'current-time', // The current time of playback
                'duration', // The full duration of the media
                'mute', // Toggle mute
                'volume', // Volume control
                'captions', // Toggle captions
                'settings', // Settings menu
                'pip', // Picture-in-picture (currently Safari only)
                'airplay', // Airplay (currently Safari only)
                'fullscreen' // Toggle fullscreen
            ],
            settings: ['captions', 'quality', 'speed', 'loop'],
            speed: { selected: 1, options: [0.5, 0.75, 1, 1.25, 1.5, 1.75, 2] },
            keyboard: { focused: true, global: true },
            tooltips: { controls: true, seek: true },
            captions: { active: true, language: 'auto', update: true }
        });

        // External player functions
        function openVLC(url) {
            window.location.href = `vlc://${url}`;
        }

        function openMX(url) {
            window.location.href = `intent:${url}#Intent;package=com.mxtech.videoplayer.ad;end`;
        }

        function openPlayit(url) {
            window.location.href = `playit://playerv2/video?url=${url}`;
        }

        function openKMPlayer(url) {
            window.location.href = `intent:${url}#Intent;package=com.kmplayer;end`;
        }
    </script>
</body>
</html>
<?php
// Flush the output buffer and send the content to the browser
if (ob_get_level()) {
    ob_end_flush();
}
?>