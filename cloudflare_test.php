<?php
// Include config file
require_once 'includes/config.php';

// Cloudflare Workers test URL
$test_url = "https://nim-pata-worker-quiet-river-e51c.bdmovieshub.workers.dev/1:/File%20July%20to%20December/10-1-24/When%20Evil%20Lurks%20(2023)%20WEBRip%20Hindi%20+%20Ta%20Te%20Es%20720p%20AVC%20DDP%205.1%20ESub.mkv";
$test_title = "When Evil Lurks (2023)";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cloudflare Workers Video Test</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/style.css">
    <style>
        body {
            background-color: #141414;
            color: #fff;
            padding-top: 70px;
        }
        .container {
            max-width: 800px;
        }
        .card {
            background-color: #222;
            border: none;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #333;
            color: #fff;
            border-bottom: 1px solid #444;
            padding: 15px 20px;
        }
        .card-body {
            padding: 20px;
        }
        .btn-primary {
            background-color: #e50914;
            border-color: #e50914;
        }
        .btn-primary:hover {
            background-color: #b20710;
            border-color: #b20710;
        }
        .form-control {
            background-color: #333;
            border-color: #444;
            color: #fff;
        }
        .form-control:focus {
            background-color: #333;
            border-color: #e50914;
            color: #fff;
            box-shadow: 0 0 0 0.25rem rgba(229, 9, 20, 0.25);
        }
        .player-options {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        .player-option {
            flex: 1;
            min-width: 200px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="<?php echo SITE_URL; ?>">
                <span class="text-danger fw-bold">BANGLA</span><span class="text-light">MOBI</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>">Home</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container">
        <h1 class="mb-4">Cloudflare Workers Video Test</h1>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Test with Predefined URL</h5>
            </div>
            <div class="card-body">
                <p><strong>URL:</strong> <?php echo $test_url; ?></p>
                <p><strong>Title:</strong> <?php echo $test_title; ?></p>
                
                <div class="player-options">
                    <div class="player-option">
                        <h6>Enhanced Player (Recommended)</h6>
                        <a href="<?php echo SITE_URL; ?>/plyr_player_enhanced.php?url=<?php echo urlencode($test_url); ?>&title=<?php echo urlencode($test_title); ?>&quality=720p&language=Hindi&source=WEBRip" class="btn btn-primary w-100">
                            <i class="fas fa-play me-2"></i> Open in Enhanced Player
                        </a>
                    </div>
                    
                    <div class="player-option">
                        <h6>Native HTML5 Player</h6>
                        <button class="btn btn-outline-light w-100" id="show-native-player">
                            <i class="fas fa-play me-2"></i> Show Native Player
                        </button>
                    </div>
                </div>
                
                <div id="native-player-container" class="mt-4" style="display: none;">
                    <div class="ratio ratio-16x9">
                        <video id="native-player" controls crossorigin>
                            <source src="<?php echo $test_url; ?>" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                    </div>
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i> If the video doesn't play in the native player, try the Enhanced Player which has better compatibility.
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Test with Custom URL</h5>
            </div>
            <div class="card-body">
                <form action="plyr_player_enhanced.php" method="GET">
                    <div class="mb-3">
                        <label for="url" class="form-label">Video URL</label>
                        <input type="text" class="form-control" id="url" name="url" placeholder="Enter Cloudflare Workers URL" required>
                    </div>
                    <div class="mb-3">
                        <label for="title" class="form-label">Video Title</label>
                        <input type="text" class="form-control" id="title" name="title" placeholder="Enter video title" required>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="quality" class="form-label">Quality</label>
                            <select class="form-control" id="quality" name="quality">
                                <option value="720p">720p</option>
                                <option value="1080p">1080p</option>
                                <option value="480p">480p</option>
                                <option value="360p">360p</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="language" class="form-label">Language</label>
                            <input type="text" class="form-control" id="language" name="language" value="Hindi">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="source" class="form-label">Source</label>
                            <input type="text" class="form-control" id="source" name="source" value="WEBRip">
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-play me-2"></i> Test in Enhanced Player
                    </button>
                </form>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">Troubleshooting</h5>
            </div>
            <div class="card-body">
                <h6>If the video doesn't play:</h6>
                <ol>
                    <li>Check if the URL is correct and accessible</li>
                    <li>Try using a different browser (Chrome or Firefox recommended)</li>
                    <li>Clear your browser cache and cookies</li>
                    <li>Try disabling any ad blockers or VPN</li>
                    <li>Check your internet connection</li>
                </ol>
                
                <h6>Technical Information:</h6>
                <p>The enhanced player uses multiple technologies to play videos:</p>
                <ul>
                    <li>HLS.js for HLS streams</li>
                    <li>Shaka Player for advanced streaming</li>
                    <li>Native HTML5 video as fallback</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Show/hide native player
            const showNativePlayerBtn = document.getElementById('show-native-player');
            const nativePlayerContainer = document.getElementById('native-player-container');
            const nativePlayer = document.getElementById('native-player');
            
            showNativePlayerBtn.addEventListener('click', function() {
                if (nativePlayerContainer.style.display === 'none') {
                    nativePlayerContainer.style.display = 'block';
                    showNativePlayerBtn.innerHTML = '<i class="fas fa-times me-2"></i> Hide Native Player';
                    // Try to load the video
                    nativePlayer.load();
                } else {
                    nativePlayerContainer.style.display = 'none';
                    showNativePlayerBtn.innerHTML = '<i class="fas fa-play me-2"></i> Show Native Player';
                    // Pause the video
                    nativePlayer.pause();
                }
            });
            
            // Pre-fill the form with the test URL
            document.getElementById('url').value = '<?php echo $test_url; ?>';
            document.getElementById('title').value = '<?php echo $test_title; ?>';
        });
    </script>
</body>
</html>
