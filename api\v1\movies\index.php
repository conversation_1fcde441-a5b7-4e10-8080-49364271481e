<?php
// API Movies Endpoints

// Get the request
global $request;

// Handle different endpoints based on the path parts
$action = $request['parts'][0] ?? 'list';

switch ($action) {
    case 'list':
        handle_list_movies($request);
        break;
    
    case 'featured':
        handle_featured_movies($request);
        break;
    
    case 'popular':
        handle_popular_movies($request);
        break;
    
    case 'latest':
        handle_latest_movies($request);
        break;
    
    case 'categories':
        handle_movie_categories($request);
        break;
    
    default:
        // If action is numeric, treat it as movie ID
        if (is_numeric($action)) {
            handle_get_movie($action, $request);
        } else {
            api_error('Invalid movies endpoint', 404);
        }
}

// Handle listing movies
function handle_list_movies($request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get query parameters
    $page = isset($request['params']['page']) ? (int)$request['params']['page'] : 1;
    $limit = isset($request['params']['limit']) ? (int)$request['params']['limit'] : 20;
    $category_id = isset($request['params']['category']) ? (int)$request['params']['category'] : 0;
    $search = isset($request['params']['search']) ? $request['params']['search'] : '';
    $year = isset($request['params']['year']) ? (int)$request['params']['year'] : 0;
    $sort = isset($request['params']['sort']) ? $request['params']['sort'] : 'latest';
    
    // Validate parameters
    if ($page < 1) $page = 1;
    if ($limit < 1 || $limit > 100) $limit = 20;
    
    // Calculate offset
    $offset = ($page - 1) * $limit;
    
    // Build query
    $query = "SELECT m.*, c.name as category_name 
              FROM movies m 
              LEFT JOIN categories c ON m.category_id = c.id 
              WHERE 1=1";
    
    $count_query = "SELECT COUNT(*) as total FROM movies WHERE 1=1";
    
    $params = [];
    $types = '';
    
    // Add category filter
    if ($category_id > 0) {
        $query .= " AND m.category_id = ?";
        $count_query .= " AND category_id = ?";
        $params[] = $category_id;
        $types .= 'i';
    }
    
    // Add search filter
    if (!empty($search)) {
        $search_term = "%$search%";
        $query .= " AND (m.title LIKE ? OR m.description LIKE ?)";
        $count_query .= " AND (title LIKE ? OR description LIKE ?)";
        $params[] = $search_term;
        $params[] = $search_term;
        $types .= 'ss';
    }
    
    // Add year filter
    if ($year > 0) {
        $query .= " AND m.release_year = ?";
        $count_query .= " AND release_year = ?";
        $params[] = $year;
        $types .= 'i';
    }
    
    // Add sorting
    switch ($sort) {
        case 'title':
            $query .= " ORDER BY m.title ASC";
            break;
        case 'rating':
            $query .= " ORDER BY m.rating DESC";
            break;
        case 'views':
            $query .= " ORDER BY m.views DESC";
            break;
        case 'oldest':
            $query .= " ORDER BY m.created_at ASC";
            break;
        case 'latest':
        default:
            $query .= " ORDER BY m.created_at DESC";
            break;
    }
    
    // Add pagination
    $query .= " LIMIT ?, ?";
    $params[] = $offset;
    $params[] = $limit;
    $types .= 'ii';
    
    // Get total count
    $count_stmt = mysqli_prepare($conn, $count_query);
    if (!empty($params)) {
        $count_types = substr($types, 0, -2); // Remove the last two characters (for pagination)
        mysqli_stmt_bind_param($count_stmt, $count_types, ...$params);
    }
    mysqli_stmt_execute($count_stmt);
    $count_result = mysqli_stmt_get_result($count_stmt);
    $total = mysqli_fetch_assoc($count_result)['total'];
    
    // Get movies
    $stmt = mysqli_prepare($conn, $query);
    if (!empty($params)) {
        mysqli_stmt_bind_param($stmt, $types, ...$params);
    }
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $movies = [];
    while ($movie = mysqli_fetch_assoc($result)) {
        // Format movie data
        $movies[] = format_movie_data($movie);
    }
    
    // Calculate pagination info
    $total_pages = ceil($total / $limit);
    
    // Return response
    api_response([
        'success' => true,
        'data' => [
            'movies' => $movies,
            'pagination' => [
                'total' => $total,
                'per_page' => $limit,
                'current_page' => $page,
                'last_page' => $total_pages,
                'from' => $offset + 1,
                'to' => min($offset + $limit, $total)
            ]
        ]
    ]);
}

// Handle featured movies
function handle_featured_movies($request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get query parameters
    $limit = isset($request['params']['limit']) ? (int)$request['params']['limit'] : 10;
    
    // Validate parameters
    if ($limit < 1 || $limit > 50) $limit = 10;
    
    // Get featured movies
    $query = "SELECT m.*, c.name as category_name 
              FROM movies m 
              LEFT JOIN categories c ON m.category_id = c.id 
              WHERE m.featured = 1 
              ORDER BY m.created_at DESC 
              LIMIT ?";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $limit);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $movies = [];
    while ($movie = mysqli_fetch_assoc($result)) {
        // Format movie data
        $movies[] = format_movie_data($movie);
    }
    
    // Return response
    api_response([
        'success' => true,
        'data' => [
            'movies' => $movies
        ]
    ]);
}

// Handle popular movies
function handle_popular_movies($request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get query parameters
    $limit = isset($request['params']['limit']) ? (int)$request['params']['limit'] : 10;
    
    // Validate parameters
    if ($limit < 1 || $limit > 50) $limit = 10;
    
    // Get popular movies (based on views)
    $query = "SELECT m.*, c.name as category_name 
              FROM movies m 
              LEFT JOIN categories c ON m.category_id = c.id 
              ORDER BY m.views DESC 
              LIMIT ?";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $limit);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $movies = [];
    while ($movie = mysqli_fetch_assoc($result)) {
        // Format movie data
        $movies[] = format_movie_data($movie);
    }
    
    // Return response
    api_response([
        'success' => true,
        'data' => [
            'movies' => $movies
        ]
    ]);
}

// Handle latest movies
function handle_latest_movies($request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get query parameters
    $limit = isset($request['params']['limit']) ? (int)$request['params']['limit'] : 10;
    
    // Validate parameters
    if ($limit < 1 || $limit > 50) $limit = 10;
    
    // Get latest movies
    $query = "SELECT m.*, c.name as category_name 
              FROM movies m 
              LEFT JOIN categories c ON m.category_id = c.id 
              ORDER BY m.created_at DESC 
              LIMIT ?";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $limit);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $movies = [];
    while ($movie = mysqli_fetch_assoc($result)) {
        // Format movie data
        $movies[] = format_movie_data($movie);
    }
    
    // Return response
    api_response([
        'success' => true,
        'data' => [
            'movies' => $movies
        ]
    ]);
}

// Handle movie categories
function handle_movie_categories($request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get categories for movies
    $query = "SELECT c.*, COUNT(m.id) as movie_count 
              FROM categories c 
              LEFT JOIN movies m ON c.id = m.category_id 
              WHERE c.type IN ('movie', 'both') 
              GROUP BY c.id 
              ORDER BY c.name ASC";
    
    $result = mysqli_query($conn, $query);
    
    $categories = [];
    while ($category = mysqli_fetch_assoc($result)) {
        $categories[] = [
            'id' => (int)$category['id'],
            'name' => $category['name'],
            'slug' => $category['slug'],
            'movie_count' => (int)$category['movie_count']
        ];
    }
    
    // Return response
    api_response([
        'success' => true,
        'data' => [
            'categories' => $categories
        ]
    ]);
}

// Handle get movie by ID
function handle_get_movie($movie_id, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get movie details
    $query = "SELECT m.*, c.name as category_name 
              FROM movies m 
              LEFT JOIN categories c ON m.category_id = c.id 
              WHERE m.id = ?";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $movie_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($result) === 0) {
        api_error('Movie not found', 404);
    }
    
    $movie = mysqli_fetch_assoc($result);
    
    // Get download links
    $download_links_query = "SELECT * FROM download_links 
                            WHERE content_type = 'movie' AND content_id = ? 
                            ORDER BY quality, server_name";
    
    $download_stmt = mysqli_prepare($conn, $download_links_query);
    mysqli_stmt_bind_param($download_stmt, 'i', $movie_id);
    mysqli_stmt_execute($download_stmt);
    $download_result = mysqli_stmt_get_result($download_stmt);
    
    $download_links = [];
    while ($link = mysqli_fetch_assoc($download_result)) {
        $download_links[] = [
            'id' => (int)$link['id'],
            'quality' => $link['quality'],
            'size' => $link['size'],
            'server_name' => $link['server_name'],
            'link_type' => $link['link_type'],
            'is_premium' => (bool)$link['is_premium'],
            'link_url' => is_premium_content($link) ? null : $link['link_url']
        ];
    }
    
    // Get streaming links
    $streaming_links_query = "SELECT * FROM streaming_links 
                             WHERE content_type = 'movie' AND content_id = ? 
                             ORDER BY quality, server_name";
    
    $streaming_stmt = mysqli_prepare($conn, $streaming_links_query);
    mysqli_stmt_bind_param($streaming_stmt, 'i', $movie_id);
    mysqli_stmt_execute($streaming_stmt);
    $streaming_result = mysqli_stmt_get_result($streaming_stmt);
    
    $streaming_links = [];
    while ($link = mysqli_fetch_assoc($streaming_result)) {
        $streaming_links[] = [
            'id' => (int)$link['id'],
            'quality' => $link['quality'],
            'server_name' => $link['server_name'],
            'is_premium' => (bool)$link['is_premium'],
            'stream_url' => is_premium_content($link) ? null : $link['stream_url']
        ];
    }
    
    // Get related movies
    $related_query = "SELECT m.*, c.name as category_name 
                     FROM movies m 
                     LEFT JOIN categories c ON m.category_id = c.id 
                     WHERE m.category_id = ? AND m.id != ? 
                     ORDER BY RAND() 
                     LIMIT 6";
    
    $related_stmt = mysqli_prepare($conn, $related_query);
    mysqli_stmt_bind_param($related_stmt, 'ii', $movie['category_id'], $movie_id);
    mysqli_stmt_execute($related_stmt);
    $related_result = mysqli_stmt_get_result($related_stmt);
    
    $related_movies = [];
    while ($related = mysqli_fetch_assoc($related_result)) {
        $related_movies[] = format_movie_data($related);
    }
    
    // Increment view count
    $update_query = "UPDATE movies SET views = views + 1 WHERE id = ?";
    $update_stmt = mysqli_prepare($conn, $update_query);
    mysqli_stmt_bind_param($update_stmt, 'i', $movie_id);
    mysqli_stmt_execute($update_stmt);
    
    // Format movie data with additional details
    $movie_data = format_movie_data($movie);
    $movie_data['download_links'] = $download_links;
    $movie_data['streaming_links'] = $streaming_links;
    $movie_data['related_movies'] = $related_movies;
    
    // Return response
    api_response([
        'success' => true,
        'data' => $movie_data
    ]);
}

// Helper function to format movie data
function format_movie_data($movie) {
    return [
        'id' => (int)$movie['id'],
        'title' => $movie['title'],
        'slug' => $movie['slug'] ?? slugify($movie['title']),
        'description' => $movie['description'],
        'poster' => format_image_url($movie['poster']),
        'backdrop' => format_image_url($movie['backdrop']),
        'trailer' => $movie['trailer'],
        'release_year' => (int)$movie['release_year'],
        'duration' => $movie['duration'],
        'quality' => $movie['quality'],
        'rating' => (float)$movie['rating'],
        'category_id' => (int)$movie['category_id'],
        'category_name' => $movie['category_name'],
        'language' => $movie['language'] ?? 'Unknown',
        'views' => (int)$movie['views'],
        'featured' => (bool)$movie['featured'],
        'premium_only' => (bool)$movie['premium_only'],
        'created_at' => $movie['created_at']
    ];
}

// Helper function to format image URL
function format_image_url($image) {
    if (empty($image)) {
        return null;
    }
    
    if (strpos($image, 'http') === 0) {
        // External URL (TMDB)
        return $image;
    } elseif (file_exists('../uploads/' . $image)) {
        // Local file
        return SITE_URL . '/uploads/' . $image;
    } else {
        // Try TMDB path
        return 'https://image.tmdb.org/t/p/w500' . $image;
    }
}

// Helper function to check if content is premium and user has access
function is_premium_content($content) {
    if (!(bool)$content['is_premium']) {
        return false; // Not premium content
    }
    
    // Check if user is authenticated and has premium access
    $user = get_authenticated_user();
    if (!$user) {
        return true; // Premium content but user not authenticated
    }
    
    // Check if user has premium access
    $user_id = $user['user_id'];
    global $conn;
    
    $query = "SELECT is_premium, premium_expires FROM users WHERE id = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $user_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($result) === 0) {
        return true; // User not found
    }
    
    $user_data = mysqli_fetch_assoc($result);
    
    if (!(bool)$user_data['is_premium']) {
        return true; // User doesn't have premium access
    }
    
    // Check if premium has expired
    if ($user_data['premium_expires'] && strtotime($user_data['premium_expires']) < time()) {
        return true; // Premium has expired
    }
    
    return false; // User has premium access
}

// Helper function to create slug
function slugify($text) {
    // Replace non letter or digits by -
    $text = preg_replace('~[^\pL\d]+~u', '-', $text);
    
    // Transliterate
    $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);
    
    // Remove unwanted characters
    $text = preg_replace('~[^-\w]+~', '', $text);
    
    // Trim
    $text = trim($text, '-');
    
    // Remove duplicate -
    $text = preg_replace('~-+~', '-', $text);
    
    // Lowercase
    $text = strtolower($text);
    
    if (empty($text)) {
        return 'n-a';
    }
    
    return $text;
}
