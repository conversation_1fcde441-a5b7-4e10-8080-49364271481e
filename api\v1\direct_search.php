<?php
// Include direct config file
require_once '../direct_config.php';

// Get search query
$query = isset($_GET['q']) ? $_GET['q'] : '';

if (empty($query)) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Search query is required',
        'data' => null
    ]);
    exit;
}

// Search movies without status filter
$movies_query = "SELECT m.*, c.name as category_name
                FROM movies m
                LEFT JOIN categories c ON m.category_id = c.id
                WHERE (m.title LIKE ? OR m.description LIKE ?)
                ORDER BY m.id DESC
                LIMIT 20";

$search_term = "%$query%";
$stmt = mysqli_prepare($conn, $movies_query);
mysqli_stmt_bind_param($stmt, 'ss', $search_term, $search_term);
mysqli_stmt_execute($stmt);
$movies_result = mysqli_stmt_get_result($stmt);

$movies = [];
while ($movie = mysqli_fetch_assoc($movies_result)) {
    $movies[] = [
        'id' => (int)$movie['id'],
        'title' => $movie['title'],
        'poster' => $movie['poster'] ? (strpos($movie['poster'], 'http') === 0 ? $movie['poster'] : SITE_URL . '/uploads/' . $movie['poster']) : '',
        'release_year' => (int)$movie['release_year'],
        'rating' => (float)$movie['rating'],
        'category_name' => $movie['category_name'],
        'premium_only' => (bool)$movie['premium_only']
    ];
}

// Search TV shows without status filter
$tvshows_query = "SELECT t.*, c.name as category_name
                 FROM tvshows t
                 LEFT JOIN categories c ON t.category_id = c.id
                 WHERE (t.title LIKE ? OR t.description LIKE ?)
                 ORDER BY t.id DESC
                 LIMIT 20";

$stmt = mysqli_prepare($conn, $tvshows_query);
mysqli_stmt_bind_param($stmt, 'ss', $search_term, $search_term);
mysqli_stmt_execute($stmt);
$tvshows_result = mysqli_stmt_get_result($stmt);

$tvshows = [];
while ($tvshow = mysqli_fetch_assoc($tvshows_result)) {
    $tvshows[] = [
        'id' => (int)$tvshow['id'],
        'title' => $tvshow['title'],
        'poster' => $tvshow['poster'] ? (strpos($tvshow['poster'], 'http') === 0 ? $tvshow['poster'] : SITE_URL . '/uploads/' . $tvshow['poster']) : '',
        'start_year' => (int)$tvshow['start_year'],
        'seasons' => (int)$tvshow['seasons'],
        'rating' => (float)$tvshow['rating'],
        'category_name' => $tvshow['category_name'],
        'premium_only' => (bool)$tvshow['premium_only']
    ];
}

// Return search results
http_response_code(200);
echo json_encode([
    'success' => true,
    'message' => 'Success',
    'data' => [
        'query' => $query,
        'movies' => $movies,
        'tvshows' => $tvshows
    ]
]);
?>
