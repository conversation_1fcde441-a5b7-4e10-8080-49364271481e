import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cinepix_app/constants/app_constants.dart';
import 'package:cinepix_app/controllers/movie_controller.dart';
import 'package:cinepix_app/controllers/auth_controller.dart';
import 'package:cinepix_app/controllers/watch_history_controller.dart';
import 'package:cinepix_app/controllers/review_controller.dart';
import 'package:cinepix_app/models/movie.dart';
import 'package:cinepix_app/models/download_link.dart';
import 'package:cinepix_app/views/video_player_screen.dart';
import 'package:cinepix_app/widgets/download_link_card.dart';
import 'package:cinepix_app/widgets/review_list.dart';

class MovieDetailsScreen extends StatefulWidget {
  final int movieId;

  const MovieDetailsScreen({
    super.key,
    required this.movieId,
  });

  @override
  State<MovieDetailsScreen> createState() => _MovieDetailsScreenState();
}

class _MovieDetailsScreenState extends State<MovieDetailsScreen> {
  final MovieController _movieController = Get.find<MovieController>();
  final AuthController _authController = Get.find<AuthController>();
  final WatchHistoryController _watchHistoryController =
      Get.find<WatchHistoryController>();
  final ReviewController _reviewController = Get.put(ReviewController());

  @override
  void initState() {
    super.initState();
    _loadMovieDetails();
  }

  Future<void> _loadMovieDetails() async {
    await _movieController.loadMovieDetails(widget.movieId);
  }

  @override
  void dispose() {
    _movieController.clearCurrentMovie();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        if (_movieController.isLoadingDetails.value) {
          return const Center(child: CircularProgressIndicator());
        }

        final movie = _movieController.currentMovie.value;

        if (movie == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.grey[600],
                ),
                const SizedBox(height: 16),
                Text(
                  'Movie not found',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () => Get.back(),
                  child: const Text('Go Back'),
                ),
              ],
            ),
          );
        }

        return CustomScrollView(
          slivers: [
            // App bar with movie banner
            SliverAppBar(
              expandedHeight: 250,
              pinned: true,
              flexibleSpace: FlexibleSpaceBar(
                background: Stack(
                  fit: StackFit.expand,
                  children: [
                    // Movie banner
                    CachedNetworkImage(
                      imageUrl: movie.banner,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: Colors.grey[850],
                        child: const Center(
                          child: CircularProgressIndicator(),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        color: Colors.grey[850],
                        child: const Icon(Icons.error),
                      ),
                    ),

                    // Gradient overlay
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withOpacity(0.7),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              leading: Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.5),
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () => Get.back(),
                ),
              ),
            ),

            // Movie details
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title and rating
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Movie poster
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: CachedNetworkImage(
                            imageUrl: movie.poster,
                            width: 120,
                            height: 180,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => Container(
                              width: 120,
                              height: 180,
                              color: Colors.grey[850],
                              child: const Center(
                                child: CircularProgressIndicator(),
                              ),
                            ),
                            errorWidget: (context, url, error) => Container(
                              width: 120,
                              height: 180,
                              color: Colors.grey[850],
                              child: const Icon(Icons.error),
                            ),
                          ),
                        ),

                        const SizedBox(width: 16),

                        // Title, year, duration, category
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                movie.title,
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Text(
                                    movie.releaseYear.toString(),
                                    style: TextStyle(
                                      color: Colors.grey[400],
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Text(
                                    '${movie.duration} min',
                                    style: TextStyle(
                                      color: Colors.grey[400],
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: AppConstants.surfaceColor,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  movie.categoryName,
                                  style: TextStyle(
                                    color: AppConstants.primaryColor,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 16),
                              Row(
                                children: [
                                  RatingBarIndicator(
                                    rating: movie.rating /
                                        2, // Convert 10-scale to 5-scale
                                    itemBuilder: (context, index) => const Icon(
                                      Icons.star,
                                      color: Colors.amber,
                                    ),
                                    itemCount: 5,
                                    itemSize: 20.0,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    movie.rating.toStringAsFixed(1),
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),

                              // Premium badge
                              if (movie.premiumOnly)
                                Container(
                                  margin: const EdgeInsets.only(top: 16),
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: AppConstants.primaryColor,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: const Text(
                                    'PREMIUM',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Description
                    const Text(
                      'Description',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      movie.description,
                      style: TextStyle(
                        color: Colors.grey[300],
                        height: 1.5,
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Download/Stream links
                    const Text(
                      'Download & Stream Links',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    Obx(() {
                      final links = _movieController.downloadLinks;

                      if (links.isEmpty) {
                        return Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppConstants.surfaceColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Center(
                            child: Text(
                              'No download links available',
                              style: TextStyle(
                                color: Colors.grey,
                              ),
                            ),
                          ),
                        );
                      }

                      return Column(
                        children: links.map((link) {
                          return DownloadLinkCard(
                            link: link,
                            title: movie.title,
                            onPlayTap: () => _playVideo(movie, link),
                            isPremiumUser: _authController.isPremium.value,
                          );
                        }).toList(),
                      );
                    }),

                    const SizedBox(height: 24),

                    // Reviews section
                    ReviewList(
                      contentType: 'movie',
                      contentId: movie.id,
                    ),

                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  void _playVideo(Movie movie, DownloadLink link) {
    if (movie.premiumOnly && !_authController.isPremium.value) {
      _showPremiumDialog();
      return;
    }

    // Update watch history when playing video
    _watchHistoryController.updateWatchHistory(
      contentType: 'movie',
      contentId: movie.id,
      title: movie.title,
      posterUrl: movie.poster,
      backdropUrl: movie.banner, // Use banner as backdrop
      duration: movie.duration * 60, // Convert minutes to seconds
      watchedPosition: 0, // Will be updated by the video player
    );

    Get.to(() => VideoPlayerScreen(
          title: movie.title,
          downloadLink: link,
          contentType: 'movie',
          contentId: movie.id,
          backdropUrl: movie.banner,
        ));
  }

  void _showPremiumDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Premium Content'),
        content: const Text(
            'This content is only available for premium users. Please upgrade to access premium content.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to premium page
              // Navigator.pushNamed(context, '/premium');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
            ),
            child: const Text(
              'Upgrade',
              style: TextStyle(
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
