<?php
// Direct Database Configuration

// Database connection
$db_host = 'localhost';
$db_user = 'tipsbdxy_4525';
$db_pass = '@mdsrabon13';
$db_name = 'tipsbdxy_4525';

// Create connection
$conn = mysqli_connect($db_host, $db_user, $db_pass, $db_name);

// Check connection
if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// Site settings
define('SITE_NAME', 'CinePix');
define('SITE_URL', 'https://cinepix.top');
define('SITE_EMAIL', '<EMAIL>');

// API Settings
define('API_VERSION', 'v1');
define('JWT_SECRET', 'cinepix_secure_jwt_secret_key_2025'); // Secure JWT secret key
define('JWT_EXPIRY', 604800); // 7 days in seconds

// CORS Headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization, API-Key');
header('Content-Type: application/json; charset=UTF-8');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// API Response Functions
function api_response($data, $status_code = 200, $message = 'Success') {
    http_response_code($status_code);
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => $data,
        'meta' => isset($data['meta']) ? $data['meta'] : null
    ]);
    exit;
}

function api_error($message, $status_code = 400) {
    http_response_code($status_code);
    echo json_encode([
        'success' => false,
        'message' => $message,
        'data' => null,
        'meta' => null
    ]);
    exit;
}

// JWT Functions
function generate_jwt($user_id, $username, $role) {
    $issued_at = time();
    $expiry = $issued_at + JWT_EXPIRY;

    $payload = [
        'iss' => SITE_URL, // Issuer
        'aud' => SITE_URL, // Audience
        'iat' => $issued_at, // Issued at
        'exp' => $expiry, // Expiry
        'user_id' => $user_id,
        'username' => $username,
        'role' => $role
    ];

    $header = [
        'alg' => 'HS256',
        'typ' => 'JWT'
    ];

    $header_encoded = base64_encode(json_encode($header));
    $payload_encoded = base64_encode(json_encode($payload));

    $signature = hash_hmac('sha256', "$header_encoded.$payload_encoded", JWT_SECRET, true);
    $signature_encoded = base64_encode($signature);

    return "$header_encoded.$payload_encoded.$signature_encoded";
}

function verify_jwt($token) {
    $token_parts = explode('.', $token);

    if (count($token_parts) !== 3) {
        return false;
    }

    list($header_encoded, $payload_encoded, $signature_encoded) = $token_parts;

    $signature = base64_decode($signature_encoded);
    $expected_signature = hash_hmac('sha256', "$header_encoded.$payload_encoded", JWT_SECRET, true);

    if (!hash_equals($expected_signature, $signature)) {
        return false;
    }

    $payload = json_decode(base64_decode($payload_encoded), true);

    if ($payload['exp'] < time()) {
        return false; // Token expired
    }

    return $payload;
}

// Function to check if user is authenticated
function is_authenticated() {
    global $token;

    if (!$token) {
        return false;
    }

    $payload = verify_jwt($token);
    return $payload !== false;
}

// Function to get authenticated user data
function get_authenticated_user() {
    global $token;

    if (!$token) {
        return null;
    }

    return verify_jwt($token);
}

// Function to check if user has admin role
function is_admin() {
    $user = get_authenticated_user();

    if (!$user) {
        return false;
    }

    return $user['role'] === 'admin';
}

// Get JWT token from Authorization header
$auth_header = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
$token = null;

if (preg_match('/Bearer\s(\S+)/', $auth_header, $matches)) {
    $token = $matches[1];
}
?>
