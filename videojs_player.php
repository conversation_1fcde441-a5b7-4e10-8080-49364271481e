<?php
// Include config file to get site URL and other settings
require_once 'includes/config.php';

// Get video URL from query parameter
$video_url = isset($_GET['url']) ? $_GET['url'] : '';
$video_title = isset($_GET['title']) ? $_GET['title'] : 'Video Player';
$poster = isset($_GET['poster']) ? $_GET['poster'] : '';
$quality = isset($_GET['quality']) ? $_GET['quality'] : '';
$language = isset($_GET['language']) ? $_GET['language'] : '';
$server = isset($_GET['server']) ? $_GET['server'] : '';
$is_premium = isset($_GET['premium']) && $_GET['premium'] == '1';

// Determine video type based on file extension
$file_extension = strtolower(pathinfo(parse_url($video_url, PHP_URL_PATH), PATHINFO_EXTENSION));
$video_type = 'video/mp4'; // Default video type

if ($file_extension == 'm3u8') {
    $video_type = 'application/x-mpegURL';
} elseif ($file_extension == 'mpd') {
    $video_type = 'application/dash+xml';
} elseif ($file_extension == 'mkv') {
    $video_type = 'video/x-matroska';
} elseif ($file_extension == 'webm') {
    $video_type = 'video/webm';
}

// Generate a unique cache key for this video
$cache_key = md5($video_url);

// Validate URL
if (empty($video_url)) {
    header('Location: ' . SITE_URL);
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $video_title; ?> - Advanced Player</title>

    <!-- Preload critical resources -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" as="style">
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style">
    <link rel="preload" href="https://vjs.zencdn.net/8.10.0/video-js.css" as="style">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/videojs-seek-buttons@3.0.1/dist/videojs-seek-buttons.css" as="style">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Video.js CSS -->
    <link href="https://vjs.zencdn.net/8.10.0/video-js.css" rel="stylesheet" />
    
    <!-- Video.js Seek Buttons plugin CSS -->
    <link href="https://cdn.jsdelivr.net/npm/videojs-seek-buttons@3.0.1/dist/videojs-seek-buttons.css" rel="stylesheet">

    <style>
        body {
            background-color: #111;
            color: #fff;
            padding: 0;
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .player-container {
            background-color: #000;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            margin-bottom: 20px;
        }
        .video-container {
            position: relative;
            width: 100%;
            border-radius: 8px 8px 0 0;
            overflow: hidden;
        }
        .video-info {
            padding: 20px;
            background-color: #222;
            border-radius: 0 0 8px 8px;
        }
        h1 {
            font-size: 24px;
            margin-bottom: 15px;
        }
        .btn-back {
            margin-bottom: 20px;
            display: inline-block;
        }
        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        .action-btn {
            background-color: #333;
            color: #fff;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
        }
        .action-btn:hover {
            background-color: #444;
            color: #fff;
        }
        .action-btn.primary {
            background-color: #e50914;
        }
        .action-btn.primary:hover {
            background-color: #f40612;
        }
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 10;
        }
        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #e50914;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .loading-text {
            margin-top: 15px;
            font-size: 16px;
            color: #fff;
        }
        .video-js {
            width: 100%;
            height: 0;
            padding-top: 56.25%; /* 16:9 Aspect Ratio */
        }
        .vjs-fluid {
            padding-top: 56.25%;
        }
        .vjs-big-play-button {
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
        }
        .vjs-theme-custom {
            --vjs-theme-primary: #e50914;
        }
        .vjs-theme-custom .vjs-big-play-button {
            background-color: rgba(229, 9, 20, 0.7);
            border: none;
            border-radius: 50%;
            width: 80px;
            height: 80px;
        }
        .vjs-theme-custom .vjs-big-play-button:hover {
            background-color: rgba(229, 9, 20, 0.9);
        }
        .vjs-theme-custom .vjs-control-bar {
            background-color: rgba(0, 0, 0, 0.7);
        }
        .vjs-theme-custom .vjs-button:hover {
            color: var(--vjs-theme-primary);
        }
        .vjs-theme-custom .vjs-play-progress {
            background-color: var(--vjs-theme-primary);
        }
        .vjs-theme-custom .vjs-volume-level {
            background-color: var(--vjs-theme-primary);
        }
        .vjs-theme-custom .vjs-menu-button-popup .vjs-menu {
            width: 20em;
        }
        .vjs-theme-custom .vjs-menu-content {
            background-color: rgba(0, 0, 0, 0.9);
        }
        .vjs-theme-custom .vjs-menu-item:hover,
        .vjs-theme-custom .vjs-menu-item.vjs-selected {
            background-color: var(--vjs-theme-primary);
            color: #fff;
        }
        .resume-notification {
            position: absolute;
            bottom: 70px;
            left: 0;
            right: 0;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            display: none;
            z-index: 2;
            text-align: center;
        }
        .resume-notification .resume-buttons {
            margin-top: 10px;
            display: flex;
            justify-content: center;
            gap: 10px;
        }
        .resume-notification button {
            background-color: #e50914;
            color: white;
            border: none;
            padding: 5px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        .resume-notification button.resume-cancel {
            background-color: #333;
        }
        .audio-track-notification {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            z-index: 2;
            display: none;
            transition: opacity 0.5s;
        }
        .audio-track-notification.fade-out {
            opacity: 0;
        }
        .quality-selector {
            display: none;
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 2;
            background-color: rgba(0, 0, 0, 0.8);
            border-radius: 4px;
            padding: 10px;
        }
        .quality-selector select {
            background-color: #333;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
        }
        @media (max-width: 768px) {
            .video-container {
                border-radius: 0;
            }
            .player-container {
                border-radius: 0;
                margin-left: -20px;
                margin-right: -20px;
                width: calc(100% + 40px);
            }
            .container {
                padding: 10px;
            }
            .btn-back {
                margin-bottom: 10px;
            }
            h1 {
                font-size: 18px;
            }
            .action-buttons {
                flex-direction: column;
            }
            .action-btn {
                width: 100%;
                justify-content: center;
            }
        }
        /* Landscape mode for mobile */
        @media (max-height: 500px) and (orientation: landscape) {
            .container {
                padding: 0;
            }
            .btn-back, .video-info {
                display: none;
            }
            .player-container {
                margin: 0;
                border-radius: 0;
            }
            .video-container {
                border-radius: 0;
            }
            body {
                overflow: hidden;
            }
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <a href="<?php echo SITE_URL; ?>" class="btn btn-outline-light btn-back">
            <i class="fas fa-arrow-left"></i> Back to Home
        </a>

        <div class="player-container">
            <div class="video-container">
                <!-- Loading Overlay -->
                <div id="loading-overlay" class="loading-overlay">
                    <div class="spinner"></div>
                    <div class="loading-text">Loading video...</div>
                </div>

                <!-- Resume Notification -->
                <div id="resume-notification" class="resume-notification">
                    <div>Would you like to resume from where you left off?</div>
                    <div class="resume-buttons">
                        <button id="resume-yes">Yes, Resume</button>
                        <button id="resume-cancel" class="resume-cancel">No, Start Over</button>
                    </div>
                </div>

                <!-- Audio Track Notification -->
                <div id="audio-track-notification" class="audio-track-notification">
                    Audio Track: <span id="current-audio-track">Default</span>
                </div>

                <!-- Quality Selector for HLS -->
                <div id="quality-selector" class="quality-selector">
                    <select id="quality-select">
                        <option value="auto">Auto</option>
                    </select>
                </div>

                <!-- Video.js Player -->
                <video id="video-player" class="video-js vjs-theme-custom vjs-big-play-centered vjs-fluid" controls preload="auto" poster="<?php echo $poster; ?>" data-setup='{}'>
                    <source src="<?php echo $video_url; ?>" type="<?php echo $video_type; ?>">
                    <p class="vjs-no-js">
                        To view this video please enable JavaScript, and consider upgrading to a web browser that
                        <a href="https://videojs.com/html5-video-support/" target="_blank">supports HTML5 video</a>
                    </p>
                </video>
            </div>

            <div class="video-info">
                <h1><?php echo $video_title; ?></h1>
                <div class="d-flex flex-wrap gap-2 mb-3">
                    <?php if (!empty($quality)): ?>
                    <span class="badge bg-danger"><?php echo $quality; ?></span>
                    <?php endif; ?>
                    
                    <?php if (!empty($language)): ?>
                    <span class="badge bg-secondary"><?php echo $language; ?></span>
                    <?php endif; ?>
                    
                    <?php if (!empty($server)): ?>
                    <span class="badge bg-info"><?php echo $server; ?></span>
                    <?php endif; ?>
                    
                    <?php if ($is_premium): ?>
                    <span class="badge bg-warning text-dark"><i class="fas fa-crown me-1"></i>Premium</span>
                    <?php endif; ?>
                </div>

                <div class="action-buttons">
                    <button id="restart-player" class="action-btn">
                        <i class="fas fa-redo"></i> Restart Player
                    </button>
                    <button id="copy-url" class="action-btn" data-url="<?php echo SITE_URL; ?>/videojs_player.php?url=<?php echo urlencode($video_url); ?>&title=<?php echo urlencode($video_title); ?>">
                        <i class="fas fa-copy"></i> Copy Player URL
                    </button>
                    <a href="<?php echo $video_url; ?>" class="action-btn primary" download target="_blank">
                        <i class="fas fa-download"></i> Download Video
                    </a>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Playback Tips:</strong> 
                    <ul class="mb-0 mt-2">
                        <li>Use J and L keys to skip backward and forward 10 seconds</li>
                        <li>Use K key to play/pause</li>
                        <li>Use M key to mute/unmute</li>
                        <li>Use F key for fullscreen</li>
                        <li>If the video stutters, try pausing for a few seconds to allow buffering</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Video.js -->
    <script src="https://vjs.zencdn.net/8.10.0/video.min.js"></script>
    
    <!-- HLS.js (for HLS support) -->
    <script src="https://cdn.jsdelivr.net/npm/hls.js@1.4.14/dist/hls.min.js"></script>
    
    <!-- Video.js HLS Quality Selector -->
    <script src="https://cdn.jsdelivr.net/npm/videojs-contrib-quality-levels@3.0.0/dist/videojs-contrib-quality-levels.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/videojs-hls-quality-selector@1.1.4/dist/videojs-hls-quality-selector.min.js"></script>
    
    <!-- Video.js Seek Buttons plugin -->
    <script src="https://cdn.jsdelivr.net/npm/videojs-seek-buttons@3.0.1/dist/videojs-seek-buttons.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get elements
            const videoElement = document.getElementById('video-player');
            const loadingOverlay = document.getElementById('loading-overlay');
            const resumeNotification = document.getElementById('resume-notification');
            const resumeYesBtn = document.getElementById('resume-yes');
            const resumeCancelBtn = document.getElementById('resume-cancel');
            const restartPlayerBtn = document.getElementById('restart-player');
            const copyUrlBtn = document.getElementById('copy-url');
            const audioTrackNotification = document.getElementById('audio-track-notification');
            const currentAudioTrack = document.getElementById('current-audio-track');
            const qualitySelector = document.getElementById('quality-selector');
            const qualitySelect = document.getElementById('quality-select');
            
            // Video URL and cache key
            const videoUrl = '<?php echo $video_url; ?>';
            const videoType = '<?php echo $video_type; ?>';
            const cacheKey = '<?php echo $cache_key; ?>';
            
            // Initialize Video.js
            const player = videojs('video-player', {
                controls: true,
                autoplay: false,
                preload: 'auto',
                fluid: true,
                playbackRates: [0.5, 0.75, 1, 1.25, 1.5, 1.75, 2],
                controlBar: {
                    children: [
                        'playToggle',
                        'volumePanel',
                        'currentTimeDisplay',
                        'timeDivider',
                        'durationDisplay',
                        'progressControl',
                        'liveDisplay',
                        'remainingTimeDisplay',
                        'customControlSpacer',
                        'playbackRateMenuButton',
                        'chaptersButton',
                        'descriptionsButton',
                        'subsCapsButton',
                        'audioTrackButton',
                        'qualitySelector',
                        'pictureInPictureToggle',
                        'fullscreenToggle'
                    ]
                },
                html5: {
                    vhs: {
                        overrideNative: true,
                        limitRenditionByPlayerDimensions: false,
                        smoothQualityChange: true,
                        fastQualityChange: true
                    },
                    nativeAudioTracks: false,
                    nativeVideoTracks: false
                },
                plugins: {
                    seekButtons: {
                        forward: 10,
                        back: 10
                    }
                }
            });
            
            // Add seek buttons
            player.seekButtons();
            
            // Check if it's an HLS stream
            if (videoType === 'application/x-mpegURL' && Hls.isSupported()) {
                // Setup HLS
                const hls = new Hls({
                    maxBufferLength: 90,
                    maxMaxBufferLength: 180,
                    maxBufferSize: 120 * 1000 * 1000,
                    maxBufferHole: 0.5,
                    lowLatencyMode: false,
                    startLevel: -1,
                    abrEwmaDefaultEstimate: 1000000,
                    abrBandWidthFactor: 0.95,
                    abrBandWidthUpFactor: 0.7,
                    abrMaxWithRealBitrate: true,
                    manifestLoadingTimeOut: 15000,
                    manifestLoadingMaxRetry: 6,
                    manifestLoadingRetryDelay: 1000,
                    manifestLoadingMaxRetryTimeout: 8000,
                    levelLoadingTimeOut: 15000,
                    levelLoadingMaxRetry: 6,
                    fragLoadingTimeOut: 30000,
                    fragLoadingMaxRetry: 8
                });
                
                hls.loadSource(videoUrl);
                hls.attachMedia(videoElement);
                
                // Add quality selector for HLS
                player.hlsQualitySelector();
                
                // Show quality selector
                qualitySelector.style.display = 'flex';
                
                // Handle quality level changes
                hls.on(Hls.Events.LEVEL_SWITCHED, function(event, data) {
                    const level = hls.levels[data.level];
                    if (level) {
                        const height = level.height;
                        const quality = height + 'p';
                        // Update quality selector
                        for (let i = 0; i < qualitySelect.options.length; i++) {
                            if (qualitySelect.options[i].text.includes(quality)) {
                                qualitySelect.selectedIndex = i;
                                break;
                            }
                        }
                    }
                });
                
                // Populate quality selector
                hls.on(Hls.Events.MANIFEST_PARSED, function(event, data) {
                    // Clear existing options except Auto
                    while (qualitySelect.options.length > 1) {
                        qualitySelect.remove(1);
                    }
                    
                    // Add quality options
                    const levels = hls.levels;
                    for (let i = 0; i < levels.length; i++) {
                        const level = levels[i];
                        const option = document.createElement('option');
                        option.value = i;
                        option.text = level.height + 'p';
                        qualitySelect.add(option);
                    }
                });
                
                // Handle quality selection
                qualitySelect.addEventListener('change', function() {
                    const value = qualitySelect.value;
                    if (value === 'auto') {
                        hls.currentLevel = -1; // Auto
                    } else {
                        hls.currentLevel = parseInt(value);
                    }
                });
            }
            
            // Check for saved playback position
            const savedTime = localStorage.getItem('video_time_' + cacheKey);
            if (savedTime && parseFloat(savedTime) > 10) {
                // Show resume notification after player is ready
                player.ready(function() {
                    resumeNotification.style.display = 'block';
                });
            }
            
            // Resume playback
            resumeYesBtn.addEventListener('click', function() {
                const savedTime = localStorage.getItem('video_time_' + cacheKey);
                if (savedTime) {
                    player.currentTime(parseFloat(savedTime));
                }
                resumeNotification.style.display = 'none';
                player.play();
            });
            
            // Start over
            resumeCancelBtn.addEventListener('click', function() {
                player.currentTime(0);
                resumeNotification.style.display = 'none';
                player.play();
            });
            
            // Restart player
            restartPlayerBtn.addEventListener('click', function() {
                player.reset();
                player.src({ src: videoUrl, type: videoType });
                player.load();
                player.play();
            });
            
            // Copy player URL
            copyUrlBtn.addEventListener('click', function() {
                const url = this.getAttribute('data-url');
                navigator.clipboard.writeText(url).then(function() {
                    alert('Player URL copied to clipboard!');
                }).catch(function(err) {
                    console.error('Could not copy text: ', err);
                });
            });
            
            // Hide loading overlay when video plays
            player.on('playing', function() {
                loadingOverlay.style.display = 'none';
            });
            
            // Save playback position periodically
            setInterval(() => {
                if (player.currentTime() > 0) {
                    localStorage.setItem('video_time_' + cacheKey, player.currentTime().toString());
                }
            }, 3000);
            
            // Save position on pause
            player.on('pause', function() {
                if (player.currentTime() > 0) {
                    localStorage.setItem('video_time_' + cacheKey, player.currentTime().toString());
                }
            });
            
            // Handle audio track changes
            player.on('audiotrackchange', function() {
                const tracks = player.audioTracks();
                for (let i = 0; i < tracks.length; i++) {
                    if (tracks[i].enabled) {
                        currentAudioTrack.textContent = tracks[i].label || 'Track ' + (i + 1);
                        audioTrackNotification.style.display = 'block';
                        audioTrackNotification.classList.remove('fade-out');
                        
                        // Hide notification after 3 seconds
                        setTimeout(function() {
                            audioTrackNotification.classList.add('fade-out');
                            setTimeout(function() {
                                audioTrackNotification.style.display = 'none';
                            }, 500);
                        }, 3000);
                        break;
                    }
                }
            });
            
            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Only if video player is in focus or we're in fullscreen
                if (document.activeElement === videoElement || document.fullscreenElement) {
                    // J key - seek backward 10 seconds
                    if (e.key === 'j' || e.key === 'J') {
                        player.currentTime(Math.max(0, player.currentTime() - 10));
                    }
                    
                    // L key - seek forward 10 seconds
                    if (e.key === 'l' || e.key === 'L') {
                        player.currentTime(Math.min(player.duration(), player.currentTime() + 10));
                    }
                    
                    // K key - play/pause
                    if (e.key === 'k' || e.key === 'K') {
                        if (player.paused()) {
                            player.play();
                        } else {
                            player.pause();
                        }
                    }
                    
                    // M key - mute/unmute
                    if (e.key === 'm' || e.key === 'M') {
                        player.muted(!player.muted());
                    }
                    
                    // F key - fullscreen
                    if (e.key === 'f' || e.key === 'F') {
                        if (player.isFullscreen()) {
                            player.exitFullscreen();
                        } else {
                            player.requestFullscreen();
                        }
                    }
                }
            });
            
            // Handle fullscreen changes for better mobile experience
            player.on('fullscreenchange', function() {
                if (player.isFullscreen()) {
                    // Try to force landscape orientation on mobile
                    if (screen.orientation && screen.orientation.lock) {
                        screen.orientation.lock('landscape').catch(function(error) {
                            // Silently fail if orientation locking is not supported or permission denied
                            console.log('Orientation lock failed: ', error);
                        });
                    }
                    
                    // Add a class to the body for fullscreen styling
                    document.body.classList.add('player-fullscreen-active');
                } else {
                    // Remove the class when exiting fullscreen
                    document.body.classList.remove('player-fullscreen-active');
                    
                    // Unlock orientation
                    if (screen.orientation && screen.orientation.unlock) {
                        screen.orientation.unlock();
                    }
                }
            });
            
            // Handle errors
            player.on('error', function() {
                console.error('Video.js Error:', player.error());
                loadingOverlay.style.display = 'none';
                
                // Update alert message
                const alertElement = document.querySelector('.alert-info');
                if (alertElement) {
                    alertElement.classList.remove('alert-info');
                    alertElement.classList.add('alert-danger');
                    alertElement.innerHTML = '<strong>Error:</strong> Failed to load the video. Please try again or download the file to play locally.';
                }
            });
        });
    </script>
</body>
</html>
