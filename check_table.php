<?php
// Include database connection
require_once 'includes/config.php';

// Check if the episode_links table exists
$check_table_query = "SHOW TABLES LIKE 'episode_links'";
$table_result = mysqli_query($conn, $check_table_query);

if (mysqli_num_rows($table_result) > 0) {
    echo "Table 'episode_links' exists.<br>";
    
    // Get table structure
    $describe_query = "DESCRIBE episode_links";
    $describe_result = mysqli_query($conn, $describe_query);
    
    echo "<h3>Table Structure:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = mysqli_fetch_assoc($describe_result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "Table 'episode_links' does not exist.";
}
?>
