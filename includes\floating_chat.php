<?php
/**
 * Floating Chat Widget
 * Include this in footer to show floating chat icon with message notifications
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Get session ID for tracking
$session_id = $_SESSION['tracking_session_id'] ?? session_id();

// Get unread message count for this user
function getUnreadMessageCount($session_id) {
    global $conn;
    
    if (!isset($conn) || !$conn) {
        return 0;
    }
    
    $query = "SELECT COUNT(*) as count FROM user_messages 
              WHERE session_id = ? AND message_type = 'admin' AND is_read = FALSE";
    
    // Use simple query for PHP compatibility
    $session_id_safe = mysqli_real_escape_string($conn, $session_id);
    $query = "SELECT COUNT(*) as count FROM user_messages
              WHERE session_id = '$session_id_safe' AND message_type = 'admin' AND is_read = FALSE";

    $result = mysqli_query($conn, $query);
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        return (int)$row['count'];
    }
    
    return 0;
}

$unread_count = getUnreadMessageCount($session_id);
?>

<!-- Floating Chat Widget -->
<div id="floating-chat" class="floating-chat">
    <div class="chat-toggle" onclick="toggleChat()">
        <i class="fas fa-comment-dots"></i>
        <?php if ($unread_count > 0): ?>
        <span class="chat-notification-badge" id="chat-badge"><?php echo $unread_count; ?></span>
        <?php endif; ?>
    </div>
    
    <div class="chat-window" id="chat-window" style="display: none;">
        <div class="chat-header">
            <div class="chat-title">
                <i class="fas fa-headset"></i>
                <span>সাপোর্ট চ্যাট</span>
            </div>
            <div class="chat-controls">
                <button class="chat-minimize" onclick="toggleChat()">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        
        <div class="chat-messages" id="chat-messages">
            <div class="welcome-message">
                <div class="message admin-message">
                    <div class="message-content">
                        <p>👋 স্বাগতম! আমরা আপনাকে সাহায্য করতে এখানে আছি।</p>
                        <small class="message-time"><?php echo date('H:i'); ?></small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="chat-input-area">
            <div class="input-group">
                <input type="text" id="chat-message-input" class="form-control" placeholder="আপনার মেসেজ লিখুন..." maxlength="500">
                <button class="btn btn-primary" onclick="sendChatMessage()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.floating-chat {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.chat-toggle {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    position: relative;
}

.chat-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0,0,0,0.4);
}

.chat-notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.chat-window {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 350px;
    height: 450px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.chat-minimize {
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: background 0.2s;
}

.chat-minimize:hover {
    background: rgba(255,255,255,0.2);
}

.chat-messages {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    background: #f8f9fa;
}

.message {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
}

.admin-message {
    justify-content: flex-start;
}

.user-message {
    justify-content: flex-end;
}

.message-content {
    max-width: 80%;
    padding: 10px 15px;
    border-radius: 15px;
    position: relative;
}

.admin-message .message-content {
    background: white;
    border: 1px solid #e9ecef;
    margin-left: 0;
    color: #000000;
}

.user-message .message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-right: 0;
}

.message-time {
    display: block;
    margin-top: 5px;
    opacity: 0.7;
    font-size: 11px;
}

.admin-message .message-time {
    color: #000000;
}

.admin-message .message-content p {
    color: #000000;
    margin: 0;
}

.chat-input-area {
    padding: 15px;
    background: white;
    border-top: 1px solid #e9ecef;
}

.chat-input-area .input-group {
    display: flex;
    gap: 10px;
}

.chat-input-area .form-control {
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 25px;
    padding: 10px 15px;
    font-size: 14px;
}

.chat-input-area .btn {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.welcome-message {
    text-align: center;
    margin-bottom: 20px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .floating-chat {
        bottom: 15px;
        right: 15px;
    }
    
    .chat-window {
        width: 300px;
        height: 400px;
    }
    
    .chat-toggle {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}

/* Hide on very small screens */
@media (max-width: 480px) {
    .chat-window {
        width: calc(100vw - 30px);
        right: -15px;
    }
}
</style>

<script>
let chatOpen = false;
let chatSessionId = '<?php echo $session_id; ?>';

function toggleChat() {
    const chatWindow = document.getElementById('chat-window');
    const chatToggle = document.querySelector('.chat-toggle');
    
    if (chatOpen) {
        chatWindow.style.display = 'none';
        chatToggle.innerHTML = '<i class="fas fa-comment-dots"></i>';
        chatOpen = false;
    } else {
        chatWindow.style.display = 'flex';
        chatToggle.innerHTML = '<i class="fas fa-times"></i>';
        chatOpen = true;
        
        // Load messages when opening chat
        loadChatMessages();
        
        // Mark messages as read
        markMessagesAsRead();
    }
}

function sendChatMessage() {
    const messageInput = document.getElementById('chat-message-input');
    const message = messageInput.value.trim();
    
    if (!message) return;
    
    // Add message to chat immediately
    addMessageToChat(message, 'user');
    messageInput.value = '';
    
    // Send to server
    const formData = new FormData();
    formData.append('action', 'send_message');
    formData.append('session_id', chatSessionId);
    formData.append('message', message);
    formData.append('message_type', 'user');
    
    fetch('<?php echo SITE_URL; ?>/includes/chat_handler.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            console.error('Failed to send message:', data.message);
        }
    })
    .catch(error => {
        console.error('Chat error:', error);
    });
}

function addMessageToChat(message, type) {
    const messagesContainer = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    const currentTime = new Date().toLocaleTimeString('bn-BD', {hour: '2-digit', minute: '2-digit'});
    
    messageDiv.className = `message ${type}-message`;
    messageDiv.innerHTML = `
        <div class="message-content">
            <p>${message}</p>
            <small class="message-time">${currentTime}</small>
        </div>
    `;
    
    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function loadChatMessages() {
    fetch(`<?php echo SITE_URL; ?>/includes/chat_handler.php?action=get_messages&session_id=${chatSessionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const messagesContainer = document.getElementById('chat-messages');
                // Keep welcome message and add new messages
                const welcomeMessage = messagesContainer.querySelector('.welcome-message');
                messagesContainer.innerHTML = '';
                if (welcomeMessage) {
                    messagesContainer.appendChild(welcomeMessage);
                }
                
                data.messages.forEach(msg => {
                    addMessageToChat(msg.message, msg.message_type);
                });
            }
        })
        .catch(error => console.error('Error loading messages:', error));
}

function markMessagesAsRead() {
    fetch('<?php echo SITE_URL; ?>/includes/chat_handler.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=mark_read&session_id=${chatSessionId}`
    })
    .then(() => {
        // Remove notification badge
        const badge = document.getElementById('chat-badge');
        if (badge) {
            badge.remove();
        }
    });
}

// Enter key to send message
document.addEventListener('DOMContentLoaded', function() {
    const messageInput = document.getElementById('chat-message-input');
    if (messageInput) {
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendChatMessage();
            }
        });
    }
});

// Check for new messages periodically
setInterval(function() {
    if (!chatOpen) {
        // Check for new messages and update badge
        fetch(`<?php echo SITE_URL; ?>/includes/chat_handler.php?action=get_unread_count&session_id=${chatSessionId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.count > 0) {
                    const chatToggle = document.querySelector('.chat-toggle');
                    let badge = document.getElementById('chat-badge');
                    
                    if (!badge) {
                        badge = document.createElement('span');
                        badge.id = 'chat-badge';
                        badge.className = 'chat-notification-badge';
                        chatToggle.appendChild(badge);
                    }
                    
                    badge.textContent = data.count;
                }
            });
    }
}, 10000); // Check every 10 seconds
</script>
