<?php
// API Authentication Endpoints

// Get the request
global $request;

// Handle different endpoints based on the path parts
$action = $request['parts'][0] ?? 'default';

switch ($action) {
    case 'login':
        handle_login($request);
        break;
    
    case 'register':
        handle_register($request);
        break;
    
    case 'verify':
        handle_verify_token($request);
        break;
    
    case 'refresh':
        handle_refresh_token($request);
        break;
    
    default:
        api_error('Invalid auth endpoint', 404);
}

// Handle user login
function handle_login($request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'POST') {
        api_error('Method not allowed', 405);
    }
    
    // Validate required fields
    if (empty($request['body']['username']) || empty($request['body']['password'])) {
        api_error('Username and password are required', 400);
    }
    
    $username = $request['body']['username'];
    $password = $request['body']['password'];
    
    // Get user from database
    $query = "SELECT * FROM users WHERE username = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 's', $username);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($result) === 0) {
        api_error('Invalid username or password', 401);
    }
    
    $user = mysqli_fetch_assoc($result);
    
    // Verify password
    if (!password_verify($password, $user['password'])) {
        api_error('Invalid username or password', 401);
    }
    
    // Check if user is active
    if ($user['status'] !== 'active') {
        api_error('Account is not active', 403);
    }
    
    // Generate JWT token
    $token = generate_jwt($user['id'], $user['username'], $user['role']);
    
    // Update last login timestamp
    $update_query = "UPDATE users SET last_login = NOW() WHERE id = ?";
    $update_stmt = mysqli_prepare($conn, $update_query);
    mysqli_stmt_bind_param($update_stmt, 'i', $user['id']);
    mysqli_stmt_execute($update_stmt);
    
    // Return user data and token
    api_response([
        'success' => true,
        'message' => 'Login successful',
        'user' => [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'name' => $user['name'],
            'role' => $user['role'],
            'is_premium' => (bool)$user['is_premium'],
            'premium_expires' => $user['premium_expires']
        ],
        'token' => $token,
        'expires_in' => JWT_EXPIRY
    ]);
}

// Handle user registration
function handle_register($request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'POST') {
        api_error('Method not allowed', 405);
    }
    
    // Validate required fields
    if (empty($request['body']['username']) || empty($request['body']['email']) || empty($request['body']['password'])) {
        api_error('Username, email and password are required', 400);
    }
    
    $username = $request['body']['username'];
    $email = $request['body']['email'];
    $password = $request['body']['password'];
    $name = $request['body']['name'] ?? '';
    
    // Validate email
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        api_error('Invalid email format', 400);
    }
    
    // Check if username or email already exists
    $check_query = "SELECT * FROM users WHERE username = ? OR email = ?";
    $check_stmt = mysqli_prepare($conn, $check_query);
    mysqli_stmt_bind_param($check_stmt, 'ss', $username, $email);
    mysqli_stmt_execute($check_stmt);
    $check_result = mysqli_stmt_get_result($check_stmt);
    
    if (mysqli_num_rows($check_result) > 0) {
        $existing_user = mysqli_fetch_assoc($check_result);
        if ($existing_user['username'] === $username) {
            api_error('Username already exists', 409);
        } else {
            api_error('Email already exists', 409);
        }
    }
    
    // Hash password
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    
    // Insert new user
    $insert_query = "INSERT INTO users (username, email, password, name, role, status, created_at) 
                    VALUES (?, ?, ?, ?, 'user', 'active', NOW())";
    $insert_stmt = mysqli_prepare($conn, $insert_query);
    mysqli_stmt_bind_param($insert_stmt, 'ssss', $username, $email, $hashed_password, $name);
    
    if (!mysqli_stmt_execute($insert_stmt)) {
        api_error('Failed to register user: ' . mysqli_error($conn), 500);
    }
    
    $user_id = mysqli_insert_id($conn);
    
    // Generate JWT token
    $token = generate_jwt($user_id, $username, 'user');
    
    // Return user data and token
    api_response([
        'success' => true,
        'message' => 'Registration successful',
        'user' => [
            'id' => $user_id,
            'username' => $username,
            'email' => $email,
            'name' => $name,
            'role' => 'user',
            'is_premium' => false
        ],
        'token' => $token,
        'expires_in' => JWT_EXPIRY
    ], 201);
}

// Handle token verification
function handle_verify_token($request) {
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get token from Authorization header
    $auth_header = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    $token = null;
    
    if (preg_match('/Bearer\s(\S+)/', $auth_header, $matches)) {
        $token = $matches[1];
    }
    
    if (!$token) {
        api_error('No token provided', 401);
    }
    
    // Verify token
    $payload = verify_jwt($token);
    
    if (!$payload) {
        api_error('Invalid or expired token', 401);
    }
    
    // Return user data
    api_response([
        'success' => true,
        'message' => 'Token is valid',
        'user' => [
            'id' => $payload['user_id'],
            'username' => $payload['username'],
            'role' => $payload['role']
        ],
        'expires_at' => $payload['exp']
    ]);
}

// Handle token refresh
function handle_refresh_token($request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'POST') {
        api_error('Method not allowed', 405);
    }
    
    // Get token from request body
    $token = $request['body']['token'] ?? null;
    
    if (!$token) {
        api_error('No token provided', 401);
    }
    
    // Verify token
    $payload = verify_jwt($token);
    
    if (!$payload) {
        api_error('Invalid or expired token', 401);
    }
    
    // Get user from database
    $user_id = $payload['user_id'];
    $query = "SELECT * FROM users WHERE id = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $user_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($result) === 0) {
        api_error('User not found', 404);
    }
    
    $user = mysqli_fetch_assoc($result);
    
    // Check if user is active
    if ($user['status'] !== 'active') {
        api_error('Account is not active', 403);
    }
    
    // Generate new JWT token
    $new_token = generate_jwt($user['id'], $user['username'], $user['role']);
    
    // Return new token
    api_response([
        'success' => true,
        'message' => 'Token refreshed',
        'token' => $new_token,
        'expires_in' => JWT_EXPIRY
    ]);
}
