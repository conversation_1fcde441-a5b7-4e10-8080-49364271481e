<?php
// Include direct config file
require_once '../direct_config.php';

// Get movie ID from query parameter
$movie_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($movie_id <= 0) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Movie ID is required',
        'data' => null
    ]);
    exit;
}

// Get movie details without status filter
$query = "SELECT m.*, c.name as category_name
          FROM movies m
          LEFT JOIN categories c ON m.category_id = c.id
          WHERE m.id = $movie_id";

$result = mysqli_query($conn, $query);

if (!$result) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to fetch movie details: ' . mysqli_error($conn),
        'data' => null
    ]);
    exit;
}

if (mysqli_num_rows($result) === 0) {
    http_response_code(404);
    echo json_encode([
        'success' => false,
        'message' => 'Movie not found',
        'data' => null
    ]);
    exit;
}

$movie = mysqli_fetch_assoc($result);

// Get download links
$links_query = "SELECT * FROM download_links
               WHERE content_type = 'movie' AND content_id = $movie_id
               ORDER BY quality DESC";

$links_result = mysqli_query($conn, $links_query);
$download_links = [];

if ($links_result) {
    while ($link = mysqli_fetch_assoc($links_result)) {
        $download_links[] = [
            'id' => (int)$link['id'],
            'quality' => $link['quality'],
            'url' => $link['url'] ?? $link['link_url'] ?? '',
            'server_name' => $link['server_name'] ?? '',
            'file_size' => $link['file_size'] ?? '',
            'is_premium' => (bool)($link['is_premium'] ?? false)
        ];
    }
}

// Get related movies without status filter
$related_query = "SELECT m.*, c.name as category_name
                 FROM movies m
                 LEFT JOIN categories c ON m.category_id = c.id
                 WHERE m.category_id = {$movie['category_id']}
                 AND m.id != $movie_id
                 ORDER BY RAND()
                 LIMIT 10";

$related_result = mysqli_query($conn, $related_query);
$related_movies = [];

if ($related_result) {
    while ($related = mysqli_fetch_assoc($related_result)) {
        $related_movies[] = [
            'id' => (int)$related['id'],
            'title' => $related['title'],
            'poster' => $related['poster'] ? (strpos($related['poster'], 'http') === 0 ? $related['poster'] : SITE_URL . '/uploads/' . $related['poster']) : '',
            'release_year' => (int)$related['release_year'],
            'rating' => (float)$related['rating'],
            'premium_only' => (bool)$related['premium_only']
        ];
    }
}

// Return movie details
http_response_code(200);
echo json_encode([
    'success' => true,
    'message' => 'Success',
    'data' => [
        'movie' => [
            'id' => (int)$movie['id'],
            'title' => $movie['title'],
            'description' => $movie['description'],
            'release_year' => (int)$movie['release_year'],
            'duration' => (int)$movie['duration'],
            'poster' => $movie['poster'] ? (strpos($movie['poster'], 'http') === 0 ? $movie['poster'] : SITE_URL . '/uploads/' . $movie['poster']) : '',
            'banner' => $movie['banner'] ? (strpos($movie['banner'], 'http') === 0 ? $movie['banner'] : SITE_URL . '/uploads/' . $movie['banner']) : '',
            'trailer_url' => $movie['trailer_url'],
            'rating' => (float)$movie['rating'],
            'category_id' => (int)$movie['category_id'],
            'category_name' => $movie['category_name'],
            'featured' => (bool)$movie['featured'],
            'premium_only' => (bool)$movie['premium_only'],
            'status' => $movie['status']
        ],
        'download_links' => $download_links,
        'related_movies' => $related_movies
    ]
]);
?>
