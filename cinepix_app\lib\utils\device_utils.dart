import 'dart:io';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class DeviceUtils {
  /// Check if the current device is a TV
  static bool isAndroidTV(BuildContext context) {
    // Check if running on Android
    if (!kIsWeb && Platform.isAndroid) {
      // Get screen size
      final size = MediaQuery.of(context).size;
      final diagonal =
          sqrt(size.width * size.width + size.height * size.height);

      // Check if the device has a large screen (typical for TVs)
      // and if the aspect ratio is typical for TVs (16:9 or similar)
      final aspectRatio = size.width / size.height;

      // Most TVs have diagonal > 1000 logical pixels and aspect ratio close to 16:9
      return diagonal > 1000 && (aspectRatio > 1.7 && aspectRatio < 1.8);
    }

    return false;
  }
}
