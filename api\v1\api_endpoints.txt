API Endpoints for CinePix Mobile App

1. App Configuration
   - GET /api/v1/direct_config.php

2. Authentication
   - POST /api/v1/direct_login.php
   - POST /api/v1/direct_register.php

3. Movies
   - GET /api/v1/direct_movies.php
   - GET /api/v1/direct_movie_details.php?id={movie_id}

4. TV Shows
   - GET /api/v1/direct_tvshows.php
   - GET /api/v1/direct_tvshow_details.php?id={tvshow_id}
   - GET /api/v1/direct_tvshow_episodes.php?id={tvshow_id}&season={season_number}

5. Search
   - GET /api/v1/direct_search.php?q={search_query}

6. Categories
   - GET /api/v1/direct_categories.php

7. User Profile
   - GET /api/v1/direct_profile.php

Update the app's constants.dart file with these endpoints:

```dart
// API Base URL
static const String baseUrl = 'https://cinepix.top/api/v1';

// API Endpoints
static const String configEndpoint = '/direct_config.php';
static const String loginEndpoint = '/direct_login.php';
static const String registerEndpoint = '/direct_register.php';
static const String moviesEndpoint = '/direct_movies.php';
static const String movieDetailsEndpoint = '/direct_movie_details.php';
static const String tvShowsEndpoint = '/direct_tvshows.php';
static const String tvShowDetailsEndpoint = '/direct_tvshow_details.php';
static const String tvShowEpisodesEndpoint = '/direct_tvshow_episodes.php';
static const String searchEndpoint = '/direct_search.php';
static const String categoriesEndpoint = '/direct_categories.php';
static const String profileEndpoint = '/direct_profile.php';
```
