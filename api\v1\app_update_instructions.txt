CinePix মোবাইল অ্যাপ আপডেট নির্দেশনা

১. অ্যাপের lib/utils/constants.dart ফাইলে নিম্নলিখিত পরিবর্তন করুন:

```dart
class AppConstants {
  // API Base URL
  static const String baseUrl = 'https://cinepix.top/api/v1';
  
  // API Endpoints
  static const String configEndpoint = '/direct_config.php';
  static const String loginEndpoint = '/direct_login.php';
  static const String registerEndpoint = '/direct_register.php';
  static const String moviesEndpoint = '/direct_movies.php';
  static const String movieDetailsEndpoint = '/direct_movie_details.php';
  static const String tvShowsEndpoint = '/direct_tvshows.php';
  static const String tvShowDetailsEndpoint = '/direct_tvshow_details.php';
  static const String tvShowEpisodesEndpoint = '/direct_tvshow_episodes.php';
  static const String searchEndpoint = '/direct_search.php';
  static const String categoriesEndpoint = '/direct_categories.php';
  static const String profileEndpoint = '/direct_profile.php';
  
  // App Info
  static const String appName = 'CinePix';
  static const String appVersion = '1.0.0';
  
  // Other Constants
  static const int defaultPageSize = 20;
}
```

২. অ্যাপ বিল্ড করুন:

```
cd movieapp
flutter pub get
flutter build apk --release
```

৩. অ্যাপ টেস্ট করুন:

- লগইন/রেজিস্ট্রেশন
- মুভি লিস্ট দেখা
- টিভি শো লিস্ট দেখা
- সার্চ করা
- প্রোফাইল দেখা

৪. API এন্ডপয়েন্টগুলি টেস্ট করুন:

- https://cinepix.top/api/v1/direct_config.php
- https://cinepix.top/api/v1/direct_movies.php
- https://cinepix.top/api/v1/direct_tvshows.php
- https://cinepix.top/api/v1/direct_categories.php
- https://cinepix.top/api/v1/direct_search.php?q=test

৫. পেমেন্ট সেটিংস আপডেট করুন:

- অ্যাডমিন প্যানেল থেকে বিকাশ, নগদ, রকেট নম্বর আপডেট করুন
- অথবা ডাটাবেসে সরাসরি আপডেট করুন:

```sql
UPDATE payment_settings SET 
bkash_merchant_number = 'আপনার বিকাশ নম্বর',
bkash_merchant_name = 'আপনার নাম',
nagad_merchant_number = 'আপনার নগদ নম্বর',
nagad_merchant_name = 'আপনার নাম',
rocket_merchant_number = 'আপনার রকেট নম্বর',
rocket_merchant_name = 'আপনার নাম'
WHERE id = 1;
```
