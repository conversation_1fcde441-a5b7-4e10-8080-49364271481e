<?php
// Set page title
$page_title = 'Update Database';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Process form submissions
$success_message = '';
$error_message = '';

// Define database updates
$database_updates = [
    [
        'id' => 16,
        'title' => 'Add language column to tvshows table',
        'description' => 'Adds a language column to the tvshows table to store TV show language information.',
        'query' => "ALTER TABLE tvshows ADD COLUMN IF NOT EXISTS language VARCHAR(50) DEFAULT NULL"
    ],
    [
        'id' => 17,
        'title' => 'Create seasons table',
        'description' => 'Creates a new table to store TV show seasons.',
        'query' => "CREATE TABLE IF NOT EXISTS `seasons` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `tvshow_id` int(11) NOT NULL,
          `season_number` int(11) NOT NULL,
          `title` varchar(255) DEFAULT NULL,
          `overview` text DEFAULT NULL,
          `poster` varchar(255) DEFAULT NULL,
          `created_at` datetime NOT NULL,
          `updated_at` datetime DEFAULT NULL,
          PRIMARY KEY (`id`),
          KEY `tvshow_id` (`tvshow_id`),
          CONSTRAINT `seasons_ibfk_1` FOREIGN KEY (`tvshow_id`) REFERENCES `tvshows` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
    ],
    [
        'id' => 18,
        'title' => 'Create episodes table',
        'description' => 'Creates a new table to store TV show episodes.',
        'query' => "CREATE TABLE IF NOT EXISTS `episodes` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `season_id` int(11) NOT NULL,
          `episode_number` int(11) NOT NULL,
          `title` varchar(255) DEFAULT NULL,
          `overview` text DEFAULT NULL,
          `still` varchar(255) DEFAULT NULL,
          `air_date` date DEFAULT NULL,
          `created_at` datetime NOT NULL,
          `updated_at` datetime DEFAULT NULL,
          PRIMARY KEY (`id`),
          KEY `season_id` (`season_id`),
          CONSTRAINT `episodes_ibfk_1` FOREIGN KEY (`season_id`) REFERENCES `seasons` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
    ],
    [
        'id' => 19,
        'title' => 'Create episode_links table',
        'description' => 'Creates a new table to store TV show episode links.',
        'query' => "CREATE TABLE IF NOT EXISTS `episode_links` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `episode_id` int(11) NOT NULL,
          `link_type` enum('stream','download') NOT NULL DEFAULT 'stream',
          `quality` varchar(50) DEFAULT NULL,
          `url` text NOT NULL,
          `server_name` varchar(100) DEFAULT NULL,
          `file_size` varchar(50) DEFAULT NULL,
          `created_at` datetime NOT NULL,
          PRIMARY KEY (`id`),
          KEY `episode_id` (`episode_id`),
          CONSTRAINT `episode_links_ibfk_1` FOREIGN KEY (`episode_id`) REFERENCES `episodes` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
    ],
    [
        'id' => 13,
        'title' => 'Add views column to movies table',
        'description' => 'Adds a views column to the movies table to track view count.',
        'query' => "ALTER TABLE movies ADD COLUMN IF NOT EXISTS views INT DEFAULT 0"
    ],
    [
        'id' => 14,
        'title' => 'Add views column to tvshows table',
        'description' => 'Adds a views column to the tvshows table to track view count.',
        'query' => "ALTER TABLE tvshows ADD COLUMN IF NOT EXISTS views INT DEFAULT 0"
    ],
    [
        'id' => 15,
        'title' => 'Add language column to movies table',
        'description' => 'Adds a language column to the movies table to store movie language information.',
        'query' => "ALTER TABLE movies ADD COLUMN IF NOT EXISTS language VARCHAR(50) DEFAULT NULL"
    ],
    [
        'id' => 1,
        'title' => 'Add quality column to movies table',
        'description' => 'Adds a quality column to the movies table to store video quality information.',
        'query' => "ALTER TABLE movies ADD COLUMN IF NOT EXISTS quality VARCHAR(20) DEFAULT NULL"
    ],
    [
        'id' => 2,
        'title' => 'Add quality column to tvshows table',
        'description' => 'Adds a quality column to the tvshows table to store video quality information.',
        'query' => "ALTER TABLE tvshows ADD COLUMN IF NOT EXISTS quality VARCHAR(20) DEFAULT NULL"
    ],
    [
        'id' => 3,
        'title' => 'Add quality column to episodes table',
        'description' => 'Adds a quality column to the episodes table to store video quality information.',
        'query' => "ALTER TABLE episodes ADD COLUMN IF NOT EXISTS quality VARCHAR(20) DEFAULT NULL"
    ],
    [
        'id' => 4,
        'title' => 'Add size column to download_links table',
        'description' => 'Adds a size column to the download_links table to store file size information.',
        'query' => "ALTER TABLE download_links ADD COLUMN IF NOT EXISTS size VARCHAR(20) DEFAULT NULL"
    ],
    [
        'id' => 5,
        'title' => 'Add size column to episode_links table',
        'description' => 'Adds a size column to the episode_links table to store file size information.',
        'query' => "ALTER TABLE episode_links ADD COLUMN IF NOT EXISTS size VARCHAR(20) DEFAULT NULL"
    ],
    [
        'id' => 6,
        'title' => 'Add type column to categories table',
        'description' => 'Adds a type column to the categories table to specify if the category is for movies, tvshows, or both.',
        'query' => "ALTER TABLE categories ADD COLUMN IF NOT EXISTS type ENUM('both', 'movie', 'tvshow') DEFAULT 'both'"
    ],
    [
        'id' => 7,
        'title' => 'Create activity_logs table',
        'description' => 'Creates a new table to store user activity logs.',
        'query' => "CREATE TABLE IF NOT EXISTS activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            activity_type VARCHAR(50) NOT NULL,
            description TEXT NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        )"
    ],
    [
        'id' => 8,
        'title' => 'Create system_logs table',
        'description' => 'Creates a new table to store system logs.',
        'query' => "CREATE TABLE IF NOT EXISTS system_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            log_type ENUM('info', 'warning', 'error', 'success') NOT NULL DEFAULT 'info',
            message TEXT NOT NULL,
            details TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )"
    ],
    [
        'id' => 9,
        'title' => 'Create chat_sessions table',
        'description' => 'Creates a new table to store chat sessions for live chat feature.',
        'query' => "CREATE TABLE IF NOT EXISTS chat_sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            status ENUM('active', 'closed') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )"
    ],
    [
        'id' => 10,
        'title' => 'Create chat_messages table',
        'description' => 'Creates a new table to store chat messages for live chat feature.',
        'query' => "CREATE TABLE IF NOT EXISTS chat_messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id INT NOT NULL,
            sender_id INT NOT NULL,
            receiver_id INT NOT NULL,
            message TEXT NOT NULL,
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE,
            FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE
        )"
    ],
    [
        'id' => 11,
        'title' => 'Create payment_settings table',
        'description' => 'Creates a new table to store payment settings.',
        'query' => "CREATE TABLE IF NOT EXISTS payment_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            bkash_merchant_number VARCHAR(20),
            bkash_merchant_name VARCHAR(100),
            nagad_merchant_number VARCHAR(20),
            nagad_merchant_name VARCHAR(100),
            rocket_merchant_number VARCHAR(20),
            rocket_merchant_name VARCHAR(100),
            bkash_enabled BOOLEAN DEFAULT TRUE,
            nagad_enabled BOOLEAN DEFAULT TRUE,
            rocket_enabled BOOLEAN DEFAULT TRUE,
            manual_payment_enabled BOOLEAN DEFAULT TRUE,
            payment_instructions TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )"
    ],
    [
        'id' => 12,
        'title' => 'Add is_active column to premium_plans table',
        'description' => 'Adds an is_active column to the premium_plans table to enable/disable plans.',
        'query' => "ALTER TABLE premium_plans ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE"
    ]
];

// Check if update_logs table exists
$check_table_query = "SHOW TABLES LIKE 'update_logs'";
$check_table_result = mysqli_query($conn, $check_table_query);

if (mysqli_num_rows($check_table_result) == 0) {
    // Create update_logs table
    $create_table_query = "CREATE TABLE IF NOT EXISTS update_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        update_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        status ENUM('success', 'failed') NOT NULL,
        error_message TEXT,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";

    if (!mysqli_query($conn, $create_table_query)) {
        $error_message = "Error creating update_logs table: " . mysqli_error($conn);
    }
}

// Get applied updates
$applied_updates = [];
$applied_updates_query = "SELECT update_id FROM update_logs WHERE status = 'success'";
$applied_updates_result = mysqli_query($conn, $applied_updates_query);

if ($applied_updates_result) {
    while ($row = mysqli_fetch_assoc($applied_updates_result)) {
        $applied_updates[] = $row['update_id'];
    }
}

// Apply update
if (isset($_POST['apply_update']) && !empty($_POST['update_id'])) {
    $update_id = (int)$_POST['update_id'];

    // Find the update
    $update = null;
    foreach ($database_updates as $db_update) {
        if ($db_update['id'] == $update_id) {
            $update = $db_update;
            break;
        }
    }

    if ($update) {
        // Execute the query
        if (mysqli_query($conn, $update['query'])) {
            // Log the successful update
            $title = mysqli_real_escape_string($conn, $update['title']);
            $log_query = "INSERT INTO update_logs (update_id, title, status) VALUES ($update_id, '$title', 'success')";
            mysqli_query($conn, $log_query);

            $success_message = "Update '{$update['title']}' applied successfully.";
            $applied_updates[] = $update_id;
        } else {
            // Log the failed update
            $title = mysqli_real_escape_string($conn, $update['title']);
            $error = mysqli_real_escape_string($conn, mysqli_error($conn));
            $log_query = "INSERT INTO update_logs (update_id, title, status, error_message) VALUES ($update_id, '$title', 'failed', '$error')";
            mysqli_query($conn, $log_query);

            $error_message = "Error applying update '{$update['title']}': " . mysqli_error($conn);
        }
    } else {
        $error_message = "Update with ID $update_id not found.";
    }
}

// Apply all updates
if (isset($_POST['apply_all_updates'])) {
    $success_count = 0;
    $error_count = 0;

    foreach ($database_updates as $update) {
        // Skip already applied updates
        if (in_array($update['id'], $applied_updates)) {
            continue;
        }

        // Execute the query
        if (mysqli_query($conn, $update['query'])) {
            // Log the successful update
            $title = mysqli_real_escape_string($conn, $update['title']);
            $log_query = "INSERT INTO update_logs (update_id, title, status) VALUES ({$update['id']}, '$title', 'success')";
            mysqli_query($conn, $log_query);

            $applied_updates[] = $update['id'];
            $success_count++;
        } else {
            // Log the failed update
            $title = mysqli_real_escape_string($conn, $update['title']);
            $error = mysqli_real_escape_string($conn, mysqli_error($conn));
            $log_query = "INSERT INTO update_logs (update_id, title, status, error_message) VALUES ({$update['id']}, '$title', 'failed', '$error')";
            mysqli_query($conn, $log_query);

            $error_count++;
        }
    }

    if ($success_count > 0) {
        $success_message = "$success_count update(s) applied successfully.";
    }

    if ($error_count > 0) {
        $error_message = "$error_count update(s) failed to apply. Check the update logs for details.";
    }

    if ($success_count == 0 && $error_count == 0) {
        $success_message = "All updates have already been applied.";
    }
}

// Get update logs
$logs_query = "SELECT * FROM update_logs ORDER BY executed_at DESC LIMIT 10";
$logs_result = mysqli_query($conn, $logs_query);

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title d-flex align-items-center">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>ডাটাবেস আপডেট</h1>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <?php if($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="fw-bold text-primary mb-0">উপলব্ধ আপডেট</h6>
                            <form method="POST" action="">
                                <button type="submit" name="apply_all_updates" class="btn btn-primary btn-sm" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি সমস্ত আপডেট প্রয়োগ করতে চান?');">
                                    <i class="fas fa-sync me-1"></i> সব আপডেট প্রয়োগ করুন
                                </button>
                            </form>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead>
                                    <tr>
                                        <th>আইডি</th>
                                        <th>টাইটেল</th>
                                        <th>বিবরণ</th>
                                        <th>স্ট্যাটাস</th>
                                        <th>অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($database_updates as $update): ?>
                                    <tr>
                                        <td><?php echo $update['id']; ?></td>
                                        <td><?php echo $update['title']; ?></td>
                                        <td><?php echo $update['description']; ?></td>
                                        <td>
                                            <?php if(in_array($update['id'], $applied_updates)): ?>
                                            <span class="badge bg-success">প্রয়োগ করা হয়েছে</span>
                                            <?php else: ?>
                                            <span class="badge bg-warning">প্রয়োগ করা হয়নি</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if(!in_array($update['id'], $applied_updates)): ?>
                                            <form method="POST" action="">
                                                <input type="hidden" name="update_id" value="<?php echo $update['id']; ?>">
                                                <button type="submit" name="apply_update" class="btn btn-primary btn-sm">
                                                    <i class="fas fa-play me-1"></i> প্রয়োগ করুন
                                                </button>
                                            </form>
                                            <?php else: ?>
                                            <button type="button" class="btn btn-secondary btn-sm" disabled>
                                                <i class="fas fa-check me-1"></i> প্রয়োগ করা হয়েছে
                                            </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="fw-bold text-primary mb-0">আপডেট লগ</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead>
                                    <tr>
                                        <th>আইডি</th>
                                        <th>টাইটেল</th>
                                        <th>স্ট্যাটাস</th>
                                        <th>তারিখ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if(mysqli_num_rows($logs_result) > 0): ?>
                                        <?php while($log = mysqli_fetch_assoc($logs_result)): ?>
                                        <tr>
                                            <td><?php echo $log['update_id']; ?></td>
                                            <td><?php echo $log['title']; ?></td>
                                            <td>
                                                <?php if($log['status'] == 'success'): ?>
                                                <span class="badge bg-success">সফল</span>
                                                <?php else: ?>
                                                <span class="badge bg-danger">ব্যর্থ</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo date('Y-m-d H:i:s', strtotime($log['executed_at'])); ?></td>
                                        </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="4" class="text-center">কোন আপডেট লগ পাওয়া যায়নি।</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="fw-bold text-primary mb-0">ডাটাবেস ইনফরমেশন</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label fw-bold">ডাটাবেস নাম</label>
                            <p><?php echo defined('DB_NAME') ? DB_NAME : 'tipsbdxy_4525'; ?></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">ডাটাবেস হোস্ট</label>
                            <p><?php echo defined('DB_HOST') ? DB_HOST : 'localhost'; ?></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">ডাটাবেস ইউজার</label>
                            <p><?php echo defined('DB_USER') ? DB_USER : 'root'; ?></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">টেবিল সংখ্যা</label>
                            <?php
                            $tables_query = "SHOW TABLES";
                            $tables_result = mysqli_query($conn, $tables_query);
                            $table_count = mysqli_num_rows($tables_result);
                            ?>
                            <p><?php echo $table_count; ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
