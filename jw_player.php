<?php
// Include config file to get site URL and other settings
require_once 'includes/config.php';

// Get video URL from query parameter or use default
$video_url = isset($_GET['url']) ? $_GET['url'] : 'https://odd-darkness-074fsadsafsafasfjlknmmlkaytr9pe8afnhdklnfalskdftgy.bdmovieshub.workers.dev/994/RRR+2022+-+Hindi+-WEBDL+-+720p.mkv?hash=AgADjg';
$video_title = isset($_GET['title']) ? $_GET['title'] : 'RRR (2022) - Hindi - WEBDL - 720p';

// JW Player license key
$jw_license = defined('JW_PLAYER_LICENSE') ? JW_PLAYER_LICENSE : '64HPbvSQorQcd52B8XFuhMtEoitbvY/EXJmMBfKcXZQU2Rnn';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $video_title; ?> - JW Player</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <style>
        body {
            background-color: #111;
            color: #fff;
            padding: 20px;
        }
        .player-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #000;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        .video-info {
            padding: 20px;
            background-color: #222;
            border-radius: 0 0 8px 8px;
        }
        h1 {
            font-size: 24px;
            margin-bottom: 20px;
        }
        .btn-back {
            margin-bottom: 20px;
        }
        #jwplayer-container {
            width: 100%;
            aspect-ratio: 16/9;
            border-radius: 8px 8px 0 0;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <a href="<?php echo SITE_URL; ?>" class="btn btn-outline-light btn-back">
            <i class="fas fa-arrow-left"></i> Back to Home
        </a>
        
        <div class="player-container">
            <!-- JW Player -->
            <div id="jwplayer-container"></div>
            
            <div class="video-info">
                <h1><?php echo $video_title; ?></h1>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <span class="badge bg-danger me-2">720p</span>
                        <span class="badge bg-secondary me-2">Hindi</span>
                        <span class="badge bg-info">WEBDL</span>
                    </div>
                    <div>
                        <button id="copy-url" class="btn btn-sm btn-outline-light" data-url="<?php echo SITE_URL; ?>/jw_player.php?url=<?php echo urlencode($video_url); ?>&title=<?php echo urlencode($video_title); ?>">
                            <i class="fas fa-copy"></i> Copy Player URL
                        </button>
                    </div>
                </div>
                
                <div class="alert alert-info">
                    <strong>Note:</strong> If the video doesn't play, it might be due to CORS restrictions or the video format not being supported by the browser.
                </div>
                
                <div class="mt-4">
                    <h5>Keyboard Shortcuts:</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-play me-2"></i> Space - Play/Pause</li>
                                <li><i class="fas fa-volume-up me-2"></i> M - Mute/Unmute</li>
                                <li><i class="fas fa-arrows-alt me-2"></i> F - Fullscreen</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-forward me-2"></i> → - Forward 10s</li>
                                <li><i class="fas fa-backward me-2"></i> ← - Backward 10s</li>
                                <li><i class="fas fa-volume-up me-2"></i> ↑ - Volume Up</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-volume-down me-2"></i> ↓ - Volume Down</li>
                                <li><i class="fas fa-fast-forward me-2"></i> > - Increase Speed</li>
                                <li><i class="fas fa-fast-backward me-2"></i> < - Decrease Speed</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- JW Player -->
    <script src="https://content.jwplatform.com/libraries/<?php echo $jw_license; ?>.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize JW Player
            jwplayer('jwplayer-container').setup({
                file: '<?php echo $video_url; ?>',
                title: '<?php echo $video_title; ?>',
                width: '100%',
                aspectratio: '16:9',
                stretching: 'uniform',
                primary: 'html5',
                hlshtml: true,
                controls: true,
                autostart: false,
                playbackRateControls: [0.5, 0.75, 1, 1.25, 1.5, 2],
                logo: {
                    file: '<?php echo SITE_URL; ?>/images/logo.png',
                    position: 'top-right',
                    hide: true
                }
            });
            
            // Copy URL button
            document.getElementById('copy-url').addEventListener('click', function() {
                const url = this.getAttribute('data-url');
                navigator.clipboard.writeText(url).then(() => {
                    this.innerHTML = '<i class="fas fa-check"></i> Copied!';
                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-copy"></i> Copy Player URL';
                    }, 2000);
                });
            });
        });
    </script>
</body>
</html>
