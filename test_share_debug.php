<?php
require_once 'includes/config.php';

echo "<h2>শেয়ার লিংক ডিবাগ টেস্ট</h2>";

// Step 1: Check database connection
echo "<h3>ধাপ ১: ডেটাবেস কানেকশন</h3>";
if ($conn) {
    echo "✅ ডেটাবেস কানেকশন সফল<br>";
} else {
    echo "❌ ডেটাবেস কানেকশন ব্যর্থ<br>";
    exit;
}

// Step 2: Check if shared_links table exists
echo "<h3>ধাপ ২: shared_links টেবিল চেক</h3>";
$check_table = mysqli_query($conn, "SHOW TABLES LIKE 'shared_links'");
if (mysqli_num_rows($check_table) > 0) {
    echo "✅ shared_links টেবিল আছে<br>";
} else {
    echo "❌ shared_links টেবিল নেই<br>";
    exit;
}

// Step 3: Check if there are any shared links
echo "<h3>ধাপ ৩: শেয়ার লিংক সংখ্যা</h3>";
$count_query = mysqli_query($conn, "SELECT COUNT(*) as total FROM shared_links");
$total = mysqli_fetch_assoc($count_query)['total'];
echo "মোট শেয়ার লিংক: $total<br>";

if ($total == 0) {
    echo "❌ কোনো শেয়ার লিংক নেই। প্রথমে একটি শেয়ার লিংক তৈরি করুন।<br>";
    echo "<a href='create_test_share_link.php'>টেস্ট শেয়ার লিংক তৈরি করুন</a><br>";
    exit;
}

// Step 4: Get a test token
echo "<h3>ধাপ ৪: টেস্ট টোকেন</h3>";
$token_query = mysqli_query($conn, "SELECT link_token FROM shared_links WHERE is_active = 1 LIMIT 1");
if (mysqli_num_rows($token_query) > 0) {
    $test_token = mysqli_fetch_assoc($token_query)['link_token'];
    echo "টেস্ট টোকেন: $test_token<br>";
} else {
    echo "❌ কোনো সক্রিয় শেয়ার লিংক নেই<br>";
    exit;
}

// Step 5: Test the exact query from share.php
echo "<h3>ধাপ ৫: share.php কুয়েরি টেস্ট</h3>";
$token = mysqli_real_escape_string($conn, $test_token);

$query = "SELECT sl.*, 
                 CASE 
                     WHEN sl.content_type = 'movie' THEN m.title
                     WHEN sl.content_type = 'tvshow' THEN t.title
                 END as content_title,
                 CASE 
                     WHEN sl.content_type = 'movie' THEN m.description
                     WHEN sl.content_type = 'tvshow' THEN t.description
                 END as content_description,
                 CASE 
                     WHEN sl.content_type = 'movie' THEN m.poster
                     WHEN sl.content_type = 'tvshow' THEN t.poster
                 END as content_poster,
                 CASE 
                     WHEN sl.content_type = 'movie' THEN m.banner
                     WHEN sl.content_type = 'tvshow' THEN t.banner
                 END as content_banner,
                 CASE 
                     WHEN sl.content_type = 'movie' THEN m.rating
                     WHEN sl.content_type = 'tvshow' THEN t.rating
                 END as content_rating,
                 CASE 
                     WHEN sl.content_type = 'movie' THEN m.release_year
                     WHEN sl.content_type = 'tvshow' THEN t.release_year
                 END as content_year
          FROM shared_links sl
          LEFT JOIN movies m ON sl.content_type = 'movie' AND sl.content_id = m.id
          LEFT JOIN tvshows t ON sl.content_type = 'tvshow' AND sl.content_id = t.id
          WHERE sl.link_token = '$token' AND sl.is_active = 1";

echo "কুয়েরি: " . htmlspecialchars($query) . "<br>";

$result = mysqli_query($conn, $query);

if (!$result) {
    echo "❌ কুয়েরি ফেইল: " . mysqli_error($conn) . "<br>";
} else {
    $num_rows = mysqli_num_rows($result);
    echo "✅ কুয়েরি সফল। ফলাফল: $num_rows সারি<br>";
    
    if ($num_rows > 0) {
        $shared_link = mysqli_fetch_assoc($result);
        echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";
        echo "<strong>শেয়ার লিংক তথ্য:</strong><br>";
        echo "ID: {$shared_link['id']}<br>";
        echo "টাইটেল: {$shared_link['title']}<br>";
        echo "কন্টেন্ট টাইপ: {$shared_link['content_type']}<br>";
        echo "কন্টেন্ট ID: {$shared_link['content_id']}<br>";
        echo "কন্টেন্ট টাইটেল: {$shared_link['content_title']}<br>";
        echo "এক্সেস কাউন্ট: {$shared_link['access_count']}<br>";
        echo "এক্সেস লিমিট: {$shared_link['access_limit']}<br>";
        echo "মেয়াদ: {$shared_link['expires_at']}<br>";
        echo "সক্রিয়: " . ($shared_link['is_active'] ? 'হ্যাঁ' : 'না') . "<br>";
        echo "</div>";
    }
}

// Step 6: Test the share.php URL
echo "<h3>ধাপ ৬: শেয়ার লিংক টেস্ট</h3>";
$share_url = SITE_URL . '/share.php?token=' . $test_token;
$debug_url = SITE_URL . '/share.php?token=' . $test_token . '&debug=1';

echo "সাধারণ লিংক: <a href='$share_url' target='_blank'>$share_url</a><br>";
echo "ডিবাগ লিংক: <a href='$debug_url' target='_blank'>$debug_url</a><br>";

echo "<h3>পরবর্তী ধাপ:</h3>";
echo "<ol>";
echo "<li>ডিবাগ লিংকে ক্লিক করুন</li>";
echo "<li>ডিবাগ তথ্য দেখুন</li>";
echo "<li>যদি এখনও সমস্যা থাকে তবে <a href='check_share_links.php'>check_share_links.php</a> চেক করুন</li>";
echo "</ol>";
?> 