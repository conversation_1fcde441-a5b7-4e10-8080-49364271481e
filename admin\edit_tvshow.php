<?php
// Set page title
$page_title = 'Edit TV Show';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Check if TV show ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    redirect('tvshows.php');
}

$tvshow_id = (int)$_GET['id'];

// Get TV show details
$tvshow_query = "SELECT * FROM tvshows WHERE id = $tvshow_id";
$tvshow_result = mysqli_query($conn, $tvshow_query);

if (mysqli_num_rows($tvshow_result) == 0) {
    redirect('tvshows.php');
}

$tvshow = mysqli_fetch_assoc($tvshow_result);

// Check if tags table exists
$check_tags_table = "SHOW TABLES LIKE 'tags'";
$tags_table_exists = mysqli_query($conn, $check_tags_table);

// Get all tags if the table exists
$tags = [];
if (mysqli_num_rows($tags_table_exists) > 0) {
    $tags_query = "SELECT * FROM tags ORDER BY name";
    $tags_result = mysqli_query($conn, $tags_query);
    while ($tag = mysqli_fetch_assoc($tags_result)) {
        $tags[] = $tag;
    }

    // Get tvshow tags
    $tvshow_tags = [];
    $tvshow_tags_query = "SELECT tag_id FROM tvshow_tags WHERE tvshow_id = $tvshow_id";
    $tvshow_tags_result = mysqli_query($conn, $tvshow_tags_query);
    while ($tag = mysqli_fetch_assoc($tvshow_tags_result)) {
        $tvshow_tags[] = $tag['tag_id'];
    }
}

// Process form submission
$success_message = '';
$error_message = '';

if (isset($_GET['success']) && $_GET['success'] == 'added') {
    $success_message = 'TV show added successfully.';
}

if (isset($_POST['update_tvshow'])) {
    $title = sanitize($_POST['title']);
    $category_id = (int)$_POST['category_id'];
    $start_year = (int)$_POST['start_year'];
    $end_year = !empty($_POST['end_year']) ? (int)$_POST['end_year'] : null;
    $seasons = (int)$_POST['seasons'];
    $quality = sanitize($_POST['quality']);
    $language = sanitize($_POST['language']);
    $rating = (float)$_POST['rating'];
    $description = sanitize($_POST['description']);
    $trailer_url = sanitize($_POST['trailer_url']);
    $premium_only = isset($_POST['premium_only']) ? 1 : 0;
    $featured = isset($_POST['featured']) ? 1 : 0;
    $selected_tags = isset($_POST['tags']) ? $_POST['tags'] : [];

    // Validate required fields
    if (empty($title) || empty($category_id) || empty($start_year)) {
        $error_message = 'Title, category, and start year are required.';
    } else {
        // Handle poster upload
        $poster = $tvshow['poster'];
        if (!empty($_FILES['poster']['name'])) {
            $upload_dir = '../uploads/';
            $file_ext = pathinfo($_FILES['poster']['name'], PATHINFO_EXTENSION);
            $new_poster = 'tvshow_' . time() . '.' . $file_ext;
            $upload_path = $upload_dir . $new_poster;

            // Check file type
            $allowed_types = ['jpg', 'jpeg', 'png', 'webp'];
            if (!in_array(strtolower($file_ext), $allowed_types)) {
                $error_message = 'Invalid poster file type. Allowed types: JPG, JPEG, PNG, WEBP.';
            } else if ($_FILES['poster']['size'] > 2097152) { // 2MB
                $error_message = 'Poster file size must be less than 2MB.';
            } else if (!move_uploaded_file($_FILES['poster']['tmp_name'], $upload_path)) {
                $error_message = 'Error uploading poster file.';
            } else {
                // Delete old poster if it exists and is not a URL
                if (!empty($tvshow['poster']) && strpos($tvshow['poster'], 'http') !== 0 && file_exists($upload_dir . $tvshow['poster'])) {
                    unlink($upload_dir . $tvshow['poster']);
                }
                $poster = $new_poster;
            }
        }

        // If no errors, update TV show
        if (empty($error_message)) {
            $end_year_value = $end_year ? $end_year : 'NULL';
            $query = "UPDATE tvshows SET
                    title = '$title',
                    category_id = $category_id,
                    start_year = $start_year,
                    end_year = " . ($end_year ? $end_year : 'NULL') . ",
                    seasons = $seasons,
                    quality = '$quality',
                    language = '$language',
                    rating = $rating,
                    description = '$description',
                    trailer_url = '$trailer_url',
                    poster = '$poster',
                    premium_only = $premium_only,
                    featured = $featured,
                    updated_at = NOW()
                    WHERE id = $tvshow_id";

            if (mysqli_query($conn, $query)) {
                // Update tags if tags table exists
                if (mysqli_num_rows($tags_table_exists) > 0) {
                    // First delete all existing tags for this TV show
                    $delete_tags_query = "DELETE FROM tvshow_tags WHERE tvshow_id = $tvshow_id";
                    mysqli_query($conn, $delete_tags_query);

                    // Then insert the selected tags
                    if (!empty($selected_tags)) {
                        foreach ($selected_tags as $tag_id) {
                            $tag_id = (int)$tag_id;
                            $insert_tag_query = "INSERT INTO tvshow_tags (tvshow_id, tag_id) VALUES ($tvshow_id, $tag_id)";
                            mysqli_query($conn, $insert_tag_query);
                        }
                    }
                }

                $success_message = 'TV show updated successfully.';

                // Refresh TV show data
                $tvshow_result = mysqli_query($conn, $tvshow_query);
                $tvshow = mysqli_fetch_assoc($tvshow_result);

                // Refresh TV show tags
                if (mysqli_num_rows($tags_table_exists) > 0) {
                    $tvshow_tags = [];
                    $tvshow_tags_query = "SELECT tag_id FROM tvshow_tags WHERE tvshow_id = $tvshow_id";
                    $tvshow_tags_result = mysqli_query($conn, $tvshow_tags_query);
                    while ($tag = mysqli_fetch_assoc($tvshow_tags_result)) {
                        $tvshow_tags[] = $tag['tag_id'];
                    }
                }
            } else {
                $error_message = 'Error updating TV show: ' . mysqli_error($conn);
            }
        }
    }
}

// Get categories
$categories_query = "SELECT * FROM categories ORDER BY name";
$categories_result = mysqli_query($conn, $categories_query);

// Get seasons
$seasons_query = "SELECT * FROM seasons WHERE tvshow_id = $tvshow_id ORDER BY season_number";
$seasons_result = mysqli_query($conn, $seasons_query);

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">
            <!-- Sidebar Toggle (Topbar) -->
            <button id="sidebarToggle" class="btn btn-link d-md-none rounded-circle mr-3">
                <i class="fa fa-bars"></i>
            </button>

            <!-- Topbar Title -->
            <div class="d-none d-sm-inline-block mr-auto ml-md-3 my-2 my-md-0 mw-100">
                <h1 class="h3 mb-0 text-gray-800">Edit TV Show</h1>
            </div>
        </nav>
    </div>

    <div class="container-fluid">
        <?php if($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">Edit TV Show: <?php echo $tvshow['title']; ?></h6>
                <div>
                    <a href="manage_episodes.php?tvshow=<?php echo $tvshow_id; ?>" class="btn btn-sm btn-warning">
                        <i class="fas fa-list me-1"></i> Manage Episodes
                    </a>
                    <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $tvshow_id; ?>" class="btn btn-sm btn-success" target="_blank">
                        <i class="fas fa-eye me-1"></i> View TV Show
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form method="POST" action="" enctype="multipart/form-data" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" value="<?php echo $tvshow['title']; ?>" required>
                                <div class="invalid-feedback">
                                    Please enter a title.
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="category_id" class="form-label">Category <span class="text-danger">*</span></label>
                                        <select class="form-select" id="category_id" name="category_id" required>
                                            <option value="">Select Category</option>
                                            <?php
                                            mysqli_data_seek($categories_result, 0);
                                            while($category = mysqli_fetch_assoc($categories_result)):
                                            ?>
                                            <option value="<?php echo $category['id']; ?>" <?php echo $tvshow['category_id'] == $category['id'] ? 'selected' : ''; ?>><?php echo $category['name']; ?></option>
                                            <?php endwhile; ?>
                                        </select>
                                        <div class="invalid-feedback">
                                            Please select a category.
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="start_year" class="form-label">Start Year <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="start_year" name="start_year" min="1900" max="<?php echo date('Y'); ?>" value="<?php echo $tvshow['start_year']; ?>" required>
                                        <div class="invalid-feedback">
                                            Please enter a valid start year.
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="end_year" class="form-label">End Year</label>
                                        <input type="number" class="form-control" id="end_year" name="end_year" min="1900" max="<?php echo date('Y'); ?>" value="<?php echo $tvshow['end_year']; ?>">
                                        <div class="form-text">Leave empty if still running.</div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="seasons" class="form-label">Seasons</label>
                                        <input type="number" class="form-control" id="seasons" name="seasons" min="1" value="<?php echo $tvshow['seasons']; ?>">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="quality" class="form-label">Quality</label>
                                        <select class="form-select" id="quality" name="quality">
                                            <option value="">Select Quality</option>
                                            <option value="SD" <?php echo $tvshow['quality'] == 'SD' ? 'selected' : ''; ?>>SD</option>
                                            <option value="HD" <?php echo $tvshow['quality'] == 'HD' ? 'selected' : ''; ?>>HD</option>
                                            <option value="Full HD" <?php echo $tvshow['quality'] == 'Full HD' ? 'selected' : ''; ?>>Full HD</option>
                                            <option value="4K" <?php echo $tvshow['quality'] == '4K' ? 'selected' : ''; ?>>4K</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-5">
                                    <div class="mb-3">
                                        <label for="language" class="form-label">Language</label>
                                        <input type="text" class="form-control" id="language" name="language" value="<?php echo $tvshow['language']; ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="rating" class="form-label">Rating (0-10)</label>
                                <input type="number" class="form-control" id="rating" name="rating" min="0" max="10" step="0.1" value="<?php echo $tvshow['rating']; ?>">
                            </div>

                            <?php if (mysqli_num_rows($tags_table_exists) > 0 && count($tags) > 0): ?>
                            <div class="mb-3">
                                <label for="tags" class="form-label">Tags</label>
                                <select class="form-select" id="tags" name="tags[]" multiple>
                                    <?php foreach ($tags as $tag): ?>
                                    <option value="<?php echo $tag['id']; ?>" <?php echo in_array($tag['id'], $tvshow_tags) ? 'selected' : ''; ?>><?php echo $tag['name']; ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">Select multiple tags by holding Ctrl (or Cmd on Mac) while clicking. These tags will be displayed on TV show cards.</div>
                            </div>
                            <?php endif; ?>

                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="5"><?php echo $tvshow['description']; ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="trailer_url" class="form-label">Trailer URL (YouTube)</label>
                                <input type="url" class="form-control" id="trailer_url" name="trailer_url" placeholder="https://www.youtube.com/watch?v=..." value="<?php echo $tvshow['trailer_url']; ?>">
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="poster" class="form-label">Poster Image</label>
                                <input type="file" class="form-control" id="poster" name="poster" accept="image/jpeg,image/png,image/webp">
                                <div class="form-text">Recommended size: 300x450px. Max size: 2MB.</div>
                                <div class="mt-3">
                                    <div class="poster-preview-container">
                                        <?php if (!empty($tvshow['poster'])): ?>
                                            <?php if (strpos($tvshow['poster'], 'http') === 0): ?>
                                                <!-- External URL (TMDB) -->
                                                <img id="poster-preview" src="<?php echo $tvshow['poster']; ?>" alt="Poster Preview" class="img-thumbnail" onerror="this.onerror=null; this.src='assets/img/default-poster.jpg';">
                                            <?php elseif (file_exists('../uploads/' . $tvshow['poster'])): ?>
                                                <!-- Local file -->
                                                <img id="poster-preview" src="<?php echo SITE_URL; ?>/uploads/<?php echo $tvshow['poster']; ?>" alt="Poster Preview" class="img-thumbnail">
                                            <?php else: ?>
                                                <!-- Try TMDB path -->
                                                <img id="poster-preview" src="https://image.tmdb.org/t/p/w300<?php echo $tvshow['poster']; ?>" alt="Poster Preview" class="img-thumbnail" onerror="this.onerror=null; this.src='assets/img/default-poster.jpg';">
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <img id="poster-preview" src="assets/img/default-poster.jpg" alt="Poster Preview" class="img-thumbnail">
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="premium_only" name="premium_only" <?php echo $tvshow['premium_only'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="premium_only">Premium Only</label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="featured" name="featured" <?php echo $tvshow['featured'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="featured">Featured</label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">TV Show Info</h6>
                                        <p class="mb-1"><strong>ID:</strong> <?php echo $tvshow['id']; ?></p>
                                        <p class="mb-1"><strong>Added:</strong> <?php echo date('M j, Y g:i A', strtotime($tvshow['created_at'])); ?></p>
                                        <?php if($tvshow['updated_at']): ?>
                                        <p class="mb-1"><strong>Updated:</strong> <?php echo date('M j, Y g:i A', strtotime($tvshow['updated_at'])); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="card">
                                    <div class="card-header bg-warning text-dark">
                                        <h6 class="mb-0">Seasons</h6>
                                    </div>
                                    <div class="card-body">
                                        <?php if(mysqli_num_rows($seasons_result) > 0): ?>
                                            <ul class="list-group">
                                                <?php while($season = mysqli_fetch_assoc($seasons_result)): ?>
                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                        Season <?php echo $season['season_number']; ?>
                                                        <a href="manage_episodes.php?season=<?php echo $season['id']; ?>" class="btn btn-sm btn-primary">
                                                            <i class="fas fa-list"></i> Episodes
                                                        </a>
                                                    </li>
                                                <?php endwhile; ?>
                                            </ul>
                                        <?php else: ?>
                                            <p class="text-center mb-0">No seasons added yet.</p>
                                        <?php endif; ?>
                                        <div class="d-grid mt-3">
                                            <a href="manage_episodes.php?tvshow=<?php echo $tvshow_id; ?>" class="btn btn-warning">
                                                <i class="fas fa-list me-2"></i>Manage Seasons & Episodes
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <a href="tvshows.php" class="btn btn-secondary">Back to TV Shows</a>
                        <button type="submit" name="update_tvshow" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update TV Show
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for poster preview -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const posterInput = document.getElementById('poster');
    const posterPreview = document.getElementById('poster-preview');

    posterInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                posterPreview.src = e.target.result;
            }

            reader.readAsDataURL(this.files[0]);
        }
    });
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
