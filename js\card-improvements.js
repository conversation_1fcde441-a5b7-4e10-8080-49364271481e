/**
 * Card Improvements JavaScript
 * Enhances movie cards functionality and animations
 */

document.addEventListener('DOMContentLoaded', function() {
    // Make entire movie card clickable
    const movieCards = document.querySelectorAll('.movie-card');
    
    movieCards.forEach(card => {
        // Add animation delay based on index for staggered effect
        const index = Array.from(movieCards).indexOf(card);
        card.style.animationDelay = `${index * 0.05}s`;
        
        // Make card clickable if it has a card-link
        const cardLink = card.querySelector('.card-link');
        if (cardLink) {
            card.addEventListener('click', function(e) {
                // Don't trigger if clicking on a button or link inside the card
                if (!e.target.closest('.btn') && !e.target.closest('a:not(.card-link)')) {
                    cardLink.click();
                }
            });
        }
    });
    
    // Add lazy loading for images that don't have it
    const images = document.querySelectorAll('img:not([loading])');
    images.forEach(img => {
        img.setAttribute('loading', 'lazy');
    });
    
    // Enhance hover effects
    const enhanceHoverEffects = () => {
        movieCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px)';
                this.style.boxShadow = '0 15px 30px rgba(0, 0, 0, 0.5)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = '';
                this.style.boxShadow = '';
            });
        });
    };
    
    // Only apply hover effects on non-touch devices
    if (window.matchMedia('(hover: hover)').matches) {
        enhanceHoverEffects();
    }
    
    // Add smooth scrolling for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                e.preventDefault();
                window.scrollTo({
                    top: targetElement.offsetTop - 70,
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // Add image error handling
    const handleImageErrors = () => {
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            img.addEventListener('error', function() {
                // Replace broken images with a placeholder
                this.src = 'images/placeholder.jpg';
                this.alt = 'Image not available';
            });
        });
    };
    
    handleImageErrors();
});
