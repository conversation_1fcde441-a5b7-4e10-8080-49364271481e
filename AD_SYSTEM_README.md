# Advertisement System Documentation

## Overview
This document describes the enhanced advertisement system implemented across the CinePix website. The system provides flexible ad placement options with modern styling and interactive features.

## Files Added/Modified

### New Files
- `includes/ad_placeholder.php` - Ad placeholder component system
- `js/ad-enhancements.js` - JavaScript enhancements for ad interactions
- `css/homepage-modern.css` - Enhanced homepage styling with ad support

### Modified Files
- `index.php` - Added multiple ad placements throughout homepage
- `movie_details.php` - Added ads after reviews and before similar movies
- `tvshow_details.php` - Added ads after episodes and reviews
- `movies.php` - Added top banner and middle ads
- `tvshows.php` - Added top banner and middle ads
- `search.php` - Added top banner and middle ads
- `premium.php` - Added top banner and middle ads
- `profile.php` - Added top banner and sidebar ads
- `css/ad-styling.css` - Enhanced with new ad container styles
- `includes/footer.php` - Added ad enhancement JavaScript

## Ad Placement Locations

### Homepage (index.php)
1. **Top Banner** - After hero slider
2. **Inline Ad** - After Top 10 Movies
3. **Sidebar Layout** - After Top 10 TV Shows (content + sidebar)
4. **Middle Banner** - After Featured TV Shows
5. **Bottom Banner** - After Latest TV Shows
6. **Footer Double** - Before footer (2 side-by-side ads)

### Movie Details (movie_details.php)
1. **After Reviews** - Banner ad after review section
2. **Before Similar Movies** - Inline ad before recommendations

### TV Show Details (tvshow_details.php)
1. **After Episodes** - Inline ad after episode listings
2. **After Reviews** - Banner ad after review section

### Movies Page (movies.php)
1. **Top Banner** - After page header
2. **Middle Banner** - Between movie grid and pagination

### TV Shows Page (tvshows.php)
1. **Top Banner** - After page header
2. **Middle Banner** - Between TV show grid and pagination

### Search Page (search.php)
1. **Top Banner** - After search filters
2. **Middle Banner** - Between search results and pagination

### Premium Page (premium.php)
1. **Top Banner** - After page header
2. **Middle Banner** - Between pricing plans and comparison table

### Profile Page (profile.php)
1. **Top Banner** - After page header
2. **Sidebar Ad** - In left column after recent activity

## Ad Placeholder Functions

### Basic Functions
```php
// Render a simple ad placeholder
renderAdPlaceholder($type, $label, $size)

// Render ad with container wrapper
renderAdSection($type, $label, $size, $containerClass)

// Render two ads side by side
renderDoubleAdSection($leftLabel, $rightLabel, $containerClass)
```

### Quick Usage Functions
```php
topBannerAd()           // Top banner (728x90)
bottomBannerAd()        // Bottom banner (728x90)
sidebarAd()            // Sidebar ad (300x250)
inlineContentAd()      // Inline content ad (728x90)
footerDoubleAd()       // Footer double ads
responsiveAd()         // Responsive mobile/desktop ad
```

### Ad Types Available
- `banner` - Standard banner (728x90)
- `sidebar` - Sidebar ad (300x250)
- `inline` - Inline content ad (728x90)
- `square` - Square ad (250x250)
- `leaderboard` - Leaderboard (728x90)
- `skyscraper` - Skyscraper (160x600)

## Usage Examples

### Basic Ad Placement
```php
<?php require_once 'includes/ad_placeholder.php'; ?>

<!-- Simple banner ad -->
<?php echo renderAdPlaceholder('banner', 'Top Advertisement', '728x90'); ?>

<!-- Ad with container -->
<?php echo renderAdSection('banner', 'Content Advertisement', '728x90'); ?>
```

### Quick Placement
```php
<!-- Quick banner ads -->
<?php echo topBannerAd(); ?>
<?php echo bottomBannerAd(); ?>

<!-- Sidebar ad -->
<?php echo sidebarAd(); ?>

<!-- Double footer ads -->
<?php echo footerDoubleAd(); ?>
```

### Custom Placement
```php
<!-- Custom container class -->
<?php echo renderAdSection('inline', 'Custom Ad', '728x90', 'container-fluid my-5'); ?>

<!-- Responsive ad -->
<?php echo responsiveAd('Mobile Ad (320x50)', 'Desktop Ad (728x90)'); ?>
```

## Styling Classes

### Container Classes
- `.ad-container` - Base ad container
- `.ad-banner` - Banner ad styling
- `.ad-sidebar` - Sidebar ad styling
- `.ad-inline` - Inline ad styling
- `.ad-square` - Square ad styling

### State Classes
- `.ad-loading` - Loading state with shimmer effect
- `.ad-loaded` - Loaded state
- `.ad-visible` - Visible in viewport
- `.ad-premium` - Premium ad styling
- `.ad-featured` - Featured ad styling

## JavaScript Features

### Interactive Features
- Hover effects with scaling and color changes
- Click tracking and analytics integration
- Visibility tracking for impressions
- Loading animations with shimmer effects
- Responsive size updates

### Analytics Integration
```javascript
// Track ad clicks
trackAdClick(adId)

// Track ad impressions
trackAdImpression(adId)
```

### Customization Options
- Ad rotation with multiple messages
- Responsive size adjustments
- Loading delay simulation
- Click animations

## Responsive Design

### Breakpoints
- **Desktop** (>768px): Full size ads (728x90, 300x250)
- **Tablet** (≤768px): Medium size ads
- **Mobile** (≤576px): Small size ads (320x50)

### Mobile Optimizations
- Reduced padding and margins
- Smaller font sizes
- Vertical layout for placeholders
- Touch-friendly interactions

## Integration with Real Ads

### Replacing Placeholders
To replace placeholders with real ads:

1. **Identify the ad container** by its location
2. **Replace the placeholder content** with actual ad code
3. **Maintain the container styling** for consistency
4. **Test responsive behavior** across devices

### Example Integration
```php
<!-- Replace this placeholder -->
<?php echo renderAdPlaceholder('banner', 'Top Advertisement', '728x90'); ?>

<!-- With actual ad code -->
<div class="ad-container ad-banner">
    <!-- Your ad network code here -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
    <ins class="adsbygoogle" style="display:block" data-ad-client="ca-pub-XXXXXXX" data-ad-slot="XXXXXXX"></ins>
    <script>(adsbygoogle = window.adsbygoogle || []).push({});</script>
</div>
```

## Best Practices

### Performance
- Use lazy loading for ads below the fold
- Minimize ad container sizes on mobile
- Implement proper error handling for failed ad loads

### User Experience
- Maintain consistent spacing around ads
- Ensure ads don't interfere with content
- Provide clear visual separation between ads and content

### SEO Considerations
- Use proper semantic markup
- Implement structured data for ad content
- Ensure ads don't negatively impact page speed

## Support and Maintenance

### Regular Tasks
- Monitor ad performance and loading times
- Update responsive breakpoints as needed
- Test ad placements across different devices
- Optimize ad container styles for better engagement

### Troubleshooting
- Check browser console for JavaScript errors
- Verify CSS file loading and cache busting
- Test ad visibility tracking functionality
- Ensure proper analytics integration

## Future Enhancements

### Planned Features
- A/B testing for ad placements
- Dynamic ad size optimization
- Advanced targeting options
- Real-time performance monitoring
- Integration with multiple ad networks

### Customization Options
- Theme-based ad styling
- Seasonal ad container designs
- Interactive ad formats
- Video ad placeholder support
