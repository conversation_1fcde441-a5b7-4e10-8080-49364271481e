<?php
// Set page title
$page_title = 'Premium Plans';

require_once '../includes/config.php';

// Check if is_active column exists in premium_plans table
$check_column_query = "SHOW COLUMNS FROM premium_plans LIKE 'is_active'";
$check_column_result = mysqli_query($conn, $check_column_query);

if (mysqli_num_rows($check_column_result) == 0) {
    // Add is_active column if it doesn't exist
    $add_column_query = "ALTER TABLE premium_plans ADD COLUMN is_active BOOLEAN DEFAULT TRUE";
    mysqli_query($conn, $add_column_query);

    // Update existing plans to be active
    $update_query = "UPDATE premium_plans SET is_active = TRUE WHERE is_active IS NULL";
    mysqli_query($conn, $update_query);
}

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Process form submissions
$success_message = '';
$error_message = '';

// Add Plan
if (isset($_POST['add_plan'])) {
    $name = sanitize($_POST['name']);
    $description = sanitize($_POST['description']);
    $price = (float)$_POST['price'];
    $duration = (int)$_POST['duration'];
    $features = isset($_POST['features']) ? sanitize($_POST['features']) : '';
    $is_active = isset($_POST['is_active']) ? 1 : 0;

    if (empty($name) || $price <= 0 || $duration <= 0) {
        $error_message = 'Name, price, and duration are required.';
    } else {
        // Check if description column exists
        $check_description_column = "SHOW COLUMNS FROM premium_plans LIKE 'description'";
        $description_column_result = mysqli_query($conn, $check_description_column);
        $has_description_column = mysqli_num_rows($description_column_result) > 0;

        // Build the insert query based on available columns
        if ($has_description_column) {
            $add_query = "INSERT INTO premium_plans (name, description, price, duration, features, is_active)
                         VALUES ('$name', '$description', $price, $duration, '$features', $is_active)";
        } else {
            $add_query = "INSERT INTO premium_plans (name, price, duration, features, is_active)
                         VALUES ('$name', $price, $duration, '$features', $is_active)";
        }

        if (mysqli_query($conn, $add_query)) {
            $success_message = 'Premium plan added successfully.';
        } else {
            $error_message = 'Error adding premium plan: ' . mysqli_error($conn);
        }
    }
}

// Update Plan
if (isset($_POST['update_plan'])) {
    $plan_id = (int)$_POST['plan_id'];
    $name = sanitize($_POST['name']);
    $description = sanitize($_POST['description']);
    $price = (float)$_POST['price'];
    $duration = (int)$_POST['duration'];
    $features = isset($_POST['features']) ? sanitize($_POST['features']) : '';
    $is_active = isset($_POST['is_active']) ? 1 : 0;

    if (empty($name) || $price <= 0 || $duration <= 0) {
        $error_message = 'Name, price, and duration are required.';
    } else {
        // Check if description column exists
        $check_description_column = "SHOW COLUMNS FROM premium_plans LIKE 'description'";
        $description_column_result = mysqli_query($conn, $check_description_column);
        $has_description_column = mysqli_num_rows($description_column_result) > 0;

        // Check if updated_at column exists
        $check_updated_at_column = "SHOW COLUMNS FROM premium_plans LIKE 'updated_at'";
        $updated_at_column_result = mysqli_query($conn, $check_updated_at_column);
        $has_updated_at_column = mysqli_num_rows($updated_at_column_result) > 0;

        // Build the update query based on available columns
        if ($has_description_column && $has_updated_at_column) {
            $update_query = "UPDATE premium_plans SET
                            name = '$name',
                            description = '$description',
                            price = $price,
                            duration = $duration,
                            features = '$features',
                            is_active = $is_active,
                            updated_at = NOW()
                            WHERE id = $plan_id";
        } else if ($has_description_column && !$has_updated_at_column) {
            $update_query = "UPDATE premium_plans SET
                            name = '$name',
                            description = '$description',
                            price = $price,
                            duration = $duration,
                            features = '$features',
                            is_active = $is_active
                            WHERE id = $plan_id";
        } else if (!$has_description_column && $has_updated_at_column) {
            $update_query = "UPDATE premium_plans SET
                            name = '$name',
                            price = $price,
                            duration = $duration,
                            features = '$features',
                            is_active = $is_active,
                            updated_at = NOW()
                            WHERE id = $plan_id";
        } else {
            $update_query = "UPDATE premium_plans SET
                            name = '$name',
                            price = $price,
                            duration = $duration,
                            features = '$features',
                            is_active = $is_active
                            WHERE id = $plan_id";
        }

        if (mysqli_query($conn, $update_query)) {
            $success_message = 'Premium plan updated successfully.';
        } else {
            $error_message = 'Error updating premium plan: ' . mysqli_error($conn);
        }
    }
}

// Delete Plan
if (isset($_GET['delete']) && $_GET['delete'] > 0) {
    $plan_id = (int)$_GET['delete'];

    // Check if plan is in use
    $check_subscriptions_query = "SELECT COUNT(*) as count FROM subscriptions WHERE plan_id = $plan_id";
    $check_subscriptions_result = mysqli_query($conn, $check_subscriptions_query);
    $subscriptions_count = mysqli_fetch_assoc($check_subscriptions_result)['count'];

    if ($subscriptions_count > 0) {
        $error_message = 'Cannot delete plan because it is in use by ' . $subscriptions_count . ' subscriptions.';
    } else {
        // Delete plan
        $delete_query = "DELETE FROM premium_plans WHERE id = $plan_id";

        if (mysqli_query($conn, $delete_query)) {
            $success_message = 'Premium plan deleted successfully.';
        } else {
            $error_message = 'Error deleting premium plan: ' . mysqli_error($conn);
        }
    }
}

// Get plan to edit
$edit_plan = null;
if (isset($_GET['edit']) && $_GET['edit'] > 0) {
    $plan_id = (int)$_GET['edit'];

    $edit_query = "SELECT * FROM premium_plans WHERE id = $plan_id";
    $edit_result = mysqli_query($conn, $edit_query);

    if (mysqli_num_rows($edit_result) > 0) {
        $edit_plan = mysqli_fetch_assoc($edit_result);
    }
}

// Get plans list
$plans_query = "SELECT p.*,
               (SELECT COUNT(*) FROM subscriptions WHERE plan_id = p.id) as subscriptions_count
               FROM premium_plans p
               ORDER BY p.price";
$plans_result = mysqli_query($conn, $plans_query);

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Premium Plans</h1>
            </div>
            <div class="topbar-actions">
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPlanModal">
                    <i class="fas fa-plus-circle me-2"></i>Add New Plan
                </button>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <?php if($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="row">
            <?php if($edit_plan): ?>
            <!-- Edit Plan Form -->
            <div class="col-lg-4 mb-4">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Edit Premium Plan</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="" class="needs-validation" novalidate>
                            <input type="hidden" name="plan_id" value="<?php echo $edit_plan['id']; ?>">

                            <div class="mb-3">
                                <label for="name" class="form-label">Plan Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" value="<?php echo $edit_plan['name']; ?>" required>
                                <div class="invalid-feedback">
                                    Please enter a plan name.
                                </div>
                            </div>

                            <?php
                            // Check if description column exists
                            $check_description_column = "SHOW COLUMNS FROM premium_plans LIKE 'description'";
                            $description_column_result = mysqli_query($conn, $check_description_column);
                            $has_description_column = mysqli_num_rows($description_column_result) > 0;

                            if ($has_description_column):
                            ?>
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3"><?php echo $edit_plan['description']; ?></textarea>
                            </div>
                            <?php endif; ?>

                            <div class="mb-3">
                                <label for="price" class="form-label">Price (৳) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="price" name="price" value="<?php echo $edit_plan['price']; ?>" min="0" step="1" required>
                                <div class="invalid-feedback">
                                    Please enter a valid price.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="duration" class="form-label">Duration (days) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="duration" name="duration" value="<?php echo $edit_plan['duration']; ?>" min="1" required>
                                <div class="invalid-feedback">
                                    Please enter a valid duration.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="features" class="form-label">Features (one per line)</label>
                                <textarea class="form-control" id="features" name="features" rows="4" placeholder="Access to premium movies&#10;Access to premium TV shows&#10;HD quality streaming"><?php echo $edit_plan['features']; ?></textarea>
                                <div class="form-text">Enter each feature on a new line.</div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="is_active" name="is_active" <?php echo $edit_plan['is_active'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_active">Active</label>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" name="update_plan" class="btn btn-primary">Update Plan</button>
                                <a href="premium_plans.php" class="btn btn-secondary">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Plans List -->
            <div class="col-lg-<?php echo $edit_plan ? '8' : '12'; ?> mb-4">
                <div class="card shadow">
                    <div class="card-header bg-white py-3">
                        <h6 class="m-0 font-weight-bold text-primary">All Premium Plans</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php if(mysqli_num_rows($plans_result) > 0): ?>
                                <?php while($plan = mysqli_fetch_assoc($plans_result)): ?>
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="card h-100 <?php echo isset($plan['is_active']) && $plan['is_active'] ? 'border-success' : 'border-secondary'; ?>">
                                        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                                            <h5 class="mb-0 fw-bold"><?php echo $plan['name']; ?></h5>
                                            <?php if(isset($plan['is_active']) && $plan['is_active']): ?>
                                            <span class="badge bg-success">Active</span>
                                            <?php else: ?>
                                            <span class="badge bg-secondary">Inactive</span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="card-body">
                                            <div class="text-center mb-3">
                                                <h2 class="text-primary mb-0">৳<?php echo number_format($plan['price'], 0); ?></h2>
                                                <p class="text-muted"><?php echo $plan['duration']; ?> days</p>
                                            </div>

                                            <?php
                                            // Check if description column exists and has value
                                            $check_description_column = "SHOW COLUMNS FROM premium_plans LIKE 'description'";
                                            $description_column_result = mysqli_query($conn, $check_description_column);
                                            $has_description_column = mysqli_num_rows($description_column_result) > 0;

                                            if ($has_description_column && !empty($plan['description'])):
                                            ?>
                                            <p class="mb-3"><?php echo $plan['description']; ?></p>
                                            <?php endif; ?>

                                            <?php if(!empty($plan['features'])): ?>
                                            <ul class="list-group list-group-flush mb-3">
                                                <?php foreach(explode("\n", $plan['features']) as $feature): ?>
                                                <li class="list-group-item border-0 ps-0">
                                                    <i class="fas fa-check text-success me-2"></i> <?php echo $feature; ?>
                                                </li>
                                                <?php endforeach; ?>
                                            </ul>
                                            <?php endif; ?>

                                            <div class="d-flex justify-content-between align-items-center mt-3">
                                                <span class="text-muted">
                                                    <i class="fas fa-users me-1"></i> <?php echo $plan['subscriptions_count']; ?> subscribers
                                                </span>
                                                <div class="btn-group">
                                                    <a href="premium_plans.php?edit=<?php echo $plan['id']; ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if($plan['subscriptions_count'] == 0): ?>
                                                    <a href="premium_plans.php?delete=<?php echo $plan['id']; ?>" class="btn btn-sm btn-danger btn-delete" data-bs-toggle="tooltip" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                    <?php else: ?>
                                                    <button type="button" class="btn btn-sm btn-secondary" data-bs-toggle="tooltip" title="Cannot delete (in use)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <div class="col-12 text-center">
                                    <p>No premium plans found.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Plan Modal -->
<div class="modal fade" id="addPlanModal" tabindex="-1" aria-labelledby="addPlanModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addPlanModalLabel">Add New Premium Plan</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="POST" action="" class="needs-validation" novalidate>
                    <div class="mb-3">
                        <label for="new_name" class="form-label">Plan Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="new_name" name="name" required>
                        <div class="invalid-feedback">
                            Please enter a plan name.
                        </div>
                    </div>

                    <?php
                    // Check if description column exists
                    $check_description_column = "SHOW COLUMNS FROM premium_plans LIKE 'description'";
                    $description_column_result = mysqli_query($conn, $check_description_column);
                    $has_description_column = mysqli_num_rows($description_column_result) > 0;

                    if ($has_description_column):
                    ?>
                    <div class="mb-3">
                        <label for="new_description" class="form-label">Description</label>
                        <textarea class="form-control" id="new_description" name="description" rows="3"></textarea>
                    </div>
                    <?php endif; ?>

                    <div class="mb-3">
                        <label for="new_price" class="form-label">Price (৳) <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="new_price" name="price" min="0" step="1" required>
                        <div class="invalid-feedback">
                            Please enter a valid price.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="new_duration" class="form-label">Duration (days) <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="new_duration" name="duration" min="1" required>
                        <div class="invalid-feedback">
                            Please enter a valid duration.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="new_features" class="form-label">Features (one per line)</label>
                        <textarea class="form-control" id="new_features" name="features" rows="4" placeholder="Access to premium movies&#10;Access to premium TV shows&#10;HD quality streaming"></textarea>
                        <div class="form-text">Enter each feature on a new line.</div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="new_is_active" name="is_active" checked>
                        <label class="form-check-label" for="new_is_active">Active</label>
                    </div>

                    <div class="d-grid">
                        <button type="submit" name="add_plan" class="btn btn-primary">Add Plan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
