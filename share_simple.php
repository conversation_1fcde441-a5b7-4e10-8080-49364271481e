<?php
require_once 'includes/config.php';

// Debug mode
$debug_mode = isset($_GET['debug']) && $_GET['debug'] == '1';

if ($debug_mode) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

// Get token from URL
$token = isset($_GET['token']) ? $_GET['token'] : '';

if (empty($token)) {
    if ($debug_mode) {
        echo "<h2>টোকেন নেই</h2>";
        echo "<p>URL: " . $_SERVER['REQUEST_URI'] . "</p>";
        echo "<p>GET parameters: " . print_r($_GET, true) . "</p>";
        exit;
    }
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

if ($debug_mode) {
    echo "<h2>ডিবাগ মোড</h2>";
    echo "<p><strong>টোকেন:</strong> $token</p>";
}

// Clean token
$token = mysqli_real_escape_string($conn, $token);

// Get shared link details
$query = "SELECT sl.*, 
                 CASE 
                     WHEN sl.content_type = 'movie' THEN m.title
                     WHEN sl.content_type = 'tvshow' THEN t.title
                 END as content_title,
                 CASE 
                     WHEN sl.content_type = 'movie' THEN m.description
                     WHEN sl.content_type = 'tvshow' THEN t.description
                 END as content_description,
                 CASE 
                     WHEN sl.content_type = 'movie' THEN m.poster
                     WHEN sl.content_type = 'tvshow' THEN t.poster
                 END as content_poster,
                 CASE 
                     WHEN sl.content_type = 'movie' THEN m.banner
                     WHEN sl.content_type = 'tvshow' THEN t.banner
                 END as content_banner,
                 CASE 
                     WHEN sl.content_type = 'movie' THEN m.rating
                     WHEN sl.content_type = 'tvshow' THEN t.rating
                 END as content_rating,
                 CASE 
                     WHEN sl.content_type = 'movie' THEN m.release_year
                     WHEN sl.content_type = 'tvshow' THEN t.release_year
                 END as content_year
          FROM shared_links sl
          LEFT JOIN movies m ON sl.content_type = 'movie' AND sl.content_id = m.id
          LEFT JOIN tvshows t ON sl.content_type = 'tvshow' AND sl.content_id = t.id
          WHERE sl.link_token = '$token' AND sl.is_active = 1";

if ($debug_mode) {
    echo "<h3>কুয়েরি:</h3>";
    echo "<pre>" . htmlspecialchars($query) . "</pre>";
}

$result = mysqli_query($conn, $query);

if (!$result) {
    if ($debug_mode) {
        echo "<h3>কুয়েরি ফেইল:</h3>";
        echo "<p>" . mysqli_error($conn) . "</p>";
        exit;
    }
    header('HTTP/1.0 500 Internal Server Error');
    include '404.php';
    exit;
}

$num_rows = mysqli_num_rows($result);

if ($debug_mode) {
    echo "<h3>ফলাফল:</h3>";
    echo "<p>সারি সংখ্যা: $num_rows</p>";
}

if ($num_rows == 0) {
    if ($debug_mode) {
        echo "<h3>কোনো শেয়ার লিংক পাওয়া যায়নি</h3>";
        echo "<p>টোকেন: $token</p>";
        exit;
    }
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

$shared_link = mysqli_fetch_assoc($result);

if ($debug_mode) {
    echo "<h3>শেয়ার লিংক তথ্য:</h3>";
    echo "<pre>" . print_r($shared_link, true) . "</pre>";
}

// Check if link has expired
if ($shared_link['expires_at'] && strtotime($shared_link['expires_at']) < time()) {
    $error_message = "এই শেয়ার লিংকের মেয়াদ শেষ হয়ে গেছে।";
    $show_error = true;
}

// Check if access limit reached
elseif ($shared_link['access_limit'] > 0 && $shared_link['access_count'] >= $shared_link['access_limit']) {
    $error_message = "এই শেয়ার লিংকের এক্সেস লিমিট শেষ হয়ে গেছে।";
    $show_error = true;
}

// Check password if required
elseif ($shared_link['password'] && (!isset($_POST['password']) || !password_verify($_POST['password'], $shared_link['password']))) {
    $show_password_form = true;
    if (isset($_POST['password'])) {
        $password_error = "ভুল পাসওয়ার্ড!";
    }
}

else {
    // Log the access
    $ip_address = $_SERVER['REMOTE_ADDR'];
    $user_agent = $_SERVER['HTTP_USER_AGENT'];
    $referrer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
    $user_id = isLoggedIn() ? $_SESSION['user_id'] : NULL;
    
    $log_query = "INSERT INTO shared_link_access_logs (shared_link_id, ip_address, user_agent, referrer, user_id, device_type, browser) 
                  VALUES (?, ?, ?, ?, ?, 'Desktop', 'Unknown')";
    $log_stmt = mysqli_prepare($conn, $log_query);
    mysqli_stmt_bind_param($log_stmt, 'issss', $shared_link['id'], $ip_address, $user_agent, $referrer, $user_id);
    mysqli_stmt_execute($log_stmt);
    
    // Update access count
    $update_query = "UPDATE shared_links SET access_count = access_count + 1 WHERE id = ?";
    $update_stmt = mysqli_prepare($conn, $update_query);
    mysqli_stmt_bind_param($update_stmt, 'i', $shared_link['id']);
    mysqli_stmt_execute($update_stmt);
    
    // Get download links if allowed
    if ($shared_link['allow_download']) {
        if ($shared_link['content_type'] == 'movie') {
            $links_query = "SELECT * FROM download_links WHERE content_type = 'movie' AND content_id = ? ORDER BY quality DESC";
        } else {
            $links_query = "SELECT el.*, e.episode_number, e.season_number 
                           FROM episode_links el 
                           JOIN episodes e ON el.episode_id = e.id 
                           WHERE e.tvshow_id = ? AND el.link_type = 'download' 
                           ORDER BY e.season_number, e.episode_number, el.quality DESC";
        }
        $links_stmt = mysqli_prepare($conn, $links_query);
        mysqli_stmt_bind_param($links_stmt, 'i', $shared_link['content_id']);
        mysqli_stmt_execute($links_stmt);
        
        if (function_exists('mysqli_stmt_get_result')) {
            $download_links = mysqli_stmt_get_result($links_stmt);
        } else {
            $download_links = false;
        }
    }
    
    // Get streaming links if allowed
    if ($shared_link['allow_streaming']) {
        if ($shared_link['content_type'] == 'movie') {
            $stream_query = "SELECT * FROM streaming_links WHERE content_type = 'movie' AND content_id = ? ORDER BY quality DESC";
        } else {
            $stream_query = "SELECT sl.*, e.episode_number, e.season_number 
                            FROM episode_links sl 
                            JOIN episodes e ON sl.episode_id = e.id 
                            WHERE e.tvshow_id = ? AND sl.link_type = 'stream' 
                            ORDER BY e.season_number, e.episode_number, sl.quality DESC";
        }
        $stream_stmt = mysqli_prepare($conn, $stream_query);
        mysqli_stmt_bind_param($stream_stmt, 'i', $shared_link['content_id']);
        mysqli_stmt_execute($stream_stmt);
        
        if (function_exists('mysqli_stmt_get_result')) {
            $streaming_links = mysqli_stmt_get_result($stream_stmt);
        } else {
            $streaming_links = false;
        }
    }
}

$page_title = $shared_link['title'];
require_once 'includes/header.php';
?>

<style>
.share-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.share-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.share-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.share-header {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 2rem;
    text-align: center;
}

.share-content {
    padding: 2rem;
}

.content-poster {
    width: 200px;
    height: 300px;
    object-fit: cover;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.access-info {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 2rem;
}

.download-section, .streaming-section {
    background: #fff;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.link-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid #007bff;
}

.error-container {
    text-align: center;
    padding: 3rem;
}

.password-form {
    max-width: 400px;
    margin: 2rem auto;
    padding: 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}
</style>

<div class="share-page">
    <div class="share-container">
        <div class="share-card">
            <div class="share-header">
                <h1><i class="fas fa-share-alt"></i> <?php echo htmlspecialchars($shared_link['title']); ?></h1>
                <p class="mb-0"><?php echo htmlspecialchars($shared_link['description']); ?></p>
            </div>
            
            <?php if (isset($show_error)): ?>
            <div class="error-container">
                <i class="fas fa-exclamation-triangle fa-4x text-warning mb-3"></i>
                <h3>দুঃখিত!</h3>
                <p class="text-muted"><?php echo $error_message; ?></p>
                <a href="<?php echo SITE_URL; ?>" class="btn btn-primary">হোম পেজে ফিরে যান</a>
            </div>
            
            <?php elseif (isset($show_password_form)): ?>
            <div class="share-content">
                <div class="password-form">
                    <h4 class="text-center mb-3"><i class="fas fa-lock"></i> পাসওয়ার্ড প্রয়োজন</h4>
                    <?php if (isset($password_error)): ?>
                    <div class="alert alert-danger"><?php echo $password_error; ?></div>
                    <?php endif; ?>
                    <form method="POST">
                        <div class="mb-3">
                            <input type="password" name="password" class="form-control" placeholder="পাসওয়ার্ড লিখুন" required>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">এক্সেস করুন</button>
                    </form>
                </div>
            </div>
            
            <?php else: ?>
            <div class="share-content">
                <div class="row">
                    <div class="col-md-3 text-center mb-4">
                        <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $shared_link['content_poster']; ?>" 
                             alt="<?php echo htmlspecialchars($shared_link['content_title']); ?>" 
                             class="content-poster">
                    </div>
                    
                    <div class="col-md-9">
                        <h2><?php echo htmlspecialchars($shared_link['content_title']); ?></h2>
                        <p class="text-muted"><?php echo htmlspecialchars($shared_link['content_description']); ?></p>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>টাইপ:</strong> <?php echo $shared_link['content_type'] == 'movie' ? 'মুভি' : 'টিভি সিরিজ'; ?>
                            </div>
                            <div class="col-md-6">
                                <strong>রিলিজ:</strong> <?php echo $shared_link['content_year']; ?>
                            </div>
                        </div>
                        
                        <div class="access-info">
                            <div class="row">
                                <div class="col-md-4">
                                    <i class="fas fa-eye text-primary"></i>
                                    <strong>ভিউ:</strong> <?php echo $shared_link['access_count']; ?>
                                    <?php if ($shared_link['access_limit'] > 0): ?>
                                    / <?php echo $shared_link['access_limit']; ?>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-4">
                                    <i class="fas fa-download text-success"></i>
                                    <strong>ডাউনলোড:</strong> <?php echo $shared_link['allow_download'] ? 'অনুমতি আছে' : 'অনুমতি নেই'; ?>
                                </div>
                                <div class="col-md-4">
                                    <i class="fas fa-play text-info"></i>
                                    <strong>স্ট্রিমিং:</strong> <?php echo $shared_link['allow_streaming'] ? 'অনুমতি আছে' : 'অনুমতি নেই'; ?>
                                </div>
                            </div>
                        </div>
                        
                        <?php if ($shared_link['allow_download'] && isset($download_links) && $download_links && mysqli_num_rows($download_links) > 0): ?>
                        <div class="download-section">
                            <h4><i class="fas fa-download"></i> ডাউনলোড লিংক</h4>
                            <?php while ($link = mysqli_fetch_assoc($download_links)): ?>
                            <div class="link-item">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <strong><?php echo $link['quality']; ?></strong>
                                        <?php if (isset($link['episode_number'])): ?>
                                        - S<?php echo $link['season_number']; ?>E<?php echo $link['episode_number']; ?>
                                        <?php endif; ?>
                                        <?php if (isset($link['server_name'])): ?>
                                        <span class="badge bg-info"><?php echo $link['server_name']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <a href="<?php echo $link['link_url']; ?>" class="btn btn-success btn-sm download-link" target="_blank">
                                            <i class="fas fa-download"></i> ডাউনলোড
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <?php endwhile; ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($shared_link['allow_streaming'] && isset($streaming_links) && $streaming_links && mysqli_num_rows($streaming_links) > 0): ?>
                        <div class="streaming-section">
                            <h4><i class="fas fa-play"></i> স্ট্রিমিং লিংক</h4>
                            <?php while ($link = mysqli_fetch_assoc($streaming_links)): ?>
                            <div class="link-item">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <strong><?php echo $link['quality']; ?></strong>
                                        <?php if (isset($link['episode_number'])): ?>
                                        - S<?php echo $link['season_number']; ?>E<?php echo $link['episode_number']; ?>
                                        <?php endif; ?>
                                        <?php if (isset($link['server_name'])): ?>
                                        <span class="badge bg-primary"><?php echo $link['server_name']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <a href="<?php echo $link['link_url']; ?>" class="btn btn-primary btn-sm stream-link" target="_blank">
                                            <i class="fas fa-play"></i> প্লে করুন
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <?php endwhile; ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ((!$shared_link['allow_download'] || !isset($download_links) || !$download_links || mysqli_num_rows($download_links) == 0) && 
                                  (!$shared_link['allow_streaming'] || !isset($streaming_links) || !$streaming_links || mysqli_num_rows($streaming_links) == 0)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> এই কন্টেন্টের জন্য কোনো লিংক উপলব্ধ নেই।
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="<?php echo SITE_URL; ?>" class="btn btn-outline-light">
                <i class="fas fa-home"></i> হোম পেজে ফিরে যান
            </a>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?> 