import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:cinepix_app/constants/app_constants.dart';
import 'package:cinepix_app/controllers/watch_history_controller.dart';
import 'package:cinepix_app/controllers/tv_show_controller.dart';
import 'package:cinepix_app/models/download_link.dart';

// এই লাইনটি পরিবর্তন করুন
// import 'package:cinepix_app/widgets/enhanced_video_player.dart';
// এই লাইনটি যোগ করুন
import 'package:cinepix_app/widgets/exo_video_player.dart';
import 'package:cinepix_app/services/storage_service.dart';

class VideoPlayerScreen extends StatefulWidget {
  final String title;
  final DownloadLink downloadLink;
  final String contentType;
  final int contentId;
  final String backdropUrl;
  final int? tvShowId; // Added for next episode functionality

  const VideoPlayerScreen({
    super.key,
    required this.title,
    required this.downloadLink,
    required this.contentType,
    required this.contentId,
    this.backdropUrl = '',
    this.tvShowId,
  });

  @override
  State<VideoPlayerScreen> createState() => _VideoPlayerScreenState();
}

class _VideoPlayerScreenState extends State<VideoPlayerScreen> {
  final StorageService _storageService = StorageService();
  final WatchHistoryController _watchHistoryController =
      Get.find<WatchHistoryController>();
  final TvShowController _tvShowController = Get.find<TvShowController>();
  Duration? _initialPosition;
  bool _isFullScreen = false;

  @override
  void initState() {
    super.initState();
    _loadLastPosition();
    _setLandscapeOrientation();
  }

  Future<void> _loadLastPosition() async {
    final key = '${widget.contentType}_${widget.contentId}_position';
    final positionStr = await _storageService.getWithExpiry(key);

    if (positionStr != null) {
      setState(() {
        _initialPosition = Duration(milliseconds: int.parse(positionStr));
      });
    }
  }

  void _savePosition(Duration position) async {
    final key = '${widget.contentType}_${widget.contentId}_position';
    await _storageService.saveWithExpiry(
      key,
      position.inMilliseconds.toString(),
      AppConstants.cacheExpiryMinutes,
    );

    // Update watch history with current position
    _watchHistoryController.updateWatchHistory(
      contentType: widget.contentType,
      contentId: widget.contentId,
      parentId: widget.tvShowId ?? 0, // Use tvShowId as parentId for episodes
      title: widget.title,
      posterUrl:
          '', // This will be updated only if it's empty in the controller
      backdropUrl: widget.backdropUrl, // Use backdrop URL if available
      duration: widget.downloadLink.duration ??
          7200, // Default to 2 hours if not available
      watchedPosition: position.inSeconds,
    );
  }

  void _setLandscapeOrientation() {
    // Set landscape orientation
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    // Hide status bar and navigation bar for true fullscreen experience
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.immersiveSticky,
      overlays: [], // Hide all system overlays
    );

    setState(() {
      _isFullScreen = true;
    });
  }

  void _resetOrientation() {
    // Reset to all orientations
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    // Restore system UI
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.edgeToEdge,
      overlays: SystemUiOverlay.values, // Show all system overlays
    );

    setState(() {
      _isFullScreen = false;
    });
  }

  @override
  void dispose() {
    _resetOrientation();
    super.dispose();
  }

  // Handle next episode functionality
  Future<void> _handleNextEpisode() async {
    if (widget.contentType != 'episode' || widget.tvShowId == null) {
      return;
    }

    // Load TV show details if needed
    if (_tvShowController.currentTvShow.value == null ||
        _tvShowController.currentTvShow.value!.id != widget.tvShowId) {
      await _tvShowController.loadTvShowDetails(widget.tvShowId!);
    }

    // Try to load next episode
    final nextEpisodeLink = await _tvShowController.loadNextEpisodeDetails();
    if (nextEpisodeLink == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('পরবর্তী এপিসোড পাওয়া যায়নি'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    // Get next episode info
    final nextEpisode = _tvShowController.currentEpisode.value;
    if (nextEpisode == null) return;

    final tvShow = _tvShowController.currentTvShow.value;
    if (tvShow == null) return;

    // Navigate to next episode
    Get.off(() => VideoPlayerScreen(
          title:
              '${tvShow.title} S${nextEpisode.seasonNumber}E${nextEpisode.episodeNumber} - ${nextEpisode.title}',
          downloadLink: nextEpisodeLink,
          contentType: 'episode',
          contentId: nextEpisode.id,
          backdropUrl: tvShow.banner,
          tvShowId: tvShow.id,
        ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: _isFullScreen
          ? null
          : AppBar(
              title: Text(widget.title),
              backgroundColor: Colors.black,
            ),
      body: SafeArea(
        child: Center(
          child: ExoVideoPlayer(
            downloadLink: widget.downloadLink,
            title: widget.title,
            contentType: widget.contentType,
            contentId: widget.contentId,
            onPositionChanged: _savePosition,
            initialPosition: _initialPosition ?? Duration.zero,
            onNextEpisode:
                widget.contentType == 'episode' && widget.tvShowId != null
                    ? _handleNextEpisode
                    : null,
          ),
        ),
      ),
    );
  }
}
