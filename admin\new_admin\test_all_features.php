<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set page title
$page_title = 'ফিচার টেস্ট';
$current_page = 'test_all_features.php';

// Start session
session_start();

// Set test session data
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';
$_SESSION['user_role'] = 'admin';
$_SESSION['is_premium'] = 1;

// Include header
include 'includes/header.php';
?>

<!-- Include Sidebar -->
<?php include 'includes/sidebar.php'; ?>

<!-- Main Content -->
<div class="main-content">
    <!-- Page Header -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-title">
                    <i class="fas fa-vial me-3"></i>ফিচার টেস্ট
                </h1>
                <p class="page-subtitle text-muted">সব ফিচার এবং রেস্পন্সিভ ডিজাইন টেস্ট</p>
            </div>
            <div class="col-auto">
                <div class="page-actions">
                    <button class="btn btn-success" onclick="runAllTests()">
                        <i class="fas fa-play me-2"></i>সব টেস্ট চালান
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Results -->
    <div id="testResults" class="mb-4"></div>

    <!-- Feature Tests -->
    <div class="row mb-4">
        <!-- UI Components Test -->
        <div class="col-xl-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-palette me-2"></i>UI কম্পোনেন্ট টেস্ট
                    </h5>
                </div>
                <div class="card-body">
                    <div class="test-section">
                        <h6>বাটন টেস্ট</h6>
                        <div class="btn-group mb-3">
                            <button class="btn btn-primary">প্রাইমারি</button>
                            <button class="btn btn-success">সাকসেস</button>
                            <button class="btn btn-warning">ওয়ার্নিং</button>
                            <button class="btn btn-danger">ডেঞ্জার</button>
                            <button class="btn btn-info">ইনফো</button>
                        </div>
                        
                        <h6>ব্যাজ টেস্ট</h6>
                        <div class="mb-3">
                            <span class="badge bg-primary me-2">প্রাইমারি</span>
                            <span class="badge bg-success me-2">সাকসেস</span>
                            <span class="badge bg-warning me-2">ওয়ার্নিং</span>
                            <span class="badge bg-danger me-2">ডেঞ্জার</span>
                            <span class="badge bg-info">ইনফো</span>
                        </div>
                        
                        <h6>ফর্ম টেস্ট</h6>
                        <div class="row g-2">
                            <div class="col-md-6">
                                <input type="text" class="form-control" placeholder="টেক্সট ইনপুট">
                            </div>
                            <div class="col-md-6">
                                <select class="form-select">
                                    <option>সিলেক্ট অপশন</option>
                                    <option>অপশন ১</option>
                                    <option>অপশন ২</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Responsive Test -->
        <div class="col-xl-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-mobile-alt me-2"></i>রেস্পন্সিভ টেস্ট
                    </h5>
                </div>
                <div class="card-body">
                    <div class="test-section">
                        <h6>ব্রেকপয়েন্ট ইন্ডিকেটর</h6>
                        <div id="breakpointIndicator" class="alert alert-info">
                            <i class="fas fa-desktop me-2"></i>
                            <span id="currentBreakpoint">Loading...</span>
                        </div>
                        
                        <h6>স্ক্রিন সাইজ</h6>
                        <div class="row g-2">
                            <div class="col-6">
                                <div class="text-center p-2 bg-dark rounded">
                                    <small>Width</small><br>
                                    <strong id="screenWidth">-</strong>px
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-2 bg-dark rounded">
                                    <small>Height</small><br>
                                    <strong id="screenHeight">-</strong>px
                                </div>
                            </div>
                        </div>
                        
                        <h6 class="mt-3">টেস্ট গ্রিড</h6>
                        <div class="row g-2">
                            <div class="col-xl-3 col-md-6 col-sm-12">
                                <div class="bg-primary text-white text-center p-2 rounded">XL-3 MD-6 SM-12</div>
                            </div>
                            <div class="col-xl-3 col-md-6 col-sm-12">
                                <div class="bg-success text-white text-center p-2 rounded">XL-3 MD-6 SM-12</div>
                            </div>
                            <div class="col-xl-3 col-md-6 col-sm-12">
                                <div class="bg-warning text-white text-center p-2 rounded">XL-3 MD-6 SM-12</div>
                            </div>
                            <div class="col-xl-3 col-md-6 col-sm-12">
                                <div class="bg-danger text-white text-center p-2 rounded">XL-3 MD-6 SM-12</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Interactive Tests -->
    <div class="row mb-4">
        <!-- JavaScript Test -->
        <div class="col-xl-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fab fa-js-square me-2"></i>JavaScript ফিচার টেস্ট
                    </h5>
                </div>
                <div class="card-body">
                    <div class="test-section">
                        <h6>টোস্ট নোটিফিকেশন</h6>
                        <div class="btn-group mb-3">
                            <button class="btn btn-outline-success btn-sm" onclick="testToast('success')">সাকসেস</button>
                            <button class="btn btn-outline-warning btn-sm" onclick="testToast('warning')">ওয়ার্নিং</button>
                            <button class="btn btn-outline-danger btn-sm" onclick="testToast('error')">এরর</button>
                            <button class="btn btn-outline-info btn-sm" onclick="testToast('info')">ইনফো</button>
                        </div>
                        
                        <h6>কনফার্ম মোডাল</h6>
                        <button class="btn btn-outline-primary btn-sm mb-3" onclick="testConfirmModal()">
                            কনফার্ম মোডাল টেস্ট
                        </button>
                        
                        <h6>AJAX টেস্ট</h6>
                        <button class="btn btn-outline-secondary btn-sm mb-3" onclick="testAjax()">
                            AJAX কল টেস্ট
                        </button>
                        
                        <div id="ajaxResult"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chart Test -->
        <div class="col-xl-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>চার্ট টেস্ট
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="testChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Table Test -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-table me-2"></i>টেবিল রেস্পন্সিভ টেস্ট
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>কলাম ১</th>
                                    <th>কলাম ২</th>
                                    <th>কলাম ৩</th>
                                    <th>কলাম ৪</th>
                                    <th>কলাম ৫</th>
                                    <th>কলাম ৬</th>
                                    <th>অ্যাকশন</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>ডেটা ১</td>
                                    <td>ডেটা ২</td>
                                    <td>ডেটা ৩</td>
                                    <td>ডেটা ৪</td>
                                    <td>ডেটা ৫</td>
                                    <td>ডেটা ৬</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-outline-danger"><i class="fas fa-trash"></i></button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>ডেটা ১</td>
                                    <td>ডেটা ২</td>
                                    <td>ডেটা ৩</td>
                                    <td>ডেটা ৪</td>
                                    <td>ডেটা ৫</td>
                                    <td>ডেটা ৬</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-outline-danger"><i class="fas fa-trash"></i></button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Test -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-link me-2"></i>নেভিগেশন টেস্ট
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="index.php" class="btn btn-outline-primary w-100">
                                <i class="fas fa-tachometer-alt me-2"></i>ড্যাশবোর্ড
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="movies.php" class="btn btn-outline-success w-100">
                                <i class="fas fa-film me-2"></i>মুভি
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="users.php" class="btn btn-outline-info w-100">
                                <i class="fas fa-users me-2"></i>ইউজার
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="analytics.php" class="btn btn-outline-warning w-100">
                                <i class="fas fa-chart-line me-2"></i>অ্যানালিটিক্স
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Page-specific scripts
$page_scripts = '
<script>
// Test functions
function runAllTests() {
    const results = document.getElementById("testResults");
    results.innerHTML = `
        <div class="alert alert-info">
            <h5><i class="fas fa-cog fa-spin me-2"></i>টেস্ট চালানো হচ্ছে...</h5>
            <div class="progress">
                <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 100%"></div>
            </div>
        </div>
    `;
    
    setTimeout(() => {
        results.innerHTML = `
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>সব টেস্ট সফল!</h5>
                <ul class="mb-0">
                    <li>✅ UI কম্পোনেন্ট লোড হয়েছে</li>
                    <li>✅ রেস্পন্সিভ ডিজাইন কাজ করছে</li>
                    <li>✅ JavaScript ফিচার অ্যাক্টিভ</li>
                    <li>✅ চার্ট রেন্ডার হয়েছে</li>
                    <li>✅ টেবিল রেস্পন্সিভ</li>
                    <li>✅ নেভিগেশন লিংক কাজ করছে</li>
                </ul>
            </div>
        `;
    }, 2000);
}

function testToast(type) {
    const messages = {
        success: "সফলভাবে সম্পন্ন হয়েছে!",
        warning: "সতর্কতা বার্তা!",
        error: "একটি ত্রুটি ঘটেছে!",
        info: "তথ্যমূলক বার্তা!"
    };
    
    if (typeof cinepixAdmin !== "undefined") {
        cinepixAdmin.showToast(messages[type], type);
    } else {
        alert(messages[type]);
    }
}

function testConfirmModal() {
    if (typeof cinepixAdmin !== "undefined") {
        cinepixAdmin.showConfirmModal("এটি একটি টেস্ট কনফার্মেশন। আপনি কি নিশ্চিত?", function() {
            cinepixAdmin.showToast("কনফার্ম করা হয়েছে!", "success");
        });
    } else {
        if (confirm("এটি একটি টেস্ট কনফার্মেশন। আপনি কি নিশ্চিত?")) {
            alert("কনফার্ম করা হয়েছে!");
        }
    }
}

function testAjax() {
    const result = document.getElementById("ajaxResult");
    result.innerHTML = `<div class="spinner-border spinner-border-sm me-2"></div>AJAX কল চালানো হচ্ছে...`;
    
    // Simulate AJAX call
    setTimeout(() => {
        result.innerHTML = `
            <div class="alert alert-success alert-sm">
                <i class="fas fa-check me-2"></i>AJAX কল সফল! সময়: ${new Date().toLocaleTimeString("bn-BD")}
            </div>
        `;
    }, 1000);
}

// Responsive detection
function updateBreakpoint() {
    const width = window.innerWidth;
    let breakpoint = "";
    let icon = "";
    
    if (width >= 1200) {
        breakpoint = "Extra Large (XL) - 1200px+";
        icon = "fas fa-desktop";
    } else if (width >= 992) {
        breakpoint = "Large (LG) - 992px+";
        icon = "fas fa-laptop";
    } else if (width >= 768) {
        breakpoint = "Medium (MD) - 768px+";
        icon = "fas fa-tablet-alt";
    } else if (width >= 576) {
        breakpoint = "Small (SM) - 576px+";
        icon = "fas fa-mobile-alt";
    } else {
        breakpoint = "Extra Small (XS) - <576px";
        icon = "fas fa-mobile";
    }
    
    document.getElementById("currentBreakpoint").innerHTML = `<i class="${icon} me-2"></i>${breakpoint}`;
    document.getElementById("screenWidth").textContent = width;
    document.getElementById("screenHeight").textContent = window.innerHeight;
}

// Initialize
document.addEventListener("DOMContentLoaded", function() {
    // Update breakpoint info
    updateBreakpoint();
    window.addEventListener("resize", updateBreakpoint);
    
    // Initialize test chart
    const ctx = document.getElementById("testChart");
    if (ctx) {
        new Chart(ctx, {
            type: "doughnut",
            data: {
                labels: ["সফল", "ব্যর্থ", "পেন্ডিং"],
                datasets: [{
                    data: [75, 15, 10],
                    backgroundColor: ["#28a745", "#dc3545", "#ffc107"],
                    borderWidth: 2,
                    borderColor: "#1a1a1a"
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: "bottom",
                        labels: { color: "#ffffff" }
                    }
                }
            }
        });
    }
});
</script>
';

// Include footer
include 'includes/footer.php';
?>
