/* Enhanced Ad Styling CSS */
:root {
    --ad-primary: #e50914;
    --ad-secondary: #1a1a1a;
    --ad-accent: #ffc107;
    --ad-text: #ffffff;
    --ad-text-muted: #b3b3b3;
    --ad-border: rgba(229, 9, 20, 0.3);
    --ad-gradient: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* Enhanced Ad Container */
.ad-container {
    background: var(--ad-gradient);
    border: 2px dashed var(--ad-border);
    border-radius: 12px;
    padding: 2rem;
    margin: 2rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.ad-container:hover {
    border-color: var(--ad-primary);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(229, 9, 20, 0.2);
}

.ad-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(229, 9, 20, 0.1), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Enhanced Ad Placeholder Content */
.ad-container .ad-placeholder {
    color: var(--ad-text-muted);
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    min-height: 120px;
    position: relative;
    z-index: 2;
    background: none;
    border-radius: 0;
    padding: 0;
    box-shadow: none;
}

.ad-container .ad-placeholder i {
    color: var(--ad-primary);
    font-size: 1.5rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

/* Enhanced Ad Type Specific Styles */
.ad-container.ad-banner {
    min-height: 90px;
    padding: 1.5rem;
}

.ad-container.ad-banner .ad-placeholder {
    min-height: 60px;
    font-size: 1rem;
}

.ad-container.ad-sidebar {
    min-height: 250px;
    padding: 2rem 1rem;
}

.ad-container.ad-sidebar .ad-placeholder {
    min-height: 200px;
    flex-direction: column;
    gap: 1rem;
}

.ad-container.ad-inline {
    min-height: 120px;
    margin: 1.5rem 0;
}

/* Adstera Ad Styling (Legacy) */

/* Ad Banner Container */
.ad-banner {
    padding: 20px 0;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    border-bottom: 1px solid #e9ecef;
    margin: 20px 0;
}

.ad-banner-top {
    margin-top: 20px;
}

.ad-banner-middle {
    margin: 40px 0;
}

.ad-banner-bottom {
    margin-bottom: 20px;
}

/* Ad Placeholder */
.ad-placeholder {
    background: #fff;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.ad-placeholder:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

/* Ad Label */
.ad-label {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
}

/* Ad Content */
.ad-content {
    min-height: 90px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

/* Sidebar Ad */
.sidebar-ad {
    margin: 20px 0;
}

.sidebar-ad .ad-placeholder {
    padding: 15px;
}

.sidebar-ad .ad-content {
    min-height: 250px;
}

/* In-Content Ad */
.in-content-ad {
    margin: 30px 0;
}

.in-content-ad .ad-placeholder {
    padding: 15px;
}

.in-content-ad .ad-content {
    min-height: 250px;
}

/* Test Ad Styling */
.test-ad {
    background: linear-gradient(45deg, #ff6b6b, #ff8e53) !important;
    color: white !important;
    padding: 30px 20px !important;
    text-align: center !important;
    border-radius: 15px !important;
    margin: 10px 0 !important;
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3) !important;
    transition: all 0.3s ease !important;
}

.test-ad:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4) !important;
}

.test-ad h4 {
    margin-bottom: 10px !important;
    font-weight: 600 !important;
}

.test-ad p {
    margin: 0 !important;
    opacity: 0.9 !important;
}

/* Ad Blocker Message */
.ad-blocker-message {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    background: white !important;
    padding: 30px !important;
    border-radius: 15px !important;
    box-shadow: 0 15px 40px rgba(0,0,0,0.3) !important;
    z-index: 9999 !important;
    max-width: 400px !important;
    text-align: center !important;
    animation: slideIn 0.3s ease-out !important;
}

.ad-blocker-message h3 {
    color: #e74c3c !important;
    margin-bottom: 15px !important;
    font-weight: 600 !important;
}

.ad-blocker-message p {
    color: #666 !important;
    margin-bottom: 20px !important;
    line-height: 1.5 !important;
}

.ad-blocker-message button {
    background: #e74c3c !important;
    color: white !important;
    border: none !important;
    padding: 12px 25px !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

.ad-blocker-message button:hover {
    background: #c0392b !important;
    transform: translateY(-2px) !important;
}

/* Ad Loading Animation */
.ad-content.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .ad-container {
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 8px;
    }

    .ad-container .ad-placeholder {
        font-size: 1rem;
        min-height: 80px;
        flex-direction: column;
        gap: 0.5rem;
    }

    .ad-container .ad-placeholder i {
        font-size: 1.2rem;
    }

    .ad-container.ad-banner {
        min-height: 60px;
        padding: 1rem;
    }

    .ad-container.ad-sidebar {
        min-height: 200px;
        padding: 1.5rem 1rem;
    }

    .ad-container.ad-inline {
        min-height: 80px;
        margin: 1rem 0;
    }

    /* Legacy styles */
    .ad-banner {
        padding: 15px 0;
    }

    .ad-placeholder {
        padding: 15px;
    }

    .ad-content {
        min-height: 60px;
    }

    .sidebar-ad .ad-content {
        min-height: 200px;
    }

    .in-content-ad .ad-content {
        min-height: 200px;
    }

    .ad-blocker-message {
        max-width: 90% !important;
        margin: 0 20px !important;
    }
}

@media (max-width: 576px) {
    .ad-container {
        padding: 0.75rem;
        margin: 0.75rem 0;
    }

    .ad-container .ad-placeholder {
        font-size: 0.9rem;
        min-height: 60px;
    }

    .ad-container.ad-banner {
        min-height: 50px;
        padding: 0.75rem;
    }

    .ad-container.ad-sidebar {
        min-height: 150px;
    }

    /* Legacy mobile styles */
    .ad-banner {
        padding: 10px 0;
    }

    .ad-placeholder {
        padding: 10px;
    }

    .ad-content {
        min-height: 50px;
    }

    .sidebar-ad .ad-content {
        min-height: 150px;
    }

    .in-content-ad .ad-content {
        min-height: 150px;
    }

    .test-ad {
        padding: 20px 15px !important;
    }

    .test-ad h4 {
        font-size: 16px !important;
    }

    .test-ad p {
        font-size: 14px !important;
    }
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
    .ad-banner {
        background: #2d2d2d;
        border-color: #404040;
    }
    
    .ad-placeholder {
        background: #1a1a1a;
        color: #fff;
    }
    
    .ad-label {
        color: #aaa;
    }
    
    .ad-content.loading {
        background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    }
}

/* Print Styles */
@media print {
    .ad-banner,
    .sidebar-ad,
    .in-content-ad {
        display: none !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .ad-placeholder {
        border: 2px solid #000;
    }
    
    .ad-label {
        color: #000;
        font-weight: bold;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .ad-placeholder,
    .test-ad,
    .ad-blocker-message button {
        transition: none !important;
    }
    
    .ad-content.loading {
        animation: none !important;
    }
    
    .ad-blocker-message {
        animation: none !important;
    }
} 