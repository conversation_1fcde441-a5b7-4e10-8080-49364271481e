/**
 * CinePix Admin Panel - Modern JavaScript
 * Interactive features and AJAX functionality
 */

class CinePixAdmin {
    constructor() {
        this.init();
        this.bindEvents();
        this.initializeComponents();
    }

    init() {
        // Initialize theme
        this.initTheme();

        // Initialize sidebar
        this.initSidebar();

        // Initialize mobile navigation
        this.initMobileNav();

        // Initialize search
        this.initSearch();

        // Initialize notifications
        this.initNotifications();
        
        // Initialize auto-refresh
        this.initAutoRefresh();
    }

    initTheme() {
        // Apply saved theme preference
        const savedTheme = localStorage.getItem('admin-theme') || 'dark';
        document.documentElement.setAttribute('data-theme', savedTheme);
    }

    initSidebar() {
        const sidebar = document.getElementById('sidebar');
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebarClose = document.getElementById('sidebarClose');
        const sidebarOverlay = document.getElementById('sidebarOverlay');

        // Toggle sidebar on mobile
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }

        if (sidebarClose) {
            sidebarClose.addEventListener('click', () => {
                this.closeSidebar();
            });
        }

        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', () => {
                this.closeSidebar();
            });
        }

        // Handle submenu toggles
        this.initSubmenuToggles();
    }

    initSubmenuToggles() {
        const submenuToggles = document.querySelectorAll('.dropdown-toggle');
        
        submenuToggles.forEach(toggle => {
            toggle.addEventListener('click', (e) => {
                e.preventDefault();
                const target = toggle.getAttribute('data-bs-target');
                const submenu = document.querySelector(target);
                
                if (submenu) {
                    const isExpanded = toggle.getAttribute('aria-expanded') === 'true';
                    toggle.setAttribute('aria-expanded', !isExpanded);
                    
                    if (isExpanded) {
                        submenu.classList.remove('show');
                    } else {
                        submenu.classList.add('show');
                    }
                }
            });
        });
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebarOverlay');
        
        sidebar.classList.add('show');
        overlay.classList.add('show');
        document.body.classList.add('sidebar-open');
    }

    closeSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebarOverlay');

        sidebar.classList.remove('show');
        overlay.classList.remove('show');
        document.body.classList.remove('sidebar-open');
    }

    // Initialize mobile navigation
    initMobileNav() {
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const mobileNavClose = document.getElementById('mobileNavClose');
        const mobileNav = document.getElementById('mobileNav');
        const mobileNavOverlay = document.getElementById('mobileNavOverlay');

        // Open mobile menu
        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', () => {
                this.openMobileNav();
            });
        }

        // Close mobile menu
        if (mobileNavClose) {
            mobileNavClose.addEventListener('click', () => {
                this.closeMobileNav();
            });
        }

        // Close on overlay click
        if (mobileNavOverlay) {
            mobileNavOverlay.addEventListener('click', () => {
                this.closeMobileNav();
            });
        }

        // Close on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && mobileNav && mobileNav.classList.contains('show')) {
                this.closeMobileNav();
            }
        });

        // Close on window resize to desktop
        window.addEventListener('resize', () => {
            if (window.innerWidth >= 992 && mobileNav && mobileNav.classList.contains('show')) {
                this.closeMobileNav();
            }
        });

        // Handle mobile nav item clicks
        const mobileNavItems = document.querySelectorAll('.mobile-nav-item');
        mobileNavItems.forEach(item => {
            item.addEventListener('click', () => {
                // Close mobile nav when navigating
                setTimeout(() => {
                    this.closeMobileNav();
                }, 100);
            });
        });
    }

    // Open mobile navigation
    openMobileNav() {
        const mobileNav = document.getElementById('mobileNav');
        const mobileNavOverlay = document.getElementById('mobileNavOverlay');

        if (mobileNav && mobileNavOverlay) {
            mobileNav.classList.add('show');
            mobileNavOverlay.classList.add('show');
            document.body.style.overflow = 'hidden';
        }
    }

    // Close mobile navigation
    closeMobileNav() {
        const mobileNav = document.getElementById('mobileNav');
        const mobileNavOverlay = document.getElementById('mobileNavOverlay');

        if (mobileNav && mobileNavOverlay) {
            mobileNav.classList.remove('show');
            mobileNavOverlay.classList.remove('show');
            document.body.style.overflow = '';
        }
    }

    initSearch() {
        const searchInput = document.getElementById('globalSearch');
        if (!searchInput) return;

        let searchTimeout;
        
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            const query = e.target.value.trim();
            
            if (query.length < 2) return;
            
            searchTimeout = setTimeout(() => {
                this.performSearch(query);
            }, 300);
        });
    }

    async performSearch(query) {
        try {
            const response = await fetch('ajax/global_search.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ query })
            });
            
            const results = await response.json();
            this.displaySearchResults(results);
        } catch (error) {
            console.error('Search error:', error);
        }
    }

    displaySearchResults(results) {
        // Create or update search results dropdown
        let resultsContainer = document.getElementById('searchResults');
        
        if (!resultsContainer) {
            resultsContainer = document.createElement('div');
            resultsContainer.id = 'searchResults';
            resultsContainer.className = 'search-results dropdown-menu show';
            
            const searchContainer = document.querySelector('.search-container');
            searchContainer.appendChild(resultsContainer);
        }
        
        if (results.length === 0) {
            resultsContainer.innerHTML = '<div class="dropdown-item">No results found</div>';
            return;
        }
        
        resultsContainer.innerHTML = results.map(result => `
            <a href="${result.url}" class="dropdown-item">
                <i class="${result.icon} me-2"></i>
                <div>
                    <div class="fw-bold">${result.title}</div>
                    <small class="text-muted">${result.type}</small>
                </div>
            </a>
        `).join('');
    }

    initNotifications() {
        // Check for new notifications periodically
        this.checkNotifications();
        setInterval(() => this.checkNotifications(), 30000); // Every 30 seconds
    }

    async checkNotifications() {
        try {
            const response = await fetch('ajax/get_notifications.php');
            const notifications = await response.json();
            
            this.updateNotificationBadge(notifications.unread_count);
            this.updateNotificationDropdown(notifications.recent);
        } catch (error) {
            console.error('Notification check error:', error);
        }
    }

    updateNotificationBadge(count) {
        const badge = document.querySelector('#notificationDropdown .badge');
        if (badge) {
            badge.textContent = count;
            badge.style.display = count > 0 ? 'block' : 'none';
        }
    }

    updateNotificationDropdown(notifications) {
        const dropdown = document.querySelector('.notification-dropdown');
        if (!dropdown) return;
        
        const notificationItems = notifications.map(notification => `
            <li>
                <a class="dropdown-item" href="${notification.url}">
                    <div class="d-flex">
                        <div class="flex-shrink-0">
                            <i class="${notification.icon} ${notification.color}"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold">${notification.title}</div>
                            <div class="text-muted small">${notification.time_ago}</div>
                        </div>
                    </div>
                </a>
            </li>
        `).join('');
        
        // Update dropdown content
        const existingItems = dropdown.querySelectorAll('li:not(.dropdown-header):not(:has(hr)):not(:last-child)');
        existingItems.forEach(item => item.remove());
        
        const headerItem = dropdown.querySelector('.dropdown-header').parentElement;
        headerItem.insertAdjacentHTML('afterend', '<li><hr class="dropdown-divider"></li>' + notificationItems);
    }

    initAutoRefresh() {
        // Auto-refresh dashboard data
        if (window.location.pathname.includes('index.php') || window.location.pathname.endsWith('/admin/')) {
            this.refreshDashboardData();
            setInterval(() => this.refreshDashboardData(), 60000); // Every minute
        }
    }

    async refreshDashboardData() {
        try {
            const response = await fetch('ajax/get_dashboard_data.php');
            const data = await response.json();
            
            this.updateDashboardStats(data.stats);
            this.updateRecentActivity(data.recent_activity);
        } catch (error) {
            console.error('Dashboard refresh error:', error);
        }
    }

    updateDashboardStats(stats) {
        Object.keys(stats).forEach(key => {
            const element = document.querySelector(`[data-stat="${key}"]`);
            if (element) {
                element.textContent = this.formatNumber(stats[key]);
            }
        });
    }

    updateRecentActivity(activities) {
        const container = document.getElementById('recentActivity');
        if (!container) return;
        
        container.innerHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-time">${activity.time_ago}</div>
                </div>
            </div>
        `).join('');
    }

    bindEvents() {
        // Bind common events
        this.bindFormEvents();
        this.bindTableEvents();
        this.bindModalEvents();
        this.bindUtilityEvents();
    }

    bindFormEvents() {
        // Auto-save form data
        const forms = document.querySelectorAll('form[data-auto-save]');
        forms.forEach(form => {
            const inputs = form.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('change', () => {
                    this.autoSaveForm(form);
                });
            });
        });

        // Form validation
        const validatedForms = document.querySelectorAll('.needs-validation');
        validatedForms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    }

    bindTableEvents() {
        // Initialize DataTables
        const tables = document.querySelectorAll('.data-table');
        tables.forEach(table => {
            if ($.fn.DataTable) {
                $(table).DataTable({
                    responsive: true,
                    pageLength: 25,
                    order: [[0, 'desc']],
                    language: {
                        search: "খুঁজুন:",
                        lengthMenu: "প্রতি পাতায় _MENU_ টি এন্ট্রি দেখান",
                        info: "_START_ থেকে _END_ পর্যন্ত _TOTAL_ এন্ট্রির মধ্যে",
                        paginate: {
                            first: "প্রথম",
                            last: "শেষ",
                            next: "পরবর্তী",
                            previous: "পূর্ববর্তী"
                        }
                    }
                });
            }
        });

        // Bulk actions
        const bulkActionForms = document.querySelectorAll('.bulk-action-form');
        bulkActionForms.forEach(form => {
            const selectAllCheckbox = form.querySelector('.select-all');
            const itemCheckboxes = form.querySelectorAll('.item-checkbox');
            
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', (e) => {
                    itemCheckboxes.forEach(checkbox => {
                        checkbox.checked = e.target.checked;
                    });
                    this.updateBulkActionButtons(form);
                });
            }
            
            itemCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', () => {
                    this.updateBulkActionButtons(form);
                });
            });
        });
    }

    bindModalEvents() {
        // Dynamic modal loading
        const modalTriggers = document.querySelectorAll('[data-modal-url]');
        modalTriggers.forEach(trigger => {
            trigger.addEventListener('click', async (e) => {
                e.preventDefault();
                const url = trigger.getAttribute('data-modal-url');
                await this.loadModal(url);
            });
        });
    }

    bindUtilityEvents() {
        // Copy to clipboard buttons
        const copyButtons = document.querySelectorAll('.copy-btn');
        copyButtons.forEach(button => {
            button.addEventListener('click', () => {
                const text = button.getAttribute('data-copy-text');
                this.copyToClipboard(text);
            });
        });

        // Confirm delete buttons
        const deleteButtons = document.querySelectorAll('.delete-btn');
        deleteButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const message = button.getAttribute('data-confirm-message') || 'Are you sure you want to delete this item?';
                this.showConfirmModal(message, () => {
                    window.location.href = button.href;
                });
            });
        });
    }

    initializeComponents() {
        // Initialize charts if Chart.js is available
        if (typeof Chart !== 'undefined') {
            this.initCharts();
        }

        // Initialize file uploads
        this.initFileUploads();

        // Initialize tooltips and popovers
        this.initTooltips();
    }

    initCharts() {
        // Revenue chart
        const revenueChart = document.getElementById('revenueChart');
        if (revenueChart) {
            new Chart(revenueChart, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Revenue',
                        data: [12, 19, 3, 5, 2, 3],
                        borderColor: '#e50914',
                        backgroundColor: 'rgba(229, 9, 20, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#ffffff'
                            }
                        }
                    },
                    scales: {
                        y: {
                            ticks: {
                                color: '#b3b3b3'
                            },
                            grid: {
                                color: '#333333'
                            }
                        },
                        x: {
                            ticks: {
                                color: '#b3b3b3'
                            },
                            grid: {
                                color: '#333333'
                            }
                        }
                    }
                }
            });
        }
    }

    initFileUploads() {
        const fileInputs = document.querySelectorAll('input[type="file"]');
        fileInputs.forEach(input => {
            input.addEventListener('change', (e) => {
                this.handleFileUpload(e.target);
            });
        });
    }

    initTooltips() {
        // Initialize Bootstrap tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // Utility methods
    formatNumber(num) {
        return new Intl.NumberFormat().format(num);
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showToast('Copied to clipboard!', 'success');
        } catch (err) {
            this.showToast('Failed to copy to clipboard', 'error');
        }
    }

    showToast(message, type = 'info') {
        const toast = document.getElementById('liveToast');
        const toastBody = toast.querySelector('.toast-body');
        const toastIcon = toast.querySelector('.toast-header i');
        
        toastBody.textContent = message;
        
        const iconClasses = {
            success: 'fa-check-circle text-success',
            error: 'fa-exclamation-circle text-danger',
            warning: 'fa-exclamation-triangle text-warning',
            info: 'fa-info-circle text-primary'
        };
        
        toastIcon.className = `fas me-2 ${iconClasses[type] || iconClasses.info}`;
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }

    showConfirmModal(message, callback) {
        const modal = document.getElementById('confirmModal');
        const messageEl = document.getElementById('confirmMessage');
        const confirmBtn = document.getElementById('confirmAction');
        
        messageEl.textContent = message;
        
        // Remove previous event listeners
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
        
        // Add new event listener
        newConfirmBtn.addEventListener('click', function() {
            callback();
            bootstrap.Modal.getInstance(modal).hide();
        });
        
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
    }

    async loadModal(url) {
        try {
            this.showLoadingModal();
            const response = await fetch(url);
            const html = await response.text();
            
            // Create modal container if it doesn't exist
            let modalContainer = document.getElementById('dynamicModal');
            if (!modalContainer) {
                modalContainer = document.createElement('div');
                modalContainer.id = 'dynamicModal';
                document.body.appendChild(modalContainer);
            }
            
            modalContainer.innerHTML = html;
            
            const modal = modalContainer.querySelector('.modal');
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
            
            this.hideLoadingModal();
        } catch (error) {
            this.hideLoadingModal();
            this.showToast('Failed to load modal', 'error');
        }
    }

    showLoadingModal() {
        const modal = new bootstrap.Modal(document.getElementById('loadingModal'));
        modal.show();
    }

    hideLoadingModal() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('loadingModal'));
        if (modal) {
            modal.hide();
        }
    }

    updateBulkActionButtons(form) {
        const checkedBoxes = form.querySelectorAll('.item-checkbox:checked');
        const bulkActionButtons = form.querySelectorAll('.bulk-action-btn');
        
        bulkActionButtons.forEach(button => {
            button.disabled = checkedBoxes.length === 0;
        });
    }

    autoSaveForm(form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        
        localStorage.setItem(`form_${form.id}`, JSON.stringify(data));
        this.showToast('Form auto-saved', 'info');
    }

    handleFileUpload(input) {
        const file = input.files[0];
        if (!file) return;
        
        // Show file info
        const fileInfo = document.createElement('div');
        fileInfo.className = 'file-info mt-2';
        fileInfo.innerHTML = `
            <small class="text-muted">
                Selected: ${file.name} (${this.formatFileSize(file.size)})
            </small>
        `;
        
        // Remove existing file info
        const existingInfo = input.parentNode.querySelector('.file-info');
        if (existingInfo) {
            existingInfo.remove();
        }
        
        input.parentNode.appendChild(fileInfo);
    }
}

// Initialize admin panel when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.cinepixAdmin = new CinePixAdmin();
});

// Export for use in other scripts
window.CinePixAdmin = CinePixAdmin;
