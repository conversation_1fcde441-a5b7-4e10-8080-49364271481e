-- Use existing database
USE tipsbdxy_4525;

-- Categories table (if not exists)
CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Update users table to add is_premium field if it doesn't exist
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_premium BOOLEAN DEFAULT FALSE;

-- Update movies table to add premium_only field if it doesn't exist
ALTER TABLE movies ADD COLUMN IF NOT EXISTS premium_only BOOLEAN DEFAULT FALSE;

-- Update tvshows table to add premium_only field if it doesn't exist
ALTER TABLE tvshows ADD COLUMN IF NOT EXISTS premium_only BOOLEAN DEFAULT FALSE;

-- Episodes table for TV shows
CREATE TABLE IF NOT EXISTS episodes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tvshow_id INT NOT NULL,
    season_number INT NOT NULL,
    episode_number INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    duration INT COMMENT 'Duration in minutes',
    thumbnail VARCHAR(255),
    release_date DATE,
    is_premium BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tvshow_id) REFERENCES tvshows(id) ON DELETE CASCADE,
    UNIQUE KEY unique_episode (tvshow_id, season_number, episode_number)
);

-- Download links table
CREATE TABLE IF NOT EXISTS download_links (
    id INT AUTO_INCREMENT PRIMARY KEY,
    content_type ENUM('movie', 'tvshow') NOT NULL,
    content_id INT NOT NULL,
    quality VARCHAR(20) NOT NULL COMMENT 'e.g. 720p, 1080p, 4K',
    link_type ENUM('direct', 'torrent', 'gdrive', 'mega') NOT NULL,
    link_url TEXT NOT NULL,
    is_premium BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_download_link (content_type, content_id, quality, link_type)
);

-- Streaming links table
CREATE TABLE IF NOT EXISTS streaming_links (
    id INT AUTO_INCREMENT PRIMARY KEY,
    content_type ENUM('movie', 'tvshow') NOT NULL,
    content_id INT NOT NULL,
    quality VARCHAR(20) NOT NULL COMMENT 'e.g. 720p, 1080p, 4K',
    server_name VARCHAR(50) NOT NULL COMMENT 'e.g. Server 1, Server 2',
    stream_url TEXT NOT NULL,
    is_premium BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_stream_link (content_type, content_id, quality, server_name)
);

-- Episode links table
CREATE TABLE IF NOT EXISTS episode_links (
    id INT AUTO_INCREMENT PRIMARY KEY,
    episode_id INT NOT NULL,
    link_type ENUM('download', 'stream') NOT NULL,
    quality VARCHAR(20) NOT NULL COMMENT 'e.g. 720p, 1080p, 4K',
    server_name VARCHAR(50) COMMENT 'For streaming links',
    link_url TEXT NOT NULL,
    is_premium BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (episode_id) REFERENCES episodes(id) ON DELETE CASCADE,
    UNIQUE KEY unique_episode_link (episode_id, link_type, quality, server_name)
);

-- Premium plans table
CREATE TABLE IF NOT EXISTS premium_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    duration INT NOT NULL COMMENT 'Duration in days',
    features TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Subscriptions table
CREATE TABLE IF NOT EXISTS subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    plan_id INT NOT NULL,
    start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP,
    status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES premium_plans(id) ON DELETE CASCADE
);

-- Payments table
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    subscription_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('bkash', 'nagad', 'rocket', 'manual') NOT NULL,
    transaction_id VARCHAR(100),
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE CASCADE
);

-- Insert sample premium plans if table is empty
INSERT INTO premium_plans (name, price, duration, features)
SELECT 'Basic', 199, 30, 'Access to all premium movies and TV shows\nWatch on one device at a time\nStandard video quality'
FROM dual
WHERE NOT EXISTS (SELECT 1 FROM premium_plans LIMIT 1);

INSERT INTO premium_plans (name, price, duration, features)
SELECT 'Standard', 399, 30, 'Access to all premium movies and TV shows\nWatch on two devices at a time\nHD video quality available'
FROM dual
WHERE (SELECT COUNT(*) FROM premium_plans) = 1;

INSERT INTO premium_plans (name, price, duration, features)
SELECT 'Premium', 599, 30, 'Access to all premium movies and TV shows\nWatch on four devices at a time\nHD and Ultra HD video quality available'
FROM dual
WHERE (SELECT COUNT(*) FROM premium_plans) = 2;

-- Update some movies to be premium (only if they exist)
UPDATE movies SET premium_only = TRUE WHERE id IN (1, 3, 5, 7, 9) AND EXISTS (SELECT 1 FROM movies WHERE id = 1);

-- Update some TV shows to be premium (only if they exist)
UPDATE tvshows SET premium_only = TRUE WHERE id IN (2, 4, 6, 8, 10) AND EXISTS (SELECT 1 FROM tvshows WHERE id = 2);
