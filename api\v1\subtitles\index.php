<?php
// API Subtitles Endpoints

// Get the request
global $request;

// Handle different endpoints based on the path parts
$action = $request['parts'][0] ?? 'default';

switch ($action) {
    case 'movie':
        if (isset($request['parts'][1]) && is_numeric($request['parts'][1])) {
            handle_movie_subtitles((int)$request['parts'][1], $request);
        } else {
            api_error('Invalid movie ID', 400);
        }
        break;

    case 'episode':
        if (isset($request['parts'][1]) && is_numeric($request['parts'][1])) {
            handle_episode_subtitles((int)$request['parts'][1], $request);
        } else {
            api_error('Invalid episode ID', 400);
        }
        break;

    case 'upload':
        if (is_admin()) {
            handle_upload_subtitle($request);
        } else {
            api_error('Unauthorized access', 403);
        }
        break;

    case 'delete':
        if (is_admin()) {
            handle_delete_subtitle($request);
        } else {
            api_error('Unauthorized access', 403);
        }
        break;

    default:
        api_error('Invalid subtitles endpoint', 404);
}

// Handle movie subtitles
function handle_movie_subtitles($movie_id, $request) {
    global $conn;

    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }

    // Check if movie exists
    $movie_query = "SELECT id FROM movies WHERE id = ?";
    $movie_stmt = mysqli_prepare($conn, $movie_query);
    mysqli_stmt_bind_param($movie_stmt, 'i', $movie_id);
    mysqli_stmt_execute($movie_stmt);
    $movie_result = mysqli_stmt_get_result($movie_stmt);

    if (mysqli_num_rows($movie_result) === 0) {
        api_error('Movie not found', 404);
    }

    // Get subtitles for the movie
    $query = "SELECT * FROM subtitles
              WHERE content_type = 'movie' AND content_id = ?";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $movie_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    $subtitles = [];
    while ($subtitle = mysqli_fetch_assoc($result)) {
        $subtitles[] = [
            'id' => (int)$subtitle['id'],
            'language' => $subtitle['language'],
            'url' => $subtitle['url'],
            'is_default' => (bool)$subtitle['is_default']
        ];
    }

    // Return subtitles
    api_response([
        'movie_id' => $movie_id,
        'subtitles' => $subtitles
    ]);
}

// Handle episode subtitles
function handle_episode_subtitles($episode_id, $request) {
    global $conn;

    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }

    // Check if episode exists
    $episode_query = "SELECT id FROM episodes WHERE id = ?";
    $episode_stmt = mysqli_prepare($conn, $episode_query);
    mysqli_stmt_bind_param($episode_stmt, 'i', $episode_id);
    mysqli_stmt_execute($episode_stmt);
    $episode_result = mysqli_stmt_get_result($episode_stmt);

    if (mysqli_num_rows($episode_result) === 0) {
        api_error('Episode not found', 404);
    }

    // Get subtitles for the episode
    $query = "SELECT * FROM subtitles
              WHERE content_type = 'episode' AND content_id = ?";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $episode_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    $subtitles = [];
    while ($subtitle = mysqli_fetch_assoc($result)) {
        $subtitles[] = [
            'id' => (int)$subtitle['id'],
            'language' => $subtitle['language'],
            'url' => $subtitle['url'],
            'is_default' => (bool)$subtitle['is_default']
        ];
    }

    // Return subtitles
    api_response([
        'episode_id' => $episode_id,
        'subtitles' => $subtitles
    ]);
}

// Handle upload subtitle (admin only)
function handle_upload_subtitle($request) {
    global $conn;

    // Check request method
    if ($request['method'] !== 'POST') {
        api_error('Method not allowed', 405);
    }

    // Check if it's a file upload or URL submission
    $is_file_upload = isset($_FILES['subtitle_file']) && !empty($_FILES['subtitle_file']['name']);

    // Validate required fields
    if (empty($request['body']['content_type']) ||
        empty($request['body']['content_id']) ||
        empty($request['body']['language'])) {
        api_error('Content type, content ID, and language are required', 400);
    }

    // If not a file upload, URL is required
    if (!$is_file_upload && empty($request['body']['url'])) {
        api_error('Either a subtitle file or URL is required', 400);
    }

    $content_type = $request['body']['content_type'];
    $content_id = (int)$request['body']['content_id'];
    $language = $request['body']['language'];
    $is_default = isset($request['body']['is_default']) ? (bool)$request['body']['is_default'] : false;

    // Validate content type
    if (!in_array($content_type, ['movie', 'episode'])) {
        api_error('Invalid content type. Must be movie or episode', 400);
    }

    // Check if content exists
    if ($content_type === 'movie') {
        $content_query = "SELECT id FROM movies WHERE id = ?";
    } else {
        $content_query = "SELECT id FROM episodes WHERE id = ?";
    }

    $content_stmt = mysqli_prepare($conn, $content_query);
    mysqli_stmt_bind_param($content_stmt, 'i', $content_id);
    mysqli_stmt_execute($content_stmt);
    $content_result = mysqli_stmt_get_result($content_stmt);

    if (mysqli_num_rows($content_result) === 0) {
        api_error("$content_type with ID $content_id not found", 404);
    }

    // Process file upload if present
    if ($is_file_upload) {
        $file = $_FILES['subtitle_file'];
        $file_name = $file['name'];
        $file_tmp = $file['tmp_name'];
        $file_size = $file['size'];
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

        // Check file extension
        $allowed_exts = ['srt', 'vtt', 'sub', 'sbv', 'ass'];
        if (!in_array($file_ext, $allowed_exts)) {
            api_error('Invalid file type. Allowed types: ' . implode(', ', $allowed_exts), 400);
        }

        // Check file size (2MB max)
        if ($file_size > 2097152) {
            api_error('File size must be less than 2MB', 400);
        }

        // Create subtitles directory if it doesn't exist
        $subtitles_dir = '../../uploads/subtitles';
        if (!file_exists($subtitles_dir)) {
            mkdir($subtitles_dir, 0755, true);
        }

        // Generate unique filename
        $new_filename = $content_type . '_' . $content_id . '_' . $language . '_' . time() . '.' . $file_ext;
        $upload_path = $subtitles_dir . '/' . $new_filename;

        // Upload file
        if (!move_uploaded_file($file_tmp, $upload_path)) {
            api_error('Failed to upload file', 500);
        }

        // Set URL for database
        $url = 'uploads/subtitles/' . $new_filename;
    } else {
        // Use provided URL
        $url = $request['body']['url'];
    }

    // If this is set as default, unset any existing default for this content
    if ($is_default) {
        $unset_default_query = "UPDATE subtitles SET is_default = 0
                               WHERE content_type = ? AND content_id = ?";

        $unset_default_stmt = mysqli_prepare($conn, $unset_default_query);
        mysqli_stmt_bind_param($unset_default_stmt, 'si', $content_type, $content_id);
        mysqli_stmt_execute($unset_default_stmt);
    }

    // Check if subtitle already exists for this content and language
    $check_exists_query = "SELECT id FROM subtitles WHERE content_type = ? AND content_id = ? AND language = ?";
    $check_stmt = mysqli_prepare($conn, $check_exists_query);
    mysqli_stmt_bind_param($check_stmt, 'sis', $content_type, $content_id, $language);
    mysqli_stmt_execute($check_stmt);
    $check_result = mysqli_stmt_get_result($check_stmt);

    if (mysqli_num_rows($check_result) > 0) {
        // Update existing subtitle
        $subtitle = mysqli_fetch_assoc($check_result);
        $subtitle_id = $subtitle['id'];

        $update_query = "UPDATE subtitles SET url = ?, is_default = ?, updated_at = NOW()
                         WHERE id = ?";

        $update_stmt = mysqli_prepare($conn, $update_query);
        $is_default_int = $is_default ? 1 : 0;
        mysqli_stmt_bind_param($update_stmt, 'sii', $url, $is_default_int, $subtitle_id);

        if (!mysqli_stmt_execute($update_stmt)) {
            api_error('Failed to update subtitle: ' . mysqli_error($conn), 500);
        }

        // Return success response
        api_response([
            'id' => $subtitle_id,
            'content_type' => $content_type,
            'content_id' => $content_id,
            'language' => $language,
            'url' => $url,
            'is_default' => $is_default
        ], 200, 'Subtitle updated successfully');
    } else {
        // Insert new subtitle
        $query = "INSERT INTO subtitles (content_type, content_id, language, url, is_default)
                  VALUES (?, ?, ?, ?, ?)";

        $stmt = mysqli_prepare($conn, $query);
        $is_default_int = $is_default ? 1 : 0;
        mysqli_stmt_bind_param($stmt, 'sissi', $content_type, $content_id, $language, $url, $is_default_int);

        if (!mysqli_stmt_execute($stmt)) {
            api_error('Failed to upload subtitle: ' . mysqli_error($conn), 500);
        }

        $subtitle_id = mysqli_insert_id($conn);

        // Return success response
        api_response([
            'id' => $subtitle_id,
            'content_type' => $content_type,
            'content_id' => $content_id,
            'language' => $language,
            'url' => $url,
            'is_default' => $is_default
        ], 201, 'Subtitle uploaded successfully');
    }
}

// Handle delete subtitle (admin only)
function handle_delete_subtitle($request) {
    global $conn;

    // Check request method
    if ($request['method'] !== 'POST') {
        api_error('Method not allowed', 405);
    }

    // Validate required fields
    if (empty($request['body']['subtitle_id'])) {
        api_error('Subtitle ID is required', 400);
    }

    $subtitle_id = (int)$request['body']['subtitle_id'];

    // Get subtitle info to delete file
    $get_subtitle_query = "SELECT url FROM subtitles WHERE id = ?";
    $get_stmt = mysqli_prepare($conn, $get_subtitle_query);
    mysqli_stmt_bind_param($get_stmt, 'i', $subtitle_id);
    mysqli_stmt_execute($get_stmt);
    $result = mysqli_stmt_get_result($get_stmt);

    if (mysqli_num_rows($result) === 0) {
        api_error('Subtitle not found', 404);
    }

    $subtitle = mysqli_fetch_assoc($result);
    $url = $subtitle['url'];

    // Delete subtitle from database
    $query = "DELETE FROM subtitles WHERE id = ?";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $subtitle_id);

    if (!mysqli_stmt_execute($stmt)) {
        api_error('Failed to delete subtitle: ' . mysqli_error($conn), 500);
    }

    // Delete file if it's a local file
    if (strpos($url, 'uploads/subtitles/') !== false) {
        $file_path = '../../' . $url;
        if (file_exists($file_path)) {
            unlink($file_path);
        }
    }

    // Return success response
    api_response([
        'subtitle_id' => $subtitle_id
    ], 200, 'Subtitle deleted successfully');
}
