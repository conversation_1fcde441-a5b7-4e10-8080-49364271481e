<?php
// Set error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include configuration files
// Try different possible locations for config and database files
$possible_config_files = [
    'includes/config.php',
    'config/config.php',
    'config.php'
];

$possible_db_files = [
    'includes/db.php',
    'config/db.php',
    'includes/database.php',
    'config/database.php',
    'db.php',
    'database.php'
];

$config_loaded = false;
foreach ($possible_config_files as $config_file) {
    if (file_exists($config_file)) {
        require_once $config_file;
        echo "<p class='info'>Loaded config file: $config_file</p>";
        $config_loaded = true;
        break;
    }
}

if (!$config_loaded) {
    echo "<p class='warning'>Could not find config file. Continuing anyway...</p>";
}

// Try to find and include database connection file
$db_loaded = false;
foreach ($possible_db_files as $db_file) {
    if (file_exists($db_file)) {
        require_once $db_file;
        echo "<p class='info'>Loaded database file: $db_file</p>";
        $db_loaded = true;
        break;
    }
}

// If no database file found, try to create a connection manually
if (!$db_loaded) {
    echo "<p class='warning'>Could not find database connection file. Please enter your database details:</p>";

    // Check if form is submitted
    if (isset($_POST['db_submit'])) {
        $db_host = $_POST['db_host'];
        $db_user = $_POST['db_user'];
        $db_pass = $_POST['db_pass'];
        $db_name = $_POST['db_name'];

        // Try to connect to database
        $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

        if ($conn->connect_error) {
            die("<p class='error'>Connection failed: " . $conn->connect_error . "</p>");
        } else {
            echo "<p class='success'>Database connection successful!</p>";
            $db_loaded = true;
        }
    } else {
        // Show database connection form
        echo "<form method='post' action=''>";
        echo "<div style='margin-bottom: 10px;'><label>Database Host: </label><input type='text' name='db_host' value='localhost'></div>";
        echo "<div style='margin-bottom: 10px;'><label>Database Username: </label><input type='text' name='db_user'></div>";
        echo "<div style='margin-bottom: 10px;'><label>Database Password: </label><input type='password' name='db_pass'></div>";
        echo "<div style='margin-bottom: 10px;'><label>Database Name: </label><input type='text' name='db_name'></div>";
        echo "<div><input type='submit' name='db_submit' value='Connect'></div>";
        echo "</form>";
        exit;
    }
}

// Check if database connection is established
if (!isset($conn) || !$conn) {
    // If we got here, we need to create a database connection form
    echo "<p class='warning'>Database connection not found. Please enter your database details:</p>";

    // Check if form is submitted
    if (isset($_POST['db_submit'])) {
        $db_host = $_POST['db_host'];
        $db_user = $_POST['db_user'];
        $db_pass = $_POST['db_pass'];
        $db_name = $_POST['db_name'];

        // Try to connect to database
        $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

        if ($conn->connect_error) {
            die("<p class='error'>Connection failed: " . $conn->connect_error . "</p>");
        } else {
            echo "<p class='success'>Database connection successful!</p>";
        }
    } else {
        // Show database connection form
        echo "<form method='post' action=''>";
        echo "<div style='margin-bottom: 10px;'><label>Database Host: </label><input type='text' name='db_host' value='localhost'></div>";
        echo "<div style='margin-bottom: 10px;'><label>Database Username: </label><input type='text' name='db_user'></div>";
        echo "<div style='margin-bottom: 10px;'><label>Database Password: </label><input type='password' name='db_pass'></div>";
        echo "<div style='margin-bottom: 10px;'><label>Database Name: </label><input type='text' name='db_name'></div>";
        echo "<div><input type='submit' name='db_submit' value='Connect'></div>";
        echo "</form>";
        exit;
    }
}

// Function to check if column exists
function columnExists($table, $column, $conn) {
    $sql = "SHOW COLUMNS FROM `$table` LIKE '$column'";
    $result = $conn->query($sql);
    return ($result && $result->num_rows > 0);
}

// Function to check if table exists
function tableExists($table, $conn) {
    $sql = "SHOW TABLES LIKE '$table'";
    $result = $conn->query($sql);
    return ($result && $result->num_rows > 0);
}

// HTML header for better display
echo "<!DOCTYPE html>\n";
echo "<html>\n";
echo "<head>\n";
echo "<title>Database Update for Subtitles</title>\n";
echo "<style>\n";
echo "body { font-family: Arial, sans-serif; margin: 20px; }\n";
echo ".success { color: green; }\n";
echo ".error { color: red; }\n";
echo ".warning { color: orange; }\n";
echo ".info { background-color: #f0f0f0; padding: 10px; margin: 10px 0; }\n";
echo "</style>\n";
echo "</head>\n";
echo "<body>\n";
echo "<h1>Database Update for Subtitles</h1>\n";

// Define tables and columns to add
$updates = [
    // Movies table updates
    [
        'table' => 'movies',
        'column' => 'imdb_id',
        'definition' => 'VARCHAR(20)',
        'after' => 'tmdb_id'
    ],

    // Series table updates
    [
        'table' => 'series',
        'column' => 'imdb_id',
        'definition' => 'VARCHAR(20)',
        'after' => 'tmdb_id'
    ],

    // Links table updates
    [
        'table' => 'links',
        'column' => 'subtitle_url_bn',
        'definition' => 'TEXT',
        'after' => 'link_url'
    ],
    [
        'table' => 'links',
        'column' => 'subtitle_url_en',
        'definition' => 'TEXT',
        'after' => 'subtitle_url_bn'
    ],

    // Episode links table updates
    [
        'table' => 'episode_links',
        'column' => 'subtitle_url_bn',
        'definition' => 'TEXT',
        'after' => 'link_url'
    ],
    [
        'table' => 'episode_links',
        'column' => 'subtitle_url_en',
        'definition' => 'TEXT',
        'after' => 'subtitle_url_bn'
    ]
];

// Process each update
foreach ($updates as $update) {
    $table = $update['table'];
    $column = $update['column'];
    $definition = $update['definition'];
    $after = isset($update['after']) ? $update['after'] : null;

    // Check if table exists
    if (!tableExists($table, $conn)) {
        echo "<p class='warning'>Table '$table' does not exist. Skipping column addition.</p>";
        continue;
    }

    // Check if column already exists
    if (columnExists($table, $column, $conn)) {
        echo "<p class='info'>Column '$column' already exists in table '$table'. Skipping.</p>";
        continue;
    }

    // Create the ALTER TABLE query
    $query = "ALTER TABLE `$table` ADD COLUMN `$column` $definition";
    if ($after) {
        // Check if the 'after' column exists
        if (columnExists($table, $after, $conn)) {
            $query .= " AFTER `$after`";
        }
    }

    // Execute the query
    try {
        $result = $conn->query($query);
        if ($result) {
            echo "<p class='success'>Successfully added column '$column' to table '$table'.</p>";
        } else {
            echo "<p class='error'>Error adding column '$column' to table '$table': " . $conn->error . "</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>Exception while adding column '$column' to table '$table': " . $e->getMessage() . "</p>";
    }
}

// Check for alternative table names
$alternativeTables = [
    ['links', 'download_links'],
    ['links', 'streaming_links'],
    ['episode_links', 'episode_download_links'],
    ['episode_links', 'episode_streaming_links']
];

foreach ($alternativeTables as $tablePair) {
    $mainTable = $tablePair[0];
    $altTable = $tablePair[1];

    // If main table doesn't exist but alternative does
    if (!tableExists($mainTable, $conn) && tableExists($altTable, $conn)) {
        echo "<p class='info'>Found alternative table '$altTable' instead of '$mainTable'.</p>";

        // Add subtitle columns to alternative table
        if (!columnExists($altTable, 'subtitle_url_bn', $conn)) {
            $query = "ALTER TABLE `$altTable` ADD COLUMN `subtitle_url_bn` TEXT";
            try {
                $result = $conn->query($query);
                if ($result) {
                    echo "<p class='success'>Successfully added column 'subtitle_url_bn' to table '$altTable'.</p>";
                } else {
                    echo "<p class='error'>Error adding column 'subtitle_url_bn' to table '$altTable': " . $conn->error . "</p>";
                }
            } catch (Exception $e) {
                echo "<p class='error'>Exception: " . $e->getMessage() . "</p>";
            }
        }

        if (!columnExists($altTable, 'subtitle_url_en', $conn)) {
            $query = "ALTER TABLE `$altTable` ADD COLUMN `subtitle_url_en` TEXT";
            try {
                $result = $conn->query($query);
                if ($result) {
                    echo "<p class='success'>Successfully added column 'subtitle_url_en' to table '$altTable'.</p>";
                } else {
                    echo "<p class='error'>Error adding column 'subtitle_url_en' to table '$altTable': " . $conn->error . "</p>";
                }
            } catch (Exception $e) {
                echo "<p class='error'>Exception: " . $e->getMessage() . "</p>";
            }
        }
    }
}

echo "<h2>Database Update Completed</h2>";
echo "<p>The database has been updated to support subtitles. You can now use the subtitle features.</p>";
echo "<p><a href='index.php'>Return to Home Page</a></p>";

echo "</body>\n";
echo "</html>\n";
?>
