-- Use existing database
USE tipsbdxy_4525;

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHA<PERSON>(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    profile_image VARCHAR(255) DEFAULT 'default.jpg',
    role ENUM('user', 'admin') DEFAULT 'user',
    is_premium BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Movies table
CREATE TABLE IF NOT EXISTS movies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    release_year INT,
    duration INT COMMENT 'Duration in minutes',
    poster VARCHAR(255),
    banner VARCHAR(255),
    trailer_url VARCHAR(255),
    rating DECIMAL(3,1) DEFAULT 0,
    category_id INT,
    featured BOOLEAN DEFAULT FALSE,
    premium_only BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- TV Shows table
CREATE TABLE IF NOT EXISTS tvshows (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    start_year INT,
    end_year INT,
    seasons INT DEFAULT 1,
    poster VARCHAR(255),
    banner VARCHAR(255),
    trailer_url VARCHAR(255),
    rating DECIMAL(3,1) DEFAULT 0,
    category_id INT,
    featured BOOLEAN DEFAULT FALSE,
    premium_only BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- Watchlist table
CREATE TABLE IF NOT EXISTS watchlist (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    content_type ENUM('movie', 'tvshow') NOT NULL,
    content_id INT NOT NULL,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_watchlist_item (user_id, content_type, content_id)
);

-- Reviews table
CREATE TABLE IF NOT EXISTS reviews (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    content_type ENUM('movie', 'tvshow') NOT NULL,
    content_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating BETWEEN 1 AND 10),
    comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_review (user_id, content_type, content_id)
);

-- Episodes table for TV shows
CREATE TABLE IF NOT EXISTS episodes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tvshow_id INT NOT NULL,
    season_number INT NOT NULL,
    episode_number INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    duration INT COMMENT 'Duration in minutes',
    thumbnail VARCHAR(255),
    release_date DATE,
    is_premium BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tvshow_id) REFERENCES tvshows(id) ON DELETE CASCADE,
    UNIQUE KEY unique_episode (tvshow_id, season_number, episode_number)
);

-- Download links table
CREATE TABLE IF NOT EXISTS download_links (
    id INT AUTO_INCREMENT PRIMARY KEY,
    content_type ENUM('movie', 'tvshow') NOT NULL,
    content_id INT NOT NULL,
    quality VARCHAR(20) NOT NULL COMMENT 'e.g. 720p, 1080p, 4K',
    link_type ENUM('direct', 'torrent', 'gdrive', 'mega') NOT NULL,
    link_url TEXT NOT NULL,
    is_premium BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_download_link (content_type, content_id, quality, link_type)
);

-- Streaming links table
CREATE TABLE IF NOT EXISTS streaming_links (
    id INT AUTO_INCREMENT PRIMARY KEY,
    content_type ENUM('movie', 'tvshow') NOT NULL,
    content_id INT NOT NULL,
    quality VARCHAR(20) NOT NULL COMMENT 'e.g. 720p, 1080p, 4K',
    server_name VARCHAR(50) NOT NULL COMMENT 'e.g. Server 1, Server 2',
    stream_url TEXT NOT NULL,
    is_premium BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_stream_link (content_type, content_id, quality, server_name)
);

-- Episode links table
CREATE TABLE IF NOT EXISTS episode_links (
    id INT AUTO_INCREMENT PRIMARY KEY,
    episode_id INT NOT NULL,
    link_type ENUM('download', 'stream') NOT NULL,
    quality VARCHAR(20) NOT NULL COMMENT 'e.g. 720p, 1080p, 4K',
    server_name VARCHAR(50) COMMENT 'For streaming links',
    link_url TEXT NOT NULL,
    is_premium BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (episode_id) REFERENCES episodes(id) ON DELETE CASCADE,
    UNIQUE KEY unique_episode_link (episode_id, link_type, quality, server_name)
);

-- Premium plans table
CREATE TABLE IF NOT EXISTS premium_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    duration INT NOT NULL COMMENT 'Duration in days',
    features TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Subscriptions table
CREATE TABLE IF NOT EXISTS subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    plan_id INT NOT NULL,
    start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_date DATETIME NULL,
    status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES premium_plans(id) ON DELETE CASCADE
);

-- Payments table
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    subscription_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('bkash', 'nagad', 'rocket', 'manual') NOT NULL,
    transaction_id VARCHAR(100),
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    payment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE CASCADE
);

-- Insert default admin user
INSERT INTO users (username, email, password, role) VALUES
('admin', '<EMAIL>', '$2y$10$8x.Yx5QRzlFAV2bVg0mKxOiPLt.2qKhbIQC5U.54XPYmgP5XfAR.e', 'admin');
-- Password: admin123

-- Insert sample categories
INSERT INTO categories (name) VALUES
('Action'), ('Comedy'), ('Drama'), ('Horror'), ('Sci-Fi'), ('Thriller'), ('Romance'), ('Animation'), ('Documentary'), ('Adventure');

-- Insert sample premium plans
INSERT INTO premium_plans (name, price, duration, features) VALUES
('Basic', 199, 30, 'Access to all premium movies and TV shows\nWatch on one device at a time\nStandard video quality'),
('Standard', 399, 30, 'Access to all premium movies and TV shows\nWatch on two devices at a time\nHD video quality available'),
('Premium', 599, 30, 'Access to all premium movies and TV shows\nWatch on four devices at a time\nHD and Ultra HD video quality available');

-- Insert sample movies
INSERT INTO movies (title, description, release_year, duration, poster, banner, trailer_url, rating, category_id, featured, premium_only) VALUES
('The Matrix', 'A computer hacker learns from mysterious rebels about the true nature of his reality and his role in the war against its controllers.', 1999, 136, 'matrix.jpg', 'matrix_banner.jpg', 'https://www.youtube.com/embed/m8e-FF8MsqU', 8.7, 5, 1, 0),
('Inception', 'A thief who steals corporate secrets through the use of dream-sharing technology is given the inverse task of planting an idea into the mind of a C.E.O.', 2010, 148, 'inception.jpg', 'inception_banner.jpg', 'https://www.youtube.com/embed/YoHD9XEInc0', 8.8, 5, 1, 0),
('The Shawshank Redemption', 'Two imprisoned men bond over a number of years, finding solace and eventual redemption through acts of common decency.', 1994, 142, 'shawshank.jpg', 'shawshank_banner.jpg', 'https://www.youtube.com/embed/6hB3S9bIaco', 9.3, 3, 0, 1),
('The Dark Knight', 'When the menace known as the Joker wreaks havoc and chaos on the people of Gotham, Batman must accept one of the greatest psychological and physical tests of his ability to fight injustice.', 2008, 152, 'dark_knight.jpg', 'dark_knight_banner.jpg', 'https://www.youtube.com/embed/EXeTwQWrcwY', 9.0, 1, 1, 0),
('Pulp Fiction', 'The lives of two mob hitmen, a boxer, a gangster and his wife, and a pair of diner bandits intertwine in four tales of violence and redemption.', 1994, 154, 'pulp_fiction.jpg', 'pulp_fiction_banner.jpg', 'https://www.youtube.com/embed/s7EdQ4FqbhY', 8.9, 6, 0, 1);

-- Insert sample TV shows
INSERT INTO tvshows (title, description, start_year, end_year, seasons, poster, banner, trailer_url, rating, category_id, featured, premium_only) VALUES
('Breaking Bad', 'A high school chemistry teacher diagnosed with inoperable lung cancer turns to manufacturing and selling methamphetamine in order to secure his family\'s future.', 2008, 2013, 5, 'breaking_bad.jpg', 'breaking_bad_banner.jpg', 'https://www.youtube.com/embed/HhesaQXLuRY', 9.5, 3, 1, 0),
('Game of Thrones', 'Nine noble families fight for control over the lands of Westeros, while an ancient enemy returns after being dormant for millennia.', 2011, 2019, 8, 'got.jpg', 'got_banner.jpg', 'https://www.youtube.com/embed/KPLWWIOCOOQ', 9.3, 3, 1, 0),
('Stranger Things', 'When a young boy disappears, his mother, a police chief, and his friends must confront terrifying supernatural forces in order to get him back.', 2016, NULL, 4, 'stranger_things.jpg', 'stranger_things_banner.jpg', 'https://www.youtube.com/embed/b9EkMc79ZSU', 8.7, 5, 0, 1),
('The Office', 'A mockumentary on a group of typical office workers, where the workday consists of ego clashes, inappropriate behavior, and tedium.', 2005, 2013, 9, 'the_office.jpg', 'the_office_banner.jpg', 'https://www.youtube.com/embed/LHOtME2DL4g', 8.9, 2, 0, 0),
('The Mandalorian', 'The travels of a lone bounty hunter in the outer reaches of the galaxy, far from the authority of the New Republic.', 2019, NULL, 3, 'mandalorian.jpg', 'mandalorian_banner.jpg', 'https://www.youtube.com/embed/aOC8E8z_ifw', 8.8, 5, 1, 1);

-- Insert sample episodes for TV shows
INSERT INTO episodes (tvshow_id, season_number, episode_number, title, description, duration, thumbnail, is_premium) VALUES
(1, 1, 1, 'Pilot', 'Walter White, a chemistry teacher, discovers that he has cancer and decides to get into the meth-making business to repay his medical debts.', 58, 'breaking_bad_s01e01.jpg', 0),
(1, 1, 2, 'Cat\'s in the Bag...', 'Walt and Jesse attempt to dispose of the bodies of two rivals, but fail to do so.', 48, 'breaking_bad_s01e02.jpg', 0),
(1, 1, 3, '...And the Bag\'s in the River', 'Walter fights with Jesse over his drug use, causing him to leave Walter alone with a very sick Krazy-8.', 48, 'breaking_bad_s01e03.jpg', 1),
(2, 1, 1, 'Winter Is Coming', 'Eddard Stark is torn between his family and an old friend when asked to serve at the side of King Robert Baratheon.', 62, 'got_s01e01.jpg', 0),
(2, 1, 2, 'The Kingsroad', 'While Bran recovers from his fall, Ned takes only his daughters to King\'s Landing.', 56, 'got_s01e02.jpg', 0),
(2, 1, 3, 'Lord Snow', 'Jon begins his training with the Night\'s Watch; Ned confronts his past and future at King\'s Landing.', 58, 'got_s01e03.jpg', 1);

-- Insert sample download links for movies
INSERT INTO download_links (content_type, content_id, quality, link_type, link_url, is_premium) VALUES
('movie', 1, '720p', 'direct', 'https://example.com/movies/matrix_720p.mp4', 0),
('movie', 1, '1080p', 'direct', 'https://example.com/movies/matrix_1080p.mp4', 1),
('movie', 1, '4K', 'direct', 'https://example.com/movies/matrix_4k.mp4', 1),
('movie', 2, '720p', 'direct', 'https://example.com/movies/inception_720p.mp4', 0),
('movie', 2, '1080p', 'direct', 'https://example.com/movies/inception_1080p.mp4', 1),
('movie', 3, '720p', 'direct', 'https://example.com/movies/shawshank_720p.mp4', 0),
('movie', 3, '1080p', 'direct', 'https://example.com/movies/shawshank_1080p.mp4', 1),
('movie', 4, '720p', 'direct', 'https://example.com/movies/dark_knight_720p.mp4', 0),
('movie', 4, '1080p', 'direct', 'https://example.com/movies/dark_knight_1080p.mp4', 1),
('movie', 5, '720p', 'direct', 'https://example.com/movies/pulp_fiction_720p.mp4', 0),
('movie', 5, '1080p', 'direct', 'https://example.com/movies/pulp_fiction_1080p.mp4', 1);

-- Insert sample streaming links for movies
INSERT INTO streaming_links (content_type, content_id, quality, server_name, stream_url, is_premium) VALUES
('movie', 1, '720p', 'Server 1', 'https://example.com/stream/matrix_720p_s1', 0),
('movie', 1, '1080p', 'Server 1', 'https://example.com/stream/matrix_1080p_s1', 1),
('movie', 1, '720p', 'Server 2', 'https://example.com/stream/matrix_720p_s2', 0),
('movie', 2, '720p', 'Server 1', 'https://example.com/stream/inception_720p_s1', 0),
('movie', 2, '1080p', 'Server 1', 'https://example.com/stream/inception_1080p_s1', 1),
('movie', 3, '720p', 'Server 1', 'https://example.com/stream/shawshank_720p_s1', 0),
('movie', 3, '1080p', 'Server 1', 'https://example.com/stream/shawshank_1080p_s1', 1),
('movie', 4, '720p', 'Server 1', 'https://example.com/stream/dark_knight_720p_s1', 0),
('movie', 4, '1080p', 'Server 1', 'https://example.com/stream/dark_knight_1080p_s1', 1),
('movie', 5, '720p', 'Server 1', 'https://example.com/stream/pulp_fiction_720p_s1', 0),
('movie', 5, '1080p', 'Server 1', 'https://example.com/stream/pulp_fiction_1080p_s1', 1);

-- Insert sample episode links
INSERT INTO episode_links (episode_id, link_type, quality, server_name, link_url, is_premium) VALUES
(1, 'download', '720p', NULL, 'https://example.com/episodes/breaking_bad_s01e01_720p.mp4', 0),
(1, 'download', '1080p', NULL, 'https://example.com/episodes/breaking_bad_s01e01_1080p.mp4', 1),
(1, 'stream', '720p', 'Server 1', 'https://example.com/stream/breaking_bad_s01e01_720p_s1', 0),
(1, 'stream', '1080p', 'Server 1', 'https://example.com/stream/breaking_bad_s01e01_1080p_s1', 1),
(2, 'download', '720p', NULL, 'https://example.com/episodes/breaking_bad_s01e02_720p.mp4', 0),
(2, 'download', '1080p', NULL, 'https://example.com/episodes/breaking_bad_s01e02_1080p.mp4', 1),
(2, 'stream', '720p', 'Server 1', 'https://example.com/stream/breaking_bad_s01e02_720p_s1', 0),
(3, 'download', '720p', NULL, 'https://example.com/episodes/breaking_bad_s01e03_720p.mp4', 1),
(3, 'stream', '720p', 'Server 1', 'https://example.com/stream/breaking_bad_s01e03_720p_s1', 1),
(4, 'download', '720p', NULL, 'https://example.com/episodes/got_s01e01_720p.mp4', 0),
(4, 'download', '1080p', NULL, 'https://example.com/episodes/got_s01e01_1080p.mp4', 1),
(4, 'stream', '720p', 'Server 1', 'https://example.com/stream/got_s01e01_720p_s1', 0),
(5, 'download', '720p', NULL, 'https://example.com/episodes/got_s01e02_720p.mp4', 0),
(5, 'stream', '720p', 'Server 1', 'https://example.com/stream/got_s01e02_720p_s1', 0),
(6, 'download', '720p', NULL, 'https://example.com/episodes/got_s01e03_720p.mp4', 1),
(6, 'stream', '720p', 'Server 1', 'https://example.com/stream/got_s01e03_720p_s1', 1);
