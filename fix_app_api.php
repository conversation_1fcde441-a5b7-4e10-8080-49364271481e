<?php
// Direct API fix for mobile app

// Get the base URL
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
$host = $_SERVER['HTTP_HOST'];
$base_url = $protocol . $host;

echo "<h1>CinePix App API Fix</h1>";

// Define the API files to update
$api_files = [
    'api/v1/direct_movies.php',
    'api/v1/direct_tvshows.php',
    'api/v1/direct_movie_details.php',
    'api/v1/direct_tvshow_details.php',
    'api/v1/direct_tvshow_episodes.php',
    'api/v1/direct_search.php'
];

echo "<h2>API Files Update</h2>";
echo "<table border='1' cellpadding='10'>";
echo "<tr><th>File</th><th>Status</th><th>Details</th></tr>";

foreach ($api_files as $file) {
    echo "<tr>";
    echo "<td>$file</td>";

    if (file_exists($file)) {
        $content = file_get_contents($file);

        // Remove status filters
        $patterns = [
            '/AND\s+[mt]\.status\s*=\s*\'active\'/',
            '/AND\s+status\s*=\s*\'active\'/'
        ];

        $new_content = $content;
        $changes = 0;

        foreach ($patterns as $pattern) {
            $count = 0;
            $new_content = preg_replace($pattern, '', $new_content, -1, $count);
            $changes += $count;
        }

        if ($changes > 0) {
            // Backup the original file
            $backup_file = $file . '.bak.' . date('YmdHis');
            file_put_contents($backup_file, $content);

            // Write the updated content
            file_put_contents($file, $new_content);

            echo "<td style='background-color: #dff0d8;'>✓ Updated</td>";
            echo "<td>Removed $changes status filters. Backup created: " . basename($backup_file) . "</td>";
        } else {
            echo "<td style='background-color: #fcf8e3;'>⚠ No Changes</td>";
            echo "<td>No status filters found in this file.</td>";
        }
    } else {
        echo "<td style='background-color: #f2dede;'>✗ Error</td>";
        echo "<td>File not found!</td>";
    }

    echo "</tr>";
}

echo "</table>";

// Test API endpoints after update
echo "<h2>API Endpoint Tests After Update</h2>";
echo "<table border='1' cellpadding='10'>";
echo "<tr><th>Endpoint</th><th>Status</th><th>Response</th></tr>";

$endpoints = [
    'api/v1/direct_movies.php',
    'api/v1/direct_tvshows.php',
    'api/v1/direct_movie_details.php?id=318', // Using a valid movie ID from your database
    'api/v1/direct_tvshow_details.php?id=84', // Using a valid TV show ID from your database
    'api/v1/direct_tvshow_episodes.php?id=84&season=1', // Using a valid TV show ID from your database
    'api/v1/direct_search.php?q=test'
];

foreach ($endpoints as $endpoint) {
    echo "<tr>";
    echo "<td>$endpoint</td>";

    // Get the contents of the endpoint
    $url = $base_url . '/' . $endpoint;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($http_code == 200) {
        $json = json_decode($response, true);
        if ($json && isset($json['success']) && $json['success'] === true) {
            echo "<td style='background-color: #dff0d8;'>✓ Success</td>";

            // Check specific data
            if (strpos($endpoint, 'direct_movies.php') !== false) {
                $count = count($json['data']['movies'] ?? []);
                echo "<td>Found $count movies</td>";
            } elseif (strpos($endpoint, 'direct_tvshows.php') !== false) {
                $count = count($json['data']['tvshows'] ?? []);
                echo "<td>Found $count TV shows</td>";
            } elseif (strpos($endpoint, 'direct_movie_details.php') !== false) {
                $links_count = count($json['data']['download_links'] ?? []);
                echo "<td>Movie has $links_count download links</td>";
            } elseif (strpos($endpoint, 'direct_tvshow_details.php') !== false) {
                $seasons_count = count($json['data']['seasons'] ?? []);
                echo "<td>TV show has $seasons_count seasons</td>";
            } elseif (strpos($endpoint, 'direct_tvshow_episodes.php') !== false) {
                $episodes_count = count($json['data']['episodes'] ?? []);
                echo "<td>Season has $episodes_count episodes</td>";
            } elseif (strpos($endpoint, 'direct_search.php') !== false) {
                $movies_count = count($json['data']['movies'] ?? []);
                $tvshows_count = count($json['data']['tvshows'] ?? []);
                echo "<td>Found $movies_count movies and $tvshows_count TV shows</td>";
            } else {
                echo "<td>Success</td>";
            }
        } else {
            echo "<td style='background-color: #f2dede;'>✗ Failed</td>";
            echo "<td>Invalid response format</td>";
        }
    } else {
        echo "<td style='background-color: #f2dede;'>✗ Failed ($http_code)</td>";
        echo "<td>Request failed</td>";
    }

    echo "</tr>";
}

echo "</table>";

// Flutter app API constants
echo "<h2>Flutter App API Constants</h2>";
echo "<pre>";
echo "class ApiConstants {
  // API Base URL
  static const String baseUrl = '$base_url/api/v1';

  // API Endpoints
  static const String configEndpoint = '/direct_config.php';
  static const String loginEndpoint = '/direct_login.php';
  static const String registerEndpoint = '/direct_register.php';
  static const String moviesEndpoint = '/direct_movies.php';
  static const String movieDetailsEndpoint = '/direct_movie_details.php';
  static const String tvShowsEndpoint = '/direct_tvshows.php';
  static const String tvShowDetailsEndpoint = '/direct_tvshow_details.php';
  static const String tvShowEpisodesEndpoint = '/direct_tvshow_episodes.php';
  static const String searchEndpoint = '/direct_search.php';
  static const String categoriesEndpoint = '/direct_categories.php';
  static const String profileEndpoint = '/direct_profile.php';
}";
echo "</pre>";

echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li>Restart your Flutter app</li>";
echo "<li>Check if movies and TV shows appear on the home page</li>";
echo "<li>Check if download links appear on movie and TV show details pages</li>";
echo "</ol>";

echo "<p>If you still have issues, please check the following:</p>";
echo "<ol>";
echo "<li>Make sure your download_links table has data</li>";
echo "<li>Check if your API base URL in the Flutter app is correct</li>";
echo "<li>Check if your Flutter app has internet permissions</li>";
echo "</ol>";
?>
