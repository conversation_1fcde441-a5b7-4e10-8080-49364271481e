<?php
// Set page title
$page_title = 'Site Settings';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Process form submissions
$success_message = '';
$error_message = '';

// Update General Settings
if (isset($_POST['update_general'])) {
    $site_name = sanitize($_POST['site_name']);
    $site_description = sanitize($_POST['site_description']);
    $site_email = sanitize($_POST['site_email']);
    $site_url = sanitize($_POST['site_url']);
    $maintenance_mode = isset($_POST['maintenance_mode']) ? 1 : 0;
    $maintenance_message = sanitize($_POST['maintenance_message']);

    if (empty($site_name) || empty($site_url)) {
        $error_message = 'Site name and URL are required.';
    } else {
        // Update settings in config file
        $config_file = '../includes/config.php';
        $config_content = file_get_contents($config_file);

        // Replace values
        $config_content = preg_replace('/define\s*\(\s*[\'"]SITE_NAME[\'"]\s*,\s*[\'"].*?[\'"]\s*\)/', "define('SITE_NAME', '$site_name')", $config_content);
        $config_content = preg_replace('/define\s*\(\s*[\'"]SITE_DESCRIPTION[\'"]\s*,\s*[\'"].*?[\'"]\s*\)/', "define('SITE_DESCRIPTION', '$site_description')", $config_content);
        $config_content = preg_replace('/define\s*\(\s*[\'"]SITE_EMAIL[\'"]\s*,\s*[\'"].*?[\'"]\s*\)/', "define('SITE_EMAIL', '$site_email')", $config_content);
        $config_content = preg_replace('/define\s*\(\s*[\'"]SITE_URL[\'"]\s*,\s*[\'"].*?[\'"]\s*\)/', "define('SITE_URL', '$site_url')", $config_content);
        $config_content = preg_replace('/define\s*\(\s*[\'"]MAINTENANCE_MODE[\'"]\s*,\s*.*?\s*\)/', "define('MAINTENANCE_MODE', $maintenance_mode)", $config_content);
        $config_content = preg_replace('/define\s*\(\s*[\'"]MAINTENANCE_MESSAGE[\'"]\s*,\s*[\'"].*?[\'"]\s*\)/', "define('MAINTENANCE_MESSAGE', '$maintenance_message')", $config_content);

        // Write back to file
        if (file_put_contents($config_file, $config_content)) {
            $success_message = 'General settings updated successfully.';
        } else {
            $error_message = 'Error updating config file. Please check file permissions.';
        }
    }
}

// Update TMDB Settings
if (isset($_POST['update_tmdb'])) {
    $tmdb_api_key = sanitize($_POST['tmdb_api_key']);
    $tmdb_language = sanitize($_POST['tmdb_language']);

    if (empty($tmdb_api_key)) {
        $error_message = 'TMDB API key is required.';
    } else {
        // Update settings in config file
        $config_file = '../includes/config.php';
        $config_content = file_get_contents($config_file);

        // Replace values
        $config_content = preg_replace('/define\s*\(\s*[\'"]TMDB_API_KEY[\'"]\s*,\s*[\'"].*?[\'"]\s*\)/', "define('TMDB_API_KEY', '$tmdb_api_key')", $config_content);
        $config_content = preg_replace('/define\s*\(\s*[\'"]TMDB_LANGUAGE[\'"]\s*,\s*[\'"].*?[\'"]\s*\)/', "define('TMDB_LANGUAGE', '$tmdb_language')", $config_content);

        // Write back to file
        if (file_put_contents($config_file, $config_content)) {
            $success_message = 'TMDB settings updated successfully.';
        } else {
            $error_message = 'Error updating config file. Please check file permissions.';
        }
    }
}

// Update Social Media Settings
if (isset($_POST['update_social'])) {
    $facebook_url = sanitize($_POST['facebook_url']);
    $twitter_url = sanitize($_POST['twitter_url']);
    $instagram_url = sanitize($_POST['instagram_url']);
    $youtube_url = sanitize($_POST['youtube_url']);

    // Update settings in config file
    $config_file = '../includes/config.php';
    $config_content = file_get_contents($config_file);

    // Replace values
    $config_content = preg_replace('/define\s*\(\s*[\'"]FACEBOOK_URL[\'"]\s*,\s*[\'"].*?[\'"]\s*\)/', "define('FACEBOOK_URL', '$facebook_url')", $config_content);
    $config_content = preg_replace('/define\s*\(\s*[\'"]TWITTER_URL[\'"]\s*,\s*[\'"].*?[\'"]\s*\)/', "define('TWITTER_URL', '$twitter_url')", $config_content);
    $config_content = preg_replace('/define\s*\(\s*[\'"]INSTAGRAM_URL[\'"]\s*,\s*[\'"].*?[\'"]\s*\)/', "define('INSTAGRAM_URL', '$instagram_url')", $config_content);
    $config_content = preg_replace('/define\s*\(\s*[\'"]YOUTUBE_URL[\'"]\s*,\s*[\'"].*?[\'"]\s*\)/', "define('YOUTUBE_URL', '$youtube_url')", $config_content);

    // Write back to file
    if (file_put_contents($config_file, $config_content)) {
        $success_message = 'Social media settings updated successfully.';
    } else {
        $error_message = 'Error updating config file. Please check file permissions.';
    }
}

// Update Logo and Favicon
if (isset($_POST['update_logo'])) {
    $upload_dir = '../uploads/';
    $logo_updated = false;
    $favicon_updated = false;

    // Handle logo upload
    if (!empty($_FILES['site_logo']['name'])) {
        $logo_file = $_FILES['site_logo'];
        $logo_name = 'site-logo-' . time() . '.' . pathinfo($logo_file['name'], PATHINFO_EXTENSION);
        $logo_path = $upload_dir . $logo_name;

        // Check file type
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml'];
        if (!in_array($logo_file['type'], $allowed_types)) {
            $error_message = 'Invalid logo file type. Allowed types: JPG, PNG, GIF, SVG.';
        } else if ($logo_file['size'] > 1048576) { // 1MB
            $error_message = 'Logo file size must be less than 1MB.';
        } else if (move_uploaded_file($logo_file['tmp_name'], $logo_path)) {
            // Update logo in config
            $config_file = '../includes/config.php';
            $config_content = file_get_contents($config_file);
            $config_content = preg_replace('/define\s*\(\s*[\'"]SITE_LOGO[\'"]\s*,\s*[\'"].*?[\'"]\s*\)/', "define('SITE_LOGO', 'uploads/$logo_name')", $config_content);
            file_put_contents($config_file, $config_content);
            $logo_updated = true;
        } else {
            $error_message = 'Error uploading logo file.';
        }
    }

    // Handle favicon upload
    if (!empty($_FILES['site_favicon']['name'])) {
        $favicon_file = $_FILES['site_favicon'];
        $favicon_name = 'favicon-' . time() . '.' . pathinfo($favicon_file['name'], PATHINFO_EXTENSION);
        $favicon_path = $upload_dir . $favicon_name;

        // Check file type
        $allowed_types = ['image/x-icon', 'image/png', 'image/svg+xml'];
        if (!in_array($favicon_file['type'], $allowed_types)) {
            $error_message = 'Invalid favicon file type. Allowed types: ICO, PNG, SVG.';
        } else if ($favicon_file['size'] > 204800) { // 200KB
            $error_message = 'Favicon file size must be less than 200KB.';
        } else if (move_uploaded_file($favicon_file['tmp_name'], $favicon_path)) {
            // Update favicon in config
            $config_file = '../includes/config.php';
            $config_content = file_get_contents($config_file);
            $config_content = preg_replace('/define\s*\(\s*[\'"]SITE_FAVICON[\'"]\s*,\s*[\'"].*?[\'"]\s*\)/', "define('SITE_FAVICON', 'uploads/$favicon_name')", $config_content);
            file_put_contents($config_file, $config_content);
            $favicon_updated = true;
        } else {
            $error_message = 'Error uploading favicon file.';
        }
    }

    if ($logo_updated || $favicon_updated) {
        $success_message = 'Logo and favicon updated successfully.';
    }
}

// Get current settings
$site_name = defined('SITE_NAME') ? SITE_NAME : '';
$site_description = defined('SITE_DESCRIPTION') ? SITE_DESCRIPTION : '';
$site_email = defined('SITE_EMAIL') ? SITE_EMAIL : '';

// Get site URL without /admin part
$current_site_url = defined('SITE_URL') ? SITE_URL : '';
// Remove /admin from the end if it exists
$site_url = preg_replace('/\/admin$/', '', $current_site_url);

// Get other settings with safe defaults
$maintenance_mode = defined('MAINTENANCE_MODE') ? constant('MAINTENANCE_MODE') : 0;
$maintenance_message = defined('MAINTENANCE_MESSAGE') ? constant('MAINTENANCE_MESSAGE') : 'Site is under maintenance. Please check back later.';

$tmdb_api_key = defined('TMDB_API_KEY') ? constant('TMDB_API_KEY') : '';
$tmdb_language = defined('TMDB_LANGUAGE') ? constant('TMDB_LANGUAGE') : 'en-US';

$facebook_url = defined('FACEBOOK_URL') ? constant('FACEBOOK_URL') : '';
$twitter_url = defined('TWITTER_URL') ? constant('TWITTER_URL') : '';
$instagram_url = defined('INSTAGRAM_URL') ? constant('INSTAGRAM_URL') : '';
$youtube_url = defined('YOUTUBE_URL') ? constant('YOUTUBE_URL') : '';

$site_logo = defined('SITE_LOGO') ? constant('SITE_LOGO') : '';
$site_favicon = defined('SITE_FAVICON') ? constant('SITE_FAVICON') : '';

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Site Settings</h1>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <?php if($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-lg-3 mb-4">
                <div class="card shadow">
                    <div class="card-header bg-white py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Settings Navigation</h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                            <button class="nav-link active text-start py-3 px-4 border-bottom" id="general-tab" data-bs-toggle="pill" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">
                                <i class="fas fa-cog me-2"></i> General Settings
                            </button>
                            <button class="nav-link text-start py-3 px-4 border-bottom" id="tmdb-tab" data-bs-toggle="pill" data-bs-target="#tmdb" type="button" role="tab" aria-controls="tmdb" aria-selected="false">
                                <i class="fas fa-film me-2"></i> TMDB API Settings
                            </button>
                            <button class="nav-link text-start py-3 px-4 border-bottom" id="social-tab" data-bs-toggle="pill" data-bs-target="#social" type="button" role="tab" aria-controls="social" aria-selected="false">
                                <i class="fas fa-share-alt me-2"></i> Social Media
                            </button>
                            <button class="nav-link text-start py-3 px-4 border-bottom" id="logo-tab" data-bs-toggle="pill" data-bs-target="#logo" type="button" role="tab" aria-controls="logo" aria-selected="false">
                                <i class="fas fa-image me-2"></i> Logo & Favicon
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-9 mb-4">
                <div class="tab-content" id="v-pills-tabContent">
                    <!-- General Settings -->
                    <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                        <div class="card shadow">
                            <div class="card-header bg-white py-3">
                                <h6 class="m-0 font-weight-bold text-primary">General Settings</h6>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="" class="needs-validation" novalidate>
                                    <div class="mb-3">
                                        <label for="site_name" class="form-label">Site Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="site_name" name="site_name" value="<?php echo $site_name; ?>" required>
                                        <div class="invalid-feedback">
                                            Please enter a site name.
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="site_description" class="form-label">Site Description</label>
                                        <textarea class="form-control" id="site_description" name="site_description" rows="3"><?php echo $site_description; ?></textarea>
                                    </div>

                                    <div class="mb-3">
                                        <label for="site_email" class="form-label">Site Email</label>
                                        <input type="email" class="form-control" id="site_email" name="site_email" value="<?php echo $site_email; ?>">
                                        <div class="invalid-feedback">
                                            Please enter a valid email address.
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="site_url" class="form-label">Site URL <span class="text-danger">*</span></label>
                                        <input type="url" class="form-control" id="site_url" name="site_url" value="<?php echo $site_url; ?>" required>
                                        <div class="form-text">Include http:// or https:// and no trailing slash (e.g., https://example.com)</div>
                                        <div class="invalid-feedback">
                                            Please enter a valid URL.
                                        </div>
                                    </div>

                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="maintenance_mode" name="maintenance_mode" <?php echo $maintenance_mode ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="maintenance_mode">Maintenance Mode</label>
                                    </div>

                                    <div class="mb-3">
                                        <label for="maintenance_message" class="form-label">Maintenance Message</label>
                                        <textarea class="form-control" id="maintenance_message" name="maintenance_message" rows="3"><?php echo $maintenance_message; ?></textarea>
                                    </div>

                                    <div class="d-grid">
                                        <button type="submit" name="update_general" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Save General Settings
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- TMDB API Settings -->
                    <div class="tab-pane fade" id="tmdb" role="tabpanel" aria-labelledby="tmdb-tab">
                        <div class="card shadow">
                            <div class="card-header bg-white py-3">
                                <h6 class="m-0 font-weight-bold text-primary">TMDB API Settings</h6>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="" class="needs-validation" novalidate>
                                    <div class="mb-3">
                                        <label for="tmdb_api_key" class="form-label">TMDB API Key <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="tmdb_api_key" name="tmdb_api_key" value="<?php echo $tmdb_api_key; ?>" required>
                                        <div class="form-text">Get your API key from <a href="https://www.themoviedb.org/settings/api" target="_blank">themoviedb.org</a></div>
                                        <div class="invalid-feedback">
                                            Please enter your TMDB API key.
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="tmdb_language" class="form-label">TMDB Language</label>
                                        <select class="form-select" id="tmdb_language" name="tmdb_language">
                                            <option value="en-US" <?php echo $tmdb_language == 'en-US' ? 'selected' : ''; ?>>English (en-US)</option>
                                            <option value="bn-BD" <?php echo $tmdb_language == 'bn-BD' ? 'selected' : ''; ?>>Bengali (bn-BD)</option>
                                            <option value="hi-IN" <?php echo $tmdb_language == 'hi-IN' ? 'selected' : ''; ?>>Hindi (hi-IN)</option>
                                            <option value="ar-SA" <?php echo $tmdb_language == 'ar-SA' ? 'selected' : ''; ?>>Arabic (ar-SA)</option>
                                            <option value="es-ES" <?php echo $tmdb_language == 'es-ES' ? 'selected' : ''; ?>>Spanish (es-ES)</option>
                                            <option value="fr-FR" <?php echo $tmdb_language == 'fr-FR' ? 'selected' : ''; ?>>French (fr-FR)</option>
                                            <option value="de-DE" <?php echo $tmdb_language == 'de-DE' ? 'selected' : ''; ?>>German (de-DE)</option>
                                            <option value="it-IT" <?php echo $tmdb_language == 'it-IT' ? 'selected' : ''; ?>>Italian (it-IT)</option>
                                            <option value="ja-JP" <?php echo $tmdb_language == 'ja-JP' ? 'selected' : ''; ?>>Japanese (ja-JP)</option>
                                            <option value="ko-KR" <?php echo $tmdb_language == 'ko-KR' ? 'selected' : ''; ?>>Korean (ko-KR)</option>
                                            <option value="ru-RU" <?php echo $tmdb_language == 'ru-RU' ? 'selected' : ''; ?>>Russian (ru-RU)</option>
                                            <option value="zh-CN" <?php echo $tmdb_language == 'zh-CN' ? 'selected' : ''; ?>>Chinese (zh-CN)</option>
                                        </select>
                                    </div>

                                    <div class="d-grid">
                                        <button type="submit" name="update_tmdb" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Save TMDB Settings
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Social Media Settings -->
                    <div class="tab-pane fade" id="social" role="tabpanel" aria-labelledby="social-tab">
                        <div class="card shadow">
                            <div class="card-header bg-white py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Social Media Settings</h6>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="" class="needs-validation" novalidate>
                                    <div class="mb-3">
                                        <label for="facebook_url" class="form-label">Facebook URL</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fab fa-facebook-f"></i></span>
                                            <input type="url" class="form-control" id="facebook_url" name="facebook_url" value="<?php echo $facebook_url; ?>">
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="twitter_url" class="form-label">Twitter URL</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fab fa-twitter"></i></span>
                                            <input type="url" class="form-control" id="twitter_url" name="twitter_url" value="<?php echo $twitter_url; ?>">
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="instagram_url" class="form-label">Instagram URL</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fab fa-instagram"></i></span>
                                            <input type="url" class="form-control" id="instagram_url" name="instagram_url" value="<?php echo $instagram_url; ?>">
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="youtube_url" class="form-label">YouTube URL</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fab fa-youtube"></i></span>
                                            <input type="url" class="form-control" id="youtube_url" name="youtube_url" value="<?php echo $youtube_url; ?>">
                                        </div>
                                    </div>

                                    <div class="d-grid">
                                        <button type="submit" name="update_social" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Save Social Media Settings
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Logo & Favicon Settings -->
                    <div class="tab-pane fade" id="logo" role="tabpanel" aria-labelledby="logo-tab">
                        <div class="card shadow">
                            <div class="card-header bg-white py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Logo & Favicon Settings</h6>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="" enctype="multipart/form-data" class="needs-validation" novalidate>
                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <label for="site_logo" class="form-label">Site Logo</label>
                                            <input type="file" class="form-control" id="site_logo" name="site_logo" accept="image/jpeg,image/png,image/gif,image/svg+xml">
                                            <div class="form-text">Recommended size: 200x50px. Max size: 1MB.</div>

                                            <?php if(!empty($site_logo) && file_exists('../' . $site_logo)): ?>
                                            <div class="mt-3">
                                                <p>Current Logo:</p>
                                                <img src="<?php echo SITE_URL . '/' . $site_logo; ?>" alt="Site Logo" class="img-thumbnail" style="max-height: 100px;">
                                            </div>
                                            <?php endif; ?>
                                        </div>

                                        <div class="col-md-6">
                                            <label for="site_favicon" class="form-label">Favicon</label>
                                            <input type="file" class="form-control" id="site_favicon" name="site_favicon" accept="image/x-icon,image/png,image/svg+xml">
                                            <div class="form-text">Recommended size: 32x32px or 16x16px. Max size: 200KB.</div>

                                            <?php if(!empty($site_favicon) && file_exists('../' . $site_favicon)): ?>
                                            <div class="mt-3">
                                                <p>Current Favicon:</p>
                                                <img src="<?php echo SITE_URL . '/' . $site_favicon; ?>" alt="Favicon" class="img-thumbnail" style="max-height: 32px;">
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="d-grid">
                                        <button type="submit" name="update_logo" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Save Logo & Favicon
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
