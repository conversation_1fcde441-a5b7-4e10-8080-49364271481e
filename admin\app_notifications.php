<?php
// Include config file
require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Set page title
$page_title = 'অ্যাপ নোটিফিকেশন';

// Process form submission
$success_message = '';
$error_message = '';

// Check if notifications table exists
$check_table = "SHOW TABLES LIKE 'notifications'";
$result = mysqli_query($conn, $check_table);

if (mysqli_num_rows($result) == 0) {
    // Create notifications table
    $create_table = "CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        image_url VARCHAR(255) NULL,
        action_type ENUM('none', 'movie', 'tvshow', 'url') DEFAULT 'none',
        action_id VARCHAR(100) NULL,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    
    if (!mysqli_query($conn, $create_table)) {
        $error_message = 'Failed to create notifications table: ' . mysqli_error($conn);
    }
}

// Process notification form submission
if (isset($_POST['send_notification'])) {
    $title = sanitize($_POST['title']);
    $message = sanitize($_POST['message']);
    $image_url = sanitize($_POST['image_url']);
    $action_type = sanitize($_POST['action_type']);
    $action_id = sanitize($_POST['action_id']);
    $recipient_type = sanitize($_POST['recipient_type']);
    $specific_user = isset($_POST['specific_user']) ? (int)$_POST['specific_user'] : 0;
    
    // Validate inputs
    if (empty($title) || empty($message)) {
        $error_message = 'টাইটেল এবং মেসেজ প্রয়োজন।';
    } else {
        // Prepare SQL based on recipient type
        if ($recipient_type === 'all') {
            // Send to all users (user_id = NULL means broadcast)
            $sql = "INSERT INTO notifications (user_id, title, message, image_url, action_type, action_id) 
                    VALUES (NULL, ?, ?, ?, ?, ?)";
            $stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($stmt, 'sssss', $title, $message, $image_url, $action_type, $action_id);
        } elseif ($recipient_type === 'premium') {
            // Get all premium users and send individually
            $premium_users_query = "SELECT id FROM users WHERE is_premium = TRUE";
            $premium_users_result = mysqli_query($conn, $premium_users_query);
            
            if (mysqli_num_rows($premium_users_result) > 0) {
                $sql = "INSERT INTO notifications (user_id, title, message, image_url, action_type, action_id) 
                        VALUES (?, ?, ?, ?, ?, ?)";
                $stmt = mysqli_prepare($conn, $sql);
                
                while ($user = mysqli_fetch_assoc($premium_users_result)) {
                    mysqli_stmt_bind_param($stmt, 'isssss', $user['id'], $title, $message, $image_url, $action_type, $action_id);
                    mysqli_stmt_execute($stmt);
                }
            } else {
                $error_message = 'কোন প্রিমিয়াম ব্যবহারকারী পাওয়া যায়নি।';
            }
        } elseif ($recipient_type === 'specific' && $specific_user > 0) {
            // Send to specific user
            $sql = "INSERT INTO notifications (user_id, title, message, image_url, action_type, action_id) 
                    VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($stmt, 'isssss', $specific_user, $title, $message, $image_url, $action_type, $action_id);
        } else {
            $error_message = 'অবৈধ রিসিপিয়েন্ট টাইপ।';
        }
        
        // Execute query if no error
        if (empty($error_message)) {
            if (mysqli_stmt_execute($stmt)) {
                $success_message = 'নোটিফিকেশন সফলভাবে পাঠানো হয়েছে।';
            } else {
                $error_message = 'নোটিফিকেশন পাঠাতে ব্যর্থ: ' . mysqli_error($conn);
            }
        }
    }
}

// Delete notification
if (isset($_GET['delete']) && $_GET['delete'] > 0) {
    $notification_id = (int)$_GET['delete'];
    
    $delete_query = "DELETE FROM notifications WHERE id = $notification_id";
    
    if (mysqli_query($conn, $delete_query)) {
        $success_message = 'নোটিফিকেশন সফলভাবে মুছে ফেলা হয়েছে।';
    } else {
        $error_message = 'নোটিফিকেশন মুছতে ব্যর্থ: ' . mysqli_error($conn);
    }
}

// Get all users for dropdown
$users_query = "SELECT id, username FROM users ORDER BY username";
$users_result = mysqli_query($conn, $users_query);

// Get all movies for dropdown
$movies_query = "SELECT id, title FROM movies ORDER BY title";
$movies_result = mysqli_query($conn, $movies_query);

// Get all TV shows for dropdown
$tvshows_query = "SELECT id, title FROM tvshows ORDER BY title";
$tvshows_result = mysqli_query($conn, $tvshows_query);

// Get recent notifications
$notifications_query = "SELECT n.*, u.username 
                       FROM notifications n
                       LEFT JOIN users u ON n.user_id = u.id
                       ORDER BY n.created_at DESC
                       LIMIT 50";
$notifications_result = mysqli_query($conn, $notifications_query);

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title d-flex align-items-center">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1><?php echo $page_title; ?></h1>
            </div>

            <div class="topbar-actions">
                <div class="topbar-search">
                    <form action="search.php" method="GET">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="সার্চ করুন..." name="q">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>

                <div class="dropdown">
                    <button class="btn btn-link dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <img src="assets/img/user.png" class="rounded-circle" width="32" height="32" alt="User">
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user fa-sm me-2 text-gray-400"></i> প্রোফাইল</a></li>
                        <li><a class="dropdown-item" href="site_settings.php"><i class="fas fa-cogs fa-sm me-2 text-gray-400"></i> সেটিংস</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/logout.php"><i class="fas fa-sign-out-alt fa-sm me-2 text-gray-400"></i> লগআউট</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container-fluid">
        <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="row">
            <!-- Send Notification Form -->
            <div class="col-lg-5">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">নতুন নোটিফিকেশন পাঠান</h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="">
                            <!-- Notification Title -->
                            <div class="mb-3">
                                <label for="title" class="form-label">টাইটেল <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" required>
                            </div>

                            <!-- Notification Message -->
                            <div class="mb-3">
                                <label for="message" class="form-label">মেসেজ <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="message" name="message" rows="3" required></textarea>
                            </div>

                            <!-- Image URL -->
                            <div class="mb-3">
                                <label for="image_url" class="form-label">ইমেজ URL (ঐচ্ছিক)</label>
                                <input type="url" class="form-control" id="image_url" name="image_url">
                                <div class="form-text">নোটিফিকেশনে দেখানোর জন্য ইমেজের URL</div>
                            </div>

                            <!-- Action Type -->
                            <div class="mb-3">
                                <label for="action_type" class="form-label">অ্যাকশন টাইপ</label>
                                <select class="form-select" id="action_type" name="action_type">
                                    <option value="none">কোন অ্যাকশন নেই</option>
                                    <option value="movie">মুভি</option>
                                    <option value="tvshow">টিভি শো</option>
                                    <option value="url">URL</option>
                                </select>
                            </div>

                            <!-- Action ID (conditional based on action type) -->
                            <div class="mb-3 action-fields" id="movie-field" style="display: none;">
                                <label for="movie_id" class="form-label">মুভি নির্বাচন করুন</label>
                                <select class="form-select" id="movie_id">
                                    <option value="">মুভি নির্বাচন করুন</option>
                                    <?php if ($movies_result && mysqli_num_rows($movies_result) > 0): ?>
                                        <?php while ($movie = mysqli_fetch_assoc($movies_result)): ?>
                                            <option value="<?php echo $movie['id']; ?>"><?php echo htmlspecialchars($movie['title']); ?></option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>

                            <div class="mb-3 action-fields" id="tvshow-field" style="display: none;">
                                <label for="tvshow_id" class="form-label">টিভি শো নির্বাচন করুন</label>
                                <select class="form-select" id="tvshow_id">
                                    <option value="">টিভি শো নির্বাচন করুন</option>
                                    <?php if ($tvshows_result && mysqli_num_rows($tvshows_result) > 0): ?>
                                        <?php while ($tvshow = mysqli_fetch_assoc($tvshows_result)): ?>
                                            <option value="<?php echo $tvshow['id']; ?>"><?php echo htmlspecialchars($tvshow['title']); ?></option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>

                            <div class="mb-3 action-fields" id="url-field" style="display: none;">
                                <label for="url" class="form-label">URL</label>
                                <input type="url" class="form-control" id="url">
                            </div>

                            <input type="hidden" name="action_id" id="action_id" value="">

                            <!-- Recipients -->
                            <div class="mb-3">
                                <label for="recipient_type" class="form-label">রিসিপিয়েন্ট</label>
                                <select class="form-select" id="recipient_type" name="recipient_type">
                                    <option value="all">সকল ব্যবহারকারী</option>
                                    <option value="premium">শুধু প্রিমিয়াম ব্যবহারকারী</option>
                                    <option value="specific">নির্দিষ্ট ব্যবহারকারী</option>
                                </select>
                            </div>

                            <!-- Specific User (conditional) -->
                            <div class="mb-3" id="specific-user-field" style="display: none;">
                                <label for="specific_user" class="form-label">ব্যবহারকারী নির্বাচন করুন</label>
                                <select class="form-select" id="specific_user" name="specific_user">
                                    <option value="">ব্যবহারকারী নির্বাচন করুন</option>
                                    <?php if ($users_result && mysqli_num_rows($users_result) > 0): ?>
                                        <?php while ($user = mysqli_fetch_assoc($users_result)): ?>
                                            <option value="<?php echo $user['id']; ?>"><?php echo htmlspecialchars($user['username']); ?></option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>

                            <!-- Submit Button -->
                            <div class="text-center mt-4">
                                <button type="submit" name="send_notification" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>নোটিফিকেশন পাঠান
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Recent Notifications -->
            <div class="col-lg-7">
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">সাম্প্রতিক নোটিফিকেশন</h6>
                    </div>
                    <div class="card-body">
                        <?php if ($notifications_result && mysqli_num_rows($notifications_result) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>টাইটেল</th>
                                            <th>রিসিপিয়েন্ট</th>
                                            <th>অ্যাকশন</th>
                                            <th>তারিখ</th>
                                            <th>অ্যাকশন</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($notification = mysqli_fetch_assoc($notifications_result)): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <?php if (!empty($notification['image_url'])): ?>
                                                            <img src="<?php echo htmlspecialchars($notification['image_url']); ?>" class="rounded me-2" width="32" height="32" alt="Notification Image">
                                                        <?php else: ?>
                                                            <i class="fas fa-bell text-primary me-2"></i>
                                                        <?php endif; ?>
                                                        <div>
                                                            <div class="fw-bold"><?php echo htmlspecialchars($notification['title']); ?></div>
                                                            <div class="small text-muted text-truncate" style="max-width: 200px;"><?php echo htmlspecialchars($notification['message']); ?></div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php if ($notification['user_id'] === null): ?>
                                                        <span class="badge bg-primary">সকল</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-info"><?php echo htmlspecialchars($notification['username'] ?? 'Unknown'); ?></span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($notification['action_type'] === 'movie'): ?>
                                                        <span class="badge bg-success">মুভি #<?php echo $notification['action_id']; ?></span>
                                                    <?php elseif ($notification['action_type'] === 'tvshow'): ?>
                                                        <span class="badge bg-warning">টিভি শো #<?php echo $notification['action_id']; ?></span>
                                                    <?php elseif ($notification['action_type'] === 'url'): ?>
                                                        <span class="badge bg-info">URL</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">কোন অ্যাকশন নেই</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo date('d M Y H:i', strtotime($notification['created_at'])); ?></td>
                                                <td>
                                                    <a href="#" class="btn btn-sm btn-info view-notification" data-id="<?php echo $notification['id']; ?>" data-title="<?php echo htmlspecialchars($notification['title']); ?>" data-message="<?php echo htmlspecialchars($notification['message']); ?>" data-bs-toggle="modal" data-bs-target="#viewNotificationModal">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="app_notifications.php?delete=<?php echo $notification['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই নোটিফিকেশন মুছতে চান?');">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <p class="text-muted">কোন নোটিফিকেশন পাওয়া যায়নি</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- View Notification Modal -->
<div class="modal fade" id="viewNotificationModal" tabindex="-1" aria-labelledby="viewNotificationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewNotificationModalLabel">নোটিফিকেশন দেখুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h5 id="notification-title"></h5>
                <p id="notification-message"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বন্ধ করুন</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Action type change handler
    const actionTypeSelect = document.getElementById('action_type');
    const movieField = document.getElementById('movie-field');
    const tvshowField = document.getElementById('tvshow-field');
    const urlField = document.getElementById('url-field');
    const actionIdInput = document.getElementById('action_id');
    
    actionTypeSelect.addEventListener('change', function() {
        // Hide all action fields
        document.querySelectorAll('.action-fields').forEach(field => {
            field.style.display = 'none';
        });
        
        // Show relevant field based on selection
        if (this.value === 'movie') {
            movieField.style.display = 'block';
        } else if (this.value === 'tvshow') {
            tvshowField.style.display = 'block';
        } else if (this.value === 'url') {
            urlField.style.display = 'block';
        }
        
        // Reset action_id
        actionIdInput.value = '';
    });
    
    // Movie selection handler
    document.getElementById('movie_id').addEventListener('change', function() {
        actionIdInput.value = this.value;
    });
    
    // TV Show selection handler
    document.getElementById('tvshow_id').addEventListener('change', function() {
        actionIdInput.value = this.value;
    });
    
    // URL input handler
    document.getElementById('url').addEventListener('input', function() {
        actionIdInput.value = this.value;
    });
    
    // Recipient type change handler
    const recipientTypeSelect = document.getElementById('recipient_type');
    const specificUserField = document.getElementById('specific-user-field');
    
    recipientTypeSelect.addEventListener('change', function() {
        if (this.value === 'specific') {
            specificUserField.style.display = 'block';
        } else {
            specificUserField.style.display = 'none';
        }
    });
    
    // View notification modal handler
    document.querySelectorAll('.view-notification').forEach(button => {
        button.addEventListener('click', function() {
            const title = this.getAttribute('data-title');
            const message = this.getAttribute('data-message');
            
            document.getElementById('notification-title').textContent = title;
            document.getElementById('notification-message').textContent = message;
        });
    });
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
