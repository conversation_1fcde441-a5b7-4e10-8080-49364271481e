<?php
// Set error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Try different possible locations for config and database files
$possible_config_files = [
    'includes/config.php',
    'config/config.php',
    'config.php'
];

$possible_db_files = [
    'includes/db.php',
    'config/db.php',
    'includes/database.php',
    'config/database.php',
    'db.php',
    'database.php'
];

$config_loaded = false;
foreach ($possible_config_files as $config_file) {
    if (file_exists($config_file)) {
        require_once $config_file;
        $config_loaded = true;
        break;
    }
}

if (!$config_loaded) {
    // Define SITE_URL if not defined in config
    if (!defined('SITE_URL')) {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
        $host = $_SERVER['HTTP_HOST'];
        $script_name = dirname($_SERVER['SCRIPT_NAME']);
        $path = $script_name !== '/' ? $script_name : '';
        define('SITE_URL', $protocol . $host . $path);
    }
}

// Try to find and include database connection file
$db_loaded = false;
foreach ($possible_db_files as $db_file) {
    if (file_exists($db_file)) {
        require_once $db_file;
        $db_loaded = true;
        break;
    }
}

// OpenSubtitles API credentials
// You need to register at https://www.opensubtitles.com/en/consumers to get an API key
define('OPENSUBTITLES_API_KEY', 'YOUR_OPENSUBTITLES_API_KEY'); // Replace with your API key

// TMDB API credentials
define('TMDB_API_KEY', 'YOUR_TMDB_API_KEY'); // Replace with your TMDB API key

/**
 * Search for subtitles using TMDB ID via OpenSubtitles API
 * 
 * @param int $tmdb_id TMDB ID of the movie or TV show
 * @param int $season_number Season number (for TV shows)
 * @param int $episode_number Episode number (for TV shows)
 * @param string $language Language code (default: 'bn' for Bengali)
 * @return array Array of subtitle URLs or empty array if not found
 */
function findSubtitlesByTMDB($tmdb_id, $season_number = null, $episode_number = null, $languages = ['bn', 'en']) {
    // Skip if no TMDB ID
    if (empty($tmdb_id)) {
        return [];
    }
    
    $subtitle_urls = [];
    
    // First, get IMDB ID from TMDB API
    $imdb_id = getIMDBIDFromTMDB($tmdb_id, ($season_number !== null && $episode_number !== null));
    
    if (empty($imdb_id)) {
        return [];
    }
    
    foreach ($languages as $language) {
        // Prepare API request
        $api_url = 'https://api.opensubtitles.com/api/v1/subtitles';
        $params = [];
        
        // Remove 'tt' prefix if present
        $imdb_id = str_replace('tt', '', $imdb_id);
        
        // For movies
        if ($season_number === null && $episode_number === null) {
            $params['imdb_id'] = $imdb_id;
        } 
        // For TV episodes
        else {
            $params['parent_imdb_id'] = $imdb_id;
            $params['season_number'] = $season_number;
            $params['episode_number'] = $episode_number;
        }
        
        // Add language
        $params['languages'] = $language;
        
        // Build query string
        $query_string = http_build_query($params);
        $search_url = $api_url . '?' . $query_string;
        
        // Call OpenSubtitles API
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $search_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Api-Key: ' . OPENSUBTITLES_API_KEY,
            'Content-Type: application/json'
        ]);
        
        $response = curl_exec($ch);
        curl_close($ch);
        
        if (!$response) {
            continue;
        }
        
        $data = json_decode($response, true);
        
        // Check if subtitles were found
        if (isset($data['data']) && !empty($data['data'])) {
            // Get the first subtitle file ID
            $file_id = $data['data'][0]['attributes']['files'][0]['file_id'];
            
            // Download the subtitle
            $subtitle_url = downloadSubtitle($file_id);
            if ($subtitle_url !== null) {
                $subtitle_urls[$language] = $subtitle_url;
            }
        }
    }
    
    return $subtitle_urls;
}

/**
 * Get IMDB ID from TMDB ID using TMDB API
 * 
 * @param int $tmdb_id TMDB ID
 * @param bool $is_tv Whether this is a TV show
 * @return string|null IMDB ID or null if not found
 */
function getIMDBIDFromTMDB($tmdb_id, $is_tv = false) {
    $type = $is_tv ? 'tv' : 'movie';
    $url = "https://api.themoviedb.org/3/{$type}/{$tmdb_id}/external_ids?api_key=" . TMDB_API_KEY;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    curl_close($ch);
    
    if (!$response) {
        return null;
    }
    
    $data = json_decode($response, true);
    
    if (isset($data['imdb_id'])) {
        return $data['imdb_id'];
    }
    
    return null;
}

/**
 * Download subtitle file from OpenSubtitles
 * 
 * @param string $file_id The file ID from OpenSubtitles
 * @return string|null URL to the downloaded subtitle file or null if failed
 */
function downloadSubtitle($file_id) {
    $download_url = 'https://api.opensubtitles.com/api/v1/download';
    
    // Call OpenSubtitles API to download
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $download_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['file_id' => $file_id]));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Api-Key: ' . OPENSUBTITLES_API_KEY,
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    if (!$response) {
        return null;
    }
    
    $data = json_decode($response, true);
    
    // Check if download link is available
    if (isset($data['link'])) {
        // Download the subtitle file
        $subtitle_content = file_get_contents($data['link']);
        
        // Save to a local file
        $file_name = 'subtitles/' . md5($file_id . time()) . '.srt';
        $file_path = __DIR__ . '/' . $file_name;
        
        // Create subtitles directory if it doesn't exist
        if (!file_exists(__DIR__ . '/subtitles')) {
            mkdir(__DIR__ . '/subtitles', 0755, true);
        }
        
        // Save the subtitle file
        file_put_contents($file_path, $subtitle_content);
        
        // Return the URL to the subtitle file
        return SITE_URL . '/' . $file_name;
    }
    
    return null;
}

/**
 * Get subtitle URLs for a movie
 * 
 * @param int $movie_id Movie ID from database
 * @param int $link_id Link ID from database (optional)
 * @return array Array of subtitle URLs by language
 */
function getMovieSubtitleUrls($movie_id, $link_id = null) {
    global $conn;
    
    if (!isset($conn) || !$conn) {
        return [];
    }
    
    $subtitle_urls = [];
    
    // First check if there are manual subtitle URLs for this link
    if ($link_id !== null) {
        $sql = "SELECT subtitle_url_bn, subtitle_url_en FROM links WHERE id = ? AND (subtitle_url_bn IS NOT NULL OR subtitle_url_en IS NOT NULL)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $link_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            if (!empty($row['subtitle_url_bn'])) {
                $subtitle_urls['bn'] = $row['subtitle_url_bn'];
            }
            if (!empty($row['subtitle_url_en'])) {
                $subtitle_urls['en'] = $row['subtitle_url_en'];
            }
            
            // If both languages are found, return them
            if (isset($subtitle_urls['bn']) && isset($subtitle_urls['en'])) {
                return $subtitle_urls;
            }
        }
    }
    
    // If no manual subtitles or missing some languages, get movie TMDB ID and try to find subtitles
    $sql = "SELECT tmdb_id FROM movies WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $movie_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $tmdb_id = $row['tmdb_id'];
        
        // Find subtitles using TMDB ID
        if (!empty($tmdb_id)) {
            // Only search for languages that we don't already have
            $languages_to_search = [];
            if (!isset($subtitle_urls['bn'])) $languages_to_search[] = 'bn';
            if (!isset($subtitle_urls['en'])) $languages_to_search[] = 'en';
            
            if (!empty($languages_to_search)) {
                $found_subtitles = findSubtitlesByTMDB($tmdb_id, null, null, $languages_to_search);
                $subtitle_urls = array_merge($subtitle_urls, $found_subtitles);
                
                // If found, update the link with the subtitle URLs
                if (!empty($found_subtitles) && $link_id !== null) {
                    $update_sql = "UPDATE links SET ";
                    $update_params = [];
                    $update_types = "";
                    
                    if (isset($found_subtitles['bn'])) {
                        $update_sql .= "subtitle_url_bn = ?, ";
                        $update_params[] = $found_subtitles['bn'];
                        $update_types .= "s";
                    }
                    
                    if (isset($found_subtitles['en'])) {
                        $update_sql .= "subtitle_url_en = ?, ";
                        $update_params[] = $found_subtitles['en'];
                        $update_types .= "s";
                    }
                    
                    // Remove trailing comma and space
                    $update_sql = rtrim($update_sql, ", ");
                    
                    $update_sql .= " WHERE id = ?";
                    $update_params[] = $link_id;
                    $update_types .= "i";
                    
                    $update_stmt = $conn->prepare($update_sql);
                    $update_stmt->bind_param($update_types, ...$update_params);
                    $update_stmt->execute();
                }
            }
        }
    }
    
    return $subtitle_urls;
}

/**
 * Get subtitle URLs for a TV episode
 * 
 * @param int $series_id Series ID from database
 * @param int $season_number Season number
 * @param int $episode_number Episode number
 * @param int $link_id Link ID from database (optional)
 * @return array Array of subtitle URLs by language
 */
function getEpisodeSubtitleUrls($series_id, $season_number, $episode_number, $link_id = null) {
    global $conn;
    
    if (!isset($conn) || !$conn) {
        return [];
    }
    
    $subtitle_urls = [];
    
    // First check if there are manual subtitle URLs for this link
    if ($link_id !== null) {
        $sql = "SELECT subtitle_url_bn, subtitle_url_en FROM episode_links WHERE id = ? AND (subtitle_url_bn IS NOT NULL OR subtitle_url_en IS NOT NULL)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $link_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            if (!empty($row['subtitle_url_bn'])) {
                $subtitle_urls['bn'] = $row['subtitle_url_bn'];
            }
            if (!empty($row['subtitle_url_en'])) {
                $subtitle_urls['en'] = $row['subtitle_url_en'];
            }
            
            // If both languages are found, return them
            if (isset($subtitle_urls['bn']) && isset($subtitle_urls['en'])) {
                return $subtitle_urls;
            }
        }
    }
    
    // If no manual subtitles or missing some languages, get series TMDB ID and try to find subtitles
    $sql = "SELECT tmdb_id FROM series WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $series_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $tmdb_id = $row['tmdb_id'];
        
        // Find subtitles using TMDB ID
        if (!empty($tmdb_id)) {
            // Only search for languages that we don't already have
            $languages_to_search = [];
            if (!isset($subtitle_urls['bn'])) $languages_to_search[] = 'bn';
            if (!isset($subtitle_urls['en'])) $languages_to_search[] = 'en';
            
            if (!empty($languages_to_search)) {
                $found_subtitles = findSubtitlesByTMDB($tmdb_id, $season_number, $episode_number, $languages_to_search);
                $subtitle_urls = array_merge($subtitle_urls, $found_subtitles);
                
                // If found, update the link with the subtitle URLs
                if (!empty($found_subtitles) && $link_id !== null) {
                    $update_sql = "UPDATE episode_links SET ";
                    $update_params = [];
                    $update_types = "";
                    
                    if (isset($found_subtitles['bn'])) {
                        $update_sql .= "subtitle_url_bn = ?, ";
                        $update_params[] = $found_subtitles['bn'];
                        $update_types .= "s";
                    }
                    
                    if (isset($found_subtitles['en'])) {
                        $update_sql .= "subtitle_url_en = ?, ";
                        $update_params[] = $found_subtitles['en'];
                        $update_types .= "s";
                    }
                    
                    // Remove trailing comma and space
                    $update_sql = rtrim($update_sql, ", ");
                    
                    $update_sql .= " WHERE id = ?";
                    $update_params[] = $link_id;
                    $update_types .= "i";
                    
                    $update_stmt = $conn->prepare($update_sql);
                    $update_stmt->bind_param($update_types, ...$update_params);
                    $update_stmt->execute();
                }
            }
        }
    }
    
    return $subtitle_urls;
}
?>
