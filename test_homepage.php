<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/ad_placeholder.php';

echo "<h1>Homepage Test</h1>";

// Test database connection
if ($conn) {
    echo "<p style='color: green;'>✅ Database connected successfully</p>";
} else {
    echo "<p style='color: red;'>❌ Database connection failed: " . mysqli_connect_error() . "</p>";
}

// Test ad function
if (function_exists('renderRealAd')) {
    echo "<p style='color: green;'>✅ renderRealAd function exists</p>";
} else {
    echo "<p style='color: red;'>❌ renderRealAd function not found</p>";
}

// Test premium user function
if (function_exists('isPremiumUser')) {
    echo "<p style='color: green;'>✅ isPremiumUser function exists</p>";
    $isPremium = isPremiumUser();
    echo "<p>Premium status: " . ($isPremium ? 'Yes' : 'No') . "</p>";
} else {
    echo "<p style='color: red;'>❌ isPremiumUser function not found</p>";
}

// Test queries
echo "<h2>Database Test:</h2>";

// Test latest movies query
$latest_movies_query = "SELECT m.*, c.name as category_name FROM movies m LEFT JOIN categories c ON m.category_id = c.id ORDER BY m.created_at DESC LIMIT 5";
$latest_movies_result = mysqli_query($conn, $latest_movies_query);

if ($latest_movies_result) {
    $count = mysqli_num_rows($latest_movies_result);
    echo "<p style='color: green;'>✅ Latest movies query successful - Found $count movies</p>";
} else {
    echo "<p style='color: red;'>❌ Latest movies query failed: " . mysqli_error($conn) . "</p>";
}

// Test featured movies query
$featured_movies_query = "SELECT m.*, c.name as category_name FROM movies m LEFT JOIN categories c ON m.category_id = c.id ORDER BY m.rating DESC LIMIT 5";
$featured_movies_result = mysqli_query($conn, $featured_movies_query);

if ($featured_movies_result) {
    $count = mysqli_num_rows($featured_movies_result);
    echo "<p style='color: green;'>✅ Featured movies query successful - Found $count movies</p>";
} else {
    echo "<p style='color: red;'>❌ Featured movies query failed: " . mysqli_error($conn) . "</p>";
}

// Test ad rendering
echo "<h2>Ad Test:</h2>";
echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
echo renderRealAd('banner_728x90');
echo "</div>";

echo "<h2>CSS Files Test:</h2>";
$css_files = [
    'css/homepage-modern.css',
    'css/ad-styling.css'
];

foreach ($css_files as $css_file) {
    if (file_exists($css_file)) {
        echo "<p style='color: green;'>✅ $css_file exists</p>";
    } else {
        echo "<p style='color: red;'>❌ $css_file not found</p>";
    }
}

echo "<h2>JavaScript Files Test:</h2>";
$js_files = [
    'js/ad-enhancements.js'
];

foreach ($js_files as $js_file) {
    if (file_exists($js_file)) {
        echo "<p style='color: green;'>✅ $js_file exists</p>";
    } else {
        echo "<p style='color: red;'>❌ $js_file not found</p>";
    }
}
?>
