<?php
// Include necessary files
require_once 'includes/config.php';
require_once 'includes/streaming_helper.php';

// Test Cloudflare Worker URL
$worker_url = "https://odd-darkness-074fsadsafsafasfjlknmmlkaytr9pe8afnhdklnfalskdftgy.bdmovieshub.workers.dev/994/RRR+2022+-+Hindi+-WEBDL+-+720p.mkv?hash=AgADjg";
$title = "উন্নত প্লেয়ার টেস্ট";
$poster = "https://image.tmdb.org/t/p/w500/wE0I6efAW4cDDmZQWtwZMOW44EJ.jpg";

// Get streaming URL
$streaming_url = "plyr_player_enhanced.php?url=" . urlencode($worker_url) . "&title=" . urlencode($title) . "&poster=" . urlencode($poster);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>উন্নত প্লেয়ার টেস্ট</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #111;
            color: #fff;
            padding: 20px;
            font-family: 'SolaimanLipi', Arial, sans-serif;
        }
        .card {
            background-color: #222;
            border: none;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #333;
            color: #fff;
            border-bottom: 1px solid #444;
        }
        .btn-primary {
            background-color: #e50914;
            border-color: #e50914;
        }
        .btn-primary:hover {
            background-color: #b30710;
            border-color: #b30710;
        }
        .feature-list {
            list-style-type: none;
            padding-left: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #333;
            display: flex;
            align-items: center;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list li i {
            color: #e50914;
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        .steps-list {
            counter-reset: step-counter;
            list-style-type: none;
            padding-left: 0;
        }
        .steps-list li {
            position: relative;
            padding: 10px 0 10px 40px;
            margin-bottom: 10px;
            border-bottom: 1px solid #333;
        }
        .steps-list li:before {
            content: counter(step-counter);
            counter-increment: step-counter;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 30px;
            height: 30px;
            background-color: #e50914;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">উন্নত প্লেয়ার টেস্ট</h1>

        <div class="card">
            <div class="card-header">
                <h5>নতুন ফিচারসমূহ</h5>
            </div>
            <div class="card-body">
                <ul class="feature-list">
                    <li><i class="fas fa-history"></i> <strong>প্লেব্যাক পপআপ:</strong> আগে দেখা থাকলে একটি পপআপ আসবে যেখানে আপনি আগের অবস্থান থেকে বা শুরু থেকে দেখার অপশন পাবেন</li>
                    <li><i class="fas fa-tachometer-alt"></i> <strong>দ্রুত লোডিং:</strong> ভিডিও লোডিং প্রক্রিয়া উন্নত করা হয়েছে যাতে দ্রুত লোড হয়</li>
                    <li><i class="fas fa-forward"></i> <strong>বাফারিং উন্নতি:</strong> বাফারিং সিস্টেম উন্নত করা হয়েছে যাতে ভিডিও স্মুথভাবে চলে</li>
                    <li><i class="fas fa-wifi"></i> <strong>নেটওয়ার্ক অপ্টিমাইজেশন:</strong> নেটওয়ার্ক কন্ডিশন অনুযায়ী ভিডিও কোয়ালিটি অপ্টিমাইজ করা হয়েছে</li>
                    <li><i class="fas fa-keyboard"></i> <strong>কীবোর্ড শর্টকাট:</strong> Escape কী চাপলে পপআপ বন্ধ হবে এবং ভিডিও চালু হবে</li>
                </ul>

                <div class="mt-4">
                    <a href="<?php echo $streaming_url; ?>" class="btn btn-primary">
                        <i class="fas fa-play-circle me-2"></i>উন্নত প্লেয়ার টেস্ট করুন
                    </a>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5>টেস্ট করার পদ্ধতি</h5>
            </div>
            <div class="card-body">
                <ol class="steps-list">
                    <li>প্লেয়ার খুলুন এবং ভিডিও কিছুক্ষণ চালান (কমপক্ষে ৩০ সেকেন্ড)</li>
                    <li>ব্রাউজার বন্ধ করুন বা পেজ রিফ্রেশ করুন</li>
                    <li>আবার প্লেয়ার খুলুন - আপনি দেখবেন একটি পপআপ আসবে যেখানে আপনি আগের অবস্থান থেকে বা শুরু থেকে দেখার অপশন পাবেন</li>
                    <li>"আগের অবস্থান থেকে দেখুন" বাটনে ক্লিক করুন - ভিডিও আগের অবস্থান থেকে চালু হবে</li>
                    <li>আবার রিফ্রেশ করুন এবং এবার "শুরু থেকে দেখুন" বাটনে ক্লিক করুন - ভিডিও শুরু থেকে চালু হবে</li>
                </ol>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>টিপস:</strong> পপআপ দেখানোর জন্য আপনাকে অবশ্যই ৩০ সেকেন্ডের বেশি ভিডিও দেখতে হবে। এর কম হলে পপআপ আসবে না।
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
