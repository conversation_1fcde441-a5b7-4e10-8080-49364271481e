-- Use existing database
USE tipsbdxy_4525;

-- Premium plans table
CREATE TABLE IF NOT EXISTS premium_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    duration INT NOT NULL COMMENT 'Duration in days',
    features TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Subscriptions table
CREATE TABLE IF NOT EXISTS subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    plan_id INT NOT NULL,
    start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP,
    status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (plan_id) REFERENCES premium_plans(id) ON DELETE CASCADE
);

-- Payments table
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    subscription_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('bkash', 'nagad', 'rocket', 'manual') NOT NULL,
    transaction_id VARCHAR(100),
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE CASCADE
);

-- Add premium_only field to movies and tvshows tables if they don't exist
ALTER TABLE movies ADD COLUMN IF NOT EXISTS premium_only BOOLEAN DEFAULT FALSE;
ALTER TABLE tvshows ADD COLUMN IF NOT EXISTS premium_only BOOLEAN DEFAULT FALSE;
ALTER TABLE episodes ADD COLUMN IF NOT EXISTS is_premium BOOLEAN DEFAULT FALSE;

-- Add premium field to users table if it doesn't exist
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_premium BOOLEAN DEFAULT FALSE;

-- Insert sample premium plans if table is empty
INSERT INTO premium_plans (name, price, duration, features)
SELECT 'Basic', 199, 30, 'Access to all premium movies and TV shows\nWatch on one device at a time\nStandard video quality'
FROM dual
WHERE NOT EXISTS (SELECT 1 FROM premium_plans LIMIT 1);

INSERT INTO premium_plans (name, price, duration, features)
SELECT 'Standard', 399, 30, 'Access to all premium movies and TV shows\nWatch on two devices at a time\nHD video quality available'
FROM dual
WHERE (SELECT COUNT(*) FROM premium_plans) = 1;

INSERT INTO premium_plans (name, price, duration, features)
SELECT 'Premium', 599, 30, 'Access to all premium movies and TV shows\nWatch on four devices at a time\nHD and Ultra HD video quality available'
FROM dual
WHERE (SELECT COUNT(*) FROM premium_plans) = 2;
