<?php
// Start output buffering to prevent 'headers already sent' errors
ob_start();

// Include config file if not already included
if (!defined('SITE_URL')) {
    require_once 'includes/config.php';
}

// Vidzee.wtf Proxy Script
// This script acts as a proxy between the client and vidzee.wtf video sources

// Set unlimited execution time for large files
set_time_limit(0);

// Increase memory limit
ini_set('memory_limit', '512M');

// Get video URL from query parameter
if (!isset($_GET['url']) || empty($_GET['url'])) {
    header('HTTP/1.1 400 Bad Request');
    exit('URL parameter is required');
}

$url = urldecode($_GET['url']);

// Validate URL
if (!filter_var($url, FILTER_VALIDATE_URL)) {
    header('HTTP/1.1 400 Bad Request');
    exit('Invalid URL');
}

// Check if it's a vidzee.wtf URL
if (strpos($url, 'vidzee.wtf') === false) {
    header('HTTP/1.1 400 Bad Request');
    exit('Only vidzee.wtf URLs are supported by this proxy');
}

// Extract the content ID from the URL
$parts = explode('/', $url);
$content_id = end($parts);

// For TV shows, we need to handle season and episode numbers
if (strpos($url, 'vidzee.wtf/tv/') !== false) {
    // Format: vidzee.wtf/tv/{id}/{season}/{episode}
    $episode_number = $content_id;
    $season_number = $parts[count($parts) - 2];
    $show_id = $parts[count($parts) - 3];

    // Construct a direct video URL (this is an example, adjust as needed)
    $direct_url = "https://nim-pata-worker-quiet-river-e51c.bdmovieshub.workers.dev/tv/$show_id/$season_number/$episode_number";
} else {
    // Format: vidzee.wtf/movie/{id}
    // Construct a direct video URL (this is an example, adjust as needed)
    $direct_url = "https://nim-pata-worker-quiet-river-e51c.bdmovieshub.workers.dev/movie/$content_id";
}

// Set content type to HLS
header('Content-Type: application/x-mpegURL');

// Log the request for debugging (only if in debug mode)
if (defined('DEBUG_MODE') && DEBUG_MODE) {
    file_put_contents('vidzee_proxy_log.txt', date('Y-m-d H:i:s') . " - Original URL: $url, Direct URL: $direct_url\n", FILE_APPEND);
}

// Stream the content from the direct URL
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $direct_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, false);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($ch, CURLOPT_WRITEFUNCTION, function(/** @noinspection PhpUnusedParameterInspection */ $unused, $data) {
    echo $data;
    return strlen($data);
});

// Execute and close
curl_exec($ch);
curl_close($ch);
exit;
?>
