<?php
header('Content-Type: application/json');

// Include configuration and functions
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(403);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

try {
    $response = [
        'unread_count' => 0,
        'recent' => []
    ];

    // Get recent notifications (simulated data for now)
    $notifications = [];

    // Check for pending payments
    $pending_payments_query = "SELECT COUNT(*) as count FROM payments WHERE status = 'pending'";
    $pending_result = mysqli_query($conn, $pending_payments_query);
    if ($pending_result && $row = mysqli_fetch_assoc($pending_result)) {
        if ($row['count'] > 0) {
            $notifications[] = [
                'icon' => 'fas fa-credit-card',
                'color' => 'text-warning',
                'title' => $row['count'] . ' টি পেন্ডিং পেমেন্ট',
                'time_ago' => 'এখনই',
                'url' => 'payments.php?status=pending'
            ];
        }
    }

    // Check for new users today
    $new_users_query = "SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE() AND role = 'user'";
    $new_users_result = mysqli_query($conn, $new_users_query);
    if ($new_users_result && $row = mysqli_fetch_assoc($new_users_result)) {
        if ($row['count'] > 0) {
            $notifications[] = [
                'icon' => 'fas fa-user-plus',
                'color' => 'text-success',
                'title' => 'আজ ' . $row['count'] . ' জন নতুন ইউজার',
                'time_ago' => 'আজ',
                'url' => 'users.php?filter=today'
            ];
        }
    }

    // Check for new reviews
    $new_reviews_query = "SELECT COUNT(*) as count FROM reviews WHERE DATE(created_at) = CURDATE()";
    $new_reviews_result = mysqli_query($conn, $new_reviews_query);
    if ($new_reviews_result && $row = mysqli_fetch_assoc($new_reviews_result)) {
        if ($row['count'] > 0) {
            $notifications[] = [
                'icon' => 'fas fa-star',
                'color' => 'text-info',
                'title' => 'আজ ' . $row['count'] . ' টি নতুন রিভিউ',
                'time_ago' => 'আজ',
                'url' => 'reviews.php?filter=today'
            ];
        }
    }

    // Check for expiring premium users
    $expiring_premium_query = "SELECT COUNT(*) as count FROM users 
                              WHERE is_premium = 1 
                              AND premium_expires <= DATE_ADD(NOW(), INTERVAL 3 DAY)
                              AND premium_expires > NOW()";
    $expiring_result = mysqli_query($conn, $expiring_premium_query);
    if ($expiring_result && $row = mysqli_fetch_assoc($expiring_result)) {
        if ($row['count'] > 0) {
            $notifications[] = [
                'icon' => 'fas fa-crown',
                'color' => 'text-warning',
                'title' => $row['count'] . ' জন প্রিমিয়াম ইউজারের মেয়াদ শেষ হচ্ছে',
                'time_ago' => '৩ দিনের মধ্যে',
                'url' => 'manage_premium.php?filter=expiring'
            ];
        }
    }

    // Check for low storage space (simulated)
    $storage_usage = rand(70, 95); // Simulate storage usage percentage
    if ($storage_usage > 85) {
        $notifications[] = [
            'icon' => 'fas fa-hdd',
            'color' => 'text-danger',
            'title' => 'স্টোরেজ স্পেস কম (' . $storage_usage . '%)',
            'time_ago' => 'এখনই',
            'url' => 'site_settings.php#storage'
        ];
    }

    // Check for failed backup (simulated)
    $last_backup = rand(1, 10);
    if ($last_backup > 7) {
        $notifications[] = [
            'icon' => 'fas fa-database',
            'color' => 'text-danger',
            'title' => 'ব্যাকআপ ব্যর্থ হয়েছে',
            'time_ago' => $last_backup . ' দিন আগে',
            'url' => 'backup.php'
        ];
    }

    // Sort notifications by priority (errors first, then warnings, then info)
    usort($notifications, function($a, $b) {
        $priority = [
            'text-danger' => 1,
            'text-warning' => 2,
            'text-info' => 3,
            'text-success' => 4,
            'text-primary' => 5
        ];
        
        return ($priority[$a['color']] ?? 5) - ($priority[$b['color']] ?? 5);
    });

    $response['unread_count'] = count($notifications);
    $response['recent'] = array_slice($notifications, 0, 5); // Show only first 5

    echo json_encode($response);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error: ' . $e->getMessage()]);
}
?>
