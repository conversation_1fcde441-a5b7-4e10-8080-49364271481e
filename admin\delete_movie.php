<?php
require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Check if movie ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('movies.php');
}

$movie_id = (int)$_GET['id'];

// Check if movie exists
$check_query = "SELECT id, title, poster, banner FROM movies WHERE id = $movie_id";
$check_result = mysqli_query($conn, $check_query);

if (mysqli_num_rows($check_result) == 0) {
    redirect('movies.php');
}

$movie = mysqli_fetch_assoc($check_result);

// Begin transaction
mysqli_begin_transaction($conn);

try {
    // Delete streaming links
    $delete_streaming_links = "DELETE FROM streaming_links WHERE content_type = 'movie' AND content_id = $movie_id";
    mysqli_query($conn, $delete_streaming_links);
    
    // Delete download links
    $delete_download_links = "DELETE FROM download_links WHERE content_type = 'movie' AND content_id = $movie_id";
    mysqli_query($conn, $delete_download_links);
    
    // Delete reviews
    $delete_reviews = "DELETE FROM reviews WHERE content_type = 'movie' AND content_id = $movie_id";
    mysqli_query($conn, $delete_reviews);
    
    // Delete from watchlist
    $delete_watchlist = "DELETE FROM watchlist WHERE content_type = 'movie' AND content_id = $movie_id";
    mysqli_query($conn, $delete_watchlist);
    
    // Delete movie genres if the table exists
    $check_movie_genres_table = "SHOW TABLES LIKE 'movie_genres'";
    $movie_genres_table_exists = mysqli_query($conn, $check_movie_genres_table);
    if (mysqli_num_rows($movie_genres_table_exists) > 0) {
        $delete_movie_genres = "DELETE FROM movie_genres WHERE movie_id = $movie_id";
        mysqli_query($conn, $delete_movie_genres);
    }
    
    // Delete movie images if they exist
    $upload_dir = '../uploads/';
    
    // Delete poster if it's a local file
    if (!empty($movie['poster']) && strpos($movie['poster'], 'http') !== 0) {
        $poster_path = $upload_dir . $movie['poster'];
        if (file_exists($poster_path)) {
            unlink($poster_path);
        }
    }
    
    // Delete banner if it's a local file
    if (!empty($movie['banner']) && strpos($movie['banner'], 'http') !== 0) {
        $banner_path = $upload_dir . $movie['banner'];
        if (file_exists($banner_path)) {
            unlink($banner_path);
        }
    }
    
    // Finally, delete the movie
    $delete_movie = "DELETE FROM movies WHERE id = $movie_id";
    mysqli_query($conn, $delete_movie);
    
    // Commit transaction
    mysqli_commit($conn);
    
    // Log the action if activity_logs table exists
    $check_logs_table = "SHOW TABLES LIKE 'activity_logs'";
    $logs_table_exists = mysqli_query($conn, $check_logs_table);
    if (mysqli_num_rows($logs_table_exists) > 0) {
        $user_id = $_SESSION['user_id'];
        $description = "Deleted movie '{$movie['title']}' (ID: $movie_id)";
        $ip_address = $_SERVER['REMOTE_ADDR'];
        $user_agent = $_SERVER['HTTP_USER_AGENT'];
        
        $log_query = "INSERT INTO activity_logs (user_id, activity_type, description, ip_address, user_agent) 
                     VALUES ($user_id, 'delete', '$description', '$ip_address', '$user_agent')";
        mysqli_query($conn, $log_query);
    }
    
    // Redirect with success message
    $_SESSION['success_message'] = 'Movie deleted successfully.';
    redirect('movies.php');
} catch (Exception $e) {
    // Rollback transaction on error
    mysqli_rollback($conn);
    
    // Redirect with error message
    $_SESSION['error_message'] = 'Error deleting movie: ' . $e->getMessage();
    redirect('movies.php');
}
?>
