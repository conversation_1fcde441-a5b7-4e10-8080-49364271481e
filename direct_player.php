<?php
// Include config file to get site URL and other settings
require_once 'includes/config.php';

// Get video URL from query parameter
$video_url = isset($_GET['url']) ? $_GET['url'] : '';
$video_title = isset($_GET['title']) ? $_GET['title'] : 'Video Player';
$poster = isset($_GET['poster']) ? $_GET['poster'] : '';
$quality = isset($_GET['quality']) ? $_GET['quality'] : '';
$language = isset($_GET['language']) ? $_GET['language'] : '';
$server = isset($_GET['server']) ? $_GET['server'] : '';
$is_premium = isset($_GET['premium']) && $_GET['premium'] == '1';
$subtitle_url_bn = isset($_GET['subtitle_bn']) ? $_GET['subtitle_bn'] : '';
$subtitle_url_en = isset($_GET['subtitle_en']) ? $_GET['subtitle_en'] : '';

// Generate a unique cache key for this video
$cache_key = md5($video_url);

// Validate URL
if (empty($video_url)) {
    header('Location: ' . SITE_URL);
    exit;
}
?>
<?php
// Include header
require_once 'includes/header.php';
?>

<style>
        /* Custom player styles */
        /* Player container styles */
        .player-wrapper {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .player-container {
            background-color: #000;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            margin-bottom: 20px;
        }
        .video-container {
            position: relative;
            width: 100%;
            border-radius: 8px 8px 0 0;
            overflow: hidden;
        }
        .video-info {
            padding: 20px;
            background-color: #222;
            border-radius: 0 0 8px 8px;
        }
        h1 {
            font-size: 24px;
            margin-bottom: 15px;
        }
        .btn-back, .btn-outline-light.btn-sm {
            margin-bottom: 20px;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .btn-outline-light.btn-sm:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            transform: translateX(-5px);
        }
        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        .action-btn {
            background-color: var(--primary-color);
            color: #fff;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
        }
        .action-btn:hover {
            background-color: #f40612;
            color: #fff;
        }
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 10;
        }
        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #e50914;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .loading-text {
            margin-top: 15px;
            font-size: 18px;
            color: #fff;
            font-weight: bold;
        }
        .loading-subtext {
            margin-top: 5px;
            font-size: 14px;
            color: #ccc;
        }

        /* Buffering indicator styles */
        .buffering-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            display: none;
            z-index: 9;
            text-align: center;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
        }

        .buffering-spinner {
            width: 30px;
            height: 30px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #e50914;
            animation: spin 1s ease-in-out infinite;
            margin: 0 auto 10px;
        }

        /* Subtitle notification styles */
        .subtitle-notification {
            position: absolute;
            bottom: 70px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            z-index: 20;
            font-size: 14px;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }
        .video-player-wrapper {
            position: relative;
            width: 100%;
            background-color: #000;
            aspect-ratio: 16/9;
            overflow: hidden;
        }

        /* Center Controls Styles */
        .center-controls {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            align-items: center;
            gap: 30px;
            z-index: 10;
            opacity: 0; /* Hidden by default */
            transition: opacity 0.3s;
        }

        /* Show center controls when hovering over the video player */
        .video-player-wrapper:hover .center-controls {
            opacity: 1;
        }

        /* Show center controls when video is paused */
        .video-player-wrapper.paused .center-controls {
            opacity: 1;
        }

        .center-control-btn {
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 22px;
            transition: background-color 0.2s, transform 0.2s;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
        }

        .center-control-btn:hover {
            background-color: rgba(229, 9, 20, 0.8);
            transform: scale(1.1);
        }

        .main-play-btn {
            width: 90px;
            height: 90px;
            background-color: rgba(229, 9, 20, 0.8);
            font-size: 32px;
        }

        .main-play-btn:hover {
            background-color: rgba(229, 9, 20, 1);
        }
        #video-player {
            width: 100%;
            height: 100%;
            display: block;
            background-color: #000;
            object-fit: contain;
        }
        .custom-controls {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
            opacity: 0;
            transition: opacity 0.3s;
            z-index: 5;
        }
        .video-player-wrapper:hover .custom-controls {
            opacity: 1;
        }
        .control-btn {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 16px;
            padding: 8px;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
        }
        .control-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
        #play-pause-btn {
            background-color: rgba(229, 9, 20, 0.8);
            width: 45px;
            height: 45px;
        }
        #play-pause-btn:hover {
            background-color: rgba(229, 9, 20, 1);
        }
        .progress-container {
            flex-grow: 1;
            height: 5px;
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            cursor: pointer;
            position: relative;
            margin: 0 10px;
        }
        .progress-container:hover {
            height: 8px;
        }
        .progress-bar {
            height: 100%;
            background-color: #e50914;
            border-radius: 5px;
            width: 0;
            position: relative;
        }
        .progress-bar::after {
            content: '';
            position: absolute;
            right: -6px;
            top: 50%;
            transform: translateY(-50%);
            width: 12px;
            height: 12px;
            background-color: #e50914;
            border-radius: 50%;
            opacity: 0;
            transition: opacity 0.2s;
        }
        .progress-container:hover .progress-bar::after {
            opacity: 1;
        }
        .time-display {
            font-size: 14px;
            color: white;
            min-width: 100px;
            text-align: center;
        }
        /* Volume control styles */
        .volume-control {
            margin: 0 5px;
            display: flex;
            align-items: center;
            width: 100px;
        }
        .volume-icon {
            margin-right: 8px;
            font-size: 16px;
            cursor: pointer;
        }
        .volume-slider {
            -webkit-appearance: none;
            appearance: none;
            width: 100%;
            height: 5px;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.3);
            outline: none;
            transition: background 0.2s;
        }
        .volume-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #e50914;
            cursor: pointer;
        }
        .volume-slider::-moz-range-thumb {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #e50914;
            cursor: pointer;
            border: none;
        }
        .volume-slider:hover {
            background: rgba(255, 255, 255, 0.4);
        }
        /* Show controls on mobile even without hover */
        @media (max-width: 768px) {
            .custom-controls {
                opacity: 1;
                padding: 8px;
            }
            .control-btn {
                width: 36px;
                height: 36px;
                font-size: 14px;
            }
            #play-pause-btn-bottom {
                width: 40px;
                height: 40px;
            }
            .time-display {
                min-width: 80px;
                font-size: 12px;
            }
            .volume-control {
                width: 80px;
            }
            .volume-icon {
                font-size: 14px;
            }

            /* Center controls for mobile */
            .center-controls {
                gap: 15px;
            }
            .center-control-btn {
                width: 45px;
                height: 45px;
                font-size: 18px;
            }
            .main-play-btn {
                width: 70px;
                height: 70px;
                font-size: 24px;
            }
        }
        .resume-notification {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            display: none;
            z-index: 30;
            transition: opacity 0.3s ease;
            text-align: center;
            max-width: 90%;
            width: 400px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
        }
        .resume-notification .resume-buttons {
            margin-top: 15px;
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        .resume-notification button {
            background-color: #e50914;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.2s ease;
            min-width: 120px;
        }
        .resume-notification button:hover {
            transform: scale(1.05);
            background-color: #f40612;
        }
        .resume-notification button.resume-cancel {
            background-color: #333;
        }
        .resume-notification button.resume-cancel:hover {
            background-color: #444;
        }
        /* Mobile styles for resume notification */
        @media (max-width: 768px) {
            .resume-notification {
                width: 85%;
                padding: 15px;
            }
            .resume-notification button {
                padding: 8px 15px;
                min-width: 100px;
                font-size: 14px;
            }

            .video-container {
                border-radius: 0;
            }
            .player-container {
                border-radius: 0;
                margin-left: -20px;
                margin-right: -20px;
                width: calc(100% + 40px);
            }
            .container {
                padding: 10px;
            }
            .btn-back {
                margin-bottom: 10px;
            }
            h1 {
                font-size: 18px;
            }
            .action-buttons {
                flex-direction: column;
            }
            .action-btn {
                width: 100%;
                justify-content: center;
            }
        }
        /* Landscape mode for mobile */
        @media (max-height: 500px) and (orientation: landscape) {
            .container {
                padding: 0;
                max-width: 100%;
            }
            .btn-back, .btn-outline-light.btn-sm {
                position: absolute;
                top: 10px;
                left: 10px;
                z-index: 100;
                background-color: rgba(0, 0, 0, 0.7);
                color: white;
                border-color: rgba(255, 255, 255, 0.3);
            }
            .video-info {
                padding: 5px;
                max-height: 100px;
                overflow-y: auto;
            }
            .video-info h1 {
                font-size: 1rem;
                margin-bottom: 5px;
            }
            .player-container {
                margin: 0;
                border-radius: 0;
            }
            .video-container {
                border-radius: 0;
            }
            body {
                overflow: hidden;
            }
            /* Optimize controls for landscape */
            .custom-controls {
                padding: 5px;
            }
            /* Make sure video takes full width in landscape */
            .video-player-wrapper {
                width: 100vw;
                height: 100vh;
            }
        }
    </style>

<div class="container mt-5 pt-4">
    <div class="row">
        <div class="col-12 mb-3">
            <a href="<?php echo isset($_SERVER['HTTP_REFERER']) ? htmlspecialchars($_SERVER['HTTP_REFERER']) : SITE_URL; ?>" onclick="return goBack();" class="btn btn-outline-light btn-sm">
                <i class="fas fa-arrow-left"></i> আগের পেজে ফিরুন
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="player-container">
                <div class="video-container">
                <!-- Loading Overlay -->
                <div id="loading-overlay" class="loading-overlay">
                    <div class="spinner"></div>
                    <div class="loading-text">ভিডিও লোড হচ্ছে...</div>
                    <div class="loading-subtext">কিছুক্ষণ অপেক্ষা করুন</div>
                </div>

                <!-- Resume Notification -->
                <div id="resume-notification" class="resume-notification">
                    <div>আপনি কি আগের দেখা স্থান থেকে চালিয়ে যেতে চান?</div>
                    <div class="resume-buttons">
                        <button id="resume-yes">হ্যাঁ, চালিয়ে যান</button>
                        <button id="resume-cancel" class="resume-cancel">না, শুরু থেকে দেখুন</button>
                    </div>
                </div>

                <!-- Buffering Indicator -->
                <div id="buffering-indicator" class="buffering-indicator">
                    <div class="buffering-spinner"></div>
                    <div>বাফারিং হচ্ছে...</div>
                </div>

                <!-- Subtitle Notification -->
                <div id="subtitle-notification" class="subtitle-notification" style="display: none;">
                    <div id="subtitle-notification-text">সাবটাইটেল লোড হচ্ছে...</div>
                </div>

                <!-- Video Player -->
                <div class="video-player-wrapper">
                    <video id="video-player" preload="auto" poster="<?php echo $poster; ?>" controlsList="nodownload">
                        <source src="<?php echo $video_url; ?>" type="video/mp4">
                        <?php if (!empty($subtitle_url_bn)): ?>
                        <track kind="subtitles" src="<?php echo SITE_URL; ?>/subtitle_converter.php?url=<?php echo urlencode($subtitle_url_bn); ?>&t=<?php echo time(); ?>" srclang="bn" label="Bangla" default>
                        <?php endif; ?>
                        <?php if (!empty($subtitle_url_en)): ?>
                        <track kind="subtitles" src="<?php echo SITE_URL; ?>/subtitle_converter.php?url=<?php echo urlencode($subtitle_url_en); ?>&t=<?php echo time(); ?>" srclang="en" label="English" <?php echo empty($subtitle_url_bn) ? 'default' : ''; ?>>
                        <?php endif; ?>
                        Your browser does not support the video tag.
                    </video>

                    <!-- Center Play Controls -->
                    <div class="center-controls">
                        <button id="rewind-btn" class="center-control-btn" title="10 সেকেন্ড পিছনে">
                            <i class="fas fa-backward"></i> 10s
                        </button>
                        <button id="play-pause-btn" class="center-control-btn main-play-btn" title="প্লে/পজ">
                            <i class="fas fa-play"></i>
                        </button>
                        <button id="forward-btn" class="center-control-btn" title="10 সেকেন্ড সামনে">
                            <i class="fas fa-forward"></i> 10s
                        </button>
                    </div>

                    <!-- Bottom Custom Controls -->
                    <div class="custom-controls">
                        <button id="rewind-btn-bottom" class="control-btn" title="10 সেকেন্ড পিছনে">
                            <i class="fas fa-backward"></i> 10s
                        </button>
                        <button id="play-pause-btn-bottom" class="control-btn" title="প্লে/পজ">
                            <i class="fas fa-play"></i>
                        </button>
                        <button id="forward-btn-bottom" class="control-btn" title="10 সেকেন্ড সামনে">
                            <i class="fas fa-forward"></i> 10s
                        </button>
                        <div class="progress-container">
                            <div class="progress-bar" id="progress-bar"></div>
                        </div>
                        <div class="time-display">
                            <span id="current-time">0:00</span> / <span id="duration">0:00</span>
                        </div>
                        <div class="volume-control">
                            <div class="volume-icon" id="volume-icon" title="ভলিউম">
                                <i class="fas fa-volume-up"></i>
                            </div>
                            <input type="range" class="volume-slider" id="volume-slider" min="0" max="1" step="0.1" value="1" title="ভলিউম স্লাইডার">
                        </div>
                        <?php if (!empty($subtitle_url_bn) || !empty($subtitle_url_en)): ?>
                        <button id="subtitle-btn" class="control-btn" title="সাবটাইটেল">
                            <i class="fas fa-closed-captioning"></i>
                        </button>
                        <?php endif; ?>
                        <button id="fullscreen-btn" class="control-btn" title="ফুলস্ক্রিন">
                            <i class="fas fa-expand"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="video-info">
                <h1><?php echo $video_title; ?></h1>
                <div class="d-flex flex-wrap gap-2 mb-3">
                    <?php if (!empty($quality)): ?>
                    <span class="badge bg-danger"><?php echo $quality; ?></span>
                    <?php endif; ?>

                    <?php if (!empty($language)): ?>
                    <span class="badge bg-secondary"><?php echo $language; ?></span>
                    <?php endif; ?>

                    <?php if (!empty($server)): ?>
                    <span class="badge bg-info"><?php echo $server; ?></span>
                    <?php endif; ?>

                    <?php if ($is_premium): ?>
                    <span class="badge bg-warning text-dark"><i class="fas fa-crown me-1"></i>Premium</span>
                    <?php endif; ?>
                </div>

                <div class="action-buttons">
                    <button id="restart-player" class="action-btn">
                        <i class="fas fa-redo"></i> প্লেয়ার রিস্টার্ট করুন
                    </button>
                    <?php if (!empty($subtitle_url_bn) || !empty($subtitle_url_en)): ?>
                    <button id="reload-subtitles" class="action-btn">
                        <i class="fas fa-closed-captioning"></i> সাবটাইটেল রিলোড করুন
                    </button>
                    <?php endif; ?>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>প্লেব্যাক টিপস:</strong>
                    <ul class="mb-0 mt-2">
                        <li>কিবোর্ড শর্টকাট: J (পিছনে যান), K (প্লে/পজ), L (সামনে যান)</li>
                        <li>ভলিউম কন্ট্রোল: M (মিউট/অনমিউট), আপ/ডাউন অ্যারো (ভলিউম বাড়ান/কমান)</li>
                        <li>যদি ভিডিও স্টাটার করে, কিছুক্ষণ পজ করে বাফারিং হতে দিন</li>
                        <li>স্মুথ প্লেব্যাকের জন্য ভিডিও লোড হওয়ার পর প্লে করুন</li>
                        <li>মোবাইলে ভালো প্লেব্যাকের জন্য ওয়াইফাই ব্যবহার করুন</li>
                    </ul>
                </div>

                <?php if (!empty($subtitle_url_bn) || !empty($subtitle_url_en)): ?>
                <div class="alert alert-success mt-3">
                    <i class="fas fa-closed-captioning me-2"></i>
                    <strong>সাবটাইটেল তথ্য:</strong>
                    <div class="mt-2">
                        <?php if (!empty($subtitle_url_bn)): ?>
                        <span class="badge bg-success me-2">বাংলা সাবটাইটেল উপলব্ধ</span>
                        <?php endif; ?>
                        <?php if (!empty($subtitle_url_en)): ?>
                        <span class="badge bg-info me-2">ইংরেজি সাবটাইটেল উপলব্ধ</span>
                        <?php endif; ?>
                        <p class="mt-2 mb-0">সাবটাইটেল চালু করতে <i class="fas fa-closed-captioning"></i> বাটন ক্লিক করুন অথবা কিবোর্ডে C চাপুন।</p>
                    </div>
                </div>
                <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>



    <script>
        // Function to go back to previous page
        function goBack() {
            if (document.referrer) {
                // If there's a referrer, go to it
                window.location.href = document.referrer;
            } else {
                // Otherwise go to home page
                window.location.href = '<?php echo SITE_URL; ?>';
            }
            return false; // Prevent default link behavior
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Add event listener to back button
            const backButton = document.querySelector('.btn-outline-light.btn-sm');
            if (backButton) {
                backButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    goBack();
                });
            }

            // Get elements
            const videoElement = document.getElementById('video-player');
            const loadingOverlay = document.getElementById('loading-overlay');
            const resumeNotification = document.getElementById('resume-notification');
            const resumeYesBtn = document.getElementById('resume-yes');
            const resumeCancelBtn = document.getElementById('resume-cancel');
            const restartPlayerBtn = document.getElementById('restart-player');
            const bufferingIndicator = document.getElementById('buffering-indicator');

            // Video URL and cache key
            const videoUrl = '<?php echo $video_url; ?>';
            const cacheKey = '<?php echo $cache_key; ?>';

            // Check for saved playback position
            const savedTime = localStorage.getItem('video_time_' + cacheKey);
            if (savedTime && parseFloat(savedTime) > 10) {
                // Show resume notification with a slight delay to ensure it's visible
                setTimeout(() => {
                    resumeNotification.style.display = 'block';
                    // Ensure controls are visible when resume notification is shown
                    showControls();
                }, 500);
            }

            // Resume playback with improved transition
            resumeYesBtn.addEventListener('click', function() {
                const savedTime = localStorage.getItem('video_time_' + cacheKey);
                if (savedTime) {
                    videoElement.currentTime = parseFloat(savedTime);
                }
                // Add fade-out effect
                resumeNotification.style.opacity = '0';
                setTimeout(() => {
                    resumeNotification.style.display = 'none';
                    resumeNotification.style.opacity = '1';
                }, 300);

                // Play video after notification is hidden
                videoElement.play()
                    .then(() => {
                        console.log('Resumed playback from saved position');
                    })
                    .catch(err => {
                        console.error('Error resuming playback:', err);
                    });
            });

            // Start over with improved transition
            resumeCancelBtn.addEventListener('click', function() {
                videoElement.currentTime = 0;
                // Add fade-out effect
                resumeNotification.style.opacity = '0';
                setTimeout(() => {
                    resumeNotification.style.display = 'none';
                    resumeNotification.style.opacity = '1';
                }, 300);

                // Play video after notification is hidden
                videoElement.play()
                    .then(() => {
                        console.log('Started playback from beginning');
                    })
                    .catch(err => {
                        console.error('Error starting playback:', err);
                    });
            });

            // Restart player with improved loading
            restartPlayerBtn.addEventListener('click', function() {
                // Show loading overlay during restart
                loadingOverlay.style.display = 'flex';

                // Force reload with cache-busting parameter
                const cacheBuster = '?cb=' + new Date().getTime();
                const urlWithCacheBuster = videoUrl.includes('?') ?
                    videoUrl + '&cb=' + new Date().getTime() :
                    videoUrl + cacheBuster;

                // Reset video element
                videoElement.pause();
                videoElement.src = urlWithCacheBuster;
                videoElement.load();

                // Clear any buffering state
                bufferingDetected = false;
                bufferingIndicator.style.display = 'none';

                // Play after a short delay to allow initial buffering
                setTimeout(function() {
                    videoElement.play()
                        .then(() => {
                            console.log('Video playback started after restart');
                        })
                        .catch(err => {
                            console.error('Error playing video after restart:', err);
                        });
                }, 1000);
            });

            // Add event listener for subtitle reload button
            const reloadSubtitlesBtn = document.getElementById('reload-subtitles');
            if (reloadSubtitlesBtn) {
                reloadSubtitlesBtn.addEventListener('click', function() {
                    reloadSubtitles();
                });
            }



            // Variables for buffering detection
            let lastPlayPos = 0;
            let currentPlayPos = 0;
            let bufferingDetected = false;

            // Function to check if video is buffering
            function checkBuffering() {
                currentPlayPos = videoElement.currentTime;

                // Check if video is buffering (current time not advancing despite being in play state)
                const offset = 0.1; // Allow for small fluctuations in time reporting
                if (!videoElement.paused && currentPlayPos < (lastPlayPos + offset) && !bufferingDetected) {
                    bufferingIndicator.style.display = 'block';
                    bufferingDetected = true;
                }

                // Check if video has resumed playing after buffering
                if (bufferingDetected && currentPlayPos > (lastPlayPos + offset) && !videoElement.paused) {
                    bufferingIndicator.style.display = 'none';
                    bufferingDetected = false;
                }

                lastPlayPos = currentPlayPos;
            }

            // Check for buffering every 300ms while video is playing
            setInterval(checkBuffering, 300);

            // Hide loading overlay when video starts loading
            videoElement.addEventListener('loadeddata', function() {
                loadingOverlay.style.display = 'none';
                // Initialize controls state
                if (videoElement.paused) {
                    videoPlayerWrapper.classList.add('paused');
                } else {
                    videoPlayerWrapper.classList.remove('paused');
                    hideControlsWithDelay();
                }

                // Set video buffer size (helps with smoother playback)
                try {
                    // Attempt to increase buffer size for smoother playback
                    if (videoElement.buffered.length > 0) {
                        console.log('Initial buffer: ' + videoElement.buffered.end(0) + ' seconds');
                    }
                } catch (e) {
                    console.error('Buffer info error:', e);
                }
            });

            // Also hide loading overlay when video plays
            videoElement.addEventListener('playing', function() {
                loadingOverlay.style.display = 'none';
                bufferingIndicator.style.display = 'none';
                bufferingDetected = false;
                videoPlayerWrapper.classList.remove('paused');
                hideControlsWithDelay();
            });

            // Hide loading overlay when video can play through
            videoElement.addEventListener('canplay', function() {
                loadingOverlay.style.display = 'none';
            });

            // Show buffering indicator when waiting for data
            videoElement.addEventListener('waiting', function() {
                bufferingIndicator.style.display = 'block';
                bufferingDetected = true;
            });

            // Hide buffering indicator when enough data is available
            videoElement.addEventListener('canplaythrough', function() {
                bufferingIndicator.style.display = 'none';
                bufferingDetected = false;
            });

            // Hide loading overlay after 3 seconds as a fallback
            setTimeout(function() {
                loadingOverlay.style.display = 'none';
            }, 3000);

            // Add click event to loading overlay to hide it manually
            loadingOverlay.addEventListener('click', function() {
                loadingOverlay.style.display = 'none';
            });

            // Save playback position periodically
            setInterval(() => {
                if (videoElement.currentTime > 0) {
                    localStorage.setItem('video_time_' + cacheKey, videoElement.currentTime.toString());
                }
            }, 3000);

            // Save position on pause
            videoElement.addEventListener('pause', function() {
                if (videoElement.currentTime > 0) {
                    localStorage.setItem('video_time_' + cacheKey, videoElement.currentTime.toString());
                }
            });

            // Handle errors
            videoElement.addEventListener('error', function() {
                console.error('Video Error:', videoElement.error);
                loadingOverlay.style.display = 'none';

                // Update alert message
                const alertElement = document.querySelector('.alert-info');
                if (alertElement) {
                    alertElement.classList.remove('alert-info');
                    alertElement.classList.add('alert-danger');
                    alertElement.innerHTML = '<strong>ত্রুটি:</strong> ভিডিও লোড করতে ব্যর্থ হয়েছে। অনুগ্রহ করে ফাইলটি ডাউনলোড করে লোকালি প্লে করুন।';
                }
            });

            // Get custom control elements
            const playPauseBtn = document.getElementById('play-pause-btn');
            const rewindBtn = document.getElementById('rewind-btn');
            const forwardBtn = document.getElementById('forward-btn');

            // Get bottom control elements
            const playPauseBtnBottom = document.getElementById('play-pause-btn-bottom');
            const rewindBtnBottom = document.getElementById('rewind-btn-bottom');
            const forwardBtnBottom = document.getElementById('forward-btn-bottom');

            const progressContainer = document.querySelector('.progress-container');
            const progressBar = document.getElementById('progress-bar');
            const currentTimeDisplay = document.getElementById('current-time');
            const durationDisplay = document.getElementById('duration');
            const fullscreenBtn = document.getElementById('fullscreen-btn');

            // Volume control elements
            const volumeIcon = document.getElementById('volume-icon');
            const volumeSlider = document.getElementById('volume-slider');

            // Format time in MM:SS format
            function formatTime(seconds) {
                const minutes = Math.floor(seconds / 60);
                seconds = Math.floor(seconds % 60);
                return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
            }

            // Update progress bar and time display
            function updateProgress() {
                if (videoElement.duration) {
                    const percent = (videoElement.currentTime / videoElement.duration) * 100;
                    progressBar.style.width = `${percent}%`;
                    currentTimeDisplay.textContent = formatTime(videoElement.currentTime);
                    durationDisplay.textContent = formatTime(videoElement.duration);
                }
            }

            // Play/Pause toggle
            function togglePlayPause() {
                if (videoElement.paused) {
                    videoElement.play();
                    playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
                    playPauseBtnBottom.innerHTML = '<i class="fas fa-pause"></i>';
                } else {
                    videoElement.pause();
                    playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
                    playPauseBtnBottom.innerHTML = '<i class="fas fa-play"></i>';
                }
            }

            // Seek backward 10 seconds
            function rewind10() {
                videoElement.currentTime = Math.max(0, videoElement.currentTime - 10);
            }

            // Seek forward 10 seconds
            function forward10() {
                videoElement.currentTime = Math.min(videoElement.duration, videoElement.currentTime + 10);
            }

            // Toggle fullscreen
            function toggleFullscreen() {
                if (!document.fullscreenElement) {
                    if (videoElement.requestFullscreen) {
                        videoElement.requestFullscreen();
                    } else if (videoElement.webkitRequestFullscreen) {
                        videoElement.webkitRequestFullscreen();
                    } else if (videoElement.msRequestFullscreen) {
                        videoElement.msRequestFullscreen();
                    }
                    fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
                } else {
                    if (document.exitFullscreen) {
                        document.exitFullscreen();
                    } else if (document.webkitExitFullscreen) {
                        document.webkitExitFullscreen();
                    } else if (document.msExitFullscreen) {
                        document.msExitFullscreen();
                    }
                    fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
                }
            }

            // Function to show subtitle notification
            function showSubtitleNotification(message, duration = 3000) {
                const notification = document.getElementById('subtitle-notification');
                const notificationText = document.getElementById('subtitle-notification-text');

                if (!notification || !notificationText) return;

                notificationText.textContent = message;
                notification.style.display = 'block';

                // Fade in
                setTimeout(() => {
                    notification.style.opacity = '1';
                }, 10);

                // Fade out after duration
                setTimeout(() => {
                    notification.style.opacity = '0';
                    setTimeout(() => {
                        notification.style.display = 'none';
                    }, 300);
                }, duration);
            }

            // Function to check subtitle track status
            function checkSubtitleTracks() {
                const tracks = videoElement.textTracks;
                if (tracks.length === 0) {
                    console.log("No text tracks found");
                    return;
                }

                console.log("Text tracks found:", tracks.length);
                let hasCues = false;

                for (let i = 0; i < tracks.length; i++) {
                    console.log(`Track ${i}: language=${tracks[i].language}, label=${tracks[i].label}, mode=${tracks[i].mode}`);

                    // Check if track has cues
                    if (tracks[i].cues) {
                        console.log(`  Track ${i} has ${tracks[i].cues.length} cues`);
                        if (tracks[i].cues.length > 0) {
                            hasCues = true;
                        }
                    } else {
                        console.log(`  Track ${i} has no cues or cues not loaded yet`);
                    }
                }

                // Show notification based on cue status
                if (hasCues) {
                    showSubtitleNotification("সাবটাইটেল লোড হয়েছে। CC বাটনে ক্লিক করে চালু করুন।");
                } else {
                    // Try again after a delay
                    setTimeout(checkSubtitleTracks, 2000);
                }
            }

            // Add event listeners for track loading
            videoElement.addEventListener('loadedmetadata', function() {
                const tracks = videoElement.textTracks;

                if (tracks.length > 0) {
                    // Show initial notification that subtitles are loading
                    showSubtitleNotification("সাবটাইটেল লোড হচ্ছে...", 5000);

                    for (let i = 0; i < tracks.length; i++) {
                        // Add event listener for cue changes
                        tracks[i].addEventListener('cuechange', function() {
                            console.log(`Cue changed for track ${i}`);
                        });

                        // Add error handling for tracks
                        const trackElement = videoElement.querySelector(`track[srclang="${tracks[i].language}"]`);
                        if (trackElement) {
                            trackElement.addEventListener('error', function(e) {
                                console.error(`Error loading track ${i}:`, e);
                                showSubtitleNotification(`সাবটাইটেল লোড করতে সমস্যা হয়েছে (${tracks[i].label || tracks[i].language})`);
                            });
                        }

                        // Force mode to be 'hidden' initially to ensure tracks load properly
                        if (i === 0) {
                            setTimeout(() => {
                                if (tracks[0].mode !== 'showing') {
                                    tracks[0].mode = 'showing';
                                    console.log("Forced first track to showing mode");
                                }
                            }, 1000);
                        }
                    }

                    // Check tracks after a delay to allow loading
                    setTimeout(checkSubtitleTracks, 2000);
                }
            });

            // Add manual reload function for subtitles
            function reloadSubtitles() {
                const tracks = videoElement.textTracks;
                if (tracks.length === 0) return;

                showSubtitleNotification("সাবটাইটেল পুনরায় লোড করা হচ্ছে...");

                // Reload each track by recreating the track elements
                const trackElements = videoElement.querySelectorAll('track');
                trackElements.forEach((track, index) => {
                    const src = track.src;
                    const srclang = track.srclang;
                    const label = track.label;
                    const isDefault = track.default;

                    // Remove old track
                    track.remove();

                    // Create new track with cache-busting parameter
                    const newTrack = document.createElement('track');
                    newTrack.kind = 'subtitles';
                    newTrack.src = src.includes('&t=') ?
                        src.replace(/&t=\d+/, '&t=' + new Date().getTime()) :
                        src + '&t=' + new Date().getTime();
                    newTrack.srclang = srclang;
                    newTrack.label = label;
                    newTrack.default = isDefault;

                    // Add new track to video
                    videoElement.appendChild(newTrack);
                });

                // Check tracks after reload
                setTimeout(checkSubtitleTracks, 2000);
            }

            // Toggle subtitles
            function toggleSubtitles() {
                const tracks = videoElement.textTracks;
                if (tracks.length === 0) {
                    console.log("No text tracks found");
                    showSubtitleNotification("কোন সাবটাইটেল পাওয়া যায়নি");
                    return;
                }

                console.log("Text tracks found:", tracks.length);
                for (let i = 0; i < tracks.length; i++) {
                    console.log(`Track ${i}: language=${tracks[i].language}, label=${tracks[i].label}, mode=${tracks[i].mode}`);
                }

                let activeTrackFound = false;
                let nextTrackIndex = 0;
                let currentTrackLabel = "";

                // First check if any track is showing
                for (let i = 0; i < tracks.length; i++) {
                    if (tracks[i].mode === 'showing') {
                        activeTrackFound = true;
                        currentTrackLabel = tracks[i].label || tracks[i].language;
                        nextTrackIndex = (i + 1) % tracks.length;
                        tracks[i].mode = 'disabled'; // Completely disable current track
                        break;
                    }
                }

                // If no track is active, enable the first one
                // If a track was active, enable the next one in sequence
                if (!activeTrackFound) {
                    tracks[0].mode = 'showing';
                    const label = tracks[0].label || tracks[0].language;
                    console.log("Enabled first subtitle track:", label);
                    showSubtitleNotification(`${label} সাবটাইটেল চালু করা হয়েছে`);
                } else if (nextTrackIndex === 0) {
                    // If we've gone through all tracks, disable all (turn off subtitles)
                    console.log("Disabled all subtitle tracks");
                    showSubtitleNotification("সাবটাইটেল বন্ধ করা হয়েছে");
                } else {
                    tracks[nextTrackIndex].mode = 'showing';
                    const label = tracks[nextTrackIndex].label || tracks[nextTrackIndex].language;
                    console.log(`Enabled next subtitle track: ${nextTrackIndex} (${label})`);
                    showSubtitleNotification(`${label} সাবটাইটেল চালু করা হয়েছে`);
                }

                // Check tracks after toggling
                setTimeout(checkSubtitleTracks, 500);
            }

            // Set up event listeners for custom controls
            playPauseBtn.addEventListener('click', togglePlayPause);
            rewindBtn.addEventListener('click', rewind10);
            forwardBtn.addEventListener('click', forward10);
            fullscreenBtn.addEventListener('click', toggleFullscreen);

            // Add subtitle toggle if button exists
            const subtitleBtn = document.getElementById('subtitle-btn');
            if (subtitleBtn) {
                subtitleBtn.addEventListener('click', toggleSubtitles);
            }

            // Set up event listeners for bottom controls
            playPauseBtnBottom.addEventListener('click', togglePlayPause);
            rewindBtnBottom.addEventListener('click', rewind10);
            forwardBtnBottom.addEventListener('click', forward10);

            // Volume control functions
            function updateVolumeIcon(volume) {
                // Update volume icon based on current volume level
                if (volume === 0) {
                    volumeIcon.innerHTML = '<i class="fas fa-volume-mute"></i>';
                } else if (volume < 0.5) {
                    volumeIcon.innerHTML = '<i class="fas fa-volume-down"></i>';
                } else {
                    volumeIcon.innerHTML = '<i class="fas fa-volume-up"></i>';
                }
            }

            // Set initial volume
            videoElement.volume = parseFloat(volumeSlider.value);
            updateVolumeIcon(videoElement.volume);

            // Change volume when slider is moved
            volumeSlider.addEventListener('input', function() {
                const volume = parseFloat(this.value);
                videoElement.volume = volume;
                updateVolumeIcon(volume);

                // Save volume preference
                localStorage.setItem('video_volume', volume);
            });

            // Toggle mute when volume icon is clicked
            volumeIcon.addEventListener('click', function() {
                if (videoElement.volume > 0) {
                    // Store current volume before muting
                    videoElement.dataset.previousVolume = videoElement.volume;
                    videoElement.volume = 0;
                    volumeSlider.value = 0;
                    updateVolumeIcon(0);
                } else {
                    // Restore previous volume
                    const previousVolume = videoElement.dataset.previousVolume || 1;
                    videoElement.volume = previousVolume;
                    volumeSlider.value = previousVolume;
                    updateVolumeIcon(previousVolume);
                }
            });

            // Load saved volume preference if available
            const savedVolume = localStorage.getItem('video_volume');
            if (savedVolume !== null) {
                videoElement.volume = parseFloat(savedVolume);
                volumeSlider.value = savedVolume;
                updateVolumeIcon(parseFloat(savedVolume));
            }

            // Update progress bar as video plays
            videoElement.addEventListener('timeupdate', updateProgress);

            // Control visibility timer
            let controlsTimeout;

            // Function to show controls
            function showControls() {
                const centerControls = document.querySelector('.center-controls');
                if (centerControls) {
                    centerControls.style.opacity = '1';
                }
            }

            // Function to hide controls after delay
            function hideControlsWithDelay() {
                clearTimeout(controlsTimeout);
                controlsTimeout = setTimeout(() => {
                    if (!videoElement.paused) {
                        const centerControls = document.querySelector('.center-controls');
                        if (centerControls) {
                            centerControls.style.opacity = '0';
                        }
                    }
                }, 2500); // Hide after 2.5 seconds
            }

            // Show controls on mouse movement and touch
            const videoPlayerWrapper = document.querySelector('.video-player-wrapper');

            // For mouse devices
            videoPlayerWrapper.addEventListener('mousemove', () => {
                showControls();
                hideControlsWithDelay();
            });

            // For touch devices
            videoPlayerWrapper.addEventListener('touchstart', () => {
                showControls();
                hideControlsWithDelay();
            });

            // Hide controls when leaving the video area
            videoPlayerWrapper.addEventListener('mouseleave', () => {
                if (!videoElement.paused) {
                    const centerControls = document.querySelector('.center-controls');
                    if (centerControls) {
                        centerControls.style.opacity = '0';
                    }
                }
            });

            // Update play/pause button when video state changes
            videoElement.addEventListener('play', function() {
                playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
                playPauseBtnBottom.innerHTML = '<i class="fas fa-pause"></i>';
                videoPlayerWrapper.classList.remove('paused');
                hideControlsWithDelay(); // Hide controls after delay when playing
            });

            videoElement.addEventListener('pause', function() {
                playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
                playPauseBtnBottom.innerHTML = '<i class="fas fa-play"></i>';
                videoPlayerWrapper.classList.add('paused');
                showControls(); // Always show controls when paused
            });

            // Set initial duration when metadata is loaded
            videoElement.addEventListener('loadedmetadata', function() {
                durationDisplay.textContent = formatTime(videoElement.duration);

                // Set initial controls state
                if (videoElement.paused) {
                    videoPlayerWrapper.classList.add('paused');
                    showControls();
                } else {
                    videoPlayerWrapper.classList.remove('paused');
                    hideControlsWithDelay();
                }
            });

            // Click on progress bar to seek
            progressContainer.addEventListener('click', function(e) {
                const rect = progressContainer.getBoundingClientRect();
                const pos = (e.clientX - rect.left) / rect.width;
                videoElement.currentTime = pos * videoElement.duration;
            });

            // Double click on video to toggle fullscreen
            videoElement.addEventListener('dblclick', toggleFullscreen);

            // Click on video to toggle play/pause
            videoElement.addEventListener('click', function() {
                togglePlayPause();
                // Controls visibility will be handled by the play/pause event listeners
            });

            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // J key - seek backward 10 seconds
                if (e.key === 'j' || e.key === 'J') {
                    rewind10();
                    showControls();
                    hideControlsWithDelay();
                }

                // L key - seek forward 10 seconds
                if (e.key === 'l' || e.key === 'L') {
                    forward10();
                    showControls();
                    hideControlsWithDelay();
                }

                // K key or Space - play/pause
                if (e.key === 'k' || e.key === 'K' || e.key === ' ') {
                    togglePlayPause();
                    e.preventDefault(); // Prevent page scrolling on space
                    // Controls visibility will be handled by the play/pause event listeners
                }

                // F key - fullscreen
                if (e.key === 'f' || e.key === 'F') {
                    toggleFullscreen();
                    showControls();
                    hideControlsWithDelay();
                }

                // C key - toggle subtitles
                if (e.key === 'c' || e.key === 'C') {
                    if (subtitleBtn) {
                        toggleSubtitles();
                        showControls();
                        hideControlsWithDelay();
                    }
                }

                // B key - toggle buffering indicator (for testing)
                if (e.key === 'b' || e.key === 'B') {
                    if (bufferingIndicator.style.display === 'block') {
                        bufferingIndicator.style.display = 'none';
                    } else {
                        bufferingIndicator.style.display = 'block';
                    }
                }

                // M key - toggle mute
                if (e.key === 'm' || e.key === 'M') {
                    if (videoElement.volume > 0) {
                        // Store current volume before muting
                        videoElement.dataset.previousVolume = videoElement.volume;
                        videoElement.volume = 0;
                        volumeSlider.value = 0;
                        updateVolumeIcon(0);
                    } else {
                        // Restore previous volume
                        const previousVolume = videoElement.dataset.previousVolume || 1;
                        videoElement.volume = previousVolume;
                        volumeSlider.value = previousVolume;
                        updateVolumeIcon(previousVolume);
                    }
                }

                // Up/Down arrow keys - adjust volume
                if (e.key === 'ArrowUp') {
                    e.preventDefault(); // Prevent page scrolling
                    const newVolume = Math.min(1, videoElement.volume + 0.1);
                    videoElement.volume = newVolume;
                    volumeSlider.value = newVolume;
                    updateVolumeIcon(newVolume);
                    localStorage.setItem('video_volume', newVolume);
                }

                if (e.key === 'ArrowDown') {
                    e.preventDefault(); // Prevent page scrolling
                    const newVolume = Math.max(0, videoElement.volume - 0.1);
                    videoElement.volume = newVolume;
                    volumeSlider.value = newVolume;
                    updateVolumeIcon(newVolume);
                    localStorage.setItem('video_volume', newVolume);
                }
            });

            // Optimize video playback
            function optimizeVideoPlayback() {
                // Try to prefetch video content
                if ('fetch' in window) {
                    try {
                        // Prefetch the video URL to improve initial loading
                        fetch(videoUrl, { method: 'HEAD' })
                            .then(response => {
                                console.log('Video prefetch successful');
                            })
                            .catch(err => {
                                console.error('Video prefetch failed:', err);
                            });
                    } catch (e) {
                        console.error('Prefetch error:', e);
                    }
                }

                // Set video element properties for better performance
                videoElement.autoplay = false; // We control playback manually
                videoElement.muted = false;
                videoElement.playsInline = true; // Better mobile experience
                videoElement.disablePictureInPicture = true; // Prevent PiP which can cause issues

                // For mobile devices, optimize for lower bandwidth
                if (window.innerWidth <= 768) {
                    // Mobile devices might struggle with higher quality
                    console.log('Mobile device detected, optimizing for performance');
                }
            }

            // Call optimization function
            optimizeVideoPlayback();
        });
    </script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
