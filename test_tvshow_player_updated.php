<?php
// Include necessary files
require_once 'includes/config.php';
require_once 'includes/streaming_helper.php';

// Test data
$tvshow_id = 1; // Replace with a valid TV show ID from your database
$episode_id = 1; // Replace with a valid episode ID from your database

// Get TV show details
$tvshow_query = "SELECT * FROM tvshows WHERE id = $tvshow_id";
$tvshow_result = mysqli_query($conn, $tvshow_query);
$tvshow = mysqli_fetch_assoc($tvshow_result);

// Get episode details
$episode_query = "SELECT * FROM episodes WHERE id = $episode_id";
$episode_result = mysqli_query($conn, $episode_query);
$episode = mysqli_fetch_assoc($episode_result);

// Get streaming links for this episode
$stream_query = "SELECT * FROM episode_links 
                WHERE episode_id = $episode_id 
                AND link_type = 'stream' 
                ORDER BY quality DESC, server_name ASC";
$stream_result = mysqli_query($conn, $stream_query);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>টিভি শো প্লেয়ার আপডেট টেস্ট</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #111;
            color: #fff;
            padding: 20px;
            font-family: 'SolaimanLipi', Arial, sans-serif;
        }
        .card {
            background-color: #222;
            border: none;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #333;
            color: #fff;
            border-bottom: 1px solid #444;
        }
        .btn-primary {
            background-color: #e50914;
            border-color: #e50914;
        }
        .btn-primary:hover {
            background-color: #b30710;
            border-color: #b30710;
        }
        .feature-list {
            list-style-type: none;
            padding-left: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #333;
            display: flex;
            align-items: center;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list li i {
            color: #e50914;
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        .stream-links {
            margin-top: 15px;
        }
        .stream-link {
            display: inline-block;
            margin-right: 10px;
            margin-bottom: 10px;
            padding: 8px 15px;
            background-color: #333;
            color: white;
            border-radius: 4px;
            text-decoration: none;
            transition: background-color 0.3s;
        }
        .stream-link:hover {
            background-color: #e50914;
            color: white;
        }
        .dropdown-menu {
            background-color: #333;
            border: 1px solid #444;
        }
        .dropdown-item {
            color: #fff;
        }
        .dropdown-item:hover {
            background-color: #e50914;
            color: #fff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">টিভি শো প্লেয়ার আপডেট টেস্ট</h1>
        
        <div class="card">
            <div class="card-header">
                <h5>আপডেট সম্পর্কে</h5>
            </div>
            <div class="card-body">
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> <strong>ডিফল্ট vidzee লিঙ্ক রিমুভ করা হয়েছে:</strong> এডমিন প্যানেলে এপিসোড লিঙ্ক ডিলিট করলে টিভি শো ডিটেইলস পেজে আর দেখাবে না</li>
                    <li><i class="fas fa-check"></i> <strong>"All Options" লিঙ্ক রিমুভ করা হয়েছে:</strong> "Watch" ড্রপডাউন মেনু থেকে "All Options" লিঙ্ক রিমুভ করা হয়েছে</li>
                </ul>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>ড্রপডাউন মেনু টেস্ট</h5>
            </div>
            <div class="card-body">
                <p>নিচের ড্রপডাউন মেনুতে "All Options" লিঙ্ক নেই:</p>
                
                <div class="dropdown">
                    <button class="btn btn-danger dropdown-toggle" type="button" id="streamDropdownTest" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-play me-1"></i> Watch
                    </button>
                    <ul class="dropdown-menu dropdown-menu-dark" aria-labelledby="streamDropdownTest">
                        <?php 
                        // Reset pointer to beginning
                        if (mysqli_num_rows($stream_result) > 0) {
                            mysqli_data_seek($stream_result, 0);
                            
                            // Get episode thumbnail or use TV show poster as fallback
                            $poster_url = SITE_URL . '/uploads/' . (!empty($episode['thumbnail']) ? $episode['thumbnail'] : $tvshow['poster']);
                            
                            while ($stream = mysqli_fetch_assoc($stream_result)):
                                // Check if it's a Cloudflare Worker link
                                $is_worker_url = (stripos($stream['link_url'], 'workers.dev') !== false);
                                
                                if ($is_worker_url) {
                                    // Generate enhanced player URL for worker links
                                    $player_url = getEpisodeStreamingUrl(
                                        $stream['link_url'],
                                        $episode['title'],
                                        $poster_url,
                                        $episode['is_premium'],
                                        $tvshow['title'],
                                        $episode['season_number'],
                                        $episode['episode_number']
                                    );
                                } else {
                                    // Use episode_player.php for non-worker links
                                    $player_url = SITE_URL . '/episode_player.php?id=' . $episode_id . '&stream=' . $stream['id'];
                                }
                                
                                // Get server name
                                $server_name = !empty($stream['server_name']) ? $stream['server_name'] : 'Server';
                        ?>
                        <li>
                            <a class="dropdown-item" href="<?php echo $player_url; ?>" target="<?php echo $is_worker_url ? '_blank' : '_self'; ?>">
                                <?php echo $server_name; ?> (<?php echo $stream['quality']; ?>)
                                <?php if($stream['is_premium']): ?>
                                <i class="fas fa-crown text-warning ms-1"></i>
                                <?php endif; ?>
                            </a>
                        </li>
                        <?php 
                            endwhile;
                        } else {
                            echo '<li><span class="dropdown-item">No streaming links available</span></li>';
                        }
                        ?>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>ব্যবহার নির্দেশিকা</h5>
            </div>
            <div class="card-body">
                <p>আপডেট করা সিস্টেম ব্যবহার করার জন্য:</p>
                <ol>
                    <li>অ্যাডমিন প্যানেলে যান</li>
                    <li>এপিসোড লিংক ম্যানেজমেন্টে যান</li>
                    <li>প্রতিটি এপিসোডের জন্য স্ট্রিমিং লিংক যুক্ত করুন</li>
                    <li>যদি কোন এপিসোডের জন্য স্ট্রিমিং লিংক না থাকে, তাহলে টিভি শো ডিটেইলস পেজে "Watch" বাটন দেখাবে না</li>
                    <li>Cloudflare Worker লিংক যুক্ত করলে সেগুলি স্বয়ংক্রিয়ভাবে উন্নত প্লেয়ারে প্লে হবে</li>
                </ol>
                
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>নোট:</strong> এখন আর ডিফল্ট vidzee লিঙ্ক স্বয়ংক্রিয়ভাবে তৈরি হবে না। আপনাকে অবশ্যই অ্যাডমিন প্যানেলে স্ট্রিমিং লিংক যুক্ত করতে হবে।
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
