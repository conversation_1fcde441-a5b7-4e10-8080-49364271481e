<?php
require_once 'includes/config.php';

// SQL commands to execute
$sql_commands = [
    "ALTER TABLE movies ADD COLUMN IF NOT EXISTS tmdb_id INT DEFAULT NULL",
    "ALTER TABLE movies ADD INDEX IF NOT EXISTS idx_tmdb_id (tmdb_id)",
    "ALTER TABLE tvshows ADD COLUMN IF NOT EXISTS tmdb_id INT DEFAULT NULL",
    "ALTER TABLE tvshows ADD INDEX IF NOT EXISTS idx_tmdb_id (tmdb_id)"
];

// Execute each SQL command
$success = true;
$messages = [];

foreach ($sql_commands as $sql) {
    if (mysqli_query($conn, $sql)) {
        $messages[] = "Success: " . $sql;
    } else {
        $success = false;
        $messages[] = "Error: " . $sql . " - " . mysqli_error($conn);
    }
}

// Output results
echo "<h1>Database Update Results</h1>";
echo "<ul>";
foreach ($messages as $message) {
    echo "<li>" . $message . "</li>";
}
echo "</ul>";

if ($success) {
    echo "<p>All database updates completed successfully!</p>";
} else {
    echo "<p>Some database updates failed. Please check the errors above.</p>";
}
?>
