<?php
// Set page title
$page_title = 'Update Active Status';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

$success_message = '';
$error_message = '';

// Check if is_active column exists in movies table
$check_movies_column_query = "SHOW COLUMNS FROM movies LIKE 'is_active'";
$check_movies_column_result = mysqli_query($conn, $check_movies_column_query);

// Check if is_active column exists in tvshows table
$check_tvshows_column_query = "SHOW COLUMNS FROM tvshows LIKE 'is_active'";
$check_tvshows_column_result = mysqli_query($conn, $check_tvshows_column_query);

if (mysqli_num_rows($check_movies_column_result) == 0) {
    // Add is_active column to movies table if it doesn't exist
    $add_movies_column_query = "ALTER TABLE movies ADD COLUMN is_active BOOLEAN DEFAULT TRUE";
    if (mysqli_query($conn, $add_movies_column_query)) {
        $success_message .= "Added is_active column to movies table.<br>";
        
        // Update existing movies to be active
        $update_movies_query = "UPDATE movies SET is_active = TRUE WHERE is_active IS NULL";
        if (mysqli_query($conn, $update_movies_query)) {
            $success_message .= "Updated existing movies to be active.<br>";
        } else {
            $error_message .= "Error updating movies: " . mysqli_error($conn) . "<br>";
        }
    } else {
        $error_message .= "Error adding is_active column to movies table: " . mysqli_error($conn) . "<br>";
    }
} else {
    $success_message .= "is_active column already exists in movies table.<br>";
}

if (mysqli_num_rows($check_tvshows_column_result) == 0) {
    // Add is_active column to tvshows table if it doesn't exist
    $add_tvshows_column_query = "ALTER TABLE tvshows ADD COLUMN is_active BOOLEAN DEFAULT TRUE";
    if (mysqli_query($conn, $add_tvshows_column_query)) {
        $success_message .= "Added is_active column to tvshows table.<br>";
        
        // Update existing tvshows to be active
        $update_tvshows_query = "UPDATE tvshows SET is_active = TRUE WHERE is_active IS NULL";
        if (mysqli_query($conn, $update_tvshows_query)) {
            $success_message .= "Updated existing TV shows to be active.<br>";
        } else {
            $error_message .= "Error updating TV shows: " . mysqli_error($conn) . "<br>";
        }
    } else {
        $error_message .= "Error adding is_active column to tvshows table: " . mysqli_error($conn) . "<br>";
    }
} else {
    $success_message .= "is_active column already exists in tvshows table.<br>";
}

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">
            <!-- Sidebar Toggle (Topbar) -->
            <button id="sidebarToggle" class="btn btn-link d-md-none rounded-circle mr-3">
                <i class="fa fa-bars"></i>
            </button>

            <!-- Topbar Title -->
            <div class="d-none d-sm-inline-block mr-auto ml-md-3 my-2 my-md-0 mw-100">
                <h1 class="h3 mb-0 text-gray-800">Update Active Status</h1>
            </div>
        </nav>
    </div>

    <div class="container-fluid">
        <?php if($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Database Update</h6>
            </div>
            <div class="card-body">
                <p>The database has been updated to add active/inactive status functionality for movies and TV shows.</p>
                <p>You can now activate or deactivate movies and TV shows from their respective management pages.</p>
                <div class="mt-4">
                    <a href="movies.php" class="btn btn-primary me-2">Go to Movies</a>
                    <a href="tvshows.php" class="btn btn-primary">Go to TV Shows</a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
