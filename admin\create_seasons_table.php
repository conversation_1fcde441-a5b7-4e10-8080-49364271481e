<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include config file
require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
    exit;
}

// Create seasons table
$create_table_query = "
CREATE TABLE IF NOT EXISTS `seasons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tvshow_id` int(11) NOT NULL,
  `season_number` int(11) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `overview` text DEFAULT NULL,
  `poster` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `tvshow_id` (`tvshow_id`),
  CONSTRAINT `seasons_ibfk_1` FOREIGN KEY (`tvshow_id`) REFERENCES `tvshows` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
";

// Create episodes table
$create_episodes_table_query = "
CREATE TABLE IF NOT EXISTS `episodes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `season_id` int(11) NOT NULL,
  `episode_number` int(11) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `overview` text DEFAULT NULL,
  `still` varchar(255) DEFAULT NULL,
  `air_date` date DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `season_id` (`season_id`),
  CONSTRAINT `episodes_ibfk_1` FOREIGN KEY (`season_id`) REFERENCES `seasons` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
";

// Create episode_links table
$create_episode_links_table_query = "
CREATE TABLE IF NOT EXISTS `episode_links` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `episode_id` int(11) NOT NULL,
  `link_type` enum('stream','download') NOT NULL DEFAULT 'stream',
  `quality` varchar(50) DEFAULT NULL,
  `url` text NOT NULL,
  `server_name` varchar(100) DEFAULT NULL,
  `file_size` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `episode_id` (`episode_id`),
  CONSTRAINT `episode_links_ibfk_1` FOREIGN KEY (`episode_id`) REFERENCES `episodes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
";

// Execute queries
$success = true;
$messages = [];

if (mysqli_query($conn, $create_table_query)) {
    $messages[] = "Seasons table created successfully.";
} else {
    $success = false;
    $messages[] = "Error creating seasons table: " . mysqli_error($conn);
}

if (mysqli_query($conn, $create_episodes_table_query)) {
    $messages[] = "Episodes table created successfully.";
} else {
    $success = false;
    $messages[] = "Error creating episodes table: " . mysqli_error($conn);
}

if (mysqli_query($conn, $create_episode_links_table_query)) {
    $messages[] = "Episode links table created successfully.";
} else {
    $success = false;
    $messages[] = "Error creating episode links table: " . mysqli_error($conn);
}

// Display results
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Seasons Tables</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Create Seasons Tables</h1>
        
        <?php if ($success): ?>
        <div class="alert alert-success">
            <h4>Success!</h4>
            <ul>
                <?php foreach ($messages as $message): ?>
                <li><?php echo $message; ?></li>
                <?php endforeach; ?>
            </ul>
            <p>You can now <a href="import_tmdb_tvshow.php" class="alert-link">import TV shows</a>.</p>
        </div>
        <?php else: ?>
        <div class="alert alert-danger">
            <h4>Error!</h4>
            <ul>
                <?php foreach ($messages as $message): ?>
                <li><?php echo $message; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
