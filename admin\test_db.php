<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit();
}

echo "<h2>Database Connection Test</h2>";

// Test database connection
if (isset($conn) && $conn) {
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    // Test if user_activity table exists
    $table_check = "SHOW TABLES LIKE 'user_activity'";
    $result = mysqli_query($conn, $table_check);
    
    if (mysqli_num_rows($result) > 0) {
        echo "<p style='color: green;'>✅ user_activity table exists!</p>";
        
        // Count records
        $count_query = "SELECT COUNT(*) as count FROM user_activity";
        $count_result = mysqli_query($conn, $count_query);
        if ($count_result) {
            $count = mysqli_fetch_assoc($count_result)['count'];
            echo "<p>📊 Total records in user_activity: <strong>$count</strong></p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ user_activity table does not exist. Creating...</p>";
        
        // Create table
        $create_table = "CREATE TABLE IF NOT EXISTS user_activity (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NULL,
            session_id VARCHAR(255) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT NULL,
            current_page VARCHAR(500) NULL,
            referrer VARCHAR(500) NULL,
            country VARCHAR(100) NULL,
            city VARCHAR(100) NULL,
            device_type VARCHAR(50) NULL,
            browser VARCHAR(100) NULL,
            is_premium BOOLEAN DEFAULT FALSE,
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            first_visit TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            page_views INT DEFAULT 1,
            INDEX idx_session_id (session_id),
            INDEX idx_last_activity (last_activity),
            INDEX idx_user_id (user_id),
            INDEX idx_ip_address (ip_address)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if (mysqli_query($conn, $create_table)) {
            echo "<p style='color: green;'>✅ user_activity table created successfully!</p>";
        } else {
            echo "<p style='color: red;'>❌ Error creating table: " . mysqli_error($conn) . "</p>";
        }
    }
    
    // Test if user_messages table exists
    $table_check2 = "SHOW TABLES LIKE 'user_messages'";
    $result2 = mysqli_query($conn, $table_check2);
    
    if (mysqli_num_rows($result2) > 0) {
        echo "<p style='color: green;'>✅ user_messages table exists!</p>";
        
        // Count records
        $count_query2 = "SELECT COUNT(*) as count FROM user_messages";
        $count_result2 = mysqli_query($conn, $count_query2);
        if ($count_result2) {
            $count2 = mysqli_fetch_assoc($count_result2)['count'];
            echo "<p>📊 Total records in user_messages: <strong>$count2</strong></p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ user_messages table does not exist. Creating...</p>";
        
        // Create table
        $create_table2 = "CREATE TABLE IF NOT EXISTS user_messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NULL,
            session_id VARCHAR(255) NOT NULL,
            admin_id INT NULL,
            message TEXT NOT NULL,
            message_type ENUM('user', 'admin') NOT NULL,
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_session_id (session_id),
            INDEX idx_created_at (created_at),
            INDEX idx_user_id (user_id),
            INDEX idx_admin_id (admin_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if (mysqli_query($conn, $create_table2)) {
            echo "<p style='color: green;'>✅ user_messages table created successfully!</p>";
        } else {
            echo "<p style='color: red;'>❌ Error creating table: " . mysqli_error($conn) . "</p>";
        }
    }
    
} else {
    echo "<p style='color: red;'>❌ Database connection failed!</p>";
}

echo "<hr>";
echo "<h3>Test Complete</h3>";
echo "<p><a href='live_user_tracker.php'>Go to Live User Tracker</a></p>";
echo "<p><a href='index_dark.php'>Back to Admin Dashboard</a></p>";
?>
