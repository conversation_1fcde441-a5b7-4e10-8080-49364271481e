<?php
require_once 'config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'login_required']);
    exit;
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Get review details
$content_id = isset($_POST['content_id']) ? (int)$_POST['content_id'] : 0;
$content_type = isset($_POST['content_type']) ? sanitize($_POST['content_type']) : '';
$rating = isset($_POST['rating']) ? (int)$_POST['rating'] : 0;
$comment = isset($_POST['comment']) ? sanitize($_POST['comment']) : '';

// Validate input
if ($content_id <= 0 || ($content_type != 'movie' && $content_type != 'tvshow')) {
    echo json_encode(['success' => false, 'message' => 'Invalid content']);
    exit;
}

if ($rating < 1 || $rating > 10) {
    echo json_encode(['success' => false, 'message' => 'Rating must be between 1 and 10']);
    exit;
}

if (empty($comment)) {
    echo json_encode(['success' => false, 'message' => 'Review comment is required']);
    exit;
}

// Check if content exists
if ($content_type == 'movie') {
    $check_query = "SELECT id FROM movies WHERE id = $content_id";
} else {
    $check_query = "SELECT id FROM tvshows WHERE id = $content_id";
}

$check_result = mysqli_query($conn, $check_query);
if (mysqli_num_rows($check_result) == 0) {
    echo json_encode(['success' => false, 'message' => 'Content not found']);
    exit;
}

// Check if user already reviewed this content
$user_id = $_SESSION['user_id'];
$review_check_query = "SELECT id FROM reviews 
                      WHERE user_id = $user_id 
                      AND content_type = '$content_type' 
                      AND content_id = $content_id";
$review_check_result = mysqli_query($conn, $review_check_query);

if (mysqli_num_rows($review_check_result) > 0) {
    // Update existing review
    $review_id = mysqli_fetch_assoc($review_check_result)['id'];
    $update_query = "UPDATE reviews 
                    SET rating = $rating, comment = '$comment' 
                    WHERE id = $review_id";
    
    if (mysqli_query($conn, $update_query)) {
        // Update content rating
        updateContentRating($content_type, $content_id, $conn);
        echo json_encode(['success' => true, 'message' => 'Review updated']);
    } else {
        echo json_encode(['success' => false, 'message' => mysqli_error($conn)]);
    }
} else {
    // Add new review
    $insert_query = "INSERT INTO reviews (user_id, content_type, content_id, rating, comment) 
                    VALUES ($user_id, '$content_type', $content_id, $rating, '$comment')";
    
    if (mysqli_query($conn, $insert_query)) {
        // Update content rating
        updateContentRating($content_type, $content_id, $conn);
        echo json_encode(['success' => true, 'message' => 'Review added']);
    } else {
        echo json_encode(['success' => false, 'message' => mysqli_error($conn)]);
    }
}

// Function to update content rating
function updateContentRating($content_type, $content_id, $conn) {
    // Calculate average rating
    $rating_query = "SELECT AVG(rating) as avg_rating FROM reviews 
                    WHERE content_type = '$content_type' AND content_id = $content_id";
    $rating_result = mysqli_query($conn, $rating_query);
    $avg_rating = mysqli_fetch_assoc($rating_result)['avg_rating'];
    
    // Update content rating
    if ($content_type == 'movie') {
        $update_query = "UPDATE movies SET rating = $avg_rating WHERE id = $content_id";
    } else {
        $update_query = "UPDATE tvshows SET rating = $avg_rating WHERE id = $content_id";
    }
    
    mysqli_query($conn, $update_query);
}
?>
