<?php
// Include direct config file
require_once '../direct_config.php';

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed. Use POST.',
        'data' => null
    ]);
    exit;
}

// Get JSON data from request body
$json_data = file_get_contents('php://input');
$data = json_decode($json_data, true);

// Check if data is valid JSON
if ($data === null) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Invalid JSON data.',
        'data' => null
    ]);
    exit;
}

// Check if device_tokens table exists
$check_table = "SHOW TABLES LIKE 'device_tokens'";
$result = mysqli_query($conn, $check_table);

if (mysqli_num_rows($result) == 0) {
    // Create device_tokens table
    $create_table = "CREATE TABLE IF NOT EXISTS device_tokens (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        device_token VARCHAR(255) NOT NULL,
        device_type ENUM('android', 'ios') NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_device_token (device_token)
    )";
    
    if (!mysqli_query($conn, $create_table)) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to create device_tokens table: ' . mysqli_error($conn),
            'data' => null
        ]);
        exit;
    }
}

// Extract data from request
$user_id = isset($data['user_id']) ? (int)$data['user_id'] : 0;
$device_token = isset($data['device_token']) ? $data['device_token'] : '';
$device_type = isset($data['device_type']) ? $data['device_type'] : '';

// Validate required fields
if ($user_id <= 0 || empty($device_token) || empty($device_type)) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'User ID, device token, and device type are required.',
        'data' => null
    ]);
    exit;
}

// Validate device type
if ($device_type !== 'android' && $device_type !== 'ios') {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Invalid device type. Must be "android" or "ios".',
        'data' => null
    ]);
    exit;
}

// Check if user exists
$user_check = "SELECT id FROM users WHERE id = ?";
$stmt = mysqli_prepare($conn, $user_check);
mysqli_stmt_bind_param($stmt, 'i', $user_id);
mysqli_stmt_execute($stmt);
mysqli_stmt_store_result($stmt);

if (mysqli_stmt_num_rows($stmt) == 0) {
    http_response_code(404);
    echo json_encode([
        'success' => false,
        'message' => 'User not found.',
        'data' => null
    ]);
    mysqli_stmt_close($stmt);
    exit;
}
mysqli_stmt_close($stmt);

// Check if device token already exists
$token_check = "SELECT id, user_id FROM device_tokens WHERE device_token = ?";
$stmt = mysqli_prepare($conn, $token_check);
mysqli_stmt_bind_param($stmt, 's', $device_token);
mysqli_stmt_execute($stmt);
mysqli_stmt_store_result($stmt);

if (mysqli_stmt_num_rows($stmt) > 0) {
    // Token exists, update user_id and device_type
    mysqli_stmt_bind_result($stmt, $token_id, $existing_user_id);
    mysqli_stmt_fetch($stmt);
    mysqli_stmt_close($stmt);
    
    // Update token
    $update_sql = "UPDATE device_tokens SET user_id = ?, device_type = ?, updated_at = NOW() WHERE id = ?";
    $update_stmt = mysqli_prepare($conn, $update_sql);
    mysqli_stmt_bind_param($update_stmt, 'isi', $user_id, $device_type, $token_id);
    
    if (mysqli_stmt_execute($update_stmt)) {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'message' => 'Device token updated successfully.',
            'data' => null
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to update device token: ' . mysqli_error($conn),
            'data' => null
        ]);
    }
    
    mysqli_stmt_close($update_stmt);
} else {
    // Token doesn't exist, insert new record
    mysqli_stmt_close($stmt);
    
    $insert_sql = "INSERT INTO device_tokens (user_id, device_token, device_type) VALUES (?, ?, ?)";
    $insert_stmt = mysqli_prepare($conn, $insert_sql);
    mysqli_stmt_bind_param($insert_stmt, 'iss', $user_id, $device_token, $device_type);
    
    if (mysqli_stmt_execute($insert_stmt)) {
        http_response_code(201);
        echo json_encode([
            'success' => true,
            'message' => 'Device token registered successfully.',
            'data' => null
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to register device token: ' . mysqli_error($conn),
            'data' => null
        ]);
    }
    
    mysqli_stmt_close($insert_stmt);
}

// Close database connection
mysqli_close($conn);
?>
