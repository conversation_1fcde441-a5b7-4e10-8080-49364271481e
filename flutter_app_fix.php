<?php
// Flutter app fix for CinePix

// Get the base URL
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
$host = $_SERVER['HTTP_HOST'];
$base_url = $protocol . $host;

echo "<h1>CinePix Flutter App Fix</h1>";

// Get a list of movies
$movies_url = $base_url . '/api/v1/direct_movies.php';
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $movies_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

$movies = [];
$first_movie_id = 0;

if ($http_code == 200) {
    $json = json_decode($response, true);
    if ($json && isset($json['success']) && $json['success'] === true && isset($json['data']['movies'])) {
        $movies = $json['data']['movies'];
        if (!empty($movies)) {
            $first_movie_id = $movies[0]['id'];
        }
    }
}

// Get a list of TV shows
$tvshows_url = $base_url . '/api/v1/direct_tvshows.php';
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $tvshows_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

$tvshows = [];
$first_tvshow_id = 0;

if ($http_code == 200) {
    $json = json_decode($response, true);
    if ($json && isset($json['success']) && $json['success'] === true && isset($json['data']['tvshows'])) {
        $tvshows = $json['data']['tvshows'];
        if (!empty($tvshows)) {
            $first_tvshow_id = $tvshows[0]['id'];
        }
    }
}

echo "<h2>Database Content Summary</h2>";
echo "<table border='1' cellpadding='10'>";
echo "<tr><th>Content Type</th><th>Count</th><th>First ID</th><th>Sample Titles</th></tr>";

echo "<tr>";
echo "<td>Movies</td>";
echo "<td>" . count($movies) . "</td>";
echo "<td>" . $first_movie_id . "</td>";
echo "<td>";
$sample_titles = array_slice(array_column($movies, 'title'), 0, 5);
echo implode(", ", $sample_titles);
echo "</td>";
echo "</tr>";

echo "<tr>";
echo "<td>TV Shows</td>";
echo "<td>" . count($tvshows) . "</td>";
echo "<td>" . $first_tvshow_id . "</td>";
echo "<td>";
$sample_titles = array_slice(array_column($tvshows, 'title'), 0, 5);
echo implode(", ", $sample_titles);
echo "</td>";
echo "</tr>";

echo "</table>";

// Test API endpoints with valid IDs
echo "<h2>API Endpoint Tests</h2>";
echo "<table border='1' cellpadding='10'>";
echo "<tr><th>Endpoint</th><th>Status</th><th>Response</th></tr>";

$endpoints = [
    'api/v1/direct_movies.php',
    'api/v1/direct_tvshows.php'
];

if ($first_movie_id > 0) {
    $endpoints[] = "api/v1/direct_movie_details.php?id=$first_movie_id";
}

if ($first_tvshow_id > 0) {
    $endpoints[] = "api/v1/direct_tvshow_details.php?id=$first_tvshow_id";
    $endpoints[] = "api/v1/direct_tvshow_episodes.php?id=$first_tvshow_id&season=1";
}

$endpoints[] = 'api/v1/direct_search.php?q=test';

foreach ($endpoints as $endpoint) {
    echo "<tr>";
    echo "<td>$endpoint</td>";
    
    // Get the contents of the endpoint
    $url = $base_url . '/' . $endpoint;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code == 200) {
        $json = json_decode($response, true);
        if ($json && isset($json['success']) && $json['success'] === true) {
            echo "<td style='background-color: #dff0d8;'>✓ Success</td>";
            
            // Check specific data
            if (strpos($endpoint, 'direct_movies.php') !== false) {
                $count = count($json['data']['movies'] ?? []);
                echo "<td>Found $count movies</td>";
            } elseif (strpos($endpoint, 'direct_tvshows.php') !== false) {
                $count = count($json['data']['tvshows'] ?? []);
                echo "<td>Found $count TV shows</td>";
            } elseif (strpos($endpoint, 'direct_movie_details.php') !== false) {
                $links_count = count($json['data']['download_links'] ?? []);
                echo "<td>Movie has $links_count download links</td>";
            } elseif (strpos($endpoint, 'direct_tvshow_details.php') !== false) {
                $seasons_count = count($json['data']['seasons'] ?? []);
                echo "<td>TV show has $seasons_count seasons</td>";
            } elseif (strpos($endpoint, 'direct_tvshow_episodes.php') !== false) {
                $episodes_count = count($json['data']['episodes'] ?? []);
                echo "<td>Season has $episodes_count episodes</td>";
            } elseif (strpos($endpoint, 'direct_search.php') !== false) {
                $movies_count = count($json['data']['movies'] ?? []);
                $tvshows_count = count($json['data']['tvshows'] ?? []);
                echo "<td>Found $movies_count movies and $tvshows_count TV shows</td>";
            } else {
                echo "<td>Success</td>";
            }
        } else {
            echo "<td style='background-color: #f2dede;'>✗ Failed</td>";
            echo "<td>Invalid response format</td>";
        }
    } else {
        echo "<td style='background-color: #f2dede;'>✗ Failed ($http_code)</td>";
        echo "<td>Request failed</td>";
    }
    
    echo "</tr>";
}

echo "</table>";

// Check download links for the first movie
if ($first_movie_id > 0) {
    echo "<h2>Download Links Check</h2>";
    
    $movie_details_url = $base_url . "/api/v1/direct_movie_details.php?id=$first_movie_id";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $movie_details_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code == 200) {
        $json = json_decode($response, true);
        if ($json && isset($json['success']) && $json['success'] === true) {
            $movie = $json['data']['movie'] ?? [];
            $download_links = $json['data']['download_links'] ?? [];
            
            echo "<h3>Movie: " . ($movie['title'] ?? 'Unknown') . " (ID: $first_movie_id)</h3>";
            
            if (!empty($download_links)) {
                echo "<table border='1' cellpadding='10'>";
                echo "<tr><th>ID</th><th>Quality</th><th>URL</th><th>Premium</th></tr>";
                
                foreach ($download_links as $link) {
                    echo "<tr>";
                    echo "<td>" . ($link['id'] ?? 'N/A') . "</td>";
                    echo "<td>" . ($link['quality'] ?? 'N/A') . "</td>";
                    $url = $link['url'] ?? 'No URL';
                    echo "<td>" . substr($url, 0, 50) . "...</td>";
                    $is_premium = isset($link['is_premium']) ? ($link['is_premium'] ? 'Yes' : 'No') : 'Unknown';
                    echo "<td>$is_premium</td>";
                    echo "</tr>";
                }
                
                echo "</table>";
            } else {
                echo "<p style='color: red;'>No download links found for this movie!</p>";
                
                // Check download_links table
                echo "<h3>Database Check</h3>";
                
                // Include direct config file
                if (file_exists('api/direct_config.php')) {
                    require_once 'api/direct_config.php';
                    
                    if (isset($conn)) {
                        $links_query = "SELECT * FROM download_links WHERE content_type = 'movie' AND content_id = $first_movie_id";
                        $links_result = mysqli_query($conn, $links_query);
                        
                        if ($links_result) {
                            $links_count = mysqli_num_rows($links_result);
                            
                            if ($links_count > 0) {
                                echo "<p>Found $links_count links in the database for this movie.</p>";
                                
                                echo "<table border='1' cellpadding='10'>";
                                echo "<tr><th>ID</th><th>Quality</th><th>URL</th><th>Premium</th></tr>";
                                
                                while ($link = mysqli_fetch_assoc($links_result)) {
                                    echo "<tr>";
                                    echo "<td>" . ($link['id'] ?? 'N/A') . "</td>";
                                    echo "<td>" . ($link['quality'] ?? 'N/A') . "</td>";
                                    $url = isset($link['url']) ? $link['url'] : (isset($link['link_url']) ? $link['link_url'] : 'No URL');
                                    echo "<td>" . substr($url, 0, 50) . "...</td>";
                                    $is_premium = isset($link['is_premium']) ? ($link['is_premium'] ? 'Yes' : 'No') : 'Unknown';
                                    echo "<td>$is_premium</td>";
                                    echo "</tr>";
                                }
                                
                                echo "</table>";
                            } else {
                                echo "<p style='color: red;'>No links found in the database for this movie!</p>";
                                
                                // Show table structure
                                $table_query = "DESCRIBE download_links";
                                $table_result = mysqli_query($conn, $table_query);
                                
                                if ($table_result) {
                                    echo "<h4>download_links Table Structure</h4>";
                                    echo "<table border='1' cellpadding='10'>";
                                    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
                                    
                                    while ($field = mysqli_fetch_assoc($table_result)) {
                                        echo "<tr>";
                                        echo "<td>" . $field['Field'] . "</td>";
                                        echo "<td>" . $field['Type'] . "</td>";
                                        echo "<td>" . $field['Null'] . "</td>";
                                        echo "<td>" . $field['Key'] . "</td>";
                                        echo "<td>" . $field['Default'] . "</td>";
                                        echo "<td>" . $field['Extra'] . "</td>";
                                        echo "</tr>";
                                    }
                                    
                                    echo "</table>";
                                }
                            }
                        } else {
                            echo "<p style='color: red;'>Error querying download_links table: " . mysqli_error($conn) . "</p>";
                        }
                    } else {
                        echo "<p style='color: red;'>Database connection not available!</p>";
                    }
                } else {
                    echo "<p style='color: red;'>Could not include direct_config.php!</p>";
                }
            }
        } else {
            echo "<p style='color: red;'>Failed to get movie details!</p>";
        }
    } else {
        echo "<p style='color: red;'>Failed to get movie details (HTTP $http_code)!</p>";
    }
}

// Flutter app API constants
echo "<h2>Flutter App API Constants</h2>";
echo "<pre>";
echo "class ApiConstants {
  // API Base URL
  static const String baseUrl = '$base_url/api/v1';

  // API Endpoints
  static const String configEndpoint = '/direct_config.php';
  static const String loginEndpoint = '/direct_login.php';
  static const String registerEndpoint = '/direct_register.php';
  static const String moviesEndpoint = '/direct_movies.php';
  static const String movieDetailsEndpoint = '/direct_movie_details.php';
  static const String tvShowsEndpoint = '/direct_tvshows.php';
  static const String tvShowDetailsEndpoint = '/direct_tvshow_details.php';
  static const String tvShowEpisodesEndpoint = '/direct_tvshow_episodes.php';
  static const String searchEndpoint = '/direct_search.php';
  static const String categoriesEndpoint = '/direct_categories.php';
  static const String profileEndpoint = '/direct_profile.php';
}";
echo "</pre>";

echo "<h2>Flutter App Troubleshooting</h2>";
echo "<ol>";
echo "<li>Make sure your Flutter app is using the correct API base URL: <code>$base_url/api/v1</code></li>";
echo "<li>Check if your Flutter app has internet permissions in <code>AndroidManifest.xml</code>:</li>";
echo "</ol>";

echo "<pre>";
echo "&lt;manifest ...&gt;
    &lt;uses-permission android:name=\"android.permission.INTERNET\" /&gt;
    ...
&lt;/manifest&gt;";
echo "</pre>";

echo "<h3>Common Issues and Solutions</h3>";
echo "<table border='1' cellpadding='10'>";
echo "<tr><th>Issue</th><th>Solution</th></tr>";

echo "<tr>";
echo "<td>No movies or TV shows on home page</td>";
echo "<td>
    <ol>
        <li>Check if the API endpoints return data</li>
        <li>Make sure your Flutter app is using the correct API base URL</li>
        <li>Check if your Flutter app has internet permissions</li>
        <li>Add debug prints in your Flutter code to see the API responses</li>
    </ol>
</td>";
echo "</tr>";

echo "<tr>";
echo "<td>No download links on movie/TV show details page</td>";
echo "<td>
    <ol>
        <li>Check if the movie/TV show has download links in the database</li>
        <li>Make sure the download_links table has the correct structure</li>
        <li>Check if the API endpoint returns download links</li>
        <li>Add debug prints in your Flutter code to see the API responses</li>
    </ol>
</td>";
echo "</tr>";

echo "</table>";

echo "<h3>Debug Code for Flutter</h3>";
echo "<p>Add this code to your Flutter app to debug API responses:</p>";

echo "<pre>";
echo "// In your API service
Future&lt;dynamic&gt; get(String endpoint, {Map&lt;String, dynamic&gt;? queryParams}) async {
  try {
    print('API Request: \${_dio.options.baseUrl}\$endpoint');
    print('Query Params: \$queryParams');
    
    final response = await _dio.get(
      endpoint,
      queryParameters: queryParams,
    );
    
    print('API Response: \${response.statusCode}');
    print('API Data: \${response.data}');
    
    return _handleResponse(response);
  } on DioException catch (e) {
    print('API Error: \${e.message}');
    print('API Error Response: \${e.response?.data}');
    return _handleError(e);
  } catch (e) {
    print('API Unknown Error: \$e');
    return {'success': false, 'message': AppConstants.unknownError};
  }
}";
echo "</pre>";

echo "<p><a href='api_test.php'>Run API Test</a></p>";
?>
