/* Search Page Card Styles */
.search-card {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    margin-bottom: 10px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    cursor: pointer;
}

.search-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
}

.search-card img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: filter 0.3s ease;
}

.search-card:hover img {
    filter: brightness(0.7);
}

.search-card-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0) 100%);
    padding: 15px 10px;
    transition: all 0.3s ease;
}

.search-card:hover .search-card-overlay {
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.7) 100%);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.search-card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 8px;
    transition: transform 0.3s ease;
    color: #fff;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.search-card:hover .search-card-title {
    transform: translateY(-10px);
}

.search-card-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 0.8rem;
    opacity: 0.8;
    transition: transform 0.3s ease, opacity 0.3s ease;
    color: #ddd;
}

.search-card:hover .search-card-info {
    transform: translateY(-5px);
    opacity: 1;
}

.search-card-rating {
    display: inline-block;
    background-color: rgba(0, 0, 0, 0.7);
    padding: 2px 6px;
    border-radius: 5px;
    font-size: 0.8rem;
    margin-bottom: 10px;
    transition: transform 0.3s ease;
    color: #ffc107;
}

.search-card:hover .search-card-rating {
    transform: translateY(-5px);
}

.search-card-rating i {
    color: #ffc107;
    margin-right: 3px;
}

.search-card-buttons {
    position: absolute;
    bottom: 15px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.3s ease, transform 0.3s ease;
    transform: translateY(15px);
    padding: 0 15px;
}

.search-card:hover .search-card-buttons {
    opacity: 1;
    transform: translateY(0);
}

.search-card-btn {
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
    text-decoration: none;
    font-size: 0.9rem;
}

.search-card-btn:hover {
    background-color: #e50914;
    color: #fff;
    text-decoration: none;
}

/* Make cards fully clickable */
.search-card-link {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

/* Responsive styles */
@media (max-width: 767.98px) {
    .search-card img {
        height: 200px;
    }

    .search-card-title {
        font-size: 0.9rem;
    }

    .search-card-info {
        font-size: 0.75rem;
    }

    .search-card-buttons {
        gap: 5px;
    }

    .search-card-btn {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }
}

@media (max-width: 575.98px) {
    .search-card img {
        height: 180px;
    }

    .search-card-title {
        font-size: 0.85rem;
    }

    .search-card-info {
        font-size: 0.7rem;
    }

    .search-card-rating {
        font-size: 0.7rem;
        padding: 2px 5px;
    }

    .search-card-btn {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }
}
