<?php
require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Set current page for sidebar
$current_page = 'manage_episode_links.php';

// Get episode ID from URL
$episode_id = isset($_GET['episode']) ? (int)$_GET['episode'] : 0;

// Get episode details
$episode_query = "SELECT e.*, t.title as tvshow_title FROM episodes e 
                  JOIN tvshows t ON e.tvshow_id = t.id 
                  WHERE e.id = $episode_id";
$episode_result = mysqli_query($conn, $episode_query);

if (mysqli_num_rows($episode_result) == 0) {
    redirect(SITE_URL . '/admin/tvshows.php');
}

$episode = mysqli_fetch_assoc($episode_result);

// Process form submissions
$success_message = '';
$error_message = '';

// Add/Edit Episode Download Link
if (isset($_POST['add_download_link'])) {
    $quality = sanitize($_POST['quality']);
    $link_url = sanitize($_POST['link_url']);
    $is_premium = isset($_POST['is_premium']) ? 1 : 0;
    $link_id = isset($_POST['link_id']) ? (int)$_POST['link_id'] : 0;

    if (empty($quality) || empty($link_url)) {
        $error_message = 'Please fill in all required fields.';
    } else {
        if ($link_id > 0) {
            // Update existing link
            $update_query = "UPDATE episode_links SET
                            quality = '$quality',
                            link_url = '$link_url',
                            is_premium = $is_premium
                            WHERE id = $link_id AND episode_id = $episode_id AND link_type = 'download'";

            if (mysqli_query($conn, $update_query)) {
                $success_message = 'Download link updated successfully.';
            } else {
                $error_message = 'Error updating download link: ' . mysqli_error($conn);
            }
        } else {
            // Add new link
            $insert_query = "INSERT INTO episode_links (episode_id, link_type, quality, link_url, is_premium)
                            VALUES ($episode_id, 'download', '$quality', '$link_url', $is_premium)";

            if (mysqli_query($conn, $insert_query)) {
                $success_message = 'Download link added successfully.';
            } else {
                $error_message = 'Error adding download link: ' . mysqli_error($conn);
            }
        }
    }
}

// Add/Edit Episode Streaming Link
if (isset($_POST['add_streaming_link'])) {
    $quality = sanitize($_POST['quality']);
    $server_name = sanitize($_POST['server_name']);
    $link_url = sanitize($_POST['link_url']);
    $is_premium = isset($_POST['is_premium']) ? 1 : 0;
    $link_id = isset($_POST['link_id']) ? (int)$_POST['link_id'] : 0;

    if (empty($quality) || empty($server_name) || empty($link_url)) {
        $error_message = 'Please fill in all required fields.';
    } else {
        if ($link_id > 0) {
            // Update existing link
            $update_query = "UPDATE episode_links SET
                            quality = '$quality',
                            server_name = '$server_name',
                            link_url = '$link_url',
                            is_premium = $is_premium
                            WHERE id = $link_id AND episode_id = $episode_id AND link_type = 'stream'";

            if (mysqli_query($conn, $update_query)) {
                $success_message = 'Streaming link updated successfully.';
            } else {
                $error_message = 'Error updating streaming link: ' . mysqli_error($conn);
            }
        } else {
            // Add new link
            $insert_query = "INSERT INTO episode_links (episode_id, link_type, quality, server_name, link_url, is_premium)
                            VALUES ($episode_id, 'stream', '$quality', '$server_name', '$link_url', $is_premium)";

            if (mysqli_query($conn, $insert_query)) {
                $success_message = 'Streaming link added successfully.';
            } else {
                $error_message = 'Error adding streaming link: ' . mysqli_error($conn);
            }
        }
    }
}

// Delete Episode Download Link
if (isset($_GET['delete_download']) && $_GET['delete_download'] > 0) {
    $link_id = (int)$_GET['delete_download'];

    $delete_query = "DELETE FROM episode_links WHERE id = $link_id AND episode_id = $episode_id AND link_type = 'download'";

    if (mysqli_query($conn, $delete_query)) {
        $success_message = 'Download link deleted successfully.';
    } else {
        $error_message = 'Error deleting download link: ' . mysqli_error($conn);
    }
}

// Delete Episode Streaming Link
if (isset($_GET['delete_streaming']) && $_GET['delete_streaming'] > 0) {
    $link_id = (int)$_GET['delete_streaming'];

    $delete_query = "DELETE FROM episode_links WHERE id = $link_id AND episode_id = $episode_id AND link_type = 'stream'";

    if (mysqli_query($conn, $delete_query)) {
        $success_message = 'Streaming link deleted successfully.';
    } else {
        $error_message = 'Error deleting streaming link: ' . mysqli_error($conn);
    }
}

// Get episode download links
$download_query = "SELECT * FROM episode_links WHERE episode_id = $episode_id AND link_type = 'download' ORDER BY quality DESC";
$download_result = mysqli_query($conn, $download_query);

// Get episode streaming links
$streaming_query = "SELECT * FROM episode_links WHERE episode_id = $episode_id AND link_type = 'stream' ORDER BY quality DESC, server_name ASC";
$streaming_result = mysqli_query($conn, $streaming_query);

// Get link to edit if specified
$edit_download_link = null;
$edit_streaming_link = null;

if (isset($_GET['edit_download']) && $_GET['edit_download'] > 0) {
    $link_id = (int)$_GET['edit_download'];

    $edit_query = "SELECT * FROM episode_links WHERE id = $link_id AND episode_id = $episode_id AND link_type = 'download'";
    $edit_result = mysqli_query($conn, $edit_query);

    if (mysqli_num_rows($edit_result) > 0) {
        $edit_download_link = mysqli_fetch_assoc($edit_result);
    }
}

if (isset($_GET['edit_streaming']) && $_GET['edit_streaming'] > 0) {
    $link_id = (int)$_GET['edit_streaming'];

    $edit_query = "SELECT * FROM episode_links WHERE id = $link_id AND episode_id = $episode_id AND link_type = 'stream'";
    $edit_result = mysqli_query($conn, $edit_query);

    if (mysqli_num_rows($edit_result) > 0) {
        $edit_streaming_link = mysqli_fetch_assoc($edit_result);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Episode Links - <?php echo SITE_NAME; ?> Admin</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Sidebar Responsive CSS -->
    <link rel="stylesheet" href="assets/css/sidebar-responsive.css">
    <!-- Custom CSS -->
    <style>
        body {
            background-color: #f8f9fa;
        }
        .main-content {
            margin-left: 0;
            padding: 20px;
            flex: 1;
        }
        .card {
            border: none;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            border-radius: 10px;
        }
        .card-header {
            border-radius: 10px 10px 0 0 !important;
            border-bottom: none;
        }
        .btn {
            border-radius: 8px;
            font-weight: 500;
        }
        .table {
            border-radius: 8px;
            overflow: hidden;
        }
        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .badge {
            border-radius: 6px;
            font-weight: 500;
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .episode-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .col-md-6 {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 15px;
            }
            
            .d-flex.justify-content-between {
                flex-direction: column;
                gap: 15px;
            }
            
            .d-flex.justify-content-between .btn {
                width: 100%;
                margin-bottom: 10px;
            }
            
            .episode-info .row {
                flex-direction: column;
                gap: 15px;
            }
            
            .episode-info .col-md-8,
            .episode-info .col-md-4 {
                flex: 0 0 100%;
                max-width: 100%;
            }
            
            .episode-info .text-end {
                text-align: left !important;
            }
            
            .table-responsive {
                font-size: 14px;
            }
            
            .table th,
            .table td {
                padding: 8px 5px;
            }
            
            .btn-group .btn {
                padding: 4px 8px;
                font-size: 12px;
            }
            
            .card-body {
                padding: 15px;
            }
            
            .form-control,
            .form-select {
                font-size: 14px;
            }
        }

        @media (max-width: 576px) {
            .main-content {
                padding: 10px;
            }
            
            h1 {
                font-size: 1.5rem;
            }
            
            .card-header h5 {
                font-size: 1rem;
            }
            
            .episode-info {
                padding: 15px;
            }
            
            .episode-info h4 {
                font-size: 1.2rem;
            }
            
            .episode-info h5 {
                font-size: 1rem;
            }
            
            .table {
                font-size: 12px;
            }
            
            .table th,
            .table td {
                padding: 5px 3px;
            }
            
            .btn {
                font-size: 12px;
                padding: 6px 12px;
            }
            
            .badge {
                font-size: 10px;
            }
            
            .modal-dialog {
                margin: 10px;
            }
            
            .modal-body {
                padding: 15px;
            }
        }

        /* Mobile-first table improvements */
        @media (max-width: 768px) {
            .table-responsive {
                border: none;
            }
            
            .table thead {
                display: none;
            }
            
            .table tbody tr {
                display: block;
                margin-bottom: 15px;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
                background: white;
            }
            
            .table tbody td {
                display: block;
                text-align: left;
                border: none;
                padding: 5px 0;
            }
            
            .table tbody td:before {
                content: attr(data-label) ": ";
                font-weight: bold;
                color: #495057;
            }
            
            .table tbody td.actions {
                text-align: center;
                margin-top: 10px;
            }
            
            .table tbody td.actions:before {
                content: "";
            }
        }

    </style>
</head>
<body>
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Manage Episode Links</h1>
                <div>
                    <a href="manage_episodes.php?tvshow=<?php echo $episode['tvshow_id']; ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Episodes
                    </a>
                    <a href="<?php echo SITE_URL; ?>/episode_details.php?id=<?php echo $episode_id; ?>" class="btn btn-primary" target="_blank">
                        <i class="fas fa-eye me-2"></i>View Episode
                    </a>
                </div>
            </div>

            <!-- Episode Info -->
            <div class="episode-info">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4 class="mb-1"><?php echo $episode['tvshow_title']; ?></h4>
                        <h5 class="mb-2">S<?php echo $episode['season_number']; ?>E<?php echo $episode['episode_number']; ?> - <?php echo $episode['title']; ?></h5>
                        <?php if($episode['description']): ?>
                        <p class="mb-0 opacity-75"><?php echo $episode['description']; ?></p>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-4 text-end">
                        <?php if($episode['duration']): ?>
                        <span class="badge bg-light text-dark me-2"><?php echo $episode['duration']; ?> min</span>
                        <?php endif; ?>
                        <?php if($episode['is_premium']): ?>
                        <span class="badge bg-danger">Premium</span>
                        <?php else: ?>
                        <span class="badge bg-success">Free</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <?php if($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <?php if($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <div class="row">
                <!-- Download Links Section -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-download me-2"></i>Download Links
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- Add/Edit Download Link Form -->
                            <form method="POST" action="" class="mb-4">
                                <input type="hidden" name="link_id" value="<?php echo $edit_download_link ? $edit_download_link['id'] : ''; ?>">
                                <div class="mb-3">
                                    <label for="quality" class="form-label">Quality</label>
                                    <select class="form-select" id="quality" name="quality" required>
                                        <option value="">Select Quality</option>
                                        <option value="480p" <?php echo ($edit_download_link && $edit_download_link['quality'] == '480p') ? 'selected' : ''; ?>>480p</option>
                                        <option value="720p" <?php echo ($edit_download_link && $edit_download_link['quality'] == '720p') ? 'selected' : ''; ?>>720p</option>
                                        <option value="1080p" <?php echo ($edit_download_link && $edit_download_link['quality'] == '1080p') ? 'selected' : ''; ?>>1080p</option>
                                        <option value="4K" <?php echo ($edit_download_link && $edit_download_link['quality'] == '4K') ? 'selected' : ''; ?>>4K</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="link_url" class="form-label">Download URL</label>
                                    <input type="url" class="form-control" id="link_url" name="link_url" 
                                           value="<?php echo $edit_download_link ? $edit_download_link['link_url'] : ''; ?>" required>
                                </div>
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="is_premium" name="is_premium" 
                                           <?php echo ($edit_download_link && $edit_download_link['is_premium']) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_premium">Premium Only</label>
                                </div>
                                <div class="d-grid gap-2">
                                    <button type="submit" name="add_download_link" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        <?php echo $edit_download_link ? 'Update Download Link' : 'Add Download Link'; ?>
                                    </button>
                                    <?php if($edit_download_link): ?>
                                    <a href="manage_episode_links.php?episode=<?php echo $episode_id; ?>" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </form>

                            <!-- Download Links Table -->
                            <?php if(mysqli_num_rows($download_result) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Quality</th>
                                            <th>Premium</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while($link = mysqli_fetch_assoc($download_result)): ?>
                                        <tr>
                                            <td data-label="Quality">
                                                <span class="badge bg-primary"><?php echo $link['quality']; ?></span>
                                            </td>
                                            <td data-label="Premium">
                                                <?php if($link['is_premium']): ?>
                                                <span class="badge bg-danger">Yes</span>
                                                <?php else: ?>
                                                <span class="badge bg-secondary">No</span>
                                                <?php endif; ?>
                                            </td>
                                            <td data-label="Actions" class="actions">
                                                <div class="btn-group" role="group">
                                                    <a href="manage_episode_links.php?episode=<?php echo $episode_id; ?>&edit_download=<?php echo $link['id']; ?>" 
                                                       class="btn btn-sm btn-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="manage_episode_links.php?episode=<?php echo $episode_id; ?>&delete_download=<?php echo $link['id']; ?>" 
                                                       class="btn btn-sm btn-danger" 
                                                       onclick="return confirm('Are you sure you want to delete this download link?')" 
                                                       title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <p class="mb-0">No download links added yet.</p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Streaming Links Section -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-play me-2"></i>Streaming Links
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- Add/Edit Streaming Link Form -->
                            <form method="POST" action="" class="mb-4">
                                <input type="hidden" name="link_id" value="<?php echo $edit_streaming_link ? $edit_streaming_link['id'] : ''; ?>">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="quality" class="form-label">Quality</label>
                                        <select class="form-select" id="quality" name="quality" required>
                                            <option value="">Select Quality</option>
                                            <option value="480p" <?php echo ($edit_streaming_link && $edit_streaming_link['quality'] == '480p') ? 'selected' : ''; ?>>480p</option>
                                            <option value="720p" <?php echo ($edit_streaming_link && $edit_streaming_link['quality'] == '720p') ? 'selected' : ''; ?>>720p</option>
                                            <option value="1080p" <?php echo ($edit_streaming_link && $edit_streaming_link['quality'] == '1080p') ? 'selected' : ''; ?>>1080p</option>
                                            <option value="4K" <?php echo ($edit_streaming_link && $edit_streaming_link['quality'] == '4K') ? 'selected' : ''; ?>>4K</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="server_name" class="form-label">Server Name</label>
                                        <input type="text" class="form-control" id="server_name" name="server_name" 
                                               value="<?php echo $edit_streaming_link ? $edit_streaming_link['server_name'] : ''; ?>" required>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="link_url" class="form-label">Stream URL</label>
                                    <input type="url" class="form-control" id="link_url" name="link_url" 
                                           value="<?php echo $edit_streaming_link ? $edit_streaming_link['link_url'] : ''; ?>" required>
                                </div>
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="is_premium_stream" name="is_premium" 
                                           <?php echo ($edit_streaming_link && $edit_streaming_link['is_premium']) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_premium_stream">Premium Only</label>
                                </div>
                                <div class="d-grid gap-2">
                                    <button type="submit" name="add_streaming_link" class="btn btn-success">
                                        <i class="fas fa-save me-2"></i>
                                        <?php echo $edit_streaming_link ? 'Update Streaming Link' : 'Add Streaming Link'; ?>
                                    </button>
                                    <?php if($edit_streaming_link): ?>
                                    <a href="manage_episode_links.php?episode=<?php echo $episode_id; ?>" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </form>

                            <!-- Streaming Links Table -->
                            <?php if(mysqli_num_rows($streaming_result) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Quality</th>
                                            <th>Server</th>
                                            <th>Premium</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while($link = mysqli_fetch_assoc($streaming_result)): ?>
                                        <tr>
                                            <td data-label="Quality">
                                                <span class="badge bg-success"><?php echo $link['quality']; ?></span>
                                            </td>
                                            <td data-label="Server"><?php echo $link['server_name']; ?></td>
                                            <td data-label="Premium">
                                                <?php if($link['is_premium']): ?>
                                                <span class="badge bg-danger">Yes</span>
                                                <?php else: ?>
                                                <span class="badge bg-secondary">No</span>
                                                <?php endif; ?>
                                            </td>
                                            <td data-label="Actions" class="actions">
                                                <div class="btn-group" role="group">
                                                    <a href="manage_episode_links.php?episode=<?php echo $episode_id; ?>&edit_streaming=<?php echo $link['id']; ?>" 
                                                       class="btn btn-sm btn-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="manage_episode_links.php?episode=<?php echo $episode_id; ?>&delete_streaming=<?php echo $link['id']; ?>" 
                                                       class="btn btn-sm btn-danger" 
                                                       onclick="return confirm('Are you sure you want to delete this streaming link?')" 
                                                       title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <p class="mb-0">No streaming links added yet.</p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
</body>
</html>
