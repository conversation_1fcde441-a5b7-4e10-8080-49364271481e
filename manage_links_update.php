<?php
// Set error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// This file contains instructions for updating manage_links.php and manage_episode_links.php
// to include subtitle URL fields

/**
 * Update the download link form in manage_links.php to include subtitle URL fields
 *
 * Instructions:
 * 1. Find the download link form in manage_links.php
 * 2. Add the following code before the "Premium Only" checkbox:
 */

/*
<div class="mb-3">
    <label for="subtitle_url_bn" class="form-label">Bangla Subtitle URL (Optional)</label>
    <input type="url" class="form-control" id="subtitle_url_bn" name="subtitle_url_bn" value="<?php echo $edit_download_link ? $edit_download_link['subtitle_url_bn'] : ''; ?>">
    <small class="form-text text-muted">Leave empty to auto-detect from OpenSubtitles</small>
</div>
<div class="mb-3">
    <label for="subtitle_url_en" class="form-label">English Subtitle URL (Optional)</label>
    <input type="url" class="form-control" id="subtitle_url_en" name="subtitle_url_en" value="<?php echo $edit_download_link ? $edit_download_link['subtitle_url_en'] : ''; ?>">
    <small class="form-text text-muted">Leave empty to auto-detect from OpenSubtitles</small>
</div>
*/

/**
 * Update the PHP code in manage_links.php to handle subtitle URLs
 *
 * Instructions:
 * 1. Find the section where download link form data is processed
 * 2. Add the following code to extract subtitle URLs:
 */

/*
$subtitle_url_bn = sanitize($_POST['subtitle_url_bn']);
$subtitle_url_en = sanitize($_POST['subtitle_url_en']);
*/

/**
 * 3. Update the INSERT query to include subtitle URLs:
 */

/*
// For new links
$insert_query = "INSERT INTO links (content_type, content_id, link_type, quality, server_name, file_size, link_url, subtitle_url_bn, subtitle_url_en, is_premium)
                VALUES ('$content_type', $content_id, '$link_type', '$quality', '$server_name', '$file_size', '$link_url', '$subtitle_url_bn', '$subtitle_url_en', $is_premium)";

// For updating links
$update_query = "UPDATE links SET
                link_type = '$link_type',
                quality = '$quality',
                server_name = '$server_name',
                file_size = '$file_size',
                link_url = '$link_url',
                subtitle_url_bn = '$subtitle_url_bn',
                subtitle_url_en = '$subtitle_url_en',
                is_premium = $is_premium
                WHERE id = $link_id";
*/

/**
 * Update the streaming link form in manage_links.php to include subtitle URL fields
 *
 * Instructions:
 * 1. Find the streaming link form in manage_links.php
 * 2. Add the same subtitle URL fields as for download links
 * 3. Update the INSERT and UPDATE queries for streaming links similarly
 */

/**
 * Update manage_episode_links.php in the same way
 *
 * Instructions:
 * 1. Add subtitle URL fields to both download and streaming link forms
 * 2. Update the PHP code to handle these fields
 * 3. Update the INSERT and UPDATE queries to include subtitle URLs
 */

// HTML header for better display
echo "<!DOCTYPE html>\n";
echo "<html>\n";
echo "<head>\n";
echo "<title>Update Instructions for Subtitle Fields</title>\n";
echo "<style>\n";
echo "body { font-family: Arial, sans-serif; margin: 20px; }\n";
echo "pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }\n";
echo "h1, h2, h3 { color: #333; }\n";
echo "</style>\n";
echo "</head>\n";
echo "<body>\n";

echo "<h1>Update Instructions for Subtitle Fields</h1>";
echo "<p>This file contains instructions for updating manage_links.php and manage_episode_links.php to include subtitle URL fields.</p>";
echo "<p>Please follow the instructions in the code comments to update your files.</p>";

echo "<h2>Instructions for manage_links.php</h2>";
echo "<ol>";
echo "<li>Find the download link form in manage_links.php</li>";
echo "<li>Add the subtitle URL fields before the 'Premium Only' checkbox</li>";
echo "<li>Update the PHP code to handle these fields</li>";
echo "<li>Update the INSERT and UPDATE queries to include subtitle URLs</li>";
echo "</ol>";

echo "<h2>Instructions for manage_episode_links.php</h2>";
echo "<ol>";
echo "<li>Find the download link form in manage_episode_links.php</li>";
echo "<li>Add the subtitle URL fields before the 'Premium Only' checkbox</li>";
echo "<li>Update the PHP code to handle these fields</li>";
echo "<li>Update the INSERT and UPDATE queries to include subtitle URLs</li>";
echo "</ol>";

echo "<h2>Code Samples</h2>";

echo "<h3>HTML Form Fields</h3>";
echo "<pre>";
echo htmlspecialchars("<div class=\"mb-3\">
    <label for=\"subtitle_url_bn\" class=\"form-label\">Bangla Subtitle URL (Optional)</label>
    <input type=\"url\" class=\"form-control\" id=\"subtitle_url_bn\" name=\"subtitle_url_bn\" value=\"<?php echo \$edit_download_link ? \$edit_download_link['subtitle_url_bn'] : ''; ?>\">
    <small class=\"form-text text-muted\">Leave empty to auto-detect from OpenSubtitles</small>
</div>
<div class=\"mb-3\">
    <label for=\"subtitle_url_en\" class=\"form-label\">English Subtitle URL (Optional)</label>
    <input type=\"url\" class=\"form-control\" id=\"subtitle_url_en\" name=\"subtitle_url_en\" value=\"<?php echo \$edit_download_link ? \$edit_download_link['subtitle_url_en'] : ''; ?>\">
    <small class=\"form-text text-muted\">Leave empty to auto-detect from OpenSubtitles</small>
</div>");
echo "</pre>";

echo "<h3>PHP Code</h3>";
echo "<pre>";
echo htmlspecialchars("\$subtitle_url_bn = sanitize(\$_POST['subtitle_url_bn']);
\$subtitle_url_en = sanitize(\$_POST['subtitle_url_en']);");
echo "</pre>";

echo "<h3>SQL Queries</h3>";
echo "<pre>";
echo htmlspecialchars("// For new links
\$insert_query = \"INSERT INTO links (content_type, content_id, link_type, quality, server_name, file_size, link_url, subtitle_url_bn, subtitle_url_en, is_premium)
                VALUES ('\$content_type', \$content_id, '\$link_type', '\$quality', '\$server_name', '\$file_size', '\$link_url', '\$subtitle_url_bn', '\$subtitle_url_en', \$is_premium)\";

// For updating links
\$update_query = \"UPDATE links SET
                link_type = '\$link_type',
                quality = '\$quality',
                server_name = '\$server_name',
                file_size = '\$file_size',
                link_url = '\$link_url',
                subtitle_url_bn = '\$subtitle_url_bn',
                subtitle_url_en = '\$subtitle_url_en',
                is_premium = \$is_premium
                WHERE id = \$link_id\";");
echo "</pre>";

echo "<p><a href='index.php'>Return to Home Page</a></p>";

echo "</body>\n";
echo "</html>\n";
?>
