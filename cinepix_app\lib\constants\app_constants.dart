import 'package:flutter/material.dart';

class AppConstants {
  // App Name
  static const String appName = 'CinePix';
  
  // App Version
  static const String appVersion = '1.0.0';
  
  // Colors
  static const Color primaryColor = Color(0xFFE50914);
  static const Color accentColor = Color(0xFFE50914);
  static const Color backgroundColor = Color(0xFF121212);
  static const Color surfaceColor = Color(0xFF1E1E1E);
  static const Color textColor = Color(0xFFFFFFFF);
  static const Color secondaryTextColor = Color(0xFFB3B3B3);
  
  // Padding
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  
  // Border Radius
  static const double defaultBorderRadius = 8.0;
  static const double cardBorderRadius = 12.0;
  
  // Animation Duration
  static const Duration defaultAnimationDuration = Duration(milliseconds: 300);
  
  // Image Placeholders
  static const String moviePlaceholder = 'assets/images/movie_placeholder.png';
  static const String profilePlaceholder = 'assets/images/profile_placeholder.png';
  
  // Error Messages
  static const String networkErrorMessage = 'Network error. Please check your internet connection.';
  static const String serverErrorMessage = 'Server error. Please try again later.';
  static const String unknownErrorMessage = 'An unknown error occurred. Please try again.';
  
  // Shared Preferences Keys
  static const String tokenKey = 'auth_token';
  static const String userIdKey = 'user_id';
  static const String userNameKey = 'user_name';
  static const String userEmailKey = 'user_email';
  static const String isPremiumKey = 'is_premium';
  static const String isDarkModeKey = 'is_dark_mode';
  
  // Other Constants
  static const int defaultPageSize = 20;
  static const int cacheExpiryMinutes = 30;
}
