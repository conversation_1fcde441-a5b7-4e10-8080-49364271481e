<?php
// Standalone API Configuration File
// This file does not depend on ../includes/config.php

// Database configuration
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'tipsbdxy_4525');
define('DB_PASSWORD', 'tipsbdxy_4525'); // Update with correct password
define('DB_NAME', 'tipsbdxy_4525');

// Create database connection
$conn = mysqli_connect(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);

// Check connection
if($conn === false){
    die("ERROR: Could not connect to database. " . mysqli_connect_error());
}

// Set character set
mysqli_set_charset($conn, "utf8mb4");

// Site configuration
define('SITE_NAME', 'CinePix');
define('SITE_URL', 'https://cinepix.top');
define('SITE_EMAIL', '<EMAIL>');
define('SITE_DESCRIPTION', 'Watch and download movies and TV shows');
define('SITE_KEYWORDS', 'movies, tv shows, streaming, download, entertainment');

// API Settings
define('API_VERSION', 'v1');
define('API_KEY_LENGTH', 32);
define('JWT_SECRET', 'cinepix_secure_jwt_secret_key_2025'); // Secure JWT secret key
define('JWT_EXPIRY', 604800); // 7 days in seconds

// CORS Headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization, API-Key');
header('Content-Type: application/json; charset=UTF-8');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// API Response Functions
function api_response($data, $status_code = 200, $message = 'Success') {
    http_response_code($status_code);
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => $data,
        'meta' => isset($data['meta']) ? $data['meta'] : null
    ]);
    exit;
}

function api_error($message, $status_code = 400) {
    http_response_code($status_code);
    echo json_encode([
        'success' => false,
        'message' => $message,
        'data' => null,
        'meta' => null
    ]);
    exit;
}

// JWT Functions
function generate_jwt($user_id, $username, $role) {
    $issued_at = time();
    $expiry = $issued_at + JWT_EXPIRY;

    $payload = [
        'iss' => SITE_URL, // Issuer
        'aud' => SITE_URL, // Audience
        'iat' => $issued_at, // Issued at
        'exp' => $expiry, // Expiry
        'user_id' => $user_id,
        'username' => $username,
        'role' => $role
    ];

    $header = [
        'alg' => 'HS256',
        'typ' => 'JWT'
    ];

    $header_encoded = base64_encode(json_encode($header));
    $payload_encoded = base64_encode(json_encode($payload));

    $signature = hash_hmac('sha256', "$header_encoded.$payload_encoded", JWT_SECRET, true);
    $signature_encoded = base64_encode($signature);

    return "$header_encoded.$payload_encoded.$signature_encoded";
}

function verify_jwt($token) {
    $token_parts = explode('.', $token);

    if (count($token_parts) !== 3) {
        return false;
    }

    list($header_encoded, $payload_encoded, $signature_encoded) = $token_parts;

    $signature = base64_decode($signature_encoded);
    $expected_signature = hash_hmac('sha256', "$header_encoded.$payload_encoded", JWT_SECRET, true);

    if (!hash_equals($expected_signature, $signature)) {
        return false;
    }

    $payload = json_decode(base64_decode($payload_encoded), true);

    if ($payload['exp'] < time()) {
        return false; // Token expired
    }

    return $payload;
}

// Function to check if user is authenticated
function is_authenticated() {
    global $token;

    if (!$token) {
        return false;
    }

    $payload = verify_jwt($token);
    return $payload !== false;
}

// Function to get authenticated user data
function get_authenticated_user() {
    global $token;

    if (!$token) {
        return null;
    }

    return verify_jwt($token);
}

// Function to check if user has admin role
function is_admin() {
    $user = get_authenticated_user();

    if (!$user) {
        return false;
    }

    return $user['role'] === 'admin';
}

// Get JWT token from Authorization header
$auth_header = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
$token = null;

if (preg_match('/Bearer\s(\S+)/', $auth_header, $matches)) {
    $token = $matches[1];
}

// Function to check if user is premium
function isPremiumUser() {
    $user = get_authenticated_user();
    return $user && isset($user['is_premium']) && $user['is_premium'] == 1;
}

// Function to sanitize input data
function sanitize($data) {
    global $conn;
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    $data = mysqli_real_escape_string($conn, $data);
    return $data;
}
?>
