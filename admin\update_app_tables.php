<?php
// Include config file
require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Set page title
$page_title = 'অ্যাপ টেবিল আপডেট';

// Process form submission
$success_message = '';
$error_message = '';

// Check if tables exist
$app_config_exists = false;
$api_logs_exists = false;
$device_tokens_exists = false;
$notifications_exists = false;

// Check app_config table
$check_table = "SHOW TABLES LIKE 'app_config'";
$result = mysqli_query($conn, $check_table);
$app_config_exists = mysqli_num_rows($result) > 0;

// Check api_logs table
$check_table = "SHOW TABLES LIKE 'api_logs'";
$result = mysqli_query($conn, $check_table);
$api_logs_exists = mysqli_num_rows($result) > 0;

// Check device_tokens table
$check_table = "SHOW TABLES LIKE 'device_tokens'";
$result = mysqli_query($conn, $check_table);
$device_tokens_exists = mysqli_num_rows($result) > 0;

// Check notifications table
$check_table = "SHOW TABLES LIKE 'notifications'";
$result = mysqli_query($conn, $check_table);
$notifications_exists = mysqli_num_rows($result) > 0;

// Process update request
if (isset($_POST['update_tables'])) {
    $success = true;
    $messages = [];
    
    // Create app_config table if it doesn't exist
    if (!$app_config_exists) {
        $create_table = "CREATE TABLE IF NOT EXISTS app_config (
            id INT AUTO_INCREMENT PRIMARY KEY,
            config_key VARCHAR(100) NOT NULL,
            config_value TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_config_key (config_key)
        )";
        
        if (mysqli_query($conn, $create_table)) {
            $messages[] = 'app_config টেবিল সফলভাবে তৈরি করা হয়েছে।';
            
            // Insert default app config values
            $default_configs = [
                ['app_version', '1.0.0'],
                ['min_app_version', '1.0.0'],
                ['force_update', 'false'],
                ['update_message', 'Please update to the latest version for new features and bug fixes.'],
                ['maintenance_mode', 'false'],
                ['maintenance_message', 'We are currently performing maintenance. Please try again later.'],
                ['app_name', 'CinePix'],
                ['app_logo', 'https://cinepix.top/images/logo.png'],
                ['app_theme_color', '#E50914'],
                ['app_accent_color', '#0071EB']
            ];
            
            foreach ($default_configs as $config) {
                $insert_query = "INSERT INTO app_config (config_key, config_value) VALUES ('{$config[0]}', '{$config[1]}')";
                mysqli_query($conn, $insert_query);
            }
            
            $app_config_exists = true;
        } else {
            $success = false;
            $messages[] = 'app_config টেবিল তৈরি করতে ব্যর্থ: ' . mysqli_error($conn);
        }
    }
    
    // Create api_logs table if it doesn't exist
    if (!$api_logs_exists) {
        $create_table = "CREATE TABLE IF NOT EXISTS api_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            endpoint VARCHAR(255) NOT NULL,
            method VARCHAR(10) NOT NULL,
            user_id INT NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT NULL,
            status_code INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user (user_id),
            INDEX idx_endpoint (endpoint),
            INDEX idx_created_at (created_at)
        )";
        
        if (mysqli_query($conn, $create_table)) {
            $messages[] = 'api_logs টেবিল সফলভাবে তৈরি করা হয়েছে।';
            $api_logs_exists = true;
        } else {
            $success = false;
            $messages[] = 'api_logs টেবিল তৈরি করতে ব্যর্থ: ' . mysqli_error($conn);
        }
    }
    
    // Create device_tokens table if it doesn't exist
    if (!$device_tokens_exists) {
        $create_table = "CREATE TABLE IF NOT EXISTS device_tokens (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            device_token VARCHAR(255) NOT NULL,
            device_type ENUM('android', 'ios') NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            UNIQUE KEY unique_device_token (device_token)
        )";
        
        if (mysqli_query($conn, $create_table)) {
            $messages[] = 'device_tokens টেবিল সফলভাবে তৈরি করা হয়েছে।';
            $device_tokens_exists = true;
        } else {
            $success = false;
            $messages[] = 'device_tokens টেবিল তৈরি করতে ব্যর্থ: ' . mysqli_error($conn);
        }
    }
    
    // Create notifications table if it doesn't exist
    if (!$notifications_exists) {
        $create_table = "CREATE TABLE IF NOT EXISTS notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NULL,
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            image_url VARCHAR(255) NULL,
            action_type ENUM('none', 'movie', 'tvshow', 'url') DEFAULT 'none',
            action_id VARCHAR(100) NULL,
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )";
        
        if (mysqli_query($conn, $create_table)) {
            $messages[] = 'notifications টেবিল সফলভাবে তৈরি করা হয়েছে।';
            $notifications_exists = true;
        } else {
            $success = false;
            $messages[] = 'notifications টেবিল তৈরি করতে ব্যর্থ: ' . mysqli_error($conn);
        }
    }
    
    // Set success or error message
    if ($success) {
        $success_message = implode('<br>', $messages);
    } else {
        $error_message = implode('<br>', $messages);
    }
}

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title d-flex align-items-center">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1><?php echo $page_title; ?></h1>
            </div>

            <div class="topbar-actions">
                <div class="topbar-search">
                    <form action="search.php" method="GET">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="সার্চ করুন..." name="q">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>

                <div class="dropdown">
                    <button class="btn btn-link dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <img src="assets/img/user.png" class="rounded-circle" width="32" height="32" alt="User">
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user fa-sm me-2 text-gray-400"></i> প্রোফাইল</a></li>
                        <li><a class="dropdown-item" href="site_settings.php"><i class="fas fa-cogs fa-sm me-2 text-gray-400"></i> সেটিংস</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/logout.php"><i class="fas fa-sign-out-alt fa-sm me-2 text-gray-400"></i> লগআউট</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container-fluid">
        <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-lg-12">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">অ্যাপ টেবিল স্ট্যাটাস</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>টেবিল</th>
                                        <th>স্ট্যাটাস</th>
                                        <th>বিবরণ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><code>app_config</code></td>
                                        <td>
                                            <?php if ($app_config_exists): ?>
                                                <span class="badge bg-success">আছে</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">নেই</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>অ্যাপ কনফিগারেশন সেটিংস স্টোর করে</td>
                                    </tr>
                                    <tr>
                                        <td><code>api_logs</code></td>
                                        <td>
                                            <?php if ($api_logs_exists): ?>
                                                <span class="badge bg-success">আছে</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">নেই</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>API রিকোয়েস্ট লগ স্টোর করে</td>
                                    </tr>
                                    <tr>
                                        <td><code>device_tokens</code></td>
                                        <td>
                                            <?php if ($device_tokens_exists): ?>
                                                <span class="badge bg-success">আছে</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">নেই</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>মোবাইল ডিভাইস টোকেন স্টোর করে</td>
                                    </tr>
                                    <tr>
                                        <td><code>notifications</code></td>
                                        <td>
                                            <?php if ($notifications_exists): ?>
                                                <span class="badge bg-success">আছে</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">নেই</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>অ্যাপ নোটিফিকেশন স্টোর করে</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <?php if (!$app_config_exists || !$api_logs_exists || !$device_tokens_exists || !$notifications_exists): ?>
                            <form method="POST" action="" class="mt-4">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    কিছু প্রয়োজনীয় টেবিল নেই। অ্যাপ ম্যানেজমেন্ট ফিচার ব্যবহার করতে এই টেবিলগুলি তৈরি করুন।
                                </div>
                                <button type="submit" name="update_tables" class="btn btn-primary">
                                    <i class="fas fa-database me-2"></i>টেবিল তৈরি করুন
                                </button>
                            </form>
                        <?php else: ?>
                            <div class="alert alert-success mt-4">
                                <i class="fas fa-check-circle me-2"></i>
                                সব প্রয়োজনীয় টেবিল আছে। আপনি অ্যাপ ম্যানেজমেন্ট ফিচার ব্যবহার করতে পারেন।
                            </div>
                            <div class="mt-3">
                                <a href="app_management.php" class="btn btn-primary me-2">
                                    <i class="fas fa-cog me-2"></i>অ্যাপ সেটিংস
                                </a>
                                <a href="app_statistics.php" class="btn btn-info me-2">
                                    <i class="fas fa-chart-bar me-2"></i>অ্যাপ স্ট্যাটিসটিক্স
                                </a>
                                <a href="app_notifications.php" class="btn btn-warning">
                                    <i class="fas fa-bell me-2"></i>অ্যাপ নোটিফিকেশন
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Table Descriptions -->
        <div class="row">
            <div class="col-lg-12">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">টেবিল বিবরণ</h6>
                    </div>
                    <div class="card-body">
                        <div class="accordion" id="tableDescriptions">
                            <!-- app_config Table -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="headingAppConfig">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseAppConfig" aria-expanded="false" aria-controls="collapseAppConfig">
                                        <strong>app_config</strong> - অ্যাপ কনফিগারেশন টেবিল
                                    </button>
                                </h2>
                                <div id="collapseAppConfig" class="accordion-collapse collapse" aria-labelledby="headingAppConfig" data-bs-parent="#tableDescriptions">
                                    <div class="accordion-body">
                                        <p>এই টেবিল অ্যাপ কনফিগারেশন সেটিংস স্টোর করে, যেমন:</p>
                                        <ul>
                                            <li><code>app_version</code> - বর্তমান অ্যাপ ভার্সন</li>
                                            <li><code>min_app_version</code> - সর্বনিম্ন সমর্থিত ভার্সন</li>
                                            <li><code>force_update</code> - বাধ্যতামূলক আপডেট কিনা</li>
                                            <li><code>update_message</code> - আপডেট মেসেজ</li>
                                            <li><code>maintenance_mode</code> - মেইনটেন্যান্স মোড চালু কিনা</li>
                                            <li><code>maintenance_message</code> - মেইনটেন্যান্স মেসেজ</li>
                                        </ul>
                                        <pre class="bg-light p-3 rounded"><code>CREATE TABLE app_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL,
    config_value TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_config_key (config_key)
)</code></pre>
                                    </div>
                                </div>
                            </div>

                            <!-- api_logs Table -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="headingApiLogs">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseApiLogs" aria-expanded="false" aria-controls="collapseApiLogs">
                                        <strong>api_logs</strong> - API লগ টেবিল
                                    </button>
                                </h2>
                                <div id="collapseApiLogs" class="accordion-collapse collapse" aria-labelledby="headingApiLogs" data-bs-parent="#tableDescriptions">
                                    <div class="accordion-body">
                                        <p>এই টেবিল API রিকোয়েস্ট লগ স্টোর করে, যা অ্যাপ ব্যবহার সম্পর্কে তথ্য প্রদান করে।</p>
                                        <pre class="bg-light p-3 rounded"><code>CREATE TABLE api_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    user_id INT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    status_code INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user (user_id),
    INDEX idx_endpoint (endpoint),
    INDEX idx_created_at (created_at)
)</code></pre>
                                    </div>
                                </div>
                            </div>

                            <!-- device_tokens Table -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="headingDeviceTokens">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseDeviceTokens" aria-expanded="false" aria-controls="collapseDeviceTokens">
                                        <strong>device_tokens</strong> - ডিভাইস টোকেন টেবিল
                                    </button>
                                </h2>
                                <div id="collapseDeviceTokens" class="accordion-collapse collapse" aria-labelledby="headingDeviceTokens" data-bs-parent="#tableDescriptions">
                                    <div class="accordion-body">
                                        <p>এই টেবিল মোবাইল ডিভাইস টোকেন স্টোর করে, যা পুশ নোটিফিকেশন পাঠানোর জন্য ব্যবহৃত হয়।</p>
                                        <pre class="bg-light p-3 rounded"><code>CREATE TABLE device_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    device_token VARCHAR(255) NOT NULL,
    device_type ENUM('android', 'ios') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_device_token (device_token)
)</code></pre>
                                    </div>
                                </div>
                            </div>

                            <!-- notifications Table -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="headingNotifications">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseNotifications" aria-expanded="false" aria-controls="collapseNotifications">
                                        <strong>notifications</strong> - নোটিফিকেশন টেবিল
                                    </button>
                                </h2>
                                <div id="collapseNotifications" class="accordion-collapse collapse" aria-labelledby="headingNotifications" data-bs-parent="#tableDescriptions">
                                    <div class="accordion-body">
                                        <p>এই টেবিল অ্যাপ নোটিফিকেশন স্টোর করে, যা ব্যবহারকারীদের কাছে পাঠানো হয়।</p>
                                        <pre class="bg-light p-3 rounded"><code>CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    image_url VARCHAR(255) NULL,
    action_type ENUM('none', 'movie', 'tvshow', 'url') DEFAULT 'none',
    action_id VARCHAR(100) NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
)</code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
