import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cinepix_app/constants/app_constants.dart';
import 'package:cinepix_app/services/download_manager.dart';
import 'package:cinepix_app/services/storage_service.dart';

class DownloadSettingsScreen extends StatefulWidget {
  const DownloadSettingsScreen({super.key});

  @override
  State<DownloadSettingsScreen> createState() => _DownloadSettingsScreenState();
}

class _DownloadSettingsScreenState extends State<DownloadSettingsScreen> {
  final DownloadManager _downloadManager = DownloadManager();
  final StorageService _storageService = StorageService();
  
  bool _isLoading = true;
  
  // Settings values
  int _autoDeleteDays = 7;
  int _maxStorageGB = 5;
  String _downloadQuality = 'auto';
  bool _autoDownloadEnabled = true;
  bool _downloadOnWifiOnly = true;
  
  final List<int> _autoDeleteOptions = [0, 1, 3, 7, 14, 30]; // 0 means never
  final List<int> _storageOptions = [1, 2, 5, 10, 20, 50];
  final List<String> _qualityOptions = ['auto', '720p', '1080p'];

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load current settings from DownloadManager
      _autoDeleteDays = _downloadManager.autoDeleteDays;
      _maxStorageGB = _downloadManager.maxStorageGB;
      _downloadQuality = _downloadManager.downloadQuality;
      
      // Load additional settings from storage
      _autoDownloadEnabled = await _storageService.getBool('auto_download_enabled') ?? true;
      _downloadOnWifiOnly = await _storageService.getBool('download_wifi_only') ?? true;
      
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      Get.snackbar(
        'Error',
        'Failed to load settings: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _saveSettings() async {
    try {
      // Update DownloadManager settings
      await _downloadManager.updateSettings(
        autoDeleteDays: _autoDeleteDays,
        maxStorageGB: _maxStorageGB,
        downloadQuality: _downloadQuality,
      );
      
      // Save additional settings to storage
      await _storageService.saveBool('auto_download_enabled', _autoDownloadEnabled);
      await _storageService.saveBool('download_wifi_only', _downloadOnWifiOnly);
      
      Get.snackbar(
        'Success',
        'Settings saved successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to save settings: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _clearAllDownloads() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Downloads'),
        content: const Text(
          'Are you sure you want to delete all downloaded videos? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Clear All', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final downloads = await _downloadManager.getDownloadedItems();
        for (final download in downloads) {
          await _downloadManager.deleteDownload(download.id);
        }
        
        Get.snackbar(
          'Success',
          'All downloads cleared successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } catch (e) {
        Get.snackbar(
          'Error',
          'Failed to clear downloads: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Download Settings'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveSettings,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // Auto Download Section
                _buildSectionHeader('Auto Download'),
                Card(
                  child: Column(
                    children: [
                      SwitchListTile(
                        title: const Text('Enable Auto Download'),
                        subtitle: const Text('Automatically download videos when playing'),
                        value: _autoDownloadEnabled,
                        onChanged: (value) {
                          setState(() {
                            _autoDownloadEnabled = value;
                          });
                        },
                      ),
                      const Divider(height: 1),
                      SwitchListTile(
                        title: const Text('WiFi Only'),
                        subtitle: const Text('Download only when connected to WiFi'),
                        value: _downloadOnWifiOnly,
                        onChanged: (value) {
                          setState(() {
                            _downloadOnWifiOnly = value;
                          });
                        },
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Quality Section
                _buildSectionHeader('Download Quality'),
                Card(
                  child: ListTile(
                    title: const Text('Video Quality'),
                    subtitle: Text('Current: $_downloadQuality'),
                    trailing: DropdownButton<String>(
                      value: _downloadQuality,
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _downloadQuality = value;
                          });
                        }
                      },
                      items: _qualityOptions.map((quality) {
                        return DropdownMenuItem(
                          value: quality,
                          child: Text(quality.toUpperCase()),
                        );
                      }).toList(),
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Storage Section
                _buildSectionHeader('Storage Management'),
                Card(
                  child: Column(
                    children: [
                      ListTile(
                        title: const Text('Max Storage'),
                        subtitle: Text('Current: ${_maxStorageGB}GB'),
                        trailing: DropdownButton<int>(
                          value: _maxStorageGB,
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _maxStorageGB = value;
                              });
                            }
                          },
                          items: _storageOptions.map((size) {
                            return DropdownMenuItem(
                              value: size,
                              child: Text('${size}GB'),
                            );
                          }).toList(),
                        ),
                      ),
                      const Divider(height: 1),
                      ListTile(
                        title: const Text('Auto Delete'),
                        subtitle: Text(_autoDeleteDays == 0 
                          ? 'Never delete automatically' 
                          : 'Delete after $_autoDeleteDays days'),
                        trailing: DropdownButton<int>(
                          value: _autoDeleteDays,
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _autoDeleteDays = value;
                              });
                            }
                          },
                          items: _autoDeleteOptions.map((days) {
                            return DropdownMenuItem(
                              value: days,
                              child: Text(days == 0 ? 'Never' : '$days days'),
                            );
                          }).toList(),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Actions Section
                _buildSectionHeader('Actions'),
                Card(
                  child: ListTile(
                    title: const Text('Clear All Downloads'),
                    subtitle: const Text('Delete all downloaded videos'),
                    leading: const Icon(Icons.delete_forever, color: Colors.red),
                    onTap: _clearAllDownloads,
                  ),
                ),

                const SizedBox(height: 24),

                // Info Section
                Card(
                  color: Colors.blue.withOpacity(0.1),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.info, color: Colors.blue[400]),
                            const SizedBox(width: 8),
                            Text(
                              'Download Information',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue[400],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        const Text(
                          '• Videos are automatically downloaded when you start watching\n'
                          '• Downloaded videos can be played offline\n'
                          '• Auto-delete helps manage storage space\n'
                          '• WiFi-only download saves mobile data',
                          style: TextStyle(fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppConstants.primaryColor,
        ),
      ),
    );
  }
}
