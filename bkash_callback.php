<?php
// Start output buffering to prevent 'headers already sent' errors
ob_start();

require_once 'includes/config.php';

// Log all GET parameters for debugging
error_log("bKash callback received: " . json_encode($_GET));

// Check if we have the necessary parameters
if (!isset($_GET['paymentID']) || !isset($_GET['status'])) {
    error_log("bKash callback missing required parameters");
    redirect(SITE_URL . '/premium.php');
}

$bkash_payment_id = $_GET['paymentID'];
$status = $_GET['status'];

// Get the payment ID from session
if (!isset($_SESSION['payment_id'])) {
    error_log("bKash callback: payment_id not found in session");
    redirect(SITE_URL . '/premium.php');
}

$payment_id = $_SESSION['payment_id'];
error_log("bKash callback processing payment ID: $payment_id");

// Get payment details
$payment_query = "SELECT p.*, s.plan_id, pp.name as plan_name, pp.price
                 FROM payments p
                 JOIN subscriptions s ON p.subscription_id = s.id
                 JOIN premium_plans pp ON s.plan_id = pp.id
                 WHERE p.id = $payment_id AND p.user_id = {$_SESSION['user_id']}";
$payment_result = mysqli_query($conn, $payment_query);

if (mysqli_num_rows($payment_result) == 0) {
    error_log("bKash callback: payment not found in database");
    redirect(SITE_URL . '/premium.php');
}

$payment = mysqli_fetch_assoc($payment_result);

// bKash Sandbox API credentials
$bkash_app_key = '5tunt4masn6pv2hnvte1sb5n3j';
$bkash_app_secret = '1vggbqd4hqk9g96o9rrrp2jftvek578v7d2bnerim12a87dbrrka';
$bkash_username = 'sandboxTestUser';
$bkash_password = 'hWD@8vtzw0';
$bkash_sandbox_url = 'https://checkout.sandbox.bka.sh/v1.2.0-beta';

// Debug API credentials
error_log("bKash callback API credentials (direct): APP_KEY=$bkash_app_key, USERNAME=$bkash_username");

// Function to get bKash token
function getBkashToken($url, $app_key, $app_secret, $username, $password) {
    // Debug information
    error_log("bKash callback token request parameters: URL=$url, APP_KEY=$app_key, USERNAME=$username");

    $post_token = array(
        'app_key' => $app_key,
        'app_secret' => $app_secret,
        'username' => $username,
        'password' => $password
    );

    // Debug the request payload
    error_log("bKash callback token request payload: " . json_encode($post_token));

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url . '/checkout/token/grant');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post_token));
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
        'Accept: application/json'
    ));

    // Add SSL verification options
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    $response = curl_exec($ch);

    // Check for cURL errors
    if(curl_errno($ch)) {
        error_log("bKash callback cURL Error: " . curl_error($ch));
    }

    curl_close($ch);

    // Debug the response
    error_log("bKash callback token response: " . $response);

    return json_decode($response, true);
}

// Function to execute bKash payment
function executeBkashPayment($url, $token, $paymentID) {
    $post_data = array(
        'paymentID' => $paymentID
    );

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url . '/checkout/payment/execute');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
        'Accept: application/json',
        'Authorization: ' . $token,
        'X-APP-Key: ' . BKASH_APP_KEY
    ));

    $response = curl_exec($ch);
    curl_close($ch);

    return json_decode($response, true);
}

// Process the callback
$payment_status = '';
$payment_message = '';

// Get token
$token_response = getBkashToken($bkash_sandbox_url, $bkash_app_key, $bkash_app_secret, $bkash_username, $bkash_password);
error_log("bKash token response: " . json_encode($token_response));

if (isset($token_response['id_token'])) {
    $bkash_token = $token_response['id_token'];

    if ($status == 'success') {
        // Execute payment
        $execute_response = executeBkashPayment($bkash_sandbox_url, $bkash_token, $bkash_payment_id);
        error_log("bKash execute payment response: " . json_encode($execute_response));

        if (isset($execute_response['transactionStatus']) && $execute_response['transactionStatus'] == 'Completed') {
            // Begin transaction
            mysqli_begin_transaction($conn);

            try {
                // Update payment status to completed
                $bkash_transaction_id = $execute_response['trxID'];
                $update_query = "UPDATE payments SET payment_status = 'completed', transaction_id = '$bkash_transaction_id' WHERE id = $payment_id";
                $update_result = mysqli_query($conn, $update_query);

                if (!$update_result) {
                    throw new Exception("Failed to update payment: " . mysqli_error($conn));
                }

                // Update user to premium
                $update_user_query = "UPDATE users SET is_premium = TRUE WHERE id = {$_SESSION['user_id']}";
                $update_user_result = mysqli_query($conn, $update_user_query);

                if (!$update_user_result) {
                    throw new Exception("Failed to update user: " . mysqli_error($conn));
                }

                // Set session variable
                $_SESSION['is_premium'] = true;

                // Commit transaction
                mysqli_commit($conn);

                $payment_status = 'success';
                $payment_message = 'Payment successful! Your premium subscription is now active.';

                // Log the successful payment
                error_log("bKash payment successful for payment ID: $payment_id, Transaction ID: $bkash_transaction_id");

                // Redirect to success page
                redirect(SITE_URL . '/bkash_payment.php?payment_id=' . $payment_id . '&status=success&bkash_payment_id=' . $bkash_payment_id);
            } catch (Exception $e) {
                // Rollback transaction on error
                mysqli_rollback($conn);

                error_log("bKash payment transaction failed: " . $e->getMessage());

                // Redirect to failed page
                redirect(SITE_URL . '/bkash_payment.php?payment_id=' . $payment_id . '&status=failed&bkash_payment_id=' . $bkash_payment_id . '&error=' . urlencode($e->getMessage()));
            }
        } else {
            // Update payment status to failed
            $update_query = "UPDATE payments SET payment_status = 'failed' WHERE id = $payment_id";
            mysqli_query($conn, $update_query);

            $payment_status = 'failed';
            $payment_message = 'Payment execution failed. Please try again or choose a different payment method.';

            // Log the error
            error_log("bKash payment execution failed: " . json_encode($execute_response));

            // Redirect to failed page
            redirect(SITE_URL . '/bkash_payment.php?payment_id=' . $payment_id . '&status=failed&bkash_payment_id=' . $bkash_payment_id);
        }
    } else {
        // Update payment status to failed
        $update_query = "UPDATE payments SET payment_status = 'failed' WHERE id = $payment_id";
        mysqli_query($conn, $update_query);

        $payment_status = 'failed';
        $payment_message = 'Payment failed. Please try again or choose a different payment method.';

        // Log the error
        error_log("bKash payment failed with status: $status");

        // Redirect to failed page
        redirect(SITE_URL . '/bkash_payment.php?payment_id=' . $payment_id . '&status=failed&bkash_payment_id=' . $bkash_payment_id);
    }
} else {
    $payment_status = 'failed';
    $payment_message = 'Could not authenticate with bKash. Please try again later.';

    // Log the error
    error_log("bKash token error: " . json_encode($token_response));

    // Redirect to failed page
    redirect(SITE_URL . '/bkash_payment.php?payment_id=' . $payment_id . '&status=failed');
}

// If we get here, something went wrong with the redirects
redirect(SITE_URL . '/premium.php');
?>
