<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Starting debug...<br>";

try {
    echo "1. Loading config...<br>";
    require_once 'includes/config.php';
    echo "✅ Config loaded<br>";
    
    echo "2. Loading streaming helper...<br>";
    require_once 'includes/streaming_helper.php';
    echo "✅ Streaming helper loaded<br>";
    
    echo "3. Loading ad placeholder...<br>";
    require_once 'includes/ad_placeholder.php';
    echo "✅ Ad placeholder loaded<br>";
    
    echo "4. Loading SEO helper...<br>";
    require_once 'includes/seo_helper.php';
    echo "✅ SEO helper loaded<br>";
    
    echo "5. Loading SEO content...<br>";
    require_once 'includes/seo_content.php';
    echo "✅ SEO content loaded<br>";
    
    echo "6. Checking GET parameter...<br>";
    if (!isset($_GET['id'])) {
        echo "❌ No ID provided. Use: debug_movie.php?id=1<br>";
        exit;
    }
    echo "✅ ID parameter found: " . $_GET['id'] . "<br>";
    
    $id = (int)$_GET['id'];
    echo "7. ID converted to integer: $id<br>";
    
    echo "8. Checking database connection...<br>";
    if (!$conn) {
        echo "❌ Database connection failed<br>";
        exit;
    }
    echo "✅ Database connected<br>";
    
    echo "9. Preparing movie query...<br>";
    $query = "SELECT m.*, c.name as category_name, m.premium_only FROM movies m
              LEFT JOIN categories c ON m.category_id = c.id
              WHERE m.id = $id";
    echo "Query: $query<br>";
    
    echo "10. Executing query...<br>";
    $result = mysqli_query($conn, $query);
    
    if (!$result) {
        echo "❌ Query failed: " . mysqli_error($conn) . "<br>";
        exit;
    }
    echo "✅ Query executed successfully<br>";
    
    echo "11. Checking if movie exists...<br>";
    if (mysqli_num_rows($result) == 0) {
        echo "❌ No movie found with ID: $id<br>";
        exit;
    }
    echo "✅ Movie found<br>";
    
    echo "12. Fetching movie data...<br>";
    $movie = mysqli_fetch_assoc($result);
    echo "✅ Movie data fetched: " . $movie['title'] . "<br>";
    
    echo "13. Setting SEO variables...<br>";
    $page_title = $movie['title'] . " (" . $movie['release_year'] . ") - Download Full Movie | " . SITE_NAME;
    $page_description = "Download " . $movie['title'] . " (" . $movie['release_year'] . ") full movie in HD quality.";
    $page_keywords = $movie['title'] . ", download, full movie";
    $page_image = SITE_URL . "/uploads/" . $movie['poster'];
    $canonical_url = SITE_URL . "/movie_details.php?id=" . $movie['id'];
    echo "✅ SEO variables set<br>";
    
    echo "14. Testing SEO functions...<br>";
    if (function_exists('getMovieKeywords')) {
        echo "✅ getMovieKeywords function exists<br>";
        try {
            $keywords = getMovieKeywords($movie);
            echo "✅ Keywords generated: $keywords<br>";
        } catch (Exception $e) {
            echo "❌ Error in getMovieKeywords: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "❌ getMovieKeywords function not found<br>";
    }
    
    if (function_exists('getMovieSeoUrl')) {
        echo "✅ getMovieSeoUrl function exists<br>";
        try {
            $seo_url = getMovieSeoUrl($movie['id'], $movie['title'], $movie['release_year']);
            echo "✅ SEO URL generated: $seo_url<br>";
        } catch (Exception $e) {
            echo "❌ Error in getMovieSeoUrl: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "❌ getMovieSeoUrl function not found<br>";
    }
    
    echo "15. Loading header...<br>";
    require_once 'includes/header.php';
    echo "✅ Header loaded successfully<br>";
    
    echo "<h1>🎉 All checks passed!</h1>";
    echo "<p>Movie: " . $movie['title'] . " (" . $movie['release_year'] . ")</p>";
    echo "<p>Category: " . $movie['category_name'] . "</p>";
    echo "<p>Description: " . substr($movie['description'], 0, 100) . "...</p>";
    
} catch (Exception $e) {
    echo "❌ Fatal Error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
} catch (Error $e) {
    echo "❌ PHP Error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}

h1 {
    color: #28a745;
}

p {
    margin: 5px 0;
}
</style>
