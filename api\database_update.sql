-- Database Update SQL for Mobile App Integration

-- 1. Create device_tokens table
CREATE TABLE IF NOT EXISTS device_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    device_token VARCHAR(255) NOT NULL,
    device_type ENUM('android', 'ios') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_device_token (device_token)
);

-- 2. Create app_config table
CREATE TABLE IF NOT EXISTS app_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL,
    config_value TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_config_key (config_key)
);

-- 3. Create notifications table
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    image_url VARCHAR(255) NULL,
    action_type ENUM('none', 'movie', 'tvshow', 'url') DEFAULT 'none',
    action_id VARCHAR(100) NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 4. Create subtitles table
CREATE TABLE IF NOT EXISTS subtitles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    content_type ENUM('movie', 'episode') NOT NULL,
    content_id INT NOT NULL,
    language VARCHAR(50) NOT NULL,
    url VARCHAR(255) NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_content (content_type, content_id)
);

-- 5. Create api_logs table
CREATE TABLE IF NOT EXISTS api_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    user_id INT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    status_code INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user (user_id),
    INDEX idx_endpoint (endpoint),
    INDEX idx_created_at (created_at)
);

-- 6. Update watchlist table
ALTER TABLE watchlist 
ADD COLUMN IF NOT EXISTS last_watched_position INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_watched_at TIMESTAMP NULL;

-- 7. Update users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS profile_image VARCHAR(255) NULL;

-- 8. Update download_links table
ALTER TABLE download_links 
ADD COLUMN IF NOT EXISTS server_name VARCHAR(100) NULL,
ADD COLUMN IF NOT EXISTS file_size VARCHAR(20) NULL;

-- 9. Insert default app_config values
INSERT IGNORE INTO app_config (config_key, config_value) VALUES 
('app_version', '1.0.0'),
('min_app_version', '1.0.0'),
('force_update', 'false'),
('update_message', 'Please update to the latest version for new features and bug fixes.'),
('maintenance_mode', 'false'),
('maintenance_message', 'We are currently performing maintenance. Please try again later.');

-- 10. Create payment_settings table
CREATE TABLE IF NOT EXISTS payment_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    bkash_enabled BOOLEAN DEFAULT TRUE,
    bkash_merchant_number VARCHAR(20) NULL,
    bkash_merchant_name VARCHAR(100) NULL,
    nagad_enabled BOOLEAN DEFAULT TRUE,
    nagad_merchant_number VARCHAR(20) NULL,
    nagad_merchant_name VARCHAR(100) NULL,
    rocket_enabled BOOLEAN DEFAULT TRUE,
    rocket_merchant_number VARCHAR(20) NULL,
    rocket_merchant_name VARCHAR(100) NULL,
    payment_instructions TEXT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default payment settings if table is empty
INSERT INTO payment_settings (
    bkash_enabled, bkash_merchant_number, bkash_merchant_name,
    nagad_enabled, nagad_merchant_number, nagad_merchant_name,
    rocket_enabled, rocket_merchant_number, rocket_merchant_name,
    payment_instructions
)
SELECT 1, '01XXXXXXXXX', 'CinePix',
       1, '01XXXXXXXXX', 'CinePix',
       1, '01XXXXXXXXX', 'CinePix',
       'Please send the exact amount to the merchant number and provide the transaction ID.'
FROM dual
WHERE NOT EXISTS (SELECT 1 FROM payment_settings);

-- 11. Create payment_requests table
CREATE TABLE IF NOT EXISTS payment_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    plan_id INT NOT NULL,
    payment_method VARCHAR(20) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_id VARCHAR(50) NOT NULL,
    transaction_id VARCHAR(100) NULL,
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES premium_plans(id) ON DELETE CASCADE
);

-- 12. Create subscriptions table
CREATE TABLE IF NOT EXISTS subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    plan_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    payment_method VARCHAR(20) NOT NULL,
    transaction_id VARCHAR(100) NULL,
    status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES premium_plans(id) ON DELETE CASCADE
);

-- 13. Create favorites table
CREATE TABLE IF NOT EXISTS favorites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    content_type ENUM('movie', 'tvshow') NOT NULL,
    content_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_favorite (user_id, content_type, content_id)
);
