<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include direct config file
require_once '../direct_config.php';

// Simple config endpoint without using the router
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization, API-Key');
header('Content-Type: application/json; charset=UTF-8');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Check if app_config table exists
$check_table = "SHOW TABLES LIKE 'app_config'";
$result = mysqli_query($conn, $check_table);

if (mysqli_num_rows($result) == 0) {
    // Create app_config table
    $create_table = "CREATE TABLE IF NOT EXISTS app_config (
        id INT AUTO_INCREMENT PRIMARY KEY,
        config_key VARCHAR(100) NOT NULL,
        config_value TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_config_key (config_key)
    )";

    if (!mysqli_query($conn, $create_table)) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to create app_config table: ' . mysqli_error($conn),
            'data' => null
        ]);
        exit;
    }

    // Insert default app config values
    $default_configs = [
        ['app_version', '1.0.0'],
        ['min_app_version', '1.0.0'],
        ['force_update', 'false'],
        ['update_message', 'Please update to the latest version for new features and bug fixes.'],
        ['maintenance_mode', 'false'],
        ['maintenance_message', 'We are currently performing maintenance. Please try again later.'],
        ['app_name', 'CinePix'],
        ['app_logo', 'https://cinepix.top/images/logo.png'],
        ['app_theme_color', '#E50914'],
        ['app_accent_color', '#0071EB']
    ];

    foreach ($default_configs as $config) {
        $insert_query = "INSERT INTO app_config (config_key, config_value) VALUES ('{$config[0]}', '{$config[1]}')";
        mysqli_query($conn, $insert_query);
    }
}

// Check if premium_plans table exists
$check_table = "SHOW TABLES LIKE 'premium_plans'";
$result = mysqli_query($conn, $check_table);

if (mysqli_num_rows($result) == 0) {
    // Create premium_plans table
    $create_table = "CREATE TABLE IF NOT EXISTS premium_plans (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        duration INT NOT NULL,
        features TEXT NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";

    if (!mysqli_query($conn, $create_table)) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to create premium_plans table: ' . mysqli_error($conn),
            'data' => null
        ]);
        exit;
    }

    // Insert default plans
    $sql = "INSERT INTO premium_plans (name, price, duration, features, status) VALUES
        ('Basic', 30, 30, 'Access to premium movies\nAccess to premium TV shows\nHD quality', 'active'),
        ('Standard', 60, 60, 'Access to premium movies\nAccess to premium TV shows\nFull HD quality\nPriority support', 'active'),
        ('Premium', 90, 90, 'Access to premium movies\nAccess to premium TV shows\n4K quality\nPriority support\nEarly access to new content', 'active')";

    mysqli_query($conn, $sql);
}

// Check if payment_settings table exists
$check_table = "SHOW TABLES LIKE 'payment_settings'";
$result = mysqli_query($conn, $check_table);

if (mysqli_num_rows($result) == 0) {
    // Create payment_settings table
    $create_table = "CREATE TABLE IF NOT EXISTS payment_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        bkash_enabled BOOLEAN DEFAULT TRUE,
        bkash_merchant_number VARCHAR(20) NULL,
        bkash_merchant_name VARCHAR(100) NULL,
        nagad_enabled BOOLEAN DEFAULT TRUE,
        nagad_merchant_number VARCHAR(20) NULL,
        nagad_merchant_name VARCHAR(100) NULL,
        rocket_enabled BOOLEAN DEFAULT TRUE,
        rocket_merchant_number VARCHAR(20) NULL,
        rocket_merchant_name VARCHAR(100) NULL,
        payment_instructions TEXT NULL,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";

    if (!mysqli_query($conn, $create_table)) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to create payment_settings table: ' . mysqli_error($conn),
            'data' => null
        ]);
        exit;
    }

    // Insert default payment settings
    $sql = "INSERT INTO payment_settings (
        bkash_enabled, bkash_merchant_number, bkash_merchant_name,
        nagad_enabled, nagad_merchant_number, nagad_merchant_name,
        rocket_enabled, rocket_merchant_number, rocket_merchant_name,
        payment_instructions
    ) VALUES (
        1, '01XXXXXXXXX', 'CinePix',
        1, '01XXXXXXXXX', 'CinePix',
        1, '01XXXXXXXXX', 'CinePix',
        'Please send the exact amount to the merchant number and provide the transaction ID.'
    )";

    mysqli_query($conn, $sql);
}

// Get app configurations
$query = "SELECT config_key, config_value FROM app_config";
$result = mysqli_query($conn, $query);

if (!$result) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to fetch app configuration: ' . mysqli_error($conn),
        'data' => null
    ]);
    exit;
}

$config = [];
while ($row = mysqli_fetch_assoc($result)) {
    $config[$row['config_key']] = $row['config_value'];
}

// Add additional app information
$config['api_version'] = 'v1';
$config['site_name'] = SITE_NAME;
$config['site_url'] = SITE_URL;

// Add API endpoints
$config['api_endpoints'] = [
    'base_url' => SITE_URL . '/api/v1',
    'log_usage' => SITE_URL . '/api/v1/log_app_usage.php',
    'register_device' => SITE_URL . '/api/v1/register_device.php',
    'get_notifications' => SITE_URL . '/api/v1/get_notifications.php',
    'movies' => SITE_URL . '/api/v1/direct_movies.php',
    'movie_details' => SITE_URL . '/api/v1/direct_movie_details.php',
    'tvshows' => SITE_URL . '/api/v1/direct_tvshows.php',
    'tvshow_details' => SITE_URL . '/api/v1/direct_tvshow_details.php',
    'search' => SITE_URL . '/api/v1/direct_search.php',
    'login' => SITE_URL . '/api/v1/direct_login.php',
    'register' => SITE_URL . '/api/v1/direct_register.php',
    'user_profile' => SITE_URL . '/api/v1/direct_user_profile.php'
];

// Get premium plans
$plans_query = "SELECT id, name, price, duration, features FROM premium_plans WHERE status = 'active' ORDER BY price ASC";
$plans_result = mysqli_query($conn, $plans_query);

if ($plans_result) {
    $premium_plans = [];
    while ($plan = mysqli_fetch_assoc($plans_result)) {
        $premium_plans[] = [
            'id' => (int)$plan['id'],
            'name' => $plan['name'],
            'price' => (float)$plan['price'],
            'duration' => (int)$plan['duration'],
            'features' => explode("\n", $plan['features'])
        ];
    }
    $config['premium_plans'] = $premium_plans;
}

// Get payment methods
$payment_query = "SELECT * FROM payment_settings WHERE id = 1";
$payment_result = mysqli_query($conn, $payment_query);

if ($payment_result && mysqli_num_rows($payment_result) > 0) {
    $payment_settings = mysqli_fetch_assoc($payment_result);
    $payment_methods = [];

    if ($payment_settings['bkash_enabled']) {
        $payment_methods[] = [
            'id' => 'bkash',
            'name' => 'বিকাশ',
            'merchant_number' => $payment_settings['bkash_merchant_number'],
            'merchant_name' => $payment_settings['bkash_merchant_name'],
            'is_automatic' => true
        ];
    }

    if ($payment_settings['nagad_enabled']) {
        $payment_methods[] = [
            'id' => 'nagad',
            'name' => 'নগদ',
            'merchant_number' => $payment_settings['nagad_merchant_number'],
            'merchant_name' => $payment_settings['nagad_merchant_name'],
            'is_automatic' => false
        ];
    }

    if ($payment_settings['rocket_enabled']) {
        $payment_methods[] = [
            'id' => 'rocket',
            'name' => 'রকেট',
            'merchant_number' => $payment_settings['rocket_merchant_number'],
            'merchant_name' => $payment_settings['rocket_merchant_name'],
            'is_automatic' => false
        ];
    }

    $config['payment_methods'] = $payment_methods;
    $config['payment_instructions'] = $payment_settings['payment_instructions'];
}

// Return app configuration
http_response_code(200);
echo json_encode([
    'success' => true,
    'message' => 'Success',
    'data' => $config
]);
?>
