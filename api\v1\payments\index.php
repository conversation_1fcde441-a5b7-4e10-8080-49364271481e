<?php
// API Payments Endpoints

// Get the request
global $request;

// Check if user is authenticated
if (!is_authenticated()) {
    api_error('Authentication required', 401);
}

// Get authenticated user
$current_user = get_authenticated_user();

// Handle different endpoints based on the path parts
$action = $request['parts'][0] ?? 'default';

switch ($action) {
    case 'plans':
        handle_get_plans($request);
        break;
    
    case 'initiate':
        handle_initiate_payment($current_user, $request);
        break;
    
    case 'verify':
        handle_verify_payment($current_user, $request);
        break;
    
    case 'history':
        handle_payment_history($current_user, $request);
        break;
    
    case 'check':
        handle_check_payment($current_user, $request);
        break;
    
    default:
        api_error('Invalid payments endpoint', 404);
}

// Handle get premium plans
function handle_get_plans($request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get premium plans
    $query = "SELECT * FROM premium_plans WHERE status = 'active' ORDER BY price ASC";
    $result = mysqli_query($conn, $query);
    
    $plans = [];
    while ($plan = mysqli_fetch_assoc($result)) {
        $plans[] = [
            'id' => (int)$plan['id'],
            'name' => $plan['name'],
            'price' => (float)$plan['price'],
            'duration' => (int)$plan['duration'],
            'features' => explode("\n", $plan['features']),
            'status' => $plan['status']
        ];
    }
    
    // Get payment methods
    $payment_query = "SELECT * FROM payment_settings WHERE id = 1";
    $payment_result = mysqli_query($conn, $payment_query);
    
    $payment_methods = [];
    if (mysqli_num_rows($payment_result) > 0) {
        $payment_settings = mysqli_fetch_assoc($payment_result);
        
        if ($payment_settings['bkash_enabled']) {
            $payment_methods[] = [
                'id' => 'bkash',
                'name' => 'বিকাশ',
                'merchant_number' => $payment_settings['bkash_merchant_number'],
                'merchant_name' => $payment_settings['bkash_merchant_name'],
                'is_automatic' => true
            ];
        }
        
        if ($payment_settings['nagad_enabled']) {
            $payment_methods[] = [
                'id' => 'nagad',
                'name' => 'নগদ',
                'merchant_number' => $payment_settings['nagad_merchant_number'],
                'merchant_name' => $payment_settings['nagad_merchant_name'],
                'is_automatic' => false
            ];
        }
        
        if ($payment_settings['rocket_enabled']) {
            $payment_methods[] = [
                'id' => 'rocket',
                'name' => 'রকেট',
                'merchant_number' => $payment_settings['rocket_merchant_number'],
                'merchant_name' => $payment_settings['rocket_merchant_name'],
                'is_automatic' => false
            ];
        }
    }
    
    // Return premium plans and payment methods
    api_response([
        'plans' => $plans,
        'payment_methods' => $payment_methods
    ]);
}

// Handle initiate payment
function handle_initiate_payment($user, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'POST') {
        api_error('Method not allowed', 405);
    }
    
    // Validate required fields
    if (empty($request['body']['plan_id']) || empty($request['body']['payment_method'])) {
        api_error('Plan ID and payment method are required', 400);
    }
    
    $plan_id = (int)$request['body']['plan_id'];
    $payment_method = $request['body']['payment_method'];
    
    // Validate payment method
    if (!in_array($payment_method, ['bkash', 'nagad', 'rocket'])) {
        api_error('Invalid payment method', 400);
    }
    
    // Get plan details
    $plan_query = "SELECT * FROM premium_plans WHERE id = ? AND status = 'active'";
    $plan_stmt = mysqli_prepare($conn, $plan_query);
    mysqli_stmt_bind_param($plan_stmt, 'i', $plan_id);
    mysqli_stmt_execute($plan_stmt);
    $plan_result = mysqli_stmt_get_result($plan_stmt);
    
    if (mysqli_num_rows($plan_result) === 0) {
        api_error('Plan not found or inactive', 404);
    }
    
    $plan = mysqli_fetch_assoc($plan_result);
    
    // Get payment settings
    $settings_query = "SELECT * FROM payment_settings WHERE id = 1";
    $settings_result = mysqli_query($conn, $settings_query);
    
    if (mysqli_num_rows($settings_result) === 0) {
        api_error('Payment settings not found', 500);
    }
    
    $settings = mysqli_fetch_assoc($settings_result);
    
    // Check if payment method is enabled
    $method_enabled = $payment_method . '_enabled';
    if (!$settings[$method_enabled]) {
        api_error('Payment method is not available', 400);
    }
    
    // Check if user already has an active subscription
    $subscription_query = "SELECT * FROM subscriptions 
                          WHERE user_id = ? AND status = 'active' 
                          AND end_date > NOW()";
    
    $subscription_stmt = mysqli_prepare($conn, $subscription_query);
    mysqli_stmt_bind_param($subscription_stmt, 'i', $user['user_id']);
    mysqli_stmt_execute($subscription_stmt);
    $subscription_result = mysqli_stmt_get_result($subscription_stmt);
    
    if (mysqli_num_rows($subscription_result) > 0) {
        api_error('You already have an active subscription', 400);
    }
    
    // Check if user has a pending payment request today
    $today = date('Y-m-d');
    $pending_query = "SELECT * FROM payment_requests 
                     WHERE user_id = ? AND DATE(created_at) = ? AND status = 'pending'";
    
    $pending_stmt = mysqli_prepare($conn, $pending_query);
    mysqli_stmt_bind_param($pending_stmt, 'is', $user['user_id'], $today);
    mysqli_stmt_execute($pending_stmt);
    $pending_result = mysqli_stmt_get_result($pending_stmt);
    
    if (mysqli_num_rows($pending_result) > 0) {
        api_error('You already have a pending payment request today', 400);
    }
    
    // Generate payment ID
    $payment_id = uniqid('PAY');
    
    // Create payment request
    $request_query = "INSERT INTO payment_requests 
                     (user_id, plan_id, payment_method, amount, payment_id, status) 
                     VALUES (?, ?, ?, ?, ?, 'pending')";
    
    $request_stmt = mysqli_prepare($conn, $request_query);
    mysqli_stmt_bind_param($request_stmt, 'iisds', $user['user_id'], $plan_id, $payment_method, $plan['price'], $payment_id);
    
    if (!mysqli_stmt_execute($request_stmt)) {
        api_error('Failed to create payment request: ' . mysqli_error($conn), 500);
    }
    
    $request_id = mysqli_insert_id($conn);
    
    // Get merchant details
    $merchant_number = $settings[$payment_method . '_merchant_number'];
    $merchant_name = $settings[$payment_method . '_merchant_name'];
    
    // Return payment details
    api_response([
        'request_id' => (int)$request_id,
        'payment_id' => $payment_id,
        'plan_id' => $plan_id,
        'plan_name' => $plan['name'],
        'amount' => (float)$plan['price'],
        'payment_method' => $payment_method,
        'merchant_number' => $merchant_number,
        'merchant_name' => $merchant_name,
        'status' => 'pending',
        'created_at' => date('Y-m-d H:i:s')
    ], 201, 'Payment request created successfully');
}

// Handle verify payment
function handle_verify_payment($user, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'POST') {
        api_error('Method not allowed', 405);
    }
    
    // Validate required fields
    if (empty($request['body']['payment_id']) || empty($request['body']['transaction_id'])) {
        api_error('Payment ID and transaction ID are required', 400);
    }
    
    $payment_id = $request['body']['payment_id'];
    $transaction_id = $request['body']['transaction_id'];
    
    // Get payment request
    $request_query = "SELECT pr.*, pp.duration 
                     FROM payment_requests pr
                     JOIN premium_plans pp ON pr.plan_id = pp.id
                     WHERE pr.payment_id = ? AND pr.user_id = ?";
    
    $request_stmt = mysqli_prepare($conn, $request_query);
    mysqli_stmt_bind_param($request_stmt, 'si', $payment_id, $user['user_id']);
    mysqli_stmt_execute($request_stmt);
    $request_result = mysqli_stmt_get_result($request_stmt);
    
    if (mysqli_num_rows($request_result) === 0) {
        api_error('Payment request not found', 404);
    }
    
    $payment_request = mysqli_fetch_assoc($request_result);
    
    // Check if payment is already processed
    if ($payment_request['status'] !== 'pending') {
        api_error('Payment request is already ' . $payment_request['status'], 400);
    }
    
    // Update payment request
    $update_query = "UPDATE payment_requests 
                    SET transaction_id = ?, status = 'processing', updated_at = NOW() 
                    WHERE id = ?";
    
    $update_stmt = mysqli_prepare($conn, $update_query);
    mysqli_stmt_bind_param($update_stmt, 'si', $transaction_id, $payment_request['id']);
    
    if (!mysqli_stmt_execute($update_stmt)) {
        api_error('Failed to update payment request: ' . mysqli_error($conn), 500);
    }
    
    // For automatic payment methods (e.g., bKash), verify transaction
    if ($payment_request['payment_method'] === 'bkash' && !empty($transaction_id)) {
        // TODO: Implement bKash transaction verification
        // For now, we'll simulate a successful verification
        $is_verified = true;
        
        if ($is_verified) {
            // Create subscription
            $start_date = date('Y-m-d');
            $end_date = date('Y-m-d', strtotime("+{$payment_request['duration']} days"));
            
            $subscription_query = "INSERT INTO subscriptions 
                                  (user_id, plan_id, start_date, end_date, payment_method, transaction_id, status) 
                                  VALUES (?, ?, ?, ?, ?, ?, 'active')";
            
            $subscription_stmt = mysqli_prepare($conn, $subscription_query);
            mysqli_stmt_bind_param($subscription_stmt, 'iissss', $user['user_id'], $payment_request['plan_id'], $start_date, $end_date, $payment_request['payment_method'], $transaction_id);
            
            if (!mysqli_stmt_execute($subscription_stmt)) {
                api_error('Failed to create subscription: ' . mysqli_error($conn), 500);
            }
            
            $subscription_id = mysqli_insert_id($conn);
            
            // Update payment request
            $complete_query = "UPDATE payment_requests 
                              SET status = 'completed', updated_at = NOW() 
                              WHERE id = ?";
            
            $complete_stmt = mysqli_prepare($conn, $complete_query);
            mysqli_stmt_bind_param($complete_stmt, 'i', $payment_request['id']);
            mysqli_stmt_execute($complete_stmt);
            
            // Update user premium status
            $premium_query = "UPDATE users 
                             SET is_premium = 1, premium_expires = ? 
                             WHERE id = ?";
            
            $premium_stmt = mysqli_prepare($conn, $premium_query);
            mysqli_stmt_bind_param($premium_stmt, 'si', $end_date, $user['user_id']);
            mysqli_stmt_execute($premium_stmt);
            
            // Return success response
            api_response([
                'subscription_id' => (int)$subscription_id,
                'plan_id' => (int)$payment_request['plan_id'],
                'start_date' => $start_date,
                'end_date' => $end_date,
                'payment_method' => $payment_request['payment_method'],
                'transaction_id' => $transaction_id,
                'status' => 'active'
            ], 200, 'Payment verified and subscription activated');
        } else {
            // Update payment request
            $failed_query = "UPDATE payment_requests 
                            SET status = 'failed', updated_at = NOW() 
                            WHERE id = ?";
            
            $failed_stmt = mysqli_prepare($conn, $failed_query);
            mysqli_stmt_bind_param($failed_stmt, 'i', $payment_request['id']);
            mysqli_stmt_execute($failed_stmt);
            
            api_error('Transaction verification failed', 400);
        }
    } else {
        // For manual payment methods, return success and wait for admin approval
        api_response([
            'request_id' => (int)$payment_request['id'],
            'payment_id' => $payment_id,
            'transaction_id' => $transaction_id,
            'status' => 'processing'
        ], 200, 'Payment verification submitted. Waiting for admin approval.');
    }
}

// Handle payment history
function handle_payment_history($user, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get pagination parameters
    $page = isset($request['params']['page']) ? (int)$request['params']['page'] : 1;
    $limit = isset($request['params']['limit']) ? (int)$request['params']['limit'] : 10;
    $offset = ($page - 1) * $limit;
    
    // Get payment history
    $query = "SELECT pr.*, pp.name as plan_name, pp.price as plan_price, pp.duration as plan_duration 
             FROM payment_requests pr
             JOIN premium_plans pp ON pr.plan_id = pp.id
             WHERE pr.user_id = ?
             ORDER BY pr.created_at DESC
             LIMIT ? OFFSET ?";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'iii', $user['user_id'], $limit, $offset);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $payments = [];
    while ($payment = mysqli_fetch_assoc($result)) {
        $payments[] = [
            'id' => (int)$payment['id'],
            'payment_id' => $payment['payment_id'],
            'plan_id' => (int)$payment['plan_id'],
            'plan_name' => $payment['plan_name'],
            'amount' => (float)$payment['amount'],
            'payment_method' => $payment['payment_method'],
            'transaction_id' => $payment['transaction_id'],
            'status' => $payment['status'],
            'created_at' => $payment['created_at'],
            'updated_at' => $payment['updated_at']
        ];
    }
    
    // Get total count for pagination
    $count_query = "SELECT COUNT(*) as total FROM payment_requests WHERE user_id = ?";
    $count_stmt = mysqli_prepare($conn, $count_query);
    mysqli_stmt_bind_param($count_stmt, 'i', $user['user_id']);
    mysqli_stmt_execute($count_stmt);
    $count_result = mysqli_stmt_get_result($count_stmt);
    $total = mysqli_fetch_assoc($count_result)['total'];
    
    // Return payment history with pagination
    api_response([
        'payments' => $payments,
        'meta' => [
            'pagination' => [
                'total' => (int)$total,
                'per_page' => $limit,
                'current_page' => $page,
                'last_page' => ceil($total / $limit)
            ]
        ]
    ]);
}

// Handle check payment status
function handle_check_payment($user, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Validate required fields
    if (empty($request['params']['payment_id'])) {
        api_error('Payment ID is required', 400);
    }
    
    $payment_id = $request['params']['payment_id'];
    
    // Get payment request
    $query = "SELECT pr.*, pp.name as plan_name, pp.price as plan_price 
             FROM payment_requests pr
             JOIN premium_plans pp ON pr.plan_id = pp.id
             WHERE pr.payment_id = ? AND pr.user_id = ?";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'si', $payment_id, $user['user_id']);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($result) === 0) {
        api_error('Payment request not found', 404);
    }
    
    $payment = mysqli_fetch_assoc($result);
    
    // Check if subscription was created
    $subscription_query = "SELECT * FROM subscriptions 
                          WHERE user_id = ? AND transaction_id = ?";
    
    $subscription_stmt = mysqli_prepare($conn, $subscription_query);
    mysqli_stmt_bind_param($subscription_stmt, 'is', $user['user_id'], $payment['transaction_id']);
    mysqli_stmt_execute($subscription_stmt);
    $subscription_result = mysqli_stmt_get_result($subscription_stmt);
    
    $subscription = null;
    if (mysqli_num_rows($subscription_result) > 0) {
        $sub = mysqli_fetch_assoc($subscription_result);
        $subscription = [
            'id' => (int)$sub['id'],
            'plan_id' => (int)$sub['plan_id'],
            'start_date' => $sub['start_date'],
            'end_date' => $sub['end_date'],
            'status' => $sub['status']
        ];
    }
    
    // Return payment status
    api_response([
        'id' => (int)$payment['id'],
        'payment_id' => $payment['payment_id'],
        'plan_id' => (int)$payment['plan_id'],
        'plan_name' => $payment['plan_name'],
        'amount' => (float)$payment['amount'],
        'payment_method' => $payment['payment_method'],
        'transaction_id' => $payment['transaction_id'],
        'status' => $payment['status'],
        'subscription' => $subscription,
        'created_at' => $payment['created_at'],
        'updated_at' => $payment['updated_at']
    ]);
}
