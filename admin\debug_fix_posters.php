<?php
// Set page title
$page_title = 'Debug Fix Posters';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include config file
require_once '../includes/config.php';

// Display debug information
echo "<h1>Debug Information</h1>";
echo "<pre>";
echo "Session Variables:\n";
print_r($_SESSION);
echo "\n";
echo "isLoggedIn(): " . (isLoggedIn() ? "Yes" : "No") . "\n";
echo "isAdmin(): " . (isAdmin() ? "Yes" : "No") . "\n";
echo "</pre>";

// Display the rest of the page only if user is logged in and is admin
if (isLoggedIn() && isAdmin()) {
    echo "<h2>You are logged in as admin. You should be able to access fix_posters.php</h2>";
    
    echo "<p>Click <a href='fix_posters.php'>here</a> to go to fix_posters.php</p>";
    
    // Display the actual fix posters functionality
    echo "<h2>Fix Posters Functionality</h2>";
    
    // Process form submission
    $success_message = '';
    $error_message = '';
    $fixed_count = 0;
    $failed_count = 0;
    
    if (isset($_POST['fix_posters'])) {
        // Get all movies with TMDB poster paths
        $movies_query = "SELECT id, tmdb_id, poster FROM movies WHERE poster LIKE '/%'";
        $movies_result = mysqli_query($conn, $movies_query);
        
        while ($movie = mysqli_fetch_assoc($movies_result)) {
            $tmdb_id = $movie['tmdb_id'];
            $poster_path = $movie['poster'];
            
            // Download poster image from TMDB
            if (!empty($poster_path)) {
                $poster_url = 'https://image.tmdb.org/t/p/w500' . $poster_path;
                $poster_filename = 'movie_poster_' . $tmdb_id . '_' . time() . '.jpg';
                $poster_path_local = '../uploads/' . $poster_filename;
                
                // Create uploads directory if it doesn't exist
                if (!file_exists('../uploads')) {
                    mkdir('../uploads', 0777, true);
                }
                
                // Download the image
                $image_content = @file_get_contents($poster_url);
                if ($image_content !== false) {
                    file_put_contents($poster_path_local, $image_content);
                    
                    // Update movie record
                    $update_query = "UPDATE movies SET poster = '$poster_filename' WHERE id = {$movie['id']}";
                    if (mysqli_query($conn, $update_query)) {
                        $fixed_count++;
                    } else {
                        $failed_count++;
                    }
                } else {
                    $failed_count++;
                }
            }
        }
        
        // Get all TV shows with TMDB poster paths
        $tvshows_query = "SELECT id, tmdb_id, poster FROM tvshows WHERE poster LIKE '/%'";
        $tvshows_result = mysqli_query($conn, $tvshows_query);
        
        while ($tvshow = mysqli_fetch_assoc($tvshows_result)) {
            $tmdb_id = $tvshow['tmdb_id'];
            $poster_path = $tvshow['poster'];
            
            // Download poster image from TMDB
            if (!empty($poster_path)) {
                $poster_url = 'https://image.tmdb.org/t/p/w500' . $poster_path;
                $poster_filename = 'tvshow_poster_' . $tmdb_id . '_' . time() . '.jpg';
                $poster_path_local = '../uploads/' . $poster_filename;
                
                // Create uploads directory if it doesn't exist
                if (!file_exists('../uploads')) {
                    mkdir('../uploads', 0777, true);
                }
                
                // Download the image
                $image_content = @file_get_contents($poster_url);
                if ($image_content !== false) {
                    file_put_contents($poster_path_local, $image_content);
                    
                    // Update TV show record
                    $update_query = "UPDATE tvshows SET poster = '$poster_filename' WHERE id = {$tvshow['id']}";
                    if (mysqli_query($conn, $update_query)) {
                        $fixed_count++;
                    } else {
                        $failed_count++;
                    }
                } else {
                    $failed_count++;
                }
            }
        }
        
        if ($fixed_count > 0) {
            $success_message = "Fixed $fixed_count posters successfully.";
        }
        
        if ($failed_count > 0) {
            $error_message = "Failed to fix $failed_count posters.";
        }
        
        if ($fixed_count == 0 && $failed_count == 0) {
            $success_message = "No posters needed fixing.";
        }
    }
    
    // Display messages
    if ($success_message) {
        echo "<div style='color: green; padding: 10px; background-color: #dff0d8; margin-bottom: 15px;'>";
        echo $success_message;
        echo "</div>";
    }
    
    if ($error_message) {
        echo "<div style='color: red; padding: 10px; background-color: #f2dede; margin-bottom: 15px;'>";
        echo $error_message;
        echo "</div>";
    }
    
    // Display form
    echo "<div style='padding: 20px; background-color: #f8f9fa; border-radius: 5px;'>";
    echo "<h3>Fix Missing Posters</h3>";
    echo "<p>This tool will download posters from TMDB for all movies and TV shows that have TMDB poster paths but no local posters.</p>";
    echo "<p>Use this if you're seeing missing posters on your site.</p>";
    
    echo "<form method='POST' action=''>";
    echo "<button type='submit' name='fix_posters' style='padding: 10px 15px; background-color: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer;'>";
    echo "Fix Posters";
    echo "</button>";
    echo "</form>";
    echo "</div>";
} else {
    echo "<h2>You are not logged in as admin. You cannot access fix_posters.php</h2>";
}
?>
