<?php
// Set page title
$page_title = 'Live Chat';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Process form submissions
$success_message = '';
$error_message = '';

// Check if chat_sessions and chat_messages tables exist
$check_tables_query = "SHOW TABLES LIKE 'chat_sessions'";
$check_tables_result = mysqli_query($conn, $check_tables_query);

if (mysqli_num_rows($check_tables_result) == 0) {
    // Create chat_sessions table
    $create_sessions_table = "CREATE TABLE IF NOT EXISTS chat_sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        status ENUM('active', 'closed') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    mysqli_query($conn, $create_sessions_table);
    
    // Create chat_messages table
    $create_messages_table = "CREATE TABLE IF NOT EXISTS chat_messages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        session_id INT NOT NULL,
        sender_id INT NOT NULL,
        receiver_id INT NOT NULL,
        message TEXT NOT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE,
        FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    mysqli_query($conn, $create_messages_table);
}

// Close chat session
if (isset($_GET['close_session']) && !empty($_GET['close_session'])) {
    $session_id = (int)$_GET['close_session'];
    
    $close_query = "UPDATE chat_sessions SET status = 'closed' WHERE id = $session_id";
    if (mysqli_query($conn, $close_query)) {
        $success_message = 'Chat session closed successfully.';
    } else {
        $error_message = 'Error closing chat session: ' . mysqli_error($conn);
    }
}

// Send message
if (isset($_POST['send_message'])) {
    $session_id = (int)$_POST['session_id'];
    $message = sanitize($_POST['message']);
    $user_id = (int)$_POST['user_id'];
    
    if (empty($message)) {
        $error_message = 'Message cannot be empty.';
    } else {
        // Get admin ID
        $admin_id = $_SESSION['user_id'];
        
        // Insert message
        $insert_query = "INSERT INTO chat_messages (session_id, sender_id, receiver_id, message) 
                        VALUES ($session_id, $admin_id, $user_id, '$message')";
        
        if (mysqli_query($conn, $insert_query)) {
            // Update session timestamp
            mysqli_query($conn, "UPDATE chat_sessions SET updated_at = NOW() WHERE id = $session_id");
            
            // Redirect to avoid form resubmission
            redirect("live_chat.php?session=$session_id");
        } else {
            $error_message = 'Error sending message: ' . mysqli_error($conn);
        }
    }
}

// Mark messages as read
if (isset($_GET['session']) && !empty($_GET['session'])) {
    $session_id = (int)$_GET['session'];
    $admin_id = $_SESSION['user_id'];
    
    // Mark messages as read
    $mark_read_query = "UPDATE chat_messages SET is_read = TRUE 
                       WHERE session_id = $session_id AND receiver_id = $admin_id AND is_read = FALSE";
    mysqli_query($conn, $mark_read_query);
}

// Get active chat sessions
$sessions_query = "SELECT cs.*, u.username, u.profile_image,
                  (SELECT COUNT(*) FROM chat_messages WHERE session_id = cs.id AND receiver_id = {$_SESSION['user_id']} AND is_read = FALSE) as unread_count,
                  (SELECT created_at FROM chat_messages WHERE session_id = cs.id ORDER BY created_at DESC LIMIT 1) as last_message_time
                  FROM chat_sessions cs
                  JOIN users u ON cs.user_id = u.id
                  WHERE cs.status = 'active'
                  ORDER BY last_message_time DESC";
$sessions_result = mysqli_query($conn, $sessions_query);

// Get current session messages if selected
$current_session = null;
$current_user = null;
$messages = [];

if (isset($_GET['session']) && !empty($_GET['session'])) {
    $session_id = (int)$_GET['session'];
    
    // Get session details
    $session_query = "SELECT cs.*, u.username, u.profile_image, u.id as user_id
                     FROM chat_sessions cs
                     JOIN users u ON cs.user_id = u.id
                     WHERE cs.id = $session_id";
    $session_result = mysqli_query($conn, $session_query);
    
    if (mysqli_num_rows($session_result) > 0) {
        $current_session = mysqli_fetch_assoc($session_result);
        $current_user = $current_session['user_id'];
        
        // Get messages
        $messages_query = "SELECT cm.*, 
                          u_sender.username as sender_username, 
                          u_sender.profile_image as sender_image,
                          u_sender.role as sender_role
                          FROM chat_messages cm
                          JOIN users u_sender ON cm.sender_id = u_sender.id
                          WHERE cm.session_id = $session_id
                          ORDER BY cm.created_at ASC";
        $messages_result = mysqli_query($conn, $messages_query);
        
        while ($message = mysqli_fetch_assoc($messages_result)) {
            $messages[] = $message;
        }
    }
}

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title d-flex align-items-center">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>লাইভ চ্যাট</h1>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <?php if($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>
        
        <?php if($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="row">
            <!-- Chat Sessions List -->
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="fw-bold text-primary mb-0">চ্যাট সেশন</h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            <?php if(mysqli_num_rows($sessions_result) > 0): ?>
                                <?php while($session = mysqli_fetch_assoc($sessions_result)): ?>
                                <a href="live_chat.php?session=<?php echo $session['id']; ?>" class="list-group-item list-group-item-action <?php echo (isset($_GET['session']) && $_GET['session'] == $session['id']) ? 'active' : ''; ?>">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <img src="<?php echo !empty($session['profile_image']) ? SITE_URL . '/uploads/' . $session['profile_image'] : SITE_URL . '/assets/img/default-avatar.png'; ?>" class="rounded-circle" width="40" height="40" alt="<?php echo $session['username']; ?>">
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0"><?php echo $session['username']; ?></h6>
                                                <?php if($session['unread_count'] > 0): ?>
                                                <span class="badge bg-danger rounded-pill"><?php echo $session['unread_count']; ?></span>
                                                <?php endif; ?>
                                            </div>
                                            <small class="text-muted">
                                                <?php echo timeAgo($session['last_message_time']); ?>
                                            </small>
                                        </div>
                                    </div>
                                </a>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <div class="text-center p-4">
                                    <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                                    <p>কোন সক্রিয় চ্যাট সেশন নেই।</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Chat Messages -->
            <div class="col-md-8">
                <?php if($current_session): ?>
                <div class="card">
                    <div class="card-header bg-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <img src="<?php echo !empty($current_session['profile_image']) ? SITE_URL . '/uploads/' . $current_session['profile_image'] : SITE_URL . '/assets/img/default-avatar.png'; ?>" class="rounded-circle me-2" width="40" height="40" alt="<?php echo $current_session['username']; ?>">
                                <div>
                                    <h6 class="mb-0"><?php echo $current_session['username']; ?></h6>
                                    <small class="text-muted">
                                        <?php echo timeAgo($current_session['created_at']); ?> থেকে চ্যাট করছেন
                                    </small>
                                </div>
                            </div>
                            <div>
                                <a href="live_chat.php?close_session=<?php echo $current_session['id']; ?>" class="btn btn-sm btn-outline-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই চ্যাট সেশন বন্ধ করতে চান?');">
                                    <i class="fas fa-times me-1"></i> চ্যাট বন্ধ করুন
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body chat-messages" style="height: 400px; overflow-y: auto;">
                        <?php foreach($messages as $message): ?>
                            <?php $is_admin = ($message['sender_role'] == 'admin'); ?>
                            <div class="d-flex mb-3 <?php echo $is_admin ? 'justify-content-end' : ''; ?>">
                                <?php if(!$is_admin): ?>
                                <div class="flex-shrink-0 me-2">
                                    <img src="<?php echo !empty($message['sender_image']) ? SITE_URL . '/uploads/' . $message['sender_image'] : SITE_URL . '/assets/img/default-avatar.png'; ?>" class="rounded-circle" width="32" height="32" alt="<?php echo $message['sender_username']; ?>">
                                </div>
                                <?php endif; ?>
                                <div class="<?php echo $is_admin ? 'chat-message-admin' : 'chat-message-user'; ?>" style="max-width: 70%; background-color: <?php echo $is_admin ? '#e3f2fd' : '#f1f1f1'; ?>; padding: 10px 15px; border-radius: 15px; position: relative;">
                                    <div class="message-text"><?php echo nl2br($message['message']); ?></div>
                                    <div class="message-time text-muted" style="font-size: 0.7rem;">
                                        <?php echo date('h:i A', strtotime($message['created_at'])); ?>
                                        <?php if($is_admin && $message['is_read']): ?>
                                        <i class="fas fa-check-double ms-1 text-primary"></i>
                                        <?php elseif($is_admin): ?>
                                        <i class="fas fa-check ms-1"></i>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php if($is_admin): ?>
                                <div class="flex-shrink-0 ms-2">
                                    <img src="<?php echo !empty($message['sender_image']) ? SITE_URL . '/uploads/' . $message['sender_image'] : SITE_URL . '/assets/img/default-avatar.png'; ?>" class="rounded-circle" width="32" height="32" alt="<?php echo $message['sender_username']; ?>">
                                </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="card-footer bg-white">
                        <form method="POST" action="" class="needs-validation" novalidate>
                            <input type="hidden" name="session_id" value="<?php echo $current_session['id']; ?>">
                            <input type="hidden" name="user_id" value="<?php echo $current_user; ?>">
                            <div class="input-group">
                                <textarea class="form-control" name="message" placeholder="আপনার মেসেজ লিখুন..." rows="1" required></textarea>
                                <button type="submit" name="send_message" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <?php else: ?>
                <div class="card">
                    <div class="card-body text-center p-5">
                        <i class="fas fa-comments fa-4x text-muted mb-3"></i>
                        <h5>কোন চ্যাট সেশন নির্বাচন করা হয়নি</h5>
                        <p class="text-muted">বার্তালাপ শুরু করতে বাম দিক থেকে একটি চ্যাট সেশন নির্বাচন করুন।</p>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-scroll to bottom of chat messages
document.addEventListener('DOMContentLoaded', function() {
    const chatMessages = document.querySelector('.chat-messages');
    if (chatMessages) {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    // Auto-resize textarea
    const textarea = document.querySelector('textarea[name="message"]');
    if (textarea) {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
    }
});
</script>

<?php
// Helper function to format time ago
function timeAgo($datetime) {
    $timestamp = strtotime($datetime);
    $diff = time() - $timestamp;
    
    if ($diff < 60) {
        return 'এইমাত্র';
    } elseif ($diff < 3600) {
        $minutes = floor($diff / 60);
        return $minutes . ' মিনিট আগে';
    } elseif ($diff < 86400) {
        $hours = floor($diff / 3600);
        return $hours . ' ঘন্টা আগে';
    } elseif ($diff < 604800) {
        $days = floor($diff / 86400);
        return $days . ' দিন আগে';
    } else {
        return date('M j, Y', $timestamp);
    }
}

// Include footer
require_once 'includes/footer.php';
?>
