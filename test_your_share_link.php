<?php
require_once 'includes/config.php';

echo "<h2>আপনার শেয়ার লিংক টেস্ট</h2>";

// The token you mentioned
$token = 'f09f1306224549b69631e43075707e60_1751652803';

echo "<h3>টেস্ট করা টোকেন:</h3>";
echo "<p><code>$token</code></p>";

// Clean token
$clean_token = mysqli_real_escape_string($conn, $token);

// Test query - Fixed for tvshows table structure
$query = "SELECT sl.*, 
                 CASE 
                     WHEN sl.content_type = 'movie' THEN m.title
                     WHEN sl.content_type = 'tvshow' THEN t.title
                 END as content_title,
                 CASE 
                     WHEN sl.content_type = 'movie' THEN m.description
                     WHEN sl.content_type = 'tvshow' THEN t.description
                 END as content_description,
                 CASE 
                     WHEN sl.content_type = 'movie' THEN m.poster
                     WHEN sl.content_type = 'tvshow' THEN t.poster
                 END as content_poster,
                 CASE 
                     WHEN sl.content_type = 'movie' THEN m.banner
                     WHEN sl.content_type = 'tvshow' THEN t.banner
                 END as content_banner,
                 CASE 
                     WHEN sl.content_type = 'movie' THEN m.rating
                     WHEN sl.content_type = 'tvshow' THEN t.rating
                 END as content_rating,
                 CASE 
                     WHEN sl.content_type = 'movie' THEN m.release_year
                     WHEN sl.content_type = 'tvshow' THEN t.year
                 END as content_year
          FROM shared_links sl
          LEFT JOIN movies m ON sl.content_type = 'movie' AND sl.content_id = m.id
          LEFT JOIN tvshows t ON sl.content_type = 'tvshow' AND sl.content_id = t.id
          WHERE sl.link_token = '$clean_token'";

echo "<h3>কুয়েরি:</h3>";
echo "<pre>" . htmlspecialchars($query) . "</pre>";

$result = mysqli_query($conn, $query);

if (!$result) {
    echo "<div style='color: red; padding: 10px; margin: 10px 0; border: 1px solid red;'>";
    echo "<h3>❌ কুয়েরি ফেইল:</h3>";
    echo "<p>" . mysqli_error($conn) . "</p>";
    echo "</div>";
    exit;
}

$num_rows = mysqli_num_rows($result);

echo "<h3>ফলাফল:</h3>";
echo "<p>সারি সংখ্যা: $num_rows</p>";

if ($num_rows == 0) {
    echo "<div style='color: orange; padding: 10px; margin: 10px 0; border: 1px solid orange;'>";
    echo "<h3>❌ কোনো শেয়ার লিংক পাওয়া যায়নি</h3>";
    echo "<p>টোকেন: $token</p>";
    echo "</div>";
    
    // Check if token exists without content join
    $simple_query = "SELECT * FROM shared_links WHERE link_token = '$clean_token'";
    $simple_result = mysqli_query($conn, $simple_query);
    
    if (mysqli_num_rows($simple_result) > 0) {
        echo "<div style='color: blue; padding: 10px; margin: 10px 0; border: 1px solid blue;'>";
        echo "<h3>ℹ️ শেয়ার লিংক আছে কিন্তু কন্টেন্ট নেই</h3>";
        $link_data = mysqli_fetch_assoc($simple_result);
        echo "<pre>" . print_r($link_data, true) . "</pre>";
        echo "</div>";
    } else {
        echo "<div style='color: red; padding: 10px; margin: 10px 0; border: 1px solid red;'>";
        echo "<h3>❌ শেয়ার লিংকই নেই</h3>";
        echo "</div>";
    }
    
} else {
    echo "<div style='color: green; padding: 10px; margin: 10px 0; border: 1px solid green;'>";
    echo "<h3>✅ শেয়ার লিংক পাওয়া গেছে!</h3>";
    $shared_link = mysqli_fetch_assoc($result);
    echo "<pre>" . print_r($shared_link, true) . "</pre>";
    echo "</div>";
    
    // Create test URLs
    $share_url = SITE_URL . '/share.php?token=' . $token;
    $debug_url = SITE_URL . '/share.php?token=' . $token . '&debug=1';
    
    echo "<h3>টেস্ট লিংক:</h3>";
    echo "<p><a href='$debug_url' target='_blank' style='color: green; font-size: 18px;'>🔗 ডিবাগ লিংক</a></p>";
    echo "<p><a href='$share_url' target='_blank' style='color: blue; font-size: 18px;'>🔗 সাধারণ লিংক</a></p>";
}

// Check shared_links table structure
echo "<h3>শেয়ার লিংক টেবিল চেক:</h3>";
$table_check = mysqli_query($conn, "DESCRIBE shared_links");
if ($table_check) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ফিল্ড</th><th>টাইপ</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = mysqli_fetch_assoc($table_check)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ টেবিল চেক করতে সমস্যা হয়েছে</p>";
}
?> 