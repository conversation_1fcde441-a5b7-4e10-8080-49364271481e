<?php
// Set page title
$page_title = 'Analytics';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Get date range for analytics
$date_from = isset($_GET['date_from']) ? sanitize($_GET['date_from']) : date('Y-m-d', strtotime('-30 days'));
$date_to = isset($_GET['date_to']) ? sanitize($_GET['date_to']) : date('Y-m-d');

// Get user statistics
$users_query = "SELECT
                (SELECT COUNT(*) FROM users) as total_users,
                (SELECT COUNT(*) FROM users WHERE created_at >= '$date_from' AND created_at <= '$date_to 23:59:59') as new_users,
                (SELECT COUNT(*) FROM users WHERE role = 'admin') as admin_users,
                (SELECT COUNT(*) FROM users WHERE is_premium = TRUE) as premium_users";
$users_result = mysqli_query($conn, $users_query);
$users_stats = mysqli_fetch_assoc($users_result) ?: [
    'total_users' => 0,
    'new_users' => 0,
    'admin_users' => 0,
    'premium_users' => 0
];

// Check if movies, tvshows, episodes, and categories tables exist
$check_movies_query = "SHOW TABLES LIKE 'movies'";
$check_movies_result = mysqli_query($conn, $check_movies_query);

$check_tvshows_query = "SHOW TABLES LIKE 'tvshows'";
$check_tvshows_result = mysqli_query($conn, $check_tvshows_query);

$check_episodes_query = "SHOW TABLES LIKE 'episodes'";
$check_episodes_result = mysqli_query($conn, $check_episodes_query);

$check_categories_query = "SHOW TABLES LIKE 'categories'";
$check_categories_result = mysqli_query($conn, $check_categories_query);

// Create tables if they don't exist
if (mysqli_num_rows($check_movies_result) == 0) {
    $create_movies_query = "CREATE TABLE IF NOT EXISTS movies (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        poster VARCHAR(255),
        category_id INT,
        release_date DATE,
        views INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    mysqli_query($conn, $create_movies_query);
}

if (mysqli_num_rows($check_tvshows_result) == 0) {
    $create_tvshows_query = "CREATE TABLE IF NOT EXISTS tvshows (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        poster VARCHAR(255),
        category_id INT,
        release_date DATE,
        views INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    mysqli_query($conn, $create_tvshows_query);
}

if (mysqli_num_rows($check_episodes_result) == 0) {
    $create_episodes_query = "CREATE TABLE IF NOT EXISTS episodes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        tvshow_id INT NOT NULL,
        season_number INT NOT NULL,
        episode_number INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    mysqli_query($conn, $create_episodes_query);
}

if (mysqli_num_rows($check_categories_result) == 0) {
    $create_categories_query = "CREATE TABLE IF NOT EXISTS categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        type ENUM('both', 'movie', 'tvshow') DEFAULT 'both',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    mysqli_query($conn, $create_categories_query);
}

// Get content statistics
$content_query = "SELECT
                 (SELECT COUNT(*) FROM movies) as total_movies,
                 (SELECT COUNT(*) FROM tvshows) as total_tvshows,
                 (SELECT COUNT(*) FROM episodes) as total_episodes,
                 (SELECT COUNT(*) FROM categories) as total_categories";
$content_result = mysqli_query($conn, $content_query);
$content_stats = mysqli_fetch_assoc($content_result) ?: [
    'total_movies' => 0,
    'total_tvshows' => 0,
    'total_episodes' => 0,
    'total_categories' => 0
];

// Check if payments table exists
$check_payments_query = "SHOW TABLES LIKE 'payments'";
$check_payments_result = mysqli_query($conn, $check_payments_query);

// Create payments table if it doesn't exist
if (mysqli_num_rows($check_payments_result) == 0) {
    $create_payments_query = "CREATE TABLE IF NOT EXISTS payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        plan_id INT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        payment_method ENUM('bkash', 'nagad', 'rocket', 'manual') NOT NULL,
        transaction_id VARCHAR(100),
        payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    mysqli_query($conn, $create_payments_query);
}

// Get premium statistics
$premium_query = "SELECT
                 (SELECT COUNT(*) FROM payments WHERE payment_status = 'completed') as total_payments,
                 (SELECT COUNT(*) FROM payments WHERE payment_status = 'completed' AND created_at >= '$date_from' AND created_at <= '$date_to 23:59:59') as recent_payments,
                 (SELECT SUM(amount) FROM payments WHERE payment_status = 'completed') as total_revenue,
                 (SELECT SUM(amount) FROM payments WHERE payment_status = 'completed' AND created_at >= '$date_from' AND created_at <= '$date_to 23:59:59') as recent_revenue";
$premium_result = mysqli_query($conn, $premium_query);
$premium_stats = mysqli_fetch_assoc($premium_result) ?: [
    'total_payments' => 0,
    'recent_payments' => 0,
    'total_revenue' => 0,
    'recent_revenue' => 0
];

// Check if users table exists
$check_users_query = "SHOW TABLES LIKE 'users'";
$check_users_result = mysqli_query($conn, $check_users_query);

// Create users table if it doesn't exist
if (mysqli_num_rows($check_users_result) == 0) {
    $create_users_query = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        role ENUM('user', 'admin') DEFAULT 'user',
        is_premium BOOLEAN DEFAULT FALSE,
        profile_image VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    mysqli_query($conn, $create_users_query);
}

// Get user registration data for chart
$registration_query = "SELECT DATE(created_at) as date, COUNT(*) as count
                      FROM users
                      WHERE created_at >= '$date_from' AND created_at <= '$date_to 23:59:59'
                      GROUP BY DATE(created_at)
                      ORDER BY date";
$registration_result = mysqli_query($conn, $registration_query);

$registration_dates = [];
$registration_counts = [];

if ($registration_result) {
    while ($row = mysqli_fetch_assoc($registration_result)) {
        $registration_dates[] = $row['date'];
        $registration_counts[] = $row['count'];
    }
}

// Get payment data for chart
$payment_query = "SELECT DATE(created_at) as date, SUM(amount) as total
                 FROM payments
                 WHERE payment_status = 'completed' AND created_at >= '$date_from' AND created_at <= '$date_to 23:59:59'
                 GROUP BY DATE(created_at)
                 ORDER BY date";
$payment_result = mysqli_query($conn, $payment_query);

$payment_dates = [];
$payment_amounts = [];

if ($payment_result) {
    while ($row = mysqli_fetch_assoc($payment_result)) {
        $payment_dates[] = $row['date'];
        $payment_amounts[] = $row['total'];
    }
}
// Get most viewed content
$most_viewed_query = "SELECT m.id, m.title, COALESCE(m.views, 0) as views, 'movie' as type
                     FROM movies m
                     UNION
                     SELECT t.id, t.title, COALESCE(t.views, 0) as views, 'tvshow' as type
                     FROM tvshows t
                     ORDER BY views DESC
                     LIMIT 5";
$most_viewed_result = mysqli_query($conn, $most_viewed_query);

// Check if activity_logs table exists
$check_activity_logs_query = "SHOW TABLES LIKE 'activity_logs'";
$check_activity_logs_result = mysqli_query($conn, $check_activity_logs_query);

// Create activity_logs table if it doesn't exist
if (mysqli_num_rows($check_activity_logs_result) == 0) {
    $create_activity_logs_query = "CREATE TABLE IF NOT EXISTS activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        activity_type VARCHAR(50) NOT NULL,
        description TEXT NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
    )";
    mysqli_query($conn, $create_activity_logs_query);
}

// Get most active users
$most_active_query = "SELECT u.id, u.username, u.profile_image, COUNT(al.id) as activity_count
                     FROM users u
                     LEFT JOIN activity_logs al ON u.id = al.user_id AND al.created_at >= '$date_from' AND al.created_at <= '$date_to 23:59:59'
                     GROUP BY u.id
                     ORDER BY activity_count DESC
                     LIMIT 5";
$most_active_result = mysqli_query($conn, $most_active_query);

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title d-flex align-items-center">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>অ্যানালিটিক্স</h1>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Date Range Filter -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="fw-bold text-primary mb-0">তারিখ নির্বাচন করুন</h6>
            </div>
            <div class="card-body">
                <form method="GET" action="">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="date_from" class="form-label">তারিখ থেকে</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="date_to" class="form-label">তারিখ পর্যন্ত</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                            </div>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <div class="mb-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-1"></i> ফিল্টার
                                </button>
                                <a href="analytics.php" class="btn btn-secondary ms-2">
                                    <i class="fas fa-sync me-1"></i> রিসেট
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Stats Overview -->
        <div class="row">
            <!-- User Stats -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-primary fw-bold">ব্যবহারকারী</h6>
                            <div class="icon-circle bg-primary-light">
                                <i class="fas fa-users text-primary"></i>
                            </div>
                        </div>
                        <div class="mb-1">
                            <h2 class="fw-bold mb-0"><?php echo $users_stats['total_users']; ?></h2>
                            <p class="text-muted small">মোট ব্যবহারকারী</p>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success me-2"><?php echo $users_stats['new_users']; ?></span>
                            <span class="small text-muted">নতুন ব্যবহারকারী (নির্বাচিত সময়ে)</span>
                        </div>
                        <hr>
                        <div class="row text-center">
                            <div class="col-6">
                                <h5 class="mb-0"><?php echo $users_stats['premium_users']; ?></h5>
                                <small class="text-muted">প্রিমিয়াম</small>
                            </div>
                            <div class="col-6">
                                <h5 class="mb-0"><?php echo $users_stats['admin_users']; ?></h5>
                                <small class="text-muted">অ্যাডমিন</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Stats -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-danger fw-bold">কন্টেন্ট</h6>
                            <div class="icon-circle bg-danger-light">
                                <i class="fas fa-photo-video text-danger"></i>
                            </div>
                        </div>
                        <div class="mb-1">
                            <h2 class="fw-bold mb-0"><?php echo $content_stats['total_movies'] + $content_stats['total_tvshows']; ?></h2>
                            <p class="text-muted small">মোট কন্টেন্ট</p>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-danger me-2"><?php echo $content_stats['total_categories']; ?></span>
                            <span class="small text-muted">ক্যাটাগরি</span>
                        </div>
                        <hr>
                        <div class="row text-center">
                            <div class="col-6">
                                <h5 class="mb-0"><?php echo $content_stats['total_movies']; ?></h5>
                                <small class="text-muted">মুভি</small>
                            </div>
                            <div class="col-6">
                                <h5 class="mb-0"><?php echo $content_stats['total_tvshows']; ?></h5>
                                <small class="text-muted">টিভি শো</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Revenue Stats -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-success fw-bold">রেভিনিউ</h6>
                            <div class="icon-circle bg-success-light">
                                <i class="fas fa-money-bill-wave text-success"></i>
                            </div>
                        </div>
                        <div class="mb-1">
                            <h2 class="fw-bold mb-0">৳<?php echo number_format($premium_stats['total_revenue'] ?? 0); ?></h2>
                            <p class="text-muted small">মোট রেভিনিউ</p>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success me-2">৳<?php echo number_format($premium_stats['recent_revenue'] ?? 0); ?></span>
                            <span class="small text-muted">নির্বাচিত সময়ে</span>
                        </div>
                        <hr>
                        <div class="row text-center">
                            <div class="col-6">
                                <h5 class="mb-0"><?php echo $premium_stats['total_payments']; ?></h5>
                                <small class="text-muted">মোট পেমেন্ট</small>
                            </div>
                            <div class="col-6">
                                <h5 class="mb-0"><?php echo $premium_stats['recent_payments']; ?></h5>
                                <small class="text-muted">সাম্প্রতিক</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Episodes Stats -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-info fw-bold">এপিসোড</h6>
                            <div class="icon-circle bg-info-light">
                                <i class="fas fa-film text-info"></i>
                            </div>
                        </div>
                        <div class="mb-1">
                            <h2 class="fw-bold mb-0"><?php echo $content_stats['total_episodes']; ?></h2>
                            <p class="text-muted small">মোট এপিসোড</p>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-info me-2"><?php echo $content_stats['total_tvshows']; ?></span>
                            <span class="small text-muted">টিভি শো</span>
                        </div>
                        <hr>
                        <div class="row text-center">
                            <div class="col-12">
                                <h5 class="mb-0"><?php echo round($content_stats['total_episodes'] / max(1, $content_stats['total_tvshows']), 1); ?></h5>
                                <small class="text-muted">গড় এপিসোড/শো</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="row">
            <!-- User Registration Chart -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="fw-bold text-primary mb-0">ব্যবহারকারী রেজিস্ট্রেশন</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="userRegistrationChart" height="300"></canvas>
                    </div>
                </div>
            </div>

            <!-- Revenue Chart -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="fw-bold text-primary mb-0">রেভিনিউ</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="revenueChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Most Viewed Content -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="fw-bold text-primary mb-0">সর্বাধিক দেখা কন্টেন্ট</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead>
                                    <tr>
                                        <th>টাইটেল</th>
                                        <th>টাইপ</th>
                                        <th>ভিউ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if(mysqli_num_rows($most_viewed_result) > 0): ?>
                                        <?php while($content = mysqli_fetch_assoc($most_viewed_result)): ?>
                                        <tr>
                                            <td><?php echo $content['title']; ?></td>
                                            <td>
                                                <?php if($content['type'] == 'movie'): ?>
                                                <span class="badge bg-primary">মুভি</span>
                                                <?php else: ?>
                                                <span class="badge bg-danger">টিভি শো</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo number_format($content['views']); ?></td>
                                        </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="4" class="text-center">কোন ডাটা পাওয়া যায়নি।</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Most Active Users -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="fw-bold text-primary mb-0">সর্বাধিক সক্রিয় ব্যবহারকারী</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead>
                                    <tr>
                                        <th>প্রোফাইল</th>
                                        <th>ইউজারনেম</th>
                                        <th>অ্যাক্টিভিটি</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if(mysqli_num_rows($most_active_result) > 0): ?>
                                        <?php while($user = mysqli_fetch_assoc($most_active_result)): ?>
                                        <tr>
                                            <td>
                                                <img src="<?php echo !empty($user['profile_image']) ? SITE_URL . '/uploads/' . $user['profile_image'] : SITE_URL . '/assets/img/default-avatar.png'; ?>" class="rounded-circle" width="40" height="40" alt="<?php echo $user['username']; ?>">
                                            </td>
                                            <td><?php echo $user['username']; ?></td>
                                            <td><?php echo $user['activity_count']; ?></td>
                                        </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="3" class="text-center">কোন ডাটা পাওয়া যায়নি।</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // User Registration Chart
    const userRegistrationCtx = document.getElementById('userRegistrationChart').getContext('2d');
    const userRegistrationChart = new Chart(userRegistrationCtx, {
        type: 'line',
        data: {
            labels: <?php echo json_encode($registration_dates); ?>,
            datasets: [{
                label: 'নতুন ব্যবহারকারী',
                data: <?php echo json_encode($registration_counts); ?>,
                backgroundColor: 'rgba(78, 115, 223, 0.05)',
                borderColor: 'rgba(78, 115, 223, 1)',
                borderWidth: 2,
                pointBackgroundColor: 'rgba(78, 115, 223, 1)',
                pointBorderColor: '#fff',
                pointRadius: 3,
                pointHoverRadius: 5,
                fill: true,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            }
        }
    });

    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    const revenueChart = new Chart(revenueCtx, {
        type: 'bar',
        data: {
            labels: <?php echo json_encode($payment_dates); ?>,
            datasets: [{
                label: 'রেভিনিউ (৳)',
                data: <?php echo json_encode($payment_amounts); ?>,
                backgroundColor: 'rgba(40, 167, 69, 0.2)',
                borderColor: 'rgba(40, 167, 69, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            }
        }
    });
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
