import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:cinepix_app/services/download_manager.dart';
import 'package:cinepix_app/services/storage_service.dart';

class StorageInfo {
  final int totalSpace;
  final int usedSpace;
  final int freeSpace;
  final int downloadedVideosSize;
  final int cacheSize;
  final int otherAppDataSize;
  
  StorageInfo({
    required this.totalSpace,
    required this.usedSpace,
    required this.freeSpace,
    required this.downloadedVideosSize,
    required this.cacheSize,
    required this.otherAppDataSize,
  });
  
  double get usedPercentage => totalSpace > 0 ? (usedSpace / totalSpace) * 100 : 0;
  double get downloadedVideosPercentage => totalSpace > 0 ? (downloadedVideosSize / totalSpace) * 100 : 0;
  double get cachePercentage => totalSpace > 0 ? (cacheSize / totalSpace) * 100 : 0;
}

class StorageManager {
  static final StorageManager _instance = StorageManager._internal();
  factory StorageManager() => _instance;
  StorageManager._internal();
  
  final DownloadManager _downloadManager = DownloadManager();
  final StorageService _storageService = StorageService();
  
  // Storage limits and settings
  int maxStorageGB = 5;
  int warningThresholdPercentage = 80;
  bool autoCleanupEnabled = true;
  int autoCleanupDays = 30;
  
  Future<void> initialize() async {
    await _loadSettings();
  }
  
  Future<void> _loadSettings() async {
    maxStorageGB = await _storageService.getInt('storage_max_gb') ?? 5;
    warningThresholdPercentage = await _storageService.getInt('storage_warning_threshold') ?? 80;
    autoCleanupEnabled = await _storageService.getBool('storage_auto_cleanup') ?? true;
    autoCleanupDays = await _storageService.getInt('storage_auto_cleanup_days') ?? 30;
  }
  
  Future<void> updateSettings({
    int? maxStorageGB,
    int? warningThresholdPercentage,
    bool? autoCleanupEnabled,
    int? autoCleanupDays,
  }) async {
    if (maxStorageGB != null) {
      this.maxStorageGB = maxStorageGB;
      await _storageService.saveInt('storage_max_gb', maxStorageGB);
    }
    if (warningThresholdPercentage != null) {
      this.warningThresholdPercentage = warningThresholdPercentage;
      await _storageService.saveInt('storage_warning_threshold', warningThresholdPercentage);
    }
    if (autoCleanupEnabled != null) {
      this.autoCleanupEnabled = autoCleanupEnabled;
      await _storageService.saveBool('storage_auto_cleanup', autoCleanupEnabled);
    }
    if (autoCleanupDays != null) {
      this.autoCleanupDays = autoCleanupDays;
      await _storageService.saveInt('storage_auto_cleanup_days', autoCleanupDays);
    }
  }
  
  Future<StorageInfo> getStorageInfo() async {
    try {
      // Get app directory
      final appDir = await getApplicationDocumentsDirectory();
      
      // Calculate downloaded videos size
      final downloadedVideosSize = await _downloadManager.getTotalStorageUsed();
      
      // Calculate cache size
      final cacheSize = await _calculateCacheSize();
      
      // Calculate other app data size
      final otherAppDataSize = await _calculateDirectorySize(appDir) - downloadedVideosSize - cacheSize;
      
      // Get device storage info (simplified)
      final totalSpace = await _getDeviceTotalSpace();
      final freeSpace = await _getDeviceFreeSpace();
      final usedSpace = totalSpace - freeSpace;
      
      return StorageInfo(
        totalSpace: totalSpace,
        usedSpace: usedSpace,
        freeSpace: freeSpace,
        downloadedVideosSize: downloadedVideosSize,
        cacheSize: cacheSize,
        otherAppDataSize: otherAppDataSize > 0 ? otherAppDataSize : 0,
      );
    } catch (e) {
      debugPrint('Error getting storage info: $e');
      return StorageInfo(
        totalSpace: 0,
        usedSpace: 0,
        freeSpace: 0,
        downloadedVideosSize: 0,
        cacheSize: 0,
        otherAppDataSize: 0,
      );
    }
  }
  
  Future<int> _calculateCacheSize() async {
    try {
      final tempDir = await getTemporaryDirectory();
      return await _calculateDirectorySize(tempDir);
    } catch (e) {
      debugPrint('Error calculating cache size: $e');
      return 0;
    }
  }
  
  Future<int> _calculateDirectorySize(Directory directory) async {
    int size = 0;
    try {
      if (await directory.exists()) {
        await for (final entity in directory.list(recursive: true, followLinks: false)) {
          if (entity is File) {
            try {
              size += await entity.length();
            } catch (e) {
              // Skip files that can't be accessed
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error calculating directory size: $e');
    }
    return size;
  }
  
  Future<int> _getDeviceTotalSpace() async {
    // This is a simplified implementation
    // In a real app, you might use a plugin like device_info_plus
    // or platform-specific code to get actual device storage
    return 64 * 1024 * 1024 * 1024; // 64GB default
  }
  
  Future<int> _getDeviceFreeSpace() async {
    // This is a simplified implementation
    // In a real app, you might use platform-specific code
    return 32 * 1024 * 1024 * 1024; // 32GB default
  }
  
  Future<bool> checkStorageSpace({int? requiredBytes}) async {
    final storageInfo = await getStorageInfo();
    
    if (requiredBytes != null) {
      return storageInfo.freeSpace >= requiredBytes;
    }
    
    // Check if we're approaching the limit
    final maxBytes = maxStorageGB * 1024 * 1024 * 1024;
    final currentUsage = storageInfo.downloadedVideosSize;
    
    return currentUsage < maxBytes;
  }
  
  Future<bool> isStorageWarningNeeded() async {
    final storageInfo = await getStorageInfo();
    return storageInfo.usedPercentage >= warningThresholdPercentage;
  }
  
  Future<void> performAutoCleanup() async {
    if (!autoCleanupEnabled) return;
    
    try {
      // Clean up old downloads
      await _cleanupOldDownloads();
      
      // Clean up cache
      await _cleanupCache();
      
      debugPrint('Auto cleanup completed');
    } catch (e) {
      debugPrint('Error during auto cleanup: $e');
    }
  }
  
  Future<void> _cleanupOldDownloads() async {
    final downloads = await _downloadManager.getDownloadedItems();
    final cutoffDate = DateTime.now().subtract(Duration(days: autoCleanupDays));
    
    for (final download in downloads) {
      if (download.downloadedAt.isBefore(cutoffDate)) {
        await _downloadManager.deleteDownload(download.id);
        debugPrint('Deleted old download: ${download.title}');
      }
    }
  }
  
  Future<void> _cleanupCache() async {
    try {
      final tempDir = await getTemporaryDirectory();
      if (await tempDir.exists()) {
        await for (final entity in tempDir.list()) {
          try {
            if (entity is File) {
              await entity.delete();
            } else if (entity is Directory) {
              await entity.delete(recursive: true);
            }
          } catch (e) {
            // Skip files/directories that can't be deleted
          }
        }
      }
    } catch (e) {
      debugPrint('Error cleaning cache: $e');
    }
  }
  
  Future<void> clearAllCache() async {
    await _cleanupCache();
  }
  
  Future<void> clearAllDownloads() async {
    final downloads = await _downloadManager.getDownloadedItems();
    for (final download in downloads) {
      await _downloadManager.deleteDownload(download.id);
    }
  }
  
  String formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
  
  Future<List<Map<String, dynamic>>> getStorageBreakdown() async {
    final storageInfo = await getStorageInfo();
    
    return [
      {
        'name': 'Downloaded Videos',
        'size': storageInfo.downloadedVideosSize,
        'percentage': storageInfo.downloadedVideosPercentage,
        'color': const Color(0xFF2196F3), // Blue
      },
      {
        'name': 'Cache',
        'size': storageInfo.cacheSize,
        'percentage': storageInfo.cachePercentage,
        'color': const Color(0xFFFF9800), // Orange
      },
      {
        'name': 'App Data',
        'size': storageInfo.otherAppDataSize,
        'percentage': (storageInfo.otherAppDataSize / storageInfo.totalSpace) * 100,
        'color': const Color(0xFF4CAF50), // Green
      },
      {
        'name': 'Free Space',
        'size': storageInfo.freeSpace,
        'percentage': (storageInfo.freeSpace / storageInfo.totalSpace) * 100,
        'color': const Color(0xFFE0E0E0), // Light Grey
      },
    ];
  }
}
