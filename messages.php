<?php
require_once 'includes/header.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect(SITE_URL . '/login.php');
}

// Get user's chat sessions
$user_id = $_SESSION['user_id'];
$sessions_query = "SELECT cs.*, u.username, u.profile_image, u.role,
                  (SELECT COUNT(*) FROM chat_messages WHERE session_id = cs.id AND receiver_id = $user_id AND is_read = FALSE) as unread_count,
                  (SELECT message FROM chat_messages WHERE session_id = cs.id ORDER BY created_at DESC LIMIT 1) as last_message,
                  (SELECT created_at FROM chat_messages WHERE session_id = cs.id ORDER BY created_at DESC LIMIT 1) as last_message_time
                  FROM chat_sessions cs
                  JOIN users u ON cs.admin_id = u.id
                  WHERE cs.user_id = $user_id
                  ORDER BY cs.updated_at DESC";
$sessions_result = mysqli_query($conn, $sessions_query);

// Get selected session
$selected_session = null;
$session_messages = [];

if (isset($_GET['session']) && $_GET['session'] > 0) {
    $session_id = (int)$_GET['session'];

    // Get session details
    $session_query = "SELECT cs.*, u.username, u.email, u.profile_image, u.role
                     FROM chat_sessions cs
                     JOIN users u ON cs.admin_id = u.id
                     WHERE cs.id = $session_id AND cs.user_id = $user_id";
    $session_result = mysqli_query($conn, $session_query);

    if (mysqli_num_rows($session_result) > 0) {
        $selected_session = mysqli_fetch_assoc($session_result);

        // Mark messages as read
        $update_query = "UPDATE chat_messages SET is_read = TRUE
                        WHERE session_id = $session_id AND receiver_id = $user_id AND is_read = FALSE";
        mysqli_query($conn, $update_query);

        // Get messages
        $messages_query = "SELECT cm.*, u.username, u.profile_image, u.role
                          FROM chat_messages cm
                          JOIN users u ON cm.sender_id = u.id
                          WHERE cm.session_id = $session_id
                          ORDER BY cm.created_at ASC";
        $messages_result = mysqli_query($conn, $messages_query);

        while ($message = mysqli_fetch_assoc($messages_result)) {
            $session_messages[] = $message;
        }
    }
}

// Process message sending
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_message'])) {
    $session_id = (int)$_POST['session_id'];
    $message = sanitize($_POST['message']);
    $receiver_id = (int)$_POST['receiver_id'];

    if (empty($message)) {
        $error_message = 'Message cannot be empty.';
    } else {
        $insert_query = "INSERT INTO chat_messages (session_id, sender_id, receiver_id, message)
                        VALUES ($session_id, $user_id, $receiver_id, '$message')";

        if (mysqli_query($conn, $insert_query)) {
            // Update session timestamp
            $update_query = "UPDATE chat_sessions SET updated_at = NOW() WHERE id = $session_id";
            mysqli_query($conn, $update_query);

            // Redirect to avoid form resubmission
            redirect(SITE_URL . "/messages.php?session=$session_id");
        } else {
            $error_message = 'Error sending message: ' . mysqli_error($conn);
        }
    }
}
?>

<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="mb-0">আমার চ্যাট</h2>
                    <button class="btn btn-danger" id="newChatBtn">
                        <i class="fas fa-plus me-2"></i> নতুন চ্যাট শুরু করুন
                    </button>
                </div>

                <div class="chat-container" style="height: 600px; display: flex; border-radius: 10px; overflow: hidden; box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);">
                    <div class="chat-sidebar" style="width: 300px; background-color: #f8f9fa; border-right: 1px solid #dee2e6; overflow-y: auto;">
                        <div class="p-3 bg-light border-bottom">
                            <h5 class="mb-0">আমার চ্যাটসমূহ</h5>
                        </div>
                        <?php if (mysqli_num_rows($sessions_result) > 0): ?>
                            <?php while ($session = mysqli_fetch_assoc($sessions_result)): ?>
                                <div class="chat-session-item <?php echo (isset($_GET['session']) && $_GET['session'] == $session['id']) ? 'active' : ''; ?>"
                                     onclick="window.location.href='messages.php?session=<?php echo $session['id']; ?>'"
                                     style="padding: 15px; border-bottom: 1px solid #dee2e6; cursor: pointer; transition: all 0.3s ease;<?php echo (isset($_GET['session']) && $_GET['session'] == $session['id']) ? 'background-color: #e9ecef; border-left: 4px solid #dc3545;' : ''; ?>">
                                    <div class="chat-session-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                        <div class="user-info" style="display: flex; align-items: center;">
                                            <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $session['profile_image'] ? $session['profile_image'] : 'default.jpg'; ?>"
                                                 alt="User Avatar"
                                                 style="width: 40px; height: 40px; border-radius: 50%; margin-right: 10px; object-fit: cover;">
                                            <h6 class="chat-session-name" style="font-weight: 600; margin: 0;">
                                                <?php echo $session['username']; ?>
                                                <?php if ($session['unread_count'] > 0): ?>
                                                    <span class="unread-badge" style="display: inline-block; width: 20px; height: 20px; background-color: #dc3545; color: #fff; border-radius: 50%; text-align: center; font-size: 0.75rem; line-height: 20px; margin-left: 5px;"><?php echo $session['unread_count']; ?></span>
                                                <?php endif; ?>
                                            </h6>
                                        </div>
                                    </div>
                                    <div class="chat-session-time" style="font-size: 0.8rem; color: #6c757d;">
                                        <?php echo date('M d, h:i A', strtotime($session['last_message_time'] ?? $session['updated_at'])); ?>
                                    </div>
                                    <?php if (!empty($session['last_message'])): ?>
                                        <div class="chat-session-preview" style="font-size: 0.85rem; color: #6c757d; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                                            <?php echo mb_substr($session['last_message'], 0, 30); ?><?php echo (mb_strlen($session['last_message']) > 30) ? '...' : ''; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <div class="p-4 text-center text-muted">
                                <i class="fas fa-comments fa-3x mb-3 opacity-25"></i>
                                <p>কোন চ্যাট সেশন নেই</p>
                                <p>নতুন চ্যাট শুরু করতে বাটনে ক্লিক করুন</p>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="chat-main" style="flex: 1; display: flex; flex-direction: column; background-color: #fff;">
                        <?php if ($selected_session): ?>
                            <div class="chat-header" style="padding: 15px; border-bottom: 1px solid #dee2e6; background-color: #f8f9fa; display: flex; justify-content: space-between; align-items: center;">
                                <div class="user-info" style="display: flex; align-items: center;">
                                    <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $selected_session['profile_image'] ? $selected_session['profile_image'] : 'default.jpg'; ?>"
                                         alt="User Avatar"
                                         style="width: 40px; height: 40px; border-radius: 50%; margin-right: 10px; object-fit: cover;">
                                    <div>
                                        <h5 class="mb-0"><?php echo $selected_session['username']; ?></h5>
                                        <small class="text-muted"><?php echo $selected_session['role'] == 'admin' ? 'Admin' : 'User'; ?></small>
                                    </div>
                                </div>
                            </div>
                            <div class="chat-messages" id="chatMessages" style="flex: 1; padding: 15px; overflow-y: auto; background-color: #f8f9fa;">
                                <?php foreach ($session_messages as $message): ?>
                                    <div class="chat-message <?php echo $message['sender_id'] == $user_id ? 'user' : 'admin'; ?>"
                                         style="margin-bottom: 15px; display: flex; flex-direction: column; max-width: 80%; <?php echo $message['sender_id'] == $user_id ? 'align-self: flex-end;' : 'align-self: flex-start;'; ?>">
                                        <div class="chat-message-bubble"
                                             style="padding: 10px 15px; border-radius: 15px; position: relative; word-break: break-word; <?php echo $message['sender_id'] == $user_id ? 'background-color: #dc3545; color: #fff; border-bottom-right-radius: 5px;' : 'background-color: #f1f0f0; border-bottom-left-radius: 5px;'; ?>">
                                            <?php echo nl2br($message['message']); ?>
                                        </div>
                                        <div class="chat-message-info" style="display: flex; justify-content: space-between; font-size: 0.75rem; color: #6c757d; margin-top: 5px;">
                                            <span><?php echo $message['username']; ?></span>
                                            <span class="chat-message-time"><?php echo date('h:i A', strtotime($message['created_at'])); ?></span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="chat-input" style="padding: 15px; border-top: 1px solid #dee2e6; background-color: #fff;">
                                <form method="POST" action="">
                                    <input type="hidden" name="session_id" value="<?php echo $selected_session['id']; ?>">
                                    <input type="hidden" name="receiver_id" value="<?php echo $selected_session['admin_id']; ?>">
                                    <div class="input-group">
                                        <textarea class="form-control" name="message" placeholder="আপনার মেসেজ লিখুন..." rows="1" required></textarea>
                                        <button type="submit" name="send_message" class="btn btn-primary">
                                            <i class="fas fa-paper-plane"></i>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        <?php else: ?>
                            <div class="chat-empty" style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100%; color: #6c757d;">
                                <i class="fas fa-comments" style="font-size: 4rem; margin-bottom: 20px; color: #dee2e6;"></i>
                                <h4>কোন চ্যাট নির্বাচন করা হয়নি</h4>
                                <p>বাম দিক থেকে একটি চ্যাট নির্বাচন করুন অথবা নতুন চ্যাট শুরু করুন</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- New Chat Modal -->
    <div class="modal fade" id="newChatModal" tabindex="-1" aria-labelledby="newChatModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="newChatModalLabel">নতুন চ্যাট শুরু করুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>একজন এডমিনের সাথে নতুন চ্যাট শুরু করতে নিচের ফরমটি পূরণ করুন।</p>
                    <form id="newChatForm" method="post" action="<?php echo SITE_URL; ?>/start_chat.php">
                        <div class="mb-3">
                            <label for="chatMessage" class="form-label">আপনার প্রথম মেসেজ</label>
                            <textarea class="form-control" id="chatMessage" name="message" rows="4" placeholder="আপনার প্রথম মেসেজ লিখুন..." required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">চ্যাট শুরু করুন</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Chat page scripts
document.addEventListener('DOMContentLoaded', function() {
    // New chat button
    const newChatBtn = document.getElementById('newChatBtn');

    if (newChatBtn) {
        newChatBtn.addEventListener('click', function() {
            const newChatModal = new bootstrap.Modal(document.getElementById('newChatModal'));
            newChatModal.show();
        });
    }

    // Auto-scroll to bottom of chat messages
    const chatMessages = document.getElementById('chatMessages');
    if (chatMessages) {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Auto-resize textarea
    const textarea = document.querySelector('.chat-input textarea');
    if (textarea) {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
    }

    // Poll for new messages every 3 seconds
    <?php if ($selected_session): ?>
    const sessionId = <?php echo $selected_session['id']; ?>;
    const userId = <?php echo $user_id; ?>;
    let lastMessageId = <?php echo !empty($session_messages) ? end($session_messages)['id'] : 0; ?>;

    setInterval(function() {
        fetch('<?php echo SITE_URL; ?>/chat_server.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=poll&session_id=' + sessionId + '&last_message_id=' + lastMessageId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.messages.length > 0) {
                data.messages.forEach(message => {
                    // Create message element
                    const messageElement = document.createElement('div');
                    messageElement.classList.add('chat-message');
                    messageElement.classList.add(message.sender_id == userId ? 'user' : 'admin');
                    messageElement.style.marginBottom = '15px';
                    messageElement.style.display = 'flex';
                    messageElement.style.flexDirection = 'column';
                    messageElement.style.maxWidth = '80%';
                    messageElement.style.alignSelf = message.sender_id == userId ? 'flex-end' : 'flex-start';

                    // Create message bubble
                    const messageBubble = document.createElement('div');
                    messageBubble.classList.add('chat-message-bubble');
                    messageBubble.style.padding = '10px 15px';
                    messageBubble.style.borderRadius = '15px';
                    messageBubble.style.position = 'relative';
                    messageBubble.style.wordBreak = 'break-word';

                    if (message.sender_id == userId) {
                        messageBubble.style.backgroundColor = '#dc3545';
                        messageBubble.style.color = '#fff';
                        messageBubble.style.borderBottomRightRadius = '5px';
                    } else {
                        messageBubble.style.backgroundColor = '#f1f0f0';
                        messageBubble.style.borderBottomLeftRadius = '5px';
                    }

                    messageBubble.innerHTML = message.message.replace(/\n/g, '<br>');

                    // Create message info
                    const messageInfo = document.createElement('div');
                    messageInfo.classList.add('chat-message-info');
                    messageInfo.style.display = 'flex';
                    messageInfo.style.justifyContent = 'space-between';
                    messageInfo.style.fontSize = '0.75rem';
                    messageInfo.style.color = '#6c757d';
                    messageInfo.style.marginTop = '5px';

                    const usernameSpan = document.createElement('span');
                    usernameSpan.textContent = message.username;

                    const timeSpan = document.createElement('span');
                    timeSpan.classList.add('chat-message-time');
                    const date = new Date(message.created_at);
                    timeSpan.textContent = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

                    messageInfo.appendChild(usernameSpan);
                    messageInfo.appendChild(timeSpan);

                    // Append elements
                    messageElement.appendChild(messageBubble);
                    messageElement.appendChild(messageInfo);

                    // Add to chat messages
                    chatMessages.appendChild(messageElement);

                    // Update last message ID
                    if (message.id > lastMessageId) {
                        lastMessageId = message.id;
                    }
                });

                // Scroll to bottom
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
        })
        .catch(error => {
            console.error('Error polling messages:', error);
        });
    }, 3000);
    <?php endif; ?>
});
</script>

<?php require_once 'includes/footer.php'; ?>
