import 'package:flutter/material.dart';
import 'package:cinepix_app/constants/app_constants.dart';
import 'package:cinepix_app/models/movie.dart';
import 'package:cinepix_app/models/tv_show.dart';
import 'package:cinepix_app/widgets/movie_card.dart';
import 'package:cinepix_app/widgets/tv_show_card.dart';
import 'package:shimmer/shimmer.dart';

class GenreSection extends StatelessWidget {
  final String genreName;
  final List<dynamic> items;
  final bool isLoading;
  final Function(dynamic) onItemTap;
  final VoidCallback? onSeeAllTap;
  
  const GenreSection({
    super.key,
    required this.genreName,
    required this.items,
    required this.isLoading,
    required this.onItemTap,
    this.onSeeAllTap,
  });
  
  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return _buildLoadingList();
    }
    
    if (items.isEmpty) {
      return const SizedBox.shrink(); // Hide section if empty
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                genreName,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (onSeeAllTap != null)
                TextButton(
                  onPressed: onSeeAllTap,
                  child: const Text('See All'),
                ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 220,
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            scrollDirection: Axis.horizontal,
            itemCount: items.length > 10 ? 10 : items.length, // Limit to 10 items
            itemBuilder: (context, index) {
              final item = items[index];
              
              if (item is Movie) {
                return MovieCard(
                  movie: item,
                  onTap: () => onItemTap(item),
                );
              } else if (item is TvShow) {
                return TvShowCard(
                  tvShow: item,
                  onTap: () => onItemTap(item),
                );
              }
              
              return const SizedBox.shrink();
            },
          ),
        ),
      ],
    );
  }
  
  Widget _buildLoadingList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(
            genreName,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 220,
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            scrollDirection: Axis.horizontal,
            itemCount: 5,
            itemBuilder: (context, index) {
              return Shimmer.fromColors(
                baseColor: Colors.grey[850]!,
                highlightColor: Colors.grey[700]!,
                child: Container(
                  width: 140,
                  margin: const EdgeInsets.only(right: 10),
                  decoration: BoxDecoration(
                    color: Colors.grey[850],
                    borderRadius: BorderRadius.circular(AppConstants.cardBorderRadius),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
