import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class VideoCacheService {
  static const String _cacheInfoKey = 'video_cache_info';
  static const int _maxCacheSize = 1024 * 1024 * 1024; // 1GB max cache size

  // Singleton instance
  static final VideoCacheService _instance = VideoCacheService._internal();

  factory VideoCacheService() {
    return _instance;
  }

  VideoCacheService._internal();

  // Get cache directory
  Future<Directory> get _cacheDir async {
    final Directory appCacheDir = await getTemporaryDirectory();
    final Directory cacheDir = Directory('${appCacheDir.path}/video_cache');

    if (!await cacheDir.exists()) {
      await cacheDir.create(recursive: true);
    }

    return cacheDir;
  }

  // Generate a unique filename for a URL
  String _generateCacheKey(String url) {
    final bytes = utf8.encode(url);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Check if a video is cached
  Future<bool> isVideoCached(String url) async {
    final String cacheKey = _generateCacheKey(url);
    final Directory cacheDir = await _cacheDir;
    final File cacheFile = File('${cacheDir.path}/$cacheKey');

    return await cacheFile.exists();
  }

  // Get cached video file path
  Future<String?> getCachedVideoPath(String url) async {
    final bool isCached = await isVideoCached(url);

    if (isCached) {
      final String cacheKey = _generateCacheKey(url);
      final Directory cacheDir = await _cacheDir;
      return '${cacheDir.path}/$cacheKey';
    }

    return null;
  }

  // Get cached video file
  Future<File> getCachedFile(String url) async {
    final String? path = await getCachedVideoPath(url);

    if (path != null) {
      return File(path);
    }

    throw Exception('Video is not cached');
  }

  // Cache a video
  Future<String?> cacheVideo(String url, {Function(double)? onProgress}) async {
    try {
      final String cacheKey = _generateCacheKey(url);
      final Directory cacheDir = await _cacheDir;
      final File cacheFile = File('${cacheDir.path}/$cacheKey');

      // Check if already cached
      if (await cacheFile.exists()) {
        return cacheFile.path;
      }

      // Check available space and clean cache if needed
      await _cleanCacheIfNeeded();

      // Download the file
      final http.Client client = http.Client();
      final http.Request request = http.Request('GET', Uri.parse(url));
      final http.StreamedResponse response = await client.send(request);

      if (response.statusCode != 200) {
        return null;
      }

      // Get total size for progress calculation
      final int? totalBytes = response.contentLength;
      int receivedBytes = 0;

      // Create file and write stream
      final IOSink sink = cacheFile.openWrite();

      // Process the stream
      await response.stream.forEach((List<int> chunk) {
        sink.add(chunk);
        receivedBytes += chunk.length;

        if (totalBytes != null && onProgress != null) {
          final double progress = receivedBytes / totalBytes;
          onProgress(progress);
        }
      });

      // Close the file
      await sink.flush();
      await sink.close();

      // Save cache info
      await _saveCacheInfo(url, cacheKey, cacheFile.lengthSync());

      return cacheFile.path;
    } catch (e) {
      debugPrint('Error caching video: $e');
      return null;
    }
  }

  // Save cache info to SharedPreferences
  Future<void> _saveCacheInfo(String url, String cacheKey, int fileSize) async {
    final prefs = await SharedPreferences.getInstance();
    final String? cacheInfoJson = prefs.getString(_cacheInfoKey);

    Map<String, dynamic> cacheInfo = {};

    if (cacheInfoJson != null) {
      cacheInfo = json.decode(cacheInfoJson);
    }

    cacheInfo[cacheKey] = {
      'url': url,
      'size': fileSize,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    await prefs.setString(_cacheInfoKey, json.encode(cacheInfo));
  }

  // Clean cache if it exceeds the maximum size
  Future<void> _cleanCacheIfNeeded() async {
    final prefs = await SharedPreferences.getInstance();
    final String? cacheInfoJson = prefs.getString(_cacheInfoKey);

    if (cacheInfoJson == null) {
      return;
    }

    final Map<String, dynamic> cacheInfo = json.decode(cacheInfoJson);
    final Directory cacheDir = await _cacheDir;

    // Calculate total cache size
    int totalSize = 0;
    for (final entry in cacheInfo.values) {
      totalSize += entry['size'] as int;
    }

    // If cache size exceeds the maximum, remove oldest files
    if (totalSize > _maxCacheSize) {
      // Sort entries by timestamp (oldest first)
      final List<MapEntry<String, dynamic>> sortedEntries = cacheInfo.entries
          .toList()
        ..sort((a, b) => (a.value['timestamp'] as int)
            .compareTo(b.value['timestamp'] as int));

      // Remove oldest files until cache size is below the limit
      for (final entry in sortedEntries) {
        final String cacheKey = entry.key;
        final int fileSize = entry.value['size'] as int;

        // Delete the file
        final File cacheFile = File('${cacheDir.path}/$cacheKey');
        if (await cacheFile.exists()) {
          await cacheFile.delete();
        }

        // Remove from cache info
        cacheInfo.remove(cacheKey);

        // Update total size
        totalSize -= fileSize;

        // Stop if cache size is below the limit
        if (totalSize <= _maxCacheSize * 0.8) {
          break;
        }
      }

      // Save updated cache info
      await prefs.setString(_cacheInfoKey, json.encode(cacheInfo));
    }
  }

  // Clear all cached videos
  Future<void> clearCache() async {
    final Directory cacheDir = await _cacheDir;

    if (await cacheDir.exists()) {
      await cacheDir.delete(recursive: true);
      await cacheDir.create();
    }

    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_cacheInfoKey);
  }

  // Get total cache size
  Future<int> getCacheSize() async {
    final prefs = await SharedPreferences.getInstance();
    final String? cacheInfoJson = prefs.getString(_cacheInfoKey);

    if (cacheInfoJson == null) {
      return 0;
    }

    final Map<String, dynamic> cacheInfo = json.decode(cacheInfoJson);

    int totalSize = 0;
    for (final entry in cacheInfo.values) {
      totalSize += entry['size'] as int;
    }

    return totalSize;
  }

  // Get formatted cache size (KB, MB, GB)
  Future<String> getFormattedCacheSize() async {
    final int totalBytes = await getCacheSize();

    if (totalBytes < 1024) {
      return '$totalBytes B';
    } else if (totalBytes < 1024 * 1024) {
      final double kb = totalBytes / 1024;
      return '${kb.toStringAsFixed(1)} KB';
    } else if (totalBytes < 1024 * 1024 * 1024) {
      final double mb = totalBytes / (1024 * 1024);
      return '${mb.toStringAsFixed(1)} MB';
    } else {
      final double gb = totalBytes / (1024 * 1024 * 1024);
      return '${gb.toStringAsFixed(1)} GB';
    }
  }
}
