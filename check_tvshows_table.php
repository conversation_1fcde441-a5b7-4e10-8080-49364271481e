<?php
require_once 'includes/config.php';

echo "<h2>টিভি শো টেবিল চেক</h2>";

// Check tvshows table structure
echo "<h3>টিভি শো টেবিল স্ট্রাকচার:</h3>";
$table_check = mysqli_query($conn, "DESCRIBE tvshows");
if ($table_check) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ফিল্ড</th><th>টাইপ</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = mysqli_fetch_assoc($table_check)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ টিভি শো টেবিল চেক করতে সমস্যা হয়েছে</p>";
}

// Check movies table structure for comparison
echo "<h3>মুভি টেবিল স্ট্রাকচার (তুলনা):</h3>";
$movies_check = mysqli_query($conn, "DESCRIBE movies");
if ($movies_check) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ফিল্ড</th><th>টাইপ</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = mysqli_fetch_assoc($movies_check)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ মুভি টেবিল চেক করতে সমস্যা হয়েছে</p>";
}

// Check if tvshows table exists
echo "<h3>টিভি শো টেবিল আছে কিনা:</h3>";
$table_exists = mysqli_query($conn, "SHOW TABLES LIKE 'tvshows'");
if (mysqli_num_rows($table_exists) > 0) {
    echo "<p style='color: green;'>✅ টিভি শো টেবিল আছে</p>";
} else {
    echo "<p style='color: red;'>❌ টিভি শো টেবিল নেই</p>";
}
?> 