<?php
// API TV Shows Endpoints

// Get the request
global $request;

// Handle different endpoints based on the path parts
$action = $request['parts'][0] ?? 'list';

switch ($action) {
    case 'list':
        handle_list_tvshows($request);
        break;
    
    case 'featured':
        handle_featured_tvshows($request);
        break;
    
    case 'popular':
        handle_popular_tvshows($request);
        break;
    
    case 'latest':
        handle_latest_tvshows($request);
        break;
    
    case 'categories':
        handle_tvshow_categories($request);
        break;
    
    case 'seasons':
        if (isset($request['parts'][1]) && is_numeric($request['parts'][1])) {
            handle_tvshow_seasons($request['parts'][1], $request);
        } else {
            api_error('Invalid TV show ID', 400);
        }
        break;
    
    case 'episodes':
        if (isset($request['parts'][1]) && is_numeric($request['parts'][1]) && 
            isset($request['parts'][2]) && is_numeric($request['parts'][2])) {
            handle_season_episodes($request['parts'][1], $request['parts'][2], $request);
        } else {
            api_error('Invalid TV show ID or season number', 400);
        }
        break;
    
    default:
        // If action is numeric, treat it as TV show ID
        if (is_numeric($action)) {
            handle_get_tvshow($action, $request);
        } else {
            api_error('Invalid tvshows endpoint', 404);
        }
}

// Handle listing TV shows
function handle_list_tvshows($request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get query parameters
    $page = isset($request['params']['page']) ? (int)$request['params']['page'] : 1;
    $limit = isset($request['params']['limit']) ? (int)$request['params']['limit'] : 20;
    $category_id = isset($request['params']['category']) ? (int)$request['params']['category'] : 0;
    $search = isset($request['params']['search']) ? $request['params']['search'] : '';
    $year = isset($request['params']['year']) ? (int)$request['params']['year'] : 0;
    $sort = isset($request['params']['sort']) ? $request['params']['sort'] : 'latest';
    
    // Validate parameters
    if ($page < 1) $page = 1;
    if ($limit < 1 || $limit > 100) $limit = 20;
    
    // Calculate offset
    $offset = ($page - 1) * $limit;
    
    // Build query
    $query = "SELECT t.*, c.name as category_name 
              FROM tvshows t 
              LEFT JOIN categories c ON t.category_id = c.id 
              WHERE 1=1";
    
    $count_query = "SELECT COUNT(*) as total FROM tvshows WHERE 1=1";
    
    $params = [];
    $types = '';
    
    // Add category filter
    if ($category_id > 0) {
        $query .= " AND t.category_id = ?";
        $count_query .= " AND category_id = ?";
        $params[] = $category_id;
        $types .= 'i';
    }
    
    // Add search filter
    if (!empty($search)) {
        $search_term = "%$search%";
        $query .= " AND (t.title LIKE ? OR t.description LIKE ?)";
        $count_query .= " AND (title LIKE ? OR description LIKE ?)";
        $params[] = $search_term;
        $params[] = $search_term;
        $types .= 'ss';
    }
    
    // Add year filter
    if ($year > 0) {
        $query .= " AND t.start_year = ?";
        $count_query .= " AND start_year = ?";
        $params[] = $year;
        $types .= 'i';
    }
    
    // Add sorting
    switch ($sort) {
        case 'title':
            $query .= " ORDER BY t.title ASC";
            break;
        case 'rating':
            $query .= " ORDER BY t.rating DESC";
            break;
        case 'views':
            $query .= " ORDER BY t.views DESC";
            break;
        case 'oldest':
            $query .= " ORDER BY t.created_at ASC";
            break;
        case 'latest':
        default:
            $query .= " ORDER BY t.created_at DESC";
            break;
    }
    
    // Add pagination
    $query .= " LIMIT ?, ?";
    $params[] = $offset;
    $params[] = $limit;
    $types .= 'ii';
    
    // Get total count
    $count_stmt = mysqli_prepare($conn, $count_query);
    if (!empty($params)) {
        $count_types = substr($types, 0, -2); // Remove the last two characters (for pagination)
        mysqli_stmt_bind_param($count_stmt, $count_types, ...$params);
    }
    mysqli_stmt_execute($count_stmt);
    $count_result = mysqli_stmt_get_result($count_stmt);
    $total = mysqli_fetch_assoc($count_result)['total'];
    
    // Get TV shows
    $stmt = mysqli_prepare($conn, $query);
    if (!empty($params)) {
        mysqli_stmt_bind_param($stmt, $types, ...$params);
    }
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $tvshows = [];
    while ($tvshow = mysqli_fetch_assoc($result)) {
        // Format TV show data
        $tvshows[] = format_tvshow_data($tvshow);
    }
    
    // Calculate pagination info
    $total_pages = ceil($total / $limit);
    
    // Return response
    api_response([
        'success' => true,
        'data' => [
            'tvshows' => $tvshows,
            'pagination' => [
                'total' => $total,
                'per_page' => $limit,
                'current_page' => $page,
                'last_page' => $total_pages,
                'from' => $offset + 1,
                'to' => min($offset + $limit, $total)
            ]
        ]
    ]);
}

// Handle featured TV shows
function handle_featured_tvshows($request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get query parameters
    $limit = isset($request['params']['limit']) ? (int)$request['params']['limit'] : 10;
    
    // Validate parameters
    if ($limit < 1 || $limit > 50) $limit = 10;
    
    // Get featured TV shows
    $query = "SELECT t.*, c.name as category_name 
              FROM tvshows t 
              LEFT JOIN categories c ON t.category_id = c.id 
              WHERE t.featured = 1 
              ORDER BY t.created_at DESC 
              LIMIT ?";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $limit);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $tvshows = [];
    while ($tvshow = mysqli_fetch_assoc($result)) {
        // Format TV show data
        $tvshows[] = format_tvshow_data($tvshow);
    }
    
    // Return response
    api_response([
        'success' => true,
        'data' => [
            'tvshows' => $tvshows
        ]
    ]);
}

// Handle popular TV shows
function handle_popular_tvshows($request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get query parameters
    $limit = isset($request['params']['limit']) ? (int)$request['params']['limit'] : 10;
    
    // Validate parameters
    if ($limit < 1 || $limit > 50) $limit = 10;
    
    // Get popular TV shows (based on views)
    $query = "SELECT t.*, c.name as category_name 
              FROM tvshows t 
              LEFT JOIN categories c ON t.category_id = c.id 
              ORDER BY t.views DESC 
              LIMIT ?";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $limit);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $tvshows = [];
    while ($tvshow = mysqli_fetch_assoc($result)) {
        // Format TV show data
        $tvshows[] = format_tvshow_data($tvshow);
    }
    
    // Return response
    api_response([
        'success' => true,
        'data' => [
            'tvshows' => $tvshows
        ]
    ]);
}

// Handle latest TV shows
function handle_latest_tvshows($request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get query parameters
    $limit = isset($request['params']['limit']) ? (int)$request['params']['limit'] : 10;
    
    // Validate parameters
    if ($limit < 1 || $limit > 50) $limit = 10;
    
    // Get latest TV shows
    $query = "SELECT t.*, c.name as category_name 
              FROM tvshows t 
              LEFT JOIN categories c ON t.category_id = c.id 
              ORDER BY t.created_at DESC 
              LIMIT ?";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $limit);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $tvshows = [];
    while ($tvshow = mysqli_fetch_assoc($result)) {
        // Format TV show data
        $tvshows[] = format_tvshow_data($tvshow);
    }
    
    // Return response
    api_response([
        'success' => true,
        'data' => [
            'tvshows' => $tvshows
        ]
    ]);
}

// Handle TV show categories
function handle_tvshow_categories($request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get categories for TV shows
    $query = "SELECT c.*, COUNT(t.id) as tvshow_count 
              FROM categories c 
              LEFT JOIN tvshows t ON c.id = t.category_id 
              WHERE c.type IN ('tvshow', 'both') 
              GROUP BY c.id 
              ORDER BY c.name ASC";
    
    $result = mysqli_query($conn, $query);
    
    $categories = [];
    while ($category = mysqli_fetch_assoc($result)) {
        $categories[] = [
            'id' => (int)$category['id'],
            'name' => $category['name'],
            'slug' => $category['slug'],
            'tvshow_count' => (int)$category['tvshow_count']
        ];
    }
    
    // Return response
    api_response([
        'success' => true,
        'data' => [
            'categories' => $categories
        ]
    ]);
}

// Handle get TV show by ID
function handle_get_tvshow($tvshow_id, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Get TV show details
    $query = "SELECT t.*, c.name as category_name 
              FROM tvshows t 
              LEFT JOIN categories c ON t.category_id = c.id 
              WHERE t.id = ?";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $tvshow_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($result) === 0) {
        api_error('TV show not found', 404);
    }
    
    $tvshow = mysqli_fetch_assoc($result);
    
    // Get seasons count
    $seasons_query = "SELECT DISTINCT season_number FROM episodes WHERE tvshow_id = ? ORDER BY season_number";
    $seasons_stmt = mysqli_prepare($conn, $seasons_query);
    mysqli_stmt_bind_param($seasons_stmt, 'i', $tvshow_id);
    mysqli_stmt_execute($seasons_stmt);
    $seasons_result = mysqli_stmt_get_result($seasons_stmt);
    
    $seasons = [];
    while ($season = mysqli_fetch_assoc($seasons_result)) {
        $season_number = $season['season_number'];
        
        // Get episodes count for this season
        $episodes_count_query = "SELECT COUNT(*) as count FROM episodes WHERE tvshow_id = ? AND season_number = ?";
        $episodes_count_stmt = mysqli_prepare($conn, $episodes_count_query);
        mysqli_stmt_bind_param($episodes_count_stmt, 'ii', $tvshow_id, $season_number);
        mysqli_stmt_execute($episodes_count_stmt);
        $episodes_count_result = mysqli_stmt_get_result($episodes_count_stmt);
        $episodes_count = mysqli_fetch_assoc($episodes_count_result)['count'];
        
        $seasons[] = [
            'season_number' => (int)$season_number,
            'episodes_count' => (int)$episodes_count
        ];
    }
    
    // Get streaming links
    $streaming_links_query = "SELECT * FROM streaming_links 
                             WHERE content_type = 'tvshow' AND content_id = ? 
                             ORDER BY quality, server_name";
    
    $streaming_stmt = mysqli_prepare($conn, $streaming_links_query);
    mysqli_stmt_bind_param($streaming_stmt, 'i', $tvshow_id);
    mysqli_stmt_execute($streaming_stmt);
    $streaming_result = mysqli_stmt_get_result($streaming_stmt);
    
    $streaming_links = [];
    while ($link = mysqli_fetch_assoc($streaming_result)) {
        $streaming_links[] = [
            'id' => (int)$link['id'],
            'quality' => $link['quality'],
            'server_name' => $link['server_name'],
            'is_premium' => (bool)$link['is_premium'],
            'stream_url' => is_premium_content($link) ? null : $link['stream_url']
        ];
    }
    
    // Get related TV shows
    $related_query = "SELECT t.*, c.name as category_name 
                     FROM tvshows t 
                     LEFT JOIN categories c ON t.category_id = c.id 
                     WHERE t.category_id = ? AND t.id != ? 
                     ORDER BY RAND() 
                     LIMIT 6";
    
    $related_stmt = mysqli_prepare($conn, $related_query);
    mysqli_stmt_bind_param($related_stmt, 'ii', $tvshow['category_id'], $tvshow_id);
    mysqli_stmt_execute($related_stmt);
    $related_result = mysqli_stmt_get_result($related_stmt);
    
    $related_tvshows = [];
    while ($related = mysqli_fetch_assoc($related_result)) {
        $related_tvshows[] = format_tvshow_data($related);
    }
    
    // Increment view count
    $update_query = "UPDATE tvshows SET views = views + 1 WHERE id = ?";
    $update_stmt = mysqli_prepare($conn, $update_query);
    mysqli_stmt_bind_param($update_stmt, 'i', $tvshow_id);
    mysqli_stmt_execute($update_stmt);
    
    // Format TV show data with additional details
    $tvshow_data = format_tvshow_data($tvshow);
    $tvshow_data['seasons'] = $seasons;
    $tvshow_data['streaming_links'] = $streaming_links;
    $tvshow_data['related_tvshows'] = $related_tvshows;
    
    // Return response
    api_response([
        'success' => true,
        'data' => $tvshow_data
    ]);
}

// Handle get TV show seasons
function handle_tvshow_seasons($tvshow_id, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Check if TV show exists
    $tvshow_query = "SELECT * FROM tvshows WHERE id = ?";
    $tvshow_stmt = mysqli_prepare($conn, $tvshow_query);
    mysqli_stmt_bind_param($tvshow_stmt, 'i', $tvshow_id);
    mysqli_stmt_execute($tvshow_stmt);
    $tvshow_result = mysqli_stmt_get_result($tvshow_stmt);
    
    if (mysqli_num_rows($tvshow_result) === 0) {
        api_error('TV show not found', 404);
    }
    
    $tvshow = mysqli_fetch_assoc($tvshow_result);
    
    // Get seasons
    $seasons_query = "SELECT DISTINCT season_number FROM episodes WHERE tvshow_id = ? ORDER BY season_number";
    $seasons_stmt = mysqli_prepare($conn, $seasons_query);
    mysqli_stmt_bind_param($seasons_stmt, 'i', $tvshow_id);
    mysqli_stmt_execute($seasons_stmt);
    $seasons_result = mysqli_stmt_get_result($seasons_stmt);
    
    $seasons = [];
    while ($season = mysqli_fetch_assoc($seasons_result)) {
        $season_number = $season['season_number'];
        
        // Get episodes count for this season
        $episodes_count_query = "SELECT COUNT(*) as count FROM episodes WHERE tvshow_id = ? AND season_number = ?";
        $episodes_count_stmt = mysqli_prepare($conn, $episodes_count_query);
        mysqli_stmt_bind_param($episodes_count_stmt, 'ii', $tvshow_id, $season_number);
        mysqli_stmt_execute($episodes_count_stmt);
        $episodes_count_result = mysqli_stmt_get_result($episodes_count_stmt);
        $episodes_count = mysqli_fetch_assoc($episodes_count_result)['count'];
        
        $seasons[] = [
            'season_number' => (int)$season_number,
            'episodes_count' => (int)$episodes_count
        ];
    }
    
    // Return response
    api_response([
        'success' => true,
        'data' => [
            'tvshow' => [
                'id' => (int)$tvshow['id'],
                'title' => $tvshow['title']
            ],
            'seasons' => $seasons
        ]
    ]);
}

// Handle get season episodes
function handle_season_episodes($tvshow_id, $season_number, $request) {
    global $conn;
    
    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }
    
    // Check if TV show exists
    $tvshow_query = "SELECT * FROM tvshows WHERE id = ?";
    $tvshow_stmt = mysqli_prepare($conn, $tvshow_query);
    mysqli_stmt_bind_param($tvshow_stmt, 'i', $tvshow_id);
    mysqli_stmt_execute($tvshow_stmt);
    $tvshow_result = mysqli_stmt_get_result($tvshow_stmt);
    
    if (mysqli_num_rows($tvshow_result) === 0) {
        api_error('TV show not found', 404);
    }
    
    $tvshow = mysqli_fetch_assoc($tvshow_result);
    
    // Get episodes for this season
    $episodes_query = "SELECT * FROM episodes WHERE tvshow_id = ? AND season_number = ? ORDER BY episode_number";
    $episodes_stmt = mysqli_prepare($conn, $episodes_query);
    mysqli_stmt_bind_param($episodes_stmt, 'ii', $tvshow_id, $season_number);
    mysqli_stmt_execute($episodes_stmt);
    $episodes_result = mysqli_stmt_get_result($episodes_stmt);
    
    if (mysqli_num_rows($episodes_result) === 0) {
        api_error('Season not found', 404);
    }
    
    $episodes = [];
    while ($episode = mysqli_fetch_assoc($episodes_result)) {
        $episode_id = $episode['id'];
        
        // Get episode links
        $links_query = "SELECT * FROM episode_links WHERE episode_id = ? ORDER BY quality, server_name";
        $links_stmt = mysqli_prepare($conn, $links_query);
        mysqli_stmt_bind_param($links_stmt, 'i', $episode_id);
        mysqli_stmt_execute($links_stmt);
        $links_result = mysqli_stmt_get_result($links_stmt);
        
        $links = [];
        while ($link = mysqli_fetch_assoc($links_result)) {
            $links[] = [
                'id' => (int)$link['id'],
                'link_type' => $link['link_type'],
                'quality' => $link['quality'],
                'server_name' => $link['server_name'],
                'is_premium' => (bool)$link['is_premium'],
                'link_url' => is_premium_content($link) ? null : $link['link_url']
            ];
        }
        
        $episodes[] = [
            'id' => (int)$episode['id'],
            'tvshow_id' => (int)$episode['tvshow_id'],
            'season_number' => (int)$episode['season_number'],
            'episode_number' => (int)$episode['episode_number'],
            'title' => $episode['title'],
            'description' => $episode['description'],
            'thumbnail' => format_image_url($episode['thumbnail']),
            'duration' => $episode['duration'],
            'release_date' => $episode['release_date'],
            'is_premium' => (bool)$episode['is_premium'],
            'links' => $links
        ];
    }
    
    // Return response
    api_response([
        'success' => true,
        'data' => [
            'tvshow' => [
                'id' => (int)$tvshow['id'],
                'title' => $tvshow['title']
            ],
            'season_number' => (int)$season_number,
            'episodes' => $episodes
        ]
    ]);
}

// Helper function to format TV show data
function format_tvshow_data($tvshow) {
    return [
        'id' => (int)$tvshow['id'],
        'title' => $tvshow['title'],
        'slug' => $tvshow['slug'] ?? slugify($tvshow['title']),
        'description' => $tvshow['description'],
        'poster' => format_image_url($tvshow['poster']),
        'backdrop' => format_image_url($tvshow['backdrop']),
        'trailer' => $tvshow['trailer'],
        'start_year' => (int)$tvshow['start_year'],
        'end_year' => $tvshow['end_year'] ? (int)$tvshow['end_year'] : null,
        'quality' => $tvshow['quality'],
        'rating' => (float)$tvshow['rating'],
        'category_id' => (int)$tvshow['category_id'],
        'category_name' => $tvshow['category_name'],
        'language' => $tvshow['language'] ?? 'Unknown',
        'views' => (int)$tvshow['views'],
        'featured' => (bool)$tvshow['featured'],
        'premium_only' => (bool)$tvshow['premium_only'],
        'created_at' => $tvshow['created_at']
    ];
}

// Helper function to format image URL
function format_image_url($image) {
    if (empty($image)) {
        return null;
    }
    
    if (strpos($image, 'http') === 0) {
        // External URL (TMDB)
        return $image;
    } elseif (file_exists('../uploads/' . $image)) {
        // Local file
        return SITE_URL . '/uploads/' . $image;
    } else {
        // Try TMDB path
        return 'https://image.tmdb.org/t/p/w500' . $image;
    }
}

// Helper function to check if content is premium and user has access
function is_premium_content($content) {
    if (!(bool)$content['is_premium']) {
        return false; // Not premium content
    }
    
    // Check if user is authenticated and has premium access
    $user = get_authenticated_user();
    if (!$user) {
        return true; // Premium content but user not authenticated
    }
    
    // Check if user has premium access
    $user_id = $user['user_id'];
    global $conn;
    
    $query = "SELECT is_premium, premium_expires FROM users WHERE id = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $user_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($result) === 0) {
        return true; // User not found
    }
    
    $user_data = mysqli_fetch_assoc($result);
    
    if (!(bool)$user_data['is_premium']) {
        return true; // User doesn't have premium access
    }
    
    // Check if premium has expired
    if ($user_data['premium_expires'] && strtotime($user_data['premium_expires']) < time()) {
        return true; // Premium has expired
    }
    
    return false; // User has premium access
}

// Helper function to create slug
function slugify($text) {
    // Replace non letter or digits by -
    $text = preg_replace('~[^\pL\d]+~u', '-', $text);
    
    // Transliterate
    $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);
    
    // Remove unwanted characters
    $text = preg_replace('~[^-\w]+~', '', $text);
    
    // Trim
    $text = trim($text, '-');
    
    // Remove duplicate -
    $text = preg_replace('~-+~', '-', $text);
    
    // Lowercase
    $text = strtolower($text);
    
    if (empty($text)) {
        return 'n-a';
    }
    
    return $text;
}
