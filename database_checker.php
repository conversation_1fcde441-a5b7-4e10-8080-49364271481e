<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: login.php');
    exit();
}

/**
 * Database Structure Checker
 * Checks if all required tables and columns exist for SEO functionality
 */

function checkDatabaseStructure() {
    global $conn;
    
    $results = [];
    
    // Check if movies table exists and has SEO columns
    $movies_check = mysqli_query($conn, "SHOW TABLES LIKE 'movies'");
    if (mysqli_num_rows($movies_check) > 0) {
        $results['movies_table'] = 'EXISTS';
        
        // Check for SEO columns in movies table
        $movies_columns = mysqli_query($conn, "SHOW COLUMNS FROM movies LIKE 'seo_%'");
        $seo_columns = [];
        while ($col = mysqli_fetch_assoc($movies_columns)) {
            $seo_columns[] = $col['Field'];
        }
        $results['movies_seo_columns'] = $seo_columns;
        
        // Check if we need to add SEO columns
        if (!in_array('seo_title', $seo_columns)) {
            $results['movies_needs_seo_title'] = true;
        }
        if (!in_array('seo_description', $seo_columns)) {
            $results['movies_needs_seo_description'] = true;
        }
        if (!in_array('seo_url', $seo_columns)) {
            $results['movies_needs_seo_url'] = true;
        }
    } else {
        $results['movies_table'] = 'MISSING';
    }
    
    // Check if tvshows table exists and has SEO columns
    $tvshows_check = mysqli_query($conn, "SHOW TABLES LIKE 'tvshows'");
    if (mysqli_num_rows($tvshows_check) > 0) {
        $results['tvshows_table'] = 'EXISTS';
        
        // Check for SEO columns in tvshows table
        $tvshows_columns = mysqli_query($conn, "SHOW COLUMNS FROM tvshows LIKE 'seo_%'");
        $seo_columns = [];
        while ($col = mysqli_fetch_assoc($tvshows_columns)) {
            $seo_columns[] = $col['Field'];
        }
        $results['tvshows_seo_columns'] = $seo_columns;
        
        // Check if we need to add SEO columns
        if (!in_array('seo_title', $seo_columns)) {
            $results['tvshows_needs_seo_title'] = true;
        }
        if (!in_array('seo_description', $seo_columns)) {
            $results['tvshows_needs_seo_description'] = true;
        }
        if (!in_array('seo_url', $seo_columns)) {
            $results['tvshows_needs_seo_url'] = true;
        }
    } else {
        $results['tvshows_table'] = 'MISSING';
    }
    
    return $results;
}

function addMissingSEOColumns() {
    global $conn;
    
    $messages = [];
    
    // Add SEO columns to movies table if missing
    $movies_columns = mysqli_query($conn, "SHOW COLUMNS FROM movies LIKE 'seo_%'");
    $existing_columns = [];
    while ($col = mysqli_fetch_assoc($movies_columns)) {
        $existing_columns[] = $col['Field'];
    }
    
    if (!in_array('seo_title', $existing_columns)) {
        $query = "ALTER TABLE movies ADD COLUMN seo_title VARCHAR(255) NULL";
        if (mysqli_query($conn, $query)) {
            $messages[] = "Added seo_title column to movies table";
        } else {
            $messages[] = "Failed to add seo_title column to movies table: " . mysqli_error($conn);
        }
    }
    
    if (!in_array('seo_description', $existing_columns)) {
        $query = "ALTER TABLE movies ADD COLUMN seo_description TEXT NULL";
        if (mysqli_query($conn, $query)) {
            $messages[] = "Added seo_description column to movies table";
        } else {
            $messages[] = "Failed to add seo_description column to movies table: " . mysqli_error($conn);
        }
    }
    
    if (!in_array('seo_url', $existing_columns)) {
        $query = "ALTER TABLE movies ADD COLUMN seo_url VARCHAR(255) NULL";
        if (mysqli_query($conn, $query)) {
            $messages[] = "Added seo_url column to movies table";
        } else {
            $messages[] = "Failed to add seo_url column to movies table: " . mysqli_error($conn);
        }
    }
    
    // Add SEO columns to tvshows table if missing
    $tvshows_columns = mysqli_query($conn, "SHOW COLUMNS FROM tvshows LIKE 'seo_%'");
    $existing_columns = [];
    while ($col = mysqli_fetch_assoc($tvshows_columns)) {
        $existing_columns[] = $col['Field'];
    }
    
    if (!in_array('seo_title', $existing_columns)) {
        $query = "ALTER TABLE tvshows ADD COLUMN seo_title VARCHAR(255) NULL";
        if (mysqli_query($conn, $query)) {
            $messages[] = "Added seo_title column to tvshows table";
        } else {
            $messages[] = "Failed to add seo_title column to tvshows table: " . mysqli_error($conn);
        }
    }
    
    if (!in_array('seo_description', $existing_columns)) {
        $query = "ALTER TABLE tvshows ADD COLUMN seo_description TEXT NULL";
        if (mysqli_query($conn, $query)) {
            $messages[] = "Added seo_description column to tvshows table";
        } else {
            $messages[] = "Failed to add seo_description column to tvshows table: " . mysqli_error($conn);
        }
    }
    
    if (!in_array('seo_url', $existing_columns)) {
        $query = "ALTER TABLE tvshows ADD COLUMN seo_url VARCHAR(255) NULL";
        if (mysqli_query($conn, $query)) {
            $messages[] = "Added seo_url column to tvshows table";
        } else {
            $messages[] = "Failed to add seo_url column to tvshows table: " . mysqli_error($conn);
        }
    }
    
    return $messages;
}

// Handle form submission
$message = '';
$error = '';

if ($_POST && isset($_POST['action'])) {
    if ($_POST['action'] == 'add_columns') {
        $results = addMissingSEOColumns();
        $message = implode('<br>', $results);
    }
}

$db_structure = checkDatabaseStructure();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Checker - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h2><i class="fas fa-database"></i> Database Structure Checker</h2>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                        <div class="alert alert-success"><?php echo $message; ?></div>
                        <?php endif; ?>
                        
                        <?php if ($error): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>
                        
                        <h4>Movies Table Status:</h4>
                        <p><strong>Table:</strong> 
                            <span class="badge bg-<?php echo $db_structure['movies_table'] == 'EXISTS' ? 'success' : 'danger'; ?>">
                                <?php echo $db_structure['movies_table']; ?>
                            </span>
                        </p>
                        
                        <?php if ($db_structure['movies_table'] == 'EXISTS'): ?>
                        <p><strong>SEO Columns:</strong> <?php echo implode(', ', $db_structure['movies_seo_columns']); ?></p>
                        
                        <?php if (isset($db_structure['movies_needs_seo_title']) || isset($db_structure['movies_needs_seo_description']) || isset($db_structure['movies_needs_seo_url'])): ?>
                        <div class="alert alert-warning">
                            <strong>Missing SEO columns in movies table:</strong>
                            <ul>
                                <?php if (isset($db_structure['movies_needs_seo_title'])): ?>
                                <li>seo_title</li>
                                <?php endif; ?>
                                <?php if (isset($db_structure['movies_needs_seo_description'])): ?>
                                <li>seo_description</li>
                                <?php endif; ?>
                                <?php if (isset($db_structure['movies_needs_seo_url'])): ?>
                                <li>seo_url</li>
                                <?php endif; ?>
                            </ul>
                        </div>
                        <?php endif; ?>
                        <?php endif; ?>
                        
                        <hr>
                        
                        <h4>TV Shows Table Status:</h4>
                        <p><strong>Table:</strong> 
                            <span class="badge bg-<?php echo $db_structure['tvshows_table'] == 'EXISTS' ? 'success' : 'danger'; ?>">
                                <?php echo $db_structure['tvshows_table']; ?>
                            </span>
                        </p>
                        
                        <?php if ($db_structure['tvshows_table'] == 'EXISTS'): ?>
                        <p><strong>SEO Columns:</strong> <?php echo implode(', ', $db_structure['tvshows_seo_columns']); ?></p>
                        
                        <?php if (isset($db_structure['tvshows_needs_seo_title']) || isset($db_structure['tvshows_needs_seo_description']) || isset($db_structure['tvshows_needs_seo_url'])): ?>
                        <div class="alert alert-warning">
                            <strong>Missing SEO columns in tvshows table:</strong>
                            <ul>
                                <?php if (isset($db_structure['tvshows_needs_seo_title'])): ?>
                                <li>seo_title</li>
                                <?php endif; ?>
                                <?php if (isset($db_structure['tvshows_needs_seo_description'])): ?>
                                <li>seo_description</li>
                                <?php endif; ?>
                                <?php if (isset($db_structure['tvshows_needs_seo_url'])): ?>
                                <li>seo_url</li>
                                <?php endif; ?>
                            </ul>
                        </div>
                        <?php endif; ?>
                        <?php endif; ?>
                        
                        <?php if (isset($db_structure['movies_needs_seo_title']) || isset($db_structure['movies_needs_seo_description']) || isset($db_structure['movies_needs_seo_url']) || isset($db_structure['tvshows_needs_seo_title']) || isset($db_structure['tvshows_needs_seo_description']) || isset($db_structure['tvshows_needs_seo_url'])): ?>
                        <hr>
                        <form method="post">
                            <input type="hidden" name="action" value="add_columns">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-plus"></i> Add Missing SEO Columns
                            </button>
                        </form>
                        <?php else: ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> All required SEO columns are present!
                        </div>
                        <?php endif; ?>
                        
                        <hr>
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="seo_checker.php" class="btn btn-primary">
                                <i class="fas fa-search"></i> Run SEO Checker
                            </a>
                            <a href="seo_monitor.php" class="btn btn-info">
                                <i class="fas fa-chart-line"></i> SEO Monitor
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
