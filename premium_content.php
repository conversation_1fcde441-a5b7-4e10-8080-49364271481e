<?php
require_once 'includes/header.php';

// Check if user is logged in
if (!isLoggedIn()) {
    $_SESSION['redirect_to'] = SITE_URL . '/premium_content.php';
    redirect(SITE_URL . '/login.php');
}

// Check if user is premium using isPremium() function
// This will update the user's premium status based on subscriptions
$is_premium = isPremium();

// Get current subscription details if premium
$subscription = null;
if ($is_premium) {
    $user_id = $_SESSION['user_id'];
    $subscription_query = "SELECT s.*, p.name as plan_name
                          FROM subscriptions s
                          JOIN premium_plans p ON s.plan_id = p.id
                          WHERE s.user_id = $user_id AND s.status = 'active' AND s.end_date > NOW()
                          ORDER BY s.end_date DESC LIMIT 1";
    $subscription_result = mysqli_query($conn, $subscription_query);

    if (mysqli_num_rows($subscription_result) > 0) {
        $subscription = mysqli_fetch_assoc($subscription_result);
    }
}

// If not premium, redirect to premium page
if (!$is_premium) {
    redirect(SITE_URL . '/premium.php');
}

// Get premium movies
$premium_movies_query = "SELECT m.*, c.name as category_name FROM movies m
                        LEFT JOIN categories c ON m.category_id = c.id
                        WHERE m.premium_only = 1
                        ORDER BY m.release_year DESC
                        LIMIT 12";
$premium_movies_result = mysqli_query($conn, $premium_movies_query);

// Get premium TV shows
$premium_tvshows_query = "SELECT t.*, c.name as category_name FROM tvshows t
                         LEFT JOIN categories c ON t.category_id = c.id
                         WHERE t.premium_only = 1
                         ORDER BY t.start_year DESC
                         LIMIT 12";
$premium_tvshows_result = mysqli_query($conn, $premium_tvshows_query);
?>

<!-- Premium Content Header -->
<section class="py-5 bg-dark">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 fw-bold">Premium Content</h1>
                <p class="lead text-muted">Exclusive movies and TV shows available only to premium subscribers</p>
                <div class="d-flex align-items-center mt-3">
                    <span class="badge bg-danger p-2 me-2">PREMIUM</span>
                    <span>
                        <?php if ($subscription): ?>
                            Your <?php echo $subscription['plan_name']; ?> plan is active until <?php echo date('F j, Y', strtotime($subscription['end_date'])); ?>
                        <?php else: ?>
                            Your premium account is active
                        <?php endif; ?>
                    </span>
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="<?php echo SITE_URL; ?>/premium.php" class="btn btn-outline-light">Manage Subscription</a>
            </div>
        </div>
    </div>
</section>

<!-- Premium Movies Section -->
<section class="py-5">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="section-title mb-0">Premium Movies</h2>
            <a href="<?php echo SITE_URL; ?>/movies.php" class="btn btn-outline-danger">View All Movies</a>
        </div>

        <?php if(mysqli_num_rows($premium_movies_result) > 0): ?>
        <div class="row">
            <?php while($movie = mysqli_fetch_assoc($premium_movies_result)): ?>
            <div class="col-6 col-md-4 col-lg-3 mb-4">
                <div class="movie-card" data-category="<?php echo $movie['category_id']; ?>">
                    <div class="premium-badge">
                        <span class="badge bg-danger">PREMIUM</span>
                    </div>
                    <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $movie['poster']; ?>" alt="<?php echo $movie['title']; ?>">
                    <div class="movie-card-overlay">
                        <h5 class="movie-card-title"><?php echo $movie['title']; ?></h5>
                        <div class="movie-card-info">
                            <span><?php echo $movie['release_year']; ?></span> •
                            <span><?php echo $movie['category_name']; ?></span>
                        </div>
                        <div class="movie-card-rating">
                            <i class="fas fa-star"></i> <?php echo number_format($movie['rating'], 1); ?>
                        </div>
                    </div>
                    <div class="movie-card-buttons">
                        <a href="<?php echo SITE_URL; ?>/details.php?type=movie&id=<?php echo $movie['id']; ?>" class="movie-card-btn" title="More Info">
                            <i class="fas fa-info-circle"></i>
                        </a>
                        <a href="#" class="movie-card-btn add-to-watchlist" data-id="<?php echo $movie['id']; ?>" data-type="movie" title="Add to Watchlist">
                            <i class="fas fa-plus"></i>
                        </a>
                        <?php if($movie['trailer_url']): ?>
                        <a href="#" class="movie-card-btn play-trailer" data-trailer="<?php echo $movie['trailer_url']; ?>" title="Play Trailer">
                            <i class="fas fa-play"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endwhile; ?>
        </div>
        <?php else: ?>
        <div class="alert alert-info">
            <p class="mb-0">No premium movies available at the moment. Please check back later.</p>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Premium TV Shows Section -->
<section class="py-5 bg-dark">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="section-title mb-0">Premium TV Shows</h2>
            <a href="<?php echo SITE_URL; ?>/tvshows.php" class="btn btn-outline-danger">View All TV Shows</a>
        </div>

        <?php if(mysqli_num_rows($premium_tvshows_result) > 0): ?>
        <div class="row">
            <?php while($tvshow = mysqli_fetch_assoc($premium_tvshows_result)): ?>
            <div class="col-6 col-md-4 col-lg-3 mb-4">
                <div class="movie-card" data-category="<?php echo $tvshow['category_id']; ?>">
                    <div class="premium-badge">
                        <span class="badge bg-danger">PREMIUM</span>
                    </div>
                    <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $tvshow['poster']; ?>" alt="<?php echo $tvshow['title']; ?>">
                    <div class="movie-card-overlay">
                        <h5 class="movie-card-title"><?php echo $tvshow['title']; ?></h5>
                        <div class="movie-card-info">
                            <span><?php echo $tvshow['start_year']; ?><?php echo $tvshow['end_year'] ? ' - ' . $tvshow['end_year'] : ' - Present'; ?></span> •
                            <span><?php echo $tvshow['category_name']; ?></span>
                        </div>
                        <div class="movie-card-rating">
                            <i class="fas fa-star"></i> <?php echo number_format($tvshow['rating'], 1); ?>
                        </div>
                    </div>
                    <div class="movie-card-buttons">
                        <a href="<?php echo SITE_URL; ?>/details.php?type=tvshow&id=<?php echo $tvshow['id']; ?>" class="movie-card-btn" title="More Info">
                            <i class="fas fa-info-circle"></i>
                        </a>
                        <a href="#" class="movie-card-btn add-to-watchlist" data-id="<?php echo $tvshow['id']; ?>" data-type="tvshow" title="Add to Watchlist">
                            <i class="fas fa-plus"></i>
                        </a>
                        <?php if($tvshow['trailer_url']): ?>
                        <a href="#" class="movie-card-btn play-trailer" data-trailer="<?php echo $tvshow['trailer_url']; ?>" title="Play Trailer">
                            <i class="fas fa-play"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endwhile; ?>
        </div>
        <?php else: ?>
        <div class="alert alert-info">
            <p class="mb-0">No premium TV shows available at the moment. Please check back later.</p>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Premium Benefits Section -->
<section class="py-5">
    <div class="container">
        <h2 class="text-center mb-5">Premium Benefits</h2>

        <div class="row text-center">
            <div class="col-md-4 mb-4">
                <div class="p-4">
                    <div class="mb-3">
                        <i class="fas fa-film fa-3x text-danger"></i>
                    </div>
                    <h3>Exclusive Content</h3>
                    <p class="text-muted">Access to premium movies and TV shows not available to regular users</p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="p-4">
                    <div class="mb-3">
                        <i class="fas fa-ban fa-3x text-danger"></i>
                    </div>
                    <h3>Ad-Free Experience</h3>
                    <p class="text-muted">Enjoy uninterrupted streaming without any advertisements</p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="p-4">
                    <div class="mb-3">
                        <i class="fas fa-download fa-3x text-danger"></i>
                    </div>
                    <h3>Download & Watch</h3>
                    <p class="text-muted">Download your favorite content and watch offline anytime</p>
                </div>
            </div>
        </div>
    </div>
</section>

<?php require_once 'includes/footer.php'; ?>
