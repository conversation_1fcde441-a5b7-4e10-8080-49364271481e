/* Movies Page Styles */

/* <PERSON> Header */
.movies-header {
    background: linear-gradient(to bottom, #141414, #000000);
    padding: 60px 0;
    position: relative;
    overflow: hidden;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.movies-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30"><path fill="%23e50914" fill-opacity="0.05" d="M15 0L18.75 11.25H30L20.625 18.75L24.375 30L15 22.5L5.625 30L9.375 18.75L0 11.25H11.25L15 0Z"/></svg>');
    z-index: 0;
}

.movies-header-content {
    position: relative;
    z-index: 1;
}

.movies-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 15px;
    color: #fff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    position: relative;
    display: inline-block;
}

.movies-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, #e50914, #ff5f6d);
    border-radius: 2px;
}

.movies-subtitle {
    font-size: 1.2rem;
    color: #adb5bd;
    margin-bottom: 30px;
    max-width: 600px;
}

.search-form {
    position: relative;
    max-width: 500px;
}

.search-form .form-control {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    padding: 12px 20px;
    border-radius: 30px;
    padding-right: 50px;
    transition: all 0.3s ease;
}

.search-form .form-control:focus {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: #e50914;
    box-shadow: 0 0 0 0.25rem rgba(229, 9, 20, 0.25);
}

.search-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.search-form .btn {
    position: absolute;
    right: 5px;
    top: 5px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #e50914, #ff5f6d);
    border: none;
    box-shadow: 0 4px 10px rgba(229, 9, 20, 0.3);
    transition: all 0.3s ease;
}

.search-form .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(229, 9, 20, 0.4);
}

/* Category Filter */
.category-filter {
    background-color: rgba(20, 20, 20, 0.7);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.filter-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #fff;
    display: flex;
    align-items: center;
}

.filter-title i {
    margin-right: 10px;
    color: #e50914;
}

.filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.filter-btn {
    padding: 8px 16px;
    border-radius: 30px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-btn:hover {
    background-color: rgba(229, 9, 20, 0.2);
    border-color: rgba(229, 9, 20, 0.3);
    color: #fff;
    transform: translateY(-2px);
}

.filter-btn.active {
    background: linear-gradient(135deg, #e50914, #ff5f6d);
    color: #fff;
    border-color: transparent;
    box-shadow: 0 4px 10px rgba(229, 9, 20, 0.3);
}

/* Movies Grid */
.movies-grid {
    margin-bottom: 50px;
}

.movie-card-link {
    display: block;
    text-decoration: none;
    color: #fff;
    height: 100%;
}

.movie-card-link:hover {
    text-decoration: none;
    color: #fff;
}

.movie-card {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    margin-bottom: 10px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    background-color: #141414;
    cursor: pointer;
    aspect-ratio: 2/3;
}

.movie-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

.movie-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.movie-card:hover img {
    transform: scale(1.05);
    filter: brightness(0.7);
}

.movie-card-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0) 100%);
    padding: 10px;
    transition: all 0.3s ease;
}

.movie-card:hover .movie-card-overlay {
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.7) 100%);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.movie-card-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: #fff;
    transition: transform 0.3s ease;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}

.movie-card:hover .movie-card-title {
    transform: translateY(-3px);
    -webkit-line-clamp: 2;
}

.movie-card-info {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-bottom: 5px;
    font-size: 0.7rem;
    color: #adb5bd;
    transition: transform 0.3s ease, opacity 0.3s ease;
    opacity: 0.8;
}

.movie-card:hover .movie-card-info {
    transform: translateY(-3px);
    opacity: 1;
}

.movie-card-rating {
    display: inline-block;
    background-color: rgba(0, 0, 0, 0.7);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.7rem;
    margin-bottom: 5px;
    transition: transform 0.3s ease;
}

.movie-card:hover .movie-card-rating {
    transform: translateY(-3px);
}

.movie-card-rating i {
    color: #ffc107;
    margin-right: 2px;
}

.movie-card-buttons {
    position: absolute;
    top: 5px;
    right: 5px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 10;
}

.movie-card:hover .movie-card-buttons {
    opacity: 1;
}

.movie-card-btn {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    font-size: 0.7rem;
}

.movie-card-btn:hover {
    background-color: #e50914;
    color: #fff;
    transform: scale(1.1);
}

/* Premium Badge */
.premium-badge {
    position: absolute;
    top: 5px;
    left: 5px;
    background: linear-gradient(135deg, #ff7b00, #ff0062);
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    font-size: 0.6rem;
    font-weight: 600;
    z-index: 2;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 1.5s infinite;
}

.premium-badge i {
    color: #ffc107;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Pagination */
.pagination-container {
    margin-top: 50px;
    margin-bottom: 30px;
}

.pagination {
    justify-content: center;
}

.pagination .page-item .page-link {
    background-color: rgba(20, 20, 20, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    padding: 10px 15px;
    margin: 0 5px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.pagination .page-item .page-link:hover {
    background-color: rgba(229, 9, 20, 0.7);
    border-color: #e50914;
    transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #e50914, #ff5f6d);
    border-color: #e50914;
    box-shadow: 0 4px 10px rgba(229, 9, 20, 0.3);
}

.pagination .page-item.disabled .page-link {
    background-color: rgba(20, 20, 20, 0.5);
    border-color: rgba(255, 255, 255, 0.05);
    color: #6c757d;
}

/* No Movies Message */
.no-movies-message {
    background-color: rgba(20, 20, 20, 0.7);
    border-radius: 10px;
    padding: 50px 30px;
    text-align: center;
    margin: 30px 0;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.no-movies-message h4 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #fff;
}

.no-movies-message p {
    font-size: 1.1rem;
    color: #adb5bd;
    max-width: 600px;
    margin: 0 auto;
}

.no-movies-message i {
    font-size: 3rem;
    color: #e50914;
    margin-bottom: 20px;
    display: block;
}

/* Responsive Styles */
@media (max-width: 1399.98px) {
    .movie-card-title {
        font-size: 0.85rem;
    }

    .movie-card-info {
        font-size: 0.65rem;
    }
}

@media (max-width: 1199.98px) {
    .movie-card-title {
        font-size: 0.8rem;
    }

    .movie-card-info {
        font-size: 0.6rem;
    }
}

@media (max-width: 991.98px) {
    .movies-title {
        font-size: 2.5rem;
    }

    .movie-card-title {
        font-size: 0.75rem;
    }

    .movie-card-overlay {
        padding: 8px;
    }

    .movie-card-info {
        gap: 3px;
    }
}

@media (max-width: 767.98px) {
    .movies-header {
        padding: 40px 0;
    }

    .movies-title {
        font-size: 2rem;
    }

    .movies-subtitle {
        font-size: 1rem;
    }

    .movie-card-title {
        font-size: 0.8rem;
        margin-bottom: 4px;
    }

    .movie-card-info {
        font-size: 0.65rem;
        margin-bottom: 4px;
    }

    .movie-card-rating {
        font-size: 0.65rem;
        padding: 2px 5px;
        margin-bottom: 4px;
    }

    .movie-card-btn {
        width: 22px;
        height: 22px;
        font-size: 0.7rem;
    }

    .premium-badge {
        width: 18px;
        height: 18px;
        font-size: 0.6rem;
    }
}

@media (max-width: 575.98px) {
    .movies-header {
        padding: 30px 0;
    }

    .movies-title {
        font-size: 1.8rem;
    }

    .search-form .form-control {
        padding: 10px 15px;
    }

    .search-form .btn {
        width: 35px;
        height: 35px;
    }

    .filter-btn {
        padding: 6px 10px;
        font-size: 0.75rem;
    }

    .movie-card-title {
        font-size: 0.85rem;
        -webkit-line-clamp: 1;
    }

    .movie-card-info {
        font-size: 0.65rem;
    }

    .movie-card-rating {
        font-size: 0.65rem;
        padding: 2px 4px;
    }

    .movie-card-btn {
        width: 20px;
        height: 20px;
        font-size: 0.65rem;
    }

    .premium-badge {
        width: 18px;
        height: 18px;
        font-size: 0.55rem;
        top: 4px;
        left: 4px;
    }

    .pagination .page-item .page-link {
        padding: 6px 10px;
        font-size: 0.8rem;
    }

    .movie-card-buttons {
        gap: 4px;
        top: 4px;
        right: 4px;
    }
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.movie-card {
    animation: fadeIn 0.5s ease forwards;
}

.movie-card:nth-child(2) {
    animation-delay: 0.1s;
}

.movie-card:nth-child(3) {
    animation-delay: 0.2s;
}

.movie-card:nth-child(4) {
    animation-delay: 0.3s;
}

.movie-card:nth-child(5) {
    animation-delay: 0.4s;
}

.movie-card:nth-child(6) {
    animation-delay: 0.5s;
}

.movie-card:nth-child(7) {
    animation-delay: 0.6s;
}

.movie-card:nth-child(8) {
    animation-delay: 0.7s;
}
