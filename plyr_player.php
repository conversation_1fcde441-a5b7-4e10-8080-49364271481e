<?php
// Include config file to get site URL and other settings
require_once 'includes/config.php';

// Get video URL from query parameter or use default
$video_url = isset($_GET['url']) ? $_GET['url'] : 'https://odd-darkness-074fsadsafsafasfjlknmmlkaytr9pe8afnhdklnfalskdftgy.bdmovieshub.workers.dev/994/RRR+2022+-+Hindi+-WEBDL+-+720p.mkv?hash=AgADjg';
$video_title = isset($_GET['title']) ? $_GET['title'] : 'RRR (2022) - Hindi - WEBDL - 720p';

// Check file extension to determine video type
$file_extension = strtolower(pathinfo(parse_url($video_url, PHP_URL_PATH), PATHINFO_EXTENSION));
$video_type = 'video/mp4'; // Default video type

// Set appropriate MIME type based on file extension
if ($file_extension === 'mkv') {
    $video_type = 'video/x-matroska';
} elseif ($file_extension === 'webm') {
    $video_type = 'video/webm';
} elseif ($file_extension === 'ogg' || $file_extension === 'ogv') {
    $video_type = 'video/ogg';
} elseif ($file_extension === 'mov') {
    $video_type = 'video/quicktime';
} elseif ($file_extension === 'avi') {
    $video_type = 'video/x-msvideo';
} elseif ($file_extension === 'flv') {
    $video_type = 'video/x-flv';
} elseif ($file_extension === '3gp') {
    $video_type = 'video/3gpp';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $video_title; ?> - Plyr Player</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Plyr.io CSS -->
    <link rel="stylesheet" href="https://cdn.plyr.io/3.7.8/plyr.css" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <style>
        body {
            background-color: #111;
            color: #fff;
            padding: 20px;
        }
        .player-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #000;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        .video-info {
            padding: 20px;
            background-color: #222;
            border-radius: 0 0 8px 8px;
        }
        h1 {
            font-size: 24px;
            margin-bottom: 20px;
        }
        .plyr {
            border-radius: 8px 8px 0 0;
            --plyr-color-main: #e50914;
        }
        .btn-back {
            margin-bottom: 20px;
        }
        .player-options {
            margin-top: 20px;
            padding: 15px;
            background-color: #333;
            border-radius: 8px;
        }
        .player-selector {
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <a href="<?php echo SITE_URL; ?>" class="btn btn-outline-light btn-back">
            <i class="fas fa-arrow-left"></i> Back to Home
        </a>

        <div class="player-container">
            <!-- Plyr.js Player -->
            <video id="player" playsinline controls crossorigin>
                <source src="<?php echo $video_url; ?>" type="<?php echo $video_type; ?>" />
            </video>

            <div class="video-info">
                <h1><?php echo $video_title; ?></h1>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <span class="badge bg-danger me-2">720p</span>
                        <span class="badge bg-secondary me-2">Hindi</span>
                        <span class="badge bg-info">WEBDL</span>
                    </div>
                    <div>
                        <button id="copy-url" class="btn btn-sm btn-outline-light" data-url="<?php echo SITE_URL; ?>/plyr_player.php?url=<?php echo urlencode($video_url); ?>&title=<?php echo urlencode($video_title); ?>">
                            <i class="fas fa-copy"></i> Copy Player URL
                        </button>
                    </div>
                </div>

                <div class="alert alert-info">
                    <strong>Note:</strong> If the video doesn't play, it might be due to CORS restrictions or the video format not being supported by the browser.
                </div>

                <div class="mt-4">
                    <h5>Keyboard Shortcuts:</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-play me-2"></i> Space - Play/Pause</li>
                                <li><i class="fas fa-volume-up me-2"></i> M - Mute/Unmute</li>
                                <li><i class="fas fa-arrows-alt me-2"></i> F - Fullscreen</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-forward me-2"></i> → - Forward 10s</li>
                                <li><i class="fas fa-backward me-2"></i> ← - Backward 10s</li>
                                <li><i class="fas fa-volume-up me-2"></i> ↑ - Volume Up</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-volume-down me-2"></i> ↓ - Volume Down</li>
                                <li><i class="fas fa-fast-forward me-2"></i> > - Increase Speed</li>
                                <li><i class="fas fa-fast-backward me-2"></i> < - Decrease Speed</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Plyr.js -->
    <script src="https://cdn.plyr.io/3.7.8/plyr.polyfilled.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Plyr
            const player = new Plyr('#player', {
                controls: [
                    'play-large', 'play', 'progress', 'current-time', 'mute',
                    'volume', 'captions', 'settings', 'pip', 'airplay', 'fullscreen'
                ],
                seekTime: 10,
                keyboard: { focused: true, global: true },
                tooltips: { controls: true, seek: true },
                captions: { active: true, language: 'auto', update: true }
            });

            // Copy URL button
            document.getElementById('copy-url').addEventListener('click', function() {
                const url = this.getAttribute('data-url');
                navigator.clipboard.writeText(url).then(() => {
                    this.innerHTML = '<i class="fas fa-check"></i> Copied!';
                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-copy"></i> Copy Player URL';
                    }, 2000);
                });
            });
        });
    </script>
</body>
</html>
