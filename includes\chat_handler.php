<?php
/**
 * <PERSON><PERSON>
 * Handles all chat-related AJAX requests
 */

require_once 'config.php';

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

// Check database connection
if (!isset($conn) || !$conn) {
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit();
}

// Get action
$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'send_message':
        sendMessage();
        break;
        
    case 'get_messages':
        getMessages();
        break;
        
    case 'mark_read':
        markMessagesAsRead();
        break;
        
    case 'get_unread_count':
        getUnreadCount();
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

function sendMessage() {
    global $conn;
    
    $session_id = $_POST['session_id'] ?? '';
    $message = trim($_POST['message'] ?? '');
    $message_type = $_POST['message_type'] ?? 'user';
    $user_id = $_SESSION['user_id'] ?? null;
    $admin_id = ($message_type === 'admin') ? ($_SESSION['user_id'] ?? null) : null;
    
    if (!$session_id || !$message) {
        echo json_encode(['success' => false, 'message' => 'Missing required fields']);
        return;
    }
    
    // Validate message type
    if (!in_array($message_type, ['user', 'admin'])) {
        echo json_encode(['success' => false, 'message' => 'Invalid message type']);
        return;
    }
    
    // Insert message
    $insert_query = "INSERT INTO user_messages (user_id, session_id, admin_id, message, message_type) 
                     VALUES (?, ?, ?, ?, ?)";
    
    $stmt = mysqli_prepare($conn, $insert_query);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, 'isiss', $user_id, $session_id, $admin_id, $message, $message_type);
        
        if (mysqli_stmt_execute($stmt)) {
            echo json_encode(['success' => true, 'message' => 'Message sent successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to send message']);
        }
        
        mysqli_stmt_close($stmt);
    } else {
        echo json_encode(['success' => false, 'message' => 'Database error']);
    }
}

function getMessages() {
    global $conn;
    
    $session_id = $_GET['session_id'] ?? '';
    
    if (!$session_id) {
        echo json_encode(['success' => false, 'message' => 'Session ID required']);
        return;
    }
    
    $query = "SELECT message, message_type, created_at 
              FROM user_messages 
              WHERE session_id = ? 
              ORDER BY created_at ASC 
              LIMIT 50";
    
    // Use simple query for PHP compatibility
    $session_id_safe = mysqli_real_escape_string($conn, $session_id);
    $query = "SELECT message, message_type, created_at FROM user_messages
              WHERE session_id = '$session_id_safe'
              ORDER BY created_at ASC
              LIMIT 50";

    $result = mysqli_query($conn, $query);
    if ($result) {
        $messages = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $messages[] = [
                'message' => htmlspecialchars($row['message']),
                'message_type' => $row['message_type'],
                'time' => date('H:i', strtotime($row['created_at']))
            ];
        }

        echo json_encode(['success' => true, 'messages' => $messages]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Database error']);
    }
}

function markMessagesAsRead() {
    global $conn;
    
    $session_id = $_POST['session_id'] ?? '';
    
    if (!$session_id) {
        echo json_encode(['success' => false, 'message' => 'Session ID required']);
        return;
    }
    
    $update_query = "UPDATE user_messages 
                     SET is_read = TRUE 
                     WHERE session_id = ? AND message_type = 'admin' AND is_read = FALSE";
    
    $stmt = mysqli_prepare($conn, $update_query);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, 's', $session_id);
        
        if (mysqli_stmt_execute($stmt)) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to mark messages as read']);
        }
        
        mysqli_stmt_close($stmt);
    } else {
        echo json_encode(['success' => false, 'message' => 'Database error']);
    }
}

function getUnreadCount() {
    global $conn;
    
    $session_id = $_GET['session_id'] ?? '';
    
    if (!$session_id) {
        echo json_encode(['success' => false, 'message' => 'Session ID required']);
        return;
    }
    
    $query = "SELECT COUNT(*) as count 
              FROM user_messages 
              WHERE session_id = ? AND message_type = 'admin' AND is_read = FALSE";
    
    // Use simple query for PHP compatibility
    $session_id_safe = mysqli_real_escape_string($conn, $session_id);
    $query = "SELECT COUNT(*) as count
              FROM user_messages
              WHERE session_id = '$session_id_safe' AND message_type = 'admin' AND is_read = FALSE";

    $result = mysqli_query($conn, $query);
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        echo json_encode(['success' => true, 'count' => (int)$row['count']]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Database error']);
    }
}
?>
