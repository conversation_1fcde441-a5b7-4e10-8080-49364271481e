# CinePix Admin Panel v2.0

একটি সম্পূর্ণ নতুন, আধুনিক এবং রেস্পন্সিভ এডমিন প্যানেল যা CinePix ওয়েবসাইটের জন্য তৈরি করা হয়েছে।

## 🎨 ডিজাইন ফিচার

- **আধুনিক ডার্ক থিম** - Netflix/YouTube স্টাইলের ডার্ক UI
- **সম্পূর্ণ রেস্পন্সিভ** - মোবাইল, ট্যাবলেট এবং ডেস্কটপের জন্য অপ্টিমাইজড
- **গ্লাস মরফিজম ইফেক্ট** - আধুনিক কার্ড ডিজাইন
- **স্মুথ অ্যানিমেশন** - হোভার এবং ট্রানজিশন ইফেক্ট
- **কাস্টম আইকন** - Font Awesome 6 এবং কাস্টম SVG আইকন

## 📱 লেআউট স্ট্রাকচার

- **কলাপসিবল সাইডবার** - মোবাইলে হ্যামবার্গার মেনু
- **টপ নেভিগেশন বার** - ইউজার প্রোফাইল, নোটিফিকেশন, গ্লোবাল সার্চ
- **মেইন কনটেন্ট এরিয়া** - ফ্লুইড লেআউট
- **ব্রেডক্রাম্ব নেভিগেশন** - সহজ নেভিগেশনের জন্য

## 🚀 প্রধান ফিচার

### ড্যাশবোর্ড
- রিয়েল-টাইম স্ট্যাটিস্টিক্স
- ইন্টারঅ্যাক্টিভ চার্ট (Chart.js)
- সাম্প্রতিক কার্যকলাপ
- দ্রুত অ্যাকশন বাটন

### কনটেন্ট ম্যানেজমেন্ট
- মুভি ম্যানেজমেন্ট (CRUD অপারেশন)
- টিভি শো ম্যানেজমেন্ট
- এপিসোড ম্যানেজমেন্ট
- ক্যাটাগরি ম্যানেজমেন্ট
- বাল্ক অ্যাকশন সাপোর্ট

### ইউজার ম্যানেজমেন্ট
- ইউজার তালিকা এবং ফিল্টারিং
- প্রিমিয়াম ইউজার ম্যানেজমেন্ট
- বাল্ক প্রিমিয়াম স্ট্যাটাস আপডেট

### পেমেন্ট সিস্টেম
- পেমেন্ট হিস্টরি
- প্রিমিয়াম প্ল্যান ম্যানেজমেন্ট
- পেমেন্ট স্ট্যাটাস ট্র্যাকিং

### অ্যানালিটিক্স
- রিয়েল-টাইম ডেটা
- লাইভ ইউজার ট্র্যাকিং
- রেভিনিউ চার্ট
- অ্যাক্টিভিটি লগ

## 🛠️ টেকনোলজি স্ট্যাক

### ফ্রন্টএন্ড
- **Bootstrap 5** - রেস্পন্সিভ ফ্রেমওয়ার্ক
- **Font Awesome 6** - আইকন লাইব্রেরি
- **Chart.js** - ইন্টারঅ্যাক্টিভ চার্ট
- **Custom CSS** - আধুনিক স্টাইলিং
- **Vanilla JavaScript** - ইন্টারঅ্যাক্টিভ ফিচার

### ব্যাকএন্ড
- **PHP 7.4+** - সার্ভার সাইড ল্যাঙ্গুয়েজ
- **MySQL** - ডেটাবেস
- **AJAX** - অ্যাসিনক্রোনাস ডেটা লোডিং

## 📁 ফাইল স্ট্রাকচার

```
admin/new_admin/
├── assets/
│   ├── css/
│   │   ├── admin-style.css      # মূল স্টাইল ফাইল
│   │   └── responsive.css       # রেস্পন্সিভ স্টাইল
│   └── js/
│       └── admin-script.js      # মূল JavaScript ফাইল
├── includes/
│   ├── header.php              # হেডার টেমপ্লেট
│   ├── sidebar.php             # সাইডবার টেমপ্লেট
│   └── footer.php              # ফুটার টেমপ্লেট
├── ajax/
│   ├── get_dashboard_data.php  # ড্যাশবোর্ড ডেটা API
│   ├── get_notifications.php   # নোটিফিকেশন API
│   └── global_search.php       # গ্লোবাল সার্চ API
├── index.php                   # ড্যাশবোর্ড পেজ
├── movies.php                  # মুভি ম্যানেজমেন্ট
└── README.md                   # এই ফাইল
```

## 🎯 ব্যবহারের নির্দেশনা

### ইনস্টলেশন
1. `admin/new_admin/` ফোল্ডারটি আপনার সার্ভারে আপলোড করুন
2. ডেটাবেস কনফিগারেশন চেক করুন (`../includes/config.php`)
3. ব্রাউজারে `admin/new_admin/index.php` এ যান

### লগইন
- ইউজারনেম: `admin`
- পাসওয়ার্ড: `admin123`

### প্রধান ফিচার ব্যবহার

#### ড্যাশবোর্ড
- রিয়েল-টাইম স্ট্যাটিস্টিক্স দেখুন
- চার্ট এবং গ্রাফ বিশ্লেষণ করুন
- দ্রুত অ্যাকশন বাটন ব্যবহার করুন

#### মুভি ম্যানেজমেন্ট
- নতুন মুভি যোগ করুন
- বিদ্যমান মুভি এডিট করুন
- বাল্ক অ্যাকশন ব্যবহার করুন (প্রিমিয়াম/ফ্রি/ডিলিট)
- ফিল্টার এবং সার্চ ব্যবহার করুন

#### গ্লোবাল সার্চ
- টপ নেভিগেশনে সার্চ বক্স ব্যবহার করুন
- মুভি, টিভি শো, ইউজার, ক্যাটাগরি সার্চ করুন
- রিয়েল-টাইম সার্চ রেজাল্ট পান

## 🔧 কাস্টমাইজেশন

### থিম কাস্টমাইজেশন
`assets/css/admin-style.css` ফাইলে CSS ভেরিয়েবল পরিবর্তন করুন:

```css
:root {
    --primary-color: #e50914;    /* প্রাইমারি কালার */
    --dark-bg: #0f0f0f;          /* ব্যাকগ্রাউন্ড কালার */
    --card-bg: #1a1a1a;          /* কার্ড ব্যাকগ্রাউন্ড */
    --text-primary: #ffffff;      /* প্রাইমারি টেক্সট */
}
```

### নতুন পেজ যোগ করা
1. নতুন PHP ফাইল তৈরি করুন
2. `includes/header.php` এবং `includes/footer.php` ইনক্লুড করুন
3. `includes/sidebar.php` এ নতুন মেনু আইটেম যোগ করুন

## 📱 রেস্পন্সিভ ব্রেকপয়েন্ট

- **Mobile Small**: 575px এবং নিচে
- **Mobile Large**: 576px - 767px
- **Tablet**: 768px - 991px
- **Desktop**: 992px - 1199px
- **Large Desktop**: 1200px এবং উপরে

## 🔒 নিরাপত্তা ফিচার

- সেশন-ভিত্তিক অথেন্টিকেশন
- CSRF প্রোটেকশন
- SQL ইনজেকশন প্রতিরোধ
- XSS প্রোটেকশন
- ফাইল আপলোড ভ্যালিডেশন

## 🚀 পারফরমেন্স অপ্টিমাইজেশন

- লেজি লোডিং
- AJAX-ভিত্তিক ডেটা লোডিং
- ইমেজ অপ্টিমাইজেশন
- CSS/JS মিনিফিকেশন
- ক্যাশিং সিস্টেম

## 🐛 ট্রাবলশুটিং

### সাধারণ সমস্যা
1. **পেজ লোড হচ্ছে না**: ডেটাবেস কনেকশন চেক করুন
2. **স্টাইল প্রয়োগ হচ্ছে না**: CSS ফাইলের পাথ চেক করুন
3. **AJAX কাজ করছে না**: JavaScript কনসোল চেক করুন

### লগ ফাইল
- PHP এরর লগ: `/var/log/php_errors.log`
- অ্যাপাচি এরর লগ: `/var/log/apache2/error.log`

## 📞 সাপোর্ট

কোন সমস্যা বা প্রশ্ন থাকলে যোগাযোগ করুন:
- ইমেইল: <EMAIL>
- টেলিগ্রাম: @buycinepix

## 📝 লাইসেন্স

এই প্রজেক্টটি CinePix টিমের জন্য বিশেষভাবে তৈরি করা হয়েছে।

---

**CinePix Admin Panel v2.0** - Built with ❤️ by CinePix Team
