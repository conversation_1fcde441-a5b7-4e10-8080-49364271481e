/* Global Styles */
:root {
    --primary-color: #e50914;
    --secondary-color: #141414;
    --dark-color: #000000;
    --light-color: #ffffff;
    --gray-color: #757575;
    --light-gray: #e5e5e5;
    --premium-color: #ffc107;
}

body {
    font-family: 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--dark-color);
    color: var(--light-color);
    padding-top: 56px; /* Reduced padding to match navbar height */
}

/* Navbar */
.navbar {
    background-color: rgba(0, 0, 0, 0.9);
    transition: background-color 0.3s ease;
    padding: 15px 0;
}

.navbar.scrolled {
    background-color: var(--dark-color);
}

.navbar-brand {
    font-size: 1.8rem;
    font-weight: 700;
}

.nav-link {
    color: var(--light-color) !important;
    font-weight: 500;
    margin: 0 10px;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--primary-color) !important;
}

.search-input {
    background-color: rgba(255, 255, 255, 0.2);
    border: none;
    color: var(--light-color);
}

/* Main Slider */
.main-slider {
    position: relative;
    height: 85vh;
    min-height: 650px;
    margin-top: -56px; /* Match body padding-top */
    overflow: hidden;
}

.slider-wrapper {
    height: 100%;
    position: relative;
    overflow: hidden;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease;
    z-index: 1;
}

.slide.active {
    opacity: 1;
    z-index: 2;
}

.slide-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    transform: scale(1);
    transition: transform 8s ease;
}

.slide.active .slide-image {
    transform: scale(1.1);
}

.slide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(0, 0, 0, 0.9) 20%, rgba(0, 0, 0, 0.4) 100%);
    display: flex;
    align-items: center;
}

.slide-content {
    opacity: 0;
    transform: translateY(50px);
    transition: all 1s ease 0.5s;
}

.slide.active .slide-content {
    opacity: 1;
    transform: translateY(0);
}

.premium-tag {
    display: inline-block;
    background: linear-gradient(135deg, #ff7b00, #ff0062);
    color: white;
    padding: 5px 15px;
    border-radius: 30px;
    font-size: 0.9rem;
    font-weight: bold;
    margin-bottom: 15px;
    box-shadow: 0 5px 15px rgba(255, 0, 98, 0.4);
    animation: pulse 1.5s infinite;
}

.premium-tag i {
    margin-right: 5px;
    color: var(--premium-color);
}

.content-type-badge {
    display: inline-block;
    margin-bottom: 15px;
}

.content-type-badge .badge {
    font-size: 0.9rem;
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 500;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.slide-content .premium-tag + .content-type-badge {
    margin-left: 10px;
    display: inline-block;
}

.slide-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    line-height: 1.2;
}

.slide-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    color: var(--light-gray);
}

.slide-meta span {
    display: flex;
    align-items: center;
}

.slide-meta i {
    margin-right: 5px;
    color: var(--primary-color);
}

.slide-description {
    font-size: 1.1rem;
    margin-bottom: 30px;
    max-width: 600px;
    line-height: 1.6;
}

.slide-buttons {
    display: flex;
    gap: 15px;
}

.slider-controls {
    position: absolute;
    bottom: 30px;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    z-index: 10;
}

.slider-prev,
.slider-next {
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.slider-prev:hover,
.slider-next:hover {
    background-color: var(--primary-color);
}

.slider-dots {
    display: flex;
    gap: 10px;
}

.slider-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.slider-dot.active {
    background-color: var(--primary-color);
    transform: scale(1.2);
}

/* Top 10 Section */
.top-10-carousel {
    display: flex;
    overflow-x: auto;
    padding: 20px 0;
    gap: 20px;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) var(--dark-color);
    position: relative;
    z-index: 1;
}

.top-10-carousel::-webkit-scrollbar {
    height: 8px;
}

.top-10-carousel::-webkit-scrollbar-track {
    background: var(--dark-color);
}

.top-10-carousel::-webkit-scrollbar-thumb {
    background-color: var(--primary-color);
    border-radius: 20px;
}

.top-10-item {
    position: relative;
    flex: 0 0 auto;
    width: 180px; /* Reduced width for mobile */
}

.top-10-rank {
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
    font-size: 6rem; /* Reduced font size */
    font-weight: 900;
    color: var(--light-color);
    text-shadow: -2px 0 var(--primary-color), 0 2px var(--primary-color), 2px 0 var(--primary-color), 0 -2px var(--primary-color);
    opacity: 0.8;
}

.top-10-item .movie-card {
    margin-left: 20px; /* Reduced margin */
    margin-bottom: 0;
    height: 250px; /* Fixed height */
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.profile-img-small {
    width: 30px;
    height: 30px;
    object-fit: cover;
}

/* Hero Section */
.hero {
    position: relative;
    height: 80vh;
    min-height: 500px;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    margin-top: -56px; /* Match body padding-top */
    padding-top: 56px; /* Match body padding-top */
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.5) 50%, rgba(0, 0, 0, 0.3) 100%);
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 650px;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.hero-meta {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
}

.hero-meta span {
    margin-right: 20px;
    display: flex;
    align-items: center;
}

.hero-meta i {
    margin-right: 5px;
    color: var(--primary-color);
}

.hero-rating {
    background-color: rgba(0, 0, 0, 0.7);
    padding: 5px 10px;
    border-radius: 5px;
    margin-right: 20px;
}

.hero-rating i {
    color: #ffc107;
    margin-right: 5px;
}

/* Section Titles */
.section-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background-color: var(--primary-color);
}

/* Movie Cards */
.movie-card {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    margin-bottom: 30px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.latest-section .movie-card {
    margin-bottom: 20px;
}

.movie-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.5);
}

.movie-card img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: filter 0.3s ease;
}

/* Make latest movie/series cards smaller */
.latest-section .movie-card img,
.premium-section .movie-card img {
    height: 250px;
}

.movie-card:hover img {
    filter: brightness(0.7);
}

.movie-card-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0) 100%);
    padding: 20px;
    transition: all 0.3s ease;
}

.movie-card:hover .movie-card-overlay {
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.7) 100%);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.movie-card-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
    transition: transform 0.3s ease;
}

.latest-section .movie-card-title {
    font-size: 1rem;
}

.movie-card:hover .movie-card-title {
    transform: translateY(-10px);
}

.movie-card-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 0.9rem;
    opacity: 0.8;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.latest-section .movie-card-info {
    font-size: 0.8rem;
    margin-bottom: 10px;
}

.movie-card:hover .movie-card-info {
    transform: translateY(-5px);
    opacity: 1;
}

.movie-card-rating {
    display: inline-block;
    background-color: rgba(0, 0, 0, 0.7);
    padding: 3px 8px;
    border-radius: 5px;
    font-size: 0.9rem;
    margin-bottom: 15px;
    transition: transform 0.3s ease;
}

.latest-section .movie-card-rating {
    font-size: 0.8rem;
    padding: 2px 6px;
    margin-bottom: 10px;
}

.movie-card:hover .movie-card-rating {
    transform: translateY(-5px);
}

.movie-card-rating i {
    color: var(--premium-color);
    margin-right: 3px;
}

.movie-card-buttons {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.movie-card:hover .movie-card-buttons {
    opacity: 1;
}

.movie-card .btn {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.latest-section .movie-card .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

.movie-card:hover .btn {
    opacity: 1;
    transform: translateY(0);
}

.movie-card-btn {
    background-color: rgba(0, 0, 0, 0.7);
    color: var(--light-color);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    transition: background-color 0.3s ease;
}

.movie-card-btn:hover {
    background-color: var(--primary-color);
    color: var(--light-color);
}

/* Carousel */
.owl-carousel .owl-nav button.owl-prev,
.owl-carousel .owl-nav button.owl-next {
    font-size: 2rem;
    color: var(--light-color);
    background-color: rgba(0, 0, 0, 0.5);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    transition: background-color 0.3s ease;
}

.owl-carousel .owl-nav button.owl-prev:hover,
.owl-carousel .owl-nav button.owl-next:hover {
    background-color: var(--primary-color);
}

.owl-carousel .owl-nav button.owl-prev {
    left: -25px;
}

.owl-carousel .owl-nav button.owl-next {
    right: -25px;
}

/* Details Page */
.details-banner {
    position: relative;
    height: 70vh;
    min-height: 500px;
    background-size: cover;
    background-position: center;
    margin-top: -70px;
    padding-top: 70px;
}

.details-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.9) 100%);
}

.details-content {
    position: relative;
    z-index: 1;
    padding: 50px 0;
}

.details-poster {
    width: 100%;
    border-radius: 5px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

.details-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.details-meta {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 1.5rem;
}

.details-meta span {
    margin-right: 20px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.details-meta i {
    margin-right: 5px;
    color: var(--primary-color);
}

.details-rating {
    display: inline-block;
    background-color: rgba(0, 0, 0, 0.7);
    padding: 5px 10px;
    border-radius: 5px;
    margin-right: 20px;
    margin-bottom: 10px;
}

.details-rating i {
    color: #ffc107;
    margin-right: 5px;
}

.details-actions {
    display: flex;
    margin-bottom: 2rem;
}

.details-actions .btn {
    margin-right: 15px;
    display: flex;
    align-items: center;
}

.details-actions .btn i {
    margin-right: 8px;
}

.details-description {
    margin-bottom: 2rem;
    line-height: 1.7;
}

.trailer-container {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
    border-radius: 5px;
    margin-bottom: 2rem;
}

.trailer-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
}

/* Reviews */
.review-card {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 20px;
}

.review-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.review-user-img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 15px;
}

.review-user-name {
    font-weight: 600;
    margin-bottom: 0;
}

.review-date {
    font-size: 0.9rem;
    color: var(--gray-color);
}

.review-rating {
    margin-bottom: 10px;
}

.review-rating i {
    color: #ffc107;
    margin-right: 2px;
}

.review-text {
    line-height: 1.6;
}

/* Forms */
.form-container {
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 5px;
    padding: 30px;
    margin-top: 30px;
}

.form-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-align: center;
}

.form-control {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--light-color);
}

.form-control:focus {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    color: var(--light-color);
    box-shadow: none;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-text {
    color: var(--gray-color);
}

/* Profile */
.profile-header {
    background-color: rgba(0, 0, 0, 0.5);
    padding: 30px;
    border-radius: 5px;
    margin-bottom: 30px;
}

.profile-img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 20px;
}

.profile-name {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.profile-email {
    color: var(--gray-color);
    margin-bottom: 20px;
}

.profile-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
}

.profile-stat {
    text-align: center;
}

.profile-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.profile-stat-label {
    color: var(--gray-color);
}

/* Footer */
.footer {
    background-color: rgba(0, 0, 0, 0.9);
}

.social-icons a {
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.social-icons a:hover {
    color: var(--primary-color) !important;
}

/* Pagination */
.pagination .page-item .page-link {
    background-color: rgba(0, 0, 0, 0.7);
    border-color: rgba(255, 255, 255, 0.1);
    color: var(--light-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.pagination .page-item .page-link:hover {
    background-color: rgba(229, 9, 20, 0.7);
    border-color: var(--primary-color);
}

/* Premium Badge */
.premium-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 2;
}

/* Payment Methods */
.payment-method-card {
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    padding: 15px;
    text-align: center;
    height: 100%;
    transition: all 0.3s ease;
}

.payment-method-card:hover {
    border-color: var(--primary-color);
}

.payment-method-card input[type="radio"] {
    position: absolute;
    opacity: 0;
}

.payment-method-card input[type="radio"]:checked + .payment-label {
    color: var(--primary-color);
}

.payment-method-card input[type="radio"]:checked + .payment-label::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px solid var(--primary-color);
    border-radius: 5px;
    pointer-events: none;
}

.payment-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    width: 100%;
    height: 100%;
    position: relative;
}

.payment-logo {
    max-width: 60px;
    max-height: 60px;
    margin-bottom: 10px;
}

/* Card Price */
.card-price {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0;
}

.period {
    font-size: 0.9rem;
    color: var(--gray-color);
}

/* Accordion Custom Styles */
.accordion-button:not(.collapsed) {
    background-color: rgba(229, 9, 20, 0.1);
    color: var(--primary-color);
}

.accordion-button:focus {
    box-shadow: none;
    border-color: rgba(229, 9, 20, 0.1);
}

/* Responsive */
@media (max-width: 991.98px) {
    .hero {
        height: 70vh;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .details-banner {
        height: auto;
        padding-bottom: 50px;
    }

    .details-poster {
        margin-bottom: 30px;
    }

    .details-title {
        font-size: 2rem;
    }
}

@media (max-width: 767.98px) {
    .hero {
        height: 60vh;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .movie-card img {
        height: 300px;
    }

    .owl-carousel .owl-nav button.owl-prev,
    .owl-carousel .owl-nav button.owl-next {
        width: 40px;
        height: 40px;
        font-size: 1.5rem;
    }

    .profile-img {
        width: 120px;
        height: 120px;
    }

    .profile-name {
        font-size: 1.8rem;
    }
}

@media (max-width: 575.98px) {
    .hero {
        height: 50vh;
    }

    .hero-title {
        font-size: 1.8rem;
    }

    .hero-meta {
        flex-wrap: wrap;
    }

    .hero-meta span {
        margin-bottom: 10px;
    }

    .movie-card img {
        height: 250px;
    }

    .details-title {
        font-size: 1.8rem;
    }

    .details-actions {
        flex-wrap: wrap;
    }

    .details-actions .btn {
        margin-bottom: 10px;
    }

    .profile-header {
        text-align: center;
    }

    .profile-stats {
        flex-wrap: wrap;
    }

    .profile-stat {
        width: 50%;
        margin-bottom: 20px;
    }
}

/* Enhanced Movie Details Page Styles */
/* Movie Details Section */
.movie-details-section {
    position: relative;
    padding: 50px 0 30px;
    color: var(--light-color);
}

.movie-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    z-index: -1;
}

.backdrop-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.9) 100%);
}

.movie-details-container {
    background-color: rgba(20, 20, 20, 0.8);
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    margin-bottom: 30px;
}

.movie-poster-container {
    position: relative;
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
}

.movie-poster {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 8px;
}

.premium-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(135deg, #ff7b00, #ff0062);
    color: white;
    padding: 5px 10px;
    border-radius: 30px;
    font-size: 0.8rem;
    font-weight: bold;
    box-shadow: 0 5px 15px rgba(255, 0, 98, 0.4);
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.premium-badge i {
    font-size: 1rem;
    color: var(--premium-color);
}

.premium-section {
    background: linear-gradient(to bottom, #141414, #000000);
    position: relative;
    overflow: hidden;
}

.premium-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><path fill="%23ffc107" fill-opacity="0.05" d="M10 0L12.5 7.5H20L13.75 12.5L16.25 20L10 15L3.75 20L6.25 12.5L0 7.5H7.5L10 0Z"/></svg>');
    z-index: 0;
}

.premium-header {
    position: relative;
    z-index: 1;
}

.premium-card {
    border: 2px solid transparent;
    background-clip: padding-box;
    position: relative;
    z-index: 1;
}

.premium-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #ff7b00, #ff0062, #e100ff);
    z-index: -1;
    border-radius: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.premium-card:hover::before {
    opacity: 1;
}

.movie-info {
    padding: 0 15px;
}

.movie-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    color: var(--light-color);
}

.movie-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    color: var(--light-gray);
}

.movie-meta span {
    display: flex;
    align-items: center;
}

.movie-meta i {
    margin-right: 5px;
    color: var(--primary-color);
}

.movie-rating i {
    color: #ffc107;
}

.movie-description {
    margin-top: 20px;
}

.movie-description h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--light-color);
}

.movie-description p {
    line-height: 1.6;
    color: var(--light-gray);
}

.movie-action-buttons {
    display: flex;
    gap: 15px;
    margin-top: 25px;
}

.movie-action-buttons .btn {
    padding: 12px 25px;
    font-weight: 600;
    font-size: 1.1rem;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.movie-action-buttons .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
}

.movie-action-buttons .btn-watch {
    background-color: var(--primary-color);
    color: var(--light-color);
    border: none;
}

.movie-action-buttons .btn-watch:hover {
    background-color: #c00812;
    color: var(--light-color);
}

.movie-action-buttons .btn-download {
    background-color: #007bff;
    color: var(--light-color);
    border: none;
}

.movie-action-buttons .btn-download:hover {
    background-color: #0069d9;
    color: var(--light-color);
}

/* Content Tabs Section */
.content-tabs-section {
    padding: 20px 0 50px;
}

.custom-tabs {
    background-color: rgba(20, 20, 20, 0.8);
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.custom-tab-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 15px;
}

.tab-button {
    background-color: transparent;
    border: none;
    color: var(--light-gray);
    padding: 10px 20px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 5px;
}

.tab-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--light-color);
}

.tab-button.active {
    background-color: var(--primary-color);
    color: var(--light-color);
}

.tab-content-pane {
    display: none;
}

.tab-content-pane.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Stream Tab */
.stream-container {
    margin-bottom: 30px;
}

.stream-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.stream-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0;
}

.btn-watch {
    background-color: var(--primary-color);
    color: var(--light-color);
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-watch:hover {
    background-color: #c00812;
    color: var(--light-color);
    transform: translateY(-2px);
}

.player-wrapper {
    position: relative;
    padding-top: 56.25%; /* 16:9 Aspect Ratio */
    height: 0;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    margin-bottom: 30px;
}

.player-wrapper iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
}

.streaming-links {
    margin-top: 20px;
}

.table-dark {
    background-color: rgba(30, 30, 30, 0.8);
    color: var(--light-gray);
    border-radius: 8px;
    overflow: hidden;
}

.table-dark th {
    background-color: rgba(0, 0, 0, 0.5);
    color: var(--light-color);
    font-weight: 600;
    border-color: rgba(255, 255, 255, 0.1);
}

.table-dark td {
    border-color: rgba(255, 255, 255, 0.1);
    vertical-align: middle;
}

.btn-stream {
    background-color: #28a745;
    color: white;
    transition: all 0.3s ease;
}

.btn-stream:hover {
    background-color: #218838;
    color: white;
    transform: translateY(-2px);
}

.btn-premium {
    background-color: var(--primary-color);
    color: white;
    transition: all 0.3s ease;
}

.btn-premium:hover {
    background-color: #c00812;
    color: white;
    transform: translateY(-2px);
}

/* Download Tab */
.download-container {
    margin-bottom: 30px;
}

.download-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.download-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0;
}

.btn-download {
    background-color: #007bff;
    color: var(--light-color);
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-download:hover {
    background-color: #0069d9;
    color: var(--light-color);
    transform: translateY(-2px);
}

.download-card {
    display: flex;
    align-items: center;
    background-color: rgba(30, 30, 30, 0.8);
    border-radius: 8px;
    padding: 15px;
    color: var(--light-color);
    text-decoration: none;
    transition: all 0.3s ease;
    height: 100%;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.download-card:hover {
    background-color: rgba(40, 40, 40, 0.8);
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    color: var(--light-color);
    text-decoration: none;
}

.download-card-icon {
    width: 40px;
    height: 40px;
    background-color: #007bff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.download-card-icon i {
    color: white;
    font-size: 1.2rem;
}

.download-card-info {
    flex-grow: 1;
}

.download-quality {
    display: block;
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.download-type {
    display: block;
    color: var(--light-gray);
    font-size: 0.9rem;
}

.download-card-action {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--light-gray);
    transition: all 0.3s ease;
}

.download-card:hover .download-card-action {
    color: var(--light-color);
}

.premium-download .download-card-icon {
    background-color: var(--primary-color);
}

.download-card-premium {
    background-color: var(--primary-color);
    color: white;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-left: 10px;
}

/* Download Modal */
.modal-content {
    background-color: var(--secondary-color);
    color: var(--light-color);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-close {
    color: var(--light-color);
    opacity: 0.8;
}

.download-options {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.download-quality-group {
    background-color: rgba(30, 30, 30, 0.8);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.download-quality-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.download-links-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.download-link {
    display: flex;
    align-items: center;
    background-color: rgba(40, 40, 40, 0.8);
    border-radius: 5px;
    padding: 10px 15px;
    color: var(--light-color);
    text-decoration: none;
    transition: all 0.3s ease;
    flex-grow: 1;
    min-width: 200px;
}

.download-link:hover {
    background-color: rgba(50, 50, 50, 0.8);
    transform: translateY(-2px);
    color: var(--light-color);
    text-decoration: none;
}

.download-link-type {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-right: 15px;
    font-weight: 600;
}

.download-link-info {
    flex-grow: 1;
    color: var(--light-gray);
    font-size: 0.9rem;
}

.download-link-action {
    color: var(--light-gray);
    transition: all 0.3s ease;
}

.download-link:hover .download-link-action {
    color: var(--light-color);
}

.premium-link {
    background-color: rgba(229, 9, 20, 0.2);
    border: 1px solid rgba(229, 9, 20, 0.5);
}

.premium-link:hover {
    background-color: rgba(229, 9, 20, 0.3);
}

.premium-link .download-link-type i {
    color: var(--primary-color);
}

/* Reviews Tab */
.review-form-container {
    background-color: rgba(30, 30, 30, 0.8);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.review-form-container h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.reviews-container {
    margin-top: 30px;
}

.reviews-container h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.review-card {
    background-color: rgba(30, 30, 30, 0.8);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.review-user {
    display: flex;
    align-items: center;
    gap: 10px;
}

.review-user-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.review-username {
    font-weight: 600;
}

.review-rating {
    display: flex;
    align-items: center;
    gap: 10px;
}

.review-body {
    color: var(--light-gray);
}

.review-date {
    margin-top: 10px;
    color: var(--gray-color);
}

.no-content-message,
.no-reviews-message,
.no-downloads-message {
    background-color: rgba(30, 30, 30, 0.8);
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    color: var(--light-gray);
}

/* Floating Chat Icon */
.floating-chat-icon {
    position: fixed;
    bottom: 30px;
    right: 30px !important;
    left: auto !important;
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #e53935, #9c27b0);
    color: var(--light-color);
    border-radius: 50%;
    display: flex !important;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
    cursor: pointer;
    z-index: 9999;
    transition: all 0.3s ease;
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}

.chat-pulse {
    position: absolute;
    top: 0;
    right: 0;
    min-width: 20px;
    height: 20px;
    background-color: #4CAF50;
    border-radius: 50%;
    border: 2px solid #fff;
    animation: pulse 2s infinite;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    color: white;
    padding: 0 4px;
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
    }
    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
    }
    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
    }
}

.floating-chat-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
}

.floating-chat-icon.has-unread {
    animation: shake 1s cubic-bezier(.36,.07,.19,.97) both;
    animation-iteration-count: 2;
}

@keyframes shake {
    10%, 90% {
        transform: translate3d(-1px, 0, 0);
    }
    20%, 80% {
        transform: translate3d(2px, 0, 0);
    }
    30%, 50%, 70% {
        transform: translate3d(-3px, 0, 0);
    }
    40%, 60% {
        transform: translate3d(3px, 0, 0);
    }
}

.floating-chat-icon.has-unread::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 15px;
    height: 15px;
    background-color: #28a745;
    border-radius: 50%;
    border: 2px solid var(--dark-color);
}

/* Chat Container */
.chat-container {
    display: none;
    position: fixed;
    bottom: 100px;
    right: 30px !important;
    left: auto !important;
    width: 350px;
    height: 500px;
    background-color: var(--secondary-color);
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    z-index: 1001;
    overflow: hidden;
    transition: all 0.3s ease;
    flex-direction: column;
}

.chat-header {
    background-color: var(--primary-color);
    color: var(--light-color);
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.chat-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

.chat-close {
    background: none;
    border: none;
    color: var(--light-color);
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chat-close:hover {
    transform: scale(1.1);
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    background-color: rgba(20, 20, 20, 0.8);
}

.chat-input-container {
    padding: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    gap: 10px;
    flex-shrink: 0;
}

.chat-input {
    flex: 1;
    padding: 10px 15px;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    color: var(--light-color);
    transition: all 0.3s ease;
    resize: none;
    height: 40px;
    line-height: 20px;
}

.chat-input:focus {
    outline: none;
    border-color: var(--primary-color);
    background-color: rgba(255, 255, 255, 0.15);
}

.chat-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.chat-send-btn {
    background-color: var(--primary-color);
    color: var(--light-color);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.chat-send-btn:hover {
    background-color: #c00812;
    transform: scale(1.1);
}

.chat-message {
    padding: 10px 15px;
    border-radius: 15px;
    max-width: 80%;
    word-break: break-word;
    position: relative;
}

.chat-message.user {
    align-self: flex-end;
    background-color: var(--primary-color);
    color: var(--light-color);
    border-bottom-right-radius: 5px;
}

.chat-message.admin {
    align-self: flex-start;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--light-color);
    border-bottom-left-radius: 5px;
}

.chat-message-time {
    font-size: 0.7rem;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 5px;
    text-align: right;
}

.chat-message-status {
    font-size: 0.7rem;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 2px;
    text-align: right;
}

.chat-message-status.read {
    color: #28a745;
}

.chat-typing {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
    padding: 5px 10px;
    align-self: flex-start;
}

.chat-welcome {
    text-align: center;
    padding: 20px;
    color: rgba(255, 255, 255, 0.6);
}

.chat-welcome h4 {
    margin-bottom: 10px;
    color: var(--light-color);
}

.chat-welcome p {
    margin-bottom: 0;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Message List Page */
.message-list-container {
    background-color: rgba(20, 20, 20, 0.8);
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    margin-bottom: 30px;
}

.message-card {
    background-color: rgba(30, 30, 30, 0.8);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    border-left: 3px solid var(--primary-color);
}

.message-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.message-card.unread {
    border-left: 3px solid #28a745;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.message-subject {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

.message-date {
    color: var(--gray-color);
    font-size: 0.9rem;
}

.message-content {
    margin-bottom: 15px;
    line-height: 1.6;
}

.message-reply {
    background-color: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 5px;
    margin-top: 15px;
    border-left: 3px solid #28a745;
}

.message-reply-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-weight: 600;
    color: #28a745;
}

.message-reply-content {
    line-height: 1.6;
}

.no-messages {
    text-align: center;
    padding: 30px;
    background-color: rgba(30, 30, 30, 0.8);
    border-radius: 8px;
    color: var(--gray-color);
}

/* Responsive Styles */
@media (max-width: 991.98px) {
    .movie-title {
        font-size: 2rem;
    }

    .movie-meta {
        gap: 10px;
    }

    .custom-tab-buttons {
        flex-wrap: wrap;
    }

    .message-modal {
        width: 320px;
    }
}

@media (max-width: 767.98px) {
    .movie-details-container {
        padding: 20px;
    }

    .movie-title {
        font-size: 1.8rem;
    }

    .movie-meta {
        flex-direction: column;
        gap: 5px;
        align-items: flex-start;
    }

    .stream-header,
    .download-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    /* Top 10 mobile optimization */
    .top-10-item {
        width: 150px;
    }

    .top-10-rank {
        font-size: 5rem;
        left: -5px;
    }

    .top-10-item .movie-card {
        margin-left: 15px;
        height: 220px;
    }

    /* Hero section mobile optimization */
    .hero {
        height: 70vh;
        min-height: 400px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    /* Card optimization for tablets */
    .movie-card img {
        height: 250px;
    }

    .latest-section .movie-card img,
    .premium-section .movie-card img {
        height: 220px;
    }

    .custom-tabs {
        padding: 20px;
    }

    .download-link {
        min-width: 100%;
    }

    .movie-action-buttons {
        flex-direction: column;
        width: 100%;
    }

    .movie-action-buttons .btn {
        width: 100%;
        justify-content: center;
        margin-bottom: 10px;
    }
}

@media (max-width: 575.98px) {
    .movie-details-container {
        padding: 15px;
    }

    .movie-title {
        font-size: 1.5rem;
    }

    .custom-tabs {
        padding: 15px;
    }

    .tab-button {
        padding: 8px 15px;
        font-size: 0.9rem;
    }

    .download-card {
        padding: 10px;
    }

    .download-card-icon {
        width: 35px;
        height: 35px;
        margin-right: 10px;
    }

    .download-quality {
        font-size: 1rem;
    }

    .download-type {
        font-size: 0.8rem;
    }

    /* Top 10 small mobile optimization */
    .top-10-item {
        width: 130px;
    }

    .top-10-rank {
        font-size: 4rem;
        left: -3px;
    }

    .top-10-item .movie-card {
        margin-left: 10px;
        height: 200px;
    }

    /* Hero section small mobile optimization */
    .hero {
        height: 60vh;
        min-height: 350px;
    }

    .hero-title {
        font-size: 1.8rem;
        margin-bottom: 0.5rem;
    }

    .hero-description {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
    }

    .hero-meta {
        margin-bottom: 1.5rem;
    }

    .hero-buttons .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    /* Card optimization for mobile */
    .movie-card img {
        height: 220px;
    }

    .latest-section .movie-card img,
    .premium-section .movie-card img {
        height: 180px;
    }

    .movie-card-title {
        font-size: 1rem;
    }

    .movie-card-info {
        font-size: 0.8rem;
    }

    .movie-card-rating {
        font-size: 0.8rem;
    }

    .movie-card .btn-sm {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }
}

.banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.9) 100%);
    backdrop-filter: blur(5px);
}

.details-content {
    position: relative;
    z-index: 1;
    padding: 50px 0;
}

.poster-container {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.7);
    transition: transform 0.3s ease;
}

.poster-container:hover {
    transform: scale(1.03);
}

.details-poster {
    width: 100%;
    max-width: 250px;
    border-radius: 10px;
    margin: 0 auto;
    display: block;
}

.premium-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: var(--primary-color);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.8rem;
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.details-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.details-meta {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 1rem;
}

.details-meta span {
    margin-right: 20px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    font-size: 1rem;
}

.details-meta i {
    margin-right: 8px;
    color: var(--primary-color);
}

.details-rating {
    display: inline-block;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 5px 12px;
    border-radius: 5px;
    margin-right: 20px;
    margin-bottom: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.details-rating i {
    color: #ffc107;
    margin-right: 5px;
}

.genre-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.genre-tag {
    background-color: rgba(255, 255, 255, 0.1);
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
}

.genre-tag:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.details-description {
    line-height: 1.7;
    color: #ddd;
    background-color: rgba(0, 0, 0, 0.3);
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
}

.details-description h4 {
    margin-bottom: 10px;
    font-weight: 600;
    color: var(--light-color);
}

.details-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.details-actions .btn {
    padding: 8px 16px;
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.details-actions .btn i {
    font-size: 1.1rem;
}

.details-actions .btn-danger {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.details-actions .btn-danger:hover {
    background-color: #c00812;
    border-color: #c00812;
    transform: translateY(-2px);
}

.details-actions .btn-outline-light:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* Custom Tabs */
.custom-tab-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.tab-button {
    background-color: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 5px;
    color: var(--light-color);
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.tab-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.tab-button.active {
    background-color: var(--primary-color);
    color: white;
}

.tab-content-pane {
    display: none;
}

.tab-content-pane.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.watch-btn {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 500;
    padding: 8px 15px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.watch-btn:hover {
    background-color: #c00812;
    border-color: #c00812;
    transform: translateY(-2px);
}


/* Reviews Section */
.review-form-container {
    background-color: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.rating-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.reviews-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.review-card {
    background-color: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: transform 0.3s ease;
}

.review-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.review-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.review-user-img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 15px;
    border: 2px solid var(--primary-color);
}

.review-user-name {
    margin-bottom: 0;
    font-weight: 600;
}

.review-date {
    margin-bottom: 0;
    font-size: 0.8rem;
    color: var(--gray-color);
}

.review-rating {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.stars-container {
    display: flex;
    margin-right: 10px;
}

.stars-container i {
    color: #ffc107;
    margin-right: 2px;
}

.rating-number {
    font-weight: 600;
    color: #ffc107;
}

.review-text {
    line-height: 1.6;
    margin-bottom: 0;
}

/* Download Button Styles */
.download-button-container {
    margin-bottom: 10px;
}

.download-button {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, #c00812 100%);
    color: white;
    border-radius: 8px;
    padding: 0;
    overflow: hidden;
    text-decoration: none;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    max-width: 200px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.download-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
    color: white;
    text-decoration: none;
    background: linear-gradient(135deg, #c00812 0%, var(--primary-color) 100%);
}

.download-icon {
    background-color: rgba(0, 0, 0, 0.3);
    padding: 12px 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.download-button:hover .download-icon {
    background-color: rgba(0, 0, 0, 0.4);
}

.download-icon i {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.download-button:hover .download-icon i {
    transform: translateY(-2px);
}

.download-info {
    padding: 8px 15px;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
}

.download-button:hover .download-info {
    padding-left: 18px;
}

.download-text {
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.download-meta {
    font-size: 0.75rem;
    opacity: 0.8;
    margin-top: 2px;
}

/* Player Wrapper */
.player-wrapper {
    position: relative;
    padding-top: 56.25%; /* 16:9 Aspect Ratio */
    height: 0;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Responsive Styles */
@media (max-width: 1199.98px) {
    .slide-title {
        font-size: 3rem;
    }

    .top-10-rank {
        font-size: 7rem;
    }

    .main-slider {
        height: 80vh;
    }
}

@media (max-width: 991.98px) {
    .main-slider {
        height: 70vh;
    }

    .slide-title {
        font-size: 2.8rem;
    }

    .slide-description {
        font-size: 1rem;
    }

    .top-10-rank {
        font-size: 6rem;
        left: -10px;
    }

    .top-10-item {
        width: 200px;
    }

    .slider-controls {
        bottom: 20px;
    }
}

@media (max-width: 767.98px) {
    .main-slider {
        height: 60vh;
        min-height: 500px;
    }

    .slide-title {
        font-size: 2.5rem;
    }

    .slide-description {
        font-size: 1rem;
    }

    .slide-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .slide-buttons .btn {
        width: 100%;
    }

    .top-10-rank {
        font-size: 5rem;
        left: -10px;
    }

    .top-10-item {
        width: 180px;
    }

    .top-10-item .movie-card {
        margin-left: 20px;
    }

    .premium-badge {
        width: 30px;
        height: 30px;
    }

    .premium-badge i {
        font-size: 0.8rem;
    }

    .slider-controls {
        bottom: 15px;
    }

    .slider-prev,
    .slider-next {
        width: 35px;
        height: 35px;
    }

    .slider-dot {
        width: 10px;
        height: 10px;
    }
}

@media (max-width: 575.98px) {
    .main-slider {
        height: 50vh;
        min-height: 400px;
    }

    .slide-title {
        font-size: 2rem;
    }

    .slide-meta {
        flex-wrap: wrap;
        gap: 10px;
    }

    .slide-meta span {
        font-size: 0.8rem;
    }

    .slide-description {
        font-size: 0.9rem;
    }

    .top-10-rank {
        font-size: 4rem;
        left: -5px;
    }

    .top-10-item {
        width: 150px;
    }

    .top-10-item .movie-card {
        margin-left: 15px;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .premium-badge {
        width: 25px;
        height: 25px;
    }

    .premium-badge i {
        font-size: 0.7rem;
    }

    .premium-tag {
        font-size: 0.8rem;
        padding: 4px 10px;
    }

    .slider-controls {
        bottom: 10px;
        gap: 10px;
    }

    .slider-prev,
    .slider-next {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }

    .slider-dot {
        width: 8px;
        height: 8px;
    }

    .floating-chat-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        bottom: 20px;
        right: 20px !important;
        left: auto !important;
    }

    .chat-container {
        width: 300px;
        height: 450px;
        bottom: 80px;
        right: 10px !important;
        left: auto !important;
    }
}

.player-wrapper iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
}

/* Similar Section */
.similar-section {
    background-color: rgba(0, 0, 0, 0.7);
}

.movie-card {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    margin-bottom: 30px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.movie-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.5);
}

.movie-card-image {
    position: relative;
}

.movie-card img {
    width: 100%;
    height: 350px;
    object-fit: cover;
    transition: filter 0.3s ease;
}

.movie-card:hover img {
    filter: brightness(0.7);
}

.premium-tag {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: var(--primary-color);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.movie-card-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0) 100%);
    padding: 20px;
    transition: all 0.3s ease;
}

.movie-card:hover .movie-card-overlay {
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.7) 100%);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.movie-card-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
    transition: transform 0.3s ease;
}

.movie-card:hover .movie-card-title {
    transform: translateY(-20px);
}

.movie-card-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 0.9rem;
    opacity: 0.8;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.movie-card:hover .movie-card-info {
    transform: translateY(-15px);
    opacity: 1;
}

.movie-card-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.movie-card:hover .movie-card-buttons {
    opacity: 1;
    transform: translateY(0);
}

.movie-card-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.movie-card-btn:hover {
    background-color: var(--primary-color);
    color: white;
    transform: scale(1.1);
}