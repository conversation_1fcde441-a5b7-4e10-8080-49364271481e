Write-Host "===================================" -ForegroundColor Cyan
Write-Host "CinePix Android App Build Script" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan
Write-Host ""

Set-Location -Path "cinepix_app"

Write-Host "Cleaning project..." -ForegroundColor Yellow
flutter clean

Write-Host "Getting dependencies..." -ForegroundColor Yellow
flutter pub get

Write-Host "Building APK..." -ForegroundColor Yellow
flutter build apk --release

Write-Host ""
Write-Host "===================================" -ForegroundColor Green
Write-Host "Build completed!" -ForegroundColor Green
Write-Host ""
Write-Host "APK location: build\app\outputs\flutter-apk\app-release.apk" -ForegroundColor Yellow
Write-Host "===================================" -ForegroundColor Green
Write-Host ""

Read-Host -Prompt "Press Enter to exit"
