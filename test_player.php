<?php
// Include config file to get site URL and other settings
require_once 'includes/config.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Player Test</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Plyr.io CSS -->
    <link rel="stylesheet" href="https://cdn.plyr.io/3.7.8/plyr.css" />

    <style>
        body {
            background-color: #111;
            color: #fff;
            padding: 20px;
        }
        .player-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #000;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        .video-info {
            padding: 20px;
            background-color: #222;
            border-radius: 0 0 8px 8px;
        }
        h1 {
            font-size: 24px;
            margin-bottom: 20px;
        }
        .plyr {
            border-radius: 8px 8px 0 0;
            --plyr-color-main: #e50914;
        }
        .btn-back {
            margin-bottom: 20px;
        }
        .player-options {
            margin-top: 20px;
            padding: 15px;
            background-color: #333;
            border-radius: 8px;
        }
        .player-selector {
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <a href="<?php echo SITE_URL; ?>" class="btn btn-outline-light btn-back">
            <i class="fas fa-arrow-left"></i> Back to Home
        </a>

        <div class="player-container">
            <div class="player-options">
                <div class="row">
                    <div class="col-md-6">
                        <div class="player-selector">
                            <label for="player-type" class="form-label">Select Player:</label>
                            <select id="player-type" class="form-select">
                                <option value="plyr">Plyr.js Player</option>
                                <option value="videojs">Video.js Player</option>
                                <option value="jwplayer">JW Player</option>
                                <option value="html5">Native HTML5 Player</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="video-url" class="form-label">Video URL:</label>
                            <input type="text" id="video-url" class="form-control" value="https://nim-pata-worker-quiet-river-e51c.bdmovieshub.workers.dev/1:/File%20July%20to%20December/10-1-24/When%20Evil%20Lurks%20(2023)%20WEBRip%20Hindi%20+%20Ta%20Te%20Es%20720p%20AVC%20DDP%205.1%20ESub.mkv">
                        </div>
                    </div>
                </div>
                <div class="d-grid">
                    <button id="load-video" class="btn btn-danger">Load Video</button>
                </div>
            </div>

            <!-- Player container -->
            <div id="player-wrapper">
                <!-- Plyr.js Player -->
                <div id="plyr-container" class="player-item active">
                    <video id="plyr-player" playsinline controls>
                        <source src="https://nim-pata-worker-quiet-river-e51c.bdmovieshub.workers.dev/1:/File%20July%20to%20December/10-1-24/When%20Evil%20Lurks%20(2023)%20WEBRip%20Hindi%20+%20Ta%20Te%20Es%20720p%20AVC%20DDP%205.1%20ESub.mkv" type="video/mp4" />
                    </video>
                </div>

                <!-- Video.js Player -->
                <div id="videojs-container" class="player-item" style="display:none;">
                    <video id="videojs-player" class="video-js vjs-big-play-centered" controls preload="auto" width="100%" height="600">
                        <source src="https://nim-pata-worker-quiet-river-e51c.bdmovieshub.workers.dev/1:/File%20July%20to%20December/10-1-24/When%20Evil%20Lurks%20(2023)%20WEBRip%20Hindi%20+%20Ta%20Te%20Es%20720p%20AVC%20DDP%205.1%20ESub.mkv" type="video/mp4" />
                    </video>
                </div>

                <!-- JW Player -->
                <div id="jwplayer-container" class="player-item" style="display:none;">
                    <div id="jwplayer"></div>
                </div>

                <!-- Native HTML5 Player -->
                <div id="html5-container" class="player-item" style="display:none;">
                    <video id="html5-player" controls width="100%" height="600">
                        <source src="https://nim-pata-worker-quiet-river-e51c.bdmovieshub.workers.dev/1:/File%20July%20to%20December/10-1-24/When%20Evil%20Lurks%20(2023)%20WEBRip%20Hindi%20+%20Ta%20Te%20Es%20720p%20AVC%20DDP%205.1%20ESub.mkv" type="video/mp4" />
                    </video>
                </div>
            </div>

            <div class="video-info">
                <h1>When Evil Lurks (2023) - Hindi - WEBRip - 720p</h1>
                <p>Testing different video players with Cloudflare Workers video URL.</p>
                <div class="alert alert-info">
                    <strong>Note:</strong> If the video doesn't play, it might be due to CORS restrictions or the video format not being supported by the browser.
                </div>
                <div class="mt-3">
                    <a href="<?php echo SITE_URL; ?>/plyr_player_enhanced.php?url=<?php echo urlencode('https://nim-pata-worker-quiet-river-e51c.bdmovieshub.workers.dev/1:/File%20July%20to%20December/10-1-24/When%20Evil%20Lurks%20(2023)%20WEBRip%20Hindi%20+%20Ta%20Te%20Es%20720p%20AVC%20DDP%205.1%20ESub.mkv'); ?>&title=<?php echo urlencode('When Evil Lurks (2023)'); ?>&quality=720p&language=Hindi&source=WEBRip" class="btn btn-danger">
                        <i class="fas fa-play me-2"></i> Open in Enhanced Player
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Plyr.js -->
    <script src="https://cdn.plyr.io/3.7.8/plyr.polyfilled.js"></script>

    <!-- Video.js -->
    <link href="https://vjs.zencdn.net/7.20.3/video-js.css" rel="stylesheet" />
    <script src="https://vjs.zencdn.net/7.20.3/video.min.js"></script>

    <!-- JW Player -->
    <script src="https://content.jwplatform.com/libraries/<?php echo JW_PLAYER_LICENSE; ?>.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Plyr
            const plyrPlayer = new Plyr('#plyr-player', {
                controls: [
                    'play-large', 'play', 'progress', 'current-time', 'mute',
                    'volume', 'captions', 'settings', 'pip', 'airplay', 'fullscreen'
                ],
                seekTime: 10,
                keyboard: { focused: true, global: true }
            });

            // Initialize Video.js
            const videojsPlayer = videojs('videojs-player', {
                controls: true,
                autoplay: false,
                preload: 'auto',
                fluid: true,
                playbackRates: [0.5, 1, 1.5, 2]
            });

            // Initialize JW Player
            jwplayer('jwplayer').setup({
                file: document.getElementById('video-url').value,
                width: '100%',
                aspectratio: '16:9',
                stretching: 'uniform',
                primary: 'html5',
                hlshtml: true,
                controls: true
            });

            // Player selector
            const playerType = document.getElementById('player-type');
            const loadVideoBtn = document.getElementById('load-video');
            const videoUrl = document.getElementById('video-url');

            // Player containers
            const plyrContainer = document.getElementById('plyr-container');
            const videojsContainer = document.getElementById('videojs-container');
            const jwplayerContainer = document.getElementById('jwplayer-container');
            const html5Container = document.getElementById('html5-container');

            // Hide all players
            function hideAllPlayers() {
                plyrContainer.style.display = 'none';
                videojsContainer.style.display = 'none';
                jwplayerContainer.style.display = 'none';
                html5Container.style.display = 'none';
            }

            // Load video button click
            loadVideoBtn.addEventListener('click', function() {
                const url = videoUrl.value;
                const player = playerType.value;

                // Hide all players
                hideAllPlayers();

                // Show selected player
                if (player === 'plyr') {
                    plyrContainer.style.display = 'block';
                    document.getElementById('plyr-player').src = url;
                    plyrPlayer.source = {
                        type: 'video',
                        sources: [
                            {
                                src: url,
                                type: 'video/mp4',
                            }
                        ]
                    };
                } else if (player === 'videojs') {
                    videojsContainer.style.display = 'block';
                    videojsPlayer.src({
                        src: url,
                        type: 'video/mp4'
                    });
                    videojsPlayer.load();
                } else if (player === 'jwplayer') {
                    jwplayerContainer.style.display = 'block';
                    jwplayer('jwplayer').setup({
                        file: url,
                        width: '100%',
                        aspectratio: '16:9',
                        stretching: 'uniform',
                        primary: 'html5',
                        hlshtml: true,
                        controls: true
                    });
                } else if (player === 'html5') {
                    html5Container.style.display = 'block';
                    document.getElementById('html5-player').src = url;
                    document.getElementById('html5-player').load();
                }
            });

            // Initial player setup
            hideAllPlayers();
            plyrContainer.style.display = 'block';
        });
    </script>
</body>
</html>
