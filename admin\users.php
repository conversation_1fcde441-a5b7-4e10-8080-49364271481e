<?php
// Set page title
$page_title = 'Users';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Process form submissions
$success_message = '';
$error_message = '';

// Update User
if (isset($_POST['update_user'])) {
    $user_id = (int)$_POST['user_id'];
    $username = sanitize($_POST['username']);
    $email = sanitize($_POST['email']);
    $role = sanitize($_POST['role']);
    $premium_plan = isset($_POST['premium_plan']) ? (int)$_POST['premium_plan'] : 0;
    $subscription_duration = isset($_POST['subscription_duration']) ? (int)$_POST['subscription_duration'] : 30;
    $is_premium = ($premium_plan > 0) ? 1 : 0;
    $password = !empty($_POST['password']) ? sanitize($_POST['password']) : '';

    if (empty($username) || empty($email)) {
        $error_message = 'Username and email are required.';
    } else {
        // Check if username or email already exists
        $check_query = "SELECT * FROM users WHERE (username = '$username' OR email = '$email') AND id != $user_id";
        $check_result = mysqli_query($conn, $check_query);

        if (mysqli_num_rows($check_result) > 0) {
            $error_message = 'Username or email already exists.';
        } else {
            // Begin transaction
            mysqli_begin_transaction($conn);

            try {
                // Update user
                $update_query = "UPDATE users SET
                                username = '$username',
                                email = '$email',
                                role = '$role',
                                is_premium = $is_premium";

                // Add password to update query if provided
                if (!empty($password)) {
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                    $update_query .= ", password = '$hashed_password'";
                }

                $update_query .= " WHERE id = $user_id";

                $update_result = mysqli_query($conn, $update_query);

                if (!$update_result) {
                    throw new Exception('Error updating user: ' . mysqli_error($conn));
                }

                // Handle premium status and subscription
                if ($is_premium == 0) {
                    // Cancel all active subscriptions for this user
                    $cancel_subscriptions_query = "UPDATE subscriptions SET status = 'cancelled'
                                                 WHERE user_id = $user_id AND status = 'active'";
                    $cancel_result = mysqli_query($conn, $cancel_subscriptions_query);

                    if (!$cancel_result) {
                        throw new Exception('Error cancelling subscriptions: ' . mysqli_error($conn));
                    }

                    // If this is the current logged in user, update session
                    if ($user_id == $_SESSION['user_id']) {
                        $_SESSION['is_premium'] = 0;
                    }
                } else if ($is_premium == 1 && $premium_plan > 0) {
                    // Get plan details
                    $plan_query = "SELECT * FROM premium_plans WHERE id = $premium_plan";
                    $plan_result = mysqli_query($conn, $plan_query);

                    if (!$plan_result || mysqli_num_rows($plan_result) == 0) {
                        throw new Exception('Selected premium plan not found');
                    }

                    $plan = mysqli_fetch_assoc($plan_result);
                    $plan_id = $plan['id'];
                    $plan_price = $plan['price'];

                    // Check if there's an active subscription
                    $check_subscription_query = "SELECT * FROM subscriptions
                                               WHERE user_id = $user_id AND status = 'active' AND end_date > NOW()
                                               ORDER BY end_date DESC LIMIT 1";
                    $check_result = mysqli_query($conn, $check_subscription_query);

                    if (!$check_result) {
                        throw new Exception('Error checking subscriptions: ' . mysqli_error($conn));
                    }

                    $has_active_subscription = mysqli_num_rows($check_result) > 0;
                    $current_subscription = $has_active_subscription ? mysqli_fetch_assoc($check_result) : null;

                    // If there's an active subscription with the same plan, just update the end date if needed
                    if ($has_active_subscription && $current_subscription['plan_id'] == $plan_id) {
                        // Only update if the duration has changed
                        $current_end_date = new DateTime($current_subscription['end_date']);
                        $now = new DateTime();
                        $days_remaining = $current_end_date->diff($now)->days;

                        if ($subscription_duration > $days_remaining) {
                            // Update end date
                            $new_end_date = date('Y-m-d H:i:s', strtotime("+{$subscription_duration} days"));
                            $update_subscription_query = "UPDATE subscriptions SET end_date = '$new_end_date'
                                                       WHERE id = {$current_subscription['id']}";
                            $update_result = mysqli_query($conn, $update_subscription_query);

                            if (!$update_result) {
                                throw new Exception('Error updating subscription: ' . mysqli_error($conn));
                            }

                            // Create payment record for the extension
                            $create_payment_query = "INSERT INTO payments (user_id, subscription_id, amount, payment_method, transaction_id, payment_status)
                                                   VALUES ($user_id, {$current_subscription['id']}, $plan_price, 'manual', 'ADMIN-EXT-" . time() . "', 'completed')";
                            $create_payment_result = mysqli_query($conn, $create_payment_query);

                            if (!$create_payment_result) {
                                throw new Exception('Error creating payment record: ' . mysqli_error($conn));
                            }
                        }
                    } else {
                        // If there's an active subscription with a different plan, cancel it
                        if ($has_active_subscription) {
                            $cancel_query = "UPDATE subscriptions SET status = 'cancelled'
                                           WHERE id = {$current_subscription['id']}";
                            $cancel_result = mysqli_query($conn, $cancel_query);

                            if (!$cancel_result) {
                                throw new Exception('Error cancelling current subscription: ' . mysqli_error($conn));
                            }
                        }

                        // Create new subscription
                        $start_date = date('Y-m-d H:i:s');
                        $end_date = date('Y-m-d H:i:s', strtotime("+{$subscription_duration} days"));

                        $create_subscription_query = "INSERT INTO subscriptions (user_id, plan_id, start_date, end_date, status)
                                                   VALUES ($user_id, $plan_id, '$start_date', '$end_date', 'active')";
                        $create_result = mysqli_query($conn, $create_subscription_query);

                        if (!$create_result) {
                            throw new Exception('Error creating subscription: ' . mysqli_error($conn));
                        }

                        // Create payment record
                        $subscription_id = mysqli_insert_id($conn);
                        $create_payment_query = "INSERT INTO payments (user_id, subscription_id, amount, payment_method, transaction_id, payment_status)
                                               VALUES ($user_id, $subscription_id, $plan_price, 'manual', 'ADMIN-" . time() . "', 'completed')";
                        $create_payment_result = mysqli_query($conn, $create_payment_query);

                        if (!$create_payment_result) {
                            throw new Exception('Error creating payment record: ' . mysqli_error($conn));
                        }
                    }

                    // If this is the current logged in user, update session
                    if ($user_id == $_SESSION['user_id']) {
                        $_SESSION['is_premium'] = 1;
                    }
                }

                // Commit transaction
                mysqli_commit($conn);
                $success_message = 'User updated successfully.';
            } catch (Exception $e) {
                // Rollback transaction on error
                mysqli_rollback($conn);
                $error_message = $e->getMessage();
            }
        }
    }
}

// Delete User
if (isset($_GET['delete']) && $_GET['delete'] > 0) {
    $user_id = (int)$_GET['delete'];

    // Check if user is the current admin
    if ($user_id == $_SESSION['user_id']) {
        $error_message = 'You cannot delete your own account.';
    } else {
        // Delete user
        $delete_query = "DELETE FROM users WHERE id = $user_id";

        if (mysqli_query($conn, $delete_query)) {
            $success_message = 'User deleted successfully.';
        } else {
            $error_message = 'Error deleting user: ' . mysqli_error($conn);
        }
    }
}

// Get user to edit
$edit_user = null;
if (isset($_GET['edit']) && $_GET['edit'] > 0) {
    $user_id = (int)$_GET['edit'];

    $edit_query = "SELECT * FROM users WHERE id = $user_id";
    $edit_result = mysqli_query($conn, $edit_query);

    if (mysqli_num_rows($edit_result) > 0) {
        $edit_user = mysqli_fetch_assoc($edit_result);
    }
}

// Get premium plans
$plans_query = "SELECT * FROM premium_plans ORDER BY price ASC";
$plans_result = mysqli_query($conn, $plans_query);
$premium_plans = [];
while ($plan = mysqli_fetch_assoc($plans_result)) {
    $premium_plans[] = $plan;
}

// Get users list with subscription info
$users_query = "SELECT u.*,
               (SELECT p.name FROM subscriptions s
                JOIN premium_plans p ON s.plan_id = p.id
                WHERE s.user_id = u.id AND s.status = 'active' AND s.end_date > NOW()
                ORDER BY s.end_date DESC LIMIT 1) as plan_name,
               (SELECT s.end_date FROM subscriptions s
                WHERE s.user_id = u.id AND s.status = 'active' AND s.end_date > NOW()
                ORDER BY s.end_date DESC LIMIT 1) as subscription_end_date
               FROM users u ORDER BY u.created_at DESC";
$users_result = mysqli_query($conn, $users_query);

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Users</h1>
            </div>
            <div class="topbar-actions">
                <div class="topbar-search">
                    <form action="search.php" method="GET">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="Search users..." name="q">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                    <i class="fas fa-plus-circle me-2"></i>Add New User
                </button>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <?php if($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="row">
            <?php if($edit_user): ?>
            <!-- Edit User Form -->
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Edit User</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="" class="needs-validation" novalidate>
                            <input type="hidden" name="user_id" value="<?php echo $edit_user['id']; ?>">

                            <div class="mb-3">
                                <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" value="<?php echo $edit_user['username']; ?>" required>
                                <div class="invalid-feedback">
                                    Please enter a username.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" value="<?php echo $edit_user['email']; ?>" required>
                                <div class="invalid-feedback">
                                    Please enter a valid email address.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" name="password">
                                    <button class="btn btn-outline-secondary toggle-password" type="button" data-target="password">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">Leave empty to keep current password.</div>
                            </div>

                            <div class="mb-3">
                                <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="user" <?php echo $edit_user['role'] == 'user' ? 'selected' : ''; ?>>User</option>
                                    <option value="admin" <?php echo $edit_user['role'] == 'admin' ? 'selected' : ''; ?>>Admin</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="premium_plan" class="form-label">Premium Plan</label>
                                <select class="form-select" id="premium_plan" name="premium_plan">
                                    <option value="0">No Premium (Free User)</option>
                                    <?php foreach ($premium_plans as $plan): ?>
                                    <option value="<?php echo $plan['id']; ?>" <?php
                                        // Get current active subscription for this user
                                        $current_plan_query = "SELECT s.*, p.name FROM subscriptions s
                                                              JOIN premium_plans p ON s.plan_id = p.id
                                                              WHERE s.user_id = {$edit_user['id']}
                                                              AND s.status = 'active' AND s.end_date > NOW()
                                                              ORDER BY s.end_date DESC LIMIT 1";
                                        $current_plan_result = mysqli_query($conn, $current_plan_query);
                                        $has_active_plan = mysqli_num_rows($current_plan_result) > 0;
                                        $current_plan = $has_active_plan ? mysqli_fetch_assoc($current_plan_result) : null;
                                        echo ($has_active_plan && $current_plan['plan_id'] == $plan['id']) ? 'selected' : '';
                                    ?>>
                                        <?php echo $plan['name']; ?> (৳<?php echo number_format($plan['price'], 0); ?> / <?php echo $plan['duration']; ?> দিন)
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">
                                    <?php if ($has_active_plan): ?>
                                    বর্তমান প্যাকেজ: <strong><?php echo $current_plan['name']; ?></strong>,
                                    মেয়াদ শেষ: <strong><?php echo date('d M Y', strtotime($current_plan['end_date'])); ?></strong>
                                    <?php else: ?>
                                    এই ব্যবহারকারীর কোন সক্রিয় প্রিমিয়াম প্যাকেজ নেই।
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="subscription_duration" class="form-label">সাবস্ক্রিপশন মেয়াদ (দিন)</label>
                                <input type="number" class="form-control" id="subscription_duration" name="subscription_duration" value="30" min="1" max="365">
                                <div class="form-text">নতুন প্যাকেজের মেয়াদ কত দিন হবে তা নির্ধারণ করুন।</div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" name="update_user" class="btn btn-primary">Update User</button>
                                <a href="users.php" class="btn btn-secondary">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Users List -->
            <div class="col-lg-<?php echo $edit_user ? '8' : '12'; ?> mb-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">All Users</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover datatable">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Avatar</th>
                                        <th>Username</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>Premium</th>
                                        <th>Joined</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if(mysqli_num_rows($users_result) > 0): ?>
                                        <?php while($user = mysqli_fetch_assoc($users_result)): ?>
                                        <tr>
                                            <td><?php echo $user['id']; ?></td>
                                            <td>
                                                <?php if (!empty($user['profile_image']) && file_exists('../uploads/' . $user['profile_image'])): ?>
                                                    <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $user['profile_image']; ?>" alt="<?php echo $user['username']; ?>" class="user-avatar">
                                                <?php else: ?>
                                                    <div class="user-avatar d-flex align-items-center justify-content-center bg-light">
                                                        <i class="fas fa-user text-secondary"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo $user['username']; ?></td>
                                            <td><?php echo $user['email']; ?></td>
                                            <td>
                                                <?php if($user['role'] == 'admin'): ?>
                                                <span class="badge bg-danger">Admin</span>
                                                <?php else: ?>
                                                <span class="badge bg-secondary">User</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($user['is_premium']): ?>
                                                <span class="badge bg-success">Yes</span>
                                                <?php if(!empty($user['plan_name'])): ?>
                                                <div class="small mt-1">
                                                    <strong><?php echo $user['plan_name']; ?></strong>
                                                    <?php if(!empty($user['subscription_end_date'])): ?>
                                                    <br><span class="text-muted">Until: <?php echo date('d M Y', strtotime($user['subscription_end_date'])); ?></span>
                                                    <?php endif; ?>
                                                </div>
                                                <?php endif; ?>
                                                <?php else: ?>
                                                <span class="badge bg-secondary">No</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="users.php?edit=<?php echo $user['id']; ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if($user['id'] != $_SESSION['user_id']): ?>
                                                    <a href="users.php?delete=<?php echo $user['id']; ?>" class="btn btn-sm btn-danger btn-delete" data-bs-toggle="tooltip" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="8" class="text-center">No users found.</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addUserModalLabel">Add New User</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="add_user.php" method="POST" class="needs-validation" novalidate>
                    <div class="mb-3">
                        <label for="new_username" class="form-label">Username <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="new_username" name="username" required>
                        <div class="invalid-feedback">
                            Please enter a username.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="new_email" class="form-label">Email <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="new_email" name="email" required>
                        <div class="invalid-feedback">
                            Please enter a valid email address.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="new_password" class="form-label">Password <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="new_password" name="password" required>
                            <button class="btn btn-outline-secondary toggle-password" type="button" data-target="new_password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="invalid-feedback">
                            Please enter a password.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="new_role" class="form-label">Role <span class="text-danger">*</span></label>
                        <select class="form-select" id="new_role" name="role" required>
                            <option value="user" selected>User</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="new_premium_plan" class="form-label">Premium Plan</label>
                        <select class="form-select" id="new_premium_plan" name="premium_plan">
                            <option value="0">No Premium (Free User)</option>
                            <?php foreach ($premium_plans as $plan): ?>
                            <option value="<?php echo $plan['id']; ?>">
                                <?php echo $plan['name']; ?> (৳<?php echo number_format($plan['price'], 0); ?> / <?php echo $plan['duration']; ?> দিন)
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="new_subscription_duration" class="form-label">সাবস্ক্রিপশন মেয়াদ (দিন)</label>
                        <input type="number" class="form-control" id="new_subscription_duration" name="subscription_duration" value="30" min="1" max="365">
                        <div class="form-text">প্রিমিয়াম প্যাকেজের মেয়াদ কত দিন হবে তা নির্ধারণ করুন।</div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Add User</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
