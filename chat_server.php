<?php
require_once 'includes/config.php';

// This file will handle AJAX requests for chat functionality
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'login_required']);
    exit;
}

$user_id = $_SESSION['user_id'];
$action = isset($_POST['action']) ? $_POST['action'] : '';

// Get active chat session or create new one
function getActiveChatSession($user_id, $conn) {
    // Check for existing active session
    $session_query = "SELECT * FROM chat_sessions WHERE user_id = $user_id AND status = 'active' ORDER BY updated_at DESC LIMIT 1";
    $session_result = mysqli_query($conn, $session_query);
    
    if (mysqli_num_rows($session_result) > 0) {
        // Return existing session
        return mysqli_fetch_assoc($session_result);
    } else {
        // Create new session
        $insert_query = "INSERT INTO chat_sessions (user_id) VALUES ($user_id)";
        if (mysqli_query($conn, $insert_query)) {
            $session_id = mysqli_insert_id($conn);
            
            // Get the new session
            $new_session_query = "SELECT * FROM chat_sessions WHERE id = $session_id";
            $new_session_result = mysqli_query($conn, $new_session_query);
            return mysqli_fetch_assoc($new_session_result);
        } else {
            return false;
        }
    }
}

// Send a chat message
function sendChatMessage($session_id, $sender_id, $receiver_id, $message, $conn) {
    $message = sanitize($message);
    
    $insert_query = "INSERT INTO chat_messages (session_id, sender_id, receiver_id, message) 
                    VALUES ($session_id, $sender_id, $receiver_id, '$message')";
    
    if (mysqli_query($conn, $insert_query)) {
        // Update session timestamp
        $update_query = "UPDATE chat_sessions SET updated_at = NOW() WHERE id = $session_id";
        mysqli_query($conn, $update_query);
        
        return mysqli_insert_id($conn);
    } else {
        return false;
    }
}

// Get chat messages
function getChatMessages($session_id, $user_id, $conn) {
    // Mark messages as read
    $update_query = "UPDATE chat_messages SET is_read = TRUE 
                    WHERE session_id = $session_id AND receiver_id = $user_id AND is_read = FALSE";
    mysqli_query($conn, $update_query);
    
    // Get messages
    $messages_query = "SELECT cm.*, u.username, u.profile_image, u.role 
                      FROM chat_messages cm 
                      JOIN users u ON cm.sender_id = u.id 
                      WHERE cm.session_id = $session_id 
                      ORDER BY cm.created_at ASC";
    $messages_result = mysqli_query($conn, $messages_query);
    
    $messages = [];
    while ($message = mysqli_fetch_assoc($messages_result)) {
        $messages[] = [
            'id' => $message['id'],
            'sender_id' => $message['sender_id'],
            'receiver_id' => $message['receiver_id'],
            'message' => $message['message'],
            'is_read' => $message['is_read'],
            'created_at' => $message['created_at'],
            'username' => $message['username'],
            'profile_image' => $message['profile_image'],
            'role' => $message['role'],
            'is_self' => $message['sender_id'] == $user_id
        ];
    }
    
    return $messages;
}

// Get all admin users
function getAdminUsers($conn) {
    $admin_query = "SELECT id, username, profile_image FROM users WHERE role = 'admin'";
    $admin_result = mysqli_query($conn, $admin_query);
    
    $admins = [];
    while ($admin = mysqli_fetch_assoc($admin_result)) {
        $admins[] = $admin;
    }
    
    return $admins;
}

// Handle different actions
switch ($action) {
    case 'init':
        // Initialize chat session
        $session = getActiveChatSession($user_id, $conn);
        
        if ($session) {
            $messages = getChatMessages($session['id'], $user_id, $conn);
            $admins = getAdminUsers($conn);
            
            echo json_encode([
                'success' => true,
                'session' => $session,
                'messages' => $messages,
                'admins' => $admins
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to create chat session']);
        }
        break;
        
    case 'send':
        // Send a message
        $session_id = isset($_POST['session_id']) ? (int)$_POST['session_id'] : 0;
        $message = isset($_POST['message']) ? $_POST['message'] : '';
        $receiver_id = isset($_POST['receiver_id']) ? (int)$_POST['receiver_id'] : 0;
        
        if (empty($session_id) || empty($message) || empty($receiver_id)) {
            echo json_encode(['success' => false, 'message' => 'Invalid parameters']);
            exit;
        }
        
        $message_id = sendChatMessage($session_id, $user_id, $receiver_id, $message, $conn);
        
        if ($message_id) {
            // Get the sent message
            $message_query = "SELECT cm.*, u.username, u.profile_image, u.role 
                             FROM chat_messages cm 
                             JOIN users u ON cm.sender_id = u.id 
                             WHERE cm.id = $message_id";
            $message_result = mysqli_query($conn, $message_query);
            $message_data = mysqli_fetch_assoc($message_result);
            
            $message_data['is_self'] = true;
            
            echo json_encode([
                'success' => true,
                'message' => $message_data
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to send message']);
        }
        break;
        
    case 'poll':
        // Poll for new messages
        $session_id = isset($_POST['session_id']) ? (int)$_POST['session_id'] : 0;
        $last_message_id = isset($_POST['last_message_id']) ? (int)$_POST['last_message_id'] : 0;
        
        if (empty($session_id)) {
            echo json_encode(['success' => false, 'message' => 'Invalid session ID']);
            exit;
        }
        
        // Get new messages
        $new_messages_query = "SELECT cm.*, u.username, u.profile_image, u.role 
                              FROM chat_messages cm 
                              JOIN users u ON cm.sender_id = u.id 
                              WHERE cm.session_id = $session_id AND cm.id > $last_message_id 
                              ORDER BY cm.created_at ASC";
        $new_messages_result = mysqli_query($conn, $new_messages_query);
        
        $new_messages = [];
        while ($message = mysqli_fetch_assoc($new_messages_result)) {
            // Mark as read if user is the receiver
            if ($message['receiver_id'] == $user_id && !$message['is_read']) {
                $update_query = "UPDATE chat_messages SET is_read = TRUE WHERE id = {$message['id']}";
                mysqli_query($conn, $update_query);
                $message['is_read'] = true;
            }
            
            $message['is_self'] = $message['sender_id'] == $user_id;
            $new_messages[] = $message;
        }
        
        echo json_encode([
            'success' => true,
            'messages' => $new_messages
        ]);
        break;
        
    case 'close':
        // Close chat session
        $session_id = isset($_POST['session_id']) ? (int)$_POST['session_id'] : 0;
        
        if (empty($session_id)) {
            echo json_encode(['success' => false, 'message' => 'Invalid session ID']);
            exit;
        }
        
        $update_query = "UPDATE chat_sessions SET status = 'closed' WHERE id = $session_id AND user_id = $user_id";
        
        if (mysqli_query($conn, $update_query)) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to close chat session']);
        }
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}
?>
