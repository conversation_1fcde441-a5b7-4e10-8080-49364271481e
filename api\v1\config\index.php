<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// API Config Endpoints

// Get the request
global $request;

// Handle different endpoints based on the path parts
$action = $request['parts'][0] ?? 'app_config';

switch ($action) {
    case 'app_config':
        handle_app_config($request);
        break;

    case 'update':
        if (is_admin()) {
            handle_update_config($request);
        } else {
            api_error('Unauthorized access', 403);
        }
        break;

    default:
        api_error('Invalid config endpoint', 404);
}

// Handle get app configuration
function handle_app_config($request) {
    global $conn;

    // Check request method
    if ($request['method'] !== 'GET') {
        api_error('Method not allowed', 405);
    }

    // Get all app configurations
    $query = "SELECT config_key, config_value FROM app_config";
    $result = mysqli_query($conn, $query);

    if (!$result) {
        api_error('Failed to fetch app configuration: ' . mysqli_error($conn), 500);
    }

    $config = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $config[$row['config_key']] = $row['config_value'];
    }

    // Add additional app information
    $config['api_version'] = API_VERSION;
    $config['site_name'] = SITE_NAME;
    $config['site_url'] = SITE_URL;

    // Get premium plans
    $plans_query = "SELECT id, name, price, duration, features FROM premium_plans WHERE status = 'active' ORDER BY price ASC";
    $plans_result = mysqli_query($conn, $plans_query);

    if ($plans_result) {
        $premium_plans = [];
        while ($plan = mysqli_fetch_assoc($plans_result)) {
            $premium_plans[] = [
                'id' => (int)$plan['id'],
                'name' => $plan['name'],
                'price' => (float)$plan['price'],
                'duration' => (int)$plan['duration'],
                'features' => explode("\n", $plan['features'])
            ];
        }
        $config['premium_plans'] = $premium_plans;
    }

    // Get payment methods
    $payment_query = "SELECT * FROM payment_settings WHERE id = 1";
    $payment_result = mysqli_query($conn, $payment_query);

    if ($payment_result && mysqli_num_rows($payment_result) > 0) {
        $payment_settings = mysqli_fetch_assoc($payment_result);
        $payment_methods = [];

        if ($payment_settings['bkash_enabled']) {
            $payment_methods[] = [
                'id' => 'bkash',
                'name' => 'বিকাশ',
                'merchant_number' => $payment_settings['bkash_merchant_number'],
                'merchant_name' => $payment_settings['bkash_merchant_name'],
                'is_automatic' => true
            ];
        }

        if ($payment_settings['nagad_enabled']) {
            $payment_methods[] = [
                'id' => 'nagad',
                'name' => 'নগদ',
                'merchant_number' => $payment_settings['nagad_merchant_number'],
                'merchant_name' => $payment_settings['nagad_merchant_name'],
                'is_automatic' => false
            ];
        }

        if ($payment_settings['rocket_enabled']) {
            $payment_methods[] = [
                'id' => 'rocket',
                'name' => 'রকেট',
                'merchant_number' => $payment_settings['rocket_merchant_number'],
                'merchant_name' => $payment_settings['rocket_merchant_name'],
                'is_automatic' => false
            ];
        }

        $config['payment_methods'] = $payment_methods;
        $config['payment_instructions'] = $payment_settings['payment_instructions'];
    }

    // Return app configuration
    api_response($config);
}

// Handle update app configuration (admin only)
function handle_update_config($request) {
    global $conn;

    // Check request method
    if ($request['method'] !== 'POST') {
        api_error('Method not allowed', 405);
    }

    // Validate required fields
    if (empty($request['body']['config_key']) || !isset($request['body']['config_value'])) {
        api_error('Config key and value are required', 400);
    }

    $config_key = $request['body']['config_key'];
    $config_value = $request['body']['config_value'];

    // Update or insert config
    $query = "INSERT INTO app_config (config_key, config_value)
              VALUES (?, ?)
              ON DUPLICATE KEY UPDATE config_value = ?";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'sss', $config_key, $config_value, $config_value);

    if (!mysqli_stmt_execute($stmt)) {
        api_error('Failed to update configuration: ' . mysqli_error($conn), 500);
    }

    // Return success response
    api_response([
        'config_key' => $config_key,
        'config_value' => $config_value
    ], 200, 'Configuration updated successfully');
}
