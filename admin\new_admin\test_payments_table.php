<?php
// Test payments table
require_once '../../includes/config.php';

echo "<h1>Payments Table Test</h1>";

// Check connection
if (!$conn) {
    die("❌ Database connection failed: " . mysqli_connect_error());
}
echo "✅ Database connected successfully<br><br>";

// Check if payments table exists
$table_check = "SHOW TABLES LIKE 'payments'";
$table_result = mysqli_query($conn, $table_check);

if ($table_result && mysqli_num_rows($table_result) > 0) {
    echo "✅ Payments table exists<br>";
    
    // Check table structure
    $structure_query = "DESCRIBE payments";
    $structure_result = mysqli_query($conn, $structure_query);
    
    if ($structure_result) {
        echo "<h3>Table Structure:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        
        while ($field = mysqli_fetch_assoc($structure_result)) {
            echo "<tr>";
            echo "<td>" . $field['Field'] . "</td>";
            echo "<td>" . $field['Type'] . "</td>";
            echo "<td>" . $field['Null'] . "</td>";
            echo "<td>" . $field['Key'] . "</td>";
            echo "<td>" . $field['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table><br>";
    }
    
    // Count records
    $count_query = "SELECT COUNT(*) as count FROM payments";
    $count_result = mysqli_query($conn, $count_query);
    
    if ($count_result) {
        $count_data = mysqli_fetch_assoc($count_result);
        echo "📊 Total payments: " . $count_data['count'] . "<br>";
    }
    
    // Sample data
    $sample_query = "SELECT * FROM payments LIMIT 5";
    $sample_result = mysqli_query($conn, $sample_query);
    
    if ($sample_result && mysqli_num_rows($sample_result) > 0) {
        echo "<h3>Sample Data:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>User ID</th><th>Amount</th><th>Status</th><th>Transaction ID</th><th>Created At</th></tr>";
        
        while ($payment = mysqli_fetch_assoc($sample_result)) {
            echo "<tr>";
            echo "<td>" . $payment['id'] . "</td>";
            echo "<td>" . ($payment['user_id'] ?? 'N/A') . "</td>";
            echo "<td>" . ($payment['amount'] ?? 'N/A') . "</td>";
            echo "<td>" . ($payment['status'] ?? 'N/A') . "</td>";
            echo "<td>" . ($payment['transaction_id'] ?? 'N/A') . "</td>";
            echo "<td>" . ($payment['created_at'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "📝 No payment records found<br>";
    }
    
} else {
    echo "❌ Payments table does not exist<br>";
    echo "<h3>Creating payments table...</h3>";
    
    $create_table = "CREATE TABLE IF NOT EXISTS payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        package_type VARCHAR(50) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        payment_method VARCHAR(50) NOT NULL,
        transaction_id VARCHAR(100) UNIQUE,
        status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    
    if (mysqli_query($conn, $create_table)) {
        echo "✅ Payments table created successfully<br>";
    } else {
        echo "❌ Error creating payments table: " . mysqli_error($conn) . "<br>";
    }
}

echo "<br><a href='payments.php'>← Back to Payments</a>";
echo "<br><a href='index.php'>← Back to Dashboard</a>";
?>
