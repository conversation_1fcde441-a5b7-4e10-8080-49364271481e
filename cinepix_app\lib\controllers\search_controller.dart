import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import 'package:cinepix_app/models/movie.dart';
import 'package:cinepix_app/models/tv_show.dart';
import 'package:cinepix_app/services/api_service.dart';

class SearchController extends GetxController {
  final ApiService _apiService = ApiService();

  final RxString query = ''.obs;
  final RxList<Movie> movies = <Movie>[].obs;
  final RxList<TvShow> tvShows = <TvShow>[].obs;
  final RxBool isLoading = false.obs;
  final RxBool hasSearched = false.obs;
  final RxString errorMessage = ''.obs;

  // Update search query
  void updateQuery(String newQuery) {
    query.value = newQuery;
  }

  // Clear search results
  void clearSearch() {
    query.value = '';
    movies.clear();
    tvShows.clear();
    hasSearched.value = false;
  }

  // Perform search
  Future<void> search() async {
    if (query.value.isEmpty) return;

    isLoading.value = true;
    errorMessage.value = '';
    movies.clear();
    tvShows.clear();

    try {
      debugPrint('Starting search for: ${query.value}');
      final results = await _apiService.search(query.value);
      debugPrint('Search results received: $results');

      // Process movies
      if (results.containsKey('movies') && results['movies'] is List) {
        final moviesList = results['movies'] as List;
        debugPrint('Found ${moviesList.length} movies');

        try {
          movies.value =
              moviesList.map((movie) => Movie.fromJson(movie)).toList();
          debugPrint('Movies processed successfully');
        } catch (e) {
          debugPrint('Error processing movies: $e');
          if (moviesList.isNotEmpty) {
            debugPrint('Sample movie data: ${moviesList.first}');
          }
        }
      } else {
        debugPrint('No movies found or invalid format');
      }

      // Process TV shows
      if (results.containsKey('tvshows') && results['tvshows'] is List) {
        final tvShowsList = results['tvshows'] as List;
        debugPrint('Found ${tvShowsList.length} TV shows');

        try {
          tvShows.value =
              tvShowsList.map((tvShow) => TvShow.fromJson(tvShow)).toList();
          debugPrint('TV shows processed successfully');
        } catch (e) {
          debugPrint('Error processing TV shows: $e');
          if (tvShowsList.isNotEmpty) {
            debugPrint('Sample TV show data: ${tvShowsList.first}');
          }
        }
      } else {
        debugPrint('No TV shows found or invalid format');
      }

      hasSearched.value = true;
      debugPrint(
          'Search completed. Movies: ${movies.length}, TV Shows: ${tvShows.length}');
    } catch (e) {
      errorMessage.value = e.toString();
      debugPrint('Search error: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Get total results count
  int get totalResults => movies.length + tvShows.length;

  // Check if search has results
  bool get hasResults => totalResults > 0;

  // Get search results as a combined list
  List<dynamic> get searchResults {
    final List<dynamic> results = [];

    if (movies.isNotEmpty) {
      results.add('Movies');
      results.addAll(movies);
    }

    if (tvShows.isNotEmpty) {
      results.add('TV Shows');
      results.addAll(tvShows);
    }

    return results;
  }
}
