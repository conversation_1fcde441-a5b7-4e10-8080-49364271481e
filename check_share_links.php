<?php
require_once 'includes/config.php';

echo "<h2>শেয়ার লিংক চেক</h2>";

// Check if shared_links table exists
$check_table = mysqli_query($conn, "SHOW TABLES LIKE 'shared_links'");
if (mysqli_num_rows($check_table) == 0) {
    echo "<div style='color: red;'>❌ shared_links টেবিল নেই!</div>";
    exit;
}

echo "<div style='color: green;'>✅ shared_links টেবিল আছে</div>";

// Check if there are any shared links
$check_links = mysqli_query($conn, "SELECT COUNT(*) as total FROM shared_links");
$total_links = mysqli_fetch_assoc($check_links)['total'];

echo "<h3>মোট শেয়ার লিংক: $total_links</h3>";

if ($total_links == 0) {
    echo "<div style='color: orange;'>⚠️ কোনো শেয়ার লিংক নেই। প্রথমে একটি শেয়ার লিংক তৈরি করুন।</div>";
    echo "<a href='admin/shared_links.php' style='color: blue;'>শেয়ার লিংক তৈরি করুন</a>";
    exit;
}

// Get all active shared links
$links_query = "SELECT sl.*, 
                CASE 
                    WHEN sl.content_type = 'movie' THEN m.title
                    WHEN sl.content_type = 'tvshow' THEN t.title
                END as content_title
                FROM shared_links sl
                LEFT JOIN movies m ON sl.content_type = 'movie' AND sl.content_id = m.id
                LEFT JOIN tvshows t ON sl.content_type = 'tvshow' AND sl.content_id = t.id
                WHERE sl.is_active = 1
                ORDER BY sl.created_at DESC";

$links_result = mysqli_query($conn, $links_query);

echo "<h3>সক্রিয় শেয়ার লিংক:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f0f0f0;'>";
echo "<th>ID</th>";
echo "<th>টোকেন</th>";
echo "<th>কন্টেন্ট টাইপ</th>";
echo "<th>কন্টেন্ট টাইটেল</th>";
echo "<th>স্ট্যাটাস</th>";
echo "<th>এক্সেস কাউন্ট</th>";
echo "<th>লিংক</th>";
echo "</tr>";

while ($link = mysqli_fetch_assoc($links_result)) {
    $status = 'সক্রিয়';
    if ($link['expires_at'] && strtotime($link['expires_at']) < time()) {
        $status = 'মেয়াদ শেষ';
    } elseif ($link['access_limit'] > 0 && $link['access_count'] >= $link['access_limit']) {
        $status = 'লিমিট শেষ';
    }
    
    $share_url = SITE_URL . '/share.php?token=' . $link['link_token'];
    
    echo "<tr>";
    echo "<td>{$link['id']}</td>";
    echo "<td>{$link['link_token']}</td>";
    echo "<td>{$link['content_type']}</td>";
    echo "<td>{$link['content_title']}</td>";
    echo "<td>$status</td>";
    echo "<td>{$link['access_count']}";
    if ($link['access_limit'] > 0) {
        echo " / {$link['access_limit']}";
    }
    echo "</td>";
    echo "<td><a href='$share_url' target='_blank'>লিংক খুলুন</a></td>";
    echo "</tr>";
}
echo "</table>";

// Test a specific link
if ($total_links > 0) {
    echo "<h3>টেস্ট লিংক:</h3>";
    $test_link = mysqli_query($conn, "SELECT link_token FROM shared_links WHERE is_active = 1 LIMIT 1");
    $test_token = mysqli_fetch_assoc($test_link)['link_token'];
    $test_url = SITE_URL . '/share.php?token=' . $test_token;
    
    echo "<p>টেস্ট লিংক: <a href='$test_url' target='_blank'>$test_url</a></p>";
    
    // Test the link programmatically
    echo "<h3>প্রোগ্রাম্যাটিক টেস্ট:</h3>";
    
    // Simulate the share.php logic
    $token = $test_token;
    $query = "SELECT sl.*, 
                     CASE 
                         WHEN sl.content_type = 'movie' THEN m.title
                         WHEN sl.content_type = 'tvshow' THEN t.title
                     END as content_title,
                     CASE 
                         WHEN sl.content_type = 'movie' THEN m.description
                         WHEN sl.content_type = 'tvshow' THEN t.description
                     END as content_description,
                     CASE 
                         WHEN sl.content_type = 'movie' THEN m.poster
                         WHEN sl.content_type = 'tvshow' THEN t.poster
                     END as content_poster,
                     CASE 
                         WHEN sl.content_type = 'movie' THEN m.banner
                         WHEN sl.content_type = 'tvshow' THEN t.banner
                     END as content_banner,
                     CASE 
                         WHEN sl.content_type = 'movie' THEN m.rating
                         WHEN sl.content_type = 'tvshow' THEN t.rating
                     END as content_rating,
                     CASE 
                         WHEN sl.content_type = 'movie' THEN m.release_year
                         WHEN sl.content_type = 'tvshow' THEN t.release_year
                     END as content_year
              FROM shared_links sl
              LEFT JOIN movies m ON sl.content_type = 'movie' AND sl.content_id = m.id
              LEFT JOIN tvshows t ON sl.content_type = 'tvshow' AND sl.content_id = t.id
              WHERE sl.link_token = '$token' AND sl.is_active = 1";

    $result = mysqli_query($conn, $query);
    
    if (!$result) {
        echo "<div style='color: red;'>❌ কুয়েরি ফেইল: " . mysqli_error($conn) . "</div>";
    } else {
        $num_rows = mysqli_num_rows($result);
        echo "<div style='color: green;'>✅ কুয়েরি সফল। ফলাফল: $num_rows</div>";
        
        if ($num_rows > 0) {
            $shared_link = mysqli_fetch_assoc($result);
            echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";
            echo "<strong>শেয়ার লিংক তথ্য:</strong><br>";
            echo "টাইটেল: {$shared_link['title']}<br>";
            echo "কন্টেন্ট টাইটেল: {$shared_link['content_title']}<br>";
            echo "কন্টেন্ট টাইপ: {$shared_link['content_type']}<br>";
            echo "এক্সেস কাউন্ট: {$shared_link['access_count']}<br>";
            echo "এক্সেস লিমিট: {$shared_link['access_limit']}<br>";
            echo "মেয়াদ: {$shared_link['expires_at']}<br>";
            echo "</div>";
        } else {
            echo "<div style='color: red;'>❌ কোনো শেয়ার লিংক পাওয়া যায়নি</div>";
        }
    }
}
?> 