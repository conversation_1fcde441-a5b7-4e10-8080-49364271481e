<?php
// Set page title
$page_title = 'Activity Logs';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Process form submissions
$success_message = '';
$error_message = '';

// Check if activity_logs table exists
$check_table_query = "SHOW TABLES LIKE 'activity_logs'";
$check_table_result = mysqli_query($conn, $check_table_query);

if (mysqli_num_rows($check_table_result) == 0) {
    // Create activity_logs table
    $create_table_query = "CREATE TABLE IF NOT EXISTS activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        activity_type VARCHAR(50) NOT NULL,
        description TEXT NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREI<PERSON><PERSON> KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
    )";
    
    if (mysqli_query($conn, $create_table_query)) {
        // Insert sample activity logs
        $sample_activities = [
            [1, "login", "User logged in", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64)"],
            [1, "content_add", "Added new movie: The Avengers", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64)"],
            [2, "login", "User logged in", "***********", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)"],
            [2, "profile_update", "Updated profile information", "***********", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)"],
            [1, "content_edit", "Edited movie: The Avengers", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64)"],
            [3, "registration", "New user registered", "***********", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1)"],
            [1, "settings_update", "Updated site settings", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64)"]
        ];
        
        foreach ($sample_activities as $activity) {
            $user_id = $activity[0];
            $activity_type = $activity[1];
            $description = $activity[2];
            $ip_address = $activity[3];
            $user_agent = $activity[4];
            
            $insert_query = "INSERT INTO activity_logs (user_id, activity_type, description, ip_address, user_agent) 
                           VALUES ($user_id, '$activity_type', '$description', '$ip_address', '$user_agent')";
            mysqli_query($conn, $insert_query);
        }
    } else {
        $error_message = "Error creating activity_logs table: " . mysqli_error($conn);
    }
}

// Clear logs
if (isset($_POST['clear_logs'])) {
    $clear_query = "TRUNCATE TABLE activity_logs";
    
    if (mysqli_query($conn, $clear_query)) {
        $success_message = "All activity logs cleared successfully.";
    } else {
        $error_message = "Error clearing activity logs: " . mysqli_error($conn);
    }
}

// Delete log
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $log_id = (int)$_GET['delete'];
    
    $delete_query = "DELETE FROM activity_logs WHERE id = $log_id";
    
    if (mysqli_query($conn, $delete_query)) {
        $success_message = "Activity log entry deleted successfully.";
    } else {
        $error_message = "Error deleting activity log entry: " . mysqli_error($conn);
    }
}

// Get logs with pagination and filters
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Filters
$user_id_filter = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;
$activity_type_filter = isset($_GET['activity_type']) ? sanitize($_GET['activity_type']) : '';
$date_from_filter = isset($_GET['date_from']) ? sanitize($_GET['date_from']) : '';
$date_to_filter = isset($_GET['date_to']) ? sanitize($_GET['date_to']) : '';

// Build where clause
$where_clauses = [];

if ($user_id_filter > 0) {
    $where_clauses[] = "user_id = $user_id_filter";
}

if (!empty($activity_type_filter)) {
    $where_clauses[] = "activity_type = '$activity_type_filter'";
}

if (!empty($date_from_filter)) {
    $where_clauses[] = "created_at >= '$date_from_filter 00:00:00'";
}

if (!empty($date_to_filter)) {
    $where_clauses[] = "created_at <= '$date_to_filter 23:59:59'";
}

$where_clause = '';
if (!empty($where_clauses)) {
    $where_clause = "WHERE " . implode(" AND ", $where_clauses);
}

// Get total logs count
$count_query = "SELECT COUNT(*) as total FROM activity_logs $where_clause";
$count_result = mysqli_query($conn, $count_query);
$total_logs = mysqli_fetch_assoc($count_result)['total'];
$total_pages = ceil($total_logs / $limit);

// Get logs
$logs_query = "SELECT al.*, u.username 
              FROM activity_logs al 
              LEFT JOIN users u ON al.user_id = u.id 
              $where_clause 
              ORDER BY al.created_at DESC 
              LIMIT $offset, $limit";
$logs_result = mysqli_query($conn, $logs_query);

// Get users for filter dropdown
$users_query = "SELECT id, username FROM users ORDER BY username";
$users_result = mysqli_query($conn, $users_query);

// Get activity types for filter dropdown
$activity_types_query = "SELECT DISTINCT activity_type FROM activity_logs ORDER BY activity_type";
$activity_types_result = mysqli_query($conn, $activity_types_query);

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title d-flex align-items-center">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>অ্যাক্টিভিটি লগ</h1>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <?php if($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>
        
        <?php if($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="fw-bold text-primary mb-0">অ্যাক্টিভিটি লগ ফিল্টার</h6>
                    <form method="POST" action="" class="d-inline">
                        <button type="submit" name="clear_logs" class="btn btn-sm btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি সমস্ত অ্যাক্টিভিটি লগ মুছতে চান?');">
                            <i class="fas fa-trash me-1"></i> সব লগ মুছুন
                        </button>
                    </form>
                </div>
            </div>
            <div class="card-body">
                <form method="GET" action="">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="user_id" class="form-label">ব্যবহারকারী</label>
                                <select class="form-select" id="user_id" name="user_id">
                                    <option value="">সব ব্যবহারকারী</option>
                                    <?php while($user = mysqli_fetch_assoc($users_result)): ?>
                                    <option value="<?php echo $user['id']; ?>" <?php echo $user_id_filter == $user['id'] ? 'selected' : ''; ?>>
                                        <?php echo $user['username']; ?>
                                    </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="activity_type" class="form-label">অ্যাক্টিভিটি টাইপ</label>
                                <select class="form-select" id="activity_type" name="activity_type">
                                    <option value="">সব টাইপ</option>
                                    <?php while($activity_type = mysqli_fetch_assoc($activity_types_result)): ?>
                                    <option value="<?php echo $activity_type['activity_type']; ?>" <?php echo $activity_type_filter == $activity_type['activity_type'] ? 'selected' : ''; ?>>
                                        <?php echo ucfirst(str_replace('_', ' ', $activity_type['activity_type'])); ?>
                                    </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="date_from" class="form-label">তারিখ থেকে</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from_filter; ?>">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="date_to" class="form-label">তারিখ পর্যন্ত</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to_filter; ?>">
                            </div>
                        </div>
                    </div>
                    <div class="d-flex">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter me-1"></i> ফিল্টার
                        </button>
                        <?php if(!empty($user_id_filter) || !empty($activity_type_filter) || !empty($date_from_filter) || !empty($date_to_filter)): ?>
                        <a href="activity_logs.php" class="btn btn-secondary ms-2">
                            <i class="fas fa-times me-1"></i> রিসেট
                        </a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h6 class="fw-bold text-primary mb-0">অ্যাক্টিভিটি লগ</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead>
                            <tr>
                                <th>আইডি</th>
                                <th>ব্যবহারকারী</th>
                                <th>অ্যাক্টিভিটি টাইপ</th>
                                <th>বিবরণ</th>
                                <th>আইপি ঠিকানা</th>
                                <th>তারিখ</th>
                                <th>অ্যাকশন</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if(mysqli_num_rows($logs_result) > 0): ?>
                                <?php while($log = mysqli_fetch_assoc($logs_result)): ?>
                                <tr>
                                    <td><?php echo $log['id']; ?></td>
                                    <td><?php echo $log['username'] ?? 'Unknown'; ?></td>
                                    <td>
                                        <?php 
                                        $activity_type = $log['activity_type'];
                                        $badge_class = 'bg-secondary';
                                        
                                        if ($activity_type == 'login' || $activity_type == 'registration') {
                                            $badge_class = 'bg-success';
                                        } elseif ($activity_type == 'logout') {
                                            $badge_class = 'bg-warning';
                                        } elseif (strpos($activity_type, 'add') !== false || strpos($activity_type, 'create') !== false) {
                                            $badge_class = 'bg-primary';
                                        } elseif (strpos($activity_type, 'edit') !== false || strpos($activity_type, 'update') !== false) {
                                            $badge_class = 'bg-info';
                                        } elseif (strpos($activity_type, 'delete') !== false || strpos($activity_type, 'remove') !== false) {
                                            $badge_class = 'bg-danger';
                                        }
                                        ?>
                                        <span class="badge <?php echo $badge_class; ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $activity_type)); ?>
                                        </span>
                                    </td>
                                    <td><?php echo $log['description']; ?></td>
                                    <td><?php echo $log['ip_address']; ?></td>
                                    <td><?php echo date('Y-m-d H:i:s', strtotime($log['created_at'])); ?></td>
                                    <td>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-primary view-details" data-bs-toggle="modal" data-bs-target="#logDetailsModal" data-log-id="<?php echo $log['id']; ?>" data-username="<?php echo htmlspecialchars($log['username'] ?? 'Unknown'); ?>" data-activity-type="<?php echo htmlspecialchars($log['activity_type']); ?>" data-description="<?php echo htmlspecialchars($log['description']); ?>" data-ip-address="<?php echo htmlspecialchars($log['ip_address']); ?>" data-user-agent="<?php echo htmlspecialchars($log['user_agent']); ?>" data-created-at="<?php echo date('Y-m-d H:i:s', strtotime($log['created_at'])); ?>">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <a href="activity_logs.php?delete=<?php echo $log['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই লগ এন্ট্রি মুছতে চান?');">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="7" class="text-center">কোন অ্যাক্টিভিটি লগ পাওয়া যায়নি।</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <?php if($total_pages > 1): ?>
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo !empty($user_id_filter) ? '&user_id=' . $user_id_filter : ''; ?><?php echo !empty($activity_type_filter) ? '&activity_type=' . $activity_type_filter : ''; ?><?php echo !empty($date_from_filter) ? '&date_from=' . $date_from_filter : ''; ?><?php echo !empty($date_to_filter) ? '&date_to=' . $date_to_filter : ''; ?>" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        <?php for($i = 1; $i <= $total_pages; $i++): ?>
                        <li class="page-item <?php echo $page == $i ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($user_id_filter) ? '&user_id=' . $user_id_filter : ''; ?><?php echo !empty($activity_type_filter) ? '&activity_type=' . $activity_type_filter : ''; ?><?php echo !empty($date_from_filter) ? '&date_from=' . $date_from_filter : ''; ?><?php echo !empty($date_to_filter) ? '&date_to=' . $date_to_filter : ''; ?>"><?php echo $i; ?></a>
                        </li>
                        <?php endfor; ?>
                        <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo !empty($user_id_filter) ? '&user_id=' . $user_id_filter : ''; ?><?php echo !empty($activity_type_filter) ? '&activity_type=' . $activity_type_filter : ''; ?><?php echo !empty($date_from_filter) ? '&date_from=' . $date_from_filter : ''; ?><?php echo !empty($date_to_filter) ? '&date_to=' . $date_to_filter : ''; ?>" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Log Details Modal -->
<div class="modal fade" id="logDetailsModal" tabindex="-1" aria-labelledby="logDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="logDetailsModalLabel">অ্যাক্টিভিটি লগ বিস্তারিত</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label fw-bold">আইডি</label>
                    <p id="log-id"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">ব্যবহারকারী</label>
                    <p id="log-username"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">অ্যাক্টিভিটি টাইপ</label>
                    <p id="log-activity-type"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">বিবরণ</label>
                    <p id="log-description"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">আইপি ঠিকানা</label>
                    <p id="log-ip-address"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">ব্রাউজার</label>
                    <p id="log-user-agent"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">তারিখ</label>
                    <p id="log-created-at"></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বন্ধ করুন</button>
            </div>
        </div>
    </div>
</div>

<script>
// Log details modal
document.addEventListener('DOMContentLoaded', function() {
    const viewButtons = document.querySelectorAll('.view-details');
    
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const logId = this.getAttribute('data-log-id');
            const username = this.getAttribute('data-username');
            const activityType = this.getAttribute('data-activity-type');
            const description = this.getAttribute('data-description');
            const ipAddress = this.getAttribute('data-ip-address');
            const userAgent = this.getAttribute('data-user-agent');
            const createdAt = this.getAttribute('data-created-at');
            
            document.getElementById('log-id').textContent = logId;
            document.getElementById('log-username').textContent = username;
            document.getElementById('log-activity-type').textContent = activityType;
            document.getElementById('log-description').textContent = description;
            document.getElementById('log-ip-address').textContent = ipAddress;
            document.getElementById('log-user-agent').textContent = userAgent;
            document.getElementById('log-created-at').textContent = createdAt;
        });
    });
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
