<?php
require_once 'config.php';

// Check if user is premium using the isPremium() function
// This will update the user's premium status based on subscriptions
$is_premium = false;
if (isLoggedIn()) {
    // Always check from database to ensure latest status
    $is_premium = isPremium();
}

// SEO Meta Tags (if variables are set from individual pages)
$default_title = SITE_NAME . " - Stream Movies & TV Shows";
$default_description = "Watch your favorite movies and TV shows online";
$default_keywords = "movies, tv shows, streaming, download, watch online, cinepix";
$default_image = SITE_URL . "/images/logo.png";

$final_title = isset($page_title) ? $page_title : $default_title;
$final_description = isset($page_description) ? $page_description : $default_description;
$final_keywords = isset($page_keywords) ? $page_keywords : $default_keywords;
$final_image = isset($page_image) ? $page_image : $default_image;
$final_canonical = isset($canonical_url) ? $canonical_url : "https://" . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="site-url" content="<?php echo SITE_URL; ?>">
    <meta name="theme-color" content="#e50914">

    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-3FELYTM6F3"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-3FELYTM6F3');
    </script>

    <!-- SEO Meta Tags -->
    <title><?php echo $final_title; ?></title>
    <meta name="description" content="<?php echo $final_description; ?>">
    <meta name="keywords" content="<?php echo $final_keywords; ?>">
    <meta name="author" content="<?php echo SITE_NAME; ?>">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="<?php echo $final_canonical; ?>">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $final_title; ?>">
    <meta property="og:description" content="<?php echo $final_description; ?>">
    <meta property="og:image" content="<?php echo $final_image; ?>">
    <meta property="og:url" content="<?php echo $final_canonical; ?>">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $final_title; ?>">
    <meta name="twitter:description" content="<?php echo $final_description; ?>">
    <meta name="twitter:image" content="<?php echo $final_image; ?>">

    <!-- PWA Support -->
    <link rel="manifest" href="<?php echo SITE_URL; ?>/manifest.json">
    <link rel="apple-touch-icon" href="<?php echo SITE_URL; ?>/pwa-icons/icon-192x192.jpg">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Owl Carousel -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css">
    <!-- Custom CSS with cache busting -->
    <?php if (file_exists(__DIR__ . '/../css/style.css')): ?>
    <?php if (file_exists(__DIR__ . '/../css/style.css')): ?>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/style.css?v=<?php echo filemtime(__DIR__ . '/../css/style.css'); ?>">
    <?php endif; ?>
    <?php endif; ?>
    <?php if (file_exists(__DIR__ . '/../css/design-improvements.css')): ?>
    <?php if (file_exists(__DIR__ . '/../css/design-improvements.css')): ?>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/design-improvements.css?v=<?php echo filemtime(__DIR__ . '/../css/design-improvements.css'); ?>">
    <?php endif; ?>
    <?php endif; ?>
    <?php if (file_exists(__DIR__ . '/../css/tags.css')): ?>
    <?php if (file_exists(__DIR__ . '/../css/tags.css')): ?>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/tags.css?v=<?php echo filemtime(__DIR__ . '/../css/tags.css'); ?>">
    <?php endif; ?>
    <?php endif; ?>
    <?php if (file_exists(__DIR__ . '/../css/mobile-app.css')): ?>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/mobile-app.css?v=<?php echo filemtime(__DIR__ . '/../css/mobile-app.css'); ?>">
    <?php endif; ?>
    <!-- Fallback CSS if the above fails -->
    <style>
        /* Basic fallback styles in case external CSS fails to load */
        body { background-color: #000; color: #fff; font-family: Arial, sans-serif; }
        .navbar { background-color: #000; padding: 10px 0; }
        .navbar-brand { font-size: 24px; font-weight: bold; }
        .text-danger { color: #e50914 !important; }
        .text-light { color: #fff !important; }
    </style>
    <?php
    // Add page-specific CSS files with cache busting
    $current_page = basename($_SERVER['PHP_SELF']);
    if ($current_page === 'profile.php') {
        $profile_css_path = __DIR__ . '/../css/profile.css';
        $profile_version = file_exists($profile_css_path) ? filemtime($profile_css_path) : time();
        echo '<link rel="stylesheet" href="' . SITE_URL . '/css/profile.css?v=' . $profile_version . '">';
    }
    if ($current_page === 'payment_history.php') {
        $payment_css_path = __DIR__ . '/../css/payment_history.css';
        $payment_version = file_exists($payment_css_path) ? filemtime($payment_css_path) : time();
        echo '<link rel="stylesheet" href="' . SITE_URL . '/css/payment_history.css?v=' . $payment_version . '">';
    }
    if ($current_page === 'movie_details.php') {
        $movie_css_path = __DIR__ . '/../css/movie-details.css';
        $movie_version = file_exists($movie_css_path) ? filemtime($movie_css_path) : time();
        echo '<link rel="stylesheet" href="' . SITE_URL . '/css/movie-details.css?v=' . $movie_version . '">';
    }
    if ($current_page === 'tvshow_details.php') {
        $tvshow_css_path = __DIR__ . '/../css/tvshow-details.css';
        $tvshow_version = file_exists($tvshow_css_path) ? filemtime($tvshow_css_path) : time();
        echo '<link rel="stylesheet" href="' . SITE_URL . '/css/tvshow-details.css?v=' . $tvshow_version . '">';
    }
    if ($current_page === 'movies.php') {
        $movies_css_path = __DIR__ . '/../css/movies.css';
        $movies_version = file_exists($movies_css_path) ? filemtime($movies_css_path) : time();
        echo '<link rel="stylesheet" href="' . SITE_URL . '/css/movies.css?v=' . $movies_version . '">';
    }
    ?>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="<?php echo SITE_URL; ?>">
                <span class="text-danger fw-bold">Cine</span><span class="text-light">Pix</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>/movies.php">Movies</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>/tvshows.php">TV Shows</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>/premium.php">
                            Premium
                            <?php if(!$is_premium): ?>
                            <span class="badge bg-danger">New</span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="https://nim-server2-haze-cc88.cinepixserver00.workers.dev/1:/january%20to%20march/app/CinePix.apk" id="navAppDownload">
                            <i class="fab fa-android text-success"></i> অ্যাপ ডাউনলোড
                        </a>
                    </li>
                    <?php if(isLoggedIn()): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>/watchlist.php">My List</a>
                    </li>
                    <?php endif; ?>
                </ul>
                <div class="d-flex align-items-center">
                    <form class="d-flex me-3" action="<?php echo SITE_URL; ?>/search.php" method="GET">
                        <input class="form-control me-2 search-input" type="search" name="query" placeholder="Search" aria-label="Search">
                        <button class="btn btn-outline-danger" type="submit"><i class="fas fa-search"></i></button>
                    </form>
                    <?php if(isLoggedIn()): ?>
                        <div class="dropdown">
                            <a class="nav-link dropdown-toggle text-white" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $_SESSION['profile_image']; ?>" alt="Profile" class="rounded-circle profile-img-small me-2">
                                <?php echo $_SESSION['username']; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/profile.php">Profile</a></li>
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/live_chat.php">
                                    <i class="fas fa-comment-dots text-primary"></i> Live Chat
                                </a></li>
                                <?php if($is_premium): ?>
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/premium_content.php">
                                    <i class="fas fa-crown text-warning"></i> Premium Content
                                </a></li>
                                <?php else: ?>
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/premium.php">
                                    <span class="badge bg-danger me-1">New</span> Upgrade to Premium
                                </a></li>
                                <?php endif; ?>
                                <?php if(isAdmin()): ?>
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/admin/">Admin Panel</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/logout.php">Logout</a></li>
                            </ul>
                        </div>
                    <?php else: ?>
                        <a href="<?php echo SITE_URL; ?>/login.php" class="btn btn-outline-light me-2">Login</a>
                        <a href="<?php echo SITE_URL; ?>/register.php" class="btn btn-danger">Sign Up</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>
    <!-- Main Content -->
    <main>
