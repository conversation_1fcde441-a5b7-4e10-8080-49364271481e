<?php
// Include direct config file
require_once '../direct_config.php';

// Get TV show ID from query parameter
$tvshow_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($tvshow_id <= 0) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'TV show ID is required',
        'data' => null
    ]);
    exit;
}

// Get TV show details without status filter
$query = "SELECT t.*, c.name as category_name
          FROM tvshows t
          LEFT JOIN categories c ON t.category_id = c.id
          WHERE t.id = $tvshow_id";

$result = mysqli_query($conn, $query);

if (!$result) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to fetch TV show details: ' . mysqli_error($conn),
        'data' => null
    ]);
    exit;
}

if (mysqli_num_rows($result) === 0) {
    http_response_code(404);
    echo json_encode([
        'success' => false,
        'message' => 'TV show not found',
        'data' => null
    ]);
    exit;
}

$tvshow = mysqli_fetch_assoc($result);

// Get seasons and episodes count
$seasons_query = "SELECT DISTINCT season_number FROM episodes
                 WHERE tvshow_id = $tvshow_id
                 ORDER BY season_number ASC";

$seasons_result = mysqli_query($conn, $seasons_query);
$seasons = [];

if ($seasons_result) {
    while ($season = mysqli_fetch_assoc($seasons_result)) {
        $season_number = (int)$season['season_number'];

        // Get episodes count for this season
        $episodes_count_query = "SELECT COUNT(*) as count FROM episodes
                               WHERE tvshow_id = $tvshow_id AND season_number = $season_number";

        $episodes_count_result = mysqli_query($conn, $episodes_count_query);
        $episodes_count = mysqli_fetch_assoc($episodes_count_result)['count'];

        $seasons[] = [
            'season_number' => $season_number,
            'episodes_count' => (int)$episodes_count
        ];
    }
}

// Get related TV shows without status filter
$related_query = "SELECT t.*, c.name as category_name
                 FROM tvshows t
                 LEFT JOIN categories c ON t.category_id = c.id
                 WHERE t.category_id = {$tvshow['category_id']}
                 AND t.id != $tvshow_id
                 ORDER BY RAND()
                 LIMIT 10";

$related_result = mysqli_query($conn, $related_query);
$related_tvshows = [];

if ($related_result) {
    while ($related = mysqli_fetch_assoc($related_result)) {
        $related_tvshows[] = [
            'id' => (int)$related['id'],
            'title' => $related['title'],
            'poster' => $related['poster'] ? (strpos($related['poster'], 'http') === 0 ? $related['poster'] : SITE_URL . '/uploads/' . $related['poster']) : '',
            'start_year' => (int)$related['start_year'],
            'seasons' => (int)$related['seasons'],
            'rating' => (float)$related['rating'],
            'premium_only' => (bool)$related['premium_only']
        ];
    }
}

// Return TV show details
http_response_code(200);
echo json_encode([
    'success' => true,
    'message' => 'Success',
    'data' => [
        'tvshow' => [
            'id' => (int)$tvshow['id'],
            'title' => $tvshow['title'],
            'description' => $tvshow['description'],
            'start_year' => (int)$tvshow['start_year'],
            'end_year' => $tvshow['end_year'] ? (int)$tvshow['end_year'] : null,
            'seasons' => (int)$tvshow['seasons'],
            'poster' => $tvshow['poster'] ? (strpos($tvshow['poster'], 'http') === 0 ? $tvshow['poster'] : SITE_URL . '/uploads/' . $tvshow['poster']) : '',
            'banner' => $tvshow['banner'] ? (strpos($tvshow['banner'], 'http') === 0 ? $tvshow['banner'] : SITE_URL . '/uploads/' . $tvshow['banner']) : '',
            'trailer_url' => $tvshow['trailer_url'],
            'rating' => (float)$tvshow['rating'],
            'category_id' => (int)$tvshow['category_id'],
            'category_name' => $tvshow['category_name'],
            'featured' => (bool)$tvshow['featured'],
            'premium_only' => (bool)$tvshow['premium_only'],
            'status' => $tvshow['status']
        ],
        'seasons' => $seasons,
        'related_tvshows' => $related_tvshows
    ]
]);
?>
