<?php
// Simple API test file - no authentication required

// Get the base URL
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
$host = $_SERVER['HTTP_HOST'];
$base_url = $protocol . $host;

echo "<h1>CinePix API Test</h1>";

// Test API endpoints
$endpoints = [
    'api/v1/direct_movies.php',
    'api/v1/direct_tvshows.php',
    'api/v1/direct_movie_details.php?id=318', // Using a valid movie ID from your database
    'api/v1/direct_tvshow_details.php?id=84', // Using a valid TV show ID from your database
    'api/v1/direct_tvshow_episodes.php?id=84&season=1', // Using a valid TV show ID from your database
    'api/v1/direct_search.php?q=test'
];

echo "<h2>API Endpoint Tests</h2>";
echo "<table border='1' cellpadding='10'>";
echo "<tr><th>Endpoint</th><th>Status</th><th>Response</th></tr>";

foreach ($endpoints as $endpoint) {
    echo "<tr>";
    echo "<td>$endpoint</td>";

    // Get the contents of the endpoint
    $url = $base_url . '/' . $endpoint;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($http_code == 200) {
        $json = json_decode($response, true);
        if ($json && isset($json['success']) && $json['success'] === true) {
            echo "<td style='background-color: #dff0d8;'>✓ Success</td>";

            // Check specific data
            if (strpos($endpoint, 'direct_movies.php') !== false) {
                $count = count($json['data']['movies'] ?? []);
                echo "<td>Found $count movies</td>";
            } elseif (strpos($endpoint, 'direct_tvshows.php') !== false) {
                $count = count($json['data']['tvshows'] ?? []);
                echo "<td>Found $count TV shows</td>";
            } elseif (strpos($endpoint, 'direct_movie_details.php') !== false) {
                $links_count = count($json['data']['download_links'] ?? []);
                echo "<td>Movie has $links_count download links</td>";
            } elseif (strpos($endpoint, 'direct_tvshow_details.php') !== false) {
                $seasons_count = count($json['data']['seasons'] ?? []);
                echo "<td>TV show has $seasons_count seasons</td>";
            } elseif (strpos($endpoint, 'direct_tvshow_episodes.php') !== false) {
                $episodes_count = count($json['data']['episodes'] ?? []);
                echo "<td>Season has $episodes_count episodes</td>";
            } elseif (strpos($endpoint, 'direct_search.php') !== false) {
                $movies_count = count($json['data']['movies'] ?? []);
                $tvshows_count = count($json['data']['tvshows'] ?? []);
                echo "<td>Found $movies_count movies and $tvshows_count TV shows</td>";
            } else {
                echo "<td>Success</td>";
            }
        } else {
            echo "<td style='background-color: #f2dede;'>✗ Failed</td>";
            echo "<td>Invalid response format</td>";
        }
    } else {
        echo "<td style='background-color: #f2dede;'>✗ Failed ($http_code)</td>";
        echo "<td>Request failed</td>";
    }

    echo "</tr>";
}

echo "</table>";

// Flutter app API constants
echo "<h2>Flutter App API Constants</h2>";
echo "<pre>";
echo "class ApiConstants {
  // API Base URL
  static const String baseUrl = '$base_url/api/v1';

  // API Endpoints
  static const String configEndpoint = '/direct_config.php';
  static const String loginEndpoint = '/direct_login.php';
  static const String registerEndpoint = '/direct_register.php';
  static const String moviesEndpoint = '/direct_movies.php';
  static const String movieDetailsEndpoint = '/direct_movie_details.php';
  static const String tvShowsEndpoint = '/direct_tvshows.php';
  static const String tvShowDetailsEndpoint = '/direct_tvshow_details.php';
  static const String tvShowEpisodesEndpoint = '/direct_tvshow_episodes.php';
  static const String searchEndpoint = '/direct_search.php';
  static const String categoriesEndpoint = '/direct_categories.php';
  static const String profileEndpoint = '/direct_profile.php';
}";
echo "</pre>";

// Show raw response for debugging
echo "<h2>Raw API Responses (for debugging)</h2>";

foreach ($endpoints as $endpoint) {
    echo "<h3>$endpoint</h3>";

    $url = $base_url . '/' . $endpoint;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    curl_close($ch);

    echo "<div style='max-height: 300px; overflow: auto; background-color: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
    echo "<pre>" . htmlspecialchars(substr($response, 0, 2000)) . (strlen($response) > 2000 ? "..." : "") . "</pre>";
    echo "</div>";
}
?>
