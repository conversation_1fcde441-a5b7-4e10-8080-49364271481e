<?php
// Set page title
$page_title = 'Add Movie';

require_once '../includes/config.php';

// Check if language column exists in movies table
$check_language_query = "SHOW COLUMNS FROM movies LIKE 'language'";
$check_language_result = mysqli_query($conn, $check_language_query);

if (mysqli_num_rows($check_language_result) == 0) {
    // Add language column if it doesn't exist
    $add_language_query = "ALTER TABLE movies ADD COLUMN language VARCHAR(50) DEFAULT NULL";
    mysqli_query($conn, $add_language_query);
}

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Process form submission
$success_message = '';
$error_message = '';

if (isset($_POST['add_movie'])) {
    $title = sanitize($_POST['title']);
    $category_id = (int)$_POST['category_id'];
    $release_year = (int)$_POST['release_year'];
    $duration = sanitize($_POST['duration']);
    $quality = sanitize($_POST['quality']);
    $language = sanitize($_POST['language']);
    $rating = (float)$_POST['rating'];
    $description = sanitize($_POST['description']);
    $trailer_url = sanitize($_POST['trailer_url']);
    $premium_only = isset($_POST['premium_only']) ? 1 : 0;
    $featured = isset($_POST['featured']) ? 1 : 0;

    // Validate required fields
    if (empty($title) || empty($category_id) || empty($release_year)) {
        $error_message = 'Title, category, and release year are required.';
    } else {
        // Handle poster upload
        $poster = '';
        if (!empty($_FILES['poster']['name'])) {
            $upload_dir = '../uploads/';
            $file_ext = pathinfo($_FILES['poster']['name'], PATHINFO_EXTENSION);
            $poster = 'movie_' . time() . '.' . $file_ext;
            $upload_path = $upload_dir . $poster;

            // Check file type
            $allowed_types = ['jpg', 'jpeg', 'png', 'webp'];
            if (!in_array(strtolower($file_ext), $allowed_types)) {
                $error_message = 'Invalid poster file type. Allowed types: JPG, JPEG, PNG, WEBP.';
            } else if ($_FILES['poster']['size'] > 2097152) { // 2MB
                $error_message = 'Poster file size must be less than 2MB.';
            } else if (!move_uploaded_file($_FILES['poster']['tmp_name'], $upload_path)) {
                $error_message = 'Error uploading poster file.';
            }
        }

        // If no errors, insert movie
        if (empty($error_message)) {
            $query = "INSERT INTO movies (title, category_id, release_year, duration, quality, language, rating, description, trailer_url, poster, premium_only, featured, created_at)
                     VALUES ('$title', $category_id, $release_year, '$duration', '$quality', '$language', $rating, '$description', '$trailer_url', '$poster', $premium_only, $featured, NOW())";

            if (mysqli_query($conn, $query)) {
                $movie_id = mysqli_insert_id($conn);
                $success_message = 'Movie added successfully.';

                // Redirect to edit page
                redirect("edit_movie.php?id=$movie_id&success=added");
            } else {
                $error_message = 'Error adding movie: ' . mysqli_error($conn);
            }
        }
    }
}

// Get categories
$categories_query = "SELECT * FROM categories ORDER BY name";
$categories_result = mysqli_query($conn, $categories_query);

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">
            <!-- Sidebar Toggle (Topbar) -->
            <button id="sidebarToggle" class="btn btn-link d-md-none rounded-circle mr-3">
                <i class="fa fa-bars"></i>
            </button>

            <!-- Topbar Title -->
            <div class="d-none d-sm-inline-block mr-auto ml-md-3 my-2 my-md-0 mw-100">
                <h1 class="h3 mb-0 text-gray-800">Add New Movie</h1>
            </div>
        </nav>
    </div>

    <div class="container-fluid">
        <?php if($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Movie Information</h6>
            </div>
            <div class="card-body">
                <form method="POST" action="" enctype="multipart/form-data" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" required>
                                <div class="invalid-feedback">
                                    Please enter a title.
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="category_id" class="form-label">Category <span class="text-danger">*</span></label>
                                        <select class="form-select" id="category_id" name="category_id" required>
                                            <option value="">Select Category</option>
                                            <?php while($category = mysqli_fetch_assoc($categories_result)): ?>
                                            <option value="<?php echo $category['id']; ?>"><?php echo $category['name']; ?></option>
                                            <?php endwhile; ?>
                                        </select>
                                        <div class="invalid-feedback">
                                            Please select a category.
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="release_year" class="form-label">Release Year <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="release_year" name="release_year" min="1900" max="<?php echo date('Y') + 1; ?>" required>
                                        <div class="invalid-feedback">
                                            Please enter a valid release year.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="duration" class="form-label">Duration</label>
                                        <input type="text" class="form-control" id="duration" name="duration" placeholder="e.g., 2h 30m">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="quality" class="form-label">Quality</label>
                                        <select class="form-select" id="quality" name="quality">
                                            <option value="">Select Quality</option>
                                            <option value="CAM">CAM</option>
                                            <option value="SD">SD</option>
                                            <option value="HD">HD</option>
                                            <option value="Full HD">Full HD</option>
                                            <option value="4K">4K</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="language" class="form-label">Language</label>
                                        <input type="text" class="form-control" id="language" name="language">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="rating" class="form-label">Rating (0-10)</label>
                                <input type="number" class="form-control" id="rating" name="rating" min="0" max="10" step="0.1" value="0">
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="5"></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="trailer_url" class="form-label">Trailer URL (YouTube)</label>
                                <input type="url" class="form-control" id="trailer_url" name="trailer_url" placeholder="https://www.youtube.com/watch?v=...">
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="poster" class="form-label">Poster Image</label>
                                <input type="file" class="form-control" id="poster" name="poster" accept="image/jpeg,image/png,image/webp">
                                <div class="form-text">Recommended size: 300x450px. Max size: 2MB.</div>
                                <div class="mt-3">
                                    <div class="poster-preview-container">
                                        <img id="poster-preview" src="assets/img/default-poster.jpg" alt="Poster Preview" class="img-thumbnail">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="premium_only" name="premium_only">
                                    <label class="form-check-label" for="premium_only">Premium Only</label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="featured" name="featured">
                                    <label class="form-check-label" for="featured">Featured</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <a href="movies.php" class="btn btn-secondary">Cancel</a>
                        <button type="submit" name="add_movie" class="btn btn-primary">
                            <i class="fas fa-plus-circle me-2"></i>Add Movie
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for poster preview -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const posterInput = document.getElementById('poster');
    const posterPreview = document.getElementById('poster-preview');

    posterInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                posterPreview.src = e.target.result;
            }

            reader.readAsDataURL(this.files[0]);
        }
    });
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
