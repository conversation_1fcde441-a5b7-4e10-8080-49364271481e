/* ===== CinePix Admin Panel - Modern Dark Theme ===== */

/* Root Variables */
:root {
    --primary-color: #e50914;
    --primary-dark: #b8070f;
    --secondary-color: #564d4d;
    --dark-bg: #0f0f0f;
    --darker-bg: #000000;
    --card-bg: #1a1a1a;
    --border-color: #333333;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #808080;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --sidebar-width: 280px;
    --topbar-height: 70px;
    --border-radius: 12px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, var(--dark-bg) 0%, var(--darker-bg) 100%);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.admin-body {
    background: var(--dark-bg);
    min-height: 100vh;
}

/* Loading Spinner */
.loading-spinner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* Top Navigation Bar */
.topbar {
    height: var(--topbar-height);
    background: linear-gradient(90deg, var(--darker-bg) 0%, var(--dark-bg) 100%) !important;
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
    z-index: 1030;
}

.topbar .navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    text-decoration: none;
}

.brand-text {
    line-height: 1.2;
}

/* Search Container */
.search-container {
    max-width: 500px;
}

.search-container .form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: 25px;
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.search-container .form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25);
    color: var(--text-primary);
}

.search-container .input-group-text {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--border-color);
    border-radius: 25px 0 0 25px;
}

/* Dropdown Menus */
.dropdown-menu {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    min-width: 280px;
}

.dropdown-item {
    color: var(--text-primary);
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.dropdown-item:hover,
.dropdown-item:focus {
    background: rgba(229, 9, 20, 0.1);
    color: var(--text-primary);
}

.dropdown-header {
    color: var(--text-secondary);
    font-weight: 600;
    padding: 0.75rem 1rem 0.5rem;
}

.dropdown-divider {
    border-color: var(--border-color);
}

/* Notification & Message Dropdowns */
.notification-dropdown,
.message-dropdown {
    max-height: 400px;
    overflow-y: auto;
}

.user-dropdown {
    min-width: 220px;
}

/* Admin Container */
.admin-container {
    display: flex;
    min-height: calc(100vh - var(--topbar-height));
    margin-top: var(--topbar-height);
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(180deg, var(--card-bg) 0%, var(--darker-bg) 100%);
    border-right: 1px solid var(--border-color);
    position: fixed;
    top: var(--topbar-height);
    left: 0;
    height: calc(100vh - var(--topbar-height));
    overflow-y: auto;
    transition: var(--transition);
    z-index: 1020;
}

.sidebar-content {
    padding: 1.5rem 0;
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Sidebar Header */
.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.5rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
}

.sidebar-brand {
    display: flex;
    align-items: center;
    font-size: 1.25rem;
    font-weight: 700;
}

/* User Info */
.sidebar-user-info {
    display: flex;
    align-items: center;
    padding: 0 1.5rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
}

.user-avatar {
    position: relative;
    margin-right: 1rem;
}

.user-avatar img {
    width: 45px;
    height: 45px;
    object-fit: cover;
}

.status-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid var(--card-bg);
}

.status-indicator.online {
    background: var(--success-color);
}

.user-details {
    flex: 1;
}

.user-name {
    font-weight: 600;
    font-size: 0.95rem;
    color: var(--text-primary);
}

.user-role {
    font-size: 0.8rem;
    color: var(--text-muted);
}

/* Sidebar Navigation */
.sidebar-nav {
    list-style: none;
    padding: 0;
    margin: 0;
    flex: 1;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.875rem 1.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
    border-radius: 0;
    position: relative;
}

.nav-link:hover {
    background: rgba(229, 9, 20, 0.1);
    color: var(--text-primary);
    transform: translateX(5px);
}

.nav-link.active {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
    color: var(--text-primary);
    box-shadow: 0 2px 10px rgba(229, 9, 20, 0.3);
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: var(--text-primary);
}

.nav-icon {
    width: 20px;
    margin-right: 0.75rem;
    text-align: center;
}

.nav-text {
    flex: 1;
    font-weight: 500;
}

/* Submenu */
.has-submenu .submenu-arrow {
    margin-left: auto;
    transition: var(--transition);
    font-size: 0.8rem;
}

.has-submenu[aria-expanded="true"] .submenu-arrow {
    transform: rotate(180deg);
}

.submenu {
    background: rgba(0, 0, 0, 0.3);
    border-left: 2px solid var(--primary-color);
    margin-left: 1.5rem;
}

.submenu .nav-link {
    padding: 0.625rem 1rem 0.625rem 2rem;
    font-size: 0.9rem;
}

.submenu .nav-link i {
    width: 16px;
    margin-right: 0.5rem;
    font-size: 0.8rem;
}

/* Sidebar Footer */
.sidebar-footer {
    margin-top: auto;
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.system-info {
    margin-bottom: 1rem;
}

.info-item {
    display: flex;
    align-items: center;
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-bottom: 0.5rem;
}

.info-item i {
    width: 16px;
    margin-right: 0.5rem;
}

.version-info {
    text-align: center;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    padding: 2rem;
    min-height: calc(100vh - var(--topbar-height));
    background: var(--dark-bg);
}

/* Cards */
.card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
}

.card-header {
    background: rgba(229, 9, 20, 0.1);
    border-bottom: 1px solid var(--border-color);
    padding: 1.25rem;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.625rem 1.25rem;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(45deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(229, 9, 20, 0.4);
}

/* Form Controls */
.form-control,
.form-select {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: 8px;
    transition: var(--transition);
}

.form-control:focus,
.form-select:focus {
    background: var(--card-bg);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25);
    color: var(--text-primary);
}

.form-label {
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Tables */
.table {
    color: var(--text-primary);
}

.table-dark {
    --bs-table-bg: var(--card-bg);
    --bs-table-border-color: var(--border-color);
}

.table-hover tbody tr:hover {
    background: rgba(229, 9, 20, 0.1);
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
}

/* Sidebar Overlay for Mobile */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1010;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Footer */
.admin-footer {
    background: var(--card-bg);
    border-top: 1px solid var(--border-color);
    padding: 1rem 0;
    margin-top: auto;
    margin-left: var(--sidebar-width);
}

/* Toast Notifications */
.toast {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.toast-header {
    background: rgba(229, 9, 20, 0.1);
    border-bottom: 1px solid var(--border-color);
}

/* Modal */
.modal-content {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
}

.modal-footer {
    border-top: 1px solid var(--border-color);
}

/* Dashboard Specific Styles */
.page-header {
    margin-bottom: 2rem;
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.page-subtitle {
    font-size: 1rem;
    color: var(--text-muted);
}

.page-actions {
    display: flex;
    gap: 0.5rem;
}

/* Statistics Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: linear-gradient(135deg, var(--card-bg) 0%, rgba(229, 9, 20, 0.1) 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4);
}

.bg-gradient-primary::before {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
}

.bg-gradient-success::before {
    background: linear-gradient(90deg, var(--success-color), #20c997);
}

.bg-gradient-info::before {
    background: linear-gradient(90deg, var(--info-color), #20c997);
}

.bg-gradient-warning::before {
    background: linear-gradient(90deg, var(--warning-color), #fd7e14);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(229, 9, 20, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.stat-content {
    color: var(--text-primary);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.stat-sublabel {
    font-size: 0.8rem;
    color: var(--text-muted);
}

/* Quick Stats */
.quick-stat-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.quick-stat-item:last-child {
    border-bottom: none;
}

.progress {
    background: rgba(255, 255, 255, 0.1);
}

/* Activity Feed */
.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.activity-description {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.8rem;
    color: var(--text-muted);
}

/* Chart Container */
canvas {
    max-height: 300px;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--dark-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}
