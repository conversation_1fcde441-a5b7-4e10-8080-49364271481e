<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CinePix API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #e50914;
            border-bottom: 2px solid #e50914;
            padding-bottom: 10px;
        }
        h2 {
            color: #e50914;
            margin-top: 30px;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .btn {
            display: inline-block;
            background-color: #e50914;
            color: white;
            padding: 10px 15px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 10px;
        }
        .btn:hover {
            background-color: #b20710;
        }
        .endpoint {
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .method {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-weight: bold;
            margin-right: 10px;
            font-size: 14px;
        }
        .get {
            background-color: #61affe;
            color: white;
        }
        .post {
            background-color: #49cc90;
            color: white;
        }
    </style>
</head>
<body>
    <h1>CinePix API</h1>
    
    <div class="card">
        <h2>API Documentation</h2>
        <p>Comprehensive documentation for all API endpoints used by the CinePix mobile app.</p>
        <a href="api_documentation.html" class="btn">View Documentation</a>
    </div>
    
    <div class="card">
        <h2>API Endpoints</h2>
        <p>Direct API endpoints for the CinePix mobile app:</p>
        
        <div class="endpoint">
            <span class="method get">GET</span>
            <a href="direct_config.php" target="_blank">App Configuration</a>
        </div>
        
        <div class="endpoint">
            <span class="method get">GET</span>
            <a href="direct_movies.php" target="_blank">Movies List</a>
        </div>
        
        <div class="endpoint">
            <span class="method get">GET</span>
            <a href="direct_tvshows.php" target="_blank">TV Shows List</a>
        </div>
        
        <div class="endpoint">
            <span class="method get">GET</span>
            <a href="direct_categories.php" target="_blank">Categories</a>
        </div>
        
        <div class="endpoint">
            <span class="method get">GET</span>
            <a href="direct_search.php?q=test" target="_blank">Search (Example)</a>
        </div>
    </div>
    
    <div class="card">
        <h2>App Update Instructions</h2>
        <p>Instructions for updating the CinePix mobile app to use these API endpoints.</p>
        <a href="app_update_instructions.txt" class="btn">View Instructions</a>
    </div>
    
    <div class="card">
        <h2>Test API Connection</h2>
        <p>Test if the API is working correctly:</p>
        <button id="testBtn" class="btn">Test Connection</button>
        <div id="testResult" style="margin-top: 10px;"></div>
    </div>
    
    <script>
        document.getElementById('testBtn').addEventListener('click', function() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = 'Testing connection...';
            
            fetch('direct_config.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultDiv.innerHTML = '<span style="color: green;">✓ API is working correctly!</span>';
                    } else {
                        resultDiv.innerHTML = '<span style="color: red;">✗ API returned an error: ' + data.message + '</span>';
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = '<span style="color: red;">✗ Connection failed: ' + error.message + '</span>';
                });
        });
    </script>
    
    <footer style="margin-top: 50px; text-align: center; color: #777;">
        <p>© 2023 CinePix. All rights reserved.</p>
    </footer>
</body>
</html>
