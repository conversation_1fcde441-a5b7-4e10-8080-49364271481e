/**
 * Modern Episode Design Enhancements
 * Advanced visual effects and responsive design improvements
 */

/* Glass morphism effects */
.episode-item {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.episode-header {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
}

.episode-downloads {
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
}

/* Advanced gradient overlays */
.episode-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(220, 53, 69, 0.02) 0%,
        rgba(232, 62, 140, 0.02) 25%,
        rgba(253, 126, 20, 0.02) 50%,
        rgba(255, 193, 7, 0.02) 75%,
        rgba(220, 53, 69, 0.02) 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 1;
}

.episode-item:hover::after {
    opacity: 1;
}

/* Floating animation for premium badges */
.premium-tag {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

/* Advanced button hover effects */
.download-link {
    position: relative;
    overflow: hidden;
}

.download-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    transition: left 0.6s ease;
}

.download-link:hover::before {
    left: 100%;
}

/* Quality badge enhancements */
.download-quality {
    position: relative;
    overflow: hidden;
}

.download-quality::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3));
    transition: width 0.3s ease;
}

.download-link:hover .download-quality::after {
    width: 100%;
}

/* Server name with icons */
.download-server {
    position: relative;
    padding-left: 25px;
}

.download-server::before {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.9rem;
}

/* Server-specific icons */
.download-server[data-server="Google Drive"]::before {
    content: '📁';
}

.download-server[data-server="MEGA"]::before {
    content: '☁️';
}

.download-server[data-server="MediaFire"]::before {
    content: '🔥';
}

.download-server[data-server="Dropbox"]::before {
    content: '📦';
}

.download-server[data-server="Direct Link"]::before {
    content: '🔗';
}

/* Enhanced size display */
.download-size {
    position: relative;
    font-family: 'Courier New', monospace;
}

.download-size::before {
    content: '💾';
    margin-right: 5px;
}

/* Progressive loading skeleton */
.episode-item.skeleton {
    background: linear-gradient(
        90deg,
        rgba(255,255,255,0.1) 25%,
        rgba(255,255,255,0.2) 50%,
        rgba(255,255,255,0.1) 75%
    );
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Micro-interactions */
.episode-number {
    cursor: pointer;
    user-select: none;
}

.episode-number:active {
    transform: scale(0.95);
}

/* Enhanced focus indicators */
.download-link:focus-visible {
    outline: 3px solid rgba(220, 53, 69, 0.6);
    outline-offset: 3px;
    border-radius: 16px;
}

/* Smooth scrolling for episode navigation */
html {
    scroll-behavior: smooth;
}

/* Episode thumbnail enhancements */
.episode-thumbnail {
    position: relative;
    overflow: hidden;
}

.episode-thumbnail::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        45deg,
        transparent 30%,
        rgba(255,255,255,0.1) 50%,
        transparent 70%
    );
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.episode-item:hover .episode-thumbnail::before {
    transform: translateX(100%);
}

/* Advanced responsive grid */
@media (min-width: 1400px) {
    .download-links {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
}

@media (min-width: 1200px) and (max-width: 1399px) {
    .download-links {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .episode-item {
        border: 2px solid #fff;
        background: #000;
    }
    
    .download-link {
        border: 2px solid #fff;
        background: #333;
    }
    
    .download-quality {
        background: #fff;
        color: #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .episode-item,
    .download-link,
    .episode-number,
    .premium-tag {
        animation: none;
        transition: none;
    }
    
    .episode-item:hover {
        transform: none;
    }
    
    .download-link:hover {
        transform: none;
    }
}

/* Dark theme enhancements */
@media (prefers-color-scheme: dark) {
    .episode-item {
        background: linear-gradient(
            135deg,
            rgba(15, 20, 25, 0.95) 0%,
            rgba(25, 30, 35, 0.85) 100%
        );
    }
    
    .episode-header {
        background: linear-gradient(
            135deg,
            rgba(15, 20, 25, 0.95) 0%,
            rgba(25, 30, 35, 0.9) 100%
        );
    }
    
    .episode-downloads {
        background: linear-gradient(
            135deg,
            rgba(15, 20, 25, 0.8) 0%,
            rgba(25, 30, 35, 0.6) 100%
        );
    }
}

/* Custom scrollbar for episode list */
.episode-list::-webkit-scrollbar {
    width: 8px;
}

.episode-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.episode-list::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    border-radius: 4px;
}

.episode-list::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #e83e8c, #fd7e14);
}

/* Loading states */
.episode-item.loading {
    pointer-events: none;
    opacity: 0.6;
}

.episode-item.loading .download-link {
    background: linear-gradient(
        90deg,
        rgba(255,255,255,0.1) 0%,
        rgba(255,255,255,0.2) 50%,
        rgba(255,255,255,0.1) 100%
    );
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Success states */
.download-link.success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.2), rgba(32, 201, 151, 0.1));
    border-color: rgba(40, 167, 69, 0.5);
}

.download-link.success .download-button {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

/* Error states */
.download-link.error {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.2), rgba(232, 62, 140, 0.1));
    border-color: rgba(220, 53, 69, 0.5);
}

.download-link.error .download-button {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    color: white;
}

/* Accessibility improvements */
.episode-item[aria-expanded="true"] {
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.3);
}

.download-link[aria-pressed="true"] {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.3), rgba(232, 62, 140, 0.2));
}

/* Print optimizations */
@media print {
    .episode-item {
        break-inside: avoid;
        box-shadow: none;
        background: white !important;
        color: black !important;
        border: 1px solid #ccc;
        margin-bottom: 20px;
    }
    
    .download-links,
    .episode-downloads {
        display: none;
    }
    
    .episode-thumbnail {
        display: none;
    }
}
