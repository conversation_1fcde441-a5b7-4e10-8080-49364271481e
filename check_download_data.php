<?php
// Include direct config file
require_once 'api/direct_config.php';

// Check if download_links table exists
$check_table = mysqli_query($conn, "SHOW TABLES LIKE 'download_links'");
if (mysqli_num_rows($check_table) == 0) {
    echo "Table 'download_links' does not exist!";
    exit;
}

// Get sample movie data
echo "<h2>Sample Movies:</h2>";
$movies_query = "SELECT id, title FROM movies LIMIT 5";
$movies_result = mysqli_query($conn, $movies_query);

echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Movie ID</th><th>Title</th><th>Download Links</th></tr>";

while ($movie = mysqli_fetch_assoc($movies_result)) {
    $movie_id = $movie['id'];
    $title = $movie['title'];
    
    // Get download links for this movie
    $links_query = "SELECT * FROM download_links WHERE content_type = 'movie' AND content_id = $movie_id";
    $links_result = mysqli_query($conn, $links_query);
    
    $links_count = mysqli_num_rows($links_result);
    
    echo "<tr>";
    echo "<td>$movie_id</td>";
    echo "<td>$title</td>";
    echo "<td>$links_count links";
    
    if ($links_count > 0) {
        echo "<ul>";
        while ($link = mysqli_fetch_assoc($links_result)) {
            $quality = $link['quality'];
            $url = isset($link['url']) ? $link['url'] : (isset($link['link_url']) ? $link['link_url'] : 'No URL');
            $is_premium = isset($link['is_premium']) ? ($link['is_premium'] ? 'Yes' : 'No') : 'Unknown';
            
            echo "<li>Quality: $quality, Premium: $is_premium<br>URL: " . substr($url, 0, 50) . "...</li>";
        }
        echo "</ul>";
    }
    
    echo "</td></tr>";
}

echo "</table>";

// Get sample TV show data
echo "<h2>Sample TV Shows:</h2>";
$tvshows_query = "SELECT id, title FROM tvshows LIMIT 5";
$tvshows_result = mysqli_query($conn, $tvshows_query);

echo "<table border='1' cellpadding='5'>";
echo "<tr><th>TV Show ID</th><th>Title</th><th>Episodes</th></tr>";

while ($tvshow = mysqli_fetch_assoc($tvshows_result)) {
    $tvshow_id = $tvshow['id'];
    $title = $tvshow['title'];
    
    // Get episodes for this TV show
    $episodes_query = "SELECT id, season_number, episode_number, title FROM episodes WHERE tvshow_id = $tvshow_id LIMIT 5";
    $episodes_result = mysqli_query($conn, $episodes_query);
    
    $episodes_count = mysqli_num_rows($episodes_result);
    
    echo "<tr>";
    echo "<td>$tvshow_id</td>";
    echo "<td>$title</td>";
    echo "<td>$episodes_count episodes";
    
    if ($episodes_count > 0) {
        echo "<ul>";
        while ($episode = mysqli_fetch_assoc($episodes_result)) {
            $episode_id = $episode['id'];
            $season = $episode['season_number'];
            $ep_num = $episode['episode_number'];
            $ep_title = $episode['title'];
            
            // Get download links for this episode
            $links_query = "SELECT * FROM episode_links WHERE episode_id = $episode_id";
            $links_result = mysqli_query($conn, $links_query);
            
            $links_count = mysqli_num_rows($links_result);
            
            echo "<li>S$season E$ep_num: $ep_title - $links_count links";
            
            if ($links_count > 0) {
                echo "<ul>";
                while ($link = mysqli_fetch_assoc($links_result)) {
                    $quality = $link['quality'];
                    $url = isset($link['url']) ? $link['url'] : (isset($link['link_url']) ? $link['link_url'] : 'No URL');
                    $is_premium = isset($link['is_premium']) ? ($link['is_premium'] ? 'Yes' : 'No') : 'Unknown';
                    
                    echo "<li>Quality: $quality, Premium: $is_premium<br>URL: " . substr($url, 0, 50) . "...</li>";
                }
                echo "</ul>";
            } else {
                // Check download_links table
                $dl_query = "SELECT * FROM download_links WHERE content_type = 'episode' AND content_id = $episode_id";
                $dl_result = mysqli_query($conn, $dl_query);
                
                $dl_count = mysqli_num_rows($dl_result);
                
                if ($dl_count > 0) {
                    echo " (Found $dl_count links in download_links table)";
                    echo "<ul>";
                    while ($link = mysqli_fetch_assoc($dl_result)) {
                        $quality = $link['quality'];
                        $url = isset($link['url']) ? $link['url'] : (isset($link['link_url']) ? $link['link_url'] : 'No URL');
                        $is_premium = isset($link['is_premium']) ? ($link['is_premium'] ? 'Yes' : 'No') : 'Unknown';
                        
                        echo "<li>Quality: $quality, Premium: $is_premium<br>URL: " . substr($url, 0, 50) . "...</li>";
                    }
                    echo "</ul>";
                }
            }
            
            echo "</li>";
        }
        echo "</ul>";
    }
    
    echo "</td></tr>";
}

echo "</table>";
?>
