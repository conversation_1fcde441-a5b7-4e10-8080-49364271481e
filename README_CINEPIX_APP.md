# CinePix Flutter App

## সম্পর্কে (About)

CinePix Flutter অ্যাপটি আপনার CinePix ওয়েবসাইটের মোবাইল অ্যাপ সংস্করণ। এটি আপনার ওয়েবসাইটের API ব্যবহার করে মুভি এবং টিভি সিরিজ দেখানোর জন্য ডিজাইন করা হয়েছে।

## বৈশিষ্ট্য (Features)

- নেটফ্লিক্স স্টাইল UI ডিজাইন
- মুভি এবং টিভি সিরিজ ব্রাউজিং
- ক্যারোসেল স্লাইডার
- সার্চ ফাংশন
- ভিডিও প্লেয়ার (মাল্টি অডিও সাপোর্টসহ)
- ডাউনলোড এবং স্ট্রিমিং অপশন
- প্রিমিয়াম কনটেন্ট সাপোর্ট
- ইউজার প্রোফাইল

## ইনস্টলেশন (Installation)

### প্রয়োজনীয় সফটওয়্যার (Required Software)

- Flutter SDK (latest version)
- Android Studio / VS Code
- Git

### সেটআপ (Setup)

1. রিপোজিটরি ক্লোন করুন:
   ```
   git clone https://github.com/yourusername/cinepix_app.git
   cd cinepix_app
   ```

2. ডিপেন্ডেন্সি ইনস্টল করুন:
   ```
   flutter pub get
   ```

3. অ্যাপ চালান:
   ```
   flutter run
   ```

## অ্যাপ বিল্ড করা (Building the App)

### Android APK বিল্ড করা

1. `build_android_app.bat` স্ক্রিপ্ট চালান:
   ```
   .\build_android_app.bat
   ```

   অথবা নিম্নলিখিত কমান্ড ব্যবহার করুন:
   ```
   flutter build apk --release
   ```

2. APK ফাইল এখানে পাওয়া যাবে:
   ```
   build/app/outputs/flutter-apk/app-release.apk
   ```

### Android App Bundle (AAB) বিল্ড করা

Google Play Store-এ আপলোড করার জন্য:
```
flutter build appbundle --release
```

## API কনফিগারেশন (API Configuration)

অ্যাপটি আপনার ওয়েবসাইটের API ব্যবহার করে। API এন্ডপয়েন্টগুলি `lib/constants/api_constants.dart` ফাইলে কনফিগার করা আছে:

```dart
class ApiConstants {
  // API Base URL
  static const String baseUrl = 'https://cinepix.top/api/v1';

  // API Endpoints
  static const String configEndpoint = '/direct_config.php';
  static const String loginEndpoint = '/direct_login.php';
  static const String registerEndpoint = '/direct_register.php';
  static const String moviesEndpoint = '/direct_movies.php';
  static const String movieDetailsEndpoint = '/direct_movie_details.php';
  static const String tvShowsEndpoint = '/direct_tvshows.php';
  static const String tvShowDetailsEndpoint = '/direct_tvshow_details.php';
  static const String tvShowEpisodesEndpoint = '/direct_tvshow_episodes.php';
  static const String searchEndpoint = '/direct_search.php';
  static const String categoriesEndpoint = '/direct_categories.php';
  static const String profileEndpoint = '/direct_profile.php';
}
```

## ভিডিও প্লেয়ার (Video Player)

অ্যাপটি নিম্নলিখিত ভিডিও প্লেয়ার লাইব্রেরিগুলি ব্যবহার করে:

- **BetterPlayer**: মাল্টি অডিও ট্র্যাক, সাবটাইটেল, এবং অন্যান্য উন্নত বৈশিষ্ট্য সাপোর্ট করে
- **Chewie**: সহজ ইন্টারফেস সহ ভিডিও প্লেয়ার
- **VLC Player**: অতিরিক্ত ফরম্যাট সাপোর্ট

## সমস্যা সমাধান (Troubleshooting)

### API সমস্যা

যদি অ্যাপে কোন ডাটা না দেখায়, নিম্নলিখিত বিষয়গুলি চেক করুন:

1. API বেস URL সঠিক কিনা
2. ইন্টারনেট কানেকশন আছে কিনা
3. API এন্ডপয়েন্টগুলি সঠিকভাবে কাজ করছে কিনা

API টেস্ট করতে, ওয়েবসাইটে `api_test.php` পেজটি ব্যবহার করুন।

### ভিডিও প্লেয়ার সমস্যা

যদি ভিডিও প্লে না হয়:

1. ভিডিও URL সঠিক কিনা চেক করুন
2. ডিভাইসে ইন্টারনেট কানেকশন আছে কিনা নিশ্চিত করুন
3. ভিডিও ফরম্যাট সাপোর্টেড কিনা চেক করুন

## কাস্টমাইজেশন (Customization)

### থিম পরিবর্তন

অ্যাপের থিম পরিবর্তন করতে `lib/constants/theme_constants.dart` ফাইল এডিট করুন:

```dart
class ThemeConstants {
  // Primary colors
  static const Color primaryColor = Color(0xFFE50914);
  static const Color secondaryColor = Color(0xFF221F1F);
  static const Color darkColor = Color(0xFF000000);
  static const Color lightColor = Color(0xFFFFFFFF);
  
  // Additional colors
  static const Color grayColor = Color(0xFF8C8C8C);
  static const Color lightGray = Color(0xFFB3B3B3);
  static const Color premiumColor = Color(0xFFFFD700);
  
  // Button styles
  static final ButtonStyle primaryButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: primaryColor,
    foregroundColor: lightColor,
    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(8),
    ),
  );
}
```

## সাপোর্ট (Support)

সাহায্যের জন্য যোগাযোগ করুন: [<EMAIL>]
