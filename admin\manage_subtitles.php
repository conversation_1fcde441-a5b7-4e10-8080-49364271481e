<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set page title
$page_title = 'সাবটাইটেল ম্যানেজমেন্ট';

// Debug information
echo "<!-- Debug: Starting page load -->";

// Try to include config file
try {
    require_once '../includes/config.php';
    echo "<!-- Debug: Config file loaded successfully -->";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px;'>";
    echo "<h3>Error loading config file:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
    die();
}

// Check if functions exist
echo "<!-- Debug: Checking functions -->";
if (!function_exists('isLoggedIn')) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px;'>";
    echo "<h3>Function Error:</h3>";
    echo "<p>isLoggedIn function does not exist</p>";
    echo "</div>";
    die();
}

if (!function_exists('isAdmin')) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px;'>";
    echo "<h3>Function Error:</h3>";
    echo "<p>isAdmin function does not exist</p>";
    echo "</div>";
    die();
}

// Check if user is logged in and is admin
try {
    if (!isLoggedIn() || !isAdmin()) {
        redirect(SITE_URL);
    }
    echo "<!-- Debug: User authentication passed -->";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px;'>";
    echo "<h3>Authentication Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
    die();
}

// Set current page for sidebar highlighting
$current_page = 'manage_subtitles.php';

// Check database connection
if (!isset($conn) || !$conn) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px;'>";
    echo "<h3>Database Connection Error:</h3>";
    echo "<p>Database connection is not available</p>";
    if (isset($conn) && mysqli_connect_error()) {
        echo "<p>Error: " . mysqli_connect_error() . "</p>";
    }
    echo "</div>";
    die();
}
echo "<!-- Debug: Database connection OK -->";

// Include header
try {
    require_once 'includes/header.php';
    echo "<!-- Debug: Header included successfully -->";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px;'>";
    echo "<h3>Error including header:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
    die();
}

// Function to check if subtitles table exists and create it if not
function checkSubtitlesTable($conn) {
    echo "<!-- Debug: Checking subtitles table -->";

    try {
        // Check if table exists
        $table_check_query = "SHOW TABLES LIKE 'subtitles'";
        $table_check_result = mysqli_query($conn, $table_check_query);

        if (!$table_check_result) {
            throw new Exception("Failed to check if subtitles table exists: " . mysqli_error($conn));
        }

        if (mysqli_num_rows($table_check_result) == 0) {
            echo "<!-- Debug: Subtitles table does not exist, creating it -->";

            // Create subtitles table
            $create_table_query = "CREATE TABLE IF NOT EXISTS subtitles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                content_type ENUM('movie', 'episode') NOT NULL,
                content_id INT NOT NULL,
                language VARCHAR(50) NOT NULL,
                url VARCHAR(255) NOT NULL,
                is_default BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_content (content_type, content_id)
            )";

            if (mysqli_query($conn, $create_table_query)) {
                echo "<!-- Debug: Subtitles table created successfully -->";
                return true;
            } else {
                throw new Exception("Failed to create subtitles table: " . mysqli_error($conn));
            }
        } else {
            echo "<!-- Debug: Subtitles table already exists -->";

            // Check if the table has the required columns
            $check_columns_query = "SHOW COLUMNS FROM subtitles";
            $columns_result = mysqli_query($conn, $check_columns_query);

            if (!$columns_result) {
                throw new Exception("Failed to check subtitles table columns: " . mysqli_error($conn));
            }

            $columns = [];
            while ($column = mysqli_fetch_assoc($columns_result)) {
                $columns[] = $column['Field'];
            }

            $required_columns = ['id', 'content_type', 'content_id', 'language', 'url', 'is_default', 'created_at', 'updated_at'];
            $missing_columns = array_diff($required_columns, $columns);

            if (!empty($missing_columns)) {
                echo "<!-- Debug: Subtitles table is missing columns: " . implode(', ', $missing_columns) . " -->";
                echo "<div style='background: #fff3cd; color: #856404; padding: 10px; margin: 10px;'>";
                echo "<h3>Warning:</h3>";
                echo "<p>The subtitles table is missing some required columns: " . implode(', ', $missing_columns) . "</p>";
                echo "<p>Please contact the administrator to fix this issue.</p>";
                echo "</div>";
            }

            return true;
        }
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px;'>";
        echo "<h3>Database Error:</h3>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "</div>";
        return false;
    }
}

// Check if subtitles table exists and create it if not
checkSubtitlesTable($conn);

// Check if subtitles directory exists, if not create it
$subtitles_dir = '../uploads/subtitles';
echo "<!-- Debug: Checking subtitles directory: $subtitles_dir -->";
if (!file_exists($subtitles_dir)) {
    try {
        if (mkdir($subtitles_dir, 0755, true)) {
            echo "<!-- Debug: Created subtitles directory successfully -->";
        } else {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px;'>";
            echo "<h3>Directory Creation Error:</h3>";
            echo "<p>Could not create directory: $subtitles_dir</p>";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px;'>";
        echo "<h3>Directory Creation Error:</h3>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "</div>";
    }
} else {
    echo "<!-- Debug: Subtitles directory already exists -->";
}

// Handle subtitle deletion
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $subtitle_id = $_GET['delete'];

    // Get subtitle info to delete file
    $get_subtitle_query = "SELECT url FROM subtitles WHERE id = ?";
    $stmt = mysqli_prepare($conn, $get_subtitle_query);
    mysqli_stmt_bind_param($stmt, 'i', $subtitle_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if ($subtitle = mysqli_fetch_assoc($result)) {
        // Extract filename from URL
        $url = $subtitle['url'];
        $file_path = '';

        // Check if it's a local file
        if (strpos($url, 'uploads/subtitles/') !== false) {
            $file_path = '../' . substr($url, strpos($url, 'uploads/subtitles/'));

            // Delete file if it exists
            if (file_exists($file_path)) {
                unlink($file_path);
            }
        }

        // Delete from database
        $delete_query = "DELETE FROM subtitles WHERE id = ?";
        $stmt = mysqli_prepare($conn, $delete_query);
        mysqli_stmt_bind_param($stmt, 'i', $subtitle_id);

        if (mysqli_stmt_execute($stmt)) {
            $success_message = 'সাবটাইটেল সফলভাবে মুছে ফেলা হয়েছে।';
        } else {
            $error_message = 'সাবটাইটেল মুছতে সমস্যা হয়েছে: ' . mysqli_error($conn);
        }
    }
}

// Handle subtitle upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['upload_subtitle'])) {
    echo "<!-- Debug: Processing subtitle upload form submission -->";

    // Debug POST data
    echo "<!-- Debug: POST data: " . htmlspecialchars(print_r($_POST, true)) . " -->";

    // Debug FILES data
    if (isset($_FILES['subtitle_file'])) {
        echo "<!-- Debug: FILES data: " . htmlspecialchars(print_r($_FILES['subtitle_file'], true)) . " -->";
    } else {
        echo "<!-- Debug: No subtitle_file in FILES array -->";
    }

    $content_type = isset($_POST['content_type']) ? $_POST['content_type'] : '';
    $content_id = isset($_POST['content_id']) ? $_POST['content_id'] : '';
    $language = isset($_POST['language']) ? $_POST['language'] : '';
    $is_default = isset($_POST['is_default']) ? 1 : 0;

    // Validate input
    if (empty($content_type) || empty($content_id) || empty($language)) {
        $error_message = 'কনটেন্ট টাইপ, কনটেন্ট আইডি এবং ভাষা প্রয়োজন।';
    } else if (!in_array($content_type, ['movie', 'episode'])) {
        $error_message = 'অবৈধ কনটেন্ট টাইপ। মুভি বা এপিসোড হতে হবে।';
    } else {
        // Check if content exists
        if ($content_type === 'movie') {
            $check_query = "SELECT id FROM movies WHERE id = ?";
        } else {
            $check_query = "SELECT id FROM episodes WHERE id = ?";
        }

        $stmt = mysqli_prepare($conn, $check_query);
        mysqli_stmt_bind_param($stmt, 'i', $content_id);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_store_result($stmt);

        if (mysqli_stmt_num_rows($stmt) === 0) {
            $error_message = 'কনটেন্ট খুঁজে পাওয়া যায়নি।';
        } else {
            // Handle file upload
            if (!empty($_FILES['subtitle_file']['name'])) {
                $file_name = $_FILES['subtitle_file']['name'];
                $file_tmp = $_FILES['subtitle_file']['tmp_name'];
                $file_size = $_FILES['subtitle_file']['size'];
                $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

                // Check file extension
                $allowed_exts = ['srt', 'vtt', 'sub', 'sbv', 'ass'];
                if (!in_array($file_ext, $allowed_exts)) {
                    $error_message = 'অবৈধ ফাইল টাইপ। অনুমোদিত টাইপ: ' . implode(', ', $allowed_exts);
                } else if ($file_size > 2097152) { // 2MB
                    $error_message = 'ফাইল সাইজ 2MB এর কম হতে হবে।';
                } else {
                    // Generate unique filename
                    $new_filename = $content_type . '_' . $content_id . '_' . $language . '_' . time() . '.' . $file_ext;
                    $upload_path = $subtitles_dir . '/' . $new_filename;

                    echo "<!-- Debug: Attempting to upload file to: " . htmlspecialchars($upload_path) . " -->";

                    // Check if directory is writable
                    if (!is_writable(dirname($upload_path))) {
                        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px;'>";
                        echo "<h3>Upload Error:</h3>";
                        echo "<p>Directory is not writable: " . htmlspecialchars(dirname($upload_path)) . "</p>";
                        echo "<p>Current permissions: " . substr(sprintf('%o', fileperms(dirname($upload_path))), -4) . "</p>";
                        echo "</div>";
                        $error_message = 'ফাইল আপলোড করতে সমস্যা হয়েছে। ডিরেক্টরি রাইটেবল নয়।';
                    } else if (move_uploaded_file($file_tmp, $upload_path)) {
                        echo "<!-- Debug: File uploaded successfully -->";
                        // Create URL for database
                        $subtitle_url = 'uploads/subtitles/' . $new_filename;

                        // Check if subtitle already exists for this content and language
                        $check_exists_query = "SELECT id FROM subtitles WHERE content_type = ? AND content_id = ? AND language = ?";
                        $stmt = mysqli_prepare($conn, $check_exists_query);
                        mysqli_stmt_bind_param($stmt, 'sis', $content_type, $content_id, $language);
                        mysqli_stmt_execute($stmt);
                        mysqli_stmt_store_result($stmt);

                        if (mysqli_stmt_num_rows($stmt) > 0) {
                            // Update existing subtitle
                            $update_query = "UPDATE subtitles SET url = ?, is_default = ?, updated_at = NOW() WHERE content_type = ? AND content_id = ? AND language = ?";
                            $stmt = mysqli_prepare($conn, $update_query);
                            mysqli_stmt_bind_param($stmt, 'sisis', $subtitle_url, $is_default, $content_type, $content_id, $language);

                            if (mysqli_stmt_execute($stmt)) {
                                $success_message = 'সাবটাইটেল সফলভাবে আপডেট করা হয়েছে।';
                            } else {
                                $error_message = 'সাবটাইটেল আপডেট করতে সমস্যা হয়েছে: ' . mysqli_error($conn);
                            }
                        } else {
                            // Insert new subtitle
                            $insert_query = "INSERT INTO subtitles (content_type, content_id, language, url, is_default) VALUES (?, ?, ?, ?, ?)";
                            $stmt = mysqli_prepare($conn, $insert_query);
                            mysqli_stmt_bind_param($stmt, 'sissi', $content_type, $content_id, $language, $subtitle_url, $is_default);

                            if (mysqli_stmt_execute($stmt)) {
                                $success_message = 'সাবটাইটেল সফলভাবে আপলোড করা হয়েছে।';
                            } else {
                                $error_message = 'সাবটাইটেল আপলোড করতে সমস্যা হয়েছে: ' . mysqli_error($conn);
                            }
                        }
                    } else {
                        // Get detailed error information
                        $php_error = error_get_last();
                        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px;'>";
                        echo "<h3>File Upload Error:</h3>";
                        echo "<p>Failed to move uploaded file from {$file_tmp} to {$upload_path}</p>";
                        if ($php_error) {
                            echo "<p>PHP Error: " . htmlspecialchars($php_error['message']) . "</p>";
                        }
                        echo "<p>Upload tmp directory: " . sys_get_temp_dir() . "</p>";
                        echo "<p>Destination directory permissions: " . substr(sprintf('%o', fileperms(dirname($upload_path))), -4) . "</p>";
                        echo "</div>";
                        $error_message = 'ফাইল আপলোড করতে সমস্যা হয়েছে। আরও তথ্যের জন্য অ্যাডমিনের সাথে যোগাযোগ করুন।';
                    }
                }
            } else {
                $error_message = 'সাবটাইটেল ফাইল নির্বাচন করুন।';
            }
        }
    }
}

// Get all subtitles with pagination
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Filter options
$filter_content_type = isset($_GET['filter_content_type']) ? $_GET['filter_content_type'] : '';
$filter_language = isset($_GET['filter_language']) ? $_GET['filter_language'] : '';

// Build query
$query = "SELECT s.*,
          CASE
            WHEN s.content_type = 'movie' THEN m.title
            WHEN s.content_type = 'episode' THEN CONCAT(ser.title, ' S', e.season_number, 'E', e.episode_number, ' - ', e.title)
          END AS content_title
          FROM subtitles s
          LEFT JOIN movies m ON s.content_type = 'movie' AND s.content_id = m.id
          LEFT JOIN episodes e ON s.content_type = 'episode' AND s.content_id = e.id
          LEFT JOIN series ser ON e.series_id = ser.id
          WHERE 1=1";

// Add filters
$params = [];
$types = "";

if (!empty($filter_content_type)) {
    $query .= " AND s.content_type = ?";
    $params[] = $filter_content_type;
    $types .= "s";
}

if (!empty($filter_language)) {
    $query .= " AND s.language = ?";
    $params[] = $filter_language;
    $types .= "s";
}

// Count total rows for pagination
$count_query = str_replace("SELECT s.*,
          CASE
            WHEN s.content_type = 'movie' THEN m.title
            WHEN s.content_type = 'episode' THEN CONCAT(ser.title, ' S', e.season_number, 'E', e.episode_number, ' - ', e.title)
          END AS content_title", "SELECT COUNT(*) as total", $query);

// Execute count query with error handling
try {
    echo "<!-- Debug: Preparing count query: " . htmlspecialchars($count_query) . " -->";
    $stmt = mysqli_prepare($conn, $count_query);
    if (!$stmt) {
        throw new Exception("Count query preparation failed: " . mysqli_error($conn));
    }

    if (!empty($types)) {
        echo "<!-- Debug: Binding parameters with types: " . htmlspecialchars($types) . " -->";
        mysqli_stmt_bind_param($stmt, $types, ...$params);
    }

    echo "<!-- Debug: Executing count query -->";
    if (!mysqli_stmt_execute($stmt)) {
        throw new Exception("Count query execution failed: " . mysqli_stmt_error($stmt));
    }

    $count_result = mysqli_stmt_get_result($stmt);
    if ($count_result === false) {
        throw new Exception("Failed to get count result: " . mysqli_stmt_error($stmt));
    }

    $total_rows = mysqli_fetch_assoc($count_result)['total'];
    $total_pages = ceil($total_rows / $limit);
    echo "<!-- Debug: Count query executed successfully. Total rows: $total_rows, Total pages: $total_pages -->";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px;'>";
    echo "<h3>Database Count Query Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<p>Query: " . htmlspecialchars($count_query) . "</p>";
    echo "</div>";
    // Set default values to avoid further errors
    $total_rows = 0;
    $total_pages = 1;
}

// Add order and limit
$query .= " ORDER BY s.created_at DESC LIMIT ?, ?";
$params[] = $offset;
$params[] = $limit;
$types .= "ii";

// Execute query with error handling
try {
    echo "<!-- Debug: Preparing query: " . htmlspecialchars($query) . " -->";
    $stmt = mysqli_prepare($conn, $query);
    if (!$stmt) {
        throw new Exception("Query preparation failed: " . mysqli_error($conn));
    }

    echo "<!-- Debug: Binding parameters with types: " . htmlspecialchars($types) . " -->";
    if (!empty($types)) {
        mysqli_stmt_bind_param($stmt, $types, ...$params);
    }

    echo "<!-- Debug: Executing query -->";
    if (!mysqli_stmt_execute($stmt)) {
        throw new Exception("Query execution failed: " . mysqli_stmt_error($stmt));
    }

    echo "<!-- Debug: Getting result -->";
    $result = mysqli_stmt_get_result($stmt);
    if ($result === false) {
        throw new Exception("Failed to get result: " . mysqli_stmt_error($stmt));
    }

    echo "<!-- Debug: Query executed successfully -->";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px;'>";
    echo "<h3>Database Query Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<p>Query: " . htmlspecialchars($query) . "</p>";
    echo "</div>";
    // Create an empty result set to avoid further errors
    $result = false;
}
?>

<!-- Include sidebar -->
<?php
try {
    include 'includes/sidebar.php';
    echo "<!-- Debug: Sidebar included successfully -->";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px;'>";
    echo "<h3>Error including sidebar:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<!-- Content -->
<div class="content">
    <!-- Debug info -->
    <?php if (isset($_GET['debug']) && $_GET['debug'] == 1): ?>
    <div style="background: #d4edda; color: #155724; padding: 10px; margin: 10px;">
        <h3>Debug Information:</h3>
        <p>PHP Version: <?php echo phpversion(); ?></p>
        <p>Current Page: <?php echo $current_page; ?></p>
        <p>Subtitles Directory: <?php echo $subtitles_dir; ?> (<?php echo file_exists($subtitles_dir) ? 'Exists' : 'Does not exist'; ?>)</p>
        <p>Database Connection: <?php echo isset($conn) && $conn ? 'Connected' : 'Not connected'; ?></p>
        <p>Session Data: <?php echo print_r($_SESSION, true); ?></p>
    </div>
    <?php endif; ?>

    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title d-flex align-items-center">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>সাবটাইটেল ম্যানেজমেন্ট</h1>
            </div>

            <div class="topbar-actions">
                <div class="topbar-search">
                    <form action="search.php" method="GET">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="সার্চ করুন..." name="q">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>

                <div class="dropdown">
                    <button class="btn btn-link dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <img src="assets/img/user.png" class="rounded-circle" width="32" height="32" alt="User">
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user fa-sm me-2 text-gray-400"></i> প্রোফাইল</a></li>
                        <li><a class="dropdown-item" href="site_settings.php"><i class="fas fa-cogs fa-sm me-2 text-gray-400"></i> সেটিংস</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/logout.php"><i class="fas fa-sign-out-alt fa-sm me-2 text-gray-400"></i> লগআউট</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid p-4">
        <div class="row">
            <div class="col-12">
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">সাবটাইটেল আপলোড</h6>
                    </div>
                    <div class="card-body">
                        <?php if (isset($success_message)): ?>
                            <div class="alert alert-success"><?php echo $success_message; ?></div>
                        <?php endif; ?>

                        <?php if (isset($error_message)): ?>
                            <div class="alert alert-danger"><?php echo $error_message; ?></div>
                        <?php endif; ?>

                        <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="content_type">কনটেন্ট টাইপ</label>
                                    <select class="form-control" id="content_type" name="content_type" required>
                                        <option value="">সিলেক্ট করুন</option>
                                        <option value="movie">মুভি</option>
                                        <option value="episode">এপিসোড</option>
                                    </select>
                                    <div class="invalid-feedback">কনটেন্ট টাইপ সিলেক্ট করুন</div>
                                </div>

                                <div class="col-md-3 mb-3">
                                    <label for="content_id">কনটেন্ট আইডি</label>
                                    <input type="number" class="form-control" id="content_id" name="content_id" required>
                                    <div class="invalid-feedback">কনটেন্ট আইডি দিন</div>
                                </div>

                                <div class="col-md-3 mb-3">
                                    <label for="language">ভাষা</label>
                                    <select class="form-control" id="language" name="language" required>
                                        <option value="">সিলেক্ট করুন</option>
                                        <option value="bn">বাংলা</option>
                                        <option value="en">ইংরেজি</option>
                                        <option value="hi">হিন্দি</option>
                                        <option value="ar">আরবি</option>
                                        <option value="es">স্প্যানিশ</option>
                                    </select>
                                    <div class="invalid-feedback">ভাষা সিলেক্ট করুন</div>
                                </div>

                                <div class="col-md-3 mb-3">
                                    <label for="subtitle_file">সাবটাইটেল ফাইল</label>
                                    <input type="file" class="form-control" id="subtitle_file" name="subtitle_file" required>
                                    <small class="form-text text-muted">সমর্থিত ফরম্যাট: SRT, VTT, SUB, SBV, ASS</small>
                                    <div class="invalid-feedback">সাবটাইটেল ফাইল আপলোড করুন</div>
                                </div>
                            </div>

                            <div class="form-check mb-3">
                                <input type="checkbox" class="form-check-input" id="is_default" name="is_default">
                                <label class="form-check-label" for="is_default">ডিফল্ট সাবটাইটেল</label>
                            </div>

                            <button type="submit" name="upload_subtitle" class="btn btn-primary">আপলোড করুন</button>
                        </form>
                    </div>
                </div>

                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">সাবটাইটেল লিস্ট</h6>
                    </div>
                    <div class="card-body">
                        <!-- Filter Form -->
                        <form method="get" class="mb-4">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="filter_content_type">কনটেন্ট টাইপ</label>
                                    <select class="form-control" id="filter_content_type" name="filter_content_type">
                                        <option value="">সব</option>
                                        <option value="movie" <?php echo $filter_content_type === 'movie' ? 'selected' : ''; ?>>মুভি</option>
                                        <option value="episode" <?php echo $filter_content_type === 'episode' ? 'selected' : ''; ?>>এপিসোড</option>
                                    </select>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="filter_language">ভাষা</label>
                                    <select class="form-control" id="filter_language" name="filter_language">
                                        <option value="">সব</option>
                                        <option value="bn" <?php echo $filter_language === 'bn' ? 'selected' : ''; ?>>বাংলা</option>
                                        <option value="en" <?php echo $filter_language === 'en' ? 'selected' : ''; ?>>ইংরেজি</option>
                                        <option value="hi" <?php echo $filter_language === 'hi' ? 'selected' : ''; ?>>হিন্দি</option>
                                        <option value="ar" <?php echo $filter_language === 'ar' ? 'selected' : ''; ?>>আরবি</option>
                                        <option value="es" <?php echo $filter_language === 'es' ? 'selected' : ''; ?>>স্প্যানিশ</option>
                                    </select>
                                </div>

                                <div class="col-md-4 mb-3 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary">ফিল্টার করুন</button>
                                    <a href="manage_subtitles.php" class="btn btn-secondary ml-2">রিসেট</a>
                                </div>
                            </div>
                        </form>

                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>আইডি</th>
                                        <th>কনটেন্ট</th>
                                        <th>টাইপ</th>
                                        <th>ভাষা</th>
                                        <th>ডিফল্ট</th>
                                        <th>তারিখ</th>
                                        <th>অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($result && mysqli_num_rows($result) > 0): ?>
                                        <?php while ($subtitle = mysqli_fetch_assoc($result)): ?>
                                            <tr>
                                                <td><?php echo $subtitle['id']; ?></td>
                                                <td><?php echo htmlspecialchars($subtitle['content_title']); ?></td>
                                                <td><?php echo $subtitle['content_type'] === 'movie' ? 'মুভি' : 'এপিসোড'; ?></td>
                                                <td>
                                                    <?php
                                                    $languages = [
                                                        'bn' => 'বাংলা',
                                                        'en' => 'ইংরেজি',
                                                        'hi' => 'হিন্দি',
                                                        'ar' => 'আরবি',
                                                        'es' => 'স্প্যানিশ'
                                                    ];
                                                    echo isset($languages[$subtitle['language']]) ? $languages[$subtitle['language']] : $subtitle['language'];
                                                    ?>
                                                </td>
                                                <td><?php echo $subtitle['is_default'] ? 'হ্যাঁ' : 'না'; ?></td>
                                                <td><?php echo date('d M Y', strtotime($subtitle['created_at'])); ?></td>
                                                <td>
                                                    <a href="<?php echo '../' . $subtitle['url']; ?>" class="btn btn-sm btn-info" target="_blank">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="manage_subtitles.php?delete=<?php echo $subtitle['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই সাবটাইটেল মুছতে চান?');">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="7" class="text-center">কোন সাবটাইটেল পাওয়া যায়নি</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&filter_content_type=<?php echo $filter_content_type; ?>&filter_language=<?php echo $filter_language; ?>">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    <?php endif; ?>

                                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>&filter_content_type=<?php echo $filter_content_type; ?>&filter_language=<?php echo $filter_language; ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>

                                    <?php if ($page < $total_pages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&filter_content_type=<?php echo $filter_content_type; ?>&filter_language=<?php echo $filter_language; ?>">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.9/js/responsive.bootstrap5.min.js"></script>
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- Custom JS -->
<script src="assets/js/admin.js"></script>
<script src="custom.js"></script>
<script>
    // Toggle sidebar on mobile
    document.addEventListener('DOMContentLoaded', function() {
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.querySelector('.overlay');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('active');
                overlay.classList.toggle('active');
            });
        }

        if (overlay) {
            overlay.addEventListener('click', function() {
                sidebar.classList.remove('active');
                overlay.classList.remove('active');
            });
        }
    });
</script>

<!-- Debug information at the bottom of the page -->
<?php if (isset($_GET['debug']) && $_GET['debug'] == 1): ?>
<div style="background: #f8f9fa; color: #212529; padding: 10px; margin: 10px; border: 1px solid #dee2e6;">
    <h3>Debug Information (End of Page):</h3>
    <p>Page execution completed</p>
    <p>Memory usage: <?php echo round(memory_get_usage() / 1024 / 1024, 2); ?> MB</p>
    <p>Peak memory usage: <?php echo round(memory_get_peak_usage() / 1024 / 1024, 2); ?> MB</p>
    <p>Execution time: <?php echo round(microtime(true) - $_SERVER["REQUEST_TIME_FLOAT"], 4); ?> seconds</p>
    <?php
    // Check if subtitles table exists
    $table_check_query = "SHOW TABLES LIKE 'subtitles'";
    $table_check_result = mysqli_query($conn, $table_check_query);
    $table_exists = mysqli_num_rows($table_check_result) > 0;
    ?>
    <p>Subtitles table exists: <?php echo $table_exists ? 'Yes' : 'No'; ?></p>

    <?php if ($table_exists): ?>
        <?php
        // Get table structure
        $table_structure_query = "DESCRIBE subtitles";
        $table_structure_result = mysqli_query($conn, $table_structure_query);
        ?>
        <h4>Subtitles Table Structure:</h4>
        <table style="border-collapse: collapse; width: 100%;">
            <tr>
                <th style="border: 1px solid #dee2e6; padding: 8px;">Field</th>
                <th style="border: 1px solid #dee2e6; padding: 8px;">Type</th>
                <th style="border: 1px solid #dee2e6; padding: 8px;">Null</th>
                <th style="border: 1px solid #dee2e6; padding: 8px;">Key</th>
                <th style="border: 1px solid #dee2e6; padding: 8px;">Default</th>
                <th style="border: 1px solid #dee2e6; padding: 8px;">Extra</th>
            </tr>
            <?php while ($row = mysqli_fetch_assoc($table_structure_result)): ?>
            <tr>
                <td style="border: 1px solid #dee2e6; padding: 8px;"><?php echo $row['Field']; ?></td>
                <td style="border: 1px solid #dee2e6; padding: 8px;"><?php echo $row['Type']; ?></td>
                <td style="border: 1px solid #dee2e6; padding: 8px;"><?php echo $row['Null']; ?></td>
                <td style="border: 1px solid #dee2e6; padding: 8px;"><?php echo $row['Key']; ?></td>
                <td style="border: 1px solid #dee2e6; padding: 8px;"><?php echo $row['Default']; ?></td>
                <td style="border: 1px solid #dee2e6; padding: 8px;"><?php echo $row['Extra']; ?></td>
            </tr>
            <?php endwhile; ?>
        </table>
    <?php endif; ?>
</div>
<?php endif; ?>

</body>
</html>
