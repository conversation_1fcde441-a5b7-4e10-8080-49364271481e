<?php
// Include necessary files
require_once 'includes/config.php';
require_once 'includes/streaming_helper.php';

// Test Cloudflare Worker URL
$worker_url = "https://odd-darkness-074fsadsafsafasfjlknmmlkaytr9pe8afnhdklnfalskdftgy.bdmovieshub.workers.dev/994/RRR+2022+-+Hindi+-WEBDL+-+720p.mkv?hash=AgADjg";
$title = "Test Worker Video";
$poster = "https://image.tmdb.org/t/p/w500/wE0I6efAW4cDDmZQWtwZMOW44EJ.jpg";

// Get streaming URL
$streaming_url = getStreamingUrl($worker_url, $title, $poster);

// Test non-worker URL
$non_worker_url = "https://example.com/video.mp4";
$non_worker_streaming_url = getStreamingUrl($non_worker_url, "Test Regular Video", $poster);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Worker Player</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #111;
            color: #fff;
            padding: 20px;
        }
        .card {
            background-color: #222;
            border: none;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #333;
            color: #fff;
            border-bottom: 1px solid #444;
        }
        .btn-primary {
            background-color: #e50914;
            border-color: #e50914;
        }
        .btn-primary:hover {
            background-color: #b30710;
            border-color: #b30710;
        }
        pre {
            background-color: #333;
            color: #fff;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .url-display {
            word-break: break-all;
            background-color: #333;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Test Worker Player</h1>

        <div class="card">
            <div class="card-header">
                <h5>Cloudflare Worker URL Test</h5>
            </div>
            <div class="card-body">
                <h6>Original URL:</h6>
                <div class="url-display"><?php echo htmlspecialchars($worker_url); ?></div>

                <h6>Generated Streaming URL:</h6>
                <div class="url-display"><?php echo htmlspecialchars($streaming_url); ?></div>

                <p>This should use the plyr_player_enhanced.php player.</p>

                <a href="<?php echo $streaming_url; ?>" class="btn btn-primary">
                    <i class="fas fa-play-circle me-2"></i>Test Worker URL Player
                </a>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5>Regular URL Test</h5>
            </div>
            <div class="card-body">
                <h6>Original URL:</h6>
                <div class="url-display"><?php echo htmlspecialchars($non_worker_url); ?></div>

                <h6>Generated Streaming URL:</h6>
                <div class="url-display"><?php echo htmlspecialchars($non_worker_streaming_url); ?></div>

                <p>This should use the play.php player.</p>

                <a href="<?php echo $non_worker_streaming_url; ?>" class="btn btn-primary">
                    <i class="fas fa-play-circle me-2"></i>Test Regular URL Player
                </a>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5>Code Explanation</h5>
            </div>
            <div class="card-body">
                <p>The <code>getStreamingUrl()</code> function in <code>includes/streaming_helper.php</code> has been modified to check if the URL contains "workers.dev". If it does, it uses the enhanced Plyr player; otherwise, it uses the regular player.</p>

                <pre>
function getStreamingUrl($url, $title = '', $poster = '', $is_premium = false) {
    // Check if URL is from Cloudflare Workers
    $is_worker_url = (stripos($url, 'workers.dev') !== false);

    // Use plyr_player_enhanced.php for Cloudflare Worker links, otherwise use play.php
    $player_file = $is_worker_url ? 'plyr_player_enhanced.php' : 'play.php';
    $streaming_url = $player_file . '?url=' . urlencode($url);

    // Add other parameters...

    return $streaming_url;
}</pre>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
