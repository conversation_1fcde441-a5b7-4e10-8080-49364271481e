<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set page title
$page_title = 'ক্যাটাগরি ম্যানেজমেন্ট';
$current_page = 'categories.php';

try {
    // Include configuration and functions
    require_once '../../includes/config.php';
    require_once '../../includes/functions.php';

    // Check if user is logged in and is admin
    if (!isLoggedIn() || !isAdmin()) {
        redirect(SITE_URL . '/login.php');
        exit;
    }
} catch (Exception $e) {
    die("Error: " . $e->getMessage());
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['add_category'])) {
        $name = trim($_POST['name']);
        $description = trim($_POST['description']);

        if (!empty($name)) {
            try {
                $insert_query = "INSERT INTO categories (name, description, created_at) VALUES (?, ?, NOW())";
                $stmt = mysqli_prepare($conn, $insert_query);
                mysqli_stmt_bind_param($stmt, 'ss', $name, $description);

                if (mysqli_stmt_execute($stmt)) {
                    $success_message = "নতুন ক্যাটাগরি সফলভাবে যোগ করা হয়েছে।";
                } else {
                    $error_message = "ক্যাটাগরি যোগ করতে সমস্যা হয়েছে।";
                }
            } catch (Exception $e) {
                $error_message = "Error: " . $e->getMessage();
            }
        } else {
            $error_message = "ক্যাটাগরির নাম প্রয়োজন।";
        }
    }

    if (isset($_POST['bulk_action'])) {
        $action = $_POST['bulk_action'];
        $selected_ids = $_POST['selected_categories'] ?? [];

        if (!empty($selected_ids) && is_array($selected_ids)) {
            $ids = implode(',', array_map('intval', $selected_ids));

            try {
                switch ($action) {
                    case 'delete':
                        $delete_query = "DELETE FROM categories WHERE id IN ($ids)";
                        if (mysqli_query($conn, $delete_query)) {
                            $success_message = count($selected_ids) . ' টি ক্যাটাগরি সফলভাবে ডিলিট করা হয়েছে।';
                        }
                        break;
                }
            } catch (Exception $e) {
                $error_message = "Error: " . $e->getMessage();
            }
        }
    }
}

// Get filter parameters
$search = $_GET['search'] ?? '';
$sort_by = $_GET['sort'] ?? 'created_at';
$sort_order = $_GET['order'] ?? 'DESC';

// Build query
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(name LIKE ? OR description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get total count
$count_query = "SELECT COUNT(*) as total FROM categories $where_clause";
$stmt = mysqli_prepare($conn, $count_query);
if (!empty($params)) {
    $types = str_repeat('s', count($params));
    mysqli_stmt_bind_param($stmt, $types, ...$params);
}
mysqli_stmt_execute($stmt);
$total_categories = mysqli_fetch_assoc(mysqli_stmt_get_result($stmt))['total'];

// Pagination
$page = $_GET['page'] ?? 1;
$per_page = 20;
$total_pages = ceil($total_categories / $per_page);
$offset = ($page - 1) * $per_page;

// Get categories with content count
$query = "SELECT c.*,
          (SELECT COUNT(*) FROM movies WHERE category_id = c.id) as movie_count,
          (SELECT COUNT(*) FROM tvshows WHERE category_id = c.id) as tvshow_count
          FROM categories c
          $where_clause
          ORDER BY c.$sort_by $sort_order
          LIMIT $per_page OFFSET $offset";

$stmt = mysqli_prepare($conn, $query);
if (!empty($params)) {
    $types = str_repeat('s', count($params));
    mysqli_stmt_bind_param($stmt, $types, ...$params);
}
mysqli_stmt_execute($stmt);
$categories_result = mysqli_stmt_get_result($stmt);

// Include header
include 'includes/header.php';
?>

<!-- Include Sidebar -->
<?php include 'includes/sidebar.php'; ?>

<!-- Main Content -->
<div class="main-content">
    <!-- Page Header -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-title">
                    <i class="fas fa-tags me-3"></i>ক্যাটাগরি ম্যানেজমেন্ট
                </h1>
                <p class="page-subtitle text-muted">মোট <?php echo number_format($total_categories); ?> টি ক্যাটাগরি</p>
            </div>
            <div class="col-auto">
                <div class="page-actions">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                        <i class="fas fa-plus me-2"></i>নতুন ক্যাটাগরি যোগ করুন
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">খুঁজুন</label>
                    <input type="text" class="form-control" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="ক্যাটাগরির নাম...">
                </div>
                <div class="col-md-3">
                    <label class="form-label">সর্ট</label>
                    <select class="form-select" name="sort">
                        <option value="created_at" <?php echo $sort_by == 'created_at' ? 'selected' : ''; ?>>তারিখ</option>
                        <option value="name" <?php echo $sort_by == 'name' ? 'selected' : ''; ?>>নাম</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">অর্ডার</label>
                    <select class="form-select" name="order">
                        <option value="DESC" <?php echo $sort_order == 'DESC' ? 'selected' : ''; ?>>নিচে</option>
                        <option value="ASC" <?php echo $sort_order == 'ASC' ? 'selected' : ''; ?>>উপরে</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i>
                    </button>
                    <a href="categories.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Categories Table -->
    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="card-title mb-0">ক্যাটাগরি তালিকা</h5>
                </div>
                <div class="col-auto">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAll()">
                            <i class="fas fa-check-square me-1"></i>সব নির্বাচন
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="deselectAll()">
                            <i class="fas fa-square me-1"></i>নির্বাচন বাতিল
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <form method="POST" id="bulkActionForm" class="bulk-action-form">
                <!-- Bulk Actions -->
                <div class="bulk-actions p-3 border-bottom" style="display: none;">
                    <div class="row align-items-center">
                        <div class="col">
                            <span class="selected-count">0</span> টি ক্যাটাগরি নির্বাচিত
                        </div>
                        <div class="col-auto">
                            <div class="btn-group">
                                <button type="submit" name="bulk_action" value="delete" class="btn btn-danger btn-sm bulk-action-btn delete-btn" disabled data-confirm-message="নির্বাচিত ক্যাটাগরিগুলো ডিলিট করতে চান?">
                                    <i class="fas fa-trash me-1"></i>ডিলিট করুন
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th width="50">
                                    <input type="checkbox" class="form-check-input select-all" id="selectAll">
                                </th>
                                <th>নাম</th>
                                <th>বিবরণ</th>
                                <th>মুভি</th>
                                <th>টিভি শো</th>
                                <th>তারিখ</th>
                                <th width="120">অ্যাকশন</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (mysqli_num_rows($categories_result) > 0): ?>
                                <?php while ($category = mysqli_fetch_assoc($categories_result)): ?>
                                    <tr>
                                        <td data-label="">
                                            <input type="checkbox" class="form-check-input item-checkbox" name="selected_categories[]" value="<?php echo $category['id']; ?>">
                                        </td>
                                        <td data-label="নাম">
                                            <div class="fw-bold"><?php echo htmlspecialchars($category['name']); ?></div>
                                        </td>
                                        <td data-label="বিবরণ">
                                            <small class="text-muted"><?php echo htmlspecialchars($category['description'] ?? 'কোন বিবরণ নেই'); ?></small>
                                        </td>
                                        <td data-label="মুভি">
                                            <span class="badge bg-primary"><?php echo $category['movie_count']; ?> টি</span>
                                        </td>
                                        <td data-label="টিভি শো">
                                            <span class="badge bg-success"><?php echo $category['tvshow_count']; ?> টি</span>
                                        </td>
                                        <td data-label="তারিখ">
                                            <small><?php echo date('d/m/Y', strtotime($category['created_at'])); ?></small>
                                        </td>
                                        <td data-label="অ্যাকশন">
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="editCategory(<?php echo $category['id']; ?>, '<?php echo htmlspecialchars($category['name']); ?>', '<?php echo htmlspecialchars($category['description']); ?>')" title="এডিট করুন">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <a href="delete_category.php?id=<?php echo $category['id']; ?>" class="btn btn-outline-danger delete-btn" title="ডিলিট করুন" data-confirm-message="এই ক্যাটাগরিটি ডিলিট করতে চান?">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">কোন ক্যাটাগরি পাওয়া যায়নি।</p>
                                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                                            <i class="fas fa-plus me-2"></i>প্রথম ক্যাটাগরি যোগ করুন
                                        </button>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </form>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="card-footer">
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center mb-0">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?>&<?php echo http_build_query($_GET); ?>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query($_GET); ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?>&<?php echo http_build_query($_GET); ?>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header border-secondary">
                <h5 class="modal-title" id="addCategoryModalLabel">নতুন ক্যাটাগরি যোগ করুন</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">ক্যাটাগরির নাম *</label>
                        <input type="text" class="form-control" id="categoryName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="categoryDescription" class="form-label">বিবরণ</label>
                        <textarea class="form-control" id="categoryDescription" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer border-secondary">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="submit" name="add_category" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>সেভ করুন
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Category Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1" aria-labelledby="editCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header border-secondary">
                <h5 class="modal-title" id="editCategoryModalLabel">ক্যাটাগরি এডিট করুন</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="edit_category.php">
                <div class="modal-body">
                    <input type="hidden" id="editCategoryId" name="id">
                    <div class="mb-3">
                        <label for="editCategoryName" class="form-label">ক্যাটাগরির নাম *</label>
                        <input type="text" class="form-control" id="editCategoryName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="editCategoryDescription" class="form-label">বিবরণ</label>
                        <textarea class="form-control" id="editCategoryDescription" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer border-secondary">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="submit" name="edit_category" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>আপডেট করুন
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Page-specific scripts
$page_scripts = '
<script>
function selectAll() {
    const checkboxes = document.querySelectorAll(".item-checkbox");
    const selectAllCheckbox = document.getElementById("selectAll");

    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    selectAllCheckbox.checked = true;

    updateBulkActions();
}

function deselectAll() {
    const checkboxes = document.querySelectorAll(".item-checkbox, #selectAll");

    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });

    updateBulkActions();
}

function updateBulkActions() {
    const checkedBoxes = document.querySelectorAll(".item-checkbox:checked");
    const bulkActions = document.querySelector(".bulk-actions");
    const bulkActionButtons = document.querySelectorAll(".bulk-action-btn");
    const selectedCount = document.querySelector(".selected-count");

    if (checkedBoxes.length > 0) {
        bulkActions.style.display = "block";
        bulkActionButtons.forEach(btn => btn.disabled = false);
        selectedCount.textContent = checkedBoxes.length;
    } else {
        bulkActions.style.display = "none";
        bulkActionButtons.forEach(btn => btn.disabled = true);
    }
}

function editCategory(id, name, description) {
    document.getElementById("editCategoryId").value = id;
    document.getElementById("editCategoryName").value = name;
    document.getElementById("editCategoryDescription").value = description;

    const editModal = new bootstrap.Modal(document.getElementById("editCategoryModal"));
    editModal.show();
}

// Event listeners
document.addEventListener("DOMContentLoaded", function() {
    // Select all checkbox
    document.getElementById("selectAll").addEventListener("change", function() {
        const checkboxes = document.querySelectorAll(".item-checkbox");
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });

    // Individual checkboxes
    document.querySelectorAll(".item-checkbox").forEach(checkbox => {
        checkbox.addEventListener("change", function() {
            const allCheckboxes = document.querySelectorAll(".item-checkbox");
            const checkedCheckboxes = document.querySelectorAll(".item-checkbox:checked");
            const selectAllCheckbox = document.getElementById("selectAll");

            selectAllCheckbox.checked = allCheckboxes.length === checkedCheckboxes.length;
            updateBulkActions();
        });
    });

    // Bulk action form submission
    document.getElementById("bulkActionForm").addEventListener("submit", function(e) {
        const checkedBoxes = document.querySelectorAll(".item-checkbox:checked");
        if (checkedBoxes.length === 0) {
            e.preventDefault();
            cinepixAdmin.showToast("কোন ক্যাটাগরি নির্বাচন করা হয়নি।", "warning");
        }
    });
});
</script>
';

// Include footer
include 'includes/footer.php';
?>