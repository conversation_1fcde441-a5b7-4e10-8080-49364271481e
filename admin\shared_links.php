<?php
$page_title = 'শেয়ার লিংক ম্যানেজমেন্ট';
$current_page = 'shared_links.php';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create':
                $content_type = mysqli_real_escape_string($conn, $_POST['content_type']);
                $content_id = (int)$_POST['content_id'];
                $title = mysqli_real_escape_string($conn, $_POST['title']);
                $description = mysqli_real_escape_string($conn, $_POST['description']);
                $access_limit = (int)$_POST['access_limit'];
                $expires_at = !empty($_POST['expires_at']) ? $_POST['expires_at'] : NULL;
                $allow_download = isset($_POST['allow_download']) ? 1 : 0;
                $allow_streaming = isset($_POST['allow_streaming']) ? 1 : 0;
                $password = !empty($_POST['password']) ? password_hash($_POST['password'], PASSWORD_DEFAULT) : NULL;
                
                // Generate unique token
                $link_token = bin2hex(random_bytes(16)) . '_' . time();
                
                $query = "INSERT INTO shared_links (link_token, content_type, content_id, created_by, title, description, access_limit, expires_at, allow_download, allow_streaming, password) 
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = mysqli_prepare($conn, $query);
                mysqli_stmt_bind_param($stmt, 'ssiissisiis', $link_token, $content_type, $content_id, $_SESSION['user_id'], $title, $description, $access_limit, $expires_at, $allow_download, $allow_streaming, $password);
                
                if (mysqli_stmt_execute($stmt)) {
                    $success_message = "শেয়ার লিংক সফলভাবে তৈরি হয়েছে!";
                } else {
                    $error_message = "শেয়ার লিংক তৈরি করতে সমস্যা হয়েছে।";
                }
                break;
                
            case 'toggle_status':
                $link_id = (int)$_POST['link_id'];
                $new_status = (int)$_POST['new_status'];
                
                $query = "UPDATE shared_links SET is_active = ? WHERE id = ?";
                $stmt = mysqli_prepare($conn, $query);
                mysqli_stmt_bind_param($stmt, 'ii', $new_status, $link_id);
                
                if (mysqli_stmt_execute($stmt)) {
                    $success_message = "স্ট্যাটাস আপডেট হয়েছে!";
                } else {
                    $error_message = "স্ট্যাটাস আপডেট করতে সমস্যা হয়েছে।";
                }
                break;
                
            case 'delete':
                $link_id = (int)$_POST['link_id'];
                
                $query = "DELETE FROM shared_links WHERE id = ?";
                $stmt = mysqli_prepare($conn, $query);
                mysqli_stmt_bind_param($stmt, 'i', $link_id);
                
                if (mysqli_stmt_execute($stmt)) {
                    $success_message = "শেয়ার লিংক ডিলিট হয়েছে!";
                } else {
                    $error_message = "শেয়ার লিংক ডিলিট করতে সমস্যা হয়েছে।";
                }
                break;
        }
    }
}

// Check if content is preselected via GET
$preselected_content_id = isset($_GET['content_id']) ? (int)$_GET['content_id'] : null;
$preselected_content_type = isset($_GET['content_type']) ? $_GET['content_type'] : null;
$preselected_title = isset($_GET['title']) ? $_GET['title'] : null;



// Get all shared links with stats
$links_query = "SELECT sl.*, 
                CASE 
                    WHEN sl.content_type = 'movie' THEN m.title
                    WHEN sl.content_type = 'tvshow' THEN t.title
                END as content_title,
                CASE 
                    WHEN sl.access_limit > 0 THEN ROUND((sl.access_count / sl.access_limit) * 100, 2)
                    ELSE 0
                END as usage_percentage,
                CASE 
                    WHEN sl.expires_at IS NOT NULL AND sl.expires_at < NOW() THEN 'expired'
                    WHEN sl.access_limit > 0 AND sl.access_count >= sl.access_limit THEN 'limit_reached'
                    WHEN sl.is_active = 0 THEN 'inactive'
                    ELSE 'active'
                END as status
                FROM shared_links sl
                LEFT JOIN movies m ON sl.content_type = 'movie' AND sl.content_id = m.id
                LEFT JOIN tvshows t ON sl.content_type = 'tvshow' AND sl.content_id = t.id
                ORDER BY sl.created_at DESC";
$links_result = mysqli_query($conn, $links_query);

// Get movies and TV shows for dropdown
$movies_query = "SELECT id, title FROM movies WHERE premium_only = 1 ORDER BY title";
$movies_result = mysqli_query($conn, $movies_query);

$tvshows_query = "SELECT id, title FROM tvshows WHERE premium_only = 1 ORDER BY title";
$tvshows_result = mysqli_query($conn, $tvshows_query);

require_once 'includes/header.php';
require_once 'includes/sidebar.php';


?>

<div class="main-content" style="margin-left: 250px; width: calc(100% - 250px); padding: 20px; min-height: 100vh;">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="page-header">
                    <h1><i class="fas fa-share-alt"></i> শেয়ার লিংক ম্যানেজমেন্ট</h1>
                    <p class="text-muted">প্রিমিয়াম কন্টেন্টের জন্য কাস্টম শেয়ার লিংক তৈরি ও ম্যানেজ করুন</p>
                </div>

                <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-plus"></i> নতুন শেয়ার লিংক তৈরি করুন</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="createLinkForm">
                            <input type="hidden" name="action" value="create">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <!-- Content Type Selection -->
                                    <div class="form-group">
                                        <label for="content_type">কন্টেন্ট ধরন</label>
                                        <select class="form-control" id="content_type" name="content_type" required>
                                            <option value="">-- নির্বাচন করুন --</option>
                                            <option value="movie" <?php echo ($preselected_content_type === 'movie') ? 'selected' : ''; ?>>মুভি</option>
                                            <option value="tvshow" <?php echo ($preselected_content_type === 'tvshow') ? 'selected' : ''; ?>>টিভি শো</option>
                                        </select>
                                    </div>



                                    <input type="hidden" id="content_id" name="content_id" value="<?php echo $preselected_content_id ? $preselected_content_id : ''; ?>">
                                    <input type="hidden" id="selected_title" name="title" value="<?php echo $preselected_title ? htmlspecialchars($preselected_title) : ''; ?>">
                                    <div class="form-group">
                                        <label>নির্বাচিত কন্টেন্ট</label>
                                        <div id="selectedContent" class="form-control" style="background: #f8f9fa; padding: 10px;">
                                            <?php if ($preselected_content_id && $preselected_content_type && $preselected_title): ?>
                                                <i class="fas fa-check-circle text-success"></i> <?php echo htmlspecialchars($preselected_title); ?> 
                                                <span class="badge bg-primary"><?php echo ($preselected_content_type === 'movie') ? 'মুভি' : 'টিভি শো'; ?></span>
                                            <?php else: ?>
                                                <i class="fas fa-info-circle"></i> কোনো কন্টেন্ট নির্বাচন করা হয়নি
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="access_limit">এক্সেস লিমিট</label>
                                        <input type="number" class="form-control" id="access_limit" name="access_limit" min="1" value="1" required>
                                        <small class="form-text text-muted">কতবার এই লিংক ব্যবহার করা যাবে</small>
                                    </div>
                                    <div class="form-group">
                                        <label for="expires_at">মেয়াদ</label>
                                        <input type="datetime-local" class="form-control" id="expires_at" name="expires_at">
                                        <small class="form-text text-muted">লিংক কখন মেয়াদ শেষ হবে (ঐচ্ছিক)</small>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="password">পাসওয়ার্ড (ঐচ্ছিক)</label>
                                <input type="password" class="form-control" id="password" name="password">
                                <small class="form-text text-muted">লিংকে প্রবেশের জন্য পাসওয়ার্ড সেট করুন</small>
                            </div>
                            <div class="form-group">
                                <label for="description">বিবরণ (ঐচ্ছিক)</label>
                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">শেয়ার লিংক তৈরি করুন</button>
                        </form>
                    </div>
                </div>



                <!-- Existing Share Links -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> বিদ্যমান শেয়ার লিংক সমূহ</h5>
                    </div>
                    <div class="card-body">
                        <?php if (mysqli_num_rows($links_result) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>টাইটেল</th>
                                        <th>কন্টেন্ট</th>
                                        <th>লিংক</th>
                                        <th>এক্সেস</th>
                                        <th>স্ট্যাটাস</th>
                                        <th>তৈরির তারিখ</th>
                                        <th>অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($link = mysqli_fetch_assoc($links_result)): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($link['title']); ?></strong>
                                            <?php 
                                            $description = isset($link['description']) ? $link['description'] : '';
                                            if (!empty($description)): 
                                            ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars(substr($description, 0, 50)); ?>...</small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $link['content_type'] == 'movie' ? 'মুভি' : 'সিরিজ'; ?></span>
                                            <br><small><?php echo htmlspecialchars($link['content_title']); ?></small>
                                        </td>
                                        <td>
                                            <div class="input-group input-group-sm">
                                                <input type="text" class="form-control" value="<?php echo SITE_URL; ?>/share/<?php echo $link['link_token']; ?>" readonly>
                                                <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('<?php echo SITE_URL; ?>/share/<?php echo $link['link_token']; ?>')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($link['access_limit'] > 0): ?>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar" style="width: <?php echo $link['usage_percentage']; ?>%">
                                                    <?php echo $link['access_count']; ?>/<?php echo $link['access_limit']; ?>
                                                </div>
                                            </div>
                                            <?php else: ?>
                                            <span class="badge bg-success"><?php echo $link['access_count']; ?> (আনলিমিটেড)</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $status_class = '';
                                            $status_text = '';
                                            switch ($link['status']) {
                                                case 'active':
                                                    $status_class = 'bg-success';
                                                    $status_text = 'সক্রিয়';
                                                    break;
                                                case 'inactive':
                                                    $status_class = 'bg-secondary';
                                                    $status_text = 'নিষ্ক্রিয়';
                                                    break;
                                                case 'expired':
                                                    $status_class = 'bg-warning';
                                                    $status_text = 'মেয়াদ শেষ';
                                                    break;
                                                case 'limit_reached':
                                                    $status_class = 'bg-danger';
                                                    $status_text = 'লিমিট শেষ';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                        </td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($link['created_at'])); ?></td>
                                        <td>
                                            <div class="btn-group">
                                                <button class="btn btn-sm btn-outline-primary" onclick="toggleStatus(<?php echo $link['id']; ?>, <?php echo $link['is_active'] ? 0 : 1; ?>)">
                                                    <i class="fas fa-<?php echo $link['is_active'] ? 'pause' : 'play'; ?>"></i>
                                                </button>
                                                <a href="<?php echo SITE_URL; ?>/share/<?php echo $link['link_token']; ?>" class="btn btn-sm btn-outline-success" target="_blank">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteLink(<?php echo $link['id']; ?>)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-share-alt fa-3x text-muted mb-3"></i>
                            <h5>কোনো শেয়ার লিংক নেই</h5>
                            <p class="text-muted">উপরের ফর্ম ব্যবহার করে নতুন শেয়ার লিংক তৈরি করুন।</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden forms for actions -->
<form id="toggleStatusForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="toggle_status">
    <input type="hidden" name="link_id" id="toggleLinkId">
    <input type="hidden" name="new_status" id="toggleNewStatus">
</form>

<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="delete">
    <input type="hidden" name="link_id" id="deleteLinkId">
</form>

<script>
// Load content based on type
function loadContent(type) {
    const contentSelect = document.getElementById('contentSelect');
    contentSelect.innerHTML = '<option value="">লোড হচ্ছে...</option>';
    
    if (!type) {
        contentSelect.innerHTML = '<option value="">প্রথমে কন্টেন্ট টাইপ নির্বাচন করুন</option>';
        return;
    }
    
    fetch(`ajax/get_premium_content.php?type=${type}`)
        .then(response => response.json())
        .then(data => {
            contentSelect.innerHTML = '<option value="">নির্বাচন করুন</option>';
            data.forEach(item => {
                contentSelect.innerHTML += `<option value="${item.id}">${item.title}</option>`;
            });
        })
        .catch(error => {
            contentSelect.innerHTML = '<option value="">লোড করতে সমস্যা হয়েছে</option>';
        });
}

// Copy to clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        alert('লিংক কপি হয়েছে!');
    });
}

// Toggle status
function toggleStatus(linkId, newStatus) {
    document.getElementById('toggleLinkId').value = linkId;
    document.getElementById('toggleNewStatus').value = newStatus;
    document.getElementById('toggleStatusForm').submit();
}

// Delete link
function deleteLink(linkId) {
    if (confirm('আপনি কি নিশ্চিত যে এই শেয়ার লিংকটি ডিলিট করতে চান?')) {
        document.getElementById('deleteLinkId').value = linkId;
        document.getElementById('deleteForm').submit();
    }
}
</script>

<?php require_once 'includes/footer.php'; ?>
