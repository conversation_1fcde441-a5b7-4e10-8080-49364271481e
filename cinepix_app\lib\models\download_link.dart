class DownloadLink {
  final int id;
  final String quality;
  final String url;
  final String serverName;
  final String fileSize;
  final bool isPremium;
  final int? duration; // Duration in seconds

  DownloadLink({
    required this.id,
    required this.quality,
    required this.url,
    required this.serverName,
    required this.fileSize,
    required this.isPremium,
    this.duration,
  });

  factory DownloadLink.fromJson(Map<String, dynamic> json) {
    return DownloadLink(
      id: json['id'] ?? 0,
      quality: json['quality'] ?? '',
      url: json['url'] ?? '',
      serverName: json['server_name'] ?? '',
      fileSize: json['file_size'] ?? '',
      isPremium: json['is_premium'] ?? false,
      duration: json['duration'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'quality': quality,
      'url': url,
      'server_name': serverName,
      'file_size': fileSize,
      'is_premium': isPremium,
      'duration': duration,
    };
  }
}
