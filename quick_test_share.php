<?php
require_once 'includes/config.php';

echo "<h2>দ্রুত শেয়ার লিংক টেস্ট</h2>";

// Check database connection
if (!$conn) {
    echo "❌ ডেটাবেস কানেকশন ব্যর্থ";
    exit;
}

// Check if shared_links table exists
$check_table = mysqli_query($conn, "SHOW TABLES LIKE 'shared_links'");
if (mysqli_num_rows($check_table) == 0) {
    echo "❌ shared_links টেবিল নেই";
    exit;
}

// Check if movies table has data
$check_movies = mysqli_query($conn, "SELECT COUNT(*) as total FROM movies");
$total_movies = mysqli_fetch_assoc($check_movies)['total'];

if ($total_movies == 0) {
    echo "❌ movies টেবিলে কোনো ডেটা নেই";
    exit;
}

// Get first movie
$movie_query = mysqli_query($conn, "SELECT id, title FROM movies LIMIT 1");
$movie = mysqli_fetch_assoc($movie_query);

echo "✅ মুভি পাওয়া গেছে: {$movie['title']}<br>";

// Create a simple shared link
$link_token = 'test_' . time() . '_' . rand(1000, 9999);
$content_type = 'movie';
$content_id = $movie['id'];
$title = "টেস্ট - {$movie['title']}";
$description = "দ্রুত টেস্ট শেয়ার লিংক";
$access_limit = 5;
$expires_at = date('Y-m-d H:i:s', strtotime('+1 day'));
$allow_download = 1;
$allow_streaming = 1;
$is_active = 1;

// Insert the shared link
$insert_query = "INSERT INTO shared_links (link_token, content_type, content_id, created_by, title, description, access_limit, expires_at, allow_download, allow_streaming, password, is_active) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)";

$stmt = mysqli_prepare($conn, $insert_query);
mysqli_stmt_bind_param($stmt, 'ssiissisiis', $link_token, $content_type, $content_id, 1, $title, $description, $access_limit, $expires_at, $allow_download, $allow_streaming, $is_active);

if (mysqli_stmt_execute($stmt)) {
    echo "✅ শেয়ার লিংক তৈরি হয়েছে<br>";
    
    // Test the link immediately
    $share_url = SITE_URL . '/share.php?token=' . $link_token;
    $debug_url = SITE_URL . '/share.php?token=' . $link_token . '&debug=1';
    
    echo "<h3>শেয়ার লিংক:</h3>";
    echo "<p><strong>টোকেন:</strong> $link_token</p>";
    echo "<p><strong>সাধারণ লিংক:</strong> <a href='$share_url' target='_blank'>$share_url</a></p>";
    echo "<p><strong>ডিবাগ লিংক:</strong> <a href='$debug_url' target='_blank'>$debug_url</a></p>";
    
    // Test the query directly
    echo "<h3>কুয়েরি টেস্ট:</h3>";
    $test_query = "SELECT sl.*, 
                          CASE 
                              WHEN sl.content_type = 'movie' THEN m.title
                              WHEN sl.content_type = 'tvshow' THEN t.title
                          END as content_title
                   FROM shared_links sl
                   LEFT JOIN movies m ON sl.content_type = 'movie' AND sl.content_id = m.id
                   LEFT JOIN tvshows t ON sl.content_type = 'tvshow' AND sl.content_id = t.id
                   WHERE sl.link_token = '$link_token' AND sl.is_active = 1";
    
    $test_result = mysqli_query($conn, $test_query);
    
    if (!$test_result) {
        echo "❌ কুয়েরি ফেইল: " . mysqli_error($conn) . "<br>";
    } else {
        $num_rows = mysqli_num_rows($test_result);
        echo "✅ কুয়েরি সফল। ফলাফল: $num_rows সারি<br>";
        
        if ($num_rows > 0) {
            $test_link = mysqli_fetch_assoc($test_result);
            echo "<div style='background: #e8f5e8; padding: 10px; margin: 10px 0; border: 1px solid #4caf50;'>";
            echo "<strong>শেয়ার লিংক তথ্য:</strong><br>";
            echo "ID: {$test_link['id']}<br>";
            echo "টাইটেল: {$test_link['title']}<br>";
            echo "কন্টেন্ট টাইপ: {$test_link['content_type']}<br>";
            echo "কন্টেন্ট ID: {$test_link['content_id']}<br>";
            echo "কন্টেন্ট টাইটেল: {$test_link['content_title']}<br>";
            echo "এক্সেস কাউন্ট: {$test_link['access_count']}<br>";
            echo "এক্সেস লিমিট: {$test_link['access_limit']}<br>";
            echo "মেয়াদ: {$test_link['expires_at']}<br>";
            echo "সক্রিয়: " . ($test_link['is_active'] ? 'হ্যাঁ' : 'না') . "<br>";
            echo "</div>";
        }
    }
    
    echo "<h3>পরবর্তী ধাপ:</h3>";
    echo "<ol>";
    echo "<li>ডিবাগ লিংকে ক্লিক করুন</li>";
    echo "<li>ডিবাগ তথ্য দেখুন</li>";
    echo "<li>যদি সব ঠিক থাকে তবে সাধারণ লিংকে ক্লিক করুন</li>";
    echo "</ol>";
    
} else {
    echo "❌ শেয়ার লিংক তৈরি করতে সমস্যা হয়েছে: " . mysqli_error($conn) . "<br>";
}

// Clean up old test links (optional)
$cleanup_query = "DELETE FROM shared_links WHERE link_token LIKE 'test_%' AND created_at < DATE_SUB(NOW(), INTERVAL 1 HOUR)";
mysqli_query($conn, $cleanup_query);
?> 