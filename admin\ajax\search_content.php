<?php
require_once '../../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    exit(json_encode([]));
}

// Get search parameters
$type = $_POST['type'] ?? '';
$query = $_POST['query'] ?? '';

if (!$type || !$query) {
    exit(json_encode([]));
}

// Initialize results array
$results = [];

// Search in movies table
if ($type === 'movie') {
    $sql = "SELECT id, title FROM movies 
            WHERE title LIKE ? 
            AND premium_only = 1 
            LIMIT 10";
    $stmt = mysqli_prepare($conn, $sql);
    $search_term = "%$query%";
    mysqli_stmt_bind_param($stmt, 's', $search_term);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    while ($row = mysqli_fetch_assoc($result)) {
        $results[] = [
            'id' => $row['id'],
            'title' => $row['title']
        ];
    }
}

// Search in tvshows table
if ($type === 'tvshow') {
    $sql = "SELECT id, title FROM tvshows 
            WHERE title LIKE ? 
            AND premium_only = 1 
            LIMIT 10";
    $stmt = mysqli_prepare($conn, $sql);
    $search_term = "%$query%";
    mysqli_stmt_bind_param($stmt, 's', $search_term);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    while ($row = mysqli_fetch_assoc($result)) {
        $results[] = [
            'id' => $row['id'],
            'title' => $row['title']
        ];
    }
}

// Return results as JSON
echo json_encode($results);
