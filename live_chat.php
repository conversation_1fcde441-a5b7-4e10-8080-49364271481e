<?php
// Set page title
$page_title = 'Live Chat';

require_once 'includes/header.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('login.php');
}

// Process form submissions
$success_message = '';
$error_message = '';

// Check if chat_sessions and chat_messages tables exist
$check_tables_query = "SHOW TABLES LIKE 'chat_sessions'";
$check_tables_result = mysqli_query($conn, $check_tables_query);

if (mysqli_num_rows($check_tables_result) == 0) {
    // Create chat_sessions table
    $create_sessions_table = "CREATE TABLE IF NOT EXISTS chat_sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        status ENUM('active', 'closed') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    mysqli_query($conn, $create_sessions_table);
    
    // Create chat_messages table
    $create_messages_table = "CREATE TABLE IF NOT EXISTS chat_messages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        session_id INT NOT NULL,
        sender_id INT NOT NULL,
        receiver_id INT NOT NULL,
        message TEXT NOT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE,
        FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    mysqli_query($conn, $create_messages_table);
}

// Get user ID
$user_id = $_SESSION['user_id'];

// Check if user has an active chat session
$session_query = "SELECT * FROM chat_sessions WHERE user_id = $user_id AND status = 'active'";
$session_result = mysqli_query($conn, $session_query);

$session_id = 0;
if (mysqli_num_rows($session_result) > 0) {
    $session = mysqli_fetch_assoc($session_result);
    $session_id = $session['id'];
} else {
    // Create new chat session
    $create_session_query = "INSERT INTO chat_sessions (user_id) VALUES ($user_id)";
    if (mysqli_query($conn, $create_session_query)) {
        $session_id = mysqli_insert_id($conn);
    }
}

// Send message
if (isset($_POST['send_message'])) {
    $message = sanitize($_POST['message']);
    
    if (empty($message)) {
        $error_message = 'মেসেজ খালি রাখা যাবে না।';
    } else {
        // Get admin ID (assuming admin ID is 1, adjust as needed)
        $admin_query = "SELECT id FROM users WHERE role = 'admin' LIMIT 1";
        $admin_result = mysqli_query($conn, $admin_query);
        $admin_id = 1; // Default admin ID
        
        if (mysqli_num_rows($admin_result) > 0) {
            $admin = mysqli_fetch_assoc($admin_result);
            $admin_id = $admin['id'];
        }
        
        // Insert message
        $insert_query = "INSERT INTO chat_messages (session_id, sender_id, receiver_id, message) 
                        VALUES ($session_id, $user_id, $admin_id, '$message')";
        
        if (mysqli_query($conn, $insert_query)) {
            // Update session timestamp
            mysqli_query($conn, "UPDATE chat_sessions SET updated_at = NOW() WHERE id = $session_id");
            
            // Redirect to avoid form resubmission
            redirect("live_chat.php");
        } else {
            $error_message = 'মেসেজ পাঠাতে সমস্যা হয়েছে: ' . mysqli_error($conn);
        }
    }
}

// Mark messages as read
$mark_read_query = "UPDATE chat_messages SET is_read = TRUE 
                   WHERE session_id = $session_id AND receiver_id = $user_id AND is_read = FALSE";
mysqli_query($conn, $mark_read_query);

// Get messages
$messages_query = "SELECT cm.*, 
                  u_sender.username as sender_username, 
                  u_sender.profile_image as sender_image,
                  u_sender.role as sender_role
                  FROM chat_messages cm
                  JOIN users u_sender ON cm.sender_id = u_sender.id
                  WHERE cm.session_id = $session_id
                  ORDER BY cm.created_at ASC";
$messages_result = mysqli_query($conn, $messages_query);

$messages = [];
while ($message = mysqli_fetch_assoc($messages_result)) {
    $messages[] = $message;
}

// Get admin info
$admin_info_query = "SELECT username, profile_image FROM users WHERE role = 'admin' LIMIT 1";
$admin_info_result = mysqli_query($conn, $admin_info_query);
$admin_info = mysqli_fetch_assoc($admin_info_result);
?>

<div class="container mt-4 mb-5">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex align-items-center">
                        <img src="<?php echo !empty($admin_info['profile_image']) ? SITE_URL . '/uploads/' . $admin_info['profile_image'] : SITE_URL . '/assets/img/default-avatar.png'; ?>" class="rounded-circle me-2" width="40" height="40" alt="Admin">
                        <div>
                            <h5 class="mb-0"><?php echo $admin_info['username'] ?? 'Admin'; ?></h5>
                            <small>অনলাইন</small>
                        </div>
                    </div>
                </div>
                
                <div class="card-body chat-messages" style="height: 400px; overflow-y: auto;">
                    <?php if(empty($messages)): ?>
                    <div class="text-center p-4">
                        <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                        <p>আমাদের সাথে চ্যাট করুন। আমরা আপনাকে সাহায্য করতে এখানে আছি।</p>
                    </div>
                    <?php else: ?>
                        <?php foreach($messages as $message): ?>
                            <?php $is_user = ($message['sender_id'] == $user_id); ?>
                            <div class="d-flex mb-3 <?php echo $is_user ? 'justify-content-end' : ''; ?>">
                                <?php if(!$is_user): ?>
                                <div class="flex-shrink-0 me-2">
                                    <img src="<?php echo !empty($message['sender_image']) ? SITE_URL . '/uploads/' . $message['sender_image'] : SITE_URL . '/assets/img/default-avatar.png'; ?>" class="rounded-circle" width="32" height="32" alt="<?php echo $message['sender_username']; ?>">
                                </div>
                                <?php endif; ?>
                                <div class="<?php echo $is_user ? 'chat-message-user' : 'chat-message-admin'; ?>" style="max-width: 70%; background-color: <?php echo $is_user ? '#dcf8c6' : '#f1f1f1'; ?>; padding: 10px 15px; border-radius: 15px; position: relative;">
                                    <div class="message-text"><?php echo nl2br($message['message']); ?></div>
                                    <div class="message-time text-muted" style="font-size: 0.7rem;">
                                        <?php echo date('h:i A', strtotime($message['created_at'])); ?>
                                        <?php if($is_user && $message['is_read']): ?>
                                        <i class="fas fa-check-double ms-1 text-primary"></i>
                                        <?php elseif($is_user): ?>
                                        <i class="fas fa-check ms-1"></i>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php if($is_user): ?>
                                <div class="flex-shrink-0 ms-2">
                                    <img src="<?php echo !empty($_SESSION['profile_image']) ? SITE_URL . '/uploads/' . $_SESSION['profile_image'] : SITE_URL . '/assets/img/default-avatar.png'; ?>" class="rounded-circle" width="32" height="32" alt="<?php echo $_SESSION['username']; ?>">
                                </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                
                <div class="card-footer bg-white">
                    <form method="POST" action="" class="needs-validation" novalidate>
                        <div class="input-group">
                            <textarea class="form-control" name="message" placeholder="আপনার মেসেজ লিখুন..." rows="1" required></textarea>
                            <button type="submit" name="send_message" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">সাধারণ প্রশ্ন</h5>
                </div>
                <div class="card-body">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faqOne">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="false" aria-controls="collapseOne">
                                    প্রিমিয়াম সাবস্ক্রিপশন কিভাবে কিনব?
                                </button>
                            </h2>
                            <div id="collapseOne" class="accordion-collapse collapse" aria-labelledby="faqOne" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    প্রিমিয়াম সাবস্ক্রিপশন কিনতে, আপনার প্রোফাইল পেজে যান এবং "প্রিমিয়াম কিনুন" বাটনে ক্লিক করুন। তারপর আপনার পছন্দের প্যাকেজ নির্বাচন করুন এবং পেমেন্ট সম্পূর্ণ করুন।
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faqTwo">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                    ভিডিও স্ট্রিমিং করতে সমস্যা হচ্ছে?
                                </button>
                            </h2>
                            <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="faqTwo" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    ভিডিও স্ট্রিমিং সমস্যা সমাধানের জন্য, আপনার ইন্টারনেট সংযোগ পরীক্ষা করুন, ব্রাউজার ক্যাশে পরিষ্কার করুন, অথবা অন্য সার্ভার বা কোয়ালিটি অপশন ব্যবহার করে দেখুন।
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faqThree">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                    পেমেন্ট করার পরও প্রিমিয়াম কন্টেন্ট দেখতে পারছি না
                                </button>
                            </h2>
                            <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="faqThree" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    পেমেন্ট করার পর প্রিমিয়াম কন্টেন্ট দেখতে না পারলে, আপনার পেমেন্ট স্ট্যাটাস চেক করুন। ম্যানুয়াল পেমেন্টের ক্ষেত্রে, অ্যাডমিন কর্তৃক অনুমোদনের জন্য অপেক্ষা করতে হতে পারে। আরও সাহায্যের জন্য আমাদের সাথে চ্যাট করুন।
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-scroll to bottom of chat messages
document.addEventListener('DOMContentLoaded', function() {
    const chatMessages = document.querySelector('.chat-messages');
    if (chatMessages) {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    // Auto-resize textarea
    const textarea = document.querySelector('textarea[name="message"]');
    if (textarea) {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
    }
    
    // Auto-refresh chat (every 10 seconds)
    setInterval(function() {
        fetch(window.location.href)
            .then(response => response.text())
            .then(html => {
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const newMessages = doc.querySelector('.chat-messages').innerHTML;
                
                const currentMessages = document.querySelector('.chat-messages');
                if (currentMessages.innerHTML !== newMessages) {
                    currentMessages.innerHTML = newMessages;
                    currentMessages.scrollTop = currentMessages.scrollHeight;
                }
            });
    }, 10000);
});
</script>

<?php
require_once 'includes/footer.php';
?>
