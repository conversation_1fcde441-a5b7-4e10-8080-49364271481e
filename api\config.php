<?php
// API Configuration File

// Include standalone configuration instead of main configuration
require_once __DIR__ . '/standalone_config.php';

// API Settings
define('API_VERSION', 'v1');
define('API_KEY_LENGTH', 32);
define('JWT_SECRET', 'cinepix_secure_jwt_secret_key_2025'); // Secure JWT secret key
define('JWT_EXPIRY', 604800); // 7 days in seconds

// CORS Headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization, API-Key');
header('Content-Type: application/json; charset=UTF-8');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// API Response Functions
function api_response($data, $status_code = 200, $message = 'Success') {
    http_response_code($status_code);
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => $data,
        'meta' => isset($data['meta']) ? $data['meta'] : null
    ]);
    exit;
}

function api_error($message, $status_code = 400) {
    http_response_code($status_code);
    echo json_encode([
        'success' => false,
        'message' => $message,
        'data' => null,
        'meta' => null
    ]);
    exit;
}

// API Authentication Functions
function generate_api_key() {
    return bin2hex(random_bytes(API_KEY_LENGTH / 2));
}

function verify_api_key($api_key) {
    global $conn;

    $query = "SELECT * FROM api_keys WHERE api_key = ? AND status = 'active'";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 's', $api_key);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if (mysqli_num_rows($result) > 0) {
        // Update last used timestamp
        $update_query = "UPDATE api_keys SET last_used = NOW(), usage_count = usage_count + 1 WHERE api_key = ?";
        $update_stmt = mysqli_prepare($conn, $update_query);
        mysqli_stmt_bind_param($update_stmt, 's', $api_key);
        mysqli_stmt_execute($update_stmt);
        mysqli_stmt_close($update_stmt);

        return true;
    }

    return false;
}

// JWT Functions
function generate_jwt($user_id, $username, $role) {
    $issued_at = time();
    $expiry = $issued_at + JWT_EXPIRY;

    $payload = [
        'iss' => SITE_URL, // Issuer
        'aud' => SITE_URL, // Audience
        'iat' => $issued_at, // Issued at
        'exp' => $expiry, // Expiry
        'user_id' => $user_id,
        'username' => $username,
        'role' => $role
    ];

    $header = [
        'alg' => 'HS256',
        'typ' => 'JWT'
    ];

    $header_encoded = base64_encode(json_encode($header));
    $payload_encoded = base64_encode(json_encode($payload));

    $signature = hash_hmac('sha256', "$header_encoded.$payload_encoded", JWT_SECRET, true);
    $signature_encoded = base64_encode($signature);

    return "$header_encoded.$payload_encoded.$signature_encoded";
}

function verify_jwt($token) {
    $token_parts = explode('.', $token);

    if (count($token_parts) !== 3) {
        return false;
    }

    list($header_encoded, $payload_encoded, $signature_encoded) = $token_parts;

    $signature = base64_decode($signature_encoded);
    $expected_signature = hash_hmac('sha256', "$header_encoded.$payload_encoded", JWT_SECRET, true);

    if (!hash_equals($expected_signature, $signature)) {
        return false;
    }

    $payload = json_decode(base64_decode($payload_encoded), true);

    if ($payload['exp'] < time()) {
        return false; // Token expired
    }

    return $payload;
}

// Check if API tables exist and create them if they don't
function ensure_api_tables_exist() {
    global $conn;

    // Check if api_keys table exists
    $check_api_keys_table = "SHOW TABLES LIKE 'api_keys'";
    $result = mysqli_query($conn, $check_api_keys_table);

    if (mysqli_num_rows($result) == 0) {
        // Create api_keys table
        $create_api_keys_table = "CREATE TABLE api_keys (
            id INT AUTO_INCREMENT PRIMARY KEY,
            api_key VARCHAR(64) NOT NULL UNIQUE,
            name VARCHAR(100) NOT NULL,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_used TIMESTAMP NULL,
            usage_count INT DEFAULT 0,
            rate_limit INT DEFAULT 1000 COMMENT 'Requests per day'
        )";

        mysqli_query($conn, $create_api_keys_table);
    }

    // Check if api_logs table exists
    $check_api_logs_table = "SHOW TABLES LIKE 'api_logs'";
    $result = mysqli_query($conn, $check_api_logs_table);

    if (mysqli_num_rows($result) == 0) {
        // Create api_logs table
        $create_api_logs_table = "CREATE TABLE api_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            api_key VARCHAR(64),
            endpoint VARCHAR(255) NOT NULL,
            method VARCHAR(10) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT,
            request_data TEXT,
            response_code INT,
            execution_time FLOAT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX (api_key),
            INDEX (endpoint),
            INDEX (created_at)
        )";

        mysqli_query($conn, $create_api_logs_table);
    }
}

// Log API request
function log_api_request($api_key, $endpoint, $method, $request_data, $response_code, $start_time) {
    global $conn;

    $ip_address = $_SERVER['REMOTE_ADDR'];
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $execution_time = microtime(true) - $start_time;
    $request_data_json = json_encode($request_data);

    $query = "INSERT INTO api_logs (api_key, endpoint, method, ip_address, user_agent, request_data, response_code, execution_time)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'ssssssid', $api_key, $endpoint, $method, $ip_address, $user_agent, $request_data_json, $response_code, $execution_time);
    mysqli_stmt_execute($stmt);
    mysqli_stmt_close($stmt);
}

// Ensure API tables exist
ensure_api_tables_exist();

// Start timing for API request logging
$api_request_start_time = microtime(true);

// Get API key from header
$api_key = $_SERVER['HTTP_API_KEY'] ?? '';

// Verify API key (except for auth endpoints)
$current_endpoint = $_SERVER['REQUEST_URI'];
if (!strpos($current_endpoint, '/api/v1/auth/') && !verify_api_key($api_key)) {
    api_error('Invalid or missing API key', 401);
}

// Get JWT token from Authorization header
$auth_header = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
$token = null;

if (preg_match('/Bearer\s(\S+)/', $auth_header, $matches)) {
    $token = $matches[1];
}

// Function to check if user is authenticated
function is_authenticated() {
    global $token;

    if (!$token) {
        return false;
    }

    $payload = verify_jwt($token);
    return $payload !== false;
}

// Function to get authenticated user data
function get_authenticated_user() {
    global $token;

    if (!$token) {
        return null;
    }

    return verify_jwt($token);
}

// Function to check if user has admin role
function is_admin() {
    $user = get_authenticated_user();

    if (!$user) {
        return false;
    }

    return $user['role'] === 'admin';
}
