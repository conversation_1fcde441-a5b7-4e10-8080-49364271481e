<?php
require_once 'config.php';

// Get search query
$query = isset($_GET['query']) ? sanitize($_GET['query']) : '';

// Validate query
if (empty($query) || strlen($query) < 3) {
    echo json_encode(['success' => false, 'message' => 'Query too short']);
    exit;
}

// Search movies and TV shows
$search_query = "SELECT 'movie' as type, id, title, poster FROM movies 
                WHERE title LIKE '%$query%' 
                UNION 
                SELECT 'tvshow' as type, id, title, poster FROM tvshows 
                WHERE title LIKE '%$query%' 
                ORDER BY title 
                LIMIT 5";
$search_result = mysqli_query($conn, $search_query);

$results = [];
while ($row = mysqli_fetch_assoc($search_result)) {
    $results[] = [
        'id' => $row['id'],
        'type' => $row['type'],
        'title' => $row['title'],
        'poster' => $row['poster'],
        'url' => SITE_URL . '/details.php?type=' . $row['type'] . '&id=' . $row['id']
    ];
}

echo json_encode(['success' => true, 'results' => $results]);
?>
