<?php
// Database connection
$host = 'localhost';
$username = 'tipsbdxy_4525';
$password = '@mdsrabon13';
$database = 'tipsbdxy_4525';

$conn = mysqli_connect($host, $username, $password, $database);

if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// Check if premium_plans table exists
$check_table_query = "SHOW TABLES LIKE 'premium_plans'";
$check_table_result = mysqli_query($conn, $check_table_query);

if (mysqli_num_rows($check_table_result) == 0) {
    echo "The premium_plans table does not exist!";
    exit;
}

// Get table structure
echo "<h2>Premium Plans Table Structure:</h2>";
$structure_query = "DESCRIBE premium_plans";
$structure_result = mysqli_query($conn, $structure_query);

if ($structure_result) {
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = mysqli_fetch_assoc($structure_result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "Error getting table structure: " . mysqli_error($conn);
}

// Get existing plans
echo "<h2>Existing Premium Plans:</h2>";
$plans_query = "SELECT * FROM premium_plans ORDER BY price";
$plans_result = mysqli_query($conn, $plans_query);

if (mysqli_num_rows($plans_result) > 0) {
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>ID</th><th>Name</th><th>Price</th><th>Duration (days)</th><th>Features</th><th>Created At</th></tr>";
    
    while ($plan = mysqli_fetch_assoc($plans_result)) {
        echo "<tr>";
        echo "<td>" . $plan['id'] . "</td>";
        echo "<td>" . $plan['name'] . "</td>";
        echo "<td>৳" . $plan['price'] . "</td>";
        echo "<td>" . $plan['duration'] . "</td>";
        echo "<td><pre>" . $plan['features'] . "</pre></td>";
        echo "<td>" . $plan['created_at'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "No premium plans found.";
}

// Add a simple form to add a new plan
echo "<h2>Add New Premium Plan:</h2>";
?>

<form method="POST" action="">
    <table>
        <tr>
            <td>Name:</td>
            <td><input type="text" name="name" required></td>
        </tr>
        <tr>
            <td>Price (৳):</td>
            <td><input type="number" name="price" min="1" required></td>
        </tr>
        <tr>
            <td>Duration (days):</td>
            <td><input type="number" name="duration" min="1" required></td>
        </tr>
        <tr>
            <td>Features (one per line):</td>
            <td><textarea name="features" rows="5" cols="40"></textarea></td>
        </tr>
        <tr>
            <td colspan="2"><input type="submit" value="Add Plan"></td>
        </tr>
    </table>
</form>

<?php
// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = mysqli_real_escape_string($conn, $_POST['name']);
    $price = (float)$_POST['price'];
    $duration = (int)$_POST['duration'];
    $features = mysqli_real_escape_string($conn, $_POST['features']);
    
    // Insert the new plan
    $query = "INSERT INTO premium_plans (name, price, duration, features) 
              VALUES ('$name', $price, $duration, '$features')";
    
    if (mysqli_query($conn, $query)) {
        echo "<p style='color:green;'>New premium plan added successfully!</p>";
        echo "<script>window.location.reload();</script>";
    } else {
        echo "<p style='color:red;'>Error adding premium plan: " . mysqli_error($conn) . "</p>";
    }
}

mysqli_close($conn);
?>
