/* Custom styles for admin dashboard */

/* Icon Circle */
.icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bg-primary-light {
    background-color: rgba(99, 102, 241, 0.1);
}

.bg-success-light {
    background-color: rgba(34, 197, 94, 0.1);
}

.bg-info-light {
    background-color: rgba(14, 165, 233, 0.1);
}

.bg-warning-light {
    background-color: rgba(245, 158, 11, 0.1);
}

.bg-danger-light {
    background-color: rgba(239, 68, 68, 0.1);
}

/* Card styles */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: var(--card-shadow);
    transition: all 0.3s;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: translateY(-5px);
}

.card-header {
    background-color: var(--bg-light);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: var(--bg-light);
    border-top: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
}

/* Welcome card */
.bg-gradient-primary {
    background-color: var(--primary-color);
    background-image: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    background-size: cover;
    color: white;
    border-radius: 1rem;
    box-shadow: 0 10px 15px -3px rgba(99, 102, 241, 0.3);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: var(--text-dark);
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
    border-radius: 0.5rem;
}

/* Buttons */
.btn {
    border-radius: 0.5rem;
    padding: 0.6rem 1.2rem;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.3s;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Responsive fixes */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .card-header, .card-footer {
        padding: 0.75rem 1rem;
    }
    
    .topbar {
        padding: 0 15px;
    }
}
