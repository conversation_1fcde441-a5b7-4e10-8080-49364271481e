<?php
// Include direct config file
require_once '../direct_config.php';

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed. Use POST.',
        'data' => null
    ]);
    exit;
}

// Get JSON data from request body
$json_data = file_get_contents('php://input');
$data = json_decode($json_data, true);

// Check if data is valid JSON
if ($data === null) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Invalid JSON data.',
        'data' => null
    ]);
    exit;
}

// Check if api_logs table exists
$check_table = "SHOW TABLES LIKE 'api_logs'";
$result = mysqli_query($conn, $check_table);

if (mysqli_num_rows($result) == 0) {
    // Create api_logs table
    $create_table = "CREATE TABLE IF NOT EXISTS api_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        endpoint VARCHAR(255) NOT NULL,
        method VARCHAR(10) NOT NULL,
        user_id INT NULL,
        ip_address VARCHAR(45) NOT NULL,
        user_agent TEXT NULL,
        status_code INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user (user_id),
        INDEX idx_endpoint (endpoint),
        INDEX idx_created_at (created_at)
    )";
    
    if (!mysqli_query($conn, $create_table)) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to create api_logs table: ' . mysqli_error($conn),
            'data' => null
        ]);
        exit;
    }
}

// Extract data from request
$endpoint = isset($data['endpoint']) ? $data['endpoint'] : '';
$method = isset($data['method']) ? $data['method'] : '';
$user_id = isset($data['user_id']) ? (int)$data['user_id'] : null;
$status_code = isset($data['status_code']) ? (int)$data['status_code'] : 200;

// Get IP address and user agent
$ip_address = $_SERVER['REMOTE_ADDR'];
$user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';

// Validate required fields
if (empty($endpoint) || empty($method)) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Endpoint and method are required.',
        'data' => null
    ]);
    exit;
}

// Insert log into database
$sql = "INSERT INTO api_logs (endpoint, method, user_id, ip_address, user_agent, status_code) 
        VALUES (?, ?, ?, ?, ?, ?)";
$stmt = mysqli_prepare($conn, $sql);

if ($stmt) {
    // Bind parameters
    mysqli_stmt_bind_param($stmt, 'ssissi', $endpoint, $method, $user_id, $ip_address, $user_agent, $status_code);
    
    // Execute statement
    if (mysqli_stmt_execute($stmt)) {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'message' => 'Usage logged successfully.',
            'data' => null
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to log usage: ' . mysqli_error($conn),
            'data' => null
        ]);
    }
    
    // Close statement
    mysqli_stmt_close($stmt);
} else {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to prepare statement: ' . mysqli_error($conn),
        'data' => null
    ]);
}

// Close database connection
mysqli_close($conn);
?>
