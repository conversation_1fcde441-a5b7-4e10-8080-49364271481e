<?php
/**
 * Adstera Configuration File
 * Replace YOUR_PUBLISHER_ID and YOUR_AD_SLOT with your actual values
 */

// Adstera Publisher ID
define('ADSTERA_PUBLISHER_ID', 'ca-pub-YOUR_PUBLISHER_ID');

// Ad Slots
define('ADSTERA_HEADER_SLOT', 'YOUR_HEADER_AD_SLOT');
define('ADSTERA_SIDEBAR_SLOT', 'YOUR_SIDEBAR_AD_SLOT');
define('ADSTERA_CONTENT_SLOT', 'YOUR_CONTENT_AD_SLOT');
define('ADSTERA_FOOTER_SLOT', 'YOUR_FOOTER_AD_SLOT');
define('ADSTERA_MOBILE_SLOT', 'YOUR_MOBILE_AD_SLOT');

// Ad Settings
define('ADSTERA_ENABLED', true);
define('ADSTERA_TEST_MODE', false);

/**
 * Generate Adstera Ad Code
 */
function getAdsteraAd($slot_id, $responsive = true) {
    if (!ADSTERA_ENABLED) {
        return '';
    }
    
    if (ADSTERA_TEST_MODE) {
        return getTestAd($slot_id);
    }
    
    return '
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=' . ADSTERA_PUBLISHER_ID . '"></script>
    <ins class="adsbygoogle"
         style="display:block"
         data-ad-client="' . ADSTERA_PUBLISHER_ID . '"
         data-ad-slot="' . $slot_id . '"
         data-ad-format="auto"
         data-full-width-responsive="' . ($responsive ? 'true' : 'false') . '"></ins>
    <script>
         (adsbygoogle = window.adsbygoogle || []).push({});
    </script>';
}

/**
 * Generate Test Ad
 */
function getTestAd($slot_id) {
    $colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'];
    $color = $colors[array_rand($colors)];
    
    return '
    <div class="test-ad" style="background: ' . $color . '; color: white; padding: 20px; text-align: center; border-radius: 10px; margin: 10px 0;">
        <h4>Test Ad: ' . $slot_id . '</h4>
        <p>This is a test advertisement placeholder.</p>
    </div>';
}

/**
 * Display Ad Banner
 */
function displayAdBanner($slot_id, $position = '') {
    $ad_code = getAdsteraAd($slot_id);
    
    if (empty($ad_code)) {
        return '';
    }
    
    $position_class = $position ? ' ad-banner-' . $position : '';
    
    return '
    <div class="ad-banner' . $position_class . '">
        <div class="container">
            <div class="ad-placeholder">
                <div class="ad-label">Advertisement</div>
                <div class="ad-content">
                    ' . $ad_code . '
                </div>
            </div>
        </div>
    </div>';
}

/**
 * Display Sidebar Ad
 */
function displaySidebarAd($slot_id) {
    $ad_code = getAdsteraAd($slot_id);
    
    if (empty($ad_code)) {
        return '';
    }
    
    return '
    <div class="sidebar-ad">
        <div class="ad-placeholder">
            <div class="ad-label">Advertisement</div>
            <div class="ad-content">
                ' . $ad_code . '
            </div>
        </div>
    </div>';
}

/**
 * Display In-Content Ad
 */
function displayInContentAd($slot_id) {
    $ad_code = getAdsteraAd($slot_id);
    
    if (empty($ad_code)) {
        return '';
    }
    
    return '
    <div class="in-content-ad">
        <div class="ad-placeholder">
            <div class="ad-label">Advertisement</div>
            <div class="ad-content">
                ' . $ad_code . '
            </div>
        </div>
    </div>';
}

/**
 * Ad Blocker Detection Script
 */
function getAdBlockerScript() {
    return '
    <script>
    // Ad Blocker Detection
    function detectAdBlocker() {
        let adBlockEnabled = false;
        const testAd = document.createElement("div");
        testAd.innerHTML = "&nbsp;";
        testAd.className = "adsbox";
        testAd.style.position = "absolute";
        testAd.style.left = "-9999px";
        document.body.appendChild(testAd);
        
        window.setTimeout(function() {
            if (testAd.offsetHeight === 0) {
                adBlockEnabled = true;
            }
            testAd.remove();
            
            if (adBlockEnabled) {
                showAdBlockerMessage();
            }
        }, 100);
    }
    
    function showAdBlockerMessage() {
        const message = document.createElement("div");
        message.innerHTML = `
            <div class="ad-blocker-message" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); z-index: 9999; max-width: 400px; text-align: center;">
                <h3 style="color: #e74c3c; margin-bottom: 15px;">Ad Blocker Detected</h3>
                <p style="color: #666; margin-bottom: 20px;">Please disable your ad blocker to support our site and continue enjoying free content.</p>
                <button onclick="this.parentElement.parentElement.remove()" style="background: #e74c3c; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">Close</button>
            </div>
        `;
        document.body.appendChild(message);
    }
    
    document.addEventListener("DOMContentLoaded", detectAdBlocker);
    </script>';
}

// Include ad blocker detection
echo getAdBlockerScript();
?> 