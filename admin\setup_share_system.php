<?php
$page_title = 'শেয়ার লিংক সিস্টেম সেটআপ';
$current_page = 'setup_share_system.php';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

$setup_complete = false;
$errors = [];
$success_messages = [];

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['setup_tables'])) {
    // Create shared_links table
    $shared_links_query = "CREATE TABLE IF NOT EXISTS shared_links (
        id INT AUTO_INCREMENT PRIMARY KEY,
        link_token VARCHAR(255) NOT NULL UNIQUE,
        title VARCHAR(500) NOT NULL,
        content_type ENUM('movie', 'tvshow') NOT NULL,
        content_id INT NOT NULL,
        access_limit INT DEFAULT 0,
        access_count INT DEFAULT 0,
        password_hash VARCHAR(255) NULL,
        allow_download BOOLEAN DEFAULT TRUE,
        allow_streaming BOOLEAN DEFAULT TRUE,
        expires_at TIMESTAMP NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_link_token (link_token),
        INDEX idx_content (content_type, content_id),
        INDEX idx_is_active (is_active),
        INDEX idx_expires_at (expires_at),
        INDEX idx_created_at (created_at),
        
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
    )";
    
    if (mysqli_query($conn, $shared_links_query)) {
        $success_messages[] = "shared_links টেবিল তৈরি হয়েছে";
    } else {
        $errors[] = "shared_links টেবিল তৈরি করতে সমস্যা: " . mysqli_error($conn);
    }
    
    // Create shared_link_access_logs table
    $access_logs_query = "CREATE TABLE IF NOT EXISTS shared_link_access_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        shared_link_id INT NOT NULL,
        ip_address VARCHAR(45) NOT NULL,
        user_agent TEXT,
        user_id INT NULL,
        device_type VARCHAR(50),
        browser VARCHAR(100),
        access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (shared_link_id) REFERENCES shared_links(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_shared_link_access (shared_link_id, access_time),
        INDEX idx_ip_address (ip_address),
        INDEX idx_access_time (access_time)
    )";
    
    if (mysqli_query($conn, $access_logs_query)) {
        $success_messages[] = "shared_link_access_logs টেবিল তৈরি হয়েছে";
    } else {
        $errors[] = "shared_link_access_logs টেবিল তৈরি করতে সমস্যা: " . mysqli_error($conn);
    }
    
    // Create shared_link_actions table
    $actions_query = "CREATE TABLE IF NOT EXISTS shared_link_actions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        shared_link_id INT NOT NULL,
        action_type ENUM('download', 'stream', 'view') NOT NULL,
        link_url TEXT,
        ip_address VARCHAR(45) NOT NULL,
        user_agent TEXT,
        user_id INT NULL,
        device_type VARCHAR(50),
        browser VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (shared_link_id) REFERENCES shared_links(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_shared_link_action (shared_link_id, action_type),
        INDEX idx_created_at (created_at)
    )";
    
    if (mysqli_query($conn, $actions_query)) {
        $success_messages[] = "shared_link_actions টেবিল তৈরি হয়েছে";
    } else {
        $errors[] = "shared_link_actions টেবিল তৈরি করতে সমস্যা: " . mysqli_error($conn);
    }
    
    // Create share_security_logs table
    $security_logs_query = "CREATE TABLE IF NOT EXISTS share_security_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        event_type VARCHAR(50) NOT NULL,
        ip_address VARCHAR(45) NOT NULL,
        details TEXT,
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_event_type (event_type),
        INDEX idx_ip_address (ip_address),
        INDEX idx_created_at (created_at)
    )";
    
    if (mysqli_query($conn, $security_logs_query)) {
        $success_messages[] = "share_security_logs টেবিল তৈরি হয়েছে";
    } else {
        $errors[] = "share_security_logs টেবিল তৈরি করতে সমস্যা: " . mysqli_error($conn);
    }
    
    // Create blocked_ips table
    $blocked_ips_query = "CREATE TABLE IF NOT EXISTS blocked_ips (
        id INT AUTO_INCREMENT PRIMARY KEY,
        ip_address VARCHAR(45) NOT NULL UNIQUE,
        reason TEXT,
        blocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NULL,
        is_active BOOLEAN DEFAULT TRUE,
        INDEX idx_ip_address (ip_address),
        INDEX idx_is_active (is_active)
    )";
    
    if (mysqli_query($conn, $blocked_ips_query)) {
        $success_messages[] = "blocked_ips টেবিল তৈরি হয়েছে";
    } else {
        $errors[] = "blocked_ips টেবিল তৈরি করতে সমস্যা: " . mysqli_error($conn);
    }
    
    // Create trigger for access count (drop first if exists)
    mysqli_query($conn, "DROP TRIGGER IF EXISTS update_access_count");

    $trigger_query = "CREATE TRIGGER update_access_count
                      AFTER INSERT ON shared_link_access_logs
                      FOR EACH ROW
                      UPDATE shared_links
                      SET access_count = access_count + 1
                      WHERE id = NEW.shared_link_id";

    if (mysqli_query($conn, $trigger_query)) {
        $success_messages[] = "Access count trigger তৈরি হয়েছে";
    } else {
        $errors[] = "Access count trigger তৈরি করতে সমস্যা: " . mysqli_error($conn);
    }
    
    if (empty($errors)) {
        $setup_complete = true;
    }
}

// Check if tables exist
function tableExists($conn, $table_name) {
    $query = "SHOW TABLES LIKE '$table_name'";
    $result = mysqli_query($conn, $query);
    return mysqli_num_rows($result) > 0;
}

$tables_status = [
    'shared_links' => tableExists($conn, 'shared_links'),
    'shared_link_access_logs' => tableExists($conn, 'shared_link_access_logs'),
    'shared_link_actions' => tableExists($conn, 'shared_link_actions'),
    'share_security_logs' => tableExists($conn, 'share_security_logs'),
    'blocked_ips' => tableExists($conn, 'blocked_ips')
];

$all_tables_exist = !in_array(false, $tables_status);

require_once 'includes/header.php';
require_once 'includes/sidebar.php';
?>

<div class="main-content" style="margin-left: 250px; width: calc(100% - 250px); padding: 20px; min-height: 100vh;"">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="page-header">
                    <h1><i class="fas fa-cogs"></i> শেয়ার লিংক সিস্টেম সেটআপ</h1>
                    <p class="text-muted">ডেটাবেস টেবিল তৈরি করুন এবং সিস্টেম সেটআপ করুন</p>
                </div>

                <?php if (!empty($success_messages)): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle"></i>
                    <ul class="mb-0">
                        <?php foreach ($success_messages as $message): ?>
                        <li><?php echo $message; ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if (!empty($errors)): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-circle"></i>
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                        <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if ($setup_complete): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <strong>সেটআপ সম্পূর্ণ!</strong> এখন আপনি শেয়ার লিংক সিস্টেম ব্যবহার করতে পারেন।
                    <div class="mt-3">
                        <a href="shared_links.php" class="btn btn-success me-2">
                            <i class="fas fa-share-alt"></i> শেয়ার লিংক ম্যানেজমেন্ট
                        </a>
                        <a href="share_security.php" class="btn btn-info">
                            <i class="fas fa-shield-alt"></i> সিকিউরিটি মনিটরিং
                        </a>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Tables Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-database"></i> ডেটাবেস টেবিল স্ট্যাটাস</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>টেবিল নাম</th>
                                        <th>স্ট্যাটাস</th>
                                        <th>বিবরণ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>shared_links</td>
                                        <td>
                                            <?php if ($tables_status['shared_links']): ?>
                                                <span class="badge bg-success">বিদ্যমান</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">অনুপস্থিত</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>শেয়ার লিংক তথ্য সংরক্ষণ</td>
                                    </tr>
                                    <tr>
                                        <td>shared_link_access_logs</td>
                                        <td>
                                            <?php if ($tables_status['shared_link_access_logs']): ?>
                                                <span class="badge bg-success">বিদ্যমান</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">অনুপস্থিত</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>এক্সেস লগ সংরক্ষণ</td>
                                    </tr>
                                    <tr>
                                        <td>shared_link_actions</td>
                                        <td>
                                            <?php if ($tables_status['shared_link_actions']): ?>
                                                <span class="badge bg-success">বিদ্যমান</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">অনুপস্থিত</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>ডাউনলোড/স্ট্রিম অ্যাকশন ট্র্যাকিং</td>
                                    </tr>
                                    <tr>
                                        <td>share_security_logs</td>
                                        <td>
                                            <?php if ($tables_status['share_security_logs']): ?>
                                                <span class="badge bg-success">বিদ্যমান</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">অনুপস্থিত</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>সিকিউরিটি ইভেন্ট লগ</td>
                                    </tr>
                                    <tr>
                                        <td>blocked_ips</td>
                                        <td>
                                            <?php if ($tables_status['blocked_ips']): ?>
                                                <span class="badge bg-success">বিদ্যমান</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">অনুপস্থিত</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>ব্লকড IP তালিকা</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Setup Form -->
                <?php if (!$all_tables_exist): ?>
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-play"></i> সেটআপ শুরু করুন</h5>
                    </div>
                    <div class="card-body">
                        <p>শেয়ার লিংক সিস্টেম ব্যবহার করার জন্য প্রয়োজনীয় ডেটাবেস টেবিল তৈরি করুন।</p>
                        
                        <form method="POST">
                            <button type="submit" name="setup_tables" class="btn btn-primary btn-lg" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি সেটআপ শুরু করতে চান?')">
                                <i class="fas fa-cogs"></i> ডেটাবেস সেটআপ করুন
                            </button>
                        </form>
                    </div>
                </div>
                <?php else: ?>
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-check-circle"></i> সেটআপ সম্পূর্ণ</h5>
                    </div>
                    <div class="card-body">
                        <p>সব ডেটাবেস টেবিল সফলভাবে তৈরি হয়েছে। এখন আপনি শেয়ার লিংক সিস্টেম ব্যবহার করতে পারেন।</p>
                        
                        <div class="d-flex gap-2">
                            <a href="shared_links.php" class="btn btn-success">
                                <i class="fas fa-share-alt"></i> শেয়ার লিংক ম্যানেজমেন্ট
                            </a>
                            <a href="share_security.php" class="btn btn-info">
                                <i class="fas fa-shield-alt"></i> সিকিউরিটি মনিটরিং
                            </a>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
