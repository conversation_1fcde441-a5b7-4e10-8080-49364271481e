import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cinepix_app/controllers/movie_controller.dart';
import 'package:cinepix_app/controllers/tv_show_controller.dart';
import 'package:cinepix_app/views/movie_details_screen.dart';
import 'package:cinepix_app/views/tv_show_details_screen.dart';
import 'package:cinepix_app/widgets/movie_card.dart';
import 'package:cinepix_app/widgets/tv_show_card.dart';

class CategoryScreen extends StatefulWidget {
  final int categoryId;
  final String categoryName;
  final String contentType; // 'movie' or 'tvshow'

  const CategoryScreen({
    super.key,
    required this.categoryId,
    required this.categoryName,
    required this.contentType,
  });

  @override
  State<CategoryScreen> createState() => _CategoryScreenState();
}

class _CategoryScreenState extends State<CategoryScreen> {
  final MovieController _movieController = Get.find<MovieController>();
  final TvShowController _tvShowController = Get.find<TvShowController>();

  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadContent();

    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreContent();
    }
  }

  Future<void> _loadContent() async {
    if (widget.contentType == 'movie') {
      await _movieController.loadMoviesByCategory(widget.categoryId);
    } else {
      await _tvShowController.loadTvShowsByCategory(widget.categoryId);
    }
  }

  Future<void> _loadMoreContent() async {
    if (widget.contentType == 'movie') {
      if (_movieController.hasMoreMovies.value &&
          !_movieController.isLoadingCategory.value) {
        await _movieController.loadMoreMoviesByCategory(widget.categoryId);
      }
    } else {
      if (_tvShowController.hasMoreTvShows.value &&
          !_tvShowController.isLoadingCategory.value) {
        await _tvShowController.loadMoreTvShowsByCategory(widget.categoryId);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.categoryName),
      ),
      body: widget.contentType == 'movie'
          ? _buildMoviesGrid()
          : _buildTvShowsGrid(),
    );
  }

  Widget _buildMoviesGrid() {
    return Obx(() {
      if (_movieController.isLoadingCategory.value &&
          _movieController.categoryMovies.isEmpty) {
        return const Center(child: CircularProgressIndicator());
      }

      if (_movieController.categoryMovies.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.movie_outlined,
                size: 64,
                color: Colors.grey[600],
              ),
              const SizedBox(height: 16),
              Text(
                'No movies found in this category',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        );
      }

      return Stack(
        children: [
          GridView.builder(
            controller: _scrollController,
            padding: const EdgeInsets.all(16),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 0.7,
              crossAxisSpacing: 12,
              mainAxisSpacing: 16,
            ),
            itemCount: _movieController.categoryMovies.length,
            itemBuilder: (context, index) {
              final movie = _movieController.categoryMovies[index];

              return MovieCard(
                movie: movie,
                onTap: () {
                  Get.to(() => MovieDetailsScreen(movieId: movie.id));
                },
              );
            },
          ),
          if (_movieController.isLoadingCategory.value)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(16),
                alignment: Alignment.center,
                child: const CircularProgressIndicator(),
              ),
            ),
        ],
      );
    });
  }

  Widget _buildTvShowsGrid() {
    return Obx(() {
      if (_tvShowController.isLoadingCategory.value &&
          _tvShowController.categoryTvShows.isEmpty) {
        return const Center(child: CircularProgressIndicator());
      }

      if (_tvShowController.categoryTvShows.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.tv_outlined,
                size: 64,
                color: Colors.grey[600],
              ),
              const SizedBox(height: 16),
              Text(
                'No TV shows found in this category',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        );
      }

      return Stack(
        children: [
          GridView.builder(
            controller: _scrollController,
            padding: const EdgeInsets.all(16),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 0.7,
              crossAxisSpacing: 12,
              mainAxisSpacing: 16,
            ),
            itemCount: _tvShowController.categoryTvShows.length,
            itemBuilder: (context, index) {
              final tvShow = _tvShowController.categoryTvShows[index];

              return TvShowCard(
                tvShow: tvShow,
                onTap: () {
                  Get.to(() => TvShowDetailsScreen(tvShowId: tvShow.id));
                },
              );
            },
          ),
          if (_tvShowController.isLoadingCategory.value)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(16),
                alignment: Alignment.center,
                child: const CircularProgressIndicator(),
              ),
            ),
        ],
      );
    });
  }
}
