<?php
/**
 * User Activity Tracking API
 * This endpoint receives and processes user activity data
 */

// Include config file
require_once '../../includes/config.php';

// Set headers
header('Content-Type: application/json');

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Handle only POST requests
if ($method !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get request data
$data = json_decode(file_get_contents('php://input'), true);

// If data is not in JSON format, check for form data
if (!$data && isset($_POST['action'])) {
    $data = $_POST;
}

// Validate request data
if (!$data || !isset($data['action']) || !isset($data['session_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid request data']);
    exit;
}

// Sanitize inputs
$action = sanitize($data['action']);
$session_id = sanitize($data['session_id']);
$page_url = isset($data['page_url']) ? sanitize($data['page_url']) : '';
$user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;

// Get IP address
$ip_address = $_SERVER['REMOTE_ADDR'];
if (isset($_SERVER['HTTP_X_FORWARDED_FOR']) && !empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
    $ip_address = $_SERVER['HTTP_X_FORWARDED_FOR'];
}

// Get user agent
$user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';

// Check if user_sessions table exists
$check_table = "SHOW TABLES LIKE 'user_sessions'";
$result = mysqli_query($conn, $check_table);

if (mysqli_num_rows($result) == 0) {
    // Create user_sessions table
    $create_table = "CREATE TABLE IF NOT EXISTS user_sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NULL,
        session_id VARCHAR(255) NOT NULL,
        ip_address VARCHAR(45) NOT NULL,
        user_agent TEXT NULL,
        device_type ENUM('desktop', 'tablet', 'mobile', 'tv', 'unknown') DEFAULT 'unknown',
        browser VARCHAR(50) NULL,
        os VARCHAR(50) NULL,
        page_url VARCHAR(255) NULL,
        referrer_url VARCHAR(255) NULL,
        country VARCHAR(50) NULL,
        city VARCHAR(50) NULL,
        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_session (session_id),
        INDEX idx_last_activity (last_activity)
    )";

    if (!mysqli_query($conn, $create_table)) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create user_sessions table']);
        exit;
    }
}

// Check if page_views table exists
$check_table = "SHOW TABLES LIKE 'page_views'";
$result = mysqli_query($conn, $check_table);

if (mysqli_num_rows($result) == 0) {
    // Create page_views table
    $create_table = "CREATE TABLE IF NOT EXISTS page_views (
        id INT AUTO_INCREMENT PRIMARY KEY,
        session_id VARCHAR(255) NOT NULL,
        user_id INT NULL,
        page_url VARCHAR(255) NOT NULL,
        page_title VARCHAR(255) NULL,
        time_spent INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_session (session_id),
        INDEX idx_page (page_url),
        INDEX idx_created_at (created_at)
    )";

    if (!mysqli_query($conn, $create_table)) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create page_views table']);
        exit;
    }
}

// Check if events table exists
$check_table = "SHOW TABLES LIKE 'events'";
$result = mysqli_query($conn, $check_table);

if (mysqli_num_rows($result) == 0) {
    // Create events table
    $create_table = "CREATE TABLE IF NOT EXISTS events (
        id INT AUTO_INCREMENT PRIMARY KEY,
        session_id VARCHAR(255) NOT NULL,
        user_id INT NULL,
        event_name VARCHAR(100) NOT NULL,
        event_data TEXT NULL,
        page_url VARCHAR(255) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_session (session_id),
        INDEX idx_event (event_name),
        INDEX idx_created_at (created_at)
    )";

    if (!mysqli_query($conn, $create_table)) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create events table']);
        exit;
    }
}

// Process different actions
switch ($action) {
    case 'page_view':
        handlePageView($data, $session_id, $user_id, $ip_address, $user_agent, $conn);
        break;
        
    case 'heartbeat':
        handleHeartbeat($data, $session_id, $user_id, $conn);
        break;
        
    case 'custom_event':
        handleCustomEvent($data, $session_id, $user_id, $conn);
        break;
        
    case 'update_page_time':
        handleUpdatePageTime($data, $session_id, $conn);
        break;
        
    default:
        http_response_code(400);
        echo json_encode(['error' => 'Invalid action']);
        exit;
}

/**
 * Handle page view action
 */
function handlePageView($data, $session_id, $user_id, $ip_address, $user_agent, $conn) {
    $page_url = sanitize($data['page_url']);
    $page_title = isset($data['page_title']) ? sanitize($data['page_title']) : '';
    $referrer = isset($data['referrer']) ? sanitize($data['referrer']) : '';
    
    // Get device info
    $device_info = isset($data['device_info']) ? $data['device_info'] : [];
    $device_type = isset($device_info['deviceType']) ? sanitize($device_info['deviceType']) : 'unknown';
    $browser = isset($device_info['browser']) ? sanitize($device_info['browser']) : '';
    $os = isset($device_info['os']) ? sanitize($device_info['os']) : '';
    
    // Check if session exists
    $check_session = "SELECT id FROM user_sessions WHERE session_id = '$session_id'";
    $result = mysqli_query($conn, $check_session);
    
    if (mysqli_num_rows($result) > 0) {
        // Update existing session
        $update_session = "UPDATE user_sessions SET 
                          user_id = " . ($user_id ? $user_id : "NULL") . ",
                          page_url = '$page_url',
                          last_activity = NOW()
                          WHERE session_id = '$session_id'";
        mysqli_query($conn, $update_session);
    } else {
        // Create new session
        $insert_session = "INSERT INTO user_sessions 
                          (user_id, session_id, ip_address, user_agent, device_type, browser, os, page_url, referrer_url) 
                          VALUES 
                          (" . ($user_id ? $user_id : "NULL") . ", '$session_id', '$ip_address', '$user_agent', '$device_type', '$browser', '$os', '$page_url', '$referrer')";
        mysqli_query($conn, $insert_session);
    }
    
    // Record page view
    $insert_page_view = "INSERT INTO page_views 
                        (session_id, user_id, page_url, page_title) 
                        VALUES 
                        ('$session_id', " . ($user_id ? $user_id : "NULL") . ", '$page_url', '$page_title')";
    mysqli_query($conn, $insert_page_view);
    
    // Return success response
    echo json_encode(['success' => true]);
}

/**
 * Handle heartbeat action
 */
function handleHeartbeat($data, $session_id, $user_id, $conn) {
    $page_url = sanitize($data['page_url']);
    $is_idle = isset($data['is_idle']) ? (bool)$data['is_idle'] : false;
    
    // Update session
    $update_session = "UPDATE user_sessions SET 
                      user_id = " . ($user_id ? $user_id : "NULL") . ",
                      page_url = '$page_url',
                      last_activity = NOW()
                      WHERE session_id = '$session_id'";
    mysqli_query($conn, $update_session);
    
    // Return success response
    echo json_encode(['success' => true]);
}

/**
 * Handle custom event action
 */
function handleCustomEvent($data, $session_id, $user_id, $conn) {
    $event_name = sanitize($data['event_name']);
    $page_url = sanitize($data['page_url']);
    $event_data = isset($data['event_data']) ? json_encode($data['event_data']) : '';
    
    // Record event
    $insert_event = "INSERT INTO events 
                    (session_id, user_id, event_name, event_data, page_url) 
                    VALUES 
                    ('$session_id', " . ($user_id ? $user_id : "NULL") . ", '$event_name', '$event_data', '$page_url')";
    mysqli_query($conn, $insert_event);
    
    // Return success response
    echo json_encode(['success' => true]);
}

/**
 * Handle update page time action
 */
function handleUpdatePageTime($data, $session_id, $conn) {
    $page_url = sanitize($data['page_url']);
    $time_spent = isset($data['time_spent']) ? intval($data['time_spent']) : 0;
    
    // Update page view time
    $update_page_view = "UPDATE page_views SET 
                        time_spent = $time_spent
                        WHERE session_id = '$session_id' 
                        AND page_url = '$page_url'
                        ORDER BY id DESC
                        LIMIT 1";
    mysqli_query($conn, $update_page_view);
    
    // Return success response
    echo json_encode(['success' => true]);
}
?>
