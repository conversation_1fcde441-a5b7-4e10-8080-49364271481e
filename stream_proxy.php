<?php
// Start output buffering to prevent 'headers already sent' errors
ob_start();

// Stream Proxy for handling video streaming
// This script acts as a proxy between the client and the video source

// Set unlimited execution time for large files
set_time_limit(0);

// Increase memory limit
ini_set('memory_limit', '512M');

// Get video URL from query parameter
if (!isset($_GET['url']) || empty($_GET['url'])) {
    header('HTTP/1.1 400 Bad Request');
    exit('URL parameter is required');
}

$url = urldecode($_GET['url']);

// Validate URL
if (!filter_var($url, FILTER_VALIDATE_URL)) {
    header('HTTP/1.1 400 Bad Request');
    exit('Invalid URL');
}

// Get file information
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_NOBODY, true);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
$headers = curl_exec($ch);
$fileSize = curl_getinfo($ch, CURLINFO_CONTENT_LENGTH_DOWNLOAD);
$contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
curl_close($ch);

// Determine content type if not provided
if (empty($contentType) || $contentType == 'application/octet-stream') {
    $extension = pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION);
    switch (strtolower($extension)) {
        case 'mp4':
            $contentType = 'video/mp4';
            break;
        case 'webm':
            $contentType = 'video/webm';
            break;
        case 'mkv':
            $contentType = 'video/x-matroska';
            break;
        case 'avi':
            $contentType = 'video/x-msvideo';
            break;
        case 'mov':
            $contentType = 'video/quicktime';
            break;
        default:
            $contentType = 'video/mp4'; // Default to MP4
    }
}

// Check if range is requested (for seeking)
$range = isset($_SERVER['HTTP_RANGE']) ? $_SERVER['HTTP_RANGE'] : null;

// Set headers for streaming
header('Content-Type: ' . $contentType);
header('Accept-Ranges: bytes');
header('Cache-Control: max-age=86400');
header('Access-Control-Allow-Origin: *');

// Handle range requests
if ($range !== null) {
    // Parse range header
    list($unit, $range) = explode('=', $range, 2);
    if ($unit == 'bytes') {
        // Multiple ranges could be specified at the same time, but we only support a single range
        if (strpos($range, ',') !== false) {
            header('HTTP/1.1 416 Requested Range Not Satisfiable');
            header("Content-Range: bytes */$fileSize");
            exit;
        }

        // Range format: "bytes=0-100", "bytes=-100", "bytes=100-"
        if (preg_match('/(\d*)-(\d*)/', $range, $matches)) {
            $start = $matches[1] === '' ? 0 : intval($matches[1]);
            $end = $matches[2] === '' ? ($fileSize - 1) : intval($matches[2]);

            // Check range validity
            if ($start > $end || $end >= $fileSize) {
                header('HTTP/1.1 416 Requested Range Not Satisfiable');
                header("Content-Range: bytes */$fileSize");
                exit;
            }

            // Set partial content headers
            header('HTTP/1.1 206 Partial Content');
            header("Content-Range: bytes $start-$end/$fileSize");
            header('Content-Length: ' . ($end - $start + 1));
        } else {
            header('HTTP/1.1 416 Requested Range Not Satisfiable');
            header("Content-Range: bytes */$fileSize");
            exit;
        }
    } else {
        header('HTTP/1.1 416 Requested Range Not Satisfiable');
        header("Content-Range: bytes */$fileSize");
        exit;
    }
} else {
    // Full content
    header('Content-Length: ' . $fileSize);
}

// Stream the file
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, false);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($curl, $data) {
    echo $data;
    return strlen($data);
});

// Set range if requested
if ($range !== null) {
    curl_setopt($ch, CURLOPT_RANGE, "$start-$end");
}

// Execute and close
curl_exec($ch);
curl_close($ch);
exit;
?>
