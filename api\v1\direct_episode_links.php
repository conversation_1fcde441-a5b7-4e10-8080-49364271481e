<?php
// Include direct config file
require_once '../direct_config.php';

// Get episode ID from query parameters
$episode_id = isset($_GET['episode_id']) ? (int)$_GET['episode_id'] : 0;

if ($episode_id <= 0) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Episode ID is required',
        'data' => null
    ]);
    exit;
}

// Check if episode exists
$episode_query = "SELECT e.*, t.title as tvshow_title, t.poster as tvshow_poster 
                 FROM episodes e
                 LEFT JOIN tvshows t ON e.tvshow_id = t.id
                 WHERE e.id = $episode_id";
$episode_result = mysqli_query($conn, $episode_query);

if (!$episode_result || mysqli_num_rows($episode_result) === 0) {
    http_response_code(404);
    echo json_encode([
        'success' => false,
        'message' => 'Episode not found',
        'data' => null
    ]);
    exit;
}

$episode = mysqli_fetch_assoc($episode_result);

// Get download links for this episode
// First try episode_links table
$links_query = "SELECT * FROM episode_links
               WHERE episode_id = $episode_id
               ORDER BY quality DESC";

$links_result = mysqli_query($conn, $links_query);

// If no results, try download_links table
if (!$links_result || mysqli_num_rows($links_result) === 0) {
    $links_query = "SELECT * FROM download_links
                   WHERE content_type = 'episode' AND content_id = $episode_id
                   ORDER BY quality DESC";
    $links_result = mysqli_query($conn, $links_query);
}

$download_links = [];

if ($links_result && mysqli_num_rows($links_result) > 0) {
    while ($link = mysqli_fetch_assoc($links_result)) {
        $download_links[] = [
            'id' => (int)$link['id'],
            'quality' => $link['quality'],
            'url' => $link['link_url'] ?? $link['url'] ?? '',
            'server_name' => $link['server_name'] ?? '',
            'file_size' => $link['file_size'] ?? '',
            'is_premium' => (bool)($link['is_premium'] ?? false)
        ];
    }
}

// Return episode with download links
http_response_code(200);
echo json_encode([
    'success' => true,
    'message' => 'Success',
    'data' => [
        'episode' => [
            'id' => (int)$episode['id'],
            'tvshow_id' => (int)$episode['tvshow_id'],
            'season_number' => (int)$episode['season_number'],
            'episode_number' => (int)$episode['episode_number'],
            'title' => $episode['title'],
            'description' => $episode['description'],
            'duration' => (int)$episode['duration'],
            'thumbnail' => $episode['thumbnail'] ? (strpos($episode['thumbnail'], 'http') === 0 ? $episode['thumbnail'] : SITE_URL . '/uploads/' . $episode['thumbnail']) : '',
            'release_date' => $episode['release_date'],
            'is_premium' => (bool)$episode['is_premium'],
            'tvshow_title' => $episode['tvshow_title'],
            'tvshow_poster' => $episode['tvshow_poster'] ? (strpos($episode['tvshow_poster'], 'http') === 0 ? $episode['tvshow_poster'] : SITE_URL . '/uploads/' . $episode['tvshow_poster']) : ''
        ],
        'download_links' => $download_links
    ]
]);
?>
