<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: login.php');
    exit();
}

/**
 * Google Search Console Setup Guide
 * Step-by-step guide for setting up Google Search Console
 */

// Generate verification files if requested
$message = '';
$error = '';

if ($_POST && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'generate_html_verification':
            $verification_code = isset($_POST['verification_code']) ? trim($_POST['verification_code']) : '';
            if ($verification_code) {
                $html_content = 'google-site-verification: ' . $verification_code;
                if (file_put_contents($verification_code, $html_content)) {
                    $message = 'HTML verification file created: ' . SITE_URL . '/' . $verification_code;
                } else {
                    $error = 'Failed to create verification file.';
                }
            } else {
                $error = 'Please enter verification code.';
            }
            break;
            
        case 'generate_meta_tag':
            $meta_code = isset($_POST['meta_code']) ? trim($_POST['meta_code']) : '';
            if ($meta_code) {
                $message = 'Add this meta tag to your header.php file:<br><code>&lt;meta name="google-site-verification" content="' . htmlspecialchars($meta_code) . '" /&gt;</code>';
            } else {
                $error = 'Please enter meta verification code.';
            }
            break;
    }
}

// Check current SEO status
function getCurrentSEOStatus() {
    $status = [];
    
    // Check if sitemap exists
    $status['sitemap_exists'] = file_exists('sitemap.xml');
    $status['sitemap_url'] = SITE_URL . '/sitemap.xml';
    
    // Check if robots.txt exists
    $status['robots_exists'] = file_exists('robots.txt');
    $status['robots_url'] = SITE_URL . '/robots.txt';
    
    // Check SSL
    $status['ssl_enabled'] = strpos(SITE_URL, 'https://') === 0;
    
    // Check if Google verification exists
    $verification_files = glob('google*.html');
    $status['verification_files'] = $verification_files;
    
    return $status;
}

$seo_status = getCurrentSEOStatus();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Search Console Setup - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .step-card {
            border-left: 4px solid #28a745;
            margin-bottom: 1rem;
        }
        .step-number {
            background: #28a745;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        .status-good { color: #28a745; }
        .status-error { color: #dc3545; }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 1rem;
            font-family: monospace;
            margin: 0.5rem 0;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <!-- Header -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h2><i class="fab fa-google"></i> Google Search Console Setup Guide</h2>
                <p class="mb-0">Complete guide to set up Google Search Console for better SEO</p>
            </div>
        </div>

        <?php if ($message): ?>
        <div class="alert alert-success"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <!-- Current Status -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h4><i class="fas fa-info-circle"></i> Current SEO Status</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Sitemap:</strong> 
                            <span class="<?php echo $seo_status['sitemap_exists'] ? 'status-good' : 'status-error'; ?>">
                                <i class="fas fa-<?php echo $seo_status['sitemap_exists'] ? 'check' : 'times'; ?>"></i>
                                <?php echo $seo_status['sitemap_exists'] ? 'Available' : 'Missing'; ?>
                            </span>
                            <?php if ($seo_status['sitemap_exists']): ?>
                                <br><small><a href="<?php echo $seo_status['sitemap_url']; ?>" target="_blank"><?php echo $seo_status['sitemap_url']; ?></a></small>
                            <?php endif; ?>
                        </p>
                        
                        <p><strong>Robots.txt:</strong> 
                            <span class="<?php echo $seo_status['robots_exists'] ? 'status-good' : 'status-error'; ?>">
                                <i class="fas fa-<?php echo $seo_status['robots_exists'] ? 'check' : 'times'; ?>"></i>
                                <?php echo $seo_status['robots_exists'] ? 'Available' : 'Missing'; ?>
                            </span>
                            <?php if ($seo_status['robots_exists']): ?>
                                <br><small><a href="<?php echo $seo_status['robots_url']; ?>" target="_blank"><?php echo $seo_status['robots_url']; ?></a></small>
                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>SSL Certificate:</strong> 
                            <span class="<?php echo $seo_status['ssl_enabled'] ? 'status-good' : 'status-error'; ?>">
                                <i class="fas fa-<?php echo $seo_status['ssl_enabled'] ? 'shield-alt' : 'exclamation-triangle'; ?>"></i>
                                <?php echo $seo_status['ssl_enabled'] ? 'Enabled' : 'Disabled'; ?>
                            </span>
                        </p>
                        
                        <p><strong>Google Verification:</strong> 
                            <?php if (!empty($seo_status['verification_files'])): ?>
                                <span class="status-good">
                                    <i class="fas fa-check"></i> Files found: <?php echo implode(', ', $seo_status['verification_files']); ?>
                                </span>
                            <?php else: ?>
                                <span class="status-error">
                                    <i class="fas fa-times"></i> No verification files
                                </span>
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step-by-Step Guide -->
        <div class="row">
            <div class="col-12">
                <!-- Step 1 -->
                <div class="card step-card">
                    <div class="card-body">
                        <div class="d-flex align-items-start">
                            <div class="step-number me-3">1</div>
                            <div class="flex-grow-1">
                                <h5>Go to Google Search Console</h5>
                                <p>Visit Google Search Console and sign in with your Google account.</p>
                                <a href="https://search.google.com/search-console" target="_blank" class="btn btn-primary">
                                    <i class="fab fa-google"></i> Open Google Search Console
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 2 -->
                <div class="card step-card">
                    <div class="card-body">
                        <div class="d-flex align-items-start">
                            <div class="step-number me-3">2</div>
                            <div class="flex-grow-1">
                                <h5>Add Your Property</h5>
                                <p>Click "Add Property" and select "URL prefix". Enter your website URL:</p>
                                <div class="code-block">
                                    <?php echo SITE_URL; ?>
                                </div>
                                <p><small class="text-muted">Make sure to use the exact URL with https:// if SSL is enabled.</small></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 3 -->
                <div class="card step-card">
                    <div class="card-body">
                        <div class="d-flex align-items-start">
                            <div class="step-number me-3">3</div>
                            <div class="flex-grow-1">
                                <h5>Verify Ownership</h5>
                                <p>Choose one of the verification methods:</p>
                                
                                <div class="accordion" id="verificationAccordion">
                                    <!-- HTML File Method -->
                                    <div class="accordion-item">
                                        <h2 class="accordion-header">
                                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#htmlMethod">
                                                HTML File Upload (Recommended)
                                            </button>
                                        </h2>
                                        <div id="htmlMethod" class="accordion-collapse collapse show" data-bs-parent="#verificationAccordion">
                                            <div class="accordion-body">
                                                <p>Download the HTML verification file from Google Search Console and upload it to your website root.</p>
                                                <form method="post" class="mb-3">
                                                    <input type="hidden" name="action" value="generate_html_verification">
                                                    <div class="mb-3">
                                                        <label class="form-label">Verification File Name (from Google):</label>
                                                        <input type="text" name="verification_code" class="form-control" placeholder="google1234567890abcdef.html">
                                                    </div>
                                                    <button type="submit" class="btn btn-success">Create Verification File</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Meta Tag Method -->
                                    <div class="accordion-item">
                                        <h2 class="accordion-header">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#metaMethod">
                                                HTML Meta Tag
                                            </button>
                                        </h2>
                                        <div id="metaMethod" class="accordion-collapse collapse" data-bs-parent="#verificationAccordion">
                                            <div class="accordion-body">
                                                <p>Add a meta tag to your website's header.</p>
                                                <form method="post" class="mb-3">
                                                    <input type="hidden" name="action" value="generate_meta_tag">
                                                    <div class="mb-3">
                                                        <label class="form-label">Meta Content Value (from Google):</label>
                                                        <input type="text" name="meta_code" class="form-control" placeholder="1234567890abcdef">
                                                    </div>
                                                    <button type="submit" class="btn btn-success">Generate Meta Tag</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 4 -->
                <div class="card step-card">
                    <div class="card-body">
                        <div class="d-flex align-items-start">
                            <div class="step-number me-3">4</div>
                            <div class="flex-grow-1">
                                <h5>Submit Your Sitemap</h5>
                                <p>After verification, submit your sitemap to Google:</p>
                                <ol>
                                    <li>Go to "Sitemaps" in the left menu</li>
                                    <li>Click "Add a new sitemap"</li>
                                    <li>Enter your sitemap URL:</li>
                                </ol>
                                <div class="code-block">
                                    <?php echo $seo_status['sitemap_url']; ?>
                                </div>
                                <?php if (!$seo_status['sitemap_exists']): ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i> 
                                    Sitemap not found! <a href="seo_tools.php?action=generate_sitemap">Generate sitemap first</a>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 5 -->
                <div class="card step-card">
                    <div class="card-body">
                        <div class="d-flex align-items-start">
                            <div class="step-number me-3">5</div>
                            <div class="flex-grow-1">
                                <h5>Monitor Your Performance</h5>
                                <p>After setup, regularly check these reports in Google Search Console:</p>
                                <ul>
                                    <li><strong>Performance:</strong> See how your site appears in search results</li>
                                    <li><strong>Coverage:</strong> Check for indexing issues</li>
                                    <li><strong>Enhancements:</strong> Fix mobile usability and other issues</li>
                                    <li><strong>Security Issues:</strong> Monitor for security problems</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mt-4">
            <div class="card-header bg-success text-white">
                <h4><i class="fas fa-tools"></i> Quick SEO Tools</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="seo_tools.php?action=generate_sitemap" class="btn btn-outline-primary w-100">
                            <i class="fas fa-sitemap"></i><br>Generate Sitemap
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="seo_checker.php" class="btn btn-outline-info w-100">
                            <i class="fas fa-search"></i><br>SEO Checker
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="seo_monitor.php" class="btn btn-outline-warning w-100">
                            <i class="fas fa-chart-line"></i><br>SEO Monitor
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="database_checker.php" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-database"></i><br>Database Check
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
