<?php
// Set page title
$page_title = 'Import from TMDB';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Check if TMDB API key is set
if (!defined('TMDB_API_KEY') || empty(TMDB_API_KEY)) {
    $error_message = 'TMDB API key is not set. Please set it in the site settings.';
}

// Process form submissions
$success_message = '';
$error_message = '';
$search_results = [];
$movie_details = null;

// Search Movies
if (isset($_POST['search_movies']) && !empty($_POST['search_query'])) {
    $search_query = sanitize($_POST['search_query']);
    // Replace placeholder values with actual constants
    $api_key = defined('TMDB_API_KEY') ? constant('TMDB_API_KEY') : '';
    $language = defined('TMDB_LANGUAGE') ? constant('TMDB_LANGUAGE') : 'en-US';
    $api_url = "https://api.themoviedb.org/3/search/movie?api_key=" . $api_key . "&language=" . $language . "&query=" . urlencode($search_query) . "&page=1&include_adult=false";

    // Enable error reporting for debugging
    $old_error_reporting = error_reporting(E_ALL);
    $old_display_errors = ini_get('display_errors');
    ini_set('display_errors', 1);

    // Create a stream context with timeout
    $context = stream_context_create([
        'http' => [
            'timeout' => 10, // Timeout in seconds
        ]
    ]);

    try {
        $response = @file_get_contents($api_url, false, $context);

        if ($response !== false) {
            $data = json_decode($response, true);
            if (isset($data['results']) && !empty($data['results'])) {
                $search_results = $data['results'];
            } else {
                $error_message = 'No movies found for the search query.';
            }
        } else {
            $error_message = 'Error connecting to TMDB API. URL: ' . $api_url;
        }
    } catch (Exception $e) {
        $error_message = 'Exception: ' . $e->getMessage();
    }

    // Restore error reporting settings
    error_reporting($old_error_reporting);
    ini_set('display_errors', $old_display_errors);
}

// Get Movie Details
if (isset($_GET['movie_id']) && is_numeric($_GET['movie_id'])) {
    $tmdb_id = (int)$_GET['movie_id'];
    // Replace placeholder values with actual constants
    $api_key = defined('TMDB_API_KEY') ? constant('TMDB_API_KEY') : '';
    $language = defined('TMDB_LANGUAGE') ? constant('TMDB_LANGUAGE') : 'en-US';
    $api_url = "https://api.themoviedb.org/3/movie/$tmdb_id?api_key=" . $api_key . "&language=" . $language . "&append_to_response=credits,videos";

    // Create a stream context with timeout
    $context = stream_context_create([
        'http' => [
            'timeout' => 10, // Timeout in seconds
        ]
    ]);

    try {
        $response = @file_get_contents($api_url, false, $context);

        if ($response !== false) {
            $movie_details = json_decode($response, true);
            if (!$movie_details) {
                $error_message = 'Error parsing movie details from TMDB API.';
            }
        } else {
            $error_message = 'Error fetching movie details from TMDB API. URL: ' . $api_url;
        }
    } catch (Exception $e) {
        $error_message = 'Exception: ' . $e->getMessage();
    }
}

// Create movie_genres table if it doesn't exist
$check_movie_genres_table = "SHOW TABLES LIKE 'movie_genres'";
$movie_genres_table_exists = mysqli_query($conn, $check_movie_genres_table);
if (mysqli_num_rows($movie_genres_table_exists) == 0) {
    $create_movie_genres_table = "CREATE TABLE IF NOT EXISTS movie_genres (
        id INT AUTO_INCREMENT PRIMARY KEY,
        movie_id INT NOT NULL,
        genre_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
        FOREIGN KEY (genre_id) REFERENCES categories(id) ON DELETE CASCADE,
        UNIQUE KEY unique_movie_genre (movie_id, genre_id)
    )";
    mysqli_query($conn, $create_movie_genres_table);
}

// Direct import from search results
if (isset($_POST['direct_import_from_search']) && !empty($_POST['tmdb_id'])) {
    $tmdb_id = (int)$_POST['tmdb_id'];

    // Check if movie already exists
    $check_query = "SELECT * FROM movies WHERE tmdb_id = $tmdb_id";
    $check_result = mysqli_query($conn, $check_query);

    if (mysqli_num_rows($check_result) > 0) {
        $error_message = 'Movie already exists in the database.';
    } else {
        // Get movie details from TMDB API
        $api_key = defined('TMDB_API_KEY') ? constant('TMDB_API_KEY') : '';
        $language = defined('TMDB_LANGUAGE') ? constant('TMDB_LANGUAGE') : 'en-US';
        $api_url = "https://api.themoviedb.org/3/movie/$tmdb_id?api_key=" . $api_key . "&language=" . $language . "&append_to_response=credits,videos";

        $context = stream_context_create([
            'http' => [
                'timeout' => 10, // Timeout in seconds
            ]
        ]);

        try {
            $response = @file_get_contents($api_url, false, $context);

            if ($response !== false) {
                $movie_details = json_decode($response, true);
                if (!$movie_details) {
                    $error_message = 'Error parsing movie details from TMDB API.';
                } else {
                    // Get default category
                    $category_query = "SELECT id FROM categories ORDER BY id LIMIT 1";
                    $category_result = mysqli_query($conn, $category_query);
                    $default_category_id = mysqli_num_rows($category_result) > 0 ? mysqli_fetch_assoc($category_result)['id'] : 1;

                    $title = sanitize($movie_details['title']);

                    // Get category based on TMDB genres
                    $category_id = $default_category_id;
                    if (!empty($movie_details['genres'])) {
                        // Get the first genre from TMDB
                        $tmdb_genre = $movie_details['genres'][0]['name'];

                        // Try to find a matching category in our database
                        $category_query = "SELECT id FROM categories WHERE name LIKE '%$tmdb_genre%' AND (type = 'both' OR type = 'movie') LIMIT 1";
                        $category_result = mysqli_query($conn, $category_query);

                        if (mysqli_num_rows($category_result) > 0) {
                            $category = mysqli_fetch_assoc($category_result);
                            $category_id = $category['id'];
                        }
                    }

                    $release_year = !empty($movie_details['release_date']) ? (int)date('Y', strtotime($movie_details['release_date'])) : 0;
                    $duration = !empty($movie_details['runtime']) ? floor($movie_details['runtime'] / 60) . 'h ' . ($movie_details['runtime'] % 60) . 'm' : '';
                    $quality = 'HD';
                    $language = !empty($movie_details['original_language']) ? strtoupper($movie_details['original_language']) : '';
                    $rating = (float)$movie_details['vote_average'];
                    $description = sanitize($movie_details['overview']);

                    // Get trailer URL
                    $trailer_url = '';
                    if (!empty($movie_details['videos']['results'])) {
                        foreach ($movie_details['videos']['results'] as $video) {
                            if ($video['site'] == 'YouTube' && ($video['type'] == 'Trailer' || $video['type'] == 'Teaser')) {
                                $trailer_url = 'https://www.youtube.com/watch?v=' . $video['key'];
                                break;
                            }
                        }
                    }

                    $poster_path = !empty($movie_details['poster_path']) ? $movie_details['poster_path'] : '';
                    $backdrop_path = !empty($movie_details['backdrop_path']) ? $movie_details['backdrop_path'] : '';
                    $premium_only = 0;
                    $featured = 0;

                    // Download poster image from TMDB
                    $poster_filename = '';
                    if (!empty($poster_path)) {
                        $poster_url = 'https://image.tmdb.org/t/p/w500' . $poster_path;
                        $poster_filename = 'movie_poster_' . $tmdb_id . '_' . time() . '.jpg';
                        $poster_path_local = '../uploads/' . $poster_filename;

                        // Create uploads directory if it doesn't exist
                        if (!file_exists('../uploads')) {
                            mkdir('../uploads', 0777, true);
                        }

                        // Download the image
                        $image_content = @file_get_contents($poster_url);
                        if ($image_content !== false) {
                            file_put_contents($poster_path_local, $image_content);
                        } else {
                            // If download fails, use the TMDB path directly
                            $poster_filename = $poster_path;
                        }
                    }

                    // Download banner image from TMDB
                    $banner_filename = '';
                    if (!empty($backdrop_path)) {
                        $banner_url = 'https://image.tmdb.org/t/p/original' . $backdrop_path;
                        $banner_filename = 'movie_banner_' . $tmdb_id . '_' . time() . '.jpg';
                        $banner_path_local = '../uploads/' . $banner_filename;

                        // Download the image
                        $banner_content = @file_get_contents($banner_url);
                        if ($banner_content !== false) {
                            file_put_contents($banner_path_local, $banner_content);
                        } else {
                            // If download fails, use the poster as banner
                            $banner_filename = $poster_filename;
                        }
                    } else {
                        // If no backdrop, use poster as banner
                        $banner_filename = $poster_filename;
                    }

                    // Begin transaction
                    mysqli_begin_transaction($conn);

                    try {
                        // Insert movie
                        $query = "INSERT INTO movies (tmdb_id, title, category_id, release_year, duration, quality, language, rating, description, trailer_url, poster, banner, premium_only, featured, created_at)
                                 VALUES ($tmdb_id, '$title', $category_id, $release_year, '$duration', '$quality', '$language', $rating, '$description', '$trailer_url', '$poster_filename', '$banner_filename', $premium_only, $featured, NOW())";

                        if (!mysqli_query($conn, $query)) {
                            throw new Exception('Error importing movie: ' . mysqli_error($conn));
                        }

                        $movie_id = mysqli_insert_id($conn);

                        // Add all genres to movie_genres table
                        if (!empty($movie_details['genres'])) {
                            foreach ($movie_details['genres'] as $genre) {
                                $tmdb_genre = sanitize($genre['name']);

                                // Try to find a matching category in our database
                                $genre_query = "SELECT id FROM categories WHERE name LIKE '%$tmdb_genre%' AND (type = 'both' OR type = 'movie') LIMIT 1";
                                $genre_result = mysqli_query($conn, $genre_query);

                                if (mysqli_num_rows($genre_result) > 0) {
                                    $db_genre = mysqli_fetch_assoc($genre_result);
                                    $genre_id = $db_genre['id'];

                                    // Insert into movie_genres table
                                    $insert_genre_query = "INSERT IGNORE INTO movie_genres (movie_id, genre_id) VALUES ($movie_id, $genre_id)";
                                    mysqli_query($conn, $insert_genre_query);
                                }
                            }
                        }

                        // Add automatic streaming link for vidzee.wtf
                        $stream_url = "https://vidzee.wtf/movie/{$tmdb_id}";
                        $stream_query = "INSERT INTO streaming_links (content_type, content_id, quality, server_name, stream_url, is_premium)
                                       VALUES ('movie', $movie_id, 'HD', 'Vidzee', '$stream_url', $premium_only)";
                        mysqli_query($conn, $stream_query);

                        // Commit transaction
                        mysqli_commit($conn);

                        $success_message = 'Movie imported successfully.';

                        // Redirect to edit page
                        redirect("edit_movie.php?id=$movie_id&success=imported");
                    } catch (Exception $e) {
                        // Rollback transaction on error
                        mysqli_rollback($conn);
                        $error_message = $e->getMessage();
                    }
                }
            } else {
                $error_message = 'Error fetching movie details from TMDB API. URL: ' . $api_url;
            }
        } catch (Exception $e) {
            $error_message = 'Exception: ' . $e->getMessage();
        }
    }
}

// Import Movie
if (isset($_POST['import_movie']) || isset($_POST['direct_import'])) {
    $tmdb_id = (int)$_POST['tmdb_id'];

    // Check if movie already exists
    $check_query = "SELECT * FROM movies WHERE tmdb_id = $tmdb_id";
    $check_result = mysqli_query($conn, $check_query);

    if (mysqli_num_rows($check_result) > 0) {
        $error_message = 'Movie already exists in the database.';
    } else {
        if (isset($_POST['direct_import'])) {
            // Direct import with default values
            // Get default category
            $category_query = "SELECT id FROM categories ORDER BY id LIMIT 1";
            $category_result = mysqli_query($conn, $category_query);
            $default_category_id = mysqli_num_rows($category_result) > 0 ? mysqli_fetch_assoc($category_result)['id'] : 1;

            $title = sanitize($movie_details['title']);

            // Get category based on TMDB genres
            $category_id = $default_category_id;
            if (!empty($movie_details['genres'])) {
                // Get the first genre from TMDB
                $tmdb_genre = $movie_details['genres'][0]['name'];

                // Try to find a matching category in our database
                $category_query = "SELECT id FROM categories WHERE name LIKE '%$tmdb_genre%' AND (type = 'both' OR type = 'movie') LIMIT 1";
                $category_result = mysqli_query($conn, $category_query);

                if (mysqli_num_rows($category_result) > 0) {
                    $category = mysqli_fetch_assoc($category_result);
                    $category_id = $category['id'];
                }
            }

            $release_year = !empty($movie_details['release_date']) ? (int)date('Y', strtotime($movie_details['release_date'])) : 0;
            $duration = !empty($movie_details['runtime']) ? floor($movie_details['runtime'] / 60) . 'h ' . ($movie_details['runtime'] % 60) . 'm' : '';
            $quality = 'HD';
            $language = !empty($movie_details['original_language']) ? strtoupper($movie_details['original_language']) : '';
            $rating = (float)$movie_details['vote_average'];
            $description = sanitize($movie_details['overview']);

            // Get trailer URL
            $trailer_url = '';
            if (!empty($movie_details['videos']['results'])) {
                foreach ($movie_details['videos']['results'] as $video) {
                    if ($video['site'] == 'YouTube' && ($video['type'] == 'Trailer' || $video['type'] == 'Teaser')) {
                        $trailer_url = 'https://www.youtube.com/watch?v=' . $video['key'];
                        break;
                    }
                }
            }

            $poster_path = sanitize($movie_details['poster_path']);
            $premium_only = 0;
            $featured = 0;
        } else {
            // Regular import with user-specified values
            $title = sanitize($_POST['title']);
            $category_id = (int)$_POST['category_id'];
            $release_year = (int)$_POST['release_year'];
            $duration = sanitize($_POST['duration']);
            $quality = sanitize($_POST['quality']);
            $language = sanitize($_POST['language']);
            $rating = (float)$_POST['rating'];
            $description = sanitize($_POST['description']);
            $trailer_url = sanitize($_POST['trailer_url']);
            $poster_path = sanitize($_POST['poster_path']);
            $premium_only = isset($_POST['premium_only']) ? 1 : 0;
            $featured = isset($_POST['featured']) ? 1 : 0;
        }

        // Validate required fields
        if (empty($title) || empty($category_id) || empty($release_year)) {
            $error_message = 'Title, category, and release year are required.';
        } else {
            // Download poster image from TMDB
            $poster_filename = '';
            if (!empty($poster_path)) {
                $poster_url = 'https://image.tmdb.org/t/p/w500' . $poster_path;
                $poster_filename = 'movie_poster_' . $tmdb_id . '_' . time() . '.jpg';
                $poster_path_local = '../uploads/' . $poster_filename;

                // Create uploads directory if it doesn't exist
                if (!file_exists('../uploads')) {
                    mkdir('../uploads', 0777, true);
                }

                // Download the image
                $image_content = @file_get_contents($poster_url);
                if ($image_content !== false) {
                    file_put_contents($poster_path_local, $image_content);
                } else {
                    // If download fails, use the TMDB path directly
                    $poster_filename = $poster_path;
                }
            }

            // Download banner image from TMDB
            $banner_filename = '';
            $backdrop_path = isset($_POST['backdrop_path']) ? sanitize($_POST['backdrop_path']) : '';
            if (!empty($backdrop_path)) {
                $banner_url = 'https://image.tmdb.org/t/p/original' . $backdrop_path;
                $banner_filename = 'movie_banner_' . $tmdb_id . '_' . time() . '.jpg';
                $banner_path_local = '../uploads/' . $banner_filename;

                // Download the image
                $banner_content = @file_get_contents($banner_url);
                if ($banner_content !== false) {
                    file_put_contents($banner_path_local, $banner_content);
                } else {
                    // If download fails, use the poster as banner
                    $banner_filename = $poster_filename;
                }
            } else {
                // If no backdrop, use poster as banner
                $banner_filename = $poster_filename;
            }

            // Begin transaction
            mysqli_begin_transaction($conn);

            try {
                // Insert movie
                $query = "INSERT INTO movies (tmdb_id, title, category_id, release_year, duration, quality, language, rating, description, trailer_url, poster, banner, premium_only, featured, created_at)
                         VALUES ($tmdb_id, '$title', $category_id, $release_year, '$duration', '$quality', '$language', $rating, '$description', '$trailer_url', '$poster_filename', '$banner_filename', $premium_only, $featured, NOW())";

                if (!mysqli_query($conn, $query)) {
                    throw new Exception('Error importing movie: ' . mysqli_error($conn));
                }

                $movie_id = mysqli_insert_id($conn);

                // Add all genres to movie_genres table
                if (!empty($movie_details['genres'])) {
                    foreach ($movie_details['genres'] as $genre) {
                        $tmdb_genre = sanitize($genre['name']);

                        // Try to find a matching category in our database
                        $genre_query = "SELECT id FROM categories WHERE name LIKE '%$tmdb_genre%' AND (type = 'both' OR type = 'movie') LIMIT 1";
                        $genre_result = mysqli_query($conn, $genre_query);

                        if (mysqli_num_rows($genre_result) > 0) {
                            $db_genre = mysqli_fetch_assoc($genre_result);
                            $genre_id = $db_genre['id'];

                            // Insert into movie_genres table
                            $insert_genre_query = "INSERT IGNORE INTO movie_genres (movie_id, genre_id) VALUES ($movie_id, $genre_id)";
                            mysqli_query($conn, $insert_genre_query);
                        }
                    }
                }

                // Add automatic streaming link for vidzee.wtf
                $stream_url = "https://vidzee.wtf/movie/{$tmdb_id}";
                $stream_query = "INSERT INTO streaming_links (content_type, content_id, quality, server_name, stream_url, is_premium)
                               VALUES ('movie', $movie_id, 'HD', 'Vidzee', '$stream_url', $premium_only)";
                mysqli_query($conn, $stream_query);

                // Commit transaction
                mysqli_commit($conn);

                $success_message = 'Movie imported successfully.';

                // Redirect to edit page
                redirect("edit_movie.php?id=$movie_id&success=imported");
            } catch (Exception $e) {
                // Rollback transaction on error
                mysqli_rollback($conn);
                $error_message = $e->getMessage();
            }
        }
    }
}

// Get categories
$categories_query = "SELECT * FROM categories ORDER BY name";
$categories_result = mysqli_query($conn, $categories_query);

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title d-flex align-items-center">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>টিএমডিবি থেকে মুভি ইমপোর্ট</h1>
            </div>

            <div class="topbar-actions">
                <div class="dropdown">
                    <button class="btn btn-link dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <img src="assets/img/user.png" class="rounded-circle" width="32" height="32" alt="User">
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user fa-sm me-2 text-gray-400"></i> প্রোফাইল</a></li>
                        <li><a class="dropdown-item" href="site_settings.php"><i class="fas fa-cogs fa-sm me-2 text-gray-400"></i> সেটিংস</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/logout.php"><i class="fas fa-sign-out-alt fa-sm me-2 text-gray-400"></i> লগআউট</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <?php if($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if(!isset($error_message) || (isset($error_message) && $error_message != 'TMDB API key is not set. Please set it in the site settings.')): ?>
        <!-- Search and Direct Import Forms -->
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="fw-bold text-primary mb-0">মুভি সার্চ</h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="" class="needs-validation" novalidate>
                            <div class="input-group">
                                <input type="text" class="form-control" name="search_query" placeholder="মুভির নাম লিখুন..." required>
                                <button type="submit" name="search_movies" class="btn btn-primary d-flex align-items-center">
                                    <i class="fas fa-search me-2"></i><span>সার্চ</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="fw-bold text-primary mb-0">টিএমডিবি আইডি দিয়ে ইমপোর্ট</h6>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="" class="needs-validation" novalidate>
                            <div class="input-group">
                                <input type="number" class="form-control" name="movie_id" placeholder="টিএমডিবি আইডি লিখুন..." required>
                                <button type="submit" class="btn btn-success d-flex align-items-center">
                                    <i class="fas fa-file-import me-2"></i><span>ডিটেইলস</span>
                                </button>
                            </div>
                            <div class="form-text mt-2">
                                <i class="fas fa-info-circle me-1"></i> <a href="https://www.themoviedb.org" target="_blank">themoviedb.org</a> থেকে মুভির আইডি পাবেন (যেমন, Fight Club এর জন্য 550)
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <?php if(!empty($search_results)): ?>
        <!-- Search Results -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="fw-bold text-primary mb-0">সার্চ রেজাল্ট</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="d-none d-md-table-header-group">
                            <tr>
                                <th>পোস্টার</th>
                                <th>নাম</th>
                                <th>রিলিজ ডেট</th>
                                <th>রেটিং</th>
                                <th>অ্যাকশন</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($search_results as $movie): ?>
                            <tr class="d-block d-md-table-row mb-3 mb-md-0">
                                <td class="d-flex d-md-table-cell align-items-center">
                                    <?php if(!empty($movie['poster_path'])): ?>
                                    <img src="https://image.tmdb.org/t/p/w92<?php echo $movie['poster_path']; ?>" alt="<?php echo $movie['title']; ?>" class="img-thumbnail" style="width: 50px;">
                                    <?php else: ?>
                                    <div style="width: 50px; height: 75px; background-color: #f8f9fc; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-film fa-2x text-muted"></i>
                                    </div>
                                    <?php endif; ?>
                                    <div class="ms-3 d-block d-md-none">
                                        <h6 class="mb-0"><?php echo $movie['title']; ?></h6>
                                        <small class="text-muted">
                                            <?php echo !empty($movie['release_date']) ? date('M j, Y', strtotime($movie['release_date'])) : 'N/A'; ?> •
                                            <?php echo $movie['vote_average']; ?>/10
                                        </small>
                                    </div>
                                </td>
                                <td class="d-none d-md-table-cell"><?php echo $movie['title']; ?></td>
                                <td class="d-none d-md-table-cell"><?php echo !empty($movie['release_date']) ? date('M j, Y', strtotime($movie['release_date'])) : 'N/A'; ?></td>
                                <td class="d-none d-md-table-cell"><?php echo $movie['vote_average']; ?>/10</td>
                                <td class="d-block d-md-table-cell mt-2 mt-md-0">
                                    <div class="d-flex flex-column flex-md-row gap-2">
                                        <a href="import_tmdb.php?movie_id=<?php echo $movie['id']; ?>" class="btn btn-primary btn-sm d-flex align-items-center justify-content-center">
                                            <i class="fas fa-info-circle me-2"></i><span>ডিটেইলস দেখুন</span>
                                        </a>
                                        <form method="POST" action="" class="m-0">
                                            <input type="hidden" name="direct_import_from_search" value="1">
                                            <input type="hidden" name="tmdb_id" value="<?php echo $movie['id']; ?>">
                                            <button type="submit" class="btn btn-success btn-sm d-flex align-items-center justify-content-center w-100">
                                                <i class="fas fa-file-import me-2"></i><span>ইম্পোর্ট</span>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php if($movie_details): ?>
        <!-- Movie Details -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="fw-bold text-primary mb-0">মুভির বিস্তারিত তথ্য</h6>
            </div>
            <div class="card-body">
                <form method="POST" action="" class="needs-validation" novalidate>
                    <input type="hidden" name="tmdb_id" value="<?php echo $movie_details['id']; ?>">

                    <div class="row">
                        <div class="col-md-3 mb-4">
                            <?php if(!empty($movie_details['poster_path'])): ?>
                            <img src="https://image.tmdb.org/t/p/w300<?php echo $movie_details['poster_path']; ?>" alt="<?php echo $movie_details['title']; ?>" class="img-thumbnail w-100">
                            <?php else: ?>
                            <div style="width: 100%; height: 300px; background-color: #f8f9fc; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-film fa-4x text-muted"></i>
                            </div>
                            <?php endif; ?>

                            <div class="mt-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="premium_only" name="premium_only">
                                    <label class="form-check-label" for="premium_only">প্রিমিয়াম</label>
                                </div>

                                <div class="form-check form-switch mt-2">
                                    <input class="form-check-input" type="checkbox" id="featured" name="featured">
                                    <label class="form-check-label" for="featured">ফিচার্ড</label>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-9">
                            <div class="mb-3">
                                <label for="title" class="form-label">মুভির নাম <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" value="<?php echo $movie_details['title']; ?>" required>
                                <div class="invalid-feedback">
                                    মুভির নাম দিতে হবে।
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="category_id" class="form-label">ক্যাটাগরি <span class="text-danger">*</span></label>
                                        <select class="form-select" id="category_id" name="category_id" required>
                                            <option value="">ক্যাটাগরি নির্বাচন করুন</option>
                                            <?php
                                            mysqli_data_seek($categories_result, 0);
                                            while($category = mysqli_fetch_assoc($categories_result)):
                                            ?>
                                            <option value="<?php echo $category['id']; ?>"><?php echo $category['name']; ?></option>
                                            <?php endwhile; ?>
                                        </select>
                                        <div class="invalid-feedback">
                                            Please select a category.
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="release_year" class="form-label">Release Year <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="release_year" name="release_year" value="<?php echo !empty($movie_details['release_date']) ? date('Y', strtotime($movie_details['release_date'])) : ''; ?>" required>
                                        <div class="invalid-feedback">
                                            Please enter a valid release year.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="duration" class="form-label">Duration</label>
                                        <input type="text" class="form-control" id="duration" name="duration" value="<?php echo !empty($movie_details['runtime']) ? floor($movie_details['runtime'] / 60) . 'h ' . ($movie_details['runtime'] % 60) . 'm' : ''; ?>">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="quality" class="form-label">Quality</label>
                                        <select class="form-select" id="quality" name="quality">
                                            <option value="">Select Quality</option>
                                            <option value="CAM">CAM</option>
                                            <option value="SD">SD</option>
                                            <option value="HD" selected>HD</option>
                                            <option value="Full HD">Full HD</option>
                                            <option value="4K">4K</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="language" class="form-label">Language</label>
                                        <input type="text" class="form-control" id="language" name="language" value="<?php echo !empty($movie_details['original_language']) ? strtoupper($movie_details['original_language']) : ''; ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="rating" class="form-label">Rating (0-10)</label>
                                <input type="number" class="form-control" id="rating" name="rating" min="0" max="10" step="0.1" value="<?php echo $movie_details['vote_average']; ?>">
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="5"><?php echo $movie_details['overview']; ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="trailer_url" class="form-label">Trailer URL (YouTube)</label>
                                <?php
                                $trailer_url = '';
                                if (!empty($movie_details['videos']['results'])) {
                                    foreach ($movie_details['videos']['results'] as $video) {
                                        if ($video['site'] == 'YouTube' && ($video['type'] == 'Trailer' || $video['type'] == 'Teaser')) {
                                            $trailer_url = 'https://www.youtube.com/watch?v=' . $video['key'];
                                            break;
                                        }
                                    }
                                }
                                ?>
                                <input type="url" class="form-control" id="trailer_url" name="trailer_url" value="<?php echo $trailer_url; ?>">
                            </div>

                            <input type="hidden" name="poster_path" value="<?php echo $movie_details['poster_path']; ?>">
                            <input type="hidden" name="backdrop_path" value="<?php echo $movie_details['backdrop_path']; ?>">

                            <div class="d-flex flex-column flex-md-row gap-2 mb-3">
                                <button type="submit" name="import_movie" class="btn btn-primary w-100 mb-2 mb-md-0">
                                    <i class="fas fa-file-import me-2"></i>মুভি ইমপোর্ট করুন
                                </button>
                                <button type="submit" name="direct_import" class="btn btn-success w-100">
                                    <i class="fas fa-bolt me-2"></i>সরাসরি ইমপোর্ট
                                </button>
                            </div>
                            <div class="form-text mt-2 text-center">
                                <i class="fas fa-info-circle me-1"></i> সরাসরি ইমপোর্ট ডিফল্ট ক্যাটাগরি এবং সেটিংস ব্যবহার করবে
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<style>
@media (max-width: 576px) {
    .input-group .btn span {
        display: inline-block !important;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        width: 100%;
    }

    .table td {
        vertical-align: middle;
    }

    .table td .btn {
        width: 100%;
        margin-top: 5px;
        margin-bottom: 5px;
        white-space: normal;
    }
}
</style>

<?php
// Include footer
require_once 'includes/footer.php';
?>
