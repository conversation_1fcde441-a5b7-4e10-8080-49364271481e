<?php
require_once 'includes/header.php';
require_once 'includes/streaming_helper.php';

// Test URL (Cloudflare Workers link with .mkv extension)
$test_url = "https://nim-pata-worker-quiet-river-e51c.bdmovieshub.workers.dev/1:/january%20to%20march/4-11-25/BDFLiX.Top-Chhaava%20(2025)%C2%A0BDFlix.Top-Hindi%20NetFlix%20WEB-DL%20480p.mkv";
$test_title = "Chhaava (2025)";
$test_poster = "https://image.tmdb.org/t/p/w500/wE0I6efAW4cDDmZQWtwZMOW44EJ.jpg";
$test_quality = "480p";
$test_server = "BDFlix";

// Generate play URL
$play_url = getPlayUrlFromDownload($test_url, $test_title, $test_poster, false, $test_quality, $test_server);
?>

<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <div class="card bg-dark text-white mb-4">
                <div class="card-header">
                    <h3>ডাউনলোড লিংক থেকে প্লে করার সিস্টেম টেস্ট</h3>
                </div>
                <div class="card-body">
                    <h5>টেস্ট ডাউনলোড লিংক:</h5>
                    <div class="bg-dark p-3 mb-4 border border-secondary rounded">
                        <code class="text-light"><?php echo $test_url; ?></code>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-dark border-secondary mb-3">
                                <div class="card-header">ডাউনলোড এবং প্লে বাটন</div>
                                <div class="card-body">
                                    <?php echo displayDownloadAndPlayButtons($test_url, $test_title, $test_poster, $test_quality, false, $test_server); ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-dark border-secondary">
                                <div class="card-header">জেনারেট করা প্লে URL</div>
                                <div class="card-body">
                                    <p><code><?php echo $play_url; ?></code></p>
                                    <a href="<?php echo $play_url; ?>" class="btn btn-primary" target="_blank">
                                        <i class="fas fa-play me-2"></i> প্লেয়ারে দেখুন
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <h5>ফাইল এক্সটেনশন এবং ডোমেইন অনুযায়ী প্লেয়ার সিলেকশন:</h5>
                    <div class="table-responsive">
                        <table class="table table-dark table-bordered">
                            <thead>
                                <tr>
                                    <th>ফাইল টাইপ</th>
                                    <th>ডোমেইন</th>
                                    <th>প্লেয়ার</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>.mkv</td>
                                    <td>workers.dev</td>
                                    <td>Shaka Player</td>
                                </tr>
                                <tr>
                                    <td>অন্যান্য</td>
                                    <td>workers.dev</td>
                                    <td>Plyr Player Enhanced</td>
                                </tr>
                                <tr>
                                    <td>.mkv, .m3u8, .mpd</td>
                                    <td>অন্যান্য</td>
                                    <td>Shaka Player</td>
                                </tr>
                                <tr>
                                    <td>অন্যান্য</td>
                                    <td>অন্যান্য</td>
                                    <td>Default Player</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="card bg-dark text-white">
                <div class="card-header">
                    <h3>ব্যবহার নির্দেশিকা</h3>
                </div>
                <div class="card-body">
                    <p>ডাউনলোড লিংক থেকে সরাসরি ভিডিও প্লে করার জন্য নিম্নলিখিত পদ্ধতি ব্যবহার করুন:</p>
                    
                    <h5>১. ডাউনলোড লিংক থেকে প্লে URL তৈরি করা:</h5>
                    <pre class="bg-dark p-3 border border-secondary rounded"><code>
// Include streaming helper
require_once 'includes/streaming_helper.php';

// Generate play URL
$play_url = getPlayUrlFromDownload(
    $download_link,
    $title,
    $poster_url,
    $is_premium,
    $quality,
    $server_name
);
                    </code></pre>
                    
                    <h5>২. ডাউনলোড এবং প্লে বাটন একসাথে দেখানো:</h5>
                    <pre class="bg-dark p-3 border border-secondary rounded"><code>
// Include streaming helper
require_once 'includes/streaming_helper.php';

// Display download and play buttons
echo displayDownloadAndPlayButtons(
    $download_link,
    $title,
    $poster_url,
    $quality,
    $is_premium,
    $server_name
);
                    </code></pre>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
