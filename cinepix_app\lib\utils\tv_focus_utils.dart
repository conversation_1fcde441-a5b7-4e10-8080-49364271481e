import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cinepix_app/utils/device_utils.dart';

/// Helper class for TV remote control navigation
class TvFocusUtils {
  /// Initialize focus nodes for TV navigation
  static void initFocusNodes(BuildContext context, List<FocusNode> focusNodes) {
    if (!DeviceUtils.isAndroidTV(context)) return;

    // Set initial focus to the first node
    if (focusNodes.isNotEmpty) {
      focusNodes.first.requestFocus();
    }
  }

  /// Create a focus scope for TV navigation
  static Widget createFocusScope({
    required BuildContext context,
    required Widget child,
    required FocusNode focusNode,
    FocusNode? nextFocus,
    FocusNode? previousFocus,
    FocusNode? upFocus,
    FocusNode? downFocus,
  }) {
    if (!DeviceUtils.isAndroidTV(context)) {
      return child;
    }

    return Focus(
      focusNode: focusNode,
      autofocus: false,
      onKey: (node, event) {
        if (event is! RawKeyDownEvent) {
          return KeyEventResult.ignored;
        }

        // Handle arrow keys for navigation
        if (event.logicalKey == LogicalKeyboardKey.arrowRight &&
            nextFocus != null) {
          nextFocus.requestFocus();
          return KeyEventResult.handled;
        } else if (event.logicalKey == LogicalKeyboardKey.arrowLeft &&
            previousFocus != null) {
          previousFocus.requestFocus();
          return KeyEventResult.handled;
        } else if (event.logicalKey == LogicalKeyboardKey.arrowUp &&
            upFocus != null) {
          upFocus.requestFocus();
          return KeyEventResult.handled;
        } else if (event.logicalKey == LogicalKeyboardKey.arrowDown &&
            downFocus != null) {
          downFocus.requestFocus();
          return KeyEventResult.handled;
        }

        return KeyEventResult.ignored;
      },
      child: child,
    );
  }
}
