<?php
require_once 'includes/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect(SITE_URL . '/login.php');
}

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $user_id = $_SESSION['user_id'];
    $message = sanitize($_POST['message']);
    
    // Validate input
    if (empty($message)) {
        $_SESSION['error'] = 'মেসেজ লিখুন।';
        redirect(SITE_URL . '/messages.php');
    }
    
    // Get an admin user (default to admin ID 1)
    $admin_query = "SELECT id FROM users WHERE role = 'admin' ORDER BY id ASC LIMIT 1";
    $admin_result = mysqli_query($conn, $admin_query);
    $admin_id = 1; // Default admin ID
    
    if (mysqli_num_rows($admin_result) > 0) {
        $admin_id = mysqli_fetch_assoc($admin_result)['id'];
    }
    
    // Create a new chat session
    $insert_session_query = "INSERT INTO chat_sessions (user_id, admin_id) VALUES ($user_id, $admin_id)";
    
    if (mysqli_query($conn, $insert_session_query)) {
        $session_id = mysqli_insert_id($conn);
        
        // Add the first message
        $insert_message_query = "INSERT INTO chat_messages (session_id, sender_id, receiver_id, message) 
                               VALUES ($session_id, $user_id, $admin_id, '$message')";
        
        if (mysqli_query($conn, $insert_message_query)) {
            // Redirect to the chat page
            redirect(SITE_URL . "/messages.php?session=$session_id");
        } else {
            $_SESSION['error'] = 'মেসেজ পাঠাতে সমস্যা হয়েছে: ' . mysqli_error($conn);
            redirect(SITE_URL . '/messages.php');
        }
    } else {
        $_SESSION['error'] = 'চ্যাট সেশন তৈরি করতে সমস্যা হয়েছে: ' . mysqli_error($conn);
        redirect(SITE_URL . '/messages.php');
    }
} else {
    redirect(SITE_URL . '/messages.php');
}
?>
