-- কাস্টম শেয়ার লিংক সিস্টেমের জন্য ডেটাবেস স্কিমা
-- Custom Share Link System Database Schema

USE tipsbdxy_4525;

-- শেয়ার লিংক টেবিল
CREATE TABLE IF NOT EXISTS shared_links (
    id INT AUTO_INCREMENT PRIMARY KEY,
    link_token VARCHAR(255) NOT NULL UNIQUE COMMENT 'ইউনিক টোকেন যা URL এ ব্যবহার হবে',
    content_type ENUM('movie', 'tvshow') NOT NULL COMMENT 'কন্টেন্ট টাইপ',
    content_id INT NOT NULL COMMENT 'মুভি বা টিভি শো এর ID',
    created_by INT NOT NULL COMMENT 'যে এডমিন তৈরি করেছে',
    title VARCHAR(255) NOT NULL COMMENT 'শেয়ার লিংকের টাইটেল',
    description TEXT COMMENT 'শেয়ার লিংকের বিবরণ',
    access_limit INT DEFAULT 0 COMMENT 'এক্সেস লিমিট (0 = আনলিমিটেড)',
    access_count INT DEFAULT 0 COMMENT 'কতবার এক্সেস হয়েছে',
    expires_at DATETIME NULL COMMENT 'মেয়াদ উত্তীর্ণের তারিখ',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'লিংক সক্রিয় কিনা',
    allow_download BOOLEAN DEFAULT TRUE COMMENT 'ডাউনলোড অনুমতি',
    allow_streaming BOOLEAN DEFAULT TRUE COMMENT 'স্ট্রিমিং অনুমতি',
    password VARCHAR(255) NULL COMMENT 'পাসওয়ার্ড প্রোটেকশন (ঐচ্ছিক)',
    ip_restrictions TEXT NULL COMMENT 'IP রেস্ট্রিকশন (JSON ফরম্যাট)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_link_token (link_token),
    INDEX idx_content (content_type, content_id),
    INDEX idx_created_by (created_by),
    INDEX idx_active (is_active),
    INDEX idx_expires (expires_at)
);

-- শেয়ার লিংক এক্সেস লগ টেবিল
CREATE TABLE IF NOT EXISTS shared_link_access_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    shared_link_id INT NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    referrer VARCHAR(500),
    access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id INT NULL COMMENT 'যদি লগইন ইউজার হয়',
    country VARCHAR(100),
    city VARCHAR(100),
    device_type VARCHAR(50),
    browser VARCHAR(100),
    
    FOREIGN KEY (shared_link_id) REFERENCES shared_links(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_shared_link (shared_link_id),
    INDEX idx_ip_address (ip_address),
    INDEX idx_access_time (access_time),
    INDEX idx_user_id (user_id)
);

-- শেয়ার লিংক স্ট্যাটিস্টিক্স ভিউ
CREATE OR REPLACE VIEW shared_link_stats AS
SELECT 
    sl.id,
    sl.link_token,
    sl.title,
    sl.content_type,
    sl.content_id,
    sl.access_limit,
    sl.access_count,
    sl.is_active,
    sl.created_at,
    u.username as created_by_username,
    CASE 
        WHEN sl.content_type = 'movie' THEN m.title
        WHEN sl.content_type = 'tvshow' THEN t.title
    END as content_title,
    CASE 
        WHEN sl.access_limit > 0 THEN ROUND((sl.access_count / sl.access_limit) * 100, 2)
        ELSE 0
    END as usage_percentage,
    CASE 
        WHEN sl.expires_at IS NOT NULL AND sl.expires_at < NOW() THEN 'expired'
        WHEN sl.access_limit > 0 AND sl.access_count >= sl.access_limit THEN 'limit_reached'
        WHEN sl.is_active = 0 THEN 'inactive'
        ELSE 'active'
    END as status
FROM shared_links sl
LEFT JOIN users u ON sl.created_by = u.id
LEFT JOIN movies m ON sl.content_type = 'movie' AND sl.content_id = m.id
LEFT JOIN tvshows t ON sl.content_type = 'tvshow' AND sl.content_id = t.id;

-- ইনডেক্স অপটিমাইজেশন
CREATE INDEX idx_shared_links_composite ON shared_links(is_active, expires_at, access_count, access_limit);
CREATE INDEX idx_access_logs_composite ON shared_link_access_logs(shared_link_id, access_time);

-- ট্রিগার: এক্সেস কাউন্ট আপডেট
DELIMITER //
CREATE TRIGGER update_access_count 
AFTER INSERT ON shared_link_access_logs
FOR EACH ROW
BEGIN
    UPDATE shared_links 
    SET access_count = access_count + 1 
    WHERE id = NEW.shared_link_id;
END//
DELIMITER ;

-- ফাংশন: শেয়ার লিংক ভ্যালিডেশন
DELIMITER //
CREATE FUNCTION validate_shared_link(token VARCHAR(255)) 
RETURNS JSON
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE result JSON;
    DECLARE link_id INT;
    DECLARE is_valid BOOLEAN DEFAULT FALSE;
    DECLARE error_message VARCHAR(255) DEFAULT '';
    
    SELECT id INTO link_id 
    FROM shared_links 
    WHERE link_token = token 
    AND is_active = TRUE;
    
    IF link_id IS NOT NULL THEN
        -- চেক করি মেয়াদ উত্তীর্ণ হয়েছে কিনা
        IF EXISTS(SELECT 1 FROM shared_links WHERE id = link_id AND (expires_at IS NULL OR expires_at > NOW())) THEN
            -- চেক করি এক্সেস লিমিট পার হয়েছে কিনা
            IF EXISTS(SELECT 1 FROM shared_links WHERE id = link_id AND (access_limit = 0 OR access_count < access_limit)) THEN
                SET is_valid = TRUE;
            ELSE
                SET error_message = 'Access limit exceeded';
            END IF;
        ELSE
            SET error_message = 'Link has expired';
        END IF;
    ELSE
        SET error_message = 'Invalid or inactive link';
    END IF;
    
    SET result = JSON_OBJECT(
        'valid', is_valid,
        'link_id', link_id,
        'error', error_message
    );
    
    RETURN result;
END//
DELIMITER ;

-- স্যাম্পল ডেটা ইনসার্ট (টেস্টিং এর জন্য)
INSERT INTO shared_links (link_token, content_type, content_id, created_by, title, description, access_limit) 
VALUES 
('demo_movie_123', 'movie', 1, 1, 'The Matrix - Special Share', 'বিশেষ শেয়ার লিংক - দ্য ম্যাট্রিক্স', 100),
('demo_series_456', 'tvshow', 1, 1, 'Breaking Bad - Limited Access', 'সীমিত এক্সেস - ব্রেকিং ব্যাড', 50);
