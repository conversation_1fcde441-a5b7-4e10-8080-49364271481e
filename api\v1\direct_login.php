<?php
// Include direct config file
require_once '../direct_config.php';

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed',
        'data' => null
    ]);
    exit;
}

// Get request body
$request_body = file_get_contents('php://input');
$data = json_decode($request_body, true);

// Validate required fields
if (empty($data['username']) || empty($data['password'])) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Username and password are required',
        'data' => null
    ]);
    exit;
}

$username = $data['username'];
$password = $data['password'];

// Check if user exists
$query = "SELECT * FROM users WHERE username = ? OR email = ?";
$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, 'ss', $username, $username);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) === 0) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Invalid username or password',
        'data' => null
    ]);
    exit;
}

$user = mysqli_fetch_assoc($result);

// Verify password
if (!password_verify($password, $user['password'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Invalid username or password',
        'data' => null
    ]);
    exit;
}

// Generate JWT token
$token = generate_jwt($user['id'], $user['username'], $user['role']);

// Return user data and token
http_response_code(200);
echo json_encode([
    'success' => true,
    'message' => 'Login successful',
    'data' => [
        'user' => [
            'id' => (int)$user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'name' => $user['name'],
            'role' => $user['role'],
            'profile_image' => $user['profile_image'],
            'is_premium' => (bool)$user['is_premium'],
            'premium_expires' => $user['premium_expires'],
            'created_at' => $user['created_at']
        ],
        'token' => $token
    ]
]);
?>
