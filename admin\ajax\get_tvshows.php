<?php
require_once '../../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Get all TV shows
$query = "SELECT id, title FROM tvshows ORDER BY title";
$result = mysqli_query($conn, $query);

$tvshows = [];
if ($result && mysqli_num_rows($result) > 0) {
    while ($row = mysqli_fetch_assoc($result)) {
        $tvshows[] = $row;
    }
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($tvshows);
?>
