-- Add status column to movies table if it doesn't exist
ALTER TABLE movies ADD COLUMN IF NOT EXISTS status ENUM('active', 'inactive') DEFAULT 'active';

-- Update existing movies to be active
UPDATE movies SET status = 'active' WHERE status IS NULL;

-- Add status column to tvshows table if it doesn't exist
ALTER TABLE tvshows ADD COLUMN IF NOT EXISTS status ENUM('active', 'inactive') DEFAULT 'active';

-- Update existing tvshows to be active
UPDATE tvshows SET status = 'active' WHERE status IS NULL;
