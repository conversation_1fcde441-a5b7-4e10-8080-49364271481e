<?php
// Include necessary files
require_once 'includes/config.php';
require_once 'includes/streaming_helper.php';

// Test Cloudflare Worker URL
$worker_url = "https://odd-darkness-074fsadsafsafasfjlknmmlkaytr9pe8afnhdklnfalskdftgy.bdmovieshub.workers.dev/994/RRR+2022+-+Hindi+-WEBDL+-+720p.mkv?hash=AgADjg";
$title = "ব্যাক বাটন টেস্ট";
$poster = "https://image.tmdb.org/t/p/w500/wE0I6efAW4cDDmZQWtwZMOW44EJ.jpg";

// Get streaming URL
$streaming_url = "plyr_player_enhanced.php?url=" . urlencode($worker_url) . "&title=" . urlencode($title) . "&poster=" . urlencode($poster);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ব্যাক বাটন টেস্ট</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #111;
            color: #fff;
            padding: 20px;
            font-family: 'SolaimanLipi', Arial, sans-serif;
        }
        .card {
            background-color: #222;
            border: none;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #333;
            color: #fff;
            border-bottom: 1px solid #444;
        }
        .btn-primary {
            background-color: #e50914;
            border-color: #e50914;
        }
        .btn-primary:hover {
            background-color: #b30710;
            border-color: #b30710;
        }
        .feature-list {
            list-style-type: none;
            padding-left: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #333;
            display: flex;
            align-items: center;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list li i {
            color: #e50914;
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">ব্যাক বাটন টেস্ট</h1>
        
        <div class="card">
            <div class="card-header">
                <h5>ব্যাক বাটন পরিবর্তন</h5>
            </div>
            <div class="card-body">
                <p>এই পেজ থেকে প্লেয়ার পেজে গেলে, "আগের পেজে ফিরে যান" বাটনে ক্লিক করলে আপনি এই পেজে ফিরে আসবেন।</p>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>কিভাবে কাজ করে:</strong> প্লেয়ার পেজ HTTP রেফারার চেক করে এবং সেই অনুযায়ী ব্যাক বাটনের লিংক সেট করে।
                </div>
                
                <div class="mt-4">
                    <a href="<?php echo $streaming_url; ?>" class="btn btn-primary" target="_blank">
                        <i class="fas fa-play-circle me-2"></i>প্লেয়ার খুলুন এবং ব্যাক বাটন টেস্ট করুন
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>পরিবর্তন বিবরণ</h5>
            </div>
            <div class="card-body">
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> "Back to Home" বাটন পরিবর্তন করে "আগের পেজে ফিরে যান" করা হয়েছে</li>
                    <li><i class="fas fa-check"></i> HTTP রেফারার ব্যবহার করে আগের পেজে ফিরে যাওয়ার ব্যবস্থা করা হয়েছে</li>
                    <li><i class="fas fa-check"></i> সিকিউরিটি চেক যুক্ত করা হয়েছে যাতে বাইরের সাইট থেকে আসলে হোম পেজে নিয়ে যায়</li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
