<?php
// Set page title
$page_title = 'Fix Posters';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include config file
require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
    exit; // Make sure to exit after redirect
}

// Process form submission
$success_message = '';
$error_message = '';
$fixed_count = 0;
$failed_count = 0;

if (isset($_POST['fix_posters'])) {
    // Get all movies with TMDB poster paths
    $movies_query = "SELECT id, tmdb_id, poster FROM movies WHERE poster LIKE '/%'";
    $movies_result = mysqli_query($conn, $movies_query);

    if ($movies_result) {
        while ($movie = mysqli_fetch_assoc($movies_result)) {
            $tmdb_id = $movie['tmdb_id'];
            $poster_path = $movie['poster'];

            // Download poster image from TMDB
            if (!empty($poster_path)) {
                $poster_url = 'https://image.tmdb.org/t/p/w500' . $poster_path;
                $poster_filename = 'movie_poster_' . $tmdb_id . '_' . time() . '.jpg';
                $poster_path_local = '../uploads/' . $poster_filename;

                // Create uploads directory if it doesn't exist
                if (!file_exists('../uploads')) {
                    mkdir('../uploads', 0777, true);
                }

                // Download the image
                $image_content = @file_get_contents($poster_url);
                if ($image_content !== false) {
                    file_put_contents($poster_path_local, $image_content);

                    // Update movie record
                    $update_query = "UPDATE movies SET poster = '$poster_filename' WHERE id = {$movie['id']}";
                    if (mysqli_query($conn, $update_query)) {
                        $fixed_count++;
                    } else {
                        $failed_count++;
                    }
                } else {
                    $failed_count++;
                }
            }
        }
    }

    // Get all TV shows with TMDB poster paths
    $tvshows_query = "SELECT id, tmdb_id, poster FROM tvshows WHERE poster LIKE '/%'";
    $tvshows_result = mysqli_query($conn, $tvshows_query);

    if ($tvshows_result) {
        while ($tvshow = mysqli_fetch_assoc($tvshows_result)) {
            $tmdb_id = $tvshow['tmdb_id'];
            $poster_path = $tvshow['poster'];

            // Download poster image from TMDB
            if (!empty($poster_path)) {
                $poster_url = 'https://image.tmdb.org/t/p/w500' . $poster_path;
                $poster_filename = 'tvshow_poster_' . $tmdb_id . '_' . time() . '.jpg';
                $poster_path_local = '../uploads/' . $poster_filename;

                // Create uploads directory if it doesn't exist
                if (!file_exists('../uploads')) {
                    mkdir('../uploads', 0777, true);
                }

                // Download the image
                $image_content = @file_get_contents($poster_url);
                if ($image_content !== false) {
                    file_put_contents($poster_path_local, $image_content);

                    // Update TV show record
                    $update_query = "UPDATE tvshows SET poster = '$poster_filename' WHERE id = {$tvshow['id']}";
                    if (mysqli_query($conn, $update_query)) {
                        $fixed_count++;
                    } else {
                        $failed_count++;
                    }
                } else {
                    $failed_count++;
                }
            }
        }
    }

    if ($fixed_count > 0) {
        $success_message = "Fixed $fixed_count posters successfully.";
    }

    if ($failed_count > 0) {
        $error_message = "Failed to fix $failed_count posters.";
    }

    if ($fixed_count == 0 && $failed_count == 0) {
        $success_message = "No posters needed fixing.";
    }
}

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title d-flex align-items-center">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Fix Posters</h1>
            </div>

            <div class="topbar-actions">
                <div class="dropdown">
                    <button class="btn btn-link dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <img src="assets/img/user.png" class="rounded-circle" width="32" height="32" alt="User">
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user fa-sm me-2 text-gray-400"></i> Profile</a></li>
                        <li><a class="dropdown-item" href="site_settings.php"><i class="fas fa-cogs fa-sm me-2 text-gray-400"></i> Settings</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/logout.php"><i class="fas fa-sign-out-alt fa-sm me-2 text-gray-400"></i> Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <?php if($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="card mb-4">
            <div class="card-header">
                <h6 class="fw-bold text-primary mb-0">Fix Missing Posters</h6>
            </div>
            <div class="card-body">
                <p>This tool will download posters from TMDB for all movies and TV shows that have TMDB poster paths but no local posters.</p>
                <p>Use this if you're seeing missing posters on your site.</p>

                <form method="POST" action="">
                    <button type="submit" name="fix_posters" class="btn btn-primary">
                        <i class="fas fa-sync-alt me-2"></i>Fix Posters
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
