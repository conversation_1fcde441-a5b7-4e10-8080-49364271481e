import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class TvFocusableWidget extends StatefulWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final VoidCallback? onLongPress;
  final bool autofocus;
  final FocusNode? focusNode;
  final String? debugLabel;
  final EdgeInsets padding;
  final Color? focusColor;
  final double borderRadius;

  const TvFocusableWidget({
    super.key,
    required this.child,
    this.onPressed,
    this.onLongPress,
    this.autofocus = false,
    this.focusNode,
    this.debugLabel,
    this.padding = const EdgeInsets.all(8.0),
    this.focusColor,
    this.borderRadius = 8.0,
  });

  @override
  State<TvFocusableWidget> createState() => _TvFocusableWidgetState();
}

class _TvFocusableWidgetState extends State<TvFocusableWidget> {
  late FocusNode _focusNode;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    } else {
      _focusNode.removeListener(_onFocusChange);
    }
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      autofocus: widget.autofocus,
      debugLabel: widget.debugLabel,
      onKeyEvent: (node, event) {
        if (event is KeyDownEvent) {
          // Handle Enter/Select key
          if (event.logicalKey == LogicalKeyboardKey.select ||
              event.logicalKey == LogicalKeyboardKey.enter ||
              event.logicalKey == LogicalKeyboardKey.space) {
            if (widget.onPressed != null) {
              widget.onPressed!();
              return KeyEventResult.handled;
            }
          }
          
          // Handle Menu key for long press actions
          if (event.logicalKey == LogicalKeyboardKey.contextMenu) {
            if (widget.onLongPress != null) {
              widget.onLongPress!();
              return KeyEventResult.handled;
            }
          }
        }
        return KeyEventResult.ignored;
      },
      child: GestureDetector(
        onTap: widget.onPressed,
        onLongPress: widget.onLongPress,
        child: Container(
          padding: widget.padding,
          decoration: BoxDecoration(
            border: _isFocused
                ? Border.all(
                    color: widget.focusColor ?? Colors.white,
                    width: 2.0,
                  )
                : null,
            borderRadius: BorderRadius.circular(widget.borderRadius),
            color: _isFocused
                ? (widget.focusColor ?? Colors.white).withOpacity(0.1)
                : null,
          ),
          child: widget.child,
        ),
      ),
    );
  }
}

class TvGridView extends StatefulWidget {
  final List<Widget> children;
  final int crossAxisCount;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final EdgeInsets padding;
  final ScrollController? scrollController;

  const TvGridView({
    super.key,
    required this.children,
    this.crossAxisCount = 3,
    this.mainAxisSpacing = 16.0,
    this.crossAxisSpacing = 16.0,
    this.padding = const EdgeInsets.all(16.0),
    this.scrollController,
  });

  @override
  State<TvGridView> createState() => _TvGridViewState();
}

class _TvGridViewState extends State<TvGridView> {
  late ScrollController _scrollController;
  int _focusedIndex = 0;
  late List<FocusNode> _focusNodes;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();
    _focusNodes = List.generate(
      widget.children.length,
      (index) => FocusNode(debugLabel: 'GridItem_$index'),
    );
    
    // Set first item as focused
    if (_focusNodes.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNodes[0].requestFocus();
      });
    }
  }

  @override
  void dispose() {
    if (widget.scrollController == null) {
      _scrollController.dispose();
    }
    for (final node in _focusNodes) {
      node.dispose();
    }
    super.dispose();
  }

  void _handleKeyEvent(int index, KeyEvent event) {
    if (event is! KeyDownEvent) return;

    final key = event.logicalKey;
    int newIndex = index;

    // Navigation keys
    if (key == LogicalKeyboardKey.arrowRight) {
      newIndex = (index + 1) % widget.children.length;
    } else if (key == LogicalKeyboardKey.arrowLeft) {
      newIndex = (index - 1 + widget.children.length) % widget.children.length;
    } else if (key == LogicalKeyboardKey.arrowDown) {
      newIndex = (index + widget.crossAxisCount);
      if (newIndex >= widget.children.length) {
        newIndex = index; // Stay at current position if can't go down
      }
    } else if (key == LogicalKeyboardKey.arrowUp) {
      newIndex = (index - widget.crossAxisCount);
      if (newIndex < 0) {
        newIndex = index; // Stay at current position if can't go up
      }
    }

    if (newIndex != index && newIndex >= 0 && newIndex < widget.children.length) {
      setState(() {
        _focusedIndex = newIndex;
      });
      _focusNodes[newIndex].requestFocus();
      _scrollToIndex(newIndex);
    }
  }

  void _scrollToIndex(int index) {
    final itemHeight = 200.0; // Approximate item height
    final rowIndex = index ~/ widget.crossAxisCount;
    final targetOffset = rowIndex * (itemHeight + widget.mainAxisSpacing);
    
    _scrollController.animateTo(
      targetOffset,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      controller: _scrollController,
      padding: widget.padding,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.crossAxisCount,
        mainAxisSpacing: widget.mainAxisSpacing,
        crossAxisSpacing: widget.crossAxisSpacing,
        childAspectRatio: 0.7,
      ),
      itemCount: widget.children.length,
      itemBuilder: (context, index) {
        return Focus(
          focusNode: _focusNodes[index],
          onKeyEvent: (node, event) {
            _handleKeyEvent(index, event);
            return KeyEventResult.handled;
          },
          child: widget.children[index],
        );
      },
    );
  }
}

// TV-specific button widget
class TvButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final bool autofocus;
  final Color? backgroundColor;
  final Color? textColor;

  const TvButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.autofocus = false,
    this.backgroundColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return TvFocusableWidget(
      onPressed: onPressed,
      autofocus: autofocus,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        decoration: BoxDecoration(
          color: backgroundColor ?? Colors.blue,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(icon, color: textColor ?? Colors.white),
              const SizedBox(width: 8),
            ],
            Text(
              text,
              style: TextStyle(
                color: textColor ?? Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
