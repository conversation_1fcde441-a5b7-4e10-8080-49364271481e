<?php
// Include config file
require_once 'includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    echo "You must be logged in as an admin to use this script.";
    exit;
}

// Define the new plan details
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get data from form
    $name = sanitize($_POST['name']);
    $price = (float)$_POST['price'];
    $duration = (int)$_POST['duration'];
    $features = isset($_POST['features']) ? sanitize($_POST['features']) : '';
} else {
    // Default values if not submitted via form
    $name = "New Premium Plan";
    $price = 299;
    $duration = 90;
    $features = "Access to all premium movies and TV shows\nWatch on multiple devices\nHD quality streaming\nPriority support";
}

// Insert the new plan
$query = "INSERT INTO premium_plans (name, price, duration, features)
          VALUES ('$name', $price, $duration, '$features')";

if (mysqli_query($conn, $query)) {
    echo "New premium plan added successfully!";
    echo "<br><br>Plan Details:<br>";
    echo "Name: $name<br>";
    echo "Price: ৳$price<br>";
    echo "Duration: $duration days<br>";
    echo "Features:<br><pre>$features</pre>";

    echo "<br><br><a href='admin/premium_plans.php'>Go to Premium Plans Admin Page</a>";
} else {
    echo "Error adding premium plan: " . mysqli_error($conn);
}
?>
