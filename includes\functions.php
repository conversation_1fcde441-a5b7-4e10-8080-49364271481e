<?php
/**
 * Common functions for the website
 */

// Note: sanitize() function is already defined in config.php

// Check if user is premium
function is_user_premium($user_id) {
    global $conn;

    $query = "SELECT is_premium FROM users WHERE id = $user_id";
    $result = mysqli_query($conn, $query);

    if ($result && mysqli_num_rows($result) > 0) {
        $user = mysqli_fetch_assoc($result);
        return (bool) $user['is_premium'];
    }

    return false;
}

// Get user's active subscription
function get_active_subscription($user_id) {
    global $conn;

    $query = "SELECT s.*, p.name as plan_name, p.price, p.duration_months
              FROM subscriptions s
              JOIN plans p ON s.plan_id = p.id
              WHERE s.user_id = $user_id AND s.status = 'active' AND s.end_date > NOW()
              ORDER BY s.end_date DESC
              LIMIT 1";
    $result = mysqli_query($conn, $query);

    if ($result && mysqli_num_rows($result) > 0) {
        return mysqli_fetch_assoc($result);
    }

    return null;
}

// Format date
function format_date($date, $format = 'd M Y') {
    return date($format, strtotime($date));
}

// Get user by ID
function get_user($user_id) {
    global $conn;

    $query = "SELECT * FROM users WHERE id = $user_id";
    $result = mysqli_query($conn, $query);

    if ($result && mysqli_num_rows($result) > 0) {
        return mysqli_fetch_assoc($result);
    }

    return null;
}

// Get plan by ID
function get_plan($plan_id) {
    global $conn;

    $query = "SELECT * FROM plans WHERE id = $plan_id";
    $result = mysqli_query($conn, $query);

    if ($result && mysqli_num_rows($result) > 0) {
        return mysqli_fetch_assoc($result);
    }

    return null;
}

// Get payment by ID
function get_payment($payment_id) {
    global $conn;

    $query = "SELECT * FROM payments WHERE id = $payment_id";
    $result = mysqli_query($conn, $query);

    if ($result && mysqli_num_rows($result) > 0) {
        return mysqli_fetch_assoc($result);
    }

    return null;
}

// Get user's payments
function get_user_payments($user_id) {
    global $conn;

    $query = "SELECT p.*, s.plan_id, s.start_date, s.end_date, s.status as subscription_status
              FROM payments p
              LEFT JOIN subscriptions s ON p.subscription_id = s.id
              WHERE p.user_id = $user_id
              ORDER BY p.created_at DESC";
    $result = mysqli_query($conn, $query);

    $payments = array();
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $payments[] = $row;
        }
    }

    return $payments;
}

// Get payment status text
function get_payment_status_text($status) {
    switch ($status) {
        case 'completed':
            return 'সম্পন্ন';
        case 'pending':
            return 'পেন্ডিং';
        case 'cancelled':
            return 'বাতিল';
        default:
            return $status;
    }
}

// Get subscription status text
function get_subscription_status_text($status) {
    switch ($status) {
        case 'active':
            return 'সক্রিয়';
        case 'pending':
            return 'পেন্ডিং';
        case 'expired':
            return 'মেয়াদ শেষ';
        case 'cancelled':
            return 'বাতিল';
        default:
            return $status;
    }
}
?>
