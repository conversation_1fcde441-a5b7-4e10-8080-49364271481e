<?php
require_once 'includes/header.php';
require_once 'includes/ad_placeholder.php';

// Get featured movies
$featured_movies_query = "SELECT m.*, c.name as category_name FROM movies m LEFT JOIN categories c ON m.category_id = c.id ORDER BY m.rating DESC LIMIT 5";
$featured_movies_result = mysqli_query($conn, $featured_movies_query);

// Get latest movies
$latest_movies_query = "SELECT m.*, c.name as category_name FROM movies m LEFT JOIN categories c ON m.category_id = c.id ORDER BY m.created_at DESC LIMIT 12";
$latest_movies_result = mysqli_query($conn, $latest_movies_query);

// Get latest TV shows
$latest_tvshows_query = "SELECT t.*, c.name as category_name FROM tvshows t LEFT JOIN categories c ON t.category_id = c.id ORDER BY t.created_at DESC LIMIT 12";
$latest_tvshows_result = mysqli_query($conn, $latest_tvshows_query);
?>

<!-- Hero Slider Section -->
<section class="main-slider">
    <div class="slider-wrapper">
        <?php if(mysqli_num_rows($featured_movies_result) > 0): ?>
        <?php while($movie = mysqli_fetch_assoc($featured_movies_result)): ?>
        <div class="slide active">
            <div class="slide-bg" style="background-image: url('<?php echo SITE_URL; ?>/uploads/<?php echo $movie['poster']; ?>');">
                <div class="slide-overlay"></div>
                <div class="container">
                    <div class="slide-content">
                        <div class="slide-badges">
                            <span class="badge bg-danger"><i class="fas fa-film me-1"></i> Movie</span>
                        </div>
                        <h1 class="slide-title"><?php echo htmlspecialchars($movie['title']); ?></h1>
                        <div class="slide-meta">
                            <span><i class="fas fa-calendar"></i> <?php echo $movie['release_year']; ?></span>
                            <span><i class="fas fa-star"></i> <?php echo number_format($movie['rating'], 1); ?></span>
                            <span><i class="fas fa-tag"></i> <?php echo htmlspecialchars($movie['category_name']); ?></span>
                        </div>
                        <p class="slide-description"><?php echo htmlspecialchars(substr($movie['description'], 0, 200)); ?>...</p>
                        <div class="slide-buttons">
                            <a href="<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $movie['id']; ?>" class="btn btn-danger btn-lg">
                                <i class="fas fa-play me-2"></i> এখনই দেখুন
                            </a>
                            <a href="<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $movie['id']; ?>#download" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-download me-2"></i> ডাউনলোড
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endwhile; ?>
        <?php endif; ?>
    </div>
</section>

<!-- Top Banner Ad -->
<div class="container my-4">
    <?php if (!isPremiumUser()): ?>
    <div class="ad-container ad-banner">
        <script type="text/javascript">
            atOptions = {
                "key" : "735a559a5872816da47237a603cac4ad",
                "format" : "iframe",
                "height" : 90,
                "width" : 728,
                "params" : {}
            };
        </script>
        <script type="text/javascript" src="//www.highperformanceformat.com/735a559a5872816da47237a603cac4ad/invoke.js"></script>
    </div>
    <?php endif; ?>
</div>

<!-- Latest Movies Section -->
<section class="py-5">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="section-title mb-0"><i class="fas fa-clock text-info me-2"></i>সর্বশেষ মুভি</h2>
            <a href="<?php echo SITE_URL; ?>/movies.php" class="btn btn-outline-danger">সব দেখুন <i class="fas fa-arrow-right ms-1"></i></a>
        </div>
        <div class="row">
            <?php if(mysqli_num_rows($latest_movies_result) > 0): ?>
            <?php while($movie = mysqli_fetch_assoc($latest_movies_result)): ?>
            <div class="col-6 col-md-4 col-lg-2 mb-4">
                <div class="movie-card">
                    <?php if($movie['premium_only']): ?>
                    <div class="premium-badge">
                        <i class="fas fa-crown"></i> Premium
                    </div>
                    <?php endif; ?>
                    <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $movie['poster']; ?>" alt="<?php echo htmlspecialchars($movie['title']); ?>" loading="lazy">
                    <a href="<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $movie['id']; ?>" class="card-link"></a>
                    <div class="movie-card-overlay">
                        <h5 class="movie-card-title"><?php echo htmlspecialchars($movie['title']); ?></h5>
                        <div class="movie-card-info">
                            <span><?php echo $movie['release_year']; ?></span> •
                            <span><?php echo htmlspecialchars($movie['category_name']); ?></span>
                        </div>
                        <div class="movie-card-rating">
                            <i class="fas fa-star"></i> <?php echo number_format($movie['rating'], 1); ?>
                        </div>
                        <a href="<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $movie['id']; ?>" class="btn btn-danger btn-sm mt-2">এখনই দেখুন</a>
                    </div>
                </div>
            </div>
            <?php endwhile; ?>
            <?php else: ?>
            <div class="col-12">
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i>কোনো মুভি পাওয়া যায়নি।
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Middle Ad -->
<div class="container my-4">
    <?php if (!isPremiumUser()): ?>
    <div class="ad-container ad-inline">
        <script type="text/javascript">
            atOptions = {
                "key" : "ceac305b755cbace9181e4d593e3700b",
                "format" : "iframe",
                "height" : 60,
                "width" : 468,
                "params" : {}
            };
        </script>
        <script type="text/javascript" src="//www.highperformanceformat.com/ceac305b755cbace9181e4d593e3700b/invoke.js"></script>
    </div>
    <?php endif; ?>
</div>

<!-- Latest TV Shows Section -->
<section class="py-5 bg-dark">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="section-title mb-0"><i class="fas fa-tv text-primary me-2"></i>সর্বশেষ টিভি শো</h2>
            <a href="<?php echo SITE_URL; ?>/tvshows.php" class="btn btn-outline-danger">সব দেখুন <i class="fas fa-arrow-right ms-1"></i></a>
        </div>
        <div class="row">
            <?php if(mysqli_num_rows($latest_tvshows_result) > 0): ?>
            <?php while($tvshow = mysqli_fetch_assoc($latest_tvshows_result)): ?>
            <div class="col-6 col-md-4 col-lg-2 mb-4">
                <div class="movie-card">
                    <?php if($tvshow['premium_only']): ?>
                    <div class="premium-badge">
                        <i class="fas fa-crown"></i> Premium
                    </div>
                    <?php endif; ?>
                    <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $tvshow['poster']; ?>" alt="<?php echo htmlspecialchars($tvshow['title']); ?>" loading="lazy">
                    <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $tvshow['id']; ?>" class="card-link"></a>
                    <div class="movie-card-overlay">
                        <h5 class="movie-card-title"><?php echo htmlspecialchars($tvshow['title']); ?></h5>
                        <div class="movie-card-info">
                            <span><?php echo $tvshow['start_year']; ?><?php echo $tvshow['end_year'] ? ' - ' . $tvshow['end_year'] : ' - Present'; ?></span> •
                            <span><?php echo htmlspecialchars($tvshow['category_name']); ?></span>
                        </div>
                        <div class="movie-card-rating">
                            <i class="fas fa-star"></i> <?php echo number_format($tvshow['rating'], 1); ?>
                        </div>
                        <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $tvshow['id']; ?>" class="btn btn-danger btn-sm mt-2">এখনই দেখুন</a>
                    </div>
                </div>
            </div>
            <?php endwhile; ?>
            <?php else: ?>
            <div class="col-12">
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i>কোনো টিভি শো পাওয়া যায়নি।
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Bottom Ad -->
<div class="container my-4">
    <?php if (!isPremiumUser()): ?>
    <div class="ad-container ad-sidebar">
        <script type="text/javascript">
            atOptions = {
                "key" : "7d3b7accac0194a88ccf420c241ec7aa",
                "format" : "iframe",
                "height" : 250,
                "width" : 300,
                "params" : {}
            };
        </script>
        <script type="text/javascript" src="//www.highperformanceformat.com/7d3b7accac0194a88ccf420c241ec7aa/invoke.js"></script>
    </div>
    <?php endif; ?>
</div>

<!-- Native Ads -->
<div class="container my-4">
    <?php if (!isPremiumUser()): ?>
    <div class="ad-container ad-native">
        <script type='text/javascript' src='//pl27076825.profitableratecpm.com/db/ba/2e/dbba2edda331c47423c9b3fc68f95fb1.js'></script>
    </div>
    <?php endif; ?>
</div>

<div class="container my-4">
    <?php if (!isPremiumUser()): ?>
    <div class="ad-container ad-native">
        <script type='text/javascript' src='//pl27076956.profitableratecpm.com/fd/6c/f8/fd6cf8e64921887d3713ef08ffd94b55.js'></script>
    </div>
    <?php endif; ?>
</div>

<?php require_once 'includes/footer.php'; ?>
