<?php
// Database Update Script for Mobile App Integration

// Direct database connection to bypass API authentication
$db_host = 'localhost'; // Change if needed
$db_user = 'tipsbdxy_4525'; // Change to your database username
$db_pass = 'tipsbdxy_4525'; // Change to your database password
$db_name = 'tipsbdxy_4525'; // Change to your database name

// Create connection
$conn = mysqli_connect($db_host, $db_user, $db_pass, $db_name);

// Check connection
if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// Include config file for constants
@include_once 'config.php';

// Start output buffering
ob_start();
echo "<h1>Database Update for Mobile App Integration</h1>";
echo "<pre>";

// Function to execute SQL and display result
function execute_sql($conn, $sql, $description) {
    echo "\n\n=== $description ===\n";
    echo "$sql\n";

    if (mysqli_query($conn, $sql)) {
        echo "SUCCESS: $description\n";
        return true;
    } else {
        echo "ERROR: " . mysqli_error($conn) . "\n";
        return false;
    }
}

// Check if JWT_SECRET is defined
if (!defined('JWT_SECRET')) {
    // Add JWT_SECRET to config.php
    $jwt_secret = bin2hex(random_bytes(32)); // Generate a random secret
    $config_file = file_get_contents('config.php');

    if (strpos($config_file, 'JWT_SECRET') === false) {
        $config_file .= "\n\n// JWT Secret Key for API Authentication\ndefine('JWT_SECRET', '$jwt_secret');\n";
        file_put_contents('config.php', $config_file);
        echo "Added JWT_SECRET to config.php\n";
    }
}

// 1. Create device_tokens table
$sql = "CREATE TABLE IF NOT EXISTS device_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    device_token VARCHAR(255) NOT NULL,
    device_type ENUM('android', 'ios') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_device_token (device_token)
)";
execute_sql($conn, $sql, "Create device_tokens table");

// 2. Create app_config table
$sql = "CREATE TABLE IF NOT EXISTS app_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL,
    config_value TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_config_key (config_key)
)";
execute_sql($conn, $sql, "Create app_config table");

// 3. Create notifications table
$sql = "CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    image_url VARCHAR(255) NULL,
    action_type ENUM('none', 'movie', 'tvshow', 'url') DEFAULT 'none',
    action_id VARCHAR(100) NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
)";
execute_sql($conn, $sql, "Create notifications table");

// 4. Create subtitles table
$sql = "CREATE TABLE IF NOT EXISTS subtitles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    content_type ENUM('movie', 'episode') NOT NULL,
    content_id INT NOT NULL,
    language VARCHAR(50) NOT NULL,
    url VARCHAR(255) NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_content (content_type, content_id)
)";
execute_sql($conn, $sql, "Create subtitles table");

// 5. Create api_logs table
$sql = "CREATE TABLE IF NOT EXISTS api_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    user_id INT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    status_code INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user (user_id),
    INDEX idx_endpoint (endpoint),
    INDEX idx_created_at (created_at)
)";
execute_sql($conn, $sql, "Create api_logs table");

// 6. Update watchlist table
$sql = "SHOW COLUMNS FROM watchlist LIKE 'last_watched_position'";
$result = mysqli_query($conn, $sql);

if (mysqli_num_rows($result) == 0) {
    $sql = "ALTER TABLE watchlist
        ADD COLUMN last_watched_position INT DEFAULT 0,
        ADD COLUMN last_watched_at TIMESTAMP NULL";
    execute_sql($conn, $sql, "Update watchlist table");
}

// 7. Update users table
$sql = "SHOW COLUMNS FROM users LIKE 'profile_image'";
$result = mysqli_query($conn, $sql);

if (mysqli_num_rows($result) == 0) {
    $sql = "ALTER TABLE users
        ADD COLUMN profile_image VARCHAR(255) NULL";
    execute_sql($conn, $sql, "Update users table");
}

// 8. Update download_links table
$sql = "SHOW COLUMNS FROM download_links LIKE 'server_name'";
$result = mysqli_query($conn, $sql);

if (mysqli_num_rows($result) == 0) {
    $sql = "ALTER TABLE download_links
        ADD COLUMN server_name VARCHAR(100) NULL,
        ADD COLUMN file_size VARCHAR(20) NULL";
    execute_sql($conn, $sql, "Update download_links table");
}

// 9. Insert default app_config values
$default_configs = [
    ['app_version', '1.0.0'],
    ['min_app_version', '1.0.0'],
    ['force_update', 'false'],
    ['update_message', 'Please update to the latest version for new features and bug fixes.'],
    ['maintenance_mode', 'false'],
    ['maintenance_message', 'We are currently performing maintenance. Please try again later.']
];

foreach ($default_configs as $config) {
    $check_query = "SELECT * FROM app_config WHERE config_key = '{$config[0]}'";
    $result = mysqli_query($conn, $check_query);

    if (mysqli_num_rows($result) == 0) {
        $insert_query = "INSERT INTO app_config (config_key, config_value) VALUES ('{$config[0]}', '{$config[1]}')";
        mysqli_query($conn, $insert_query);
        echo "Inserted default config: {$config[0]}\n";
    }
}

// 10. Update payment_settings table
$sql = "SHOW TABLES LIKE 'payment_settings'";
$result = mysqli_query($conn, $sql);

if (mysqli_num_rows($result) == 0) {
    $sql = "CREATE TABLE IF NOT EXISTS payment_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        bkash_enabled BOOLEAN DEFAULT TRUE,
        bkash_merchant_number VARCHAR(20) NULL,
        bkash_merchant_name VARCHAR(100) NULL,
        nagad_enabled BOOLEAN DEFAULT TRUE,
        nagad_merchant_number VARCHAR(20) NULL,
        nagad_merchant_name VARCHAR(100) NULL,
        rocket_enabled BOOLEAN DEFAULT TRUE,
        rocket_merchant_number VARCHAR(20) NULL,
        rocket_merchant_name VARCHAR(100) NULL,
        payment_instructions TEXT NULL,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    execute_sql($conn, $sql, "Create payment_settings table");

    // Insert default payment settings
    $sql = "INSERT INTO payment_settings (
        bkash_enabled, bkash_merchant_number, bkash_merchant_name,
        nagad_enabled, nagad_merchant_number, nagad_merchant_name,
        rocket_enabled, rocket_merchant_number, rocket_merchant_name,
        payment_instructions
    ) VALUES (
        1, '01XXXXXXXXX', 'CinePix',
        1, '01XXXXXXXXX', 'CinePix',
        1, '01XXXXXXXXX', 'CinePix',
        'Please send the exact amount to the merchant number and provide the transaction ID.'
    )";
    execute_sql($conn, $sql, "Insert default payment settings");
}

// 11. Create payment_requests table if not exists
$sql = "SHOW TABLES LIKE 'payment_requests'";
$result = mysqli_query($conn, $sql);

if (mysqli_num_rows($result) == 0) {
    $sql = "CREATE TABLE IF NOT EXISTS payment_requests (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        plan_id INT NOT NULL,
        payment_method VARCHAR(20) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        payment_id VARCHAR(50) NOT NULL,
        transaction_id VARCHAR(100) NULL,
        status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (plan_id) REFERENCES premium_plans(id) ON DELETE CASCADE
    )";
    execute_sql($conn, $sql, "Create payment_requests table");
}

// 12. Create subscriptions table if not exists
$sql = "SHOW TABLES LIKE 'subscriptions'";
$result = mysqli_query($conn, $sql);

if (mysqli_num_rows($result) == 0) {
    $sql = "CREATE TABLE IF NOT EXISTS subscriptions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        plan_id INT NOT NULL,
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        payment_method VARCHAR(20) NOT NULL,
        transaction_id VARCHAR(100) NULL,
        status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (plan_id) REFERENCES premium_plans(id) ON DELETE CASCADE
    )";
    execute_sql($conn, $sql, "Create subscriptions table");
}

// 13. Create favorites table if not exists
$sql = "SHOW TABLES LIKE 'favorites'";
$result = mysqli_query($conn, $sql);

if (mysqli_num_rows($result) == 0) {
    $sql = "CREATE TABLE IF NOT EXISTS favorites (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        content_type ENUM('movie', 'tvshow') NOT NULL,
        content_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_favorite (user_id, content_type, content_id)
    )";
    execute_sql($conn, $sql, "Create favorites table");
}

// 14. Check if .htaccess file exists
$htaccess_path = __DIR__ . '/.htaccess';
if (!file_exists($htaccess_path)) {
    $htaccess_content = "RewriteEngine On\nRewriteCond %{REQUEST_FILENAME} !-f\nRewriteCond %{REQUEST_FILENAME} !-d\nRewriteRule ^v1/(.*)$ v1/index.php [QSA,L]";
    file_put_contents($htaccess_path, $htaccess_content);
    echo "Created .htaccess file\n";
}

echo "\n\nDatabase update completed successfully!";
echo "</pre>";

// Create a link to test the API
echo '<p><a href="v1/config/app_config" target="_blank">Test API Connection</a></p>';

// End output buffering and flush
ob_end_flush();
?>
