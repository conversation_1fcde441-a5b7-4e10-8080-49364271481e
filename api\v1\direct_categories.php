<?php
// Include direct config file
require_once '../direct_config.php';

// Get categories
$query = "SELECT * FROM categories ORDER BY name ASC";
$result = mysqli_query($conn, $query);

if (!$result) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to fetch categories: ' . mysqli_error($conn),
        'data' => null
    ]);
    exit;
}

$categories = [];
while ($category = mysqli_fetch_assoc($result)) {
    // Count movies in this category
    $movies_count_query = "SELECT COUNT(*) as count FROM movies 
                          WHERE category_id = {$category['id']} AND status = 'active'";
    $movies_count_result = mysqli_query($conn, $movies_count_query);
    $movies_count = mysqli_fetch_assoc($movies_count_result)['count'];
    
    // Count TV shows in this category
    $tvshows_count_query = "SELECT COUNT(*) as count FROM tvshows 
                           WHERE category_id = {$category['id']} AND status = 'active'";
    $tvshows_count_result = mysqli_query($conn, $tvshows_count_query);
    $tvshows_count = mysqli_fetch_assoc($tvshows_count_result)['count'];
    
    $categories[] = [
        'id' => (int)$category['id'],
        'name' => $category['name'],
        'movies_count' => (int)$movies_count,
        'tvshows_count' => (int)$tvshows_count
    ];
}

// Return categories
http_response_code(200);
echo json_encode([
    'success' => true,
    'message' => 'Success',
    'data' => [
        'categories' => $categories
    ]
]);
?>
