<?php
// Include configuration file
require_once '../config.php';

// Include database connection
require_once '../db_connect.php';

// Include functions
require_once '../functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

// Get active sessions
$active_time = date('Y-m-d H:i:s', strtotime('-15 minutes'));
$sessions_query = "SELECT us.*, u.username, u.email, u.role, u.is_premium
                  FROM user_sessions us
                  LEFT JOIN users u ON us.user_id = u.id
                  WHERE us.last_activity >= '$active_time'
                  ORDER BY us.last_activity DESC";
$sessions_result = mysqli_query($conn, $sessions_query);

// Get session count
$session_count = mysqli_num_rows($sessions_result);

// Get unique visitor count
$visitors_query = "SELECT COUNT(DISTINCT ip_address) as unique_visitors
                  FROM user_sessions
                  WHERE last_activity >= '$active_time'";
$visitors_result = mysqli_query($conn, $visitors_query);
$visitors_data = mysqli_fetch_assoc($visitors_result);
$unique_visitors = $visitors_data['unique_visitors'];

// Get page views
$pageviews_query = "SELECT page_url, COUNT(*) as view_count
                   FROM page_views
                   WHERE created_at >= '$active_time'
                   GROUP BY page_url
                   ORDER BY view_count DESC
                   LIMIT 10";
$pageviews_result = mysqli_query($conn, $pageviews_query);

// Set page title
$page_title = 'লাইভ ইউজার ট্র্যাকিং';

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content Wrapper -->
<div class="content">
    <!-- Topbar -->
    <?php include 'includes/topbar.php'; ?>

    <!-- Begin Page Content -->
    <div class="container-fluid px-4">
        <!-- Page Heading -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0 text-gray-800">লাইভ ইউজার ট্র্যাকিং</h1>
            <button id="refreshData" class="btn btn-primary">
                <i class="fas fa-sync-alt me-1"></i> রিফ্রেশ
            </button>
        </div>

        <!-- Stats Overview -->
        <div class="row mb-4">
            <!-- Active Sessions -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-primary fw-bold">সক্রিয় সেশন</h6>
                            <div class="icon-circle bg-primary-light">
                                <i class="fas fa-users text-primary"></i>
                            </div>
                        </div>
                        <div class="mb-1">
                            <h2 class="fw-bold mb-0" id="activeSessionsCount"><?php echo $session_count; ?></h2>
                            <p class="text-muted small">গত ১৫ মিনিটে সক্রিয় সেশন</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Unique Visitors -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-success fw-bold">ইউনিক ভিজিটর</h6>
                            <div class="icon-circle bg-success-light">
                                <i class="fas fa-user-check text-success"></i>
                            </div>
                        </div>
                        <div class="mb-1">
                            <h2 class="fw-bold mb-0" id="uniqueVisitorsCount"><?php echo $unique_visitors; ?></h2>
                            <p class="text-muted small">গত ১৫ মিনিটে ইউনিক ভিজিটর</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Registered Users -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-info fw-bold">রেজিস্টার্ড ইউজার</h6>
                            <div class="icon-circle bg-info-light">
                                <i class="fas fa-user-tag text-info"></i>
                            </div>
                        </div>
                        <div class="mb-1">
                            <h2 class="fw-bold mb-0" id="registeredUsersCount">
                                <?php
                                $registered_count = 0;
                                mysqli_data_seek($sessions_result, 0);
                                while ($session = mysqli_fetch_assoc($sessions_result)) {
                                    if (!empty($session['user_id'])) {
                                        $registered_count++;
                                    }
                                }
                                echo $registered_count;
                                ?>
                            </h2>
                            <p class="text-muted small">গত ১৫ মিনিটে সক্রিয় রেজিস্টার্ড ইউজার</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Premium Users -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-warning fw-bold">প্রিমিয়াম ইউজার</h6>
                            <div class="icon-circle bg-warning-light">
                                <i class="fas fa-crown text-warning"></i>
                            </div>
                        </div>
                        <div class="mb-1">
                            <h2 class="fw-bold mb-0" id="premiumUsersCount">
                                <?php
                                $premium_count = 0;
                                mysqli_data_seek($sessions_result, 0);
                                while ($session = mysqli_fetch_assoc($sessions_result)) {
                                    if (!empty($session['user_id']) && !empty($session['is_premium'])) {
                                        $premium_count++;
                                    }
                                }
                                echo $premium_count;
                                ?>
                            </h2>
                            <p class="text-muted small">গত ১৫ মিনিটে সক্রিয় প্রিমিয়াম ইউজার</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Users Table -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">সক্রিয় ইউজার</h6>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                            <label class="form-check-label" for="autoRefresh">অটো রিফ্রেশ (৩০ সেকেন্ড)</label>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="activeUsersTable">
                                <thead>
                                    <tr>
                                        <th>ইউজার</th>
                                        <th>আইপি অ্যাড্রেস</th>
                                        <th>ডিভাইস</th>
                                        <th>পেজ</th>
                                        <th>সর্বশেষ অ্যাক্টিভিটি</th>
                                        <th>অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody id="activeUsersTableBody">
                                    <?php
                                    mysqli_data_seek($sessions_result, 0);
                                    while ($session = mysqli_fetch_assoc($sessions_result)):
                                    ?>
                                    <tr>
                                        <td>
                                            <?php if (!empty($session['user_id'])): ?>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-<?php echo $session['is_premium'] ? 'warning' : 'primary'; ?> me-2">
                                                        <?php echo $session['is_premium'] ? 'প্রিমিয়াম' : 'রেজিস্টার্ড'; ?>
                                                    </span>
                                                    <?php echo htmlspecialchars($session['username']); ?>
                                                </div>
                                            <?php else: ?>
                                                <span class="badge bg-secondary me-2">অতিথি</span> অজানা ভিজিটর
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($session['ip_address']); ?></td>
                                        <td>
                                            <small>
                                                <?php echo htmlspecialchars($session['device_type']); ?> / 
                                                <?php echo htmlspecialchars($session['browser']); ?> / 
                                                <?php echo htmlspecialchars($session['os']); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <a href="<?php echo htmlspecialchars($session['page_url']); ?>" target="_blank" class="text-truncate d-inline-block" style="max-width: 200px;">
                                                <?php echo htmlspecialchars($session['page_url']); ?>
                                            </a>
                                        </td>
                                        <td>
                                            <span class="timeago" data-timestamp="<?php echo $session['last_activity']; ?>">
                                                <?php echo timeAgo($session['last_activity']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if (!empty($session['user_id'])): ?>
                                            <a href="user_details.php?id=<?php echo $session['user_id']; ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php else: ?>
                                            <button class="btn btn-sm btn-secondary" disabled>
                                                <i class="fas fa-eye-slash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Page Views -->
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">সর্বাধিক দেখা পেজ</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>পেজ</th>
                                        <th>ভিউ কাউন্ট</th>
                                    </tr>
                                </thead>
                                <tbody id="pageViewsTableBody">
                                    <?php while ($page = mysqli_fetch_assoc($pageviews_result)): ?>
                                    <tr>
                                        <td>
                                            <a href="<?php echo htmlspecialchars($page['page_url']); ?>" target="_blank" class="text-truncate d-inline-block" style="max-width: 300px;">
                                                <?php echo htmlspecialchars($page['page_url']); ?>
                                            </a>
                                        </td>
                                        <td><?php echo $page['view_count']; ?></td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Live Activity Chart -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">লাইভ অ্যাক্টিভিটি</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" style="position: relative; height:300px;">
                            <canvas id="liveActivityChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- Moment.js for time formatting -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize activity chart
    const activityCtx = document.getElementById('liveActivityChart').getContext('2d');
    const activityLabels = [];
    const activityData = [];
    
    // Generate initial data for the last 30 minutes (30 data points)
    const now = moment();
    for (let i = 29; i >= 0; i--) {
        activityLabels.push(moment(now).subtract(i, 'minutes').format('HH:mm'));
        activityData.push(0); // Will be updated with real data
    }
    
    const liveActivityChart = new Chart(activityCtx, {
        type: 'line',
        data: {
            labels: activityLabels,
            datasets: [{
                label: 'অ্যাক্টিভ ইউজার',
                data: activityData,
                backgroundColor: 'rgba(78, 115, 223, 0.05)',
                borderColor: 'rgba(78, 115, 223, 1)',
                borderWidth: 2,
                pointBackgroundColor: 'rgba(78, 115, 223, 1)',
                pointBorderColor: '#fff',
                pointRadius: 3,
                pointHoverRadius: 5,
                fill: true,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            }
        }
    });
    
    // Function to update data
    function updateData() {
        fetch('ajax/get_live_tracking_data.php')
            .then(response => response.json())
            .then(data => {
                // Update stats
                document.getElementById('activeSessionsCount').textContent = data.session_count;
                document.getElementById('uniqueVisitorsCount').textContent = data.unique_visitors;
                document.getElementById('registeredUsersCount').textContent = data.registered_count;
                document.getElementById('premiumUsersCount').textContent = data.premium_count;
                
                // Update active users table
                const tableBody = document.getElementById('activeUsersTableBody');
                tableBody.innerHTML = '';
                
                data.sessions.forEach(session => {
                    const row = document.createElement('tr');
                    
                    // User column
                    const userCell = document.createElement('td');
                    if (session.user_id) {
                        userCell.innerHTML = `
                            <div class="d-flex align-items-center">
                                <span class="badge bg-${session.is_premium ? 'warning' : 'primary'} me-2">
                                    ${session.is_premium ? 'প্রিমিয়াম' : 'রেজিস্টার্ড'}
                                </span>
                                ${session.username}
                            </div>
                        `;
                    } else {
                        userCell.innerHTML = '<span class="badge bg-secondary me-2">অতিথি</span> অজানা ভিজিটর';
                    }
                    row.appendChild(userCell);
                    
                    // IP address
                    const ipCell = document.createElement('td');
                    ipCell.textContent = session.ip_address;
                    row.appendChild(ipCell);
                    
                    // Device
                    const deviceCell = document.createElement('td');
                    deviceCell.innerHTML = `<small>${session.device_type} / ${session.browser} / ${session.os}</small>`;
                    row.appendChild(deviceCell);
                    
                    // Page
                    const pageCell = document.createElement('td');
                    pageCell.innerHTML = `<a href="${session.page_url}" target="_blank" class="text-truncate d-inline-block" style="max-width: 200px;">${session.page_url}</a>`;
                    row.appendChild(pageCell);
                    
                    // Last activity
                    const activityCell = document.createElement('td');
                    activityCell.innerHTML = `<span class="timeago" data-timestamp="${session.last_activity}">${timeAgo(session.last_activity)}</span>`;
                    row.appendChild(activityCell);
                    
                    // Action
                    const actionCell = document.createElement('td');
                    if (session.user_id) {
                        actionCell.innerHTML = `<a href="user_details.php?id=${session.user_id}" class="btn btn-sm btn-primary"><i class="fas fa-eye"></i></a>`;
                    } else {
                        actionCell.innerHTML = `<button class="btn btn-sm btn-secondary" disabled><i class="fas fa-eye-slash"></i></button>`;
                    }
                    row.appendChild(actionCell);
                    
                    tableBody.appendChild(row);
                });
                
                // Update page views table
                const pageViewsTableBody = document.getElementById('pageViewsTableBody');
                pageViewsTableBody.innerHTML = '';
                
                data.page_views.forEach(page => {
                    const row = document.createElement('tr');
                    
                    const pageCell = document.createElement('td');
                    pageCell.innerHTML = `<a href="${page.page_url}" target="_blank" class="text-truncate d-inline-block" style="max-width: 300px;">${page.page_url}</a>`;
                    row.appendChild(pageCell);
                    
                    const countCell = document.createElement('td');
                    countCell.textContent = page.view_count;
                    row.appendChild(countCell);
                    
                    pageViewsTableBody.appendChild(row);
                });
                
                // Update chart
                activityLabels.shift();
                activityLabels.push(moment().format('HH:mm'));
                
                activityData.shift();
                activityData.push(data.session_count);
                
                liveActivityChart.update();
            })
            .catch(error => console.error('Error fetching data:', error));
    }
    
    // Time ago function
    function timeAgo(timestamp) {
        const now = new Date();
        const past = new Date(timestamp);
        const diffInSeconds = Math.floor((now - past) / 1000);
        
        if (diffInSeconds < 60) {
            return diffInSeconds + ' সেকেন্ড আগে';
        } else if (diffInSeconds < 3600) {
            return Math.floor(diffInSeconds / 60) + ' মিনিট আগে';
        } else if (diffInSeconds < 86400) {
            return Math.floor(diffInSeconds / 3600) + ' ঘন্টা আগে';
        } else {
            return Math.floor(diffInSeconds / 86400) + ' দিন আগে';
        }
    }
    
    // Update timeago elements
    function updateTimeAgo() {
        document.querySelectorAll('.timeago').forEach(element => {
            const timestamp = element.getAttribute('data-timestamp');
            element.textContent = timeAgo(timestamp);
        });
    }
    
    // Refresh button
    document.getElementById('refreshData').addEventListener('click', function() {
        updateData();
        updateTimeAgo();
    });
    
    // Auto refresh toggle
    let autoRefreshInterval;
    const autoRefreshCheckbox = document.getElementById('autoRefresh');
    
    function startAutoRefresh() {
        if (autoRefreshCheckbox.checked) {
            autoRefreshInterval = setInterval(function() {
                updateData();
                updateTimeAgo();
            }, 30000); // 30 seconds
        }
    }
    
    function stopAutoRefresh() {
        clearInterval(autoRefreshInterval);
    }
    
    autoRefreshCheckbox.addEventListener('change', function() {
        if (this.checked) {
            startAutoRefresh();
        } else {
            stopAutoRefresh();
        }
    });
    
    // Start auto refresh by default
    startAutoRefresh();
    
    // Initial update
    updateData();
    setInterval(updateTimeAgo, 60000); // Update time ago every minute
});
</script>
