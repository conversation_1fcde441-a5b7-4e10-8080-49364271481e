<?php
// Set page title
$page_title = 'Edit Movie';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Check if movie ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    redirect('movies.php');
}

$movie_id = (int)$_GET['id'];

// Check if language column exists in movies table
$check_language_query = "SHOW COLUMNS FROM movies LIKE 'language'";
$check_language_result = mysqli_query($conn, $check_language_query);

if (mysqli_num_rows($check_language_result) == 0) {
    // Add language column if it doesn't exist
    $add_language_query = "ALTER TABLE movies ADD COLUMN language VARCHAR(50) DEFAULT NULL";
    mysqli_query($conn, $add_language_query);
}

// Get movie details
$movie_query = "SELECT * FROM movies WHERE id = $movie_id";
$movie_result = mysqli_query($conn, $movie_query);

if (mysqli_num_rows($movie_result) == 0) {
    redirect('movies.php');
}

$movie = mysqli_fetch_assoc($movie_result);

// Check if tags table exists
$check_tags_table = "SHOW TABLES LIKE 'tags'";
$tags_table_exists = mysqli_query($conn, $check_tags_table);

// Get all tags if the table exists
$tags = [];
if (mysqli_num_rows($tags_table_exists) > 0) {
    $tags_query = "SELECT * FROM tags ORDER BY name";
    $tags_result = mysqli_query($conn, $tags_query);
    while ($tag = mysqli_fetch_assoc($tags_result)) {
        $tags[] = $tag;
    }

    // Get movie tags
    $movie_tags = [];
    $movie_tags_query = "SELECT tag_id FROM movie_tags WHERE movie_id = $movie_id";
    $movie_tags_result = mysqli_query($conn, $movie_tags_query);
    while ($tag = mysqli_fetch_assoc($movie_tags_result)) {
        $movie_tags[] = $tag['tag_id'];
    }
}

// Process form submission
$success_message = '';
$error_message = '';

if (isset($_GET['success']) && $_GET['success'] == 'added') {
    $success_message = 'Movie added successfully.';
}

if (isset($_POST['update_movie'])) {
    $title = sanitize($_POST['title']);
    $category_id = (int)$_POST['category_id'];
    $release_year = (int)$_POST['release_year'];
    $duration = sanitize($_POST['duration']);
    $quality = sanitize($_POST['quality']);
    $language = sanitize($_POST['language']);
    $rating = (float)$_POST['rating'];
    $description = sanitize($_POST['description']);
    $trailer_url = sanitize($_POST['trailer_url']);
    $premium_only = isset($_POST['premium_only']) ? 1 : 0;
    $featured = isset($_POST['featured']) ? 1 : 0;
    $selected_tags = isset($_POST['tags']) ? $_POST['tags'] : [];

    // Validate required fields
    if (empty($title) || empty($category_id) || empty($release_year)) {
        $error_message = 'Title, category, and release year are required.';
    } else {
        // Handle poster upload
        $poster = $movie['poster'];
        if (!empty($_FILES['poster']['name'])) {
            $upload_dir = '../uploads/';
            $file_ext = pathinfo($_FILES['poster']['name'], PATHINFO_EXTENSION);
            $new_poster = 'movie_' . time() . '.' . $file_ext;
            $upload_path = $upload_dir . $new_poster;

            // Check file type
            $allowed_types = ['jpg', 'jpeg', 'png', 'webp'];
            if (!in_array(strtolower($file_ext), $allowed_types)) {
                $error_message = 'Invalid poster file type. Allowed types: JPG, JPEG, PNG, WEBP.';
            } else if ($_FILES['poster']['size'] > 2097152) { // 2MB
                $error_message = 'Poster file size must be less than 2MB.';
            } else if (!move_uploaded_file($_FILES['poster']['tmp_name'], $upload_path)) {
                $error_message = 'Error uploading poster file.';
            } else {
                // Delete old poster if it exists and is not a URL
                if (!empty($movie['poster']) && strpos($movie['poster'], 'http') !== 0 && file_exists($upload_dir . $movie['poster'])) {
                    unlink($upload_dir . $movie['poster']);
                }
                $poster = $new_poster;
            }
        }

        // Handle banner/backdrop upload
        $banner = $movie['banner'];
        if (!empty($_FILES['banner']['name'])) {
            $upload_dir = '../uploads/';
            $file_ext = pathinfo($_FILES['banner']['name'], PATHINFO_EXTENSION);
            $new_banner = 'banner_' . time() . '.' . $file_ext;
            $upload_path = $upload_dir . $new_banner;

            // Check file type
            $allowed_types = ['jpg', 'jpeg', 'png', 'webp'];
            if (!in_array(strtolower($file_ext), $allowed_types)) {
                $error_message = 'Invalid banner file type. Allowed types: JPG, JPEG, PNG, WEBP.';
            } else if ($_FILES['banner']['size'] > 3145728) { // 3MB
                $error_message = 'Banner file size must be less than 3MB.';
            } else if (!move_uploaded_file($_FILES['banner']['tmp_name'], $upload_path)) {
                $error_message = 'Error uploading banner file.';
            } else {
                // Delete old banner if it exists and is not a URL
                if (!empty($movie['banner']) && strpos($movie['banner'], 'http') !== 0 && file_exists($upload_dir . $movie['banner'])) {
                    unlink($upload_dir . $movie['banner']);
                }
                $banner = $new_banner;
            }
        }

        // If no errors, update movie
        if (empty($error_message)) {
            $query = "UPDATE movies SET
                    title = '$title',
                    category_id = $category_id,
                    release_year = $release_year,
                    duration = '$duration',
                    quality = '$quality',
                    language = '$language',
                    rating = $rating,
                    description = '$description',
                    trailer_url = '$trailer_url',
                    poster = '$poster',
                    banner = '$banner',
                    premium_only = $premium_only,
                    featured = $featured,
                    updated_at = NOW()
                    WHERE id = $movie_id";

            if (mysqli_query($conn, $query)) {
                // Update tags if tags table exists
                if (mysqli_num_rows($tags_table_exists) > 0) {
                    // First delete all existing tags for this movie
                    $delete_tags_query = "DELETE FROM movie_tags WHERE movie_id = $movie_id";
                    mysqli_query($conn, $delete_tags_query);

                    // Then insert the selected tags
                    if (!empty($selected_tags)) {
                        foreach ($selected_tags as $tag_id) {
                            $tag_id = (int)$tag_id;
                            $insert_tag_query = "INSERT INTO movie_tags (movie_id, tag_id) VALUES ($movie_id, $tag_id)";
                            mysqli_query($conn, $insert_tag_query);
                        }
                    }
                }

                $success_message = 'Movie updated successfully.';

                // Refresh movie data
                $movie_result = mysqli_query($conn, $movie_query);
                $movie = mysqli_fetch_assoc($movie_result);

                // Refresh movie tags
                if (mysqli_num_rows($tags_table_exists) > 0) {
                    $movie_tags = [];
                    $movie_tags_query = "SELECT tag_id FROM movie_tags WHERE movie_id = $movie_id";
                    $movie_tags_result = mysqli_query($conn, $movie_tags_query);
                    while ($tag = mysqli_fetch_assoc($movie_tags_result)) {
                        $movie_tags[] = $tag['tag_id'];
                    }
                }
            } else {
                $error_message = 'Error updating movie: ' . mysqli_error($conn);
            }
        }
    }
}

// Get categories
$categories_query = "SELECT * FROM categories ORDER BY name";
$categories_result = mysqli_query($conn, $categories_query);

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">
            <!-- Sidebar Toggle (Topbar) -->
            <button id="sidebarToggle" class="btn btn-link d-md-none rounded-circle mr-3">
                <i class="fa fa-bars"></i>
            </button>

            <!-- Topbar Title -->
            <div class="d-none d-sm-inline-block mr-auto ml-md-3 my-2 my-md-0 mw-100">
                <h1 class="h3 mb-0 text-gray-800">Edit Movie</h1>
            </div>
        </nav>
    </div>

    <div class="container-fluid">
        <?php if($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">Edit Movie: <?php echo $movie['title']; ?></h6>
                <div>
                    <a href="manage_links.php?type=movie&id=<?php echo $movie_id; ?>" class="btn btn-sm btn-info">
                        <i class="fas fa-link me-1"></i> Manage Links
                    </a>
                    <a href="<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $movie_id; ?>" class="btn btn-sm btn-success" target="_blank">
                        <i class="fas fa-eye me-1"></i> View Movie
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form method="POST" action="" enctype="multipart/form-data" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" value="<?php echo $movie['title']; ?>" required>
                                <div class="invalid-feedback">
                                    Please enter a title.
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="category_id" class="form-label">Category <span class="text-danger">*</span></label>
                                        <select class="form-select" id="category_id" name="category_id" required>
                                            <option value="">Select Category</option>
                                            <?php
                                            mysqli_data_seek($categories_result, 0);
                                            while($category = mysqli_fetch_assoc($categories_result)):
                                            ?>
                                            <option value="<?php echo $category['id']; ?>" <?php echo $movie['category_id'] == $category['id'] ? 'selected' : ''; ?>><?php echo $category['name']; ?></option>
                                            <?php endwhile; ?>
                                        </select>
                                        <div class="invalid-feedback">
                                            Please select a category.
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="release_year" class="form-label">Release Year <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="release_year" name="release_year" min="1900" max="<?php echo date('Y') + 1; ?>" value="<?php echo $movie['release_year']; ?>" required>
                                        <div class="invalid-feedback">
                                            Please enter a valid release year.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="duration" class="form-label">Duration</label>
                                        <input type="text" class="form-control" id="duration" name="duration" placeholder="e.g., 2h 30m" value="<?php echo $movie['duration']; ?>">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="quality" class="form-label">Quality</label>
                                        <select class="form-select" id="quality" name="quality">
                                            <option value="">Select Quality</option>
                                            <option value="CAM" <?php echo $movie['quality'] == 'CAM' ? 'selected' : ''; ?>>CAM</option>
                                            <option value="SD" <?php echo $movie['quality'] == 'SD' ? 'selected' : ''; ?>>SD</option>
                                            <option value="HD" <?php echo $movie['quality'] == 'HD' ? 'selected' : ''; ?>>HD</option>
                                            <option value="Full HD" <?php echo $movie['quality'] == 'Full HD' ? 'selected' : ''; ?>>Full HD</option>
                                            <option value="4K" <?php echo $movie['quality'] == '4K' ? 'selected' : ''; ?>>4K</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="language" class="form-label">Language</label>
                                        <input type="text" class="form-control" id="language" name="language" value="<?php echo isset($movie['language']) ? $movie['language'] : ''; ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="rating" class="form-label">Rating (0-10)</label>
                                <input type="number" class="form-control" id="rating" name="rating" min="0" max="10" step="0.1" value="<?php echo $movie['rating']; ?>">
                            </div>

                            <?php if (mysqli_num_rows($tags_table_exists) > 0 && count($tags) > 0): ?>
                            <div class="mb-3">
                                <label for="tags" class="form-label">Tags</label>
                                <select class="form-select" id="tags" name="tags[]" multiple>
                                    <?php foreach ($tags as $tag): ?>
                                    <option value="<?php echo $tag['id']; ?>" <?php echo in_array($tag['id'], $movie_tags) ? 'selected' : ''; ?>><?php echo $tag['name']; ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">Select multiple tags by holding Ctrl (or Cmd on Mac) while clicking. These tags will be displayed on movie cards.</div>
                            </div>
                            <?php endif; ?>

                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="5"><?php echo $movie['description']; ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="trailer_url" class="form-label">Trailer URL (YouTube)</label>
                                <input type="url" class="form-control" id="trailer_url" name="trailer_url" placeholder="https://www.youtube.com/watch?v=..." value="<?php echo $movie['trailer_url']; ?>">
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="poster" class="form-label">Poster Image</label>
                                <input type="file" class="form-control" id="poster" name="poster" accept="image/jpeg,image/png,image/webp">
                                <div class="form-text">Recommended size: 300x450px. Max size: 2MB.</div>
                                <div class="mt-3">
                                    <div class="poster-preview-container" style="max-width: 200px; margin: 0 auto;">
                                        <?php if (!empty($movie['poster'])): ?>
                                            <?php if (strpos($movie['poster'], 'http') === 0): ?>
                                                <!-- External URL (TMDB) -->
                                                <img id="poster-preview" src="<?php echo $movie['poster']; ?>" alt="Poster Preview" class="img-thumbnail" onerror="this.onerror=null; this.src='assets/img/default-poster.jpg';">
                                            <?php elseif (file_exists('../uploads/' . $movie['poster'])): ?>
                                                <!-- Local file -->
                                                <img id="poster-preview" src="<?php echo SITE_URL; ?>/uploads/<?php echo $movie['poster']; ?>" alt="Poster Preview" class="img-thumbnail">
                                            <?php else: ?>
                                                <!-- Try TMDB path -->
                                                <img id="poster-preview" src="https://image.tmdb.org/t/p/w300<?php echo $movie['poster']; ?>" alt="Poster Preview" class="img-thumbnail" onerror="this.onerror=null; this.src='assets/img/default-poster.jpg';">
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <img id="poster-preview" src="assets/img/default-poster.jpg" alt="Poster Preview" class="img-thumbnail">
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="banner" class="form-label">Backdrop/Banner Image</label>
                                <input type="file" class="form-control" id="banner" name="banner" accept="image/jpeg,image/png,image/webp">
                                <div class="form-text">Recommended size: 1280x720px. Max size: 3MB.</div>
                                <div class="mt-3">
                                    <div class="banner-preview-container">
                                        <?php if (!empty($movie['banner'])): ?>
                                            <?php if (strpos($movie['banner'], 'http') === 0): ?>
                                                <!-- External URL (TMDB) -->
                                                <img id="banner-preview" src="<?php echo $movie['banner']; ?>" alt="Banner Preview" class="img-thumbnail" onerror="this.onerror=null; this.src='assets/img/default-banner.jpg';">
                                            <?php elseif (file_exists('../uploads/' . $movie['banner'])): ?>
                                                <!-- Local file -->
                                                <img id="banner-preview" src="<?php echo SITE_URL; ?>/uploads/<?php echo $movie['banner']; ?>" alt="Banner Preview" class="img-thumbnail">
                                            <?php else: ?>
                                                <!-- Try TMDB path -->
                                                <img id="banner-preview" src="https://image.tmdb.org/t/p/w780<?php echo $movie['banner']; ?>" alt="Banner Preview" class="img-thumbnail" onerror="this.onerror=null; this.src='assets/img/default-banner.jpg';">
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <div class="alert alert-warning">No backdrop image available</div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="premium_only" name="premium_only" <?php echo $movie['premium_only'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="premium_only">Premium Only</label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="featured" name="featured" <?php echo $movie['featured'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="featured">Featured</label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">Movie Info</h6>
                                        <p class="mb-1"><strong>ID:</strong> <?php echo $movie['id']; ?></p>
                                        <p class="mb-1"><strong>Added:</strong> <?php echo date('M j, Y g:i A', strtotime($movie['created_at'])); ?></p>
                                        <?php if($movie['updated_at']): ?>
                                        <p class="mb-1"><strong>Updated:</strong> <?php echo date('M j, Y g:i A', strtotime($movie['updated_at'])); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <a href="movies.php" class="btn btn-secondary">Back to Movies</a>
                        <button type="submit" name="update_movie" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Movie
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for image previews -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Poster preview
    const posterInput = document.getElementById('poster');
    const posterPreview = document.getElementById('poster-preview');

    if (posterInput && posterPreview) {
        posterInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    posterPreview.src = e.target.result;
                }

                reader.readAsDataURL(this.files[0]);
            }
        });
    }

    // Banner/backdrop preview
    const bannerInput = document.getElementById('banner');
    const bannerPreview = document.getElementById('banner-preview');
    const bannerContainer = document.querySelector('.banner-preview-container');

    if (bannerInput && bannerContainer) {
        bannerInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    // If there's no banner preview image yet, create one
                    if (!bannerPreview) {
                        bannerContainer.innerHTML = '';
                        const newBannerPreview = document.createElement('img');
                        newBannerPreview.id = 'banner-preview';
                        newBannerPreview.className = 'img-thumbnail';
                        newBannerPreview.alt = 'Banner Preview';
                        bannerContainer.appendChild(newBannerPreview);
                        newBannerPreview.src = e.target.result;
                    } else {
                        bannerPreview.src = e.target.result;
                    }
                }

                reader.readAsDataURL(this.files[0]);
            }
        });
    }
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
