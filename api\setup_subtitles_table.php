<?php
// Setup Subtitles Table
require_once 'config.php';

// Create subtitles table
$create_subtitles_table = "CREATE TABLE IF NOT EXISTS subtitles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    content_type ENUM('movie', 'episode') NOT NULL,
    content_id INT NOT NULL,
    language VARCHAR(50) NOT NULL,
    url VARCHAR(255) NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_content (content_type, content_id)
)";

if (mysqli_query($conn, $create_subtitles_table)) {
    echo "subtitles table created successfully<br>";
} else {
    echo "Error creating subtitles table: " . mysqli_error($conn) . "<br>";
}

// Create API logs table
$create_api_logs_table = "CREATE TABLE IF NOT EXISTS api_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    user_id INT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    status_code INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user (user_id),
    INDEX idx_endpoint (endpoint),
    INDEX idx_created_at (created_at)
)";

if (mysqli_query($conn, $create_api_logs_table)) {
    echo "api_logs table created successfully<br>";
} else {
    echo "Error creating api_logs table: " . mysqli_error($conn) . "<br>";
}

echo "Setup completed!";
