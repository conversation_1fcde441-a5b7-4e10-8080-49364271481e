<?php
// Set page title
$page_title = 'মুভি ম্যানেজমেন্ট';
$current_page = 'movies.php';

// Include configuration and functions
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL . '/login.php');
    exit;
}

// Handle bulk actions
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['bulk_action'])) {
    $action = $_POST['bulk_action'];
    $selected_ids = $_POST['selected_movies'] ?? [];
    
    if (!empty($selected_ids) && is_array($selected_ids)) {
        $ids = implode(',', array_map('intval', $selected_ids));
        
        switch ($action) {
            case 'delete':
                $delete_query = "DELETE FROM movies WHERE id IN ($ids)";
                if (mysqli_query($conn, $delete_query)) {
                    $success_message = count($selected_ids) . ' টি মুভি সফলভাবে ডিলিট করা হয়েছে।';
                }
                break;
                
            case 'make_premium':
                $premium_query = "UPDATE movies SET is_premium = 1 WHERE id IN ($ids)";
                if (mysqli_query($conn, $premium_query)) {
                    $success_message = count($selected_ids) . ' টি মুভি প্রিমিয়াম করা হয়েছে।';
                }
                break;
                
            case 'make_free':
                $free_query = "UPDATE movies SET is_premium = 0 WHERE id IN ($ids)";
                if (mysqli_query($conn, $free_query)) {
                    $success_message = count($selected_ids) . ' টি মুভি ফ্রি করা হয়েছে।';
                }
                break;
        }
    }
}

// Get filter parameters
$search = $_GET['search'] ?? '';
$category_filter = $_GET['category'] ?? '';
$premium_filter = $_GET['premium'] ?? '';
$sort_by = $_GET['sort'] ?? 'created_at';
$sort_order = $_GET['order'] ?? 'DESC';

// Build query
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(title LIKE ? OR description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($category_filter)) {
    $where_conditions[] = "category_id = ?";
    $params[] = $category_filter;
}

if ($premium_filter !== '') {
    $where_conditions[] = "is_premium = ?";
    $params[] = $premium_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get total count
$count_query = "SELECT COUNT(*) as total FROM movies $where_clause";
$stmt = mysqli_prepare($conn, $count_query);
if (!empty($params)) {
    $types = str_repeat('s', count($params));
    mysqli_stmt_bind_param($stmt, $types, ...$params);
}
mysqli_stmt_execute($stmt);
$total_movies = mysqli_fetch_assoc(mysqli_stmt_get_result($stmt))['total'];

// Pagination
$page = $_GET['page'] ?? 1;
$per_page = 20;
$total_pages = ceil($total_movies / $per_page);
$offset = ($page - 1) * $per_page;

// Get movies
$query = "SELECT m.*, c.name as category_name 
          FROM movies m 
          LEFT JOIN categories c ON m.category_id = c.id 
          $where_clause 
          ORDER BY $sort_by $sort_order 
          LIMIT $per_page OFFSET $offset";

$stmt = mysqli_prepare($conn, $query);
if (!empty($params)) {
    $types = str_repeat('s', count($params));
    mysqli_stmt_bind_param($stmt, $types, ...$params);
}
mysqli_stmt_execute($stmt);
$movies_result = mysqli_stmt_get_result($stmt);

// Get categories for filter
$categories_query = "SELECT * FROM categories ORDER BY name";
$categories_result = mysqli_query($conn, $categories_query);

// Include header
include 'includes/header.php';
?>

<!-- Include Sidebar -->
<?php include 'includes/sidebar.php'; ?>

<!-- Main Content -->
<div class="main-content">
    <!-- Page Header -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-title">
                    <i class="fas fa-film me-3"></i>মুভি ম্যানেজমেন্ট
                </h1>
                <p class="page-subtitle text-muted">মোট <?php echo number_format($total_movies); ?> টি মুভি</p>
            </div>
            <div class="col-auto">
                <div class="page-actions">
                    <a href="add_movie.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>নতুন মুভি যোগ করুন
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Message -->
    <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">খুঁজুন</label>
                    <input type="text" class="form-control" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="মুভির নাম...">
                </div>
                <div class="col-md-2">
                    <label class="form-label">ক্যাটাগরি</label>
                    <select class="form-select" name="category">
                        <option value="">সব ক্যাটাগরি</option>
                        <?php while ($category = mysqli_fetch_assoc($categories_result)): ?>
                            <option value="<?php echo $category['id']; ?>" <?php echo $category_filter == $category['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">প্রিমিয়াম</label>
                    <select class="form-select" name="premium">
                        <option value="">সব মুভি</option>
                        <option value="1" <?php echo $premium_filter === '1' ? 'selected' : ''; ?>>প্রিমিয়াম</option>
                        <option value="0" <?php echo $premium_filter === '0' ? 'selected' : ''; ?>>ফ্রি</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">সর্ট</label>
                    <select class="form-select" name="sort">
                        <option value="created_at" <?php echo $sort_by == 'created_at' ? 'selected' : ''; ?>>তারিখ</option>
                        <option value="title" <?php echo $sort_by == 'title' ? 'selected' : ''; ?>>নাম</option>
                        <option value="release_year" <?php echo $sort_by == 'release_year' ? 'selected' : ''; ?>>রিলিজ বছর</option>
                        <option value="rating" <?php echo $sort_by == 'rating' ? 'selected' : ''; ?>>রেটিং</option>
                    </select>
                </div>
                <div class="col-md-1">
                    <label class="form-label">অর্ডার</label>
                    <select class="form-select" name="order">
                        <option value="DESC" <?php echo $sort_order == 'DESC' ? 'selected' : ''; ?>>নিচে</option>
                        <option value="ASC" <?php echo $sort_order == 'ASC' ? 'selected' : ''; ?>>উপরে</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i>
                    </button>
                    <a href="movies.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Movies Table -->
    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="card-title mb-0">মুভি তালিকা</h5>
                </div>
                <div class="col-auto">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAll()">
                            <i class="fas fa-check-square me-1"></i>সব নির্বাচন
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="deselectAll()">
                            <i class="fas fa-square me-1"></i>নির্বাচন বাতিল
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <form method="POST" id="bulkActionForm" class="bulk-action-form">
                <!-- Bulk Actions -->
                <div class="bulk-actions p-3 border-bottom" style="display: none;">
                    <div class="row align-items-center">
                        <div class="col">
                            <span class="selected-count">0</span> টি মুভি নির্বাচিত
                        </div>
                        <div class="col-auto">
                            <div class="btn-group">
                                <button type="submit" name="bulk_action" value="make_premium" class="btn btn-warning btn-sm bulk-action-btn" disabled>
                                    <i class="fas fa-crown me-1"></i>প্রিমিয়াম করুন
                                </button>
                                <button type="submit" name="bulk_action" value="make_free" class="btn btn-success btn-sm bulk-action-btn" disabled>
                                    <i class="fas fa-unlock me-1"></i>ফ্রি করুন
                                </button>
                                <button type="submit" name="bulk_action" value="delete" class="btn btn-danger btn-sm bulk-action-btn delete-btn" disabled data-confirm-message="নির্বাচিত মুভিগুলো ডিলিট করতে চান?">
                                    <i class="fas fa-trash me-1"></i>ডিলিট করুন
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th width="50">
                                    <input type="checkbox" class="form-check-input select-all" id="selectAll">
                                </th>
                                <th width="80">পোস্টার</th>
                                <th>নাম</th>
                                <th>ক্যাটাগরি</th>
                                <th>বছর</th>
                                <th>রেটিং</th>
                                <th>স্ট্যাটাস</th>
                                <th>তারিখ</th>
                                <th width="120">অ্যাকশন</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (mysqli_num_rows($movies_result) > 0): ?>
                                <?php while ($movie = mysqli_fetch_assoc($movies_result)): ?>
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="form-check-input item-checkbox" name="selected_movies[]" value="<?php echo $movie['id']; ?>">
                                        </td>
                                        <td>
                                            <img src="<?php echo !empty($movie['poster']) ? '../../' . $movie['poster'] : '../../assets/img/no-poster.jpg'; ?>"
                                                 alt="<?php echo htmlspecialchars($movie['title']); ?>"
                                                 class="img-thumbnail" style="width: 50px; height: 75px; object-fit: cover;">
                                        </td>
                                        <td>
                                            <div class="fw-bold"><?php echo htmlspecialchars($movie['title']); ?></div>
                                            <small class="text-muted"><?php echo substr(htmlspecialchars($movie['description']), 0, 100); ?>...</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo htmlspecialchars($movie['category_name'] ?? 'N/A'); ?></span>
                                        </td>
                                        <td><?php echo $movie['release_year']; ?></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-star text-warning me-1"></i>
                                                <?php echo number_format($movie['rating'], 1); ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($movie['is_premium']): ?>
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-crown me-1"></i>প্রিমিয়াম
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-unlock me-1"></i>ফ্রি
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small><?php echo date('d/m/Y', strtotime($movie['created_at'])); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="edit_movie.php?id=<?php echo $movie['id']; ?>" class="btn btn-outline-primary" title="এডিট করুন">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="manage_links.php?type=movie&id=<?php echo $movie['id']; ?>" class="btn btn-outline-info" title="লিংক ম্যানেজ করুন">
                                                    <i class="fas fa-link"></i>
                                                </a>
                                                <a href="delete_movie.php?id=<?php echo $movie['id']; ?>" class="btn btn-outline-danger delete-btn" title="ডিলিট করুন" data-confirm-message="এই মুভিটি ডিলিট করতে চান?">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="9" class="text-center py-4">
                                        <i class="fas fa-film fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">কোন মুভি পাওয়া যায়নি।</p>
                                        <a href="add_movie.php" class="btn btn-primary">
                                            <i class="fas fa-plus me-2"></i>প্রথম মুভি যোগ করুন
                                        </a>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </form>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="card-footer">
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center mb-0">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?>&<?php echo http_build_query($_GET); ?>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query($_GET); ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?>&<?php echo http_build_query($_GET); ?>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// Page-specific scripts
$page_scripts = '
<script>
function selectAll() {
    const checkboxes = document.querySelectorAll(".item-checkbox");
    const selectAllCheckbox = document.getElementById("selectAll");
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    selectAllCheckbox.checked = true;
    
    updateBulkActions();
}

function deselectAll() {
    const checkboxes = document.querySelectorAll(".item-checkbox, #selectAll");
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    
    updateBulkActions();
}

function updateBulkActions() {
    const checkedBoxes = document.querySelectorAll(".item-checkbox:checked");
    const bulkActions = document.querySelector(".bulk-actions");
    const bulkActionButtons = document.querySelectorAll(".bulk-action-btn");
    const selectedCount = document.querySelector(".selected-count");
    
    if (checkedBoxes.length > 0) {
        bulkActions.style.display = "block";
        bulkActionButtons.forEach(btn => btn.disabled = false);
        selectedCount.textContent = checkedBoxes.length;
    } else {
        bulkActions.style.display = "none";
        bulkActionButtons.forEach(btn => btn.disabled = true);
    }
}

// Event listeners
document.addEventListener("DOMContentLoaded", function() {
    // Select all checkbox
    document.getElementById("selectAll").addEventListener("change", function() {
        const checkboxes = document.querySelectorAll(".item-checkbox");
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });
    
    // Individual checkboxes
    document.querySelectorAll(".item-checkbox").forEach(checkbox => {
        checkbox.addEventListener("change", function() {
            const allCheckboxes = document.querySelectorAll(".item-checkbox");
            const checkedCheckboxes = document.querySelectorAll(".item-checkbox:checked");
            const selectAllCheckbox = document.getElementById("selectAll");
            
            selectAllCheckbox.checked = allCheckboxes.length === checkedCheckboxes.length;
            updateBulkActions();
        });
    });
    
    // Bulk action form submission
    document.getElementById("bulkActionForm").addEventListener("submit", function(e) {
        const checkedBoxes = document.querySelectorAll(".item-checkbox:checked");
        if (checkedBoxes.length === 0) {
            e.preventDefault();
            cinepixAdmin.showToast("কোন মুভি নির্বাচন করা হয়নি।", "warning");
        }
    });
});
</script>
';

// Include footer
include 'includes/footer.php';
?>
