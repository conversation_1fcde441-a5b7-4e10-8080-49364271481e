<?php
require_once 'includes/header.php';

// Check if user is logged in
if (!isLoggedIn()) {
    // Save current page as redirect destination after login
    $_SESSION['redirect_to'] = SITE_URL . '/test_premium.php';
    redirect(SITE_URL . '/login.php');
}

// Get user data
$user_id = $_SESSION['user_id'];
$user_query = "SELECT * FROM users WHERE id = $user_id";
$user_result = mysqli_query($conn, $user_query);
$user = mysqli_fetch_assoc($user_result);

// Check premium status
$is_premium_function = isPremium();
$is_premium_session = isset($_SESSION['is_premium']) ? $_SESSION['is_premium'] : 'Not set';
$is_premium_db = $user['is_premium'];

// Check subscriptions
$subscription_query = "SELECT COUNT(*) as count FROM subscriptions 
                      WHERE user_id = $user_id AND status = 'active' AND end_date > NOW()";
$subscription_result = mysqli_query($conn, $subscription_query);
$has_active_subscription = mysqli_fetch_assoc($subscription_result)['count'] > 0;

// Get subscription details if any
$subscription_details_query = "SELECT s.*, p.name as plan_name, p.price 
                              FROM subscriptions s
                              JOIN premium_plans p ON s.plan_id = p.id
                              WHERE s.user_id = $user_id AND s.status = 'active' AND s.end_date > NOW()
                              ORDER BY s.end_date DESC LIMIT 1";
$subscription_details_result = mysqli_query($conn, $subscription_details_query);
$subscription_details = mysqli_fetch_assoc($subscription_details_result);
?>

<div class="container py-5">
    <div class="card bg-dark">
        <div class="card-header">
            <h3>Premium Status Debug Information</h3>
        </div>
        <div class="card-body">
            <h4>User Information</h4>
            <table class="table table-dark table-bordered">
                <tr>
                    <th>User ID</th>
                    <td><?php echo $user_id; ?></td>
                </tr>
                <tr>
                    <th>Username</th>
                    <td><?php echo $user['username']; ?></td>
                </tr>
                <tr>
                    <th>Email</th>
                    <td><?php echo $user['email']; ?></td>
                </tr>
                <tr>
                    <th>is_premium in DB</th>
                    <td><?php echo $is_premium_db ? 'Yes (1)' : 'No (0)'; ?></td>
                </tr>
            </table>

            <h4 class="mt-4">Premium Status</h4>
            <table class="table table-dark table-bordered">
                <tr>
                    <th>isPremium() Function Result</th>
                    <td><?php echo $is_premium_function ? 'Yes (1)' : 'No (0)'; ?></td>
                </tr>
                <tr>
                    <th>$_SESSION['is_premium']</th>
                    <td><?php echo $is_premium_session === 1 ? 'Yes (1)' : ($is_premium_session === 0 ? 'No (0)' : $is_premium_session); ?></td>
                </tr>
                <tr>
                    <th>Has Active Subscription</th>
                    <td><?php echo $has_active_subscription ? 'Yes' : 'No'; ?></td>
                </tr>
            </table>

            <?php if($subscription_details): ?>
            <h4 class="mt-4">Subscription Details</h4>
            <table class="table table-dark table-bordered">
                <tr>
                    <th>Plan Name</th>
                    <td><?php echo $subscription_details['plan_name']; ?></td>
                </tr>
                <tr>
                    <th>Price</th>
                    <td>৳<?php echo $subscription_details['price']; ?></td>
                </tr>
                <tr>
                    <th>Start Date</th>
                    <td><?php echo date('F j, Y', strtotime($subscription_details['start_date'])); ?></td>
                </tr>
                <tr>
                    <th>End Date</th>
                    <td><?php echo date('F j, Y', strtotime($subscription_details['end_date'])); ?></td>
                </tr>
                <tr>
                    <th>Status</th>
                    <td><?php echo $subscription_details['status']; ?></td>
                </tr>
            </table>
            <?php else: ?>
            <div class="alert alert-warning mt-4">
                No active subscription found.
            </div>
            <?php endif; ?>

            <h4 class="mt-4">Debug isPremium() Function</h4>
            <div class="card bg-dark border-secondary">
                <div class="card-body">
                    <pre class="text-light">
function isPremium() {
    global $conn;

    // If logged in, check from database (always check from database to ensure latest status)
    if (isLoggedIn()) {
        $user_id = $_SESSION['user_id'];

        // First check if user is marked as premium in users table
        $user_query = "SELECT is_premium FROM users WHERE id = $user_id";
        $user_result = mysqli_query($conn, $user_query);
        $user_data = mysqli_fetch_assoc($user_result);
        $is_premium_user = $user_data['is_premium'];

        // Then check for active subscriptions
        $subscription_query = "SELECT COUNT(*) as count FROM subscriptions
                             WHERE user_id = $user_id AND status = 'active' AND end_date > NOW()";
        $subscription_result = mysqli_query($conn, $subscription_query);
        $has_active_subscription = mysqli_fetch_assoc($subscription_result)['count'] > 0;

        // If user is marked as premium but has no active subscriptions, update user table
        if ($is_premium_user && !$has_active_subscription) {
            // Update user table to match subscription status
            $update_query = "UPDATE users SET is_premium = 0 WHERE id = $user_id";
            mysqli_query($conn, $update_query);
            $is_premium_user = 0;
        }

        // If user has active subscription but is not marked as premium, update user table
        if (!$is_premium_user && $has_active_subscription) {
            // Update user table to match subscription status
            $update_query = "UPDATE users SET is_premium = 1 WHERE id = $user_id";
            mysqli_query($conn, $update_query);
            $is_premium_user = 1;
        }

        // User is premium if both the user table says so and they have an active subscription
        // This ensures consistency between user table and subscriptions table
        $is_premium = $is_premium_user && $has_active_subscription;

        // Update session
        $_SESSION['is_premium'] = $is_premium;

        return $is_premium;
    }

    return false;
}
                    </pre>
                </div>
            </div>

            <div class="mt-4">
                <a href="<?php echo SITE_URL; ?>/profile.php" class="btn btn-primary">Back to Profile</a>
                <?php if(!$is_premium_function): ?>
                <a href="<?php echo SITE_URL; ?>/payment.php" class="btn btn-warning ms-2">Upgrade to Premium</a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
