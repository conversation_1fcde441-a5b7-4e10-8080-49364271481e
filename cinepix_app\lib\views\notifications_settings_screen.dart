import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cinepix_app/constants/app_constants.dart';

class NotificationsSettingsScreen extends StatefulWidget {
  const NotificationsSettingsScreen({super.key});

  @override
  State<NotificationsSettingsScreen> createState() => _NotificationsSettingsScreenState();
}

class _NotificationsSettingsScreenState extends State<NotificationsSettingsScreen> {
  // Notification settings
  bool _newContentNotifications = true;
  bool _premiumContentNotifications = true;
  bool _specialOffersNotifications = true;
  bool _appUpdatesNotifications = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notifications'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Notification Settings Card
            Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Notification Settings',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildNotificationSwitch(
                      title: 'New Content',
                      subtitle: 'Get notified when new movies or TV shows are added',
                      value: _newContentNotifications,
                      onChanged: (value) {
                        setState(() {
                          _newContentNotifications = value;
                        });
                      },
                    ),
                    const Divider(height: 1),
                    _buildNotificationSwitch(
                      title: 'Premium Content',
                      subtitle: 'Get notified about new premium content',
                      value: _premiumContentNotifications,
                      onChanged: (value) {
                        setState(() {
                          _premiumContentNotifications = value;
                        });
                      },
                    ),
                    const Divider(height: 1),
                    _buildNotificationSwitch(
                      title: 'Special Offers',
                      subtitle: 'Get notified about discounts and special offers',
                      value: _specialOffersNotifications,
                      onChanged: (value) {
                        setState(() {
                          _specialOffersNotifications = value;
                        });
                      },
                    ),
                    const Divider(height: 1),
                    _buildNotificationSwitch(
                      title: 'App Updates',
                      subtitle: 'Get notified when app updates are available',
                      value: _appUpdatesNotifications,
                      onChanged: (value) {
                        setState(() {
                          _appUpdatesNotifications = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Recent Notifications
            const Text(
              'Recent Notifications',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Sample notifications
            _buildNotificationItem(
              title: 'New Movie Added',
              message: 'The latest blockbuster "Avatar 3" is now available!',
              time: '2 hours ago',
              icon: Icons.movie,
            ),
            
            _buildNotificationItem(
              title: 'Premium Offer',
              message: 'Get 20% off on Premium subscription this week!',
              time: '1 day ago',
              icon: Icons.workspace_premium,
            ),
            
            _buildNotificationItem(
              title: 'App Update',
              message: 'CinePix v2.1 is now available with new features',
              time: '3 days ago',
              icon: Icons.system_update,
            ),
            
            const SizedBox(height: 16),
            
            // Clear all button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  _showClearNotificationsDialog();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'Clear All Notifications',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationSwitch({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 12,
          color: Colors.grey[400],
        ),
      ),
      value: value,
      onChanged: onChanged,
      activeColor: AppConstants.primaryColor,
    );
  }

  Widget _buildNotificationItem({
    required String title,
    required String message,
    required String time,
    required IconData icon,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
          child: Icon(
            icon,
            color: AppConstants.primaryColor,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(message),
            const SizedBox(height: 4),
            Text(
              time,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[400],
              ),
            ),
          ],
        ),
        isThreeLine: true,
      ),
    );
  }

  void _showClearNotificationsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Notifications'),
        content: const Text('Are you sure you want to clear all notifications?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement clear notifications
              Get.snackbar(
                'Notifications Cleared',
                'All notifications have been cleared',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.green,
                colorText: Colors.white,
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
            ),
            child: const Text(
              'Clear',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}
