<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: login.php');
    exit();
}

/**
 * Comprehensive SEO Checker for CinePix
 */

// Initialize results array
$seo_results = [];

/**
 * Check Technical SEO Issues
 */
function checkTechnicalSEO() {
    global $seo_results;
    
    $technical = [];
    
    // 1. Check if sitemap exists and is accessible
    $sitemap_url = SITE_URL . '/sitemap.xml';
    $sitemap_exists = @file_get_contents($sitemap_url);
    $technical['sitemap'] = [
        'status' => $sitemap_exists ? 'good' : 'error',
        'message' => $sitemap_exists ? 'Sitemap accessible' : 'Sitemap not found or inaccessible',
        'url' => $sitemap_url
    ];
    
    // 2. Check robots.txt
    $robots_url = SITE_URL . '/robots.txt';
    $robots_exists = @file_get_contents($robots_url);
    $technical['robots'] = [
        'status' => $robots_exists ? 'good' : 'error',
        'message' => $robots_exists ? 'Robots.txt accessible' : 'Robots.txt not found',
        'url' => $robots_url
    ];
    
    // 3. Check SSL certificate
    $is_https = strpos(SITE_URL, 'https://') === 0;
    $technical['ssl'] = [
        'status' => $is_https ? 'good' : 'error',
        'message' => $is_https ? 'SSL certificate active' : 'SSL certificate missing',
        'recommendation' => !$is_https ? 'Enable HTTPS for better SEO ranking' : ''
    ];
    
    // 4. Check site speed (basic check)
    $start_time = microtime(true);
    $response = @file_get_contents(SITE_URL);
    $load_time = microtime(true) - $start_time;
    
    $technical['speed'] = [
        'status' => $load_time < 3 ? 'good' : ($load_time < 5 ? 'warning' : 'error'),
        'message' => "Page load time: " . round($load_time, 2) . " seconds",
        'recommendation' => $load_time > 3 ? 'Optimize images, enable compression, use CDN' : ''
    ];
    
    // 5. Check mobile responsiveness (basic check)
    $has_viewport = strpos($response, 'viewport') !== false;
    $technical['mobile'] = [
        'status' => $has_viewport ? 'good' : 'error',
        'message' => $has_viewport ? 'Viewport meta tag found' : 'Viewport meta tag missing',
        'recommendation' => !$has_viewport ? 'Add viewport meta tag for mobile optimization' : ''
    ];
    
    $seo_results['technical'] = $technical;
}

/**
 * Check Content SEO
 */
function checkContentSEO() {
    global $conn, $seo_results;

    $content = [];

    // 1. Check movies with missing SEO data
    $movies_query = "SELECT COUNT(*) as total,
                     SUM(CASE WHEN seo_title IS NULL OR seo_title = '' THEN 1 ELSE 0 END) as missing_title,
                     SUM(CASE WHEN seo_description IS NULL OR seo_description = '' THEN 1 ELSE 0 END) as missing_desc
                     FROM movies WHERE is_active = 1";

    $movies_query_result = mysqli_query($conn, $movies_query);
    if ($movies_query_result) {
        $movies_result = mysqli_fetch_assoc($movies_query_result);
    } else {
        // Fallback if query fails
        $movies_result = ['total' => 0, 'missing_title' => 0, 'missing_desc' => 0];
    }

    $movies_total = $movies_result['total'] ?? 0;
    $movies_missing_title = $movies_result['missing_title'] ?? 0;
    $movies_missing_desc = $movies_result['missing_desc'] ?? 0;

    $content['movies_seo'] = [
        'total' => $movies_total,
        'missing_title' => $movies_missing_title,
        'missing_description' => $movies_missing_desc,
        'completion' => $movies_total > 0 ? round((($movies_total - $movies_missing_title) / $movies_total) * 100, 1) : 0
    ];

    // 2. Check TV shows with missing SEO data
    $tvshows_query = "SELECT COUNT(*) as total,
                      SUM(CASE WHEN seo_title IS NULL OR seo_title = '' THEN 1 ELSE 0 END) as missing_title,
                      SUM(CASE WHEN seo_description IS NULL OR seo_description = '' THEN 1 ELSE 0 END) as missing_desc
                      FROM tvshows WHERE is_active = 1";

    $tvshows_query_result = mysqli_query($conn, $tvshows_query);
    if ($tvshows_query_result) {
        $tvshows_result = mysqli_fetch_assoc($tvshows_query_result);
    } else {
        // Fallback if query fails
        $tvshows_result = ['total' => 0, 'missing_title' => 0, 'missing_desc' => 0];
    }

    $tvshows_total = $tvshows_result['total'] ?? 0;
    $tvshows_missing_title = $tvshows_result['missing_title'] ?? 0;
    $tvshows_missing_desc = $tvshows_result['missing_desc'] ?? 0;

    $content['tvshows_seo'] = [
        'total' => $tvshows_total,
        'missing_title' => $tvshows_missing_title,
        'missing_description' => $tvshows_missing_desc,
        'completion' => $tvshows_total > 0 ? round((($tvshows_total - $tvshows_missing_title) / $tvshows_total) * 100, 1) : 0
    ];

    // 3. Check for duplicate content
    $duplicate_query = mysqli_query($conn, "SELECT title, COUNT(*) as count FROM movies WHERE is_active = 1 GROUP BY title HAVING count > 1");
    $content['duplicate_movies'] = $duplicate_query ? mysqli_num_rows($duplicate_query) : 0;

    $seo_results['content'] = $content;
}

/**
 * Check Google Indexing Status
 */
function checkGoogleIndexing() {
    global $seo_results;
    
    $indexing = [];
    
    // Check if site is indexed in Google
    $site_domain = parse_url(SITE_URL, PHP_URL_HOST);
    $google_search_url = "https://www.google.com/search?q=site:" . $site_domain;
    
    $indexing['google_search_url'] = $google_search_url;
    $indexing['recommendation'] = "Check manually how many pages are indexed in Google";
    
    // Check Google Search Console submission
    $indexing['search_console'] = [
        'message' => 'Submit your sitemap to Google Search Console',
        'url' => 'https://search.google.com/search-console'
    ];
    
    $seo_results['indexing'] = $indexing;
}

/**
 * Generate SEO Score
 */
function calculateSEOScore() {
    global $seo_results;

    $score = 0;

    // Technical SEO (40 points)
    $technical_score = 0;
    if (isset($seo_results['technical']) && is_array($seo_results['technical'])) {
        foreach ($seo_results['technical'] as $check) {
            if (isset($check['status'])) {
                if ($check['status'] == 'good') $technical_score += 8;
                elseif ($check['status'] == 'warning') $technical_score += 4;
            }
        }
    }
    $score += min($technical_score, 40);

    // Content SEO (60 points)
    $content_score = 0;
    if (isset($seo_results['content'])) {
        $movies_completion = $seo_results['content']['movies_seo']['completion'] ?? 0;
        $tvshows_completion = $seo_results['content']['tvshows_seo']['completion'] ?? 0;

        // Avoid division by zero
        $total_content = ($seo_results['content']['movies_seo']['total'] ?? 0) + ($seo_results['content']['tvshows_seo']['total'] ?? 0);
        if ($total_content > 0) {
            $avg_completion = ($movies_completion + $tvshows_completion) / 2;
            $content_score = ($avg_completion / 100) * 60;
        }
    }
    $score += $content_score;

    return round($score);
}

// Run all checks
checkTechnicalSEO();
checkContentSEO();
checkGoogleIndexing();

$seo_score = calculateSEOScore();

?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO Checker - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .seo-score {
            font-size: 3rem;
            font-weight: bold;
        }
        .score-good { color: #28a745; }
        .score-warning { color: #ffc107; }
        .score-error { color: #dc3545; }
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        .card-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card mb-4">
                    <div class="card-header">
                        <h2><i class="fas fa-search"></i> SEO Checker - <?php echo SITE_NAME; ?></h2>
                    </div>
                    <div class="card-body text-center">
                        <div class="seo-score <?php echo $seo_score >= 80 ? 'score-good' : ($seo_score >= 60 ? 'score-warning' : 'score-error'); ?>">
                            <?php echo $seo_score; ?>/100
                        </div>
                        <p class="lead">
                            <?php if ($seo_score >= 80): ?>
                                <span class="text-success">চমৎকার! আপনার SEO অবস্থা ভালো</span>
                            <?php elseif ($seo_score >= 60): ?>
                                <span class="text-warning">মোটামুটি ভালো, কিছু উন্নতি প্রয়োজন</span>
                            <?php else: ?>
                                <span class="text-danger">গুরুতর সমস্যা আছে, তাৎক্ষণিক পদক্ষেপ নিন</span>
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technical SEO -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-cogs"></i> Technical SEO</h4>
                    </div>
                    <div class="card-body">
                        <?php foreach ($seo_results['technical'] as $key => $check): ?>
                        <div class="row mb-3 align-items-center">
                            <div class="col-md-3">
                                <strong><?php echo ucfirst(str_replace('_', ' ', $key)); ?></strong>
                            </div>
                            <div class="col-md-6">
                                <span class="status-<?php echo $check['status']; ?>">
                                    <i class="fas fa-<?php echo $check['status'] == 'good' ? 'check-circle' : ($check['status'] == 'warning' ? 'exclamation-triangle' : 'times-circle'); ?>"></i>
                                    <?php echo $check['message']; ?>
                                </span>
                                <?php if (!empty($check['recommendation'])): ?>
                                    <br><small class="text-muted"><?php echo $check['recommendation']; ?></small>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-3">
                                <?php if (isset($check['url'])): ?>
                                    <a href="<?php echo $check['url']; ?>" target="_blank" class="btn btn-sm btn-outline-primary">Check</a>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content SEO -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-file-alt"></i> Content SEO</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>Movies SEO Status</h5>
                                <div class="progress mb-2">
                                    <div class="progress-bar" style="width: <?php echo $seo_results['content']['movies_seo']['completion']; ?>%">
                                        <?php echo $seo_results['content']['movies_seo']['completion']; ?>%
                                    </div>
                                </div>
                                <p>
                                    Total: <?php echo $seo_results['content']['movies_seo']['total']; ?> |
                                    Missing Title: <?php echo $seo_results['content']['movies_seo']['missing_title']; ?> |
                                    Missing Description: <?php echo $seo_results['content']['movies_seo']['missing_description']; ?>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <h5>TV Shows SEO Status</h5>
                                <div class="progress mb-2">
                                    <div class="progress-bar" style="width: <?php echo $seo_results['content']['tvshows_seo']['completion']; ?>%">
                                        <?php echo $seo_results['content']['tvshows_seo']['completion']; ?>%
                                    </div>
                                </div>
                                <p>
                                    Total: <?php echo $seo_results['content']['tvshows_seo']['total']; ?> |
                                    Missing Title: <?php echo $seo_results['content']['tvshows_seo']['missing_title']; ?> |
                                    Missing Description: <?php echo $seo_results['content']['tvshows_seo']['missing_description']; ?>
                                </p>
                            </div>
                        </div>
                        
                        <?php if ($seo_results['content']['duplicate_movies'] > 0): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <?php echo $seo_results['content']['duplicate_movies']; ?> duplicate movie titles found. Consider removing duplicates.
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-tools"></i> Quick SEO Actions</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="seo_tools.php?action=generate_sitemap" class="btn btn-primary w-100">
                                    <i class="fas fa-sitemap"></i> Generate Sitemap
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="seo_tools.php?action=update_meta_tags" class="btn btn-success w-100">
                                    <i class="fas fa-tags"></i> Update Meta Tags
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="<?php echo $seo_results['indexing']['google_search_url']; ?>" target="_blank" class="btn btn-info w-100">
                                    <i class="fab fa-google"></i> Check Google Index
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="https://search.google.com/search-console" target="_blank" class="btn btn-warning w-100">
                                    <i class="fas fa-chart-line"></i> Search Console
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recommendations -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-lightbulb"></i> SEO Recommendations</h4>
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <?php if ($seo_score < 80): ?>
                            <li class="list-group-item">
                                <i class="fas fa-arrow-right text-primary"></i>
                                Complete missing SEO titles and descriptions for all content
                            </li>
                            <?php endif; ?>
                            
                            <?php if (!strpos(SITE_URL, 'https://')): ?>
                            <li class="list-group-item">
                                <i class="fas fa-arrow-right text-danger"></i>
                                Enable HTTPS SSL certificate immediately
                            </li>
                            <?php endif; ?>
                            
                            <li class="list-group-item">
                                <i class="fas fa-arrow-right text-info"></i>
                                Submit sitemap to Google Search Console and Bing Webmaster Tools
                            </li>
                            
                            <li class="list-group-item">
                                <i class="fas fa-arrow-right text-success"></i>
                                Optimize images with alt tags and compress file sizes
                            </li>
                            
                            <li class="list-group-item">
                                <i class="fas fa-arrow-right text-warning"></i>
                                Create quality backlinks from relevant websites
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
