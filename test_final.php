<?php
// Final test to check if all issues are resolved
echo "<h2>Final SEO System Test</h2>";

// Test 1: Check if config.php loads properly
echo "<h3>1. Config Test:</h3>";
if (file_exists('includes/config.php')) {
    require_once 'includes/config.php';
    if (isset($conn)) {
        echo "✅ Database connection successful<br>";
    } else {
        echo "❌ Database connection failed<br>";
    }
} else {
    echo "❌ config.php not found<br>";
}

// Test 2: Check SEO helper functions
echo "<h3>2. SEO Functions Test:</h3>";
if (file_exists('includes/seo_helper.php')) {
    require_once 'includes/seo_helper.php';
    
    if (function_exists('generateMovieSEO')) {
        echo "✅ generateMovieSEO function exists<br>";
    } else {
        echo "❌ generateMovieSEO function missing<br>";
    }
    
    if (function_exists('generateTvShowSEO')) {
        echo "✅ generateTvShowSEO function exists<br>";
    } else {
        echo "❌ generateTvShowSEO function missing<br>";
    }
} else {
    echo "❌ seo_helper.php not found<br>";
}

// Test 3: Check CSS files
echo "<h3>3. CSS Files Test:</h3>";
$css_files = [
    'css/style.css',
    'css/design-improvements.css', 
    'css/tags.css',
    'css/mobile-app.css'
];

foreach ($css_files as $file) {
    if (file_exists($file)) {
        echo "✅ $file exists<br>";
    } else {
        echo "⚠️ $file missing (will be skipped)<br>";
    }
}

// Test 4: Test movie details page
echo "<h3>4. Movie Details Page Test:</h3>";
if (file_exists('movie_details.php')) {
    echo "✅ movie_details.php exists<br>";
    
    // Check if we can include it without errors
    ob_start();
    $error_occurred = false;
    
    try {
        // Simulate a movie ID for testing
        $_GET['id'] = 1;
        
        // Capture any output
        include 'movie_details.php';
        $output = ob_get_contents();
        
        if (strpos($output, 'Fatal error') !== false || strpos($output, 'Parse error') !== false) {
            $error_occurred = true;
        }
        
    } catch (Exception $e) {
        $error_occurred = true;
        echo "❌ Error: " . $e->getMessage() . "<br>";
    }
    
    ob_end_clean();
    
    if (!$error_occurred) {
        echo "✅ movie_details.php loads without fatal errors<br>";
    } else {
        echo "❌ movie_details.php has errors<br>";
    }
} else {
    echo "❌ movie_details.php not found<br>";
}

echo "<h3>Test Complete!</h3>";
echo "<p>If all tests show ✅, your SEO system is working properly.</p>";
?>
