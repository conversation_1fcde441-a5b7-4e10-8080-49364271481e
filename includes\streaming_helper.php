<?php
/**
 * Helper functions for streaming and download links
 */

/**
 * Check if a URL is streamable directly
 *
 * @param string $url The URL to check
 * @return bool True if the URL is likely streamable
 */
function isStreamableUrl($url) {
    // If URL is empty, it's not streamable
    if (empty($url)) {
        return false;
    }

    $streamable_domains = [
        'bdmovieshub.workers.dev',
        'divine-smoke-8290.bdmovieshub.workers.dev',
        'drive.google.com',
        'docs.google.com',
        'player.vimeo.com',
        'vimeo.com',
        'youtube.com',
        'youtu.be',
        'dailymotion.com',
        'dai.ly',
        'streamable.com',
        'mega.nz',
        'mediafire.com',
        'dropbox.com',
        'dl.dropboxusercontent.com',
        'amazonaws.com',
        'cloudfront.net',
        'cloudflare.com'
    ];

    foreach ($streamable_domains as $domain) {
        if (stripos($url, $domain) !== false) {
            return true;
        }
    }

    // Check file extension
    $file_extensions = ['.mp4', '.webm', '.mkv', '.avi', '.mov', '.flv', '.wmv', '.m3u8'];
    foreach ($file_extensions as $ext) {
        if (stripos($url, $ext) !== false) {
            return true;
        }
    }

    return false;
}

/**
 * Generate a streaming URL for the player
 *
 * @param string $url The original URL
 * @param string $title The title of the content
 * @param string $poster The poster image URL
 * @param bool $is_premium Whether this is premium content
 * @param string $player_type The type of player to use (default, plyr, shaka, videojs)
 * @param string $subtitle_url_bn Bangla subtitle URL (optional)
 * @param string $subtitle_url_en English subtitle URL (optional)
 * @return string The streaming URL
 */
function getStreamingUrl($url, $title = '', $poster = '', $is_premium = false, $player_type = '', $subtitle_url_bn = '', $subtitle_url_en = '') {
    // Check if URL is from Cloudflare Workers
    $is_worker_url = (stripos($url, 'workers.dev') !== false);

    // Determine file extension
    $file_extension = strtolower(pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION));

    // Determine player file based on URL type and player preference
    if (!empty($player_type)) {
        // Use specified player if provided
        if ($player_type == 'shaka') {
            $player_file = 'shaka_player.php';
        } elseif ($player_type == 'plyr') {
            $player_file = 'plyr_player_enhanced.php';
        } elseif ($player_type == 'videojs') {
            $player_file = 'videojs_player.php';
        } else {
            $player_file = 'play.php';
        }
    } else {
        // Check if default player is set in config
        if (defined('DEFAULT_PLAYER')) {
            if (DEFAULT_PLAYER == 'shaka') {
                $player_file = 'shaka_player.php';
            } elseif (DEFAULT_PLAYER == 'plyr') {
                $player_file = 'plyr_player_enhanced.php';
            } elseif (DEFAULT_PLAYER == 'videojs') {
                $player_file = 'videojs_player.php';
            } else {
                $player_file = 'play.php';
            }
        } else {
            // Auto-select player based on URL and file type
            if ($is_worker_url) {
                $player_file = 'direct_player.php';
            } elseif ($file_extension == 'mkv' || $file_extension == 'm3u8' || $file_extension == 'mpd') {
                // Use direct player for MKV, HLS (m3u8), and DASH (mpd) formats
                $player_file = 'direct_player.php';
            } else {
                $player_file = 'play.php';
            }
        }
    }

    $streaming_url = $player_file . '?url=' . urlencode($url);

    if (!empty($title)) {
        $streaming_url .= '&title=' . urlencode($title);
    }

    if (!empty($poster)) {
        $streaming_url .= '&poster=' . urlencode($poster);
    }

    if ($is_premium) {
        $streaming_url .= '&premium=1';
    }

    // Add subtitle URLs if provided
    if (!empty($subtitle_url_bn)) {
        $streaming_url .= '&subtitle_bn=' . urlencode($subtitle_url_bn);
    }

    if (!empty($subtitle_url_en)) {
        $streaming_url .= '&subtitle_en=' . urlencode($subtitle_url_en);
    }

    return $streaming_url;
}

/**
 * Display streaming and download buttons
 *
 * @param string $url The URL of the content
 * @param string $title The title of the content
 * @param string $poster The poster image URL
 * @param string $quality The quality of the content (e.g. 720p, 1080p)
 * @param bool $is_premium Whether this is premium content
 * @return string HTML for the buttons
 */
function displayStreamingButtons($url, $title, $poster = '', $quality = '', $is_premium = false) {
    // If URL is empty, return empty string
    if (empty($url)) {
        return '';
    }

    $html = '<div class="d-flex gap-2 mb-2">';

    // Stream button
    if (isStreamableUrl($url)) {
        $stream_url = getStreamingUrl($url, $title, $poster, $is_premium);
        $html .= '<a href="' . $stream_url . '" class="btn btn-primary btn-sm">';
        $html .= '<i class="fas fa-play-circle me-1"></i> Stream';
        if (!empty($quality)) {
            $html .= ' (' . $quality . ')';
        }
        $html .= '</a>';
    }

    // Download button
    $html .= '<a href="' . $url . '" class="btn btn-secondary btn-sm" download target="_blank">';
    $html .= '<i class="fas fa-download me-1"></i> Download';
    if (!empty($quality)) {
        $html .= ' (' . $quality . ')';
    }
    $html .= '</a>';

    $html .= '</div>';

    return $html;
}

/**
 * Generate a streaming URL for TV series episodes
 *
 * @param string $url The original URL
 * @param string $title The title of the episode
 * @param string $poster The poster image URL
 * @param bool $is_premium Whether this is premium content
 * @param string $tvshow_title The TV show title
 * @param int $season_number The season number
 * @param int $episode_number The episode number
 * @param string $player_type The type of player to use (default, plyr, shaka, videojs)
 * @param string $subtitle_url_bn Bangla subtitle URL (optional)
 * @param string $subtitle_url_en English subtitle URL (optional)
 * @return string The streaming URL
 */
function getEpisodeStreamingUrl($url, $title = '', $poster = '', $is_premium = false, $tvshow_title = '', $season_number = 0, $episode_number = 0, $player_type = '', $subtitle_url_bn = '', $subtitle_url_en = '') {
    // Check if URL is from Cloudflare Workers
    $is_worker_url = (stripos($url, 'workers.dev') !== false);

    // Determine file extension
    $file_extension = strtolower(pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION));

    // Determine player file based on URL type and player preference
    if (!empty($player_type)) {
        // Use specified player if provided
        if ($player_type == 'shaka') {
            $player_file = 'shaka_player.php';
        } elseif ($player_type == 'plyr') {
            $player_file = 'plyr_player_enhanced.php';
        } elseif ($player_type == 'videojs') {
            $player_file = 'videojs_player.php';
        } else {
            $player_file = 'play.php';
        }
    } else {
        // Check if default player is set in config
        if (defined('DEFAULT_PLAYER')) {
            if (DEFAULT_PLAYER == 'shaka') {
                $player_file = 'shaka_player.php';
            } elseif (DEFAULT_PLAYER == 'plyr') {
                $player_file = 'plyr_player_enhanced.php';
            } elseif (DEFAULT_PLAYER == 'videojs') {
                $player_file = 'videojs_player.php';
            } else {
                $player_file = 'play.php';
            }
        } else {
            // Auto-select player based on URL and file type
            if ($is_worker_url) {
                $player_file = 'direct_player.php';
            } elseif ($file_extension == 'mkv' || $file_extension == 'm3u8' || $file_extension == 'mpd') {
                // Use direct player for MKV, HLS (m3u8), and DASH (mpd) formats
                $player_file = 'direct_player.php';
            } else {
                $player_file = 'play.php';
            }
        }
    }

    $streaming_url = $player_file . '?url=' . urlencode($url);

    // Create a full episode title if TV show info is provided
    if (!empty($tvshow_title) && $season_number > 0 && $episode_number > 0) {
        $full_title = $tvshow_title . ' - S' . $season_number . 'E' . str_pad($episode_number, 2, '0', STR_PAD_LEFT);
        if (!empty($title)) {
            $full_title .= ': ' . $title;
        }
        $streaming_url .= '&title=' . urlencode($full_title);
    } else if (!empty($title)) {
        $streaming_url .= '&title=' . urlencode($title);
    }

    if (!empty($poster)) {
        $streaming_url .= '&poster=' . urlencode($poster);
    }

    if ($is_premium) {
        $streaming_url .= '&premium=1';
    }

    // Add episode info as separate parameters for potential use in the player
    if (!empty($tvshow_title)) {
        $streaming_url .= '&tvshow=' . urlencode($tvshow_title);
    }

    if ($season_number > 0) {
        $streaming_url .= '&season=' . $season_number;
    }

    if ($episode_number > 0) {
        $streaming_url .= '&episode=' . $episode_number;
    }

    // Add subtitle URLs if provided
    if (!empty($subtitle_url_bn)) {
        $streaming_url .= '&subtitle_bn=' . urlencode($subtitle_url_bn);
    }

    if (!empty($subtitle_url_en)) {
        $streaming_url .= '&subtitle_en=' . urlencode($subtitle_url_en);
    }

    return $streaming_url;
}

/**
 * Display only download button with enhanced design
 *
 * @param string $url The URL of the content
 * @param string $quality The quality of the content (e.g. 720p, 1080p)
 * @param string $type The type of the content (e.g. mp4, mkv)
 * @return string HTML for the download button
 */
function displayDownloadButton($url, $quality = '', $type = '') {
    // If URL is empty, return empty string
    if (empty($url)) {
        return '';
    }

    $html = '<div class="download-button-container">';

    // Download button
    $html .= '<a href="' . $url . '" class="download-button" download target="_blank">';
    $html .= '<div class="download-icon"><i class="fas fa-download"></i></div>';
    $html .= '<div class="download-info">';
    $html .= '<span class="download-text">Download</span>';
    if (!empty($quality) || !empty($type)) {
        $html .= '<span class="download-meta">';
        if (!empty($quality)) {
            $html .= $quality;
        }
        if (!empty($quality) && !empty($type)) {
            $html .= ' - ';
        }
        if (!empty($type)) {
            $html .= strtoupper($type);
        }
        $html .= '</span>';
    }
    $html .= '</div>';
    $html .= '</a>';

    $html .= '</div>';

    return $html;
}

/**
 * Get the best player for a download link and generate a streaming URL
 *
 * @param string $url The download link URL
 * @param string $title The title of the content
 * @param string $poster The poster image URL
 * @param bool $is_premium Whether this is premium content
 * @param string $quality The quality of the content (e.g. 720p, 1080p)
 * @param string $server_name The server name (optional)
 * @param int $movie_id The movie ID (optional, for subtitle lookup)
 * @param int $series_id The series ID (optional, for subtitle lookup)
 * @param int $season The season number (optional, for subtitle lookup)
 * @param int $episode The episode number (optional, for subtitle lookup)
 * @param int $link_id The link ID (optional, for subtitle lookup)
 * @param string $subtitle_url_bn Bangla subtitle URL (optional)
 * @param string $subtitle_url_en English subtitle URL (optional)
 * @return string The streaming URL
 */
function getPlayUrlFromDownload($url, $title = '', $poster = '', $is_premium = false, $quality = '', $server_name = '', $movie_id = 0, $series_id = 0, $season = 0, $episode = 0, $link_id = 0, $subtitle_url_bn = '', $subtitle_url_en = '') {
    // Check if URL is from Cloudflare Workers
    $is_worker_url = (stripos($url, 'workers.dev') !== false);

    // Determine file extension
    $file_extension = strtolower(pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION));

    // Select the appropriate player based on URL and file type
    if ($is_worker_url) {
        // For all Cloudflare Workers links, use the direct player
        $player_file = 'direct_player.php';
    } elseif ($file_extension == 'mkv') {
        // For all MKV files, use the direct player
        $player_file = 'direct_player.php';
    } elseif ($file_extension == 'm3u8' || $file_extension == 'mpd') {
        // For HLS (m3u8) and DASH (mpd) formats, use the direct player
        $player_file = 'direct_player.php';
    } else {
        // For all other formats, use the default player
        $player_file = 'play.php';
    }

    // Build the streaming URL
    $streaming_url = $player_file . '?url=' . urlencode($url);

    // Add title if provided
    if (!empty($title)) {
        $streaming_url .= '&title=' . urlencode($title);
    }

    // Add poster if provided
    if (!empty($poster)) {
        $streaming_url .= '&poster=' . urlencode($poster);
    }

    // Add premium flag if needed
    if ($is_premium) {
        $streaming_url .= '&premium=1';
    }

    // Add quality if provided
    if (!empty($quality)) {
        $streaming_url .= '&quality=' . urlencode($quality);
    }

    // Add server name if provided
    if (!empty($server_name)) {
        $streaming_url .= '&server=' . urlencode($server_name);
    }

    // Add content IDs for subtitle lookup if provided
    if ($movie_id > 0) {
        $streaming_url .= '&movie_id=' . $movie_id;
    }

    if ($series_id > 0) {
        $streaming_url .= '&series_id=' . $series_id;
    }

    if ($season > 0) {
        $streaming_url .= '&season=' . $season;
    }

    if ($episode > 0) {
        $streaming_url .= '&episode=' . $episode;
    }

    if ($link_id > 0) {
        $streaming_url .= '&link_id=' . $link_id;
    }

    // Add subtitle URLs if provided
    if (!empty($subtitle_url_bn)) {
        $streaming_url .= '&subtitle_bn=' . urlencode($subtitle_url_bn);
    }

    if (!empty($subtitle_url_en)) {
        $streaming_url .= '&subtitle_en=' . urlencode($subtitle_url_en);
    }

    return $streaming_url;
}

/**
 * Display download and play buttons for a download link
 *
 * @param string $url The download link URL
 * @param string $title The title of the content
 * @param string $poster The poster image URL
 * @param string $quality The quality of the content (e.g. 720p, 1080p)
 * @param bool $is_premium Whether this is premium content
 * @param string $server_name The server name (optional)
 * @param int $movie_id The movie ID (optional, for subtitle lookup)
 * @param int $series_id The series ID (optional, for subtitle lookup)
 * @param int $season The season number (optional, for subtitle lookup)
 * @param int $episode The episode number (optional, for subtitle lookup)
 * @param int $link_id The link ID (optional, for subtitle lookup)
 * @param string $subtitle_url_bn Bangla subtitle URL (optional)
 * @param string $subtitle_url_en English subtitle URL (optional)
 * @return string HTML for the buttons
 */
function displayDownloadAndPlayButtons($url, $title = '', $poster = '', $quality = '', $is_premium = false, $server_name = '', $movie_id = 0, $series_id = 0, $season = 0, $episode = 0, $link_id = 0, $subtitle_url_bn = '', $subtitle_url_en = '') {
    // If URL is empty, return empty string
    if (empty($url)) {
        return '';
    }

    $html = '<div class="d-flex gap-2">';

    // Download button
    $html .= '<a href="' . $url . '" class="btn btn-sm btn-success" target="_blank" download>';
    $html .= '<i class="fas fa-download me-1"></i> ডাউনলোড';
    $html .= '</a>';

    // Play button
    $play_url = getPlayUrlFromDownload($url, $title, $poster, $is_premium, $quality, $server_name, $movie_id, $series_id, $season, $episode, $link_id, $subtitle_url_bn, $subtitle_url_en);
    $html .= '<a href="' . $play_url . '" class="btn btn-sm btn-primary">';
    $html .= '<i class="fas fa-play me-1"></i> প্লে';
    $html .= '</a>';

    // Subtitle buttons
    if (!empty($subtitle_url_bn) || !empty($subtitle_url_en)) {
        $html .= '<div class="dropdown d-inline-block">';
        $html .= '<button class="btn btn-sm btn-secondary dropdown-toggle" type="button" id="subtitleDropdown" data-bs-toggle="dropdown" aria-expanded="false">';
        $html .= '<i class="fas fa-closed-captioning me-1"></i> সাবটাইটেল';
        $html .= '</button>';
        $html .= '<ul class="dropdown-menu" aria-labelledby="subtitleDropdown">';

        if (!empty($subtitle_url_bn)) {
            $html .= '<li><a class="dropdown-item" href="' . $subtitle_url_bn . '" target="_blank" download>';
            $html .= '<i class="fas fa-download me-1"></i> বাংলা সাবটাইটেল';
            $html .= '</a></li>';
        }

        if (!empty($subtitle_url_en)) {
            $html .= '<li><a class="dropdown-item" href="' . $subtitle_url_en . '" target="_blank" download>';
            $html .= '<i class="fas fa-download me-1"></i> ইংরেজি সাবটাইটেল';
            $html .= '</a></li>';
        }

        $html .= '</ul>';
        $html .= '</div>';
    }

    $html .= '</div>';

    return $html;
}
