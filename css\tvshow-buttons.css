/* Premium and Free Button Styles */

/* Premium Notice for non-premium users */
.premium-notice {
    position: relative;
    opacity: 0.85;
}

.premium-notice::before {
    content: "প্রিমিয়াম সাবস্ক্রাইব করুন";
    cursor: pointer;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    z-index: 10;
    border-radius: 8px;
    font-size: 0.9rem;
    text-align: center;
    padding: 5px;
}

.premium-notice:hover::before {
    background: rgba(0, 0, 0, 0.8);
}
.button-group {
    display: flex;
    margin-bottom: 15px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    width: 100%;
    max-width: 300px;
}

.button-group:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
}

/* Premium Button Styles */
.premium-group {
    border: 2px solid #FFD700;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.premium-group::before {
    content: "";
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, #FFD700, transparent, #FFD700, transparent);
    z-index: -1;
    animation: premium-glow 3s linear infinite;
}

@keyframes premium-glow {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.premium-btn {
    background: linear-gradient(135deg, #ff7b00, #ff0062);
    color: white;
    border: none;
    position: relative;
    overflow: hidden;
    z-index: 1;
    font-weight: 600;
}

.premium-btn::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: 0.5s;
    z-index: -1;
}

.premium-btn:hover::after {
    left: 100%;
}

.premium-btn:hover {
    background: linear-gradient(135deg, #ff8b20, #ff2072);
    transform: translateY(-2px);
    box-shadow: 0 7px 15px rgba(255, 0, 98, 0.4);
}

.premium-label {
    font-weight: bold;
    color: #FFD700;
    display: flex;
    align-items: center;
    gap: 5px;
    text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
}

/* Free Button Styles */
.free-group {
    border: 2px solid #4CAF50;
    box-shadow: 0 0 15px rgba(76, 175, 80, 0.4);
    position: relative;
    overflow: hidden;
}

.free-btn {
    background: linear-gradient(135deg, #2E7D32, #4CAF50);
    color: white;
    border: none;
    position: relative;
    overflow: hidden;
    z-index: 1;
    font-weight: 600;
}

.free-btn::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: 0.5s;
    z-index: -1;
}

.free-btn:hover::after {
    left: 100%;
}

.free-btn:hover {
    background: linear-gradient(135deg, #388E3C, #66BB6A);
    transform: translateY(-2px);
    box-shadow: 0 7px 15px rgba(76, 175, 80, 0.4);
}

.free-label {
    font-weight: bold;
    color: white;
    display: flex;
    align-items: center;
    gap: 5px;
    text-shadow: 0 0 5px rgba(76, 175, 80, 0.5);
}

/* Common Button Styles */
.btn-download {
    display: flex;
    align-items: center;
    color: white;
    text-decoration: none;
    padding: 12px 18px;
    border-radius: 8px 0 0 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    flex-grow: 1;
}

.btn-play {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #0066ff, #0044cc);
    color: white;
    text-decoration: none;
    padding: 12px 18px;
    border-radius: 0 8px 8px 0;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 3px 6px rgba(0, 102, 255, 0.3);
    font-size: 1rem;
    min-width: 50px;
}

.btn-play:hover {
    background: linear-gradient(135deg, #0077ff, #0055dd);
    color: white;
    box-shadow: 0 5px 10px rgba(0, 102, 255, 0.5);
    transform: translateY(-2px);
}

.btn-download .quality {
    font-weight: 600;
    margin-right: 10px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.quality-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    font-size: 10px;
    margin-left: 3px;
}

.quality-badge.premium {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #000;
    box-shadow: 0 0 5px rgba(255, 215, 0, 0.7);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.7);
    }
    70% {
        box-shadow: 0 0 0 5px rgba(255, 215, 0, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0);
    }
}

.btn-download .size {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.85rem;
    margin-right: 8px;
    background-color: rgba(0, 0, 0, 0.2);
    padding: 3px 8px;
    border-radius: 4px;
}

/* Responsive Styles */
@media (max-width: 991.98px) {
    .button-group {
        max-width: 280px;
    }

    .btn-download, .btn-play {
        padding: 10px 15px;
    }
}

@media (max-width: 767.98px) {
    .button-group {
        max-width: 100%;
    }

    .episode-buttons {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 15px;
    }
}

@media (max-width: 575.98px) {
    .btn-download, .btn-play {
        padding: 8px 12px;
        font-size: 0.9rem;
    }

    .btn-download .quality {
        margin-right: 6px;
    }

    .btn-download .size {
        font-size: 0.75rem;
        padding: 2px 6px;
    }

    .quality-badge {
        width: 18px;
        height: 18px;
        font-size: 9px;
    }
}

/* Animation for Premium Badge */
@keyframes shine {
    0% {
        background-position: -100% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.premium-badge {
    position: relative;
    overflow: hidden;
}

.premium-badge::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent);
    background-size: 200% 100%;
    animation: shine 2s infinite;
}
