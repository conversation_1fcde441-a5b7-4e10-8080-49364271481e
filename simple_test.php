<?php
require_once 'includes/config.php';

echo "<h2>সহজ শেয়ার লিংক টেস্ট</h2>";

// Get first movie
$movie = mysqli_fetch_assoc(mysqli_query($conn, "SELECT id, title FROM movies LIMIT 1"));
if (!$movie) {
    echo "❌ কোনো মুভি নেই";
    exit;
}

// Create shared link
$token = 'test_' . time();
$insert = "INSERT INTO shared_links (link_token, content_type, content_id, created_by, title, description, access_limit, expires_at, allow_download, allow_streaming, is_active) 
           VALUES ('$token', 'movie', {$movie['id']}, 1, 'টেস্ট - {$movie['title']}', 'টেস্ট', 10, DATE_ADD(NOW(), INTERVAL 7 DAY), 1, 1, 1)";

if (mysqli_query($conn, $insert)) {
    echo "✅ শেয়ার লিংক তৈরি হয়েছে<br>";
    
    // Test query
    $query = "SELECT * FROM shared_links WHERE link_token = '$token'";
    $result = mysqli_query($conn, $query);
    
    if (mysqli_num_rows($result) > 0) {
        echo "✅ কুয়েরি সফল<br>";
        
        // Create URLs
        $share_url = SITE_URL . '/share.php?token=' . $token;
        $debug_url = SITE_URL . '/share.php?token=' . $token . '&debug=1';
        
        echo "<h3>শেয়ার লিংক:</h3>";
        echo "<p><a href='$debug_url' target='_blank' style='color: green; font-size: 18px;'>🔗 ডিবাগ লিংক (প্রথমে এটা ক্লিক করুন)</a></p>";
        echo "<p><a href='$share_url' target='_blank' style='color: blue; font-size: 18px;'>🔗 সাধারণ লিংক</a></p>";
        
    } else {
        echo "❌ কুয়েরি ফেইল";
    }
} else {
    echo "❌ শেয়ার লিংক তৈরি করতে সমস্যা হয়েছে: " . mysqli_error($conn);
}
?> 