<?php
require_once '../includes/config.php';

// Database connection is already established in config.php
global $conn;

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit();
}

/**
 * Create database tables if they don't exist
 */
function createUserTrackingTables($conn) {
    // Create user_activity table
    $activity_table = "CREATE TABLE IF NOT EXISTS user_activity (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NULL,
        session_id VARCHAR(255) NOT NULL,
        ip_address VARCHAR(45) NOT NULL,
        user_agent TEXT NULL,
        current_page VARCHAR(500) NULL,
        referrer VARCHAR(500) NULL,
        country VARCHAR(100) NULL,
        city VARCHAR(100) NULL,
        device_type VARCHAR(50) NULL,
        browser VARCHAR(100) NULL,
        is_premium BOOLEAN DEFAULT FALSE,
        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        first_visit TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        page_views INT DEFAULT 1,
        INDEX idx_session_id (session_id),
        INDEX idx_last_activity (last_activity),
        INDEX idx_user_id (user_id),
        INDEX idx_ip_address (ip_address)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    // Create user_messages table
    $messages_table = "CREATE TABLE IF NOT EXISTS user_messages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NULL,
        session_id VARCHAR(255) NOT NULL,
        admin_id INT NULL,
        message TEXT NOT NULL,
        message_type ENUM('user', 'admin') NOT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_session_id (session_id),
        INDEX idx_created_at (created_at),
        INDEX idx_user_id (user_id),
        INDEX idx_admin_id (admin_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    mysqli_query($conn, $activity_table);
    mysqli_query($conn, $messages_table);
}

// Create tables
createUserTrackingTables($conn);

// Get statistics
$stats = [];

// Online users (active in last 5 minutes)
$online_query = "SELECT COUNT(DISTINCT session_id) as count FROM user_activity 
                 WHERE last_activity >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)";
$online_result = mysqli_query($conn, $online_query);
$stats['online'] = $online_result ? mysqli_fetch_assoc($online_result)['count'] : 0;

// Today's visitors
$today_query = "SELECT COUNT(DISTINCT session_id) as count FROM user_activity 
                WHERE DATE(first_visit) = CURDATE()";
$today_result = mysqli_query($conn, $today_query);
$stats['today'] = $today_result ? mysqli_fetch_assoc($today_result)['count'] : 0;

// Premium users online
$premium_query = "SELECT COUNT(DISTINCT session_id) as count FROM user_activity 
                  WHERE last_activity >= DATE_SUB(NOW(), INTERVAL 5 MINUTE) AND is_premium = TRUE";
$premium_result = mysqli_query($conn, $premium_query);
$stats['premium'] = $premium_result ? mysqli_fetch_assoc($premium_result)['count'] : 0;

// Unread messages
$messages_query = "SELECT COUNT(*) as count FROM user_messages 
                   WHERE message_type = 'user' AND is_read = FALSE";
$messages_result = mysqli_query($conn, $messages_query);
$stats['messages'] = $messages_result ? mysqli_fetch_assoc($messages_result)['count'] : 0;

// Get active users
$users_query = "SELECT ua.*, u.username, u.email 
                FROM user_activity ua 
                LEFT JOIN users u ON ua.user_id = u.id 
                WHERE ua.last_activity >= DATE_SUB(NOW(), INTERVAL 5 MINUTE) 
                ORDER BY ua.last_activity DESC 
                LIMIT 50";
$users_result = mysqli_query($conn, $users_query);
$active_users = [];
if ($users_result) {
    while ($row = mysqli_fetch_assoc($users_result)) {
        $active_users[] = $row;
    }
}

// Auto-cleanup old records (older than 24 hours)
$cleanup_query = "DELETE FROM user_activity WHERE last_activity < DATE_SUB(NOW(), INTERVAL 24 HOUR)";
mysqli_query($conn, $cleanup_query);

$page_title = "লাইভ ইউজার ট্র্যাকার";
$current_page = basename($_SERVER['PHP_SELF']);
include 'includes/header_dark.php';
?>

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-users-cog text-primary"></i> লাইভ ইউজার ট্র্যাকার
        </h1>
        <div class="d-flex align-items-center">
            <div class="form-check form-switch me-3">
                <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                <label class="form-check-label" for="autoRefresh">
                    অটো রিফ্রেশ (৫ সেকেন্ড)
                </label>
            </div>
            <button class="btn btn-primary btn-sm" onclick="refreshData()">
                <i class="fas fa-sync-alt"></i> রিফ্রেশ
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                অনলাইন ইউজার
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="online-count">
                                <?php echo $stats['online']; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                আজকের ভিজিটর
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="today-count">
                                <?php echo $stats['today']; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                প্রিমিয়াম ইউজার
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="premium-count">
                                <?php echo $stats['premium']; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-crown fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                নতুন মেসেজ
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="messages-count">
                                <?php echo $stats['messages']; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-envelope fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Users Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-user-clock"></i> সক্রিয় ইউজারগণ (শেষ ৫ মিনিট)
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="activeUsersTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ইউজার</th>
                            <th>IP ঠিকানা</th>
                            <th>অবস্থান</th>
                            <th>ডিভাইস</th>
                            <th>বর্তমান পেজ</th>
                            <th>পেজ ভিউ</th>
                            <th>শেষ সক্রিয়তা</th>
                            <th>অ্যাকশন</th>
                        </tr>
                    </thead>
                    <tbody id="users-tbody">
                        <?php foreach ($active_users as $user): ?>
                        <tr>
                            <td>
                                <?php if ($user['username']): ?>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-sm me-2">
                                            <span class="badge bg-<?php echo $user['is_premium'] ? 'warning' : 'secondary'; ?>">
                                                <?php echo $user['is_premium'] ? 'P' : 'R'; ?>
                                            </span>
                                        </div>
                                        <div>
                                            <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($user['email']); ?></small>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">অতিথি ইউজার</span>
                                    <br><small>Session: <?php echo substr($user['session_id'], 0, 8); ?>...</small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <code><?php echo htmlspecialchars($user['ip_address']); ?></code>
                            </td>
                            <td>
                                <?php if ($user['country'] || $user['city']): ?>
                                    <i class="fas fa-map-marker-alt text-muted"></i>
                                    <?php echo htmlspecialchars($user['city'] . ', ' . $user['country']); ?>
                                <?php else: ?>
                                    <span class="text-muted">অজানা</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($user['device_type'] || $user['browser']): ?>
                                    <i class="fas fa-<?php echo $user['device_type'] == 'Mobile' ? 'mobile-alt' : 'desktop'; ?> text-muted"></i>
                                    <?php echo htmlspecialchars($user['device_type'] . ' - ' . $user['browser']); ?>
                                <?php else: ?>
                                    <span class="text-muted">অজানা</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($user['current_page']): ?>
                                    <small><?php echo htmlspecialchars(substr($user['current_page'], 0, 50)); ?><?php echo strlen($user['current_page']) > 50 ? '...' : ''; ?></small>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-info"><?php echo $user['page_views']; ?></span>
                            </td>
                            <td>
                                <small><?php echo date('H:i:s', strtotime($user['last_activity'])); ?></small>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick="openChat('<?php echo $user['session_id']; ?>', '<?php echo htmlspecialchars($user['username'] ?: 'অতিথি'); ?>')">
                                    <i class="fas fa-comment"></i>
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Chat Modal -->
<div class="modal fade" id="chatModal" tabindex="-1" aria-labelledby="chatModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="chatModalLabel">
                    <i class="fas fa-comment-dots"></i> চ্যাট - <span id="chat-user-name"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="chat-messages" style="height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; margin-bottom: 10px;">
                    <!-- Messages will be loaded here -->
                </div>
                <div class="input-group">
                    <input type="text" class="form-control" id="chat-message-input" placeholder="আপনার মেসেজ লিখুন..." maxlength="500">
                    <button class="btn btn-primary" type="button" onclick="sendMessage()">
                        <i class="fas fa-paper-plane"></i> পাঠান
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let autoRefreshInterval;
let currentChatSession = null;

// Auto refresh functionality
document.getElementById('autoRefresh').addEventListener('change', function() {
    if (this.checked) {
        startAutoRefresh();
    } else {
        stopAutoRefresh();
    }
});

function startAutoRefresh() {
    autoRefreshInterval = setInterval(refreshData, 5000);
}

function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
}

function refreshData() {
    fetch('ajax/get_user_tracking_data.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update statistics
                document.getElementById('online-count').textContent = data.stats.online;
                document.getElementById('today-count').textContent = data.stats.today;
                document.getElementById('premium-count').textContent = data.stats.premium;
                document.getElementById('messages-count').textContent = data.stats.messages;
                
                // Update users table
                updateUsersTable(data.users);
            }
        })
        .catch(error => console.error('Error refreshing data:', error));
}

function updateUsersTable(users) {
    const tbody = document.getElementById('users-tbody');
    tbody.innerHTML = '';
    
    users.forEach(user => {
        const row = createUserRow(user);
        tbody.appendChild(row);
    });
}

function createUserRow(user) {
    const row = document.createElement('tr');
    
    const userInfo = user.username ? 
        `<div class="d-flex align-items-center">
            <div class="avatar avatar-sm me-2">
                <span class="badge bg-${user.is_premium ? 'warning' : 'secondary'}">
                    ${user.is_premium ? 'P' : 'R'}
                </span>
            </div>
            <div>
                <strong>${user.username}</strong>
                <br><small class="text-muted">${user.email || ''}</small>
            </div>
        </div>` :
        `<span class="text-muted">অতিথি ইউজার</span>
         <br><small>Session: ${user.session_id.substring(0, 8)}...</small>`;
    
    const location = (user.country || user.city) ? 
        `<i class="fas fa-map-marker-alt text-muted"></i> ${user.city}, ${user.country}` :
        '<span class="text-muted">অজানা</span>';
    
    const device = (user.device_type || user.browser) ?
        `<i class="fas fa-${user.device_type === 'Mobile' ? 'mobile-alt' : 'desktop'} text-muted"></i> ${user.device_type} - ${user.browser}` :
        '<span class="text-muted">অজানা</span>';
    
    const currentPage = user.current_page ?
        `<small>${user.current_page.substring(0, 50)}${user.current_page.length > 50 ? '...' : ''}</small>` :
        '<span class="text-muted">-</span>';
    
    row.innerHTML = `
        <td>${userInfo}</td>
        <td><code>${user.ip_address}</code></td>
        <td>${location}</td>
        <td>${device}</td>
        <td>${currentPage}</td>
        <td><span class="badge bg-info">${user.page_views}</span></td>
        <td><small>${new Date(user.last_activity).toLocaleTimeString()}</small></td>
        <td>
            <button class="btn btn-sm btn-primary" onclick="openChat('${user.session_id}', '${user.username || 'অতিথি'}')">
                <i class="fas fa-comment"></i>
            </button>
        </td>
    `;
    
    return row;
}

function openChat(sessionId, userName) {
    currentChatSession = sessionId;
    document.getElementById('chat-user-name').textContent = userName;
    
    // Load existing messages
    loadChatMessages(sessionId);
    
    // Show modal
    const chatModal = new bootstrap.Modal(document.getElementById('chatModal'));
    chatModal.show();
}

function loadChatMessages(sessionId) {
    fetch(`../includes/chat_handler.php?action=get_messages&session_id=${sessionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const messagesDiv = document.getElementById('chat-messages');
                messagesDiv.innerHTML = '';
                
                data.messages.forEach(msg => {
                    const messageDiv = document.createElement('div');
                    messageDiv.className = `mb-2 ${msg.message_type === 'admin' ? 'text-end' : 'text-start'}`;
                    messageDiv.innerHTML = `
                        <div class="d-inline-block p-2 rounded ${msg.message_type === 'admin' ? 'bg-primary text-white' : 'bg-light'}">
                            ${msg.message}
                            <br><small class="opacity-75">${msg.time}</small>
                        </div>
                    `;
                    messagesDiv.appendChild(messageDiv);
                });
                
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            }
        })
        .catch(error => console.error('Error loading messages:', error));
}

function sendMessage() {
    const messageInput = document.getElementById('chat-message-input');
    const message = messageInput.value.trim();
    
    if (!message || !currentChatSession) return;
    
    const formData = new FormData();
    formData.append('action', 'send_message');
    formData.append('session_id', currentChatSession);
    formData.append('message', message);
    formData.append('message_type', 'admin');
    
    fetch('../includes/chat_handler.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            messageInput.value = '';
            loadChatMessages(currentChatSession);
        }
    })
    .catch(error => console.error('Error sending message:', error));
}

// Enter key to send message
document.getElementById('chat-message-input').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        sendMessage();
    }
});

// Start auto refresh on page load
document.addEventListener('DOMContentLoaded', function() {
    startAutoRefresh();
});
</script>

<?php include 'includes/footer.php'; ?>
