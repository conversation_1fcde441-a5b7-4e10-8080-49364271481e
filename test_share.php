<?php
// Test script for share.php functionality
require_once 'includes/config.php';

echo "<h2>Share.php Compatibility Test</h2>";

// Test 1: Check if mysqli_stmt_get_result exists
echo "<h3>Test 1: mysqli_stmt_get_result availability</h3>";
if (function_exists('mysqli_stmt_get_result')) {
    echo "✅ mysqli_stmt_get_result is available<br>";
} else {
    echo "⚠️ mysqli_stmt_get_result is NOT available - will use fallback method<br>";
}

// Test 2: Check database connection
echo "<h3>Test 2: Database connection</h3>";
if ($conn) {
    echo "✅ Database connection successful<br>";
} else {
    echo "❌ Database connection failed<br>";
    exit;
}

// Test 3: Check if shared_links table exists
echo "<h3>Test 3: Check shared_links table</h3>";
$check_table = mysqli_query($conn, "SHOW TABLES LIKE 'shared_links'");
if (mysqli_num_rows($check_table) > 0) {
    echo "✅ shared_links table exists<br>";
} else {
    echo "❌ shared_links table does not exist<br>";
}

// Test 4: Check if download_links table exists
echo "<h3>Test 4: Check download_links table</h3>";
$check_download = mysqli_query($conn, "SHOW TABLES LIKE 'download_links'");
if (mysqli_num_rows($check_download) > 0) {
    echo "✅ download_links table exists<br>";
} else {
    echo "❌ download_links table does not exist<br>";
}

// Test 5: Check if streaming_links table exists
echo "<h3>Test 5: Check streaming_links table</h3>";
$check_streaming = mysqli_query($conn, "SHOW TABLES LIKE 'streaming_links'");
if (mysqli_num_rows($check_streaming) > 0) {
    echo "✅ streaming_links table exists<br>";
} else {
    echo "❌ streaming_links table does not exist<br>";
}

// Test 6: Check if episode_links table exists
echo "<h3>Test 6: Check episode_links table</h3>";
$check_episode = mysqli_query($conn, "SHOW TABLES LIKE 'episode_links'");
if (mysqli_num_rows($check_episode) > 0) {
    echo "✅ episode_links table exists<br>";
} else {
    echo "❌ episode_links table does not exist<br>";
}

// Test 7: Check if movies table exists
echo "<h3>Test 7: Check movies table</h3>";
$check_movies = mysqli_query($conn, "SHOW TABLES LIKE 'movies'");
if (mysqli_num_rows($check_movies) > 0) {
    echo "✅ movies table exists<br>";
} else {
    echo "❌ movies table does not exist<br>";
}

// Test 8: Check if tvshows table exists
echo "<h3>Test 8: Check tvshows table</h3>";
$check_tvshows = mysqli_query($conn, "SHOW TABLES LIKE 'tvshows'");
if (mysqli_num_rows($check_tvshows) > 0) {
    echo "✅ tvshows table exists<br>";
} else {
    echo "❌ tvshows table does not exist<br>";
}

// Test 9: Check if episodes table exists
echo "<h3>Test 9: Check episodes table</h3>";
$check_episodes = mysqli_query($conn, "SHOW TABLES LIKE 'episodes'");
if (mysqli_num_rows($check_episodes) > 0) {
    echo "✅ episodes table exists<br>";
} else {
    echo "❌ episodes table does not exist<br>";
}

// Test 10: Check if shared_link_access_logs table exists
echo "<h3>Test 10: Check shared_link_access_logs table</h3>";
$check_logs = mysqli_query($conn, "SHOW TABLES LIKE 'shared_link_access_logs'");
if (mysqli_num_rows($check_logs) > 0) {
    echo "✅ shared_link_access_logs table exists<br>";
} else {
    echo "❌ shared_link_access_logs table does not exist<br>";
}

echo "<h3>Test Summary</h3>";
echo "All tests completed. If you see any ❌ marks, those tables need to be created.<br>";
echo "The share.php file should now work correctly with both new and old PHP versions.<br>";
?> 