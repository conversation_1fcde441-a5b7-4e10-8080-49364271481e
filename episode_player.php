<?php
require_once 'includes/config.php';

// Check if episode ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect(SITE_URL);
}

$episode_id = (int)$_GET['id'];

// Get episode details
$query = "SELECT e.*, t.title as tvshow_title, t.id as tvshow_id, t.tmdb_id as tvshow_tmdb_id, t.poster as tvshow_poster
          FROM episodes e
          JOIN tvshows t ON e.tvshow_id = t.id
          WHERE e.id = $episode_id";
$result = mysqli_query($conn, $query);

// Check if episode exists
if (mysqli_num_rows($result) == 0) {
    redirect(SITE_URL);
}

$episode = mysqli_fetch_assoc($result);

// Check if episode is premium and user is not premium
if ($episode['is_premium'] && (!isLoggedIn() || !isPremiumUser())) {
    redirect(SITE_URL . '/premium.php');
}

// Get streaming link
$stream_query = "SELECT * FROM episode_links
                WHERE episode_id = $episode_id AND link_type = 'stream'
                ORDER BY is_premium ASC, id ASC LIMIT 1";
$stream_result = mysqli_query($conn, $stream_query);

if (mysqli_num_rows($stream_result) > 0) {
    $stream = mysqli_fetch_assoc($stream_result);
    $video_url = $stream['link_url'];
    $subtitle_url_bn = $stream['subtitle_url_bn'];
    $subtitle_url_en = $stream['subtitle_url_en'];
} else {
    // No streaming link available
    redirect(SITE_URL . '/episode.php?id=' . $episode_id);
}

// Check if the URL is from Cloudflare Workers
$is_worker_url = (stripos($video_url, 'workers.dev') !== false);

// Get poster URL
$poster_url = SITE_URL . '/uploads/' . ($episode["thumbnail"] ? $episode["thumbnail"] : $episode["tvshow_poster"]);

// Get all download links
$download_query = "SELECT * FROM episode_links
                  WHERE episode_id = $episode_id AND link_type = 'download'
                  ORDER BY quality ASC";
$download_result = mysqli_query($conn, $download_query);

// Get all episodes of the same season
$season_episodes_query = "SELECT * FROM episodes
                         WHERE tvshow_id = {$episode['tvshow_id']} AND season_number = {$episode['season_number']}
                         ORDER BY episode_number ASC";
$season_episodes_result = mysqli_query($conn, $season_episodes_query);

// Get all seasons
$seasons_query = "SELECT DISTINCT season_number FROM episodes
                 WHERE tvshow_id = {$episode['tvshow_id']}
                 ORDER BY season_number ASC";
$seasons_result = mysqli_query($conn, $seasons_query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>S<?php echo $episode['season_number']; ?>E<?php echo str_pad($episode['episode_number'], 2, '0', STR_PAD_LEFT); ?>: <?php echo $episode['title']; ?> - <?php echo $episode['tvshow_title']; ?> - <?php echo SITE_NAME; ?></title>
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo SITE_URL; ?>/assets/img/favicon.png" type="image/x-icon">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- JW Player CSS -->
    <style>
        .jwplayer {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
    </style>
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #e50914;
            --secondary-color: #141414;
            --text-color: #ffffff;
            --dark-bg: #000000;
            --card-bg: #181818;
            --border-color: #333333;
        }

        body {
            background-color: var(--dark-bg);
            color: var(--text-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
        }

        .navbar {
            background-color: rgba(0, 0, 0, 0.9);
            padding: 0.5rem 1rem;
        }

        .player-container {
            background-color: var(--dark-bg);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            margin-bottom: 2rem;
        }

        .plyr {
            --plyr-color-main: var(--primary-color);
            --plyr-video-control-color: var(--text-color);
            --plyr-video-control-background-hover: var(--primary-color);
            --plyr-audio-control-background-hover: var(--primary-color);
            --plyr-audio-control-color: var(--text-color);
            --plyr-menu-background: var(--secondary-color);
            --plyr-menu-color: var(--text-color);
            border-radius: 8px;
        }

        .episode-info {
            background-color: var(--card-bg);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .episode-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--text-color);
        }

        .episode-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1rem;
            color: #aaa;
            font-size: 0.9rem;
        }

        .episode-meta span {
            display: flex;
            align-items: center;
        }

        .episode-meta i {
            margin-right: 0.5rem;
        }

        .episode-description {
            margin-bottom: 1.5rem;
            line-height: 1.7;
            color: #ddd;
        }

        .subtitle-options {
            border-top: 1px solid var(--border-color);
            padding-top: 1rem;
            margin-top: 1rem;
        }

        .subtitle-options h4 {
            font-size: 1.1rem;
            margin-bottom: 0.75rem;
        }

        .badge-premium {
            background-color: var(--primary-color);
            color: white;
            padding: 0.3rem 0.6rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: 0.5rem;
        }

        .player-options {
            background-color: var(--card-bg);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .player-options h4 {
            margin-bottom: 1rem;
            font-size: 1.2rem;
            color: var(--text-color);
        }

        .player-options .btn {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            border-radius: 4px;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #c00812;
            border-color: #c00812;
        }

        .btn-outline-light:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .back-button {
            margin-bottom: 1.5rem;
        }

        .back-button .btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            font-weight: 500;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .back-button .btn:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .player-wrapper {
            position: relative;
            padding-top: 56.25%; /* 16:9 Aspect Ratio */
            height: 0;
            overflow: hidden;
            border-radius: 8px;
        }

        .player-wrapper iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 0;
        }

        .episode-list {
            background-color: var(--card-bg);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .episode-list h4 {
            margin-bottom: 1rem;
            font-size: 1.2rem;
            color: var(--text-color);
        }

        .episode-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border-radius: 4px;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
            text-decoration: none;
            color: var(--text-color);
        }

        .episode-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .episode-item.active {
            background-color: var(--primary-color);
        }

        .episode-number {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            margin-right: 1rem;
            font-weight: 600;
        }

        .episode-item.active .episode-number {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .episode-item-title {
            flex: 1;
            font-weight: 500;
        }

        .season-selector {
            margin-bottom: 1rem;
        }

        .season-selector .btn {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .season-selector .btn.active {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        @media (max-width: 768px) {
            .episode-title {
                font-size: 1.5rem;
            }

            .episode-meta {
                font-size: 0.8rem;
                gap: 0.5rem;
            }

            .player-options .btn {
                font-size: 0.9rem;
                padding: 0.4rem 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php require_once 'includes/header.php'; ?>

    <!-- Main Content -->
    <main class="py-4">
        <div class="container">
            <!-- Back Button -->
            <div class="back-button">
                <a href="<?php echo SITE_URL; ?>/details.php?type=tvshow&id=<?php echo $episode['tvshow_id']; ?>" class="btn btn-outline-light">
                    <i class="fas fa-arrow-left"></i> Back to TV Show
                </a>
            </div>

            <!-- Episode Title -->
            <div class="episode-info">
                <h1 class="episode-title">
                    <?php echo $episode['tvshow_title']; ?> - S<?php echo $episode['season_number']; ?>E<?php echo str_pad($episode['episode_number'], 2, '0', STR_PAD_LEFT); ?>: <?php echo $episode['title']; ?>
                    <?php if($episode['is_premium']): ?>
                    <span class="badge-premium">PREMIUM</span>
                    <?php endif; ?>
                </h1>
                <div class="episode-meta">
                    <span><i class="fas fa-clock"></i> <?php echo $episode['duration']; ?> min</span>
                    <?php if($episode['release_date']): ?>
                    <span><i class="fas fa-calendar-alt"></i> <?php echo date('F j, Y', strtotime($episode['release_date'])); ?></span>
                    <?php endif; ?>
                </div>
                <p class="episode-description"><?php echo $episode['description']; ?></p>
            </div>

            <!-- Video Player -->
            <div class="player-container">
                <div class="player-wrapper">
                    <?php
                    // Generate streaming URL using the helper function
                    require_once 'includes/streaming_helper.php';

                    // Determine file extension
                    $file_extension = strtolower(pathinfo(parse_url($video_url, PHP_URL_PATH), PATHINFO_EXTENSION));

                    // Use Shaka Player for MKV, HLS (m3u8), and DASH (mpd) formats
                    if ($file_extension == 'mkv' || $file_extension == 'm3u8' || $file_extension == 'mpd') {
                        $player_type = 'shaka';
                    } elseif ($is_worker_url) {
                        $player_type = 'plyr';
                    } else {
                        $player_type = 'default';
                    }

                    $streaming_url = getEpisodeStreamingUrl(
                        $video_url,
                        $episode['title'],
                        $poster_url,
                        $episode['is_premium'],
                        $episode['tvshow_title'],
                        $episode['season_number'],
                        $episode['episode_number'],
                        $player_type,
                        $subtitle_url_bn,
                        $subtitle_url_en
                    );
                    ?>

                    <?php if ($player_type == 'shaka' || $player_type == 'plyr'): ?>
                    <!-- Use iframe for Shaka Player or Plyr Player -->
                    <iframe src="<?php echo $streaming_url; ?>" allowfullscreen frameborder="0"></iframe>
                    <?php else: ?>
                    <!-- Use JW Player for other formats -->
                    <div id="jwplayer-container"></div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <!-- Player Options -->
                    <div class="player-options">
                        <h4>External Players</h4>
                        <div class="d-flex flex-wrap">
                            <button onclick="openVLC('<?php echo $video_url; ?>')" class="btn btn-outline-light">
                                <i class="fas fa-play-circle me-2"></i>VLC Player
                            </button>
                            <button onclick="openMX('<?php echo $video_url; ?>')" class="btn btn-outline-light">
                                <i class="fas fa-play-circle me-2"></i>MX Player
                            </button>
                            <button onclick="openPlayit('<?php echo $video_url; ?>')" class="btn btn-outline-light">
                                <i class="fas fa-play-circle me-2"></i>Playit Player
                            </button>
                            <button onclick="openKMPlayer('<?php echo $video_url; ?>')" class="btn btn-outline-light">
                                <i class="fas fa-play-circle me-2"></i>KM Player
                            </button>
                            <a href="<?php echo $video_url; ?>" class="btn btn-primary" target="_blank">
                                <i class="fas fa-download me-2"></i>Download
                            </a>
                        </div>

                        <?php if (!empty($subtitle_url_bn) || !empty($subtitle_url_en)): ?>
                        <div class="subtitle-options mt-3">
                            <h4>Subtitles</h4>
                            <div class="d-flex flex-wrap">
                                <?php if (!empty($subtitle_url_bn)): ?>
                                <a href="<?php echo $subtitle_url_bn; ?>" class="btn btn-outline-light" target="_blank" download>
                                    <i class="fas fa-closed-captioning me-2"></i>Bangla Subtitle
                                </a>
                                <?php endif; ?>
                                <?php if (!empty($subtitle_url_en)): ?>
                                <a href="<?php echo $subtitle_url_en; ?>" class="btn btn-outline-light" target="_blank" download>
                                    <i class="fas fa-closed-captioning me-2"></i>English Subtitle
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Download Links -->
                    <?php if(mysqli_num_rows($download_result) > 0): ?>
                    <div class="player-options">
                        <h4>Download Links</h4>
                        <div class="d-flex flex-wrap">
                            <?php while($download = mysqli_fetch_assoc($download_result)): ?>
                            <a href="<?php echo $download['link_url']; ?>" class="btn btn-outline-light" target="_blank">
                                <i class="fas fa-download me-2"></i><?php echo $download['quality']; ?>
                                <?php if($download['is_premium']): ?>
                                <i class="fas fa-crown text-warning ms-1"></i>
                                <?php endif; ?>
                            </a>
                            <?php endwhile; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <div class="col-lg-4">
                    <!-- Episode List -->
                    <div class="episode-list">
                        <h4>Season <?php echo $episode['season_number']; ?> Episodes</h4>

                        <!-- Season Selector -->
                        <?php if(mysqli_num_rows($seasons_result) > 1): ?>
                        <div class="season-selector">
                            <?php while($season = mysqli_fetch_assoc($seasons_result)): ?>
                            <a href="<?php echo SITE_URL; ?>/details.php?type=tvshow&id=<?php echo $episode['tvshow_id']; ?>&season=<?php echo $season['season_number']; ?>" class="btn <?php echo ($season['season_number'] == $episode['season_number']) ? 'btn-danger active' : 'btn-outline-light'; ?>">
                                Season <?php echo $season['season_number']; ?>
                            </a>
                            <?php endwhile; ?>
                        </div>
                        <?php endif; ?>

                        <!-- Episodes -->
                        <div class="episode-items">
                            <?php mysqli_data_seek($season_episodes_result, 0); ?>
                            <?php while($ep = mysqli_fetch_assoc($season_episodes_result)): ?>
                            <a href="<?php echo SITE_URL; ?>/episode_player.php?id=<?php echo $ep['id']; ?>" class="episode-item <?php echo ($ep['id'] == $episode_id) ? 'active' : ''; ?>">
                                <div class="episode-number"><?php echo $ep['episode_number']; ?></div>
                                <div class="episode-item-title"><?php echo $ep['title']; ?></div>
                                <?php if($ep['is_premium']): ?>
                                <i class="fas fa-crown text-warning"></i>
                                <?php endif; ?>
                            </a>
                            <?php endwhile; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <?php require_once 'includes/footer.php'; ?>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- JW Player JS -->
    <script src="https://cdn.jwplayer.com/libraries/64HPbvSQorQcd52B8XFuhMtEoitbvY/EXJmMBfKcXZQU2Rnn.js"></script>

    <script>
        <?php if (!$is_worker_url): ?>
        // Initialize JW Player only for non-worker links
        const player = jwplayer('jwplayer-container').setup({
            file: '<?php echo $video_url; ?>',
            image: '<?php echo $poster_url; ?>',
            title: '<?php echo $episode["tvshow_title"]; ?> - S<?php echo $episode["season_number"]; ?>E<?php echo str_pad($episode["episode_number"], 2, "0", STR_PAD_LEFT); ?>: <?php echo $episode["title"]; ?>',
            width: '100%',
            aspectratio: '16:9',
            primary: 'html5',
            hlshtml: true,
            controls: true,
            autostart: false,
            playbackRateControls: true,
            <?php if (!empty($subtitle_url_bn) || !empty($subtitle_url_en)): ?>
            tracks: [
                <?php if (!empty($subtitle_url_bn)): ?>
                {
                    file: "<?php echo SITE_URL; ?>/subtitle_converter.php?url=<?php echo urlencode($subtitle_url_bn); ?>",
                    label: "Bangla",
                    kind: "captions",
                    "default": true
                }<?php echo !empty($subtitle_url_en) ? ',' : ''; ?>
                <?php endif; ?>
                <?php if (!empty($subtitle_url_en)): ?>
                {
                    file: "<?php echo SITE_URL; ?>/subtitle_converter.php?url=<?php echo urlencode($subtitle_url_en); ?>",
                    label: "English",
                    kind: "captions",
                    "default": <?php echo empty($subtitle_url_bn) ? 'true' : 'false'; ?>
                }
                <?php endif; ?>
            ],
            <?php else: ?>
            tracks: [],
            <?php endif; ?>
            sharing: {
                sites: ['facebook', 'twitter', 'email', 'linkedin']
            }
        });
        <?php endif; ?>

        // External player functions
        function openVLC(url) {
            window.location.href = `vlc://${url}`;
        }

        function openMX(url) {
            window.location.href = `intent:${url}#Intent;package=com.mxtech.videoplayer.ad;end`;
        }

        function openPlayit(url) {
            window.location.href = `playit://playerv2/video?url=${url}`;
        }

        function openKMPlayer(url) {
            window.location.href = `intent:${url}#Intent;package=com.kmplayer;end`;
        }
    </script>
</body>
</html>
