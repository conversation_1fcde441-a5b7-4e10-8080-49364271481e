name: cinepix_app
description: "CinePix - Watch & Download Movies and TV Shows"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.1.0+2

environment:
  sdk: '>=2.19.0 <3.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2

  # State management
  provider: ^6.0.5
  get: ^4.6.5

  # Network and API
  http: ^0.13.5
  dio: ^5.0.0
  cached_network_image: ^3.2.3

  # UI components
  flutter_rating_bar: ^4.0.1
  carousel_slider: ^5.0.0
  shimmer: ^2.0.0
  google_fonts: ^4.0.3
  flutter_spinkit: ^5.1.0

  # Video player - using media_kit instead
  # video_player: ^2.6.0
  # chewie: ^1.7.1

  # ExoPlayer support with media_kit
  media_kit: ^1.1.10                  # Primary package
  media_kit_video: ^1.2.4             # For video rendering
  media_kit_native_event_loop: ^1.0.8 # Support for higher number of concurrent instances
  media_kit_libs_android_video: ^1.3.6 # Android package with ExoPlayer backend

  # Storage
  shared_preferences: ^2.0.18
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Utils
  intl: ^0.18.0
  url_launcher: ^6.1.10
  path_provider: ^2.0.14
  permission_handler: ^10.2.0
  device_info_plus: ^9.0.2
  crypto: ^3.0.3
  screen_brightness: ^0.2.2
  # connectivity_plus: ^3.0.3

  # Download and file management
  sqflite: ^2.2.8
  path: ^1.8.3

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
