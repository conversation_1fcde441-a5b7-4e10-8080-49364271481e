<?php
// Database connection
$host = 'localhost';
$username = 'tipsbdxy_4525';
$password = '@mdsrabon13';
$database = 'tipsbdxy_4525';

$conn = mysqli_connect($host, $username, $password, $database);

if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// Check if premium_plans table exists
$check_table_query = "SHOW TABLES LIKE 'premium_plans'";
$check_table_result = mysqli_query($conn, $check_table_query);

if (mysqli_num_rows($check_table_result) == 0) {
    echo "<h2>The premium_plans table does not exist!</h2>";
    exit;
}

// Array of columns to check and add if missing
$columns_to_check = [
    [
        'name' => 'description',
        'definition' => 'TEXT AFTER name',
        'default_value' => "CONCAT(name, ' plan with ', duration, ' days validity')"
    ],
    [
        'name' => 'updated_at',
        'definition' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
        'default_value' => null
    ],
    [
        'name' => 'is_active',
        'definition' => 'BOOLEAN DEFAULT TRUE',
        'default_value' => '1'
    ]
];

$added_columns = [];

// Check and add missing columns
foreach ($columns_to_check as $column) {
    $check_column_query = "SHOW COLUMNS FROM premium_plans LIKE '{$column['name']}'";
    $check_column_result = mysqli_query($conn, $check_column_query);
    
    if (mysqli_num_rows($check_column_result) == 0) {
        // Column doesn't exist, add it
        $add_column_query = "ALTER TABLE premium_plans ADD COLUMN {$column['name']} {$column['definition']}";
        
        if (mysqli_query($conn, $add_column_query)) {
            $added_columns[] = $column['name'];
            
            // Update existing records with default value if provided
            if ($column['default_value'] !== null) {
                $update_query = "UPDATE premium_plans SET {$column['name']} = {$column['default_value']}";
                
                if (mysqli_query($conn, $update_query)) {
                    echo "<p>Updated existing plans with default {$column['name']} values.</p>";
                } else {
                    echo "<p>Error updating existing plans with {$column['name']}: " . mysqli_error($conn) . "</p>";
                }
            }
        } else {
            echo "<h3>Error adding '{$column['name']}' column: " . mysqli_error($conn) . "</h3>";
        }
    }
}

if (count($added_columns) > 0) {
    echo "<h2>Successfully added the following columns to premium_plans table:</h2>";
    echo "<ul>";
    foreach ($added_columns as $column) {
        echo "<li>{$column}</li>";
    }
    echo "</ul>";
} else {
    echo "<h2>All required columns already exist in the premium_plans table.</h2>";
}

// Show table structure
echo "<h3>Premium Plans Table Structure:</h3>";
$structure_query = "DESCRIBE premium_plans";
$structure_result = mysqli_query($conn, $structure_query);

if ($structure_result) {
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = mysqli_fetch_assoc($structure_result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
}

// Show existing plans
echo "<h3>Existing Premium Plans:</h3>";
$plans_query = "SELECT * FROM premium_plans ORDER BY price";
$plans_result = mysqli_query($conn, $plans_query);

if (mysqli_num_rows($plans_result) > 0) {
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    
    // Get column names
    $columns = [];
    $columns_result = mysqli_query($conn, "SHOW COLUMNS FROM premium_plans");
    while ($column = mysqli_fetch_assoc($columns_result)) {
        $columns[] = $column['Field'];
    }
    
    // Table header
    echo "<tr>";
    foreach ($columns as $column) {
        echo "<th>{$column}</th>";
    }
    echo "</tr>";
    
    // Table data
    while ($plan = mysqli_fetch_assoc($plans_result)) {
        echo "<tr>";
        foreach ($columns as $column) {
            if ($column == 'features') {
                echo "<td><pre>" . ($plan[$column] ?? 'N/A') . "</pre></td>";
            } else if ($column == 'price') {
                echo "<td>৳" . ($plan[$column] ?? 'N/A') . "</td>";
            } else if ($column == 'duration') {
                echo "<td>" . ($plan[$column] ?? 'N/A') . " days</td>";
            } else if ($column == 'is_active') {
                echo "<td>" . ($plan[$column] ? 'Yes' : 'No') . "</td>";
            } else {
                echo "<td>" . ($plan[$column] ?? 'N/A') . "</td>";
            }
        }
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>No premium plans found.</p>";
}

// Add a form to add a new plan
echo "<h3>Add New Premium Plan:</h3>";
?>

<form method="POST" action="">
    <table>
        <tr>
            <td>Name:</td>
            <td><input type="text" name="name" required></td>
        </tr>
        <tr>
            <td>Description:</td>
            <td><textarea name="description" rows="2" cols="40"></textarea></td>
        </tr>
        <tr>
            <td>Price (৳):</td>
            <td><input type="number" name="price" min="1" required></td>
        </tr>
        <tr>
            <td>Duration (days):</td>
            <td><input type="number" name="duration" min="1" required></td>
        </tr>
        <tr>
            <td>Features (one per line):</td>
            <td><textarea name="features" rows="5" cols="40"></textarea></td>
        </tr>
        <tr>
            <td>Active:</td>
            <td><input type="checkbox" name="is_active" checked></td>
        </tr>
        <tr>
            <td colspan="2"><input type="submit" value="Add Plan"></td>
        </tr>
    </table>
</form>

<?php
// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = mysqli_real_escape_string($conn, $_POST['name']);
    $description = mysqli_real_escape_string($conn, $_POST['description']);
    $price = (float)$_POST['price'];
    $duration = (int)$_POST['duration'];
    $features = mysqli_real_escape_string($conn, $_POST['features']);
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    // Insert the new plan
    $query = "INSERT INTO premium_plans (name, description, price, duration, features, is_active) 
              VALUES ('$name', '$description', $price, $duration, '$features', $is_active)";
    
    if (mysqli_query($conn, $query)) {
        echo "<p style='color:green;'>New premium plan added successfully!</p>";
        echo "<script>window.location.reload();</script>";
    } else {
        echo "<p style='color:red;'>Error adding premium plan: " . mysqli_error($conn) . "</p>";
    }
}

echo "<p><a href='admin/premium_plans.php'>Go to Premium Plans Admin Page</a></p>";

mysqli_close($conn);
?>
