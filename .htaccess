# SEO-Friendly URL Rewrite Rules for CinePix
RewriteEngine On

# Set base directory
RewriteBase /

# Force HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Remove www from domain
RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

# Movie Details SEO URLs
# /movie/title-year-id -> movie_details.php?id=id
RewriteRule ^movie/([a-zA-Z0-9\-]+)-([0-9]{4})-([0-9]+)/?$ movie_details.php?id=$3 [L,QSA]

# TV Show Details SEO URLs
# /tv-show/title-year-id -> tvshow_details.php?id=id
RewriteRule ^tv-show/([a-zA-Z0-9\-]+)-([0-9]{4})-([0-9]+)/?$ tvshow_details.php?id=$3 [L,QSA]

# TV Show with Season
# /tv-show/title-year-id/season/1 -> tvshow_details.php?id=id&season=1
RewriteRule ^tv-show/([a-zA-Z0-9\-]+)-([0-9]{4})-([0-9]+)/season/([0-9]+)/?$ tvshow_details.php?id=$3&season=$4 [L,QSA]

# Search URLs
# /search/avatar -> search.php?query=avatar
RewriteRule ^search/([^/]+)/?$ search.php?query=$1 [L,QSA]

# Download URLs
# /download/movie/title-year-id -> movie_details.php?id=id#download
RewriteRule ^download/movie/([a-zA-Z0-9\-]+)-([0-9]{4})-([0-9]+)/?$ movie_details.php?id=$3#download [L,QSA]

# Watch URLs
# /watch/movie/title-year-id -> movie_details.php?id=id#watch
RewriteRule ^watch/movie/([a-zA-Z0-9\-]+)-([0-9]{4})-([0-9]+)/?$ movie_details.php?id=$3#watch [L,QSA]
RewriteRule ^watch/tv-show/([a-zA-Z0-9\-]+)-([0-9]{4})-([0-9]+)/?$ tvshow_details.php?id=$3#watch [L,QSA]

# Year-based URLs
# /movies/2024 -> movies.php?year=2024
RewriteRule ^movies/([0-9]{4})/?$ movies.php?year=$1 [L,QSA]
RewriteRule ^tv-shows/([0-9]{4})/?$ tvshows.php?year=$1 [L,QSA]

# Quality-based URLs
# /movies/720p -> movies.php?quality=720p
RewriteRule ^movies/(720p|1080p|4k)/?$ movies.php?quality=$1 [L,QSA]

# Language-based URLs
# /movies/(bengali|hindi|english) -> movies.php?language=language
RewriteRule ^movies/(bengali|hindi|english)/?$ movies.php?language=$1 [L,QSA]

# Share Link URLs
# /share/token -> share.php?token=token
RewriteRule ^share/([a-zA-Z0-9_]+)/?$ share.php?token=$1 [L,QSA]

# Handle 404 errors
ErrorDocument 404 /404.php

# Default fallback (keep at end)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/(api|admin)/
RewriteRule ^(.*)$ index.php [QSA,L]

# Prevent directory listing
Options -Indexes

# Set default character set
AddDefaultCharset UTF-8

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json
</IfModule>

# Set CSS MIME type explicitly
AddType text/css .css

# Set caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# Protect sensitive files
<FilesMatch "^\.ht">
    Order allow,deny
    Deny from all
</FilesMatch>

<FilesMatch "^(config\.php|database\.sql)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# PHP settings
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_time 300
