import 'dart:convert';
import 'package:flutter/foundation.dart' hide Category;
import 'package:http/http.dart' as http;
import 'package:cinepix_app/constants/api_constants.dart';
import 'package:cinepix_app/models/movie.dart';
import 'package:cinepix_app/models/tv_show.dart';
import 'package:cinepix_app/models/episode.dart';
import 'package:cinepix_app/models/download_link.dart';
import 'package:cinepix_app/models/category.dart';
import 'package:cinepix_app/models/user.dart';
import 'package:cinepix_app/services/storage_service.dart';

class ApiService {
  final http.Client _client = http.Client();
  final StorageService _storageService = StorageService();

  // Helper method to get headers
  Future<Map<String, String>> _getHeaders() async {
    final token = await _storageService.getToken();
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  // Helper method to handle API response
  dynamic _handleResponse(http.Response response) {
    debugPrint('API Response Status: ${response.statusCode}');

    if (response.statusCode >= 200 && response.statusCode < 300) {
      final data = json.decode(response.body);
      return data;
    } else {
      debugPrint('API Error: ${response.statusCode} - ${response.body}');
      throw Exception('API Error: ${response.statusCode} - ${response.body}');
    }
  }

  // Get app configuration
  Future<Map<String, dynamic>> getAppConfig() async {
    final response = await _client.get(
      Uri.parse('${ApiConstants.baseUrl}${ApiConstants.configEndpoint}'),
      headers: await _getHeaders(),
    );

    final data = _handleResponse(response);
    return data['data'];
  }

  // Login user
  Future<Map<String, dynamic>> login(String username, String password) async {
    final response = await _client.post(
      Uri.parse('${ApiConstants.baseUrl}${ApiConstants.loginEndpoint}'),
      headers: await _getHeaders(),
      body: json.encode({
        'username': username,
        'password': password,
      }),
    );

    final data = _handleResponse(response);

    if (data['success'] == true) {
      final userData = data['data']['user'];
      final token = data['data']['token'];

      // Save user data and token
      await _storageService.saveToken(token);
      await _storageService.saveUser(User.fromJson(userData));

      return data['data'];
    } else {
      throw Exception(data['message'] ?? 'Login failed');
    }
  }

  // Register user
  Future<Map<String, dynamic>> register(
      String username, String email, String password, String name) async {
    debugPrint('Registering user: $username, $email, $name');

    final url = '${ApiConstants.baseUrl}${ApiConstants.registerEndpoint}';
    debugPrint('Registration URL: $url');

    final headers = await _getHeaders();
    debugPrint('Registration headers: $headers');

    final body = json.encode({
      'username': username,
      'email': email,
      'password': password,
      'name': name,
    });
    debugPrint('Registration body: $body');

    try {
      final response = await _client.post(
        Uri.parse(url),
        headers: headers,
        body: body,
      );

      debugPrint('Registration response status: ${response.statusCode}');
      debugPrint('Registration response body: ${response.body}');

      return _handleResponse(response);
    } catch (e) {
      debugPrint('Registration error: $e');
      rethrow;
    }
  }

  // Get movies list
  Future<List<Movie>> getMovies(
      {int page = 1,
      int limit = 20,
      int categoryId = 0,
      bool featured = false,
      String? sortBy}) async {
    final url =
        '${ApiConstants.baseUrl}${ApiConstants.moviesEndpoint}?page=$page&limit=$limit${categoryId > 0 ? '&category_id=$categoryId' : ''}${featured ? '&featured=1' : ''}${sortBy != null ? '&sort_by=$sortBy' : ''}';
    debugPrint('Fetching movies from URL: $url');

    try {
      final headers = await _getHeaders();

      final response = await _client.get(
        Uri.parse(url),
        headers: headers,
      );

      final data = _handleResponse(response);

      if (data['success'] == true &&
          data['data'] != null &&
          data['data']['movies'] != null) {
        final moviesList = data['data']['movies'] as List;
        debugPrint('Movies count: ${moviesList.length}');
        return moviesList
            .map((movieJson) => Movie.fromJson(movieJson))
            .toList();
      } else {
        debugPrint('No movies found in response or invalid response format');
        return [];
      }
    } catch (e) {
      debugPrint('Error fetching movies: $e');
      return [];
    }
  }

  // Get movie details
  Future<Map<String, dynamic>> getMovieDetails(int movieId) async {
    final response = await _client.get(
      Uri.parse(
          '${ApiConstants.baseUrl}${ApiConstants.movieDetailsEndpoint}?id=$movieId'),
      headers: await _getHeaders(),
    );

    final data = _handleResponse(response);

    if (data['success'] == true && data['data'] != null) {
      final movieData = data['data'];
      final movie = Movie.fromJson(movieData['movie']);

      final downloadLinks = (movieData['download_links'] as List?)
              ?.map((linkJson) => DownloadLink.fromJson(linkJson))
              .toList() ??
          [];

      final relatedMovies = (movieData['related_movies'] as List?)
              ?.map((movieJson) => Movie.fromJson(movieJson))
              .toList() ??
          [];

      return {
        'movie': movie,
        'download_links': downloadLinks,
        'related_movies': relatedMovies,
      };
    } else {
      throw Exception(data['message'] ?? 'Failed to get movie details');
    }
  }

  // Get TV shows list
  Future<List<TvShow>> getTvShows(
      {int page = 1,
      int limit = 20,
      int categoryId = 0,
      bool featured = false,
      String? sortBy}) async {
    final response = await _client.get(
      Uri.parse(
          '${ApiConstants.baseUrl}${ApiConstants.tvShowsEndpoint}?page=$page&limit=$limit${categoryId > 0 ? '&category_id=$categoryId' : ''}${featured ? '&featured=1' : ''}${sortBy != null ? '&sort_by=$sortBy' : ''}'),
      headers: await _getHeaders(),
    );

    final data = _handleResponse(response);

    if (data['success'] == true && data['data']['tvshows'] != null) {
      return (data['data']['tvshows'] as List)
          .map((tvShowJson) => TvShow.fromJson(tvShowJson))
          .toList();
    } else {
      return [];
    }
  }

  // Get TV show details
  Future<Map<String, dynamic>> getTvShowDetails(int tvShowId) async {
    final response = await _client.get(
      Uri.parse(
          '${ApiConstants.baseUrl}${ApiConstants.tvShowDetailsEndpoint}?id=$tvShowId'),
      headers: await _getHeaders(),
    );

    final data = _handleResponse(response);

    if (data['success'] == true && data['data'] != null) {
      final tvShowData = data['data'];
      final tvShow = TvShow.fromJson(tvShowData['tvshow']);

      final seasons = tvShowData['seasons'] as List? ?? [];

      final relatedTvShows = (tvShowData['related_tvshows'] as List?)
              ?.map((tvShowJson) => TvShow.fromJson(tvShowJson))
              .toList() ??
          [];

      return {
        'tvshow': tvShow,
        'seasons': seasons,
        'related_tvshows': relatedTvShows,
      };
    } else {
      throw Exception(data['message'] ?? 'Failed to get TV show details');
    }
  }

  // Get TV show episodes
  Future<List<Episode>> getTvShowEpisodes(
      int tvShowId, int seasonNumber) async {
    final response = await _client.get(
      Uri.parse(
          '${ApiConstants.baseUrl}${ApiConstants.tvShowEpisodesEndpoint}?id=$tvShowId&season=$seasonNumber'),
      headers: await _getHeaders(),
    );

    final data = _handleResponse(response);

    if (data['success'] == true && data['data']['episodes'] != null) {
      return (data['data']['episodes'] as List)
          .map((episodeJson) => Episode.fromJson(episodeJson))
          .toList();
    } else {
      return [];
    }
  }

  // Get episode download links
  Future<List<DownloadLink>> getEpisodeDownloadLinks(int episodeId) async {
    final response = await _client.get(
      Uri.parse(
          '${ApiConstants.baseUrl}${ApiConstants.episodeLinksEndpoint}?episode_id=$episodeId'),
      headers: await _getHeaders(),
    );

    final data = _handleResponse(response);

    if (data['success'] == true && data['data']['download_links'] != null) {
      return (data['data']['download_links'] as List)
          .map((linkJson) => DownloadLink.fromJson(linkJson))
          .toList();
    } else {
      return [];
    }
  }

  // Search movies and TV shows
  Future<Map<String, List>> search(String query) async {
    debugPrint('Searching for: $query');

    try {
      // Direct API URL for testing
      final directUrl =
          '${ApiConstants.baseUrl}${ApiConstants.searchEndpoint}?q=${Uri.encodeComponent(query)}';
      debugPrint('Search URL: $directUrl');

      // For debugging, try to fetch the URL directly
      try {
        final testResponse = await http.get(Uri.parse(directUrl));
        debugPrint('Direct test response status: ${testResponse.statusCode}');
        if (testResponse.body.isNotEmpty) {
          debugPrint(
              'Direct test response preview: ${testResponse.body.substring(0, testResponse.body.length > 100 ? 100 : testResponse.body.length)}...');
        }
      } catch (e) {
        debugPrint('Direct test request failed: $e');
      }

      final headers = await _getHeaders();
      debugPrint('Search headers: $headers');

      // Print full request details for debugging
      debugPrint('Making GET request to: $directUrl');

      final response = await _client
          .get(
            Uri.parse(directUrl),
            headers: headers,
          )
          .timeout(const Duration(seconds: 30)); // Add timeout

      debugPrint('Search response status: ${response.statusCode}');

      if (response.body.isNotEmpty) {
        debugPrint(
            'Search response body preview: ${response.body.substring(0, response.body.length > 100 ? 100 : response.body.length)}...');
      } else {
        debugPrint('Search response body is empty');
      }

      if (response.statusCode >= 200 && response.statusCode < 300) {
        try {
          final data = json.decode(response.body);
          debugPrint('Search response decoded successfully');

          if (data['success'] == true && data['data'] != null) {
            final searchData = data['data'];

            List movies = [];
            if (searchData.containsKey('movies') &&
                searchData['movies'] is List) {
              movies = searchData['movies'];
              debugPrint('Found ${movies.length} movies');
            } else {
              debugPrint('No movies found or invalid format');
            }

            List tvShows = [];
            if (searchData.containsKey('tvshows') &&
                searchData['tvshows'] is List) {
              tvShows = searchData['tvshows'];
              debugPrint('Found ${tvShows.length} TV shows');
            } else {
              debugPrint('No TV shows found or invalid format');
            }

            return {
              'movies': movies,
              'tvshows': tvShows,
            };
          } else {
            debugPrint('API returned success=false or data=null: $data');
          }
        } catch (e) {
          debugPrint('Error decoding JSON: $e');
        }
      } else {
        debugPrint(
            'Search API error: ${response.statusCode} - ${response.body}');
      }
    } catch (e, stackTrace) {
      debugPrint('Search exception: $e');
      debugPrint('Stack trace: $stackTrace');
    }

    // Return empty results in case of any error
    return {
      'movies': [],
      'tvshows': [],
    };
  }

  // Get categories
  Future<List<Category>> getCategories() async {
    final response = await _client.get(
      Uri.parse('${ApiConstants.baseUrl}${ApiConstants.categoriesEndpoint}'),
      headers: await _getHeaders(),
    );

    final data = _handleResponse(response);

    if (data['success'] == true && data['data']['categories'] != null) {
      return (data['data']['categories'] as List)
          .map((categoryJson) => Category.fromJson(categoryJson))
          .toList();
    } else {
      return [];
    }
  }

  // Get user profile
  Future<User> getUserProfile() async {
    final response = await _client.get(
      Uri.parse('${ApiConstants.baseUrl}${ApiConstants.profileEndpoint}'),
      headers: await _getHeaders(),
    );

    final data = _handleResponse(response);

    if (data['success'] == true && data['data']['user'] != null) {
      return User.fromJson(data['data']['user']);
    } else {
      throw Exception(data['message'] ?? 'Failed to get user profile');
    }
  }

  // Logout
  Future<void> logout() async {
    await _storageService.clearAll();
  }

  // Get reviews for a content
  Future<List<dynamic>> getReviews(String contentType, int contentId) async {
    final response = await _client.get(
      Uri.parse(
          '${ApiConstants.baseUrl}${ApiConstants.reviewsEndpoint}?content_type=$contentType&content_id=$contentId'),
      headers: await _getHeaders(),
    );

    final data = _handleResponse(response);

    if (data['success'] == true && data['data']['reviews'] != null) {
      return data['data']['reviews'] as List;
    } else {
      return [];
    }
  }

  // Submit a review
  Future<Map<String, dynamic>> submitReview(
      String contentType, int contentId, int rating, String comment) async {
    final response = await _client.post(
      Uri.parse('${ApiConstants.baseUrl}${ApiConstants.reviewsEndpoint}'),
      headers: await _getHeaders(),
      body: json.encode({
        'content_type': contentType,
        'content_id': contentId,
        'rating': rating,
        'comment': comment,
      }),
    );

    return _handleResponse(response);
  }
}
