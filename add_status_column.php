<?php
// Database connection
$db_host = 'localhost';
$db_user = 'tipsbdxy_4525';
$db_pass = 'tipsbdxy_4525';
$db_name = 'tipsbdxy_4525';

// Create connection
$conn = mysqli_connect($db_host, $db_user, $db_pass, $db_name);

// Check connection
if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// SQL commands to execute
$sql_commands = [
    "ALTER TABLE movies ADD COLUMN IF NOT EXISTS status ENUM('active', 'inactive') DEFAULT 'active'",
    "UPDATE movies SET status = 'active' WHERE status IS NULL",
    "ALTER TABLE tvshows ADD COLUMN IF NOT EXISTS status ENUM('active', 'inactive') DEFAULT 'active'",
    "UPDATE tvshows SET status = 'active' WHERE status IS NULL"
];

// Execute each SQL command
$success = true;
$messages = [];

foreach ($sql_commands as $sql) {
    if (mysqli_query($conn, $sql)) {
        $messages[] = "Success: " . $sql;
    } else {
        $success = false;
        $messages[] = "Error: " . $sql . " - " . mysqli_error($conn);
    }
}

// Output results
if ($success) {
    echo "All database updates completed successfully:\n";
} else {
    echo "Some database updates failed:\n";
}

foreach ($messages as $message) {
    echo "- " . $message . "\n";
}

// Close connection
mysqli_close($conn);
?>
