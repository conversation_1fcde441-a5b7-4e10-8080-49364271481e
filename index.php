<?php
require_once 'includes/header.php';
require_once 'includes/ad_placeholder.php';

// Top 10 Movies Query
$top_movies_query = "SELECT m.*, c.name as category_name,
    GROUP_CONCAT(t.name ORDER BY t.name SEPARATOR ',') as tags,
    GROUP_CONCAT(t.id ORDER BY t.name SEPARATOR ',') as tag_ids
    FROM movies m
    LEFT JOIN categories c ON m.category_id = c.id
    LEFT JOIN movie_tags mt ON m.id = mt.movie_id
    LEFT JOIN tags t ON mt.tag_id = t.id
    GROUP BY m.id
    ORDER BY m.rating DESC
    LIMIT 10";
$top_movies_result = mysqli_query($conn, $top_movies_query);

// Top 10 TV Shows Query
$top_tvshows_query = "SELECT t.*, c.name as category_name,
    GROUP_CONCAT(tg.name ORDER BY tg.name SEPARATOR ',') as tags,
    GROUP_CONCAT(tg.id ORDER BY tg.name SEPARATOR ',') as tag_ids
    FROM tvshows t
    LEFT JOIN categories c ON t.category_id = c.id
    LEFT JOIN tvshow_tags tt ON t.id = tt.tvshow_id
    LEFT JOIN tags tg ON tt.tag_id = tg.id
    GROUP BY t.id
    ORDER BY t.rating DESC
    LIMIT 10";
$top_tvshows_result = mysqli_query($conn, $top_tvshows_query);

?>
<!-- Bootstrap 5 CDN -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">
<link href="assets/css/custom.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
<body class="theme-light">
<!-- আগের header.php থেকেই হেডার আসবে, এখানে নতুন কোনো <nav> কোড থাকবে না -->

<!-- Offcanvas Mobile Menu -->
<div class="offcanvas offcanvas-start" tabindex="-1" id="mobileMenu" aria-labelledby="mobileMenuLabel">
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="mobileMenuLabel">মেনু</h5>
    <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
  </div>
  <div class="offcanvas-body">
    <ul class="navbar-nav">
      <li class="nav-item"><a class="nav-link active" href="/">হোম</a></li>
      <li class="nav-item"><a class="nav-link" href="/movies.php">মুভি</a></li>
      <li class="nav-item"><a class="nav-link" href="/tvshows.php">টিভি শো</a></li>
      <li class="nav-item"><a class="nav-link" href="/premium.php">প্রিমিয়াম</a></li>
      <li class="nav-item"><a class="nav-link" href="/contact.php">যোগাযোগ</a></li>
    </ul>
    <button class="btn btn-outline-dark mt-3 w-100" id="themeToggleMobile"><i class="fa-solid fa-moon"></i> থিম পরিবর্তন</button>
  </div>
</div>

<!-- Main Slider Section (Swiper) -->
<section class="main-slider py-3">
  <div class="container">
    <div class="swiper mainSwiper">
      <div class="swiper-wrapper">
        <?php
        $slider_query = "SELECT
                            'movie' as content_type,
                            m.id,
                            m.title,
                            m.description,
                            m.release_year as year,
                            m.duration,
                            m.banner,
                            m.poster,
                            m.rating,
                            m.premium_only,
                            c.name as category_name
                        FROM movies m
                        LEFT JOIN categories c ON m.category_id = c.id
                        WHERE m.featured = 1 OR m.rating >= 8.0
                        UNION
                        SELECT
                            'tvshow' as content_type,
                            t.id,
                            t.title,
                            t.description,
                            t.start_year as year,
                            NULL as duration,
                            t.banner,
                            t.poster,
                            t.rating,
                            t.premium_only,
                            c.name as category_name
                        FROM tvshows t
                        LEFT JOIN categories c ON t.category_id = c.id
                        WHERE t.featured = 1 OR t.rating >= 8.0
                        ORDER BY year DESC
                        LIMIT 5";
        $slider_result = mysqli_query($conn, $slider_query);
        while($content = mysqli_fetch_assoc($slider_result)):
            $banner_image = !empty($content['banner']) && file_exists('uploads/' . $content['banner']) ? $content['banner'] : 'movie_banner_564147_1743844083.jpg';
            $content_type = $content['content_type'];
            $content_id = $content['id'];
        ?>
        <div class="swiper-slide">
          <div class="slide-image position-relative" style="background-image: linear-gradient(120deg,rgba(26,26,46,0.7),rgba(22,33,62,0.7)), url('uploads/<?php echo $banner_image; ?>'); min-height: 340px; background-size: cover; background-position: center; border-radius: 1.5rem;">
            <div class="slide-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center" style="background:rgba(0,0,0,0.25); border-radius: 1.5rem;">
              <div class="container">
                <div class="row align-items-center">
                  <div class="col-lg-7 col-md-10">
                    <div class="slide-content text-white">
                      <div class="d-flex align-items-center mb-2">
                        <?php if($content['premium_only']): ?>
                        <span class="badge bg-warning text-dark me-2 fs-6"><i class="fa-solid fa-crown"></i> প্রিমিয়াম</span>
                        <?php endif; ?>
                        <span class="badge <?php echo $content_type=='movie'?'bg-danger':'bg-primary'; ?> ms-1 fs-6"><i class="fa-solid <?php echo $content_type=='movie'?'fa-film':'fa-tv'; ?> me-1"></i> <?php echo $content_type=='movie'?'মুভি':'টিভি শো'; ?></span>
                      </div>
                      <h2 class="slide-title fw-bold mb-2 display-5 text-shadow-lg"><?php echo htmlspecialchars($content['title']); ?></h2>
                      <div class="slide-meta mb-2">
                        <span><i class="fa-solid fa-calendar"></i> <?php echo $content['year']; ?></span>
                        <?php if($content_type == 'movie' && !empty($content['duration'])): ?>
                        <span class="ms-2"><i class="fa-solid fa-clock"></i> <?php echo $content['duration']; ?> মিনিট</span>
                        <?php endif; ?>
                        <span class="ms-2"><i class="fa-solid fa-star"></i> <?php echo number_format($content['rating'], 1); ?></span>
                        <span class="ms-2"><i class="fa-solid fa-tag"></i> <?php echo htmlspecialchars($content['category_name']); ?></span>
                      </div>
                      <p class="slide-description mb-3 d-none d-md-block fs-5 text-shadow-sm"><?php echo htmlspecialchars(substr($content['description'], 0, 180)); ?>...</p>
                      <div class="slide-buttons">
                        <?php if($content_type == 'movie'): ?>
                        <a href="movie_details.php?id=<?php echo $content_id; ?>" class="btn btn-danger btn-lg me-2 px-4 py-2 shadow"><i class="fa-solid fa-play me-2"></i> এখনই দেখুন</a>
                        <a href="movie_details.php?id=<?php echo $content_id; ?>#download" class="btn btn-outline-light btn-lg px-4 py-2 shadow"><i class="fa-solid fa-download me-2"></i> ডাউনলোড</a>
                        <?php else: ?>
                        <a href="tvshow_details.php?id=<?php echo $content_id; ?>" class="btn btn-primary btn-lg me-2 px-4 py-2 shadow"><i class="fa-solid fa-play me-2"></i> এখনই দেখুন</a>
                        <a href="tvshow_details.php?id=<?php echo $content_id; ?>#download" class="btn btn-outline-light btn-lg px-4 py-2 shadow"><i class="fa-solid fa-download me-2"></i> ডাউনলোড</a>
                        <?php endif; ?>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <?php endwhile; ?>
      </div>
      <!-- Pagination & Navigation -->
      <div class="swiper-pagination"></div>
      <div class="swiper-button-prev"></div>
      <div class="swiper-button-next"></div>
    </div>
  </div>
</section>
<!-- Top Banner Ad (as before) -->
<?php if (!(isLoggedIn() && isPremium())): ?>
<div class="container my-4">
    <div class="ad-container ad-banner">
        <script type="text/javascript">
            atOptions = {
                'key' : '735a559a5872816da47237a603cac4ad',
                'format' : 'iframe',
                'height' : 90,
                'width' : 728,
                'params' : {}
            };
        </script>
        <script type="text/javascript" src="//www.highperformanceformat.com/735a559a5872816da47237a603cac4ad/invoke.js"></script>
    </div>
</div>
<?php endif; ?>

<!-- Top 10 Movies Section -->
<section class="py-5">
  <div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2 class="section-title mb-0"><i class="fas fa-trophy text-warning me-2"></i>সপ্তাহের সেরা ১০ মুভি</h2>
    </div>
    <div class="row g-4">
      <?php $counter = 1; while($movie = mysqli_fetch_assoc($top_movies_result)): ?>
      <div class="col-6 col-md-4 col-lg-2">
        <div class="movie-card card h-100 shadow-lg position-relative">
          <span class="top-10-rank badge bg-gradient position-absolute top-0 start-0 m-2 fs-5">#<?php echo $counter; ?></span>
          <?php if($movie['premium_only']): ?>
          <span class="badge bg-warning text-dark position-absolute top-0 end-0 m-2"><i class="fa-solid fa-crown"></i> প্রিমিয়াম</span>
          <?php endif; ?>
          <img src="uploads/<?php echo $movie['poster']; ?>" class="card-img-top" alt="<?php echo htmlspecialchars($movie['title']); ?>">
          <div class="card-body d-flex flex-column">
            <h5 class="card-title text-truncate"><?php echo htmlspecialchars($movie['title']); ?></h5>
            <div class="d-flex justify-content-between align-items-center mb-2">
              <span class="badge bg-danger"><i class="fa-solid fa-film me-1"></i> মুভি</span>
              <span class="badge bg-dark"><i class="fa-solid fa-star"></i> <?php echo number_format($movie['rating'], 1); ?></span>
            </div>
            <p class="card-text small text-muted mb-1"><?php echo $movie['release_year']; ?> • <?php echo htmlspecialchars($movie['category_name']); ?></p>
            <div class="mt-auto">
              <a href="movie_details.php?id=<?php echo $movie['id']; ?>" class="btn btn-danger btn-sm w-100">এখনই দেখুন</a>
            </div>
          </div>
        </div>
      </div>
      <?php $counter++; endwhile; ?>
    </div>
  </div>
</section>
<!-- Inline Ad After Top Movies -->
<?php if (!(isLoggedIn() && isPremium())): ?>
<div class="container my-4">
    <div class="ad-container ad-inline">
        <script type="text/javascript">
            atOptions = {
                'key' : 'ceac305b755cbace9181e4d593e3700b',
                'format' : 'iframe',
                'height' : 60,
                'width' : 468,
                'params' : {}
            };
        </script>
        <script type="text/javascript" src="//www.highperformanceformat.com/ceac305b755cbace9181e4d593e3700b/invoke.js"></script>
    </div>
</div>
<?php endif; ?>

<!-- Top 10 TV Shows Section -->
<section class="py-5 bg-dark text-light">
  <div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2 class="section-title mb-0"><i class="fas fa-tv text-primary me-2"></i>সপ্তাহের সেরা ১০ টিভি শো</h2>
    </div>
    <div class="row g-4">
      <?php $counter = 1; while($tvshow = mysqli_fetch_assoc($top_tvshows_result)): ?>
      <div class="col-6 col-md-4 col-lg-2">
        <div class="movie-card card h-100 shadow-lg bg-secondary text-light position-relative">
          <span class="top-10-rank badge bg-gradient position-absolute top-0 start-0 m-2 fs-5">#<?php echo $counter; ?></span>
          <?php if($tvshow['premium_only']): ?>
          <span class="badge bg-warning text-dark position-absolute top-0 end-0 m-2"><i class="fa-solid fa-crown"></i> প্রিমিয়াম</span>
          <?php endif; ?>
          <img src="uploads/<?php echo $tvshow['poster']; ?>" class="card-img-top" alt="<?php echo htmlspecialchars($tvshow['title']); ?>">
          <div class="card-body d-flex flex-column">
            <h5 class="card-title text-truncate"><?php echo htmlspecialchars($tvshow['title']); ?></h5>
            <div class="d-flex justify-content-between align-items-center mb-2">
              <span class="badge bg-primary"><i class="fa-solid fa-tv me-1"></i> টিভি শো</span>
              <span class="badge bg-dark"><i class="fa-solid fa-star"></i> <?php echo number_format($tvshow['rating'], 1); ?></span>
            </div>
            <p class="card-text small text-light mb-1"><?php echo $tvshow['start_year']; ?><?php echo $tvshow['end_year'] ? ' - ' . $tvshow['end_year'] : ' - Present'; ?> • <?php echo htmlspecialchars($tvshow['category_name']); ?></p>
            <div class="mt-auto">
              <a href="tvshow_details.php?id=<?php echo $tvshow['id']; ?>" class="btn btn-primary btn-sm w-100">এখনই দেখুন</a>
            </div>
          </div>
        </div>
      </div>
      <?php $counter++; endwhile; ?>
    </div>
  </div>
</section>
<!-- Sidebar Ad Section -->
<?php if (!(isLoggedIn() && isPremium())): ?>
<div class="container my-4">
    <div class="row">
        <div class="col-lg-8">
            <div class="ad-container ad-inline">
                <script type="text/javascript">
                    atOptions = {
                        'key' : '735a559a5872816da47237a603cac4ad',
                        'format' : 'iframe',
                        'height' : 90,
                        'width' : 728,
                        'params' : {}
                    };
                </script>
                <script type="text/javascript" src="//www.highperformanceformat.com/735a559a5872816da47237a603cac4ad/invoke.js"></script>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="ad-container ad-sidebar">
                <script type="text/javascript">
                    atOptions = {
                        'key' : '7d3b7accac0194a88ccf420c241ec7aa',
                        'format' : 'iframe',
                        'height' : 250,
                        'width' : 300,
                        'params' : {}
                    };
                </script>
                <script type="text/javascript" src="//www.highperformanceformat.com/7d3b7accac0194a88ccf420c241ec7aa/invoke.js"></script>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Featured Movies Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2 class="section-title mb-0"><i class="fa-solid fa-star text-warning me-2"></i>ফিচার্ড মুভি</h2>
      <a href="/movies.php" class="btn btn-outline-danger">সব দেখুন <i class="fa-solid fa-arrow-right ms-1"></i></a>
    </div>
    <div class="row g-4">
      <?php
      $featured_movies_query = "SELECT * FROM movies WHERE featured = 1 ORDER BY release_year DESC LIMIT 6";
      $featured_movies_result = mysqli_query($conn, $featured_movies_query);
      while($movie = mysqli_fetch_assoc($featured_movies_result)):
      ?>
      <div class="col-6 col-md-4 col-lg-2">
        <div class="movie-card card h-100 shadow-sm">
          <div class="position-relative">
            <img src="uploads/<?php echo $movie['poster']; ?>" class="card-img-top" alt="<?php echo htmlspecialchars($movie['title']); ?>">
            <?php if($movie['premium_only']): ?>
            <span class="badge bg-warning text-dark position-absolute top-0 end-0 m-2"><i class="fa-solid fa-crown"></i> প্রিমিয়াম</span>
            <?php endif; ?>
          </div>
          <div class="card-body d-flex flex-column">
            <h5 class="card-title text-truncate"><?php echo htmlspecialchars($movie['title']); ?></h5>
            <p class="card-text small text-muted mb-1"><?php echo $movie['release_year']; ?> • <?php echo htmlspecialchars($movie['category_id']); ?></p>
            <div class="mt-auto">
              <a href="movie_details.php?id=<?php echo $movie['id']; ?>" class="btn btn-danger btn-sm w-100">এখনই দেখুন</a>
            </div>
          </div>
        </div>
      </div>
      <?php endwhile; ?>
    </div>
  </div>
</section>

<!-- Featured TV Shows Section -->
<section class="py-5 bg-dark text-light">
  <div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2 class="section-title mb-0"><i class="fa-solid fa-tv text-primary me-2"></i>ফিচার্ড টিভি শো</h2>
      <a href="/tvshows.php" class="btn btn-outline-light">সব দেখুন <i class="fa-solid fa-arrow-right ms-1"></i></a>
    </div>
    <div class="row g-4">
      <?php
      $featured_tvshows_query = "SELECT * FROM tvshows WHERE featured = 1 ORDER BY start_year DESC LIMIT 6";
      $featured_tvshows_result = mysqli_query($conn, $featured_tvshows_query);
      while($tvshow = mysqli_fetch_assoc($featured_tvshows_result)):
      ?>
      <div class="col-6 col-md-4 col-lg-2">
        <div class="movie-card card h-100 shadow-sm bg-secondary text-light">
          <div class="position-relative">
            <img src="uploads/<?php echo $tvshow['poster']; ?>" class="card-img-top" alt="<?php echo htmlspecialchars($tvshow['title']); ?>">
            <?php if($tvshow['premium_only']): ?>
            <span class="badge bg-warning text-dark position-absolute top-0 end-0 m-2"><i class="fa-solid fa-crown"></i> প্রিমিয়াম</span>
            <?php endif; ?>
          </div>
          <div class="card-body d-flex flex-column">
            <h5 class="card-title text-truncate"><?php echo htmlspecialchars($tvshow['title']); ?></h5>
            <p class="card-text small text-light mb-1"><?php echo $tvshow['start_year']; ?> • <?php echo htmlspecialchars($tvshow['category_id']); ?></p>
            <div class="mt-auto">
              <a href="tvshow_details.php?id=<?php echo $tvshow['id']; ?>" class="btn btn-primary btn-sm w-100">এখনই দেখুন</a>
            </div>
          </div>
        </div>
      </div>
      <?php endwhile; ?>
    </div>
  </div>
</section>

<!-- Middle Ad Section -->
<?php if (!(isLoggedIn() && isPremium())): ?>
<div class="container my-5">
    <div class="ad-container ad-native">
       <script type="text/javascript">
	atOptions = {
		'key' : '735a559a5872816da47237a603cac4ad',
		'format' : 'iframe',
		'height' : 90,
		'width' : 728,
		'params' : {}
	};
</script>
<script type="text/javascript" src="//www.highperformanceformat.com/735a559a5872816da47237a603cac4ad/invoke.js"></script>
    </div>
</div>
<?php endif; ?>

<!-- Latest Movies Section -->
<section class="py-5">
  <div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2 class="section-title mb-0"><i class="fa-solid fa-clock text-info me-2"></i>সর্বশেষ মুভি</h2>
      <a href="/movies.php" class="btn btn-outline-danger">সব দেখুন <i class="fa-solid fa-arrow-right ms-1"></i></a>
    </div>
    <div class="row g-4">
      <?php
      $latest_movies_query = "SELECT * FROM movies ORDER BY created_at DESC LIMIT 12";
      $latest_movies_result = mysqli_query($conn, $latest_movies_query);
      while($movie = mysqli_fetch_assoc($latest_movies_result)):
      ?>
      <div class="col-6 col-md-4 col-lg-2">
        <div class="movie-card card h-100 shadow-sm">
          <div class="position-relative">
            <img src="uploads/<?php echo $movie['poster']; ?>" class="card-img-top" alt="<?php echo htmlspecialchars($movie['title']); ?>">
            <?php if($movie['premium_only']): ?>
            <span class="badge bg-warning text-dark position-absolute top-0 end-0 m-2"><i class="fa-solid fa-crown"></i> প্রিমিয়াম</span>
            <?php endif; ?>
          </div>
          <div class="card-body d-flex flex-column">
            <h5 class="card-title text-truncate"><?php echo htmlspecialchars($movie['title']); ?></h5>
            <p class="card-text small text-muted mb-1"><?php echo $movie['release_year']; ?> • <?php echo htmlspecialchars($movie['category_id']); ?></p>
            <div class="mt-auto">
              <a href="movie_details.php?id=<?php echo $movie['id']; ?>" class="btn btn-danger btn-sm w-100">এখনই দেখুন</a>
            </div>
          </div>
        </div>
      </div>
      <?php endwhile; ?>
    </div>
  </div>
</section>

<!-- Latest TV Shows Section -->
<section class="py-5 bg-dark text-light">
  <div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2 class="section-title mb-0"><i class="fa-solid fa-clock text-info me-2"></i>সর্বশেষ টিভি শো</h2>
      <a href="/tvshows.php" class="btn btn-outline-light">সব দেখুন <i class="fa-solid fa-arrow-right ms-1"></i></a>
    </div>
    <div class="row g-4">
      <?php
      $latest_tvshows_query = "SELECT * FROM tvshows ORDER BY created_at DESC LIMIT 12";
      $latest_tvshows_result = mysqli_query($conn, $latest_tvshows_query);
      while($tvshow = mysqli_fetch_assoc($latest_tvshows_result)):
      ?>
      <div class="col-6 col-md-4 col-lg-2">
        <div class="movie-card card h-100 shadow-sm bg-secondary text-light">
          <div class="position-relative">
            <img src="uploads/<?php echo $tvshow['poster']; ?>" class="card-img-top" alt="<?php echo htmlspecialchars($tvshow['title']); ?>">
            <?php if($tvshow['premium_only']): ?>
            <span class="badge bg-warning text-dark position-absolute top-0 end-0 m-2"><i class="fa-solid fa-crown"></i> প্রিমিয়াম</span>
            <?php endif; ?>
          </div>
          <div class="card-body d-flex flex-column">
            <h5 class="card-title text-truncate"><?php echo htmlspecialchars($tvshow['title']); ?></h5>
            <p class="card-text small text-light mb-1"><?php echo $tvshow['start_year']; ?> • <?php echo htmlspecialchars($tvshow['category_id']); ?></p>
            <div class="mt-auto">
              <a href="tvshow_details.php?id=<?php echo $tvshow['id']; ?>" class="btn btn-primary btn-sm w-100">এখনই দেখুন</a>
            </div>
          </div>
        </div>
      </div>
      <?php endwhile; ?>
    </div>
  </div>
</section>

<!-- Bottom Ad Section -->
<?php if (!(isLoggedIn() && isPremium())): ?>
<div class="container my-5">
    <div class="ad-container ad-banner">
        <script type="text/javascript">
            atOptions = {
                'key' : '735a559a5872816da47237a603cac4ad',
                'format' : 'iframe',
                'height' : 90,
                'width' : 728,
                'params' : {}
            };
        </script>
        <script type="text/javascript" src="//www.highperformanceformat.com/735a559a5872816da47237a603cac4ad/invoke.js"></script>
    </div>
</div>
<?php endif; ?>

<!-- Premium Content Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2 class="section-title mb-0"><i class="fa-solid fa-gem text-warning me-2"></i>প্রিমিয়াম কন্টেন্ট</h2>
      <a href="/premium.php" class="btn btn-outline-warning">সব দেখুন <i class="fa-solid fa-arrow-right ms-1"></i></a>
    </div>
    <div class="row g-4">
      <?php
      $premium_content_query = "SELECT
                            'movie' as content_type,
                            m.id,
                            m.title,
                            m.description,
                            m.release_year as year,
                            m.duration,
                            m.banner,
                            m.poster,
                            m.rating,
                            m.premium_only,
                            c.name as category_name
                        FROM movies m
                        LEFT JOIN categories c ON m.category_id = c.id
                        WHERE m.premium_only = 1
                        UNION
                        SELECT
                            'tvshow' as content_type,
                            t.id,
                            t.title,
                            t.description,
                            t.start_year as year,
                            NULL as duration,
                            t.banner,
                            t.poster,
                            t.rating,
                            t.premium_only,
                            c.name as category_name
                        FROM tvshows t
                        LEFT JOIN categories c ON t.category_id = c.id
                        WHERE t.premium_only = 1
                        ORDER BY year DESC
                        LIMIT 10";
      $premium_content_result = mysqli_query($conn, $premium_content_query);
      while($content = mysqli_fetch_assoc($premium_content_result)):
          $banner_image = !empty($content['banner']) && file_exists('uploads/' . $content['banner']) ? $content['banner'] : 'movie_banner_564147_1743844083.jpg';
          $content_type = $content['content_type'];
          $content_id = $content['id'];
      ?>
      <div class="col-6 col-md-4 col-lg-2">
        <div class="movie-card card h-100 shadow-sm">
          <div class="position-relative">
            <img src="uploads/<?php echo $banner_image; ?>" class="card-img-top" alt="<?php echo htmlspecialchars($content['title']); ?>">
            <?php if($content['premium_only']): ?>
            <span class="badge bg-warning text-dark position-absolute top-0 end-0 m-2"><i class="fa-solid fa-crown"></i> প্রিমিয়াম</span>
            <?php endif; ?>
          </div>
          <div class="card-body d-flex flex-column">
            <h5 class="card-title text-truncate"><?php echo htmlspecialchars($content['title']); ?></h5>
            <div class="d-flex align-items-center mb-2">
              <span class="badge <?php echo $content_type=='movie'?'bg-danger':'bg-primary'; ?> ms-1 fs-6"><i class="fa-solid <?php echo $content_type=='movie'?'fa-film':'fa-tv'; ?> me-1"></i> <?php echo $content_type=='movie'?'মুভি':'টিভি শো'; ?></span>
            </div>
            <div class="slide-meta mb-2">
              <span><i class="fa-solid fa-calendar"></i> <?php echo $content['year']; ?></span>
              <?php if($content_type == 'movie' && !empty($content['duration'])): ?>
              <span class="ms-2"><i class="fa-solid fa-clock"></i> <?php echo $content['duration']; ?> মিনিট</span>
              <?php endif; ?>
              <span class="ms-2"><i class="fa-solid fa-star"></i> <?php echo number_format($content['rating'], 1); ?></span>
              <span class="ms-2"><i class="fa-solid fa-tag"></i> <?php echo htmlspecialchars($content['category_name']); ?></span>
            </div>
            <p class="slide-description mb-3 d-none d-md-block fs-5 text-shadow-sm"><?php echo htmlspecialchars(substr($content['description'], 0, 180)); ?>...</p>
            <div class="slide-buttons">
              <?php if($content_type == 'movie'): ?>
              <a href="movie_details.php?id=<?php echo $content_id; ?>" class="btn btn-danger btn-lg me-2 px-4 py-2 shadow"><i class="fa-solid fa-play me-2"></i> এখনই দেখুন</a>
              <a href="movie_details.php?id=<?php echo $content_id; ?>#download" class="btn btn-outline-light btn-lg px-4 py-2 shadow"><i class="fa-solid fa-download me-2"></i> ডাউনলোড</a>
              <?php else: ?>
              <a href="tvshow_details.php?id=<?php echo $content_id; ?>" class="btn btn-primary btn-lg me-2 px-4 py-2 shadow"><i class="fa-solid fa-play me-2"></i> এখনই দেখুন</a>
              <a href="tvshow_details.php?id=<?php echo $content_id; ?>#download" class="btn btn-outline-light btn-lg px-4 py-2 shadow"><i class="fa-solid fa-download me-2"></i> ডাউনলোড</a>
              <?php endif; ?>
            </div>
          </div>
        </div>
      </div>
      <?php endwhile; ?>
    </div>
  </div>
</section>

<!-- Final Ad Section -->
<?php if (!(isLoggedIn() && isPremium())): ?>
<div class="container my-5">
    <div class="row">
        <div class="col-md-6 mb-3">
            <div class="ad-container ad-sidebar">
                <script type="text/javascript">
                    atOptions = {
                        'key' : '7d3b7accac0194a88ccf420c241ec7aa',
                        'format' : 'iframe',
                        'height' : 250,
                        'width' : 300,
                        'params' : {}
                    };
                </script>
                <script type="text/javascript" src="//www.highperformanceformat.com/7d3b7accac0194a88ccf420c241ec7aa/invoke.js"></script>
            </div>
        </div>
        <div class="col-md-6 mb-3">
            <div class="ad-container ad-native">
                <script type='text/javascript' src='//pl27076956.profitableratecpm.com/fd/6c/f8/fd6cf8e64921887d3713ef08ffd94b55.js'></script>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Footer -->
<?php require_once 'includes/footer.php'; ?>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Custom JS -->
<script src="assets/js/custom.js"></script>
</body>