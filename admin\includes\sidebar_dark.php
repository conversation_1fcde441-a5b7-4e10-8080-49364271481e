<!-- Wrapper -->
<div class="d-flex">
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-brand-icon">
                <i class="fas fa-film text-primary"></i>
            </div>
            <div class="sidebar-brand-text">
                <span class="site-name"><span class="text-primary">CINE</span><span class="text-light">PIX</span></span>
                <span class="admin-text">Admin Panel</span>
            </div>
        </div>

        <ul class="components list-unstyled">
        <!-- Dashboard -->
        <li>
            <a href="index.php" class="<?php echo $current_page == 'index.php' ? 'active' : ''; ?>">
                <i class="fas fa-tachometer-alt"></i>
                <span class="menu-text">ড্যাশবোর্ড</span>
            </a>
        </li>

        <!-- Content Management -->
        <li>
            <a href="#contentSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle <?php echo in_array($current_page, ['movies.php', 'add_movie.php', 'edit_movie.php', 'tvshows.php', 'add_tvshow.php', 'edit_tvshow.php', 'manage_episodes.php', 'manage_episode_links.php', 'categories.php']) ? 'active' : ''; ?>">
                <i class="fas fa-photo-video"></i>
                <span class="menu-text">কনটেন্ট</span>
            </a>
            <ul class="collapse <?php echo in_array($current_page, ['movies.php', 'add_movie.php', 'edit_movie.php', 'tvshows.php', 'add_tvshow.php', 'edit_tvshow.php', 'manage_episodes.php', 'manage_episode_links.php', 'categories.php']) ? 'show' : ''; ?>" id="contentSubmenu">
                <li>
                    <a href="movies.php" class="<?php echo in_array($current_page, ['movies.php', 'add_movie.php', 'edit_movie.php']) ? 'active' : ''; ?>">
                        <i class="fas fa-film"></i>
                        <span class="menu-text">মুভি</span>
                    </a>
                </li>
                <li>
                    <a href="tvshows.php" class="<?php echo in_array($current_page, ['tvshows.php', 'add_tvshow.php', 'edit_tvshow.php', 'manage_episodes.php', 'manage_episode_links.php']) ? 'active' : ''; ?>">
                        <i class="fas fa-tv"></i>
                        <span class="menu-text">টিভি সিরিজ</span>
                    </a>
                </li>
                <li>
                    <a href="categories.php" class="<?php echo $current_page == 'categories.php' ? 'active' : ''; ?>">
                        <i class="fas fa-tags"></i>
                        <span class="menu-text">ক্যাটাগরি</span>
                    </a>
                </li>
                <li>
                    <a href="update_tags_tables.php" class="<?php echo $current_page == 'update_tags_tables.php' ? 'active' : ''; ?>">
                        <i class="fas fa-language"></i>
                        <span class="menu-text">ট্যাগ ম্যানেজমেন্ট</span>
                    </a>
                </li>
                <li>
                    <a href="manage_links.php" class="<?php echo $current_page == 'manage_links.php' ? 'active' : ''; ?>">
                        <i class="fas fa-link"></i>
                        <span class="menu-text">লিংক ম্যানেজমেন্ট</span>
                    </a>
                </li>
                <li>
                    <a href="manage_server_links.php" class="<?php echo $current_page == 'manage_server_links.php' ? 'active' : ''; ?>">
                        <i class="fas fa-server"></i>
                        <span class="menu-text">সার্ভার লিংক ম্যানেজমেন্ট</span>
                    </a>
                </li>
                <li>
                    <a href="manage_subtitles.php" class="<?php echo $current_page == 'manage_subtitles.php' ? 'active' : ''; ?>">
                        <i class="fas fa-closed-captioning"></i>
                        <span class="menu-text">সাবটাইটেল ম্যানেজমেন্ট</span>
                    </a>
                </li>
            </ul>
        </li>

        <!-- Import from TMDB -->
        <li>
            <a href="#importSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle <?php echo in_array($current_page, ['import_tmdb.php', 'import_tmdb_tvshow.php', 'fix_posters.php']) ? 'active' : ''; ?>">
                <i class="fas fa-file-import"></i>
                <span class="menu-text">ইমপোর্ট</span>
            </a>
            <ul class="collapse <?php echo in_array($current_page, ['import_tmdb.php', 'import_tmdb_tvshow.php', 'fix_posters.php']) ? 'show' : ''; ?>" id="importSubmenu">
                <li>
                    <a href="import_tmdb.php" class="<?php echo $current_page == 'import_tmdb.php' ? 'active' : ''; ?>">
                        <i class="fas fa-film"></i>
                        <span class="menu-text">মুভি ইমপোর্ট</span>
                    </a>
                </li>
                <li>
                    <a href="import_tmdb_tvshow.php" class="<?php echo $current_page == 'import_tmdb_tvshow.php' ? 'active' : ''; ?>">
                        <i class="fas fa-tv"></i>
                        <span class="menu-text">টিভি সিরিজ ইমপোর্ট</span>
                    </a>
                </li>
                <li>
                    <a href="fix_posters.php" class="<?php echo $current_page == 'fix_posters.php' ? 'active' : ''; ?>">
                        <i class="fas fa-images"></i>
                        <span class="menu-text">পোস্টার ফিক্স</span>
                    </a>
                </li>
            </ul>
        </li>

        <!-- User Management -->
        <li>
            <a href="#userSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle <?php echo in_array($current_page, ['users.php', 'reviews.php']) ? 'active' : ''; ?>">
                <i class="fas fa-users"></i>
                <span class="menu-text">ব্যবহারকারী</span>
            </a>
            <ul class="collapse <?php echo in_array($current_page, ['users.php', 'reviews.php']) ? 'show' : ''; ?>" id="userSubmenu">
                <li>
                    <a href="users.php" class="<?php echo $current_page == 'users.php' ? 'active' : ''; ?>">
                        <i class="fas fa-user"></i>
                        <span class="menu-text">ব্যবহারকারী ম্যানেজমেন্ট</span>
                    </a>
                </li>
                <li>
                    <a href="reviews.php" class="<?php echo $current_page == 'reviews.php' ? 'active' : ''; ?>">
                        <i class="fas fa-star"></i>
                        <span class="menu-text">রিভিউ</span>
                    </a>
                </li>
            </ul>
        </li>

        <!-- Premium Features -->
        <li>
            <a href="#premiumSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle <?php echo in_array($current_page, ['payments.php', 'premium_plans.php', 'manage_premium.php', 'payment_settings.php']) ? 'active' : ''; ?>">
                <i class="fas fa-crown"></i>
                <span class="menu-text">প্রিমিয়াম</span>
            </a>
            <ul class="collapse <?php echo in_array($current_page, ['payments.php', 'premium_plans.php', 'manage_premium.php', 'payment_settings.php']) ? 'show' : ''; ?>" id="premiumSubmenu">
                <li>
                    <a href="payments.php" class="<?php echo $current_page == 'payments.php' ? 'active' : ''; ?>">
                        <i class="fas fa-credit-card"></i>
                        <span class="menu-text">পেমেন্ট</span>
                        <?php
                        // Count pending payments
                        $pending_payments_query = "SELECT COUNT(*) as count FROM payments WHERE payment_status = 'pending'";
                        $pending_payments_result = mysqli_query($conn, $pending_payments_query);
                        $pending_payments = mysqli_fetch_assoc($pending_payments_result)['count'];
                        if($pending_payments > 0):
                        ?>
                        <span class="badge bg-danger rounded-pill"><?php echo $pending_payments; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                <li>
                    <a href="premium_plans.php" class="<?php echo $current_page == 'premium_plans.php' ? 'active' : ''; ?>">
                        <i class="fas fa-list-alt"></i>
                        <span class="menu-text">প্রিমিয়াম প্ল্যান</span>
                    </a>
                </li>
                <li>
                    <a href="manage_premium.php" class="<?php echo $current_page == 'manage_premium.php' ? 'active' : ''; ?>">
                        <i class="fas fa-gem"></i>
                        <span class="menu-text">প্রিমিয়াম ম্যানেজমেন্ট</span>
                    </a>
                </li>
                <li>
                    <a href="payment_settings.php" class="<?php echo $current_page == 'payment_settings.php' ? 'active' : ''; ?>">
                        <i class="fas fa-cog"></i>
                        <span class="menu-text">পেমেন্ট সেটিংস</span>
                    </a>
                </li>
            </ul>
        </li>

        <!-- Live Chat -->
        <li>
            <a href="live_chat.php" class="<?php echo $current_page == 'live_chat.php' ? 'active' : ''; ?>">
                <i class="fas fa-comment-dots"></i>
                <span class="menu-text">লাইভ চ্যাট</span>
                <?php
                // Count unread chat messages
                $unread_chat_query = "SELECT COUNT(*) as count FROM chat_messages cm
                                     JOIN chat_sessions cs ON cm.session_id = cs.id
                                     WHERE cs.status = 'active' AND cm.receiver_id = {$_SESSION['user_id']} AND cm.is_read = FALSE";
                $unread_chat_result = mysqli_query($conn, $unread_chat_query);
                $unread_chat_count = 0;
                if ($unread_chat_result) {
                    $unread_chat_count = mysqli_fetch_assoc($unread_chat_result)['count'];
                }

                if($unread_chat_count > 0):
                ?>
                <span class="badge bg-danger rounded-pill"><?php echo $unread_chat_count; ?></span>
                <?php endif; ?>
            </a>
        </li>

        <!-- Mobile App Management -->
        <li>
            <a href="#appSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle <?php echo in_array($current_page, ['app_management.php', 'app_statistics.php', 'app_notifications.php']) ? 'active' : ''; ?>">
                <i class="fas fa-mobile-alt"></i>
                <span class="menu-text">মোবাইল অ্যাপ</span>
            </a>
            <ul class="collapse <?php echo in_array($current_page, ['app_management.php', 'app_statistics.php', 'app_notifications.php']) ? 'show' : ''; ?>" id="appSubmenu">
                <li>
                    <a href="app_management.php" class="<?php echo $current_page == 'app_management.php' ? 'active' : ''; ?>">
                        <i class="fas fa-cog"></i>
                        <span class="menu-text">অ্যাপ সেটিংস</span>
                    </a>
                </li>
                <li>
                    <a href="app_statistics.php" class="<?php echo $current_page == 'app_statistics.php' ? 'active' : ''; ?>">
                        <i class="fas fa-chart-bar"></i>
                        <span class="menu-text">অ্যাপ স্ট্যাটিসটিক্স</span>
                    </a>
                </li>
                <li>
                    <a href="app_notifications.php" class="<?php echo $current_page == 'app_notifications.php' ? 'active' : ''; ?>">
                        <i class="fas fa-bell"></i>
                        <span class="menu-text">অ্যাপ নোটিফিকেশন</span>
                    </a>
                </li>
            </ul>
        </li>

        <!-- Analytics & Tracking -->
        <li>
            <a href="#analyticsSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle <?php echo in_array($current_page, ['analytics.php', 'live_tracking.php', 'live_user_tracker.php']) ? 'active' : ''; ?>">
                <i class="fas fa-chart-line"></i>
                <span class="menu-text">অ্যানালিটিক্স</span>
            </a>
            <ul class="collapse <?php echo in_array($current_page, ['analytics.php', 'live_tracking.php', 'live_user_tracker.php']) ? 'show' : ''; ?>" id="analyticsSubmenu">
                <li>
                    <a href="analytics.php" class="<?php echo $current_page == 'analytics.php' ? 'active' : ''; ?>">
                        <i class="fas fa-chart-bar"></i>
                        <span class="menu-text">অ্যানালিটিক্স ড্যাশবোর্ড</span>
                    </a>
                </li>
                <li>
                    <a href="live_user_tracker.php" class="<?php echo $current_page == 'live_user_tracker.php' ? 'active' : ''; ?>">
                        <i class="fas fa-users-cog"></i>
                        <span class="menu-text">লাইভ ইউজার ট্র্যাকার</span>
                    </a>
                </li>
                <li>
                    <a href="live_tracking.php" class="<?php echo $current_page == 'live_tracking.php' ? 'active' : ''; ?>">
                        <i class="fas fa-user-clock"></i>
                        <span class="menu-text">সেশন ট্র্যাকিং</span>
                    </a>
                </li>
            </ul>
        </li>

        <!-- System Management -->
        <li>
            <a href="#systemSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle <?php echo in_array($current_page, ['site_settings.php', 'backup.php', 'logs.php', 'activity_logs.php']) ? 'active' : ''; ?>">
                <i class="fas fa-cogs"></i>
                <span class="menu-text">সিস্টেম</span>
            </a>
            <ul class="collapse <?php echo in_array($current_page, ['site_settings.php', 'backup.php', 'logs.php', 'activity_logs.php']) ? 'show' : ''; ?>" id="systemSubmenu">
                <li>
                    <a href="site_settings.php" class="<?php echo $current_page == 'site_settings.php' ? 'active' : ''; ?>">
                        <i class="fas fa-cog"></i>
                        <span class="menu-text">সাইট সেটিংস</span>
                    </a>
                </li>
                <li>
                    <a href="backup.php" class="<?php echo $current_page == 'backup.php' ? 'active' : ''; ?>">
                        <i class="fas fa-database"></i>
                        <span class="menu-text">ব্যাকআপ ও রিস্টোর</span>
                    </a>
                </li>
                <li>
                    <a href="logs.php" class="<?php echo $current_page == 'logs.php' ? 'active' : ''; ?>">
                        <i class="fas fa-list"></i>
                        <span class="menu-text">সিস্টেম লগ</span>
                    </a>
                </li>
                <li>
                    <a href="activity_logs.php" class="<?php echo $current_page == 'activity_logs.php' ? 'active' : ''; ?>">
                        <i class="fas fa-history"></i>
                        <span class="menu-text">অ্যাক্টিভিটি লগ</span>
                    </a>
                </li>
            </ul>
        </li>

        <!-- Utilities -->
        <li>
            <a href="#utilsSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle <?php echo in_array($current_page, ['image_compressor.php', 'debug_images.php', 'fix_images.php', 'update_database.php']) ? 'active' : ''; ?>">
                <i class="fas fa-tools"></i>
                <span class="menu-text">যন্ত্রপাতি</span>
            </a>
            <ul class="collapse <?php echo in_array($current_page, ['image_compressor.php', 'debug_images.php', 'fix_images.php', 'update_database.php']) ? 'show' : ''; ?>" id="utilsSubmenu">
                <li>
                    <a href="image_compressor.php" class="<?php echo $current_page == 'image_compressor.php' ? 'active' : ''; ?>">
                        <i class="fas fa-compress"></i>
                        <span class="menu-text">ইমেজ কম্প্রেসর</span>
                    </a>
                </li>
                <li>
                    <a href="debug_images.php" class="<?php echo $current_page == 'debug_images.php' ? 'active' : ''; ?>">
                        <i class="fas fa-bug"></i>
                        <span class="menu-text">ইমেজ ডিবাগ</span>
                    </a>
                </li>
                <li>
                    <a href="fix_images.php" class="<?php echo $current_page == 'fix_images.php' ? 'active' : ''; ?>">
                        <i class="fas fa-wrench"></i>
                        <span class="menu-text">ইমেজ ফিক্স</span>
                    </a>
                </li>
                <li>
                    <a href="update_database.php" class="<?php echo $current_page == 'update_database.php' ? 'active' : ''; ?>">
                        <i class="fas fa-sync"></i>
                        <span class="menu-text">ডাটাবেজ আপডেট</span>
                    </a>
                </li>
            </ul>
        </li>

        <!-- View Site -->
        <li>
            <a href="<?php echo SITE_URL; ?>" target="_blank">
                <i class="fas fa-external-link-alt"></i>
                <span class="menu-text">সাইট দেখুন</span>
            </a>
        </li>

        <!-- Logout -->
        <li>
            <a href="<?php echo SITE_URL; ?>/logout.php">
                <i class="fas fa-sign-out-alt"></i>
                <span class="menu-text">লগআউট</span>
            </a>
        </li>
    </ul>
</nav>
