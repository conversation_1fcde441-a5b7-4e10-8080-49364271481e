<?php
require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Check if TV show ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('tvshows.php');
}

$tvshow_id = (int)$_GET['id'];

// Check if TV show exists
$check_query = "SELECT id FROM tvshows WHERE id = $tvshow_id";
$check_result = mysqli_query($conn, $check_query);

if (mysqli_num_rows($check_result) == 0) {
    redirect('tvshows.php');
}

// Begin transaction
mysqli_begin_transaction($conn);

try {
    // Get all episodes for this TV show
    $episodes_query = "SELECT id FROM episodes WHERE tvshow_id = $tvshow_id";
    $episodes_result = mysqli_query($conn, $episodes_query);
    
    // Delete episode links for each episode
    while ($episode = mysqli_fetch_assoc($episodes_result)) {
        $episode_id = $episode['id'];
        $delete_episode_links = "DELETE FROM episode_links WHERE episode_id = $episode_id";
        mysqli_query($conn, $delete_episode_links);
    }
    
    // Delete all episodes
    $delete_episodes = "DELETE FROM episodes WHERE tvshow_id = $tvshow_id";
    mysqli_query($conn, $delete_episodes);
    
    // Delete seasons if the table exists
    $check_seasons_table = "SHOW TABLES LIKE 'seasons'";
    $seasons_table_exists = mysqli_query($conn, $check_seasons_table);
    if (mysqli_num_rows($seasons_table_exists) > 0) {
        $delete_seasons = "DELETE FROM seasons WHERE tvshow_id = $tvshow_id";
        mysqli_query($conn, $delete_seasons);
    }
    
    // Delete streaming links
    $delete_streaming_links = "DELETE FROM streaming_links WHERE content_type = 'tvshow' AND content_id = $tvshow_id";
    mysqli_query($conn, $delete_streaming_links);
    
    // Delete download links
    $delete_download_links = "DELETE FROM download_links WHERE content_type = 'tvshow' AND content_id = $tvshow_id";
    mysqli_query($conn, $delete_download_links);
    
    // Delete reviews
    $delete_reviews = "DELETE FROM reviews WHERE content_type = 'tvshow' AND content_id = $tvshow_id";
    mysqli_query($conn, $delete_reviews);
    
    // Delete from watchlist
    $delete_watchlist = "DELETE FROM watchlist WHERE content_type = 'tvshow' AND content_id = $tvshow_id";
    mysqli_query($conn, $delete_watchlist);
    
    // Finally, delete the TV show
    $delete_tvshow = "DELETE FROM tvshows WHERE id = $tvshow_id";
    mysqli_query($conn, $delete_tvshow);
    
    // Commit transaction
    mysqli_commit($conn);
    
    // Redirect with success message
    $_SESSION['success_message'] = 'TV show deleted successfully.';
    redirect('tvshows.php');
} catch (Exception $e) {
    // Rollback transaction on error
    mysqli_rollback($conn);
    
    // Redirect with error message
    $_SESSION['error_message'] = 'Error deleting TV show: ' . $e->getMessage();
    redirect('tvshows.php');
}
?>
