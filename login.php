<?php
require_once 'includes/header.php';

// Check for remember me cookie
if (!isLoggedIn() && isset($_COOKIE['remember_user'])) {
    $cookie_data = json_decode($_COOKIE['remember_user'], true);

    if (isset($cookie_data['user_id']) && isset($cookie_data['token'])) {
        $user_id = $cookie_data['user_id'];
        $token = $cookie_data['token'];

        // Verify the token from database
        $query = "SELECT * FROM users WHERE id = '$user_id'";
        $result = mysqli_query($conn, $query);

        if (mysqli_num_rows($result) == 1) {
            $user = mysqli_fetch_assoc($result);

            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['profile_image'] = $user['profile_image'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['is_premium'] = $user['is_premium'];

            // Redirect to home page
            redirect(SITE_URL);
        }
    }
}

// Check if user is already logged in
if (isLoggedIn()) {
    redirect(SITE_URL);
}

// Process login form
$error = '';
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = sanitize($_POST['username']);
    $password = $_POST['password'];

    // Validate input
    if (empty($username) || empty($password)) {
        $error = 'Please enter both username and password.';
    } else {
        // Check if user exists
        $query = "SELECT * FROM users WHERE username = '$username' OR email = '$username'";
        $result = mysqli_query($conn, $query);

        if (mysqli_num_rows($result) == 1) {
            $user = mysqli_fetch_assoc($result);

            // Verify password
            if (password_verify($password, $user['password'])) {
                // Set session variables
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['profile_image'] = $user['profile_image'];
                $_SESSION['user_role'] = $user['role'];
                $_SESSION['is_premium'] = $user['is_premium'];

                // Handle remember me checkbox
                if (isset($_POST['remember']) && $_POST['remember'] == 'on') {
                    // Create a token for the remember me cookie
                    $token = bin2hex(random_bytes(32)); // Generate a secure random token

                    // Store the token in a cookie (valid for 30 days)
                    $cookie_data = json_encode([
                        'user_id' => $user['id'],
                        'token' => $token
                    ]);

                    setcookie('remember_user', $cookie_data, time() + 2592000, '/', '', false, true);
                }

                // Redirect to home page or intended page
                $redirect_to = isset($_SESSION['redirect_to']) ? $_SESSION['redirect_to'] : SITE_URL;
                unset($_SESSION['redirect_to']);
                redirect($redirect_to);
            } else {
                $error = 'Invalid password.';
            }
        } else {
            $error = 'User not found.';
        }
    }
}
?>

<!-- Login Section -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="form-container">
                    <h2 class="form-title">Login</h2>

                    <?php if($error): ?>
                    <div class="alert alert-danger">
                        <?php echo $error; ?>
                    </div>
                    <?php endif; ?>

                    <form method="POST" action="">
                        <div class="mb-3">
                            <label for="username" class="form-label">Username or Email</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember" name="remember">
                            <label class="form-check-label" for="remember">Remember me</label>
                        </div>
                        <button type="submit" class="btn btn-danger w-100">Login</button>
                    </form>

                    <div class="mt-4 text-center">
                        <p class="mb-0">Don't have an account? <a href="<?php echo SITE_URL; ?>/register.php" class="text-danger">Sign up</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php require_once 'includes/footer.php'; ?>
