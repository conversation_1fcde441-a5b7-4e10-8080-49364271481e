# মেসেজিং সিস্টেম ইনস্টলেশন নির্দেশনা

## ডাটাবেস সেটআপ

নিম্নলিখিত SQL কোড phpMyAdmin বা MySQL কমান্ড লাইন ব্যবহার করে চালান:

```sql
-- Messages table
CREATE TABLE IF NOT EXISTS messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    admin_id INT DEFAULT NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    reply TEXT DEFAULT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    is_replied B<PERSON><PERSON><PERSON>N DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (admin_id) REFERENCES users(id) ON DELETE SET NULL
);
```

## ফাইল সেটআপ

সমস্ত ফাইল আপনার সার্ভারে আপলোড করুন। নিম্নলিখিত ফাইলগুলি যুক্ত করা হয়েছে:

1. `send_message.php` - মেসেজ পাঠানোর জন্য
2. `messages.php` - ইউজারদের মেসেজ দেখার জন্য
3. `admin/messages.php` - এডমিনদের মেসেজ ম্যানেজ করার জন্য

## ব্যবহার নির্দেশনা

### ইউজারদের জন্য:
1. হোম পেজের নিচে ডান দিকে একটি ফ্লোটিং মেসেজ আইকন দেখতে পাবেন
2. আইকনে ক্লিক করে মেসেজ ফর্ম ওপেন করুন
3. বিষয় এবং মেসেজ লিখে পাঠান
4. আপনার পাঠানো মেসেজ এবং এডমিনের উত্তর দেখতে প্রোফাইল মেনু থেকে "My Messages" এ ক্লিক করুন

### এডমিনদের জন্য:
1. এডমিন প্যানেলে "Messages" মেনুতে ক্লিক করুন
2. সমস্ত মেসেজ দেখুন এবং উত্তর দিন
3. অপঠিত মেসেজের জন্য একটি লাল ব্যাজ দেখাবে

## সীমাবদ্ধতা

- একজন ইউজার 24 ঘন্টায় একটি মাত্র মেসেজ পাঠাতে পারবেন
- শুধুমাত্র লগইন করা ইউজাররা মেসেজ পাঠাতে পারবেন
