<?php
// Include configuration file
require_once '../../config.php';

// Include database connection
require_once '../../db_connect.php';

// Include functions
require_once '../../functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Get active sessions
$active_time = date('Y-m-d H:i:s', strtotime('-15 minutes'));
$sessions_query = "SELECT us.*, u.username, u.email, u.role, u.is_premium
                  FROM user_sessions us
                  LEFT JOIN users u ON us.user_id = u.id
                  WHERE us.last_activity >= '$active_time'
                  ORDER BY us.last_activity DESC";
$sessions_result = mysqli_query($conn, $sessions_query);

// Get session count
$session_count = mysqli_num_rows($sessions_result);

// Get unique visitor count
$visitors_query = "SELECT COUNT(DISTINCT ip_address) as unique_visitors
                  FROM user_sessions
                  WHERE last_activity >= '$active_time'";
$visitors_result = mysqli_query($conn, $visitors_query);
$visitors_data = mysqli_fetch_assoc($visitors_result);
$unique_visitors = $visitors_data['unique_visitors'];

// Get page views
$pageviews_query = "SELECT page_url, COUNT(*) as view_count
                   FROM page_views
                   WHERE created_at >= '$active_time'
                   GROUP BY page_url
                   ORDER BY view_count DESC
                   LIMIT 10";
$pageviews_result = mysqli_query($conn, $pageviews_query);

// Count registered and premium users
$registered_count = 0;
$premium_count = 0;
$sessions = [];

mysqli_data_seek($sessions_result, 0);
while ($session = mysqli_fetch_assoc($sessions_result)) {
    // Count registered and premium users
    if (!empty($session['user_id'])) {
        $registered_count++;
        if (!empty($session['is_premium'])) {
            $premium_count++;
        }
    }
    
    // Add session to array
    $sessions[] = [
        'session_id' => $session['session_id'],
        'user_id' => $session['user_id'],
        'username' => $session['username'] ?? null,
        'is_premium' => !empty($session['is_premium']),
        'ip_address' => $session['ip_address'],
        'device_type' => $session['device_type'],
        'browser' => $session['browser'],
        'os' => $session['os'],
        'page_url' => $session['page_url'],
        'referrer_url' => $session['referrer_url'],
        'last_activity' => $session['last_activity']
    ];
}

// Get page views
$page_views = [];
while ($page = mysqli_fetch_assoc($pageviews_result)) {
    $page_views[] = [
        'page_url' => $page['page_url'],
        'view_count' => $page['view_count']
    ];
}

// Prepare response
$response = [
    'session_count' => $session_count,
    'unique_visitors' => $unique_visitors,
    'registered_count' => $registered_count,
    'premium_count' => $premium_count,
    'sessions' => $sessions,
    'page_views' => $page_views
];

// Send response
header('Content-Type: application/json');
echo json_encode($response);
?>
