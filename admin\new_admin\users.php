<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set page title
$page_title = 'ইউজার ম্যানেজমেন্ট';
$current_page = 'users.php';

try {
    // Include configuration and functions
    require_once '../../includes/config.php';
    require_once '../../includes/functions.php';

    // Check if user is logged in and is admin
    if (!isLoggedIn() || !isAdmin()) {
        redirect(SITE_URL . '/login.php');
        exit;
    }
} catch (Exception $e) {
    die("Error: " . $e->getMessage());
}

// Handle bulk actions
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['bulk_action'])) {
    $action = $_POST['bulk_action'];
    $selected_ids = $_POST['selected_users'] ?? [];
    
    if (!empty($selected_ids) && is_array($selected_ids)) {
        $ids = implode(',', array_map('intval', $selected_ids));
        
        try {
            switch ($action) {
                case 'make_premium':
                    $premium_query = "UPDATE users SET is_premium = 1, premium_expires = DATE_ADD(NOW(), INTERVAL 1 MONTH) WHERE id IN ($ids)";
                    if (mysqli_query($conn, $premium_query)) {
                        $success_message = count($selected_ids) . ' জন ইউজারকে প্রিমিয়াম করা হয়েছে।';
                    }
                    break;
                    
                case 'remove_premium':
                    $free_query = "UPDATE users SET is_premium = 0, premium_expires = NULL WHERE id IN ($ids)";
                    if (mysqli_query($conn, $free_query)) {
                        $success_message = count($selected_ids) . ' জন ইউজারের প্রিমিয়াম সরানো হয়েছে।';
                    }
                    break;
                    
                case 'delete':
                    $delete_query = "DELETE FROM users WHERE id IN ($ids) AND role = 'user'";
                    if (mysqli_query($conn, $delete_query)) {
                        $success_message = count($selected_ids) . ' জন ইউজার ডিলিট করা হয়েছে।';
                    }
                    break;
            }
        } catch (Exception $e) {
            $error_message = "Error: " . $e->getMessage();
        }
    }
}

// Get filter parameters
$search = $_GET['search'] ?? '';
$premium_filter = $_GET['premium'] ?? '';
$sort_by = $_GET['sort'] ?? 'created_at';
$sort_order = $_GET['order'] ?? 'DESC';

// Build query
$where_conditions = ["role = 'user'"];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(username LIKE ? OR email LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($premium_filter !== '') {
    $where_conditions[] = "is_premium = ?";
    $params[] = $premium_filter;
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

// Get total count
$count_query = "SELECT COUNT(*) as total FROM users $where_clause";
$stmt = mysqli_prepare($conn, $count_query);
if (!empty($params)) {
    $types = str_repeat('s', count($params));
    mysqli_stmt_bind_param($stmt, $types, ...$params);
}
mysqli_stmt_execute($stmt);
$total_users = mysqli_fetch_assoc(mysqli_stmt_get_result($stmt))['total'];

// Pagination
$page = $_GET['page'] ?? 1;
$per_page = 20;
$total_pages = ceil($total_users / $per_page);
$offset = ($page - 1) * $per_page;

// Get users
$query = "SELECT * FROM users $where_clause ORDER BY $sort_by $sort_order LIMIT $per_page OFFSET $offset";
$stmt = mysqli_prepare($conn, $query);
if (!empty($params)) {
    $types = str_repeat('s', count($params));
    mysqli_stmt_bind_param($stmt, $types, ...$params);
}
mysqli_stmt_execute($stmt);
$users_result = mysqli_stmt_get_result($stmt);

// Include header
include 'includes/header.php';
?>

<!-- Include Sidebar -->
<?php include 'includes/sidebar.php'; ?>

<!-- Main Content -->
<div class="main-content">
    <!-- Page Header -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-title">
                    <i class="fas fa-users me-3"></i>ইউজার ম্যানেজমেন্ট
                </h1>
                <p class="page-subtitle text-muted">মোট <?php echo number_format($total_users); ?> জন ইউজার</p>
            </div>
            <div class="col-auto">
                <div class="page-actions">
                    <a href="add_user.php" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>নতুন ইউজার যোগ করুন
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">খুঁজুন</label>
                    <input type="text" class="form-control" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="ইউজারনেম বা ইমেইল...">
                </div>
                <div class="col-md-2">
                    <label class="form-label">প্রিমিয়াম</label>
                    <select class="form-select" name="premium">
                        <option value="">সব ইউজার</option>
                        <option value="1" <?php echo $premium_filter === '1' ? 'selected' : ''; ?>>প্রিমিয়াম</option>
                        <option value="0" <?php echo $premium_filter === '0' ? 'selected' : ''; ?>>ফ্রি</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">সর্ট</label>
                    <select class="form-select" name="sort">
                        <option value="created_at" <?php echo $sort_by == 'created_at' ? 'selected' : ''; ?>>তারিখ</option>
                        <option value="username" <?php echo $sort_by == 'username' ? 'selected' : ''; ?>>নাম</option>
                        <option value="email" <?php echo $sort_by == 'email' ? 'selected' : ''; ?>>ইমেইল</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">অর্ডার</label>
                    <select class="form-select" name="order">
                        <option value="DESC" <?php echo $sort_order == 'DESC' ? 'selected' : ''; ?>>নিচে</option>
                        <option value="ASC" <?php echo $sort_order == 'ASC' ? 'selected' : ''; ?>>উপরে</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i>
                    </button>
                    <a href="users.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="card-title mb-0">ইউজার তালিকা</h5>
                </div>
                <div class="col-auto">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAll()">
                            <i class="fas fa-check-square me-1"></i>সব নির্বাচন
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="deselectAll()">
                            <i class="fas fa-square me-1"></i>নির্বাচন বাতিল
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <form method="POST" id="bulkActionForm" class="bulk-action-form">
                <!-- Bulk Actions -->
                <div class="bulk-actions p-3 border-bottom" style="display: none;">
                    <div class="row align-items-center">
                        <div class="col">
                            <span class="selected-count">0</span> জন ইউজার নির্বাচিত
                        </div>
                        <div class="col-auto">
                            <div class="btn-group">
                                <button type="submit" name="bulk_action" value="make_premium" class="btn btn-warning btn-sm bulk-action-btn" disabled>
                                    <i class="fas fa-crown me-1"></i>প্রিমিয়াম করুন
                                </button>
                                <button type="submit" name="bulk_action" value="remove_premium" class="btn btn-info btn-sm bulk-action-btn" disabled>
                                    <i class="fas fa-user me-1"></i>প্রিমিয়াম সরান
                                </button>
                                <button type="submit" name="bulk_action" value="delete" class="btn btn-danger btn-sm bulk-action-btn delete-btn" disabled data-confirm-message="নির্বাচিত ইউজারদের ডিলিট করতে চান?">
                                    <i class="fas fa-trash me-1"></i>ডিলিট করুন
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th width="50">
                                    <input type="checkbox" class="form-check-input select-all" id="selectAll">
                                </th>
                                <th>ইউজারনেম</th>
                                <th>ইমেইল</th>
                                <th>স্ট্যাটাস</th>
                                <th>প্রিমিয়াম মেয়াদ</th>
                                <th>যোগদানের তারিখ</th>
                                <th width="120">অ্যাকশন</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (mysqli_num_rows($users_result) > 0): ?>
                                <?php while ($user = mysqli_fetch_assoc($users_result)): ?>
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="form-check-input item-checkbox" name="selected_users[]" value="<?php echo $user['id']; ?>">
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="../../assets/img/default-avatar.png" class="rounded-circle me-3" width="40" height="40">
                                                <div>
                                                    <div class="fw-bold"><?php echo htmlspecialchars($user['username']); ?></div>
                                                    <small class="text-muted">ID: <?php echo $user['id']; ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                                        <td>
                                            <?php if ($user['is_premium']): ?>
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-crown me-1"></i>প্রিমিয়াম
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-user me-1"></i>ফ্রি
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($user['is_premium'] && $user['premium_expires']): ?>
                                                <small><?php echo date('d/m/Y', strtotime($user['premium_expires'])); ?></small>
                                            <?php else: ?>
                                                <small class="text-muted">N/A</small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small><?php echo date('d/m/Y', strtotime($user['created_at'])); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="edit_user.php?id=<?php echo $user['id']; ?>" class="btn btn-outline-primary" title="এডিট করুন">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <?php if ($user['is_premium']): ?>
                                                    <a href="remove_premium.php?id=<?php echo $user['id']; ?>" class="btn btn-outline-warning" title="প্রিমিয়াম সরান">
                                                        <i class="fas fa-crown"></i>
                                                    </a>
                                                <?php else: ?>
                                                    <a href="make_premium.php?id=<?php echo $user['id']; ?>" class="btn btn-outline-success" title="প্রিমিয়াম করুন">
                                                        <i class="fas fa-star"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <a href="delete_user.php?id=<?php echo $user['id']; ?>" class="btn btn-outline-danger delete-btn" title="ডিলিট করুন" data-confirm-message="এই ইউজারকে ডিলিট করতে চান?">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">কোন ইউজার পাওয়া যায়নি।</p>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </form>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="card-footer">
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center mb-0">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?>&<?php echo http_build_query($_GET); ?>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query($_GET); ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?>&<?php echo http_build_query($_GET); ?>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// Page-specific scripts
$page_scripts = '
<script>
function selectAll() {
    const checkboxes = document.querySelectorAll(".item-checkbox");
    const selectAllCheckbox = document.getElementById("selectAll");
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    selectAllCheckbox.checked = true;
    
    updateBulkActions();
}

function deselectAll() {
    const checkboxes = document.querySelectorAll(".item-checkbox, #selectAll");
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    
    updateBulkActions();
}

function updateBulkActions() {
    const checkedBoxes = document.querySelectorAll(".item-checkbox:checked");
    const bulkActions = document.querySelector(".bulk-actions");
    const bulkActionButtons = document.querySelectorAll(".bulk-action-btn");
    const selectedCount = document.querySelector(".selected-count");
    
    if (checkedBoxes.length > 0) {
        bulkActions.style.display = "block";
        bulkActionButtons.forEach(btn => btn.disabled = false);
        selectedCount.textContent = checkedBoxes.length;
    } else {
        bulkActions.style.display = "none";
        bulkActionButtons.forEach(btn => btn.disabled = true);
    }
}

// Event listeners
document.addEventListener("DOMContentLoaded", function() {
    // Select all checkbox
    document.getElementById("selectAll").addEventListener("change", function() {
        const checkboxes = document.querySelectorAll(".item-checkbox");
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });
    
    // Individual checkboxes
    document.querySelectorAll(".item-checkbox").forEach(checkbox => {
        checkbox.addEventListener("change", function() {
            const allCheckboxes = document.querySelectorAll(".item-checkbox");
            const checkedCheckboxes = document.querySelectorAll(".item-checkbox:checked");
            const selectAllCheckbox = document.getElementById("selectAll");
            
            selectAllCheckbox.checked = allCheckboxes.length === checkedCheckboxes.length;
            updateBulkActions();
        });
    });
    
    // Bulk action form submission
    document.getElementById("bulkActionForm").addEventListener("submit", function(e) {
        const checkedBoxes = document.querySelectorAll(".item-checkbox:checked");
        if (checkedBoxes.length === 0) {
            e.preventDefault();
            cinepixAdmin.showToast("কোন ইউজার নির্বাচন করা হয়নি।", "warning");
        }
    });
});
</script>
';

// Include footer
include 'includes/footer.php';
?>
