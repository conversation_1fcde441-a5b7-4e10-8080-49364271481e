/**
 * Episode Page Enhancements
 * Adds interactive features and animations to episode listings
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeEpisodeEnhancements();
});

function initializeEpisodeEnhancements() {
    // Initialize all enhancement features
    setupIntersectionObserver();
    setupDownloadTracking();
    setupKeyboardNavigation();
    setupTouchGestures();
    setupProgressiveLoading();
    setupSearchFilter();
}

/**
 * Setup Intersection Observer for scroll animations
 */
function setupIntersectionObserver() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe all episode items
    document.querySelectorAll('.episode-item').forEach(item => {
        observer.observe(item);
    });
}

/**
 * Track download clicks for analytics
 */
function setupDownloadTracking() {
    document.querySelectorAll('.download-link').forEach(link => {
        link.addEventListener('click', function(e) {
            const quality = this.querySelector('.download-quality')?.textContent;
            const server = this.querySelector('.download-server')?.textContent;
            const size = this.querySelector('.download-size')?.textContent;
            
            // Add loading state
            this.classList.add('downloading');
            
            // Track the download
            trackDownload({
                quality: quality,
                server: server,
                size: size,
                url: this.href
            });
            
            // Show download notification
            showDownloadNotification(quality, server);
            
            // Remove loading state after delay
            setTimeout(() => {
                this.classList.remove('downloading');
            }, 2000);
        });
    });
}

/**
 * Setup keyboard navigation
 */
function setupKeyboardNavigation() {
    let currentFocus = -1;
    const downloadLinks = document.querySelectorAll('.download-link');
    
    document.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
            e.preventDefault();
            
            if (e.key === 'ArrowDown') {
                currentFocus = Math.min(currentFocus + 1, downloadLinks.length - 1);
            } else {
                currentFocus = Math.max(currentFocus - 1, 0);
            }
            
            // Focus the current link
            if (downloadLinks[currentFocus]) {
                downloadLinks[currentFocus].focus();
                downloadLinks[currentFocus].scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
            }
        }
        
        if (e.key === 'Enter' && document.activeElement.classList.contains('download-link')) {
            document.activeElement.click();
        }
    });
}

/**
 * Setup touch gestures for mobile
 */
function setupTouchGestures() {
    let startY = 0;
    let startTime = 0;
    
    document.querySelectorAll('.episode-item').forEach(item => {
        item.addEventListener('touchstart', function(e) {
            startY = e.touches[0].clientY;
            startTime = Date.now();
        }, { passive: true });
        
        item.addEventListener('touchend', function(e) {
            const endY = e.changedTouches[0].clientY;
            const endTime = Date.now();
            const deltaY = startY - endY;
            const deltaTime = endTime - startTime;
            
            // Detect swipe up gesture (quick swipe)
            if (deltaY > 50 && deltaTime < 300) {
                // Expand episode details
                this.classList.toggle('expanded');
            }
        }, { passive: true });
    });
}

/**
 * Progressive loading for large episode lists
 */
function setupProgressiveLoading() {
    const episodeItems = document.querySelectorAll('.episode-item');
    const loadingThreshold = 10;
    
    if (episodeItems.length > loadingThreshold) {
        // Hide episodes beyond threshold initially
        episodeItems.forEach((item, index) => {
            if (index >= loadingThreshold) {
                item.style.display = 'none';
                item.classList.add('lazy-load');
            }
        });
        
        // Add load more button
        addLoadMoreButton();
    }
}

/**
 * Add load more button for progressive loading
 */
function addLoadMoreButton() {
    const episodeList = document.querySelector('.episode-list');
    const loadMoreBtn = document.createElement('button');
    loadMoreBtn.className = 'btn btn-outline-light load-more-btn';
    loadMoreBtn.innerHTML = '<i class="fas fa-plus me-2"></i>আরো এপিসোড দেখুন';
    
    loadMoreBtn.addEventListener('click', function() {
        const hiddenItems = document.querySelectorAll('.episode-item.lazy-load[style*="display: none"]');
        const itemsToShow = Array.from(hiddenItems).slice(0, 5);
        
        itemsToShow.forEach(item => {
            item.style.display = 'block';
            item.classList.add('fade-in');
        });
        
        if (hiddenItems.length <= 5) {
            this.style.display = 'none';
        }
    });
    
    episodeList.appendChild(loadMoreBtn);
}

/**
 * Setup search/filter functionality
 */
function setupSearchFilter() {
    // Add search input if not exists
    const episodeSection = document.querySelector('.episode-list').parentElement;
    const searchContainer = document.createElement('div');
    searchContainer.className = 'episode-search-container mb-4';
    searchContainer.innerHTML = `
        <div class="input-group">
            <span class="input-group-text bg-dark border-secondary">
                <i class="fas fa-search text-light"></i>
            </span>
            <input type="text" class="form-control bg-dark border-secondary text-light" 
                   placeholder="এপিসোড খুঁজুন..." id="episodeSearch">
        </div>
    `;
    
    episodeSection.insertBefore(searchContainer, episodeSection.firstChild);
    
    // Setup search functionality
    const searchInput = document.getElementById('episodeSearch');
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const episodes = document.querySelectorAll('.episode-item');
        
        episodes.forEach(episode => {
            const title = episode.querySelector('.episode-title h5').textContent.toLowerCase();
            const episodeNumber = episode.querySelector('.episode-number span').textContent.toLowerCase();
            
            if (title.includes(searchTerm) || episodeNumber.includes(searchTerm)) {
                episode.style.display = 'block';
                episode.classList.add('search-match');
            } else {
                episode.style.display = 'none';
                episode.classList.remove('search-match');
            }
        });
        
        // Show "no results" message if needed
        const visibleEpisodes = document.querySelectorAll('.episode-item[style*="display: block"], .episode-item:not([style*="display: none"])');
        toggleNoResultsMessage(visibleEpisodes.length === 0);
    });
}

/**
 * Show/hide no results message
 */
function toggleNoResultsMessage(show) {
    let noResultsMsg = document.querySelector('.no-results-message');
    
    if (show && !noResultsMsg) {
        noResultsMsg = document.createElement('div');
        noResultsMsg.className = 'no-results-message text-center py-5';
        noResultsMsg.innerHTML = `
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">কোন এপিসোড পাওয়া যায়নি</h5>
            <p class="text-muted">অন্য কিছু খুঁজে দেখুন</p>
        `;
        document.querySelector('.episode-list').appendChild(noResultsMsg);
    } else if (!show && noResultsMsg) {
        noResultsMsg.remove();
    }
}

/**
 * Track download for analytics
 */
function trackDownload(data) {
    // Send analytics data (implement based on your analytics service)
    if (typeof gtag !== 'undefined') {
        gtag('event', 'download', {
            'event_category': 'Episode',
            'event_label': `${data.quality} - ${data.server}`,
            'value': 1
        });
    }
}

/**
 * Show download notification
 */
function showDownloadNotification(quality, server) {
    const notification = document.createElement('div');
    notification.className = 'download-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-download text-success me-2"></i>
            <span>${quality} (${server}) ডাউনলোড শুরু হচ্ছে...</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// CSS for notifications and enhancements
const enhancementStyles = `
<style>
.episode-item.animate-in {
    animation: slideInUp 0.6s ease forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.download-link.downloading {
    opacity: 0.7;
    pointer-events: none;
}

.download-link.downloading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: downloading-shimmer 1s infinite;
}

@keyframes downloading-shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.download-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    z-index: 9999;
    animation: slideInRight 0.3s ease;
}

.download-notification.fade-out {
    animation: slideOutRight 0.3s ease;
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

.load-more-btn {
    width: 100%;
    margin-top: 20px;
    padding: 15px;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.load-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.episode-search-container .form-control:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.episode-item.expanded .episode-content {
    max-height: none;
}

.episode-item.search-match {
    animation: highlightMatch 0.5s ease;
}

@keyframes highlightMatch {
    0%, 100% { background-color: transparent; }
    50% { background-color: rgba(220, 53, 69, 0.1); }
}
</style>
`;

// Inject enhancement styles
document.head.insertAdjacentHTML('beforeend', enhancementStyles);
