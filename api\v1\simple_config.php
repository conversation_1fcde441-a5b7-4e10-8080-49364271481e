<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include config file
require_once '../config.php';

// Simple config endpoint without using the router
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization, API-Key');
header('Content-Type: application/json; charset=UTF-8');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Get app configurations
$query = "SELECT config_key, config_value FROM app_config";
$result = mysqli_query($conn, $query);

if (!$result) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to fetch app configuration: ' . mysqli_error($conn),
        'data' => null
    ]);
    exit;
}

$config = [];
while ($row = mysqli_fetch_assoc($result)) {
    $config[$row['config_key']] = $row['config_value'];
}

// Add additional app information
$config['api_version'] = 'v1';
$config['site_name'] = 'CinePix';
$config['site_url'] = 'https://cinepix.top';

// Get premium plans
$plans_query = "SELECT id, name, price, duration, features FROM premium_plans WHERE status = 'active' ORDER BY price ASC";
$plans_result = mysqli_query($conn, $plans_query);

if ($plans_result) {
    $premium_plans = [];
    while ($plan = mysqli_fetch_assoc($plans_result)) {
        $premium_plans[] = [
            'id' => (int)$plan['id'],
            'name' => $plan['name'],
            'price' => (float)$plan['price'],
            'duration' => (int)$plan['duration'],
            'features' => explode("\n", $plan['features'])
        ];
    }
    $config['premium_plans'] = $premium_plans;
}

// Get payment methods
$payment_query = "SELECT * FROM payment_settings WHERE id = 1";
$payment_result = mysqli_query($conn, $payment_query);

if ($payment_result && mysqli_num_rows($payment_result) > 0) {
    $payment_settings = mysqli_fetch_assoc($payment_result);
    $payment_methods = [];
    
    if ($payment_settings['bkash_enabled']) {
        $payment_methods[] = [
            'id' => 'bkash',
            'name' => 'বিকাশ',
            'merchant_number' => $payment_settings['bkash_merchant_number'],
            'merchant_name' => $payment_settings['bkash_merchant_name'],
            'is_automatic' => true
        ];
    }
    
    if ($payment_settings['nagad_enabled']) {
        $payment_methods[] = [
            'id' => 'nagad',
            'name' => 'নগদ',
            'merchant_number' => $payment_settings['nagad_merchant_number'],
            'merchant_name' => $payment_settings['nagad_merchant_name'],
            'is_automatic' => false
        ];
    }
    
    if ($payment_settings['rocket_enabled']) {
        $payment_methods[] = [
            'id' => 'rocket',
            'name' => 'রকেট',
            'merchant_number' => $payment_settings['rocket_merchant_number'],
            'merchant_name' => $payment_settings['rocket_merchant_name'],
            'is_automatic' => false
        ];
    }
    
    $config['payment_methods'] = $payment_methods;
    $config['payment_instructions'] = $payment_settings['payment_instructions'];
}

// Return app configuration
http_response_code(200);
echo json_encode([
    'success' => true,
    'message' => 'Success',
    'data' => $config
]);
?>
