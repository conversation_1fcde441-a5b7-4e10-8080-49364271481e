// থিম টগল
function setTheme(theme) {
  document.body.classList.remove('theme-dark', 'theme-light');
  document.body.classList.add(theme);
  localStorage.setItem('cinepix_theme', theme);
  // বাটন আইকন পরিবর্তন
  const icon = theme === 'theme-dark' ? 'fa-sun' : 'fa-moon';
  document.getElementById('themeToggle').innerHTML = `<i class="fa-solid ${icon}"></i>`;
  if(document.getElementById('themeToggleMobile')) {
    document.getElementById('themeToggleMobile').innerHTML = `<i class="fa-solid ${icon}"></i> থিম পরিবর্তন`;
  }
}

function toggleTheme() {
  const current = document.body.classList.contains('theme-dark') ? 'theme-dark' : 'theme-light';
  setTheme(current === 'theme-dark' ? 'theme-light' : 'theme-dark');
}

document.addEventListener('DOMContentLoaded', function() {
  // লোকালস্টোরেজ থেকে থিম লোড
  const savedTheme = localStorage.getItem('cinepix_theme') || 'theme-light';
  setTheme(savedTheme);
  // থিম টগল বাটন
  document.getElementById('themeToggle').addEventListener('click', toggleTheme);
  if(document.getElementById('themeToggleMobile')) {
    document.getElementById('themeToggleMobile').addEventListener('click', function() {
      toggleTheme();
      // অফক্যানভাস বন্ধ
      const offcanvas = bootstrap.Offcanvas.getOrCreateInstance(document.getElementById('mobileMenu'));
      offcanvas.hide();
    });
  }
  // Swiper স্লাইডার থাকলে রিফ্রেশ/থিমে কোনো সমস্যা হবে না
}); 