<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: login.php');
    exit();
}

/**
 * SEO Monitoring Dashboard
 * Real-time SEO health monitoring for CinePix
 */

// Get current SEO metrics
function getCurrentSEOMetrics() {
    global $conn;

    $metrics = [];

    // 1. Content metrics
    $content_query = "SELECT
        (SELECT COUNT(*) FROM movies WHERE is_active = 1) as total_movies,
        (SELECT COUNT(*) FROM movies WHERE is_active = 1 AND (seo_title IS NOT NULL AND seo_title != '')) as movies_with_seo,
        (SELECT COUNT(*) FROM tvshows WHERE is_active = 1) as total_tvshows,
        (SELECT COUNT(*) FROM tvshows WHERE is_active = 1 AND (seo_title IS NOT NULL AND seo_title != '')) as tvshows_with_seo";

    $query_result = mysqli_query($conn, $content_query);
    if ($query_result) {
        $result = mysqli_fetch_assoc($query_result);
    } else {
        // Fallback if query fails
        $result = [
            'total_movies' => 0,
            'movies_with_seo' => 0,
            'total_tvshows' => 0,
            'tvshows_with_seo' => 0
        ];
    }

    $total_movies = $result['total_movies'] ?? 0;
    $movies_with_seo = $result['movies_with_seo'] ?? 0;
    $total_tvshows = $result['total_tvshows'] ?? 0;
    $tvshows_with_seo = $result['tvshows_with_seo'] ?? 0;

    $total_content = $total_movies + $total_tvshows;
    $total_seo_optimized = $movies_with_seo + $tvshows_with_seo;

    $metrics['content'] = [
        'total_content' => $total_content,
        'seo_optimized' => $total_seo_optimized,
        'optimization_rate' => $total_content > 0 ? round(($total_seo_optimized / $total_content) * 100, 1) : 0
    ];

    // 2. Technical metrics
    $metrics['technical'] = [
        'sitemap_status' => file_exists('sitemap.xml') ? 'Active' : 'Missing',
        'robots_status' => file_exists('robots.txt') ? 'Active' : 'Missing',
        'ssl_status' => strpos(SITE_URL, 'https://') === 0 ? 'Enabled' : 'Disabled',
        'last_sitemap_update' => file_exists('sitemap.xml') ? date('Y-m-d H:i:s', filemtime('sitemap.xml')) : 'Never'
    ];

    return $metrics;
}

// Check Google indexing status
function checkGoogleIndexingStatus() {
    $domain = parse_url(SITE_URL, PHP_URL_HOST);
    
    // This is a simplified check - in production, you'd use Google Search Console API
    return [
        'domain' => $domain,
        'google_search_url' => "https://www.google.com/search?q=site:" . $domain,
        'search_console_url' => "https://search.google.com/search-console",
        'last_check' => date('Y-m-d H:i:s')
    ];
}

// Get SEO recommendations based on current status
function getSEORecommendations($metrics) {
    $recommendations = [];
    
    if ($metrics['content']['optimization_rate'] < 80) {
        $recommendations[] = [
            'priority' => 'high',
            'title' => 'Complete SEO Optimization',
            'description' => 'Only ' . $metrics['content']['optimization_rate'] . '% of your content is SEO optimized. Complete meta titles and descriptions for better rankings.',
            'action' => 'seo_tools.php?action=update_meta_tags'
        ];
    }
    
    if ($metrics['technical']['ssl_status'] == 'Disabled') {
        $recommendations[] = [
            'priority' => 'critical',
            'title' => 'Enable HTTPS',
            'description' => 'SSL certificate is not enabled. This is crucial for SEO rankings and user trust.',
            'action' => '#'
        ];
    }
    
    if ($metrics['technical']['sitemap_status'] == 'Missing') {
        $recommendations[] = [
            'priority' => 'high',
            'title' => 'Generate Sitemap',
            'description' => 'XML sitemap is missing. Generate and submit to search engines.',
            'action' => 'seo_tools.php?action=generate_sitemap'
        ];
    }
    
    // Always recommend regular monitoring
    $recommendations[] = [
        'priority' => 'medium',
        'title' => 'Regular SEO Monitoring',
        'description' => 'Set up Google Search Console and monitor your site\'s performance regularly.',
        'action' => 'https://search.google.com/search-console'
    ];
    
    return $recommendations;
}

$metrics = getCurrentSEOMetrics();
$indexing_status = checkGoogleIndexingStatus();
$recommendations = getSEORecommendations($metrics);

?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO Monitor - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.css" rel="stylesheet">
    <style>
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        .priority-critical { border-left: 4px solid #dc3545; }
        .priority-high { border-left: 4px solid #fd7e14; }
        .priority-medium { border-left: 4px solid #ffc107; }
        .priority-low { border-left: 4px solid #28a745; }
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="dashboard-header text-center">
            <h1><i class="fas fa-chart-line"></i> SEO Monitoring Dashboard</h1>
            <p class="lead">Real-time SEO health monitoring for <?php echo SITE_NAME; ?></p>
            <small>Last updated: <?php echo date('Y-m-d H:i:s'); ?></small>
        </div>

        <!-- Key Metrics -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-file-alt fa-2x mb-2"></i>
                        <h3><?php echo $metrics['content']['total_content']; ?></h3>
                        <p>Total Content</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h3><?php echo $metrics['content']['seo_optimized']; ?></h3>
                        <p>SEO Optimized</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-percentage fa-2x mb-2"></i>
                        <h3><?php echo $metrics['content']['optimization_rate']; ?>%</h3>
                        <p>Optimization Rate</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-shield-alt fa-2x mb-2"></i>
                        <h3><?php echo $metrics['technical']['ssl_status']; ?></h3>
                        <p>SSL Status</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Technical Status -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-cogs"></i> Technical SEO Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-6">Sitemap:</div>
                            <div class="col-6">
                                <span class="badge bg-<?php echo $metrics['technical']['sitemap_status'] == 'Active' ? 'success' : 'danger'; ?>">
                                    <?php echo $metrics['technical']['sitemap_status']; ?>
                                </span>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-6">Robots.txt:</div>
                            <div class="col-6">
                                <span class="badge bg-<?php echo $metrics['technical']['robots_status'] == 'Active' ? 'success' : 'danger'; ?>">
                                    <?php echo $metrics['technical']['robots_status']; ?>
                                </span>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-6">SSL Certificate:</div>
                            <div class="col-6">
                                <span class="badge bg-<?php echo $metrics['technical']['ssl_status'] == 'Enabled' ? 'success' : 'danger'; ?>">
                                    <?php echo $metrics['technical']['ssl_status']; ?>
                                </span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <small class="text-muted">
                                    Last sitemap update: <?php echo $metrics['technical']['last_sitemap_update']; ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Google Indexing -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fab fa-google"></i> Google Indexing</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Domain:</strong> <?php echo $indexing_status['domain']; ?></p>
                        <p><strong>Last Check:</strong> <?php echo $indexing_status['last_check']; ?></p>
                        
                        <div class="d-grid gap-2">
                            <a href="<?php echo $indexing_status['google_search_url']; ?>" target="_blank" class="btn btn-outline-primary">
                                <i class="fab fa-google"></i> Check Google Index
                            </a>
                            <a href="<?php echo $indexing_status['search_console_url']; ?>" target="_blank" class="btn btn-outline-success">
                                <i class="fas fa-chart-line"></i> Search Console
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- SEO Recommendations -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-lightbulb"></i> SEO Recommendations</h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($recommendations as $rec): ?>
                        <div class="card mb-3 priority-<?php echo $rec['priority']; ?>">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h6 class="card-title">
                                            <span class="badge bg-<?php echo $rec['priority'] == 'critical' ? 'danger' : ($rec['priority'] == 'high' ? 'warning' : 'info'); ?>">
                                                <?php echo strtoupper($rec['priority']); ?>
                                            </span>
                                            <?php echo $rec['title']; ?>
                                        </h6>
                                        <p class="card-text"><?php echo $rec['description']; ?></p>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <a href="<?php echo $rec['action']; ?>" class="btn btn-primary" <?php echo strpos($rec['action'], 'http') === 0 ? 'target="_blank"' : ''; ?>>
                                            Take Action
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-tools"></i> Quick SEO Tools</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2 mb-2">
                                <a href="seo_checker.php" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-search"></i><br>SEO Checker
                                </a>
                            </div>
                            <div class="col-md-2 mb-2">
                                <a href="seo_tools.php?action=generate_sitemap" class="btn btn-outline-success w-100">
                                    <i class="fas fa-sitemap"></i><br>Generate Sitemap
                                </a>
                            </div>
                            <div class="col-md-2 mb-2">
                                <a href="seo_tools.php?action=update_meta_tags" class="btn btn-outline-info w-100">
                                    <i class="fas fa-tags"></i><br>Update Meta Tags
                                </a>
                            </div>
                            <div class="col-md-2 mb-2">
                                <a href="https://pagespeed.web.dev/report?url=<?php echo urlencode(SITE_URL); ?>" target="_blank" class="btn btn-outline-warning w-100">
                                    <i class="fas fa-tachometer-alt"></i><br>Speed Test
                                </a>
                            </div>
                            <div class="col-md-2 mb-2">
                                <a href="https://search.google.com/test/mobile-friendly?url=<?php echo urlencode(SITE_URL); ?>" target="_blank" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-mobile-alt"></i><br>Mobile Test
                                </a>
                            </div>
                            <div class="col-md-2 mb-2">
                                <a href="https://validator.w3.org/nu/?doc=<?php echo urlencode(SITE_URL); ?>" target="_blank" class="btn btn-outline-dark w-100">
                                    <i class="fas fa-code"></i><br>HTML Validator
                                </a>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-4 mb-2">
                                <a href="google_search_console_setup.php" class="btn btn-outline-success w-100">
                                    <i class="fab fa-google"></i><br>Search Console Setup
                                </a>
                            </div>
                            <div class="col-md-4 mb-2">
                                <a href="https://search.google.com/search-console" target="_blank" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-external-link-alt"></i><br>Open Search Console
                                </a>
                            </div>
                            <div class="col-md-4 mb-2">
                                <a href="seo_tools.php?action=submit_to_google" class="btn btn-outline-warning w-100">
                                    <i class="fas fa-paper-plane"></i><br>Submit Sitemap
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto refresh every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
