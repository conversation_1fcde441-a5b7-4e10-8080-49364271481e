import 'package:get/get.dart';
import 'package:cinepix_app/models/category.dart';
import 'package:cinepix_app/services/api_service.dart';

class CategoryController extends GetxController {
  final ApiService _apiService = ApiService();
  
  final RxList<Category> categories = <Category>[].obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  
  @override
  void onInit() {
    super.onInit();
    loadCategories();
  }
  
  // Load categories
  Future<void> loadCategories() async {
    isLoading.value = true;
    errorMessage.value = '';
    
    try {
      final categoriesList = await _apiService.getCategories();
      categories.value = categoriesList;
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }
  
  // Get category by ID
  Category? getCategoryById(int id) {
    try {
      return categories.firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }
  
  // Get category name by ID
  String getCategoryNameById(int id) {
    final category = getCategoryById(id);
    return category?.name ?? 'Unknown';
  }
}
