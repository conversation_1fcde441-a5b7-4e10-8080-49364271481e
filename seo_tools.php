<?php
/**
 * SEO Tools and Utilities for CinePix
 * Tools for managing SEO optimization and search engine submissions
 */

require_once 'includes/config.php';
require_once 'includes/seo_helper.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL . '/login.php');
}

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'generate_sitemap':
            // Generate sitemap
            $sitemap_url = SITE_URL . '/generate_sitemap.php';
            $sitemap_content = file_get_contents($sitemap_url);
            
            if ($sitemap_content) {
                file_put_contents('sitemap.xml', $sitemap_content);
                $message = 'Sitemap generated successfully!';
            } else {
                $error = 'Failed to generate sitemap.';
            }
            break;
            
        case 'submit_to_google':
            // Submit sitemap to Google Search Console
            $sitemap_url = SITE_URL . '/sitemap.xml';

            // First check if sitemap exists and is accessible
            if (!file_exists('sitemap.xml')) {
                $error = 'Sitemap file not found. Please generate sitemap first.';
                break;
            }

            // Check if sitemap is accessible via URL
            $sitemap_check = @file_get_contents($sitemap_url);
            if ($sitemap_check === false) {
                $error = 'Sitemap is not accessible via URL: ' . $sitemap_url . '. Please check your server configuration.';
                break;
            }

            // Try multiple methods to submit sitemap
            $submission_success = false;
            $error_details = [];

            // Method 1: Google Ping Service
            $google_ping_url = 'https://www.google.com/ping?sitemap=' . urlencode($sitemap_url);

            // Use cURL for better error handling
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $google_ping_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; SEO-Tools/1.0)');

            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($response !== false && $http_code == 200) {
                $submission_success = true;
                $message = 'Sitemap submitted to Google successfully via ping service!';
            } else {
                $error_details[] = "Google Ping failed (HTTP: $http_code): " . ($curl_error ?: 'Unknown error');
            }

            // Method 2: Bing submission as fallback
            if (!$submission_success) {
                $bing_ping_url = 'https://www.bing.com/ping?sitemap=' . urlencode($sitemap_url);

                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $bing_ping_url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; SEO-Tools/1.0)');

                $bing_response = curl_exec($ch);
                $bing_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $bing_curl_error = curl_error($ch);
                curl_close($ch);

                if ($bing_response !== false && $bing_http_code == 200) {
                    $submission_success = true;
                    $message = 'Sitemap submitted to Bing successfully! (Google submission failed, but Bing submission worked)';
                } else {
                    $error_details[] = "Bing Ping failed (HTTP: $bing_http_code): " . ($bing_curl_error ?: 'Unknown error');
                }
            }

            if (!$submission_success) {
                $error = 'Failed to submit sitemap automatically. Details: ' . implode('; ', $error_details) .
                        '<br><br><strong>Manual submission required:</strong><br>' .
                        '1. Go to <a href="https://search.google.com/search-console" target="_blank">Google Search Console</a><br>' .
                        '2. Add your property: ' . SITE_URL . '<br>' .
                        '3. Submit sitemap manually: ' . $sitemap_url;
            }
            break;
            
        case 'submit_to_bing':
            // Submit sitemap to Bing
            $sitemap_url = SITE_URL . '/sitemap.xml';
            $bing_ping_url = 'https://www.bing.com/ping?sitemap=' . urlencode($sitemap_url);
            
            $response = @file_get_contents($bing_ping_url);
            if ($response !== false) {
                $message = 'Sitemap submitted to Bing successfully!';
            } else {
                $error = 'Failed to submit sitemap to Bing.';
            }
            break;
            
        case 'update_meta_tags':
            // Update meta tags for all movies and TV shows
            updateAllMetaTags();
            $message = 'Meta tags updated for all content!';
            break;
            
        case 'generate_seo_urls':
            // Generate SEO-friendly URLs for existing content
            generateSeoUrlsForExistingContent();
            $message = 'SEO URLs generated for existing content!';
            break;
    }
}

function updateAllMetaTags() {
    global $conn;
    
    // Update movies
    $movies_query = "SELECT id, title, description, release_year FROM movies WHERE is_active = 1";
    $movies_result = mysqli_query($conn, $movies_query);
    
    while ($movie = mysqli_fetch_assoc($movies_result)) {
        $seo_title = $movie['title'] . " (" . $movie['release_year'] . ") - Download Full Movie | " . SITE_NAME;
        $seo_description = "Download " . $movie['title'] . " (" . $movie['release_year'] . ") full movie in HD quality. " . substr($movie['description'], 0, 120) . "...";
        
        $update_query = "UPDATE movies SET 
                        seo_title = '" . mysqli_real_escape_string($conn, $seo_title) . "',
                        seo_description = '" . mysqli_real_escape_string($conn, $seo_description) . "'
                        WHERE id = " . $movie['id'];
        mysqli_query($conn, $update_query);
    }
    
    // Update TV shows
    $tvshows_query = "SELECT id, title, description, start_year, end_year FROM tvshows";
    $tvshows_result = mysqli_query($conn, $tvshows_query);

    while ($tvshow = mysqli_fetch_assoc($tvshows_result)) {
        $year_display = $tvshow['start_year'];
        if (!empty($tvshow['end_year']) && $tvshow['end_year'] != $tvshow['start_year']) {
            $year_display = $tvshow['start_year'] . '-' . $tvshow['end_year'];
        }

        $seo_title = $tvshow['title'] . (!empty($year_display) ? " (" . $year_display . ")" : "") . " - Watch TV Series Online | " . SITE_NAME;
        $seo_description = "Watch " . $tvshow['title'] . (!empty($year_display) ? " (" . $year_display . ")" : "") . " TV series online. " . substr($tvshow['description'], 0, 120) . "...";

        $update_query = "UPDATE tvshows SET
                        seo_title = '" . mysqli_real_escape_string($conn, $seo_title) . "',
                        seo_description = '" . mysqli_real_escape_string($conn, $seo_description) . "'
                        WHERE id = " . $tvshow['id'];
        mysqli_query($conn, $update_query);
    }
}

function generateSeoUrlsForExistingContent() {
    global $conn;
    
    // Generate SEO URLs for movies
    $movies_query = "SELECT id, title, release_year FROM movies WHERE is_active = 1";
    $movies_result = mysqli_query($conn, $movies_query);
    
    while ($movie = mysqli_fetch_assoc($movies_result)) {
        $seo_url = generateSlug($movie['title']) . "-" . $movie['release_year'] . "-" . $movie['id'];
        
        $update_query = "UPDATE movies SET seo_url = '" . mysqli_real_escape_string($conn, $seo_url) . "' WHERE id = " . $movie['id'];
        mysqli_query($conn, $update_query);
    }
    
    // Generate SEO URLs for TV shows
    $tvshows_query = "SELECT id, title, start_year FROM tvshows";
    $tvshows_result = mysqli_query($conn, $tvshows_query);

    while ($tvshow = mysqli_fetch_assoc($tvshows_result)) {
        $seo_url = generateSlug($tvshow['title']) . "-" . $tvshow['start_year'] . "-" . $tvshow['id'];

        $update_query = "UPDATE tvshows SET seo_url = '" . mysqli_real_escape_string($conn, $seo_url) . "' WHERE id = " . $tvshow['id'];
        mysqli_query($conn, $update_query);
    }
}

// Get SEO statistics
function getSeoStats() {
    global $conn;
    
    $stats = [];
    
    // Total content
    $movies_count = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as count FROM movies WHERE is_active = 1"))['count'];
    $tvshows_count = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as count FROM tvshows WHERE is_active = 1"))['count'];
    
    $stats['total_movies'] = $movies_count;
    $stats['total_tvshows'] = $tvshows_count;
    $stats['total_content'] = $movies_count + $tvshows_count;
    
    // Content with SEO data
    $movies_with_seo = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as count FROM movies WHERE is_active = 1 AND seo_title IS NOT NULL"))['count'];
    $tvshows_with_seo = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as count FROM tvshows WHERE is_active = 1 AND seo_title IS NOT NULL"))['count'];
    
    $stats['movies_with_seo'] = $movies_with_seo;
    $stats['tvshows_with_seo'] = $tvshows_with_seo;
    $stats['seo_completion'] = round((($movies_with_seo + $tvshows_with_seo) / ($movies_count + $tvshows_count)) * 100, 2);
    
    return $stats;
}

$seo_stats = getSeoStats();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO Tools - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4"><i class="fas fa-search"></i> SEO Tools</h1>
                
                <?php if ($message): ?>
                    <div class="alert alert-success"><?php echo $message; ?></div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <!-- SEO Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo $seo_stats['total_content']; ?></h5>
                                <p class="card-text">Total Content</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo $seo_stats['total_movies']; ?></h5>
                                <p class="card-text">Movies</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo $seo_stats['total_tvshows']; ?></h5>
                                <p class="card-text">TV Shows</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo $seo_stats['seo_completion']; ?>%</h5>
                                <p class="card-text">SEO Completion</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- SEO Actions -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-sitemap"></i> Sitemap Management</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" class="mb-3">
                                    <input type="hidden" name="action" value="generate_sitemap">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-cog"></i> Generate Sitemap
                                    </button>
                                </form>
                                
                                <form method="POST" class="mb-3">
                                    <input type="hidden" name="action" value="submit_to_google">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fab fa-google"></i> Submit to Google
                                    </button>
                                </form>
                                
                                <form method="POST">
                                    <input type="hidden" name="action" value="submit_to_bing">
                                    <button type="submit" class="btn btn-info">
                                        <i class="fab fa-microsoft"></i> Submit to Bing
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-tags"></i> Content Optimization</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" class="mb-3">
                                    <input type="hidden" name="action" value="update_meta_tags">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-edit"></i> Update Meta Tags
                                    </button>
                                </form>
                                
                                <form method="POST">
                                    <input type="hidden" name="action" value="generate_seo_urls">
                                    <button type="submit" class="btn btn-secondary">
                                        <i class="fas fa-link"></i> Generate SEO URLs
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-external-link-alt"></i> Quick Links</h5>
                            </div>
                            <div class="card-body">
                                <a href="<?php echo SITE_URL; ?>/sitemap.xml" target="_blank" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-file-code"></i> View Sitemap
                                </a>
                                <a href="<?php echo SITE_URL; ?>/robots.txt" target="_blank" class="btn btn-outline-secondary me-2">
                                    <i class="fas fa-robot"></i> View Robots.txt
                                </a>
                                <a href="https://search.google.com/search-console" target="_blank" class="btn btn-outline-success me-2">
                                    <i class="fab fa-google"></i> Google Search Console
                                </a>
                                <a href="https://www.bing.com/webmasters" target="_blank" class="btn btn-outline-info">
                                    <i class="fab fa-microsoft"></i> Bing Webmaster Tools
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
