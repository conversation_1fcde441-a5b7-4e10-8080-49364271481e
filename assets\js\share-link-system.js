/**
 * Share Link System JavaScript
 * Handles rate limiting, notifications, and user interactions
 */

class ShareLinkSystem {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.checkRateLimit();
        this.setupNotifications();
    }

    bindEvents() {
        // Copy link functionality
        document.addEventListener('click', (e) => {
            if (e.target.matches('.copy-share-link') || e.target.closest('.copy-share-link')) {
                e.preventDefault();
                this.copyShareLink(e.target);
            }
        });

        // Download link tracking
        document.addEventListener('click', (e) => {
            if (e.target.matches('.download-link') || e.target.closest('.download-link')) {
                this.trackDownload(e.target);
            }
        });

        // Streaming link tracking
        document.addEventListener('click', (e) => {
            if (e.target.matches('.stream-link') || e.target.closest('.stream-link')) {
                this.trackStreaming(e.target);
            }
        });
    }

    checkRateLimit() {
        const token = this.getTokenFromUrl();
        if (!token) return;

        fetch(`${window.location.origin}/api/check-share-limit.php?token=${token}`)
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    this.showLimitExceededModal(data.message);
                } else if (data.warning) {
                    this.showWarningNotification(data.message);
                }
            })
            .catch(error => {
                console.error('Rate limit check failed:', error);
            });
    }

    getTokenFromUrl() {
        const path = window.location.pathname;
        const matches = path.match(/\/share\/([a-zA-Z0-9_]+)/);
        return matches ? matches[1] : null;
    }

    copyShareLink(element) {
        const link = element.getAttribute('data-link') || element.closest('[data-link]').getAttribute('data-link');
        
        if (navigator.clipboard) {
            navigator.clipboard.writeText(link).then(() => {
                this.showSuccessNotification('লিংক কপি হয়েছে!');
            }).catch(() => {
                this.fallbackCopyTextToClipboard(link);
            });
        } else {
            this.fallbackCopyTextToClipboard(link);
        }
    }

    fallbackCopyTextToClipboard(text) {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";
        
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
            this.showSuccessNotification('লিংক কপি হয়েছে!');
        } catch (err) {
            this.showErrorNotification('লিংক কপি করতে সমস্যা হয়েছে');
        }
        
        document.body.removeChild(textArea);
    }

    trackDownload(element) {
        const token = this.getTokenFromUrl();
        const linkUrl = element.href;
        
        if (token) {
            fetch(`${window.location.origin}/api/track-share-action.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    token: token,
                    action: 'download',
                    link_url: linkUrl
                })
            }).catch(error => {
                console.error('Download tracking failed:', error);
            });
        }
    }

    trackStreaming(element) {
        const token = this.getTokenFromUrl();
        const linkUrl = element.href;
        
        if (token) {
            fetch(`${window.location.origin}/api/track-share-action.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    token: token,
                    action: 'stream',
                    link_url: linkUrl
                })
            }).catch(error => {
                console.error('Streaming tracking failed:', error);
            });
        }
    }

    showLimitExceededModal(message) {
        const modal = this.createModal('limit-exceeded', 'এক্সেস লিমিট শেষ!', message, [
            {
                text: 'হোম পেজে যান',
                class: 'btn-primary',
                action: () => window.location.href = '/'
            },
            {
                text: 'বন্ধ করুন',
                class: 'btn-secondary',
                action: () => this.closeModal('limit-exceeded')
            }
        ]);
        
        document.body.appendChild(modal);
        this.showModal('limit-exceeded');
    }

    showWarningNotification(message) {
        this.showNotification(message, 'warning', 5000);
    }

    showSuccessNotification(message) {
        this.showNotification(message, 'success', 3000);
    }

    showErrorNotification(message) {
        this.showNotification(message, 'error', 4000);
    }

    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `share-notification share-notification-${type}`;
        notification.innerHTML = `
            <div class="share-notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
                <button class="share-notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Auto remove
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.parentElement.removeChild(notification);
                }
            }, 300);
        }, duration);
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || icons.info;
    }

    createModal(id, title, content, buttons = []) {
        const modal = document.createElement('div');
        modal.id = `modal-${id}`;
        modal.className = 'share-modal';
        modal.innerHTML = `
            <div class="share-modal-backdrop"></div>
            <div class="share-modal-content">
                <div class="share-modal-header">
                    <h5>${title}</h5>
                    <button class="share-modal-close" onclick="shareSystem.closeModal('${id}')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="share-modal-body">
                    <p>${content}</p>
                </div>
                <div class="share-modal-footer">
                    ${buttons.map(btn => `
                        <button class="btn ${btn.class}" onclick="${btn.action.toString().replace('function ', '').replace('() => ', '').replace('()', '')}">
                            ${btn.text}
                        </button>
                    `).join('')}
                </div>
            </div>
        `;
        return modal;
    }

    showModal(id) {
        const modal = document.getElementById(`modal-${id}`);
        if (modal) {
            modal.style.display = 'flex';
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
        }
    }

    closeModal(id) {
        const modal = document.getElementById(`modal-${id}`);
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
                if (modal.parentElement) {
                    modal.parentElement.removeChild(modal);
                }
            }, 300);
        }
    }

    setupNotifications() {
        // Add notification styles if not already present
        if (!document.getElementById('share-notification-styles')) {
            const styles = document.createElement('style');
            styles.id = 'share-notification-styles';
            styles.textContent = `
                .share-notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 9999;
                    min-width: 300px;
                    max-width: 400px;
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    transform: translateX(100%);
                    transition: transform 0.3s ease;
                }

                .share-notification.show {
                    transform: translateX(0);
                }

                .share-notification-content {
                    padding: 16px;
                    display: flex;
                    align-items: center;
                    gap: 12px;
                }

                .share-notification-success {
                    border-left: 4px solid #28a745;
                }

                .share-notification-error {
                    border-left: 4px solid #dc3545;
                }

                .share-notification-warning {
                    border-left: 4px solid #ffc107;
                }

                .share-notification-info {
                    border-left: 4px solid #17a2b8;
                }

                .share-notification-close {
                    background: none;
                    border: none;
                    color: #6c757d;
                    cursor: pointer;
                    margin-left: auto;
                }

                .share-modal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 10000;
                    display: none;
                    align-items: center;
                    justify-content: center;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                }

                .share-modal.show {
                    opacity: 1;
                }

                .share-modal-backdrop {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                }

                .share-modal-content {
                    position: relative;
                    background: white;
                    border-radius: 12px;
                    max-width: 500px;
                    width: 90%;
                    max-height: 90vh;
                    overflow-y: auto;
                }

                .share-modal-header {
                    padding: 20px;
                    border-bottom: 1px solid #dee2e6;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .share-modal-body {
                    padding: 20px;
                }

                .share-modal-footer {
                    padding: 20px;
                    border-top: 1px solid #dee2e6;
                    display: flex;
                    gap: 10px;
                    justify-content: flex-end;
                }

                .share-modal-close {
                    background: none;
                    border: none;
                    font-size: 18px;
                    cursor: pointer;
                    color: #6c757d;
                }
            `;
            document.head.appendChild(styles);
        }
    }
}

// Initialize the system when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.shareSystem = new ShareLinkSystem();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ShareLinkSystem;
}
