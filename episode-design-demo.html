<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>উন্নত এপিসোড ডিজাইন - ডেমো</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/tvshow-details.css" rel="stylesheet">
    <link href="css/episode-modern.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .demo-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 0;
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(232, 62, 140, 0.1));
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        .demo-title {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #dc3545, #e83e8c, #fd7e14, #ffc107);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }
        .demo-subtitle {
            font-size: 1.2rem;
            color: #adb5bd;
            margin-bottom: 20px;
        }
        .feature-list {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }
        .feature-badge {
            background: linear-gradient(135deg, rgba(13, 110, 253, 0.2), rgba(102, 16, 242, 0.2));
            border: 1px solid rgba(13, 110, 253, 0.3);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- Demo Header -->
        <div class="demo-header">
            <h1 class="demo-title">উন্নত এপিসোড ডিজাইন</h1>
            <p class="demo-subtitle">আধুনিক, রেসপন্সিভ এবং ইন্টারঅ্যাকটিভ এপিসোড তালিকা</p>
            <div class="feature-list">
                <span class="feature-badge"><i class="fas fa-mobile-alt me-2"></i>সম্পূর্ণ রেসপন্সিভ</span>
                <span class="feature-badge"><i class="fas fa-search me-2"></i>লাইভ সার্চ</span>
                <span class="feature-badge"><i class="fas fa-magic me-2"></i>অ্যানিমেশন এফেক্ট</span>
                <span class="feature-badge"><i class="fas fa-download me-2"></i>উন্নত ডাউনলোড লিংক</span>
                <span class="feature-badge"><i class="fas fa-crown me-2"></i>প্রিমিয়াম ব্যাজ</span>
            </div>
        </div>

        <!-- Episode Search -->
        <div class="episode-search-container mb-4">
            <div class="input-group">
                <span class="input-group-text bg-dark border-secondary">
                    <i class="fas fa-search text-light"></i>
                </span>
                <input type="text" class="form-control bg-dark border-secondary text-light" 
                       placeholder="এপিসোড খুঁজুন..." id="episodeSearch">
            </div>
        </div>

        <!-- Episode List -->
        <div class="episode-list">
            <!-- Episode 1 -->
            <div class="episode-item">
                <div class="episode-header">
                    <div class="episode-number">
                        <span>S01E01</span>
                    </div>
                    <div class="episode-title">
                        <h5>পাইলট এপিসোড</h5>
                        <div class="episode-meta">
                            <span><i class="fas fa-clock me-1"></i> 45 min</span>
                            <span class="premium-tag"><i class="fas fa-crown me-1"></i> PREMIUM</span>
                        </div>
                    </div>
                </div>

                <div class="episode-content">
                    <div class="episode-thumbnail">
                        <img src="https://via.placeholder.com/200x120/333/fff?text=Episode+1" alt="Episode 1">
                    </div>
                    <div class="episode-details">
                        <p class="episode-description">এই এপিসোডে আমরা দেখব কিভাবে গল্পের শুরু হয় এবং প্রধান চরিত্রগুলোর পরিচয় পাব। একটি রহস্যময় ঘটনার মাধ্যমে পুরো সিরিজের যাত্রা শুরু হয়...</p>
                    </div>
                </div>

                <div class="episode-downloads">
                    <div class="downloads-header">
                        <i class="fas fa-download me-2"></i> Download Links
                    </div>
                    <div class="download-links">
                        <a href="#" class="download-link">
                            <div class="download-quality">1080p</div>
                            <div class="download-server" data-server="Google Drive">Google Drive</div>
                            <div class="download-size">750 MB</div>
                            <div class="download-button">
                                <i class="fas fa-download"></i>
                            </div>
                        </a>
                        <a href="#" class="download-link">
                            <div class="download-quality">720p</div>
                            <div class="download-server" data-server="MEGA">MEGA</div>
                            <div class="download-size">450 MB</div>
                            <div class="download-button">
                                <i class="fas fa-download"></i>
                            </div>
                        </a>
                        <a href="#" class="download-link">
                            <div class="download-quality">480p</div>
                            <div class="download-server" data-server="MediaFire">MediaFire</div>
                            <div class="download-size">250 MB</div>
                            <div class="download-button">
                                <i class="fas fa-download"></i>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Episode 2 -->
            <div class="episode-item">
                <div class="episode-header">
                    <div class="episode-number">
                        <span>S01E02</span>
                    </div>
                    <div class="episode-title">
                        <h5>রহস্যের গভীরে</h5>
                        <div class="episode-meta">
                            <span><i class="fas fa-clock me-1"></i> 42 min</span>
                        </div>
                    </div>
                </div>

                <div class="episode-content">
                    <div class="episode-thumbnail">
                        <img src="https://via.placeholder.com/200x120/444/fff?text=Episode+2" alt="Episode 2">
                    </div>
                    <div class="episode-details">
                        <p class="episode-description">দ্বিতীয় এপিসোডে রহস্যটি আরো গভীর হয়ে ওঠে। নতুন চরিত্রদের আবির্ভাব এবং অপ্রত্যাশিত মোড় নিয়ে গল্প এগিয়ে চলে...</p>
                    </div>
                </div>

                <div class="episode-downloads">
                    <div class="downloads-header">
                        <i class="fas fa-download me-2"></i> Download Links
                    </div>
                    <div class="download-links">
                        <a href="#" class="download-link">
                            <div class="download-quality">1080p</div>
                            <div class="download-server" data-server="Dropbox">Dropbox</div>
                            <div class="download-size">720 MB</div>
                            <div class="download-button">
                                <i class="fas fa-download"></i>
                            </div>
                        </a>
                        <a href="#" class="download-link">
                            <div class="download-quality">720p</div>
                            <div class="download-server" data-server="Direct Link">Direct Link</div>
                            <div class="download-size">420 MB</div>
                            <div class="download-button">
                                <i class="fas fa-download"></i>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Episode 3 -->
            <div class="episode-item">
                <div class="episode-header">
                    <div class="episode-number">
                        <span>S01E03</span>
                    </div>
                    <div class="episode-title">
                        <h5>নতুন আবিষ্কার</h5>
                        <div class="episode-meta">
                            <span><i class="fas fa-clock me-1"></i> 48 min</span>
                            <span class="premium-tag"><i class="fas fa-crown me-1"></i> PREMIUM</span>
                        </div>
                    </div>
                </div>

                <div class="episode-content">
                    <div class="episode-thumbnail">
                        <img src="https://via.placeholder.com/200x120/555/fff?text=Episode+3" alt="Episode 3">
                    </div>
                    <div class="episode-details">
                        <p class="episode-description">তৃতীয় এপিসোডে একটি গুরুত্বপূর্ণ আবিষ্কার হয় যা পুরো গল্পের দিক পরিবর্তন করে দেয়। প্রধান চরিত্রদের মধ্যে নতুন সম্পর্ক গড়ে ওঠে...</p>
                    </div>
                </div>

                <div class="episode-downloads">
                    <div class="downloads-header">
                        <i class="fas fa-download me-2"></i> Download Links
                    </div>
                    <div class="download-links">
                        <a href="#" class="download-link premium-link">
                            <div class="download-quality premium-quality">4K <i class="fas fa-crown"></i></div>
                            <div class="download-server premium-server" data-server="Google Drive">Google Drive Premium</div>
                            <div class="download-size premium-size">1.2 GB</div>
                            <div class="download-button">
                                <i class="fas fa-crown"></i>
                            </div>
                        </a>
                        <a href="#" class="download-link">
                            <div class="download-quality">1080p</div>
                            <div class="download-server" data-server="MEGA">MEGA</div>
                            <div class="download-size">800 MB</div>
                            <div class="download-button">
                                <i class="fas fa-download"></i>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Load More Button -->
        <button class="btn btn-outline-light load-more-btn">
            <i class="fas fa-plus me-2"></i>আরো এপিসোড দেখুন
        </button>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/episode-enhancements.js"></script>
    <script>
        // Demo specific enhancements
        document.addEventListener('DOMContentLoaded', function() {
            // Add demo notifications
            document.querySelectorAll('.download-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Show demo notification
                    const notification = document.createElement('div');
                    notification.className = 'download-notification';
                    notification.innerHTML = `
                        <div class="notification-content">
                            <i class="fas fa-info-circle text-info me-2"></i>
                            <span>এটি একটি ডেমো - প্রকৃত ডাউনলোড নয়</span>
                        </div>
                    `;
                    
                    document.body.appendChild(notification);
                    
                    setTimeout(() => {
                        notification.classList.add('fade-out');
                        setTimeout(() => notification.remove(), 300);
                    }, 3000);
                });
            });
        });
    </script>
</body>
</html>
