<?php
require_once 'config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'login_required']);
    exit;
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Get content details
$content_id = isset($_POST['content_id']) ? (int)$_POST['content_id'] : 0;
$content_type = isset($_POST['content_type']) ? sanitize($_POST['content_type']) : '';

// Validate input
if ($content_id <= 0 || ($content_type != 'movie' && $content_type != 'tvshow')) {
    echo json_encode(['success' => false, 'message' => 'Invalid content']);
    exit;
}

// Check if content exists
if ($content_type == 'movie') {
    $check_query = "SELECT id FROM movies WHERE id = $content_id";
} else {
    $check_query = "SELECT id FROM tvshows WHERE id = $content_id";
}

$check_result = mysqli_query($conn, $check_query);
if (mysqli_num_rows($check_result) == 0) {
    echo json_encode(['success' => false, 'message' => 'Content not found']);
    exit;
}

// Check if already in watchlist
$user_id = $_SESSION['user_id'];
$watchlist_check_query = "SELECT id FROM watchlist 
                         WHERE user_id = $user_id 
                         AND content_type = '$content_type' 
                         AND content_id = $content_id";
$watchlist_check_result = mysqli_query($conn, $watchlist_check_query);

if (mysqli_num_rows($watchlist_check_result) > 0) {
    echo json_encode(['success' => false, 'message' => 'already_in_watchlist']);
    exit;
}

// Add to watchlist
$insert_query = "INSERT INTO watchlist (user_id, content_type, content_id) 
                VALUES ($user_id, '$content_type', $content_id)";

if (mysqli_query($conn, $insert_query)) {
    echo json_encode(['success' => true]);
} else {
    echo json_encode(['success' => false, 'message' => mysqli_error($conn)]);
}
?>
