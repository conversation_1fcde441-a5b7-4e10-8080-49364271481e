<?php
// Set error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// This file contains functions to update import_tmdb.php and import_tmdb_tvshow.php
// to include IMDB ID extraction and saving

/**
 * Update the movie import function to save IMDB ID
 *
 * Instructions:
 * 1. Find the API URL in import_tmdb.php where movie details are fetched
 * 2. Add "external_ids" to the append_to_response parameter
 * 3. Find the INSERT query for movies table
 * 4. Add imdb_id to the column list and values
 */

// Example code to add to import_tmdb.php:
/*
// When fetching movie details from TMDB API, add external_ids to append_to_response
$api_url = "https://api.themoviedb.org/3/movie/$tmdb_id?api_key=" . $api_key . "&language=" . $language . "&append_to_response=credits,videos,external_ids";

// Extract IMDB ID from the response
$imdb_id = isset($movie_details['external_ids']['imdb_id']) ? $movie_details['external_ids']['imdb_id'] : '';

// When inserting movie into database, include IMDB ID
$query = "INSERT INTO movies (tmdb_id, imdb_id, title, category_id, ...)
         VALUES ($tmdb_id, '$imdb_id', '$title', $category_id, ...)";
*/

/**
 * Update the TV show import function to save IMDB ID
 *
 * Instructions:
 * 1. Find the API URL in import_tmdb_tvshow.php where TV show details are fetched
 * 2. Add "external_ids" to the append_to_response parameter
 * 3. Find the INSERT query for tvshows table
 * 4. Add imdb_id to the column list and values
 */

// Example code to add to import_tmdb_tvshow.php:
/*
// When fetching TV show details from TMDB API, add external_ids to append_to_response
$api_url = "https://api.themoviedb.org/3/tv/$tmdb_id?api_key=" . $api_key . "&language=" . $language . "&append_to_response=credits,videos,content_ratings,external_ids";

// Extract IMDB ID from the response
$imdb_id = isset($tvshow_details['external_ids']['imdb_id']) ? $tvshow_details['external_ids']['imdb_id'] : '';

// When inserting TV show into database, include IMDB ID
$query = "INSERT INTO tvshows (tmdb_id, imdb_id, title, category_id, ...)
         VALUES ($tmdb_id, '$imdb_id', '$title', $category_id, ...)";
*/

// HTML header for better display
echo "<!DOCTYPE html>\n";
echo "<html>\n";
echo "<head>\n";
echo "<title>Update Instructions for IMDB ID Extraction</title>\n";
echo "<style>\n";
echo "body { font-family: Arial, sans-serif; margin: 20px; }\n";
echo "pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }\n";
echo "h1, h2, h3 { color: #333; }\n";
echo "</style>\n";
echo "</head>\n";
echo "<body>\n";

echo "<h1>Update Instructions for IMDB ID Extraction</h1>";
echo "<p>This file contains instructions for updating import_tmdb.php and import_tmdb_tvshow.php to include IMDB ID extraction and saving.</p>";
echo "<p>Please follow the instructions in the code comments to update your files.</p>";

echo "<h2>Instructions for import_tmdb.php</h2>";
echo "<ol>";
echo "<li>Find the API URL in import_tmdb.php where movie details are fetched</li>";
echo "<li>Add \"external_ids\" to the append_to_response parameter</li>";
echo "<li>Find the INSERT query for movies table</li>";
echo "<li>Add imdb_id to the column list and values</li>";
echo "</ol>";

echo "<h2>Instructions for import_tmdb_tvshow.php</h2>";
echo "<ol>";
echo "<li>Find the API URL in import_tmdb_tvshow.php where TV show details are fetched</li>";
echo "<li>Add \"external_ids\" to the append_to_response parameter</li>";
echo "<li>Find the INSERT query for tvshows table</li>";
echo "<li>Add imdb_id to the column list and values</li>";
echo "</ol>";

echo "<h2>Code Samples</h2>";

echo "<h3>For Movies</h3>";
echo "<pre>";
echo htmlspecialchars("// When fetching movie details from TMDB API, add external_ids to append_to_response\n\$api_url = \"https://api.themoviedb.org/3/movie/\$tmdb_id?api_key=\" . \$api_key . \"&language=\" . \$language . \"&append_to_response=credits,videos,external_ids\";\n\n// Extract IMDB ID from the response\n\$imdb_id = isset(\$movie_details['external_ids']['imdb_id']) ? \$movie_details['external_ids']['imdb_id'] : '';\n\n// When inserting movie into database, include IMDB ID\n\$query = \"INSERT INTO movies (tmdb_id, imdb_id, title, category_id, ...)\n         VALUES (\$tmdb_id, '\$imdb_id', '\$title', \$category_id, ...)\";\n");
echo "</pre>";

echo "<h3>For TV Shows</h3>";
echo "<pre>";
echo htmlspecialchars("// When fetching TV show details from TMDB API, add external_ids to append_to_response\n\$api_url = \"https://api.themoviedb.org/3/tv/\$tmdb_id?api_key=\" . \$api_key . \"&language=\" . \$language . \"&append_to_response=credits,videos,content_ratings,external_ids\";\n\n// Extract IMDB ID from the response\n\$imdb_id = isset(\$tvshow_details['external_ids']['imdb_id']) ? \$tvshow_details['external_ids']['imdb_id'] : '';\n\n// When inserting TV show into database, include IMDB ID\n\$query = \"INSERT INTO tvshows (tmdb_id, imdb_id, title, category_id, ...)\n         VALUES (\$tmdb_id, '\$imdb_id', '\$title', \$category_id, ...)\";\n");
echo "</pre>";

echo "<p><a href='index.php'>Return to Home Page</a></p>";

echo "</body>\n";
echo "</html>\n";
?>
