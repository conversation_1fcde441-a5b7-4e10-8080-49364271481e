<?php
// Include direct config file
require_once 'api/direct_config.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: login.php");
    exit;
}

echo "<h1>Download Links Fix</h1>";

// Check if download_links table exists
$check_table = mysqli_query($conn, "SHOW TABLES LIKE 'download_links'");
if (mysqli_num_rows($check_table) == 0) {
    echo "<p style='color: red;'>Error: download_links table does not exist!</p>";
    exit;
}

// Check columns in download_links table
$columns_result = mysqli_query($conn, "SHOW COLUMNS FROM download_links");
$columns = [];
while ($column = mysqli_fetch_assoc($columns_result)) {
    $columns[] = $column['Field'];
}

echo "<h2>Current download_links table structure:</h2>";
echo "<pre>";
print_r($columns);
echo "</pre>";

// Check if url column exists
$url_exists = in_array('url', $columns);
$link_url_exists = in_array('link_url', $columns);

if (!$url_exists && $link_url_exists) {
    // Need to add url column
    echo "<p>Adding 'url' column to match API expectations...</p>";
    
    // First add the column
    $add_column = mysqli_query($conn, "ALTER TABLE download_links ADD COLUMN url TEXT");
    
    if ($add_column) {
        // Then copy data from link_url to url
        $update_data = mysqli_query($conn, "UPDATE download_links SET url = link_url");
        
        if ($update_data) {
            echo "<p style='color: green;'>Successfully added 'url' column and copied data from 'link_url'.</p>";
        } else {
            echo "<p style='color: red;'>Error updating data: " . mysqli_error($conn) . "</p>";
        }
    } else {
        echo "<p style='color: red;'>Error adding column: " . mysqli_error($conn) . "</p>";
    }
} elseif ($url_exists && !$link_url_exists) {
    // Need to add link_url column
    echo "<p>Adding 'link_url' column to match database schema...</p>";
    
    // First add the column
    $add_column = mysqli_query($conn, "ALTER TABLE download_links ADD COLUMN link_url TEXT");
    
    if ($add_column) {
        // Then copy data from url to link_url
        $update_data = mysqli_query($conn, "UPDATE download_links SET link_url = url");
        
        if ($update_data) {
            echo "<p style='color: green;'>Successfully added 'link_url' column and copied data from 'url'.</p>";
        } else {
            echo "<p style='color: red;'>Error updating data: " . mysqli_error($conn) . "</p>";
        }
    } else {
        echo "<p style='color: red;'>Error adding column: " . mysqli_error($conn) . "</p>";
    }
} elseif ($url_exists && $link_url_exists) {
    // Both columns exist, make sure they have the same data
    echo "<p>Both 'url' and 'link_url' columns exist. Synchronizing data...</p>";
    
    $sync_data = mysqli_query($conn, "UPDATE download_links SET url = link_url WHERE url != link_url OR url IS NULL");
    
    if ($sync_data) {
        echo "<p style='color: green;'>Successfully synchronized data between 'url' and 'link_url' columns.</p>";
    } else {
        echo "<p style='color: red;'>Error synchronizing data: " . mysqli_error($conn) . "</p>";
    }
} else {
    // Neither column exists, something is wrong
    echo "<p style='color: red;'>Error: Neither 'url' nor 'link_url' column exists in the download_links table!</p>";
}

// Check if server_name and file_size columns exist
$server_name_exists = in_array('server_name', $columns);
$file_size_exists = in_array('file_size', $columns);

if (!$server_name_exists) {
    echo "<p>Adding 'server_name' column...</p>";
    $add_server_name = mysqli_query($conn, "ALTER TABLE download_links ADD COLUMN server_name VARCHAR(50) DEFAULT NULL");
    
    if ($add_server_name) {
        echo "<p style='color: green;'>Successfully added 'server_name' column.</p>";
    } else {
        echo "<p style='color: red;'>Error adding 'server_name' column: " . mysqli_error($conn) . "</p>";
    }
}

if (!$file_size_exists) {
    echo "<p>Adding 'file_size' column...</p>";
    $add_file_size = mysqli_query($conn, "ALTER TABLE download_links ADD COLUMN file_size VARCHAR(20) DEFAULT NULL");
    
    if ($add_file_size) {
        echo "<p style='color: green;'>Successfully added 'file_size' column.</p>";
    } else {
        echo "<p style='color: red;'>Error adding 'file_size' column: " . mysqli_error($conn) . "</p>";
    }
}

// Check if is_premium column exists
$is_premium_exists = in_array('is_premium', $columns);

if (!$is_premium_exists) {
    echo "<p>Adding 'is_premium' column...</p>";
    $add_is_premium = mysqli_query($conn, "ALTER TABLE download_links ADD COLUMN is_premium BOOLEAN DEFAULT FALSE");
    
    if ($add_is_premium) {
        echo "<p style='color: green;'>Successfully added 'is_premium' column.</p>";
    } else {
        echo "<p style='color: red;'>Error adding 'is_premium' column: " . mysqli_error($conn) . "</p>";
    }
}

// Check episode_links table
$check_episode_links = mysqli_query($conn, "SHOW TABLES LIKE 'episode_links'");
if (mysqli_num_rows($check_episode_links) == 0) {
    echo "<p>Table 'episode_links' does not exist. Creating it...</p>";
    
    $create_episode_links = mysqli_query($conn, "
        CREATE TABLE episode_links (
            id INT AUTO_INCREMENT PRIMARY KEY,
            episode_id INT NOT NULL,
            link_type ENUM('download', 'stream') NOT NULL,
            quality VARCHAR(20) NOT NULL COMMENT 'e.g. 720p, 1080p, 4K',
            server_name VARCHAR(50) DEFAULT NULL COMMENT 'For streaming links',
            url TEXT NOT NULL,
            file_size VARCHAR(20) DEFAULT NULL,
            is_premium BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (episode_id) REFERENCES episodes(id) ON DELETE CASCADE
        )
    ");
    
    if ($create_episode_links) {
        echo "<p style='color: green;'>Successfully created 'episode_links' table.</p>";
        
        // Copy data from download_links to episode_links
        echo "<p>Copying episode links from download_links to episode_links...</p>";
        
        $copy_data = mysqli_query($conn, "
            INSERT INTO episode_links (episode_id, link_type, quality, url, is_premium)
            SELECT content_id, 'download', quality, url, is_premium
            FROM download_links
            WHERE content_type = 'episode'
        ");
        
        if ($copy_data) {
            echo "<p style='color: green;'>Successfully copied episode links from download_links to episode_links.</p>";
        } else {
            echo "<p style='color: red;'>Error copying data: " . mysqli_error($conn) . "</p>";
        }
    } else {
        echo "<p style='color: red;'>Error creating 'episode_links' table: " . mysqli_error($conn) . "</p>";
    }
} else {
    echo "<p>Table 'episode_links' exists. Checking structure...</p>";
    
    // Check columns in episode_links table
    $ep_columns_result = mysqli_query($conn, "SHOW COLUMNS FROM episode_links");
    $ep_columns = [];
    while ($column = mysqli_fetch_assoc($ep_columns_result)) {
        $ep_columns[] = $column['Field'];
    }
    
    echo "<pre>";
    print_r($ep_columns);
    echo "</pre>";
    
    // Check if url column exists
    $ep_url_exists = in_array('url', $ep_columns);
    $ep_link_url_exists = in_array('link_url', $ep_columns);
    
    if (!$ep_url_exists && $ep_link_url_exists) {
        echo "<p>Adding 'url' column to episode_links...</p>";
        $add_url = mysqli_query($conn, "ALTER TABLE episode_links ADD COLUMN url TEXT");
        
        if ($add_url) {
            $update_url = mysqli_query($conn, "UPDATE episode_links SET url = link_url");
            
            if ($update_url) {
                echo "<p style='color: green;'>Successfully added 'url' column and copied data from 'link_url'.</p>";
            } else {
                echo "<p style='color: red;'>Error updating data: " . mysqli_error($conn) . "</p>";
            }
        } else {
            echo "<p style='color: red;'>Error adding column: " . mysqli_error($conn) . "</p>";
        }
    } elseif ($ep_url_exists && !$ep_link_url_exists) {
        echo "<p>Adding 'link_url' column to episode_links...</p>";
        $add_link_url = mysqli_query($conn, "ALTER TABLE episode_links ADD COLUMN link_url TEXT");
        
        if ($add_link_url) {
            $update_link_url = mysqli_query($conn, "UPDATE episode_links SET link_url = url");
            
            if ($update_link_url) {
                echo "<p style='color: green;'>Successfully added 'link_url' column and copied data from 'url'.</p>";
            } else {
                echo "<p style='color: red;'>Error updating data: " . mysqli_error($conn) . "</p>";
            }
        } else {
            echo "<p style='color: red;'>Error adding column: " . mysqli_error($conn) . "</p>";
        }
    }
    
    // Check if file_size column exists
    $ep_file_size_exists = in_array('file_size', $ep_columns);
    
    if (!$ep_file_size_exists) {
        echo "<p>Adding 'file_size' column to episode_links...</p>";
        $add_file_size = mysqli_query($conn, "ALTER TABLE episode_links ADD COLUMN file_size VARCHAR(20) DEFAULT NULL");
        
        if ($add_file_size) {
            echo "<p style='color: green;'>Successfully added 'file_size' column.</p>";
        } else {
            echo "<p style='color: red;'>Error adding 'file_size' column: " . mysqli_error($conn) . "</p>";
        }
    }
}

echo "<h2>Fix Complete</h2>";
echo "<p>The database structure has been updated to support the API.</p>";
echo "<p><a href='admin/index.php'>Return to Admin Dashboard</a></p>";
?>
