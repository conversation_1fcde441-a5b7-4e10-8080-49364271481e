<?php
require_once 'includes/config.php';

echo "<h2>সহজ শেয়ার লিংক টেস্ট</h2>";

// The token you mentioned
$token = 'f09f1306224549b69631e43075707e60_1751652803';

echo "<h3>টেস্ট করা টোকেন:</h3>";
echo "<p><code>$token</code></p>";

// Clean token
$clean_token = mysqli_real_escape_string($conn, $token);

// Simple query - just check if share link exists
$simple_query = "SELECT * FROM shared_links WHERE link_token = '$clean_token'";
echo "<h3>সহজ কুয়েরি:</h3>";
echo "<pre>" . htmlspecialchars($simple_query) . "</pre>";

$simple_result = mysqli_query($conn, $simple_query);

if (!$simple_result) {
    echo "<div style='color: red; padding: 10px; margin: 10px 0; border: 1px solid red;'>";
    echo "<h3>❌ কুয়েরি ফেইল:</h3>";
    echo "<p>" . mysqli_error($conn) . "</p>";
    echo "</div>";
    exit;
}

$num_rows = mysqli_num_rows($simple_result);

echo "<h3>ফলাফল:</h3>";
echo "<p>সারি সংখ্যা: $num_rows</p>";

if ($num_rows == 0) {
    echo "<div style='color: red; padding: 10px; margin: 10px 0; border: 1px solid red;'>";
    echo "<h3>❌ শেয়ার লিংক নেই</h3>";
    echo "<p>টোকেন: $token</p>";
    echo "</div>";
} else {
    echo "<div style='color: green; padding: 10px; margin: 10px 0; border: 1px solid green;'>";
    echo "<h3>✅ শেয়ার লিংক পাওয়া গেছে!</h3>";
    $shared_link = mysqli_fetch_assoc($simple_result);
    echo "<pre>" . print_r($shared_link, true) . "</pre>";
    echo "</div>";
    
    // Now try to get content details separately
    if ($shared_link['content_type'] == 'movie') {
        $content_query = "SELECT id, title, description, poster, banner, rating, release_year FROM movies WHERE id = " . $shared_link['content_id'];
    } else {
        $content_query = "SELECT id, title, description, poster, banner, rating, year FROM tvshows WHERE id = " . $shared_link['content_id'];
    }
    
    echo "<h3>কন্টেন্ট কুয়েরি:</h3>";
    echo "<pre>" . htmlspecialchars($content_query) . "</pre>";
    
    $content_result = mysqli_query($conn, $content_query);
    
    if (!$content_result) {
        echo "<div style='color: red; padding: 10px; margin: 10px 0; border: 1px solid red;'>";
        echo "<h3>❌ কন্টেন্ট কুয়েরি ফেইল:</h3>";
        echo "<p>" . mysqli_error($conn) . "</p>";
        echo "</div>";
    } else {
        $content_rows = mysqli_num_rows($content_result);
        echo "<p>কন্টেন্ট সারি সংখ্যা: $content_rows</p>";
        
        if ($content_rows > 0) {
            $content = mysqli_fetch_assoc($content_result);
            echo "<div style='color: green; padding: 10px; margin: 10px 0; border: 1px solid green;'>";
            echo "<h3>✅ কন্টেন্ট পাওয়া গেছে!</h3>";
            echo "<pre>" . print_r($content, true) . "</pre>";
            echo "</div>";
            
            // Create test URLs
            $share_url = SITE_URL . '/share_fixed.php?token=' . $token;
            $debug_url = SITE_URL . '/share_fixed.php?token=' . $token . '&debug=1';
            
            echo "<h3>টেস্ট লিংক:</h3>";
            echo "<p><a href='$debug_url' target='_blank' style='color: green; font-size: 18px;'>🔗 ডিবাগ লিংক</a></p>";
            echo "<p><a href='$share_url' target='_blank' style='color: blue; font-size: 18px;'>🔗 সাধারণ লিংক</a></p>";
        } else {
            echo "<div style='color: orange; padding: 10px; margin: 10px 0; border: 1px solid orange;'>";
            echo "<h3>❌ কন্টেন্ট নেই</h3>";
            echo "<p>কন্টেন্ট ID: " . $shared_link['content_id'] . "</p>";
            echo "<p>কন্টেন্ট টাইপ: " . $shared_link['content_type'] . "</p>";
            echo "</div>";
        }
    }
}
?> 