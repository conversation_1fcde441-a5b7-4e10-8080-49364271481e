class Episode {
  final int id;
  final int tvshowId;
  final int seasonNumber;
  final int episodeNumber;
  final String title;
  final String description;
  final int duration;
  final String thumbnail;
  final String releaseDate;
  final bool isPremium;

  Episode({
    required this.id,
    required this.tvshowId,
    required this.seasonNumber,
    required this.episodeNumber,
    required this.title,
    required this.description,
    required this.duration,
    required this.thumbnail,
    required this.releaseDate,
    required this.isPremium,
  });

  factory Episode.fromJson(Map<String, dynamic> json) {
    return Episode(
      id: json['id'] ?? 0,
      tvshowId: json['tvshow_id'] ?? 0,
      seasonNumber: json['season_number'] ?? 0,
      episodeNumber: json['episode_number'] ?? 0,
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      duration: json['duration'] ?? 0,
      thumbnail: json['thumbnail'] ?? '',
      releaseDate: json['release_date'] ?? '',
      isPremium: json['is_premium'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'tvshow_id': tvshowId,
      'season_number': seasonNumber,
      'episode_number': episodeNumber,
      'title': title,
      'description': description,
      'duration': duration,
      'thumbnail': thumbnail,
      'release_date': releaseDate,
      'is_premium': isPremium,
    };
  }
}
