<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CinePix Admin - নতুন ডিজাইন টেস্ট</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
            color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .topbar {
            background: linear-gradient(90deg, #e50914, #b20710);
            padding: 1rem 0;
            box-shadow: 0 4px 20px rgba(229, 9, 20, 0.3);
        }

        .main-content {
            padding: 2rem;
        }

        .page-header {
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid #333;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            text-align: center;
        }

        .page-title {
            font-size: 3rem;
            font-weight: 800;
            background: linear-gradient(45deg, #e50914, #ff6b6b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .success-message {
            background: linear-gradient(135deg, #28a745, #20c997);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(40, 167, 69, 0.3);
        }

        .feature-card {
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            border: 1px solid #333;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(229, 9, 20, 0.2);
        }

        .feature-card::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #e50914, #ff6b6b, #ffa726);
            border-radius: 15px 15px 0 0;
        }

        .btn-primary {
            background: linear-gradient(45deg, #e50914, #ff4757);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(229, 9, 20, 0.4);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .status-success { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-danger { background: #dc3545; }

        /* Mobile Responsive */
        @media (max-width: 575.98px) {
            .main-content {
                padding: 1rem;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .page-header {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Topbar -->
    <nav class="navbar topbar">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="#">
                <i class="fas fa-film me-2"></i>
                <span style="color: #e50914;">CINE</span><span>PIX</span>
                <span class="ms-2 badge bg-light text-dark">Admin Panel</span>
            </a>
            
            <div class="text-white">
                <i class="fas fa-check-circle text-success me-2"></i>
                নতুন ডিজাইন সফলভাবে লোড হয়েছে!
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-rocket me-3"></i>
                নতুন এডমিন প্যানেল
            </h1>
            <p class="text-muted mb-4">আধুনিক ডিজাইন সফলভাবে কাজ করছে!</p>
            
            <div class="row justify-content-center">
                <div class="col-auto">
                    <a href="index.php" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-tachometer-alt me-2"></i>ড্যাশবোর্ড দেখুন
                    </a>
                    <a href="mobile_test.php" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-mobile-alt me-2"></i>মোবাইল টেস্ট
                    </a>
                </div>
            </div>
        </div>

        <!-- Success Message -->
        <div class="success-message">
            <h3 class="text-white mb-3">
                <i class="fas fa-check-circle me-2"></i>
                সব কিছু সঠিকভাবে কাজ করছে!
            </h3>
            <p class="text-white mb-0">
                নতুন এডমিন প্যানেল সফলভাবে ইনস্টল এবং কনফিগার করা হয়েছে।
            </p>
        </div>

        <!-- Feature Status -->
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="feature-card position-relative">
                    <h5 class="text-white mb-3">
                        <i class="fas fa-palette me-2"></i>ডিজাইন ফিচার
                    </h5>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <span class="status-indicator status-success"></span>
                            আধুনিক ডার্ক থিম
                        </li>
                        <li class="mb-2">
                            <span class="status-indicator status-success"></span>
                            গ্র্যাডিয়েন্ট ইফেক্ট
                        </li>
                        <li class="mb-2">
                            <span class="status-indicator status-success"></span>
                            গ্লাস মরফিজম
                        </li>
                        <li class="mb-2">
                            <span class="status-indicator status-success"></span>
                            স্মুথ অ্যানিমেশন
                        </li>
                    </ul>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="feature-card position-relative">
                    <h5 class="text-white mb-3">
                        <i class="fas fa-mobile-alt me-2"></i>রেস্পন্সিভ ফিচার
                    </h5>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <span class="status-indicator status-success"></span>
                            মোবাইল অপ্টিমাইজড
                        </li>
                        <li class="mb-2">
                            <span class="status-indicator status-success"></span>
                            ট্যাবলেট সাপোর্ট
                        </li>
                        <li class="mb-2">
                            <span class="status-indicator status-success"></span>
                            ডেস্কটপ রেডি
                        </li>
                        <li class="mb-2">
                            <span class="status-indicator status-success"></span>
                            টাচ ফ্রেন্ডলি
                        </li>
                    </ul>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="feature-card position-relative">
                    <h5 class="text-white mb-3">
                        <i class="fas fa-cogs me-2"></i>ফাংশনাল ফিচার
                    </h5>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <span class="status-indicator status-success"></span>
                            ইন্টারঅ্যাক্টিভ চার্ট
                        </li>
                        <li class="mb-2">
                            <span class="status-indicator status-success"></span>
                            রিয়েল-টাইম ডেটা
                        </li>
                        <li class="mb-2">
                            <span class="status-indicator status-success"></span>
                            AJAX সাপোর্ট
                        </li>
                        <li class="mb-2">
                            <span class="status-indicator status-success"></span>
                            নোটিফিকেশন সিস্টেম
                        </li>
                    </ul>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="feature-card position-relative">
                    <h5 class="text-white mb-3">
                        <i class="fas fa-shield-alt me-2"></i>নিরাপত্তা ফিচার
                    </h5>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <span class="status-indicator status-success"></span>
                            সেশন ম্যানেজমেন্ট
                        </li>
                        <li class="mb-2">
                            <span class="status-indicator status-success"></span>
                            অ্যাক্সেস কন্ট্রোল
                        </li>
                        <li class="mb-2">
                            <span class="status-indicator status-success"></span>
                            ডেটা ভ্যালিডেশন
                        </li>
                        <li class="mb-2">
                            <span class="status-indicator status-success"></span>
                            এরর হ্যান্ডলিং
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Navigation Links -->
        <div class="row">
            <div class="col-12">
                <div class="feature-card position-relative text-center">
                    <h5 class="text-white mb-4">
                        <i class="fas fa-link me-2"></i>দ্রুত নেভিগেশন
                    </h5>
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="index.php" class="btn btn-outline-primary w-100">
                                <i class="fas fa-tachometer-alt me-2"></i>ড্যাশবোর্ড
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="movies.php" class="btn btn-outline-success w-100">
                                <i class="fas fa-film me-2"></i>মুভি
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="users.php" class="btn btn-outline-warning w-100">
                                <i class="fas fa-users me-2"></i>ইউজার
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="mobile_test.php" class="btn btn-outline-info w-100">
                                <i class="fas fa-mobile-alt me-2"></i>মোবাইল টেস্ট
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate feature cards on load
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });

            // Show success message
            setTimeout(() => {
                alert('🎉 নতুন এডমিন প্যানেল সফলভাবে লোড হয়েছে!\n\n✅ আধুনিক ডিজাইন\n✅ মোবাইল রেস্পন্সিভ\n✅ সব ফিচার কাজ করছে');
            }, 1000);
        });
    </script>
</body>
</html>
