/* Tags styling */
.tag-badge {
    display: inline-block;
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 4px;
    margin-right: 4px;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.tag-badge:last-child {
    margin-right: 0;
}

.movie-card .tag-badges {
    position: absolute;
    bottom: 5px;
    left: 5px;
    z-index: 2;
    display: flex;
    flex-wrap: wrap;
    max-width: calc(100% - 10px);
}

.movie-card:hover .tag-badges {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.movie-card-overlay .tag-badges {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 5px;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.movie-card:hover .movie-card-overlay .tag-badges {
    opacity: 1;
    transform: translateY(0);
}

/* Different colors for different tags */
.tag-badge.tag-bangla-dubbed {
    background-color: rgba(0, 123, 255, 0.8);
}

.tag-badge.tag-hindi {
    background-color: rgba(220, 53, 69, 0.8);
}

.tag-badge.tag-dual-audio {
    background-color: rgba(255, 193, 7, 0.8);
    color: #000;
}

.tag-badge.tag-telugu {
    background-color: rgba(40, 167, 69, 0.8);
}

.tag-badge.tag-english {
    background-color: rgba(111, 66, 193, 0.8);
}

.tag-badge.tag-tamil {
    background-color: rgba(23, 162, 184, 0.8);
}

.tag-badge.tag-malayalam {
    background-color: rgba(253, 126, 20, 0.8);
}

/* Responsive styles */
@media (max-width: 767.98px) {
    .tag-badge {
        font-size: 0.65rem;
        padding: 1px 4px;
        margin-right: 3px;
        margin-bottom: 3px;
    }
}

@media (max-width: 575.98px) {
    .tag-badge {
        font-size: 0.6rem;
        padding: 1px 3px;
        margin-right: 2px;
        margin-bottom: 2px;
    }
}
