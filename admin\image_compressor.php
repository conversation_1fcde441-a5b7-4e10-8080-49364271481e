<?php
// Include configuration file
require_once '../config.php';

// Include database connection
require_once '../db_connect.php';

// Include functions
require_once '../functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

// Set page title
$page_title = 'ইমেজ কম্প্রেসর';

// Process image compression
$success_message = '';
$error_message = '';
$original_size = 0;
$compressed_size = 0;
$compression_ratio = 0;
$original_image = '';
$compressed_image = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['compress_image'])) {
    // Check if file was uploaded
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        // Get file details
        $file_tmp = $_FILES['image']['tmp_name'];
        $file_name = $_FILES['image']['name'];
        $file_size = $_FILES['image']['size'];
        $file_type = $_FILES['image']['type'];
        
        // Check file type
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        if (in_array($file_type, $allowed_types)) {
            // Get compression quality
            $quality = isset($_POST['quality']) ? intval($_POST['quality']) : 80;
            
            // Create uploads directory if it doesn't exist
            $upload_dir = '../uploads/compressed/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            
            // Generate unique filenames
            $original_filename = 'original_' . time() . '_' . $file_name;
            $compressed_filename = 'compressed_' . time() . '_' . $file_name;
            
            // Save original image
            $original_path = $upload_dir . $original_filename;
            move_uploaded_file($file_tmp, $original_path);
            $original_size = filesize($original_path);
            
            // Compress image
            $compressed_path = $upload_dir . $compressed_filename;
            $image_info = getimagesize($original_path);
            
            if ($image_info !== false) {
                $width = $image_info[0];
                $height = $image_info[1];
                $mime = $image_info['mime'];
                
                // Create image resource based on type
                switch ($mime) {
                    case 'image/jpeg':
                    case 'image/jpg':
                        $image = imagecreatefromjpeg($original_path);
                        break;
                    case 'image/png':
                        $image = imagecreatefrompng($original_path);
                        // Set alpha blending
                        imagealphablending($image, true);
                        imagesavealpha($image, true);
                        break;
                    case 'image/webp':
                        $image = imagecreatefromwebp($original_path);
                        break;
                    default:
                        $image = false;
                }
                
                if ($image !== false) {
                    // Compress and save image
                    switch ($mime) {
                        case 'image/jpeg':
                        case 'image/jpg':
                            imagejpeg($image, $compressed_path, $quality);
                            break;
                        case 'image/png':
                            // PNG quality is 0-9, convert from percentage
                            $png_quality = 9 - round(($quality / 100) * 9);
                            imagepng($image, $compressed_path, $png_quality);
                            break;
                        case 'image/webp':
                            imagewebp($image, $compressed_path, $quality);
                            break;
                    }
                    
                    // Free memory
                    imagedestroy($image);
                    
                    // Get compressed size
                    $compressed_size = filesize($compressed_path);
                    
                    // Calculate compression ratio
                    $compression_ratio = round(($original_size - $compressed_size) / $original_size * 100, 2);
                    
                    // Set success message
                    $success_message = 'ইমেজ সফলভাবে কম্প্রেস করা হয়েছে। সাইজ ' . formatFileSize($original_size) . ' থেকে ' . formatFileSize($compressed_size) . ' এ কমেছে (' . $compression_ratio . '% কম্প্রেশন)';
                    
                    // Set image paths for display
                    $original_image = SITE_URL . '/uploads/compressed/' . $original_filename;
                    $compressed_image = SITE_URL . '/uploads/compressed/' . $compressed_filename;
                } else {
                    $error_message = 'ইমেজ প্রসেস করতে সমস্যা হয়েছে।';
                }
            } else {
                $error_message = 'ইমেজ তথ্য পড়তে সমস্যা হয়েছে।';
            }
        } else {
            $error_message = 'অনুমোদিত ইমেজ ফরম্যাট নয়। শুধুমাত্র JPG, PNG এবং WebP ফরম্যাট সমর্থিত।';
        }
    } else {
        $error_message = 'ইমেজ আপলোড করতে সমস্যা হয়েছে: ' . $_FILES['image']['error'];
    }
}

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';

// Helper function to format file size
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $i = 0;
    while ($bytes >= 1024 && $i < count($units) - 1) {
        $bytes /= 1024;
        $i++;
    }
    return round($bytes, 2) . ' ' . $units[$i];
}
?>

<!-- Content Wrapper -->
<div class="content">
    <!-- Topbar -->
    <?php include 'includes/topbar.php'; ?>

    <!-- Begin Page Content -->
    <div class="container-fluid px-4">
        <!-- Page Heading -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0 text-gray-800">ইমেজ কম্প্রেসর</h1>
        </div>

        <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="row">
            <!-- Compression Form -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">ইমেজ কম্প্রেস করুন</h6>
                    </div>
                    <div class="card-body">
                        <form method="post" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="image" class="form-label">ইমেজ নির্বাচন করুন</label>
                                <input type="file" class="form-control" id="image" name="image" accept="image/jpeg,image/png,image/webp" required>
                                <div class="form-text">সমর্থিত ফরম্যাট: JPG, PNG, WebP</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="quality" class="form-label">কম্প্রেশন কোয়ালিটি: <span id="qualityValue">80%</span></label>
                                <input type="range" class="form-range" id="quality" name="quality" min="10" max="100" value="80">
                                <div class="row text-center mt-2">
                                    <div class="col-4">
                                        <button type="button" class="btn btn-sm btn-outline-secondary w-100" data-quality="60">
                                            উচ্চ কম্প্রেশন
                                        </button>
                                    </div>
                                    <div class="col-4">
                                        <button type="button" class="btn btn-sm btn-outline-primary w-100" data-quality="80">
                                            ব্যালেন্সড
                                        </button>
                                    </div>
                                    <div class="col-4">
                                        <button type="button" class="btn btn-sm btn-outline-success w-100" data-quality="90">
                                            উচ্চ কোয়ালিটি
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" name="compress_image" class="btn btn-primary">
                                    <i class="fas fa-compress me-1"></i> ইমেজ কম্প্রেস করুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Compression Tips -->
                <div class="card shadow mt-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">কম্প্রেশন টিপস</h6>
                    </div>
                    <div class="card-body">
                        <ul class="mb-0">
                            <li>ওয়েবসাইটের জন্য 60-80% কোয়ালিটি সাধারণত যথেষ্ট।</li>
                            <li>PNG ফরম্যাট ট্রান্সপারেন্সি সমর্থন করে, কিন্তু JPG এর তুলনায় বড় হয়।</li>
                            <li>WebP ফরম্যাট সাধারণত সবচেয়ে ভালো কম্প্রেশন অফার করে।</li>
                            <li>পোস্টার ইমেজের জন্য 300x450px রেজোলিউশন ব্যবহার করুন।</li>
                            <li>ব্যাকড্রপ ইমেজের জন্য 1280x720px রেজোলিউশন ব্যবহার করুন।</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Compression Results -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">কম্প্রেশন ফলাফল</h6>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($original_image) && !empty($compressed_image)): ?>
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header py-2">
                                            <h6 class="m-0 font-weight-bold">আসল ইমেজ</h6>
                                        </div>
                                        <div class="card-body p-2">
                                            <img src="<?php echo $original_image; ?>" class="img-fluid" alt="Original Image">
                                        </div>
                                        <div class="card-footer py-2">
                                            <small class="text-muted">সাইজ: <?php echo formatFileSize($original_size); ?></small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header py-2">
                                            <h6 class="m-0 font-weight-bold">কম্প্রেসড ইমেজ</h6>
                                        </div>
                                        <div class="card-body p-2">
                                            <img src="<?php echo $compressed_image; ?>" class="img-fluid" alt="Compressed Image">
                                        </div>
                                        <div class="card-footer py-2">
                                            <small class="text-muted">সাইজ: <?php echo formatFileSize($compressed_size); ?></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card mb-4">
                                <div class="card-header py-2">
                                    <h6 class="m-0 font-weight-bold">কম্প্রেশন স্ট্যাটিসটিকস</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-md-4 mb-3">
                                            <h5 class="font-weight-bold"><?php echo formatFileSize($original_size); ?></h5>
                                            <small class="text-muted">আসল সাইজ</small>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <h5 class="font-weight-bold"><?php echo formatFileSize($compressed_size); ?></h5>
                                            <small class="text-muted">কম্প্রেসড সাইজ</small>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <h5 class="font-weight-bold text-success"><?php echo $compression_ratio; ?>%</h5>
                                            <small class="text-muted">সাইজ কমেছে</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <a href="<?php echo $compressed_image; ?>" download class="btn btn-success">
                                    <i class="fas fa-download me-1"></i> কম্প্রেসড ইমেজ ডাউনলোড করুন
                                </a>
                                <a href="<?php echo $original_image; ?>" download class="btn btn-outline-secondary">
                                    <i class="fas fa-download me-1"></i> আসল ইমেজ ডাউনলোড করুন
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-image fa-4x text-muted mb-3"></i>
                                <p>কম্প্রেশন ফলাফল দেখতে একটি ইমেজ আপলোড করুন।</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Batch Compression (Coming Soon) -->
        <div class="row">
            <div class="col-12 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">ব্যাচ কম্প্রেশন (শীঘ্রই আসছে)</h6>
                    </div>
                    <div class="card-body text-center py-5">
                        <i class="fas fa-layer-group fa-4x text-muted mb-3"></i>
                        <h5>একসাথে অনেকগুলো ইমেজ কম্প্রেস করুন</h5>
                        <p class="text-muted">এই ফিচারটি শীঘ্রই যোগ করা হবে। এটি আপনাকে একসাথে অনেকগুলো ইমেজ আপলোড এবং কম্প্রেস করতে সাহায্য করবে।</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Quality slider
    const qualitySlider = document.getElementById('quality');
    const qualityValue = document.getElementById('qualityValue');
    
    qualitySlider.addEventListener('input', function() {
        qualityValue.textContent = this.value + '%';
    });
    
    // Quality preset buttons
    const qualityButtons = document.querySelectorAll('[data-quality]');
    qualityButtons.forEach(button => {
        button.addEventListener('click', function() {
            const quality = this.getAttribute('data-quality');
            qualitySlider.value = quality;
            qualityValue.textContent = quality + '%';
        });
    });
    
    // Image preview
    const imageInput = document.getElementById('image');
    imageInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                // You could add preview functionality here if needed
            };
            reader.readAsDataURL(this.files[0]);
        }
    });
});
</script>
