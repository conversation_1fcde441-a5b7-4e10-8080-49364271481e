<?php
// Include configuration file
require_once '../config.php';

// Include database connection
require_once '../db_connect.php';

// Include functions
require_once '../functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

// Get theme preference
$theme = isset($_GET['theme']) ? $_GET['theme'] : 'light';

// Save theme preference in session
$_SESSION['admin_theme'] = $theme;

// Redirect back to previous page or dashboard
$redirect = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : 'index.php';
header('Location: ' . $redirect);
exit;
?>
