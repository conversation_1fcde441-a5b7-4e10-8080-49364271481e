<?php
// Include direct config file
require_once '../direct_config.php';

// Check if user is authenticated
$auth_header = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
$token = null;

if (preg_match('/Bearer\s(\S+)/', $auth_header, $matches)) {
    $token = $matches[1];
}

if (!$token) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Authentication required',
        'data' => null
    ]);
    exit;
}

// Verify token
$payload = verify_jwt($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Invalid or expired token',
        'data' => null
    ]);
    exit;
}

$user_id = $payload['user_id'];

// Get user profile
$query = "SELECT id, username, email, name, role, profile_image, is_premium, premium_expires, created_at 
          FROM users 
          WHERE id = $user_id";

$result = mysqli_query($conn, $query);

if (!$result || mysqli_num_rows($result) === 0) {
    http_response_code(404);
    echo json_encode([
        'success' => false,
        'message' => 'User not found',
        'data' => null
    ]);
    exit;
}

$user = mysqli_fetch_assoc($result);

// Get favorites count
$favorites_query = "SELECT COUNT(*) as count FROM favorites WHERE user_id = $user_id";
$favorites_result = mysqli_query($conn, $favorites_query);
$favorites_count = mysqli_fetch_assoc($favorites_result)['count'];

// Get watchlist count
$watchlist_query = "SELECT COUNT(*) as count FROM watchlist WHERE user_id = $user_id";
$watchlist_result = mysqli_query($conn, $watchlist_query);
$watchlist_count = mysqli_fetch_assoc($watchlist_result)['count'];

// Get active subscription
$subscription_query = "SELECT s.*, p.name as plan_name, p.price as plan_price, p.duration as plan_duration 
                      FROM subscriptions s
                      JOIN premium_plans p ON s.plan_id = p.id
                      WHERE s.user_id = $user_id AND s.status = 'active'
                      ORDER BY s.created_at DESC
                      LIMIT 1";

$subscription_result = mysqli_query($conn, $subscription_query);
$subscription = null;

if ($subscription_result && mysqli_num_rows($subscription_result) > 0) {
    $sub = mysqli_fetch_assoc($subscription_result);
    $subscription = [
        'id' => (int)$sub['id'],
        'plan_id' => (int)$sub['plan_id'],
        'plan_name' => $sub['plan_name'],
        'plan_price' => (float)$sub['plan_price'],
        'plan_duration' => (int)$sub['plan_duration'],
        'start_date' => $sub['start_date'],
        'end_date' => $sub['end_date'],
        'status' => $sub['status'],
        'payment_method' => $sub['payment_method'],
        'transaction_id' => $sub['transaction_id'],
        'created_at' => $sub['created_at']
    ];
}

// Return user profile
http_response_code(200);
echo json_encode([
    'success' => true,
    'message' => 'Success',
    'data' => [
        'user' => [
            'id' => (int)$user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'name' => $user['name'],
            'role' => $user['role'],
            'profile_image' => $user['profile_image'],
            'is_premium' => (bool)$user['is_premium'],
            'premium_expires' => $user['premium_expires'],
            'created_at' => $user['created_at'],
            'favorites_count' => (int)$favorites_count,
            'watchlist_count' => (int)$watchlist_count
        ],
        'subscription' => $subscription
    ]
]);
?>
