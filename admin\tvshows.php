<?php
// Set page title
$page_title = 'TV Shows';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Display success or error messages from session
if (isset($_SESSION['success_message'])) {
    $success_message = $_SESSION['success_message'];
    unset($_SESSION['success_message']);
}

if (isset($_SESSION['error_message'])) {
    $error_message = $_SESSION['error_message'];
    unset($_SESSION['error_message']);
}

// Check if is_active column exists in tvshows table
$check_column_query = "SHOW COLUMNS FROM tvshows LIKE 'is_active'";
$check_column_result = mysqli_query($conn, $check_column_query);

if (mysqli_num_rows($check_column_result) == 0) {
    // Add is_active column if it doesn't exist
    $add_column_query = "ALTER TABLE tvshows ADD COLUMN is_active BOOLEAN DEFAULT TRUE";
    mysqli_query($conn, $add_column_query);

    // Update existing tvshows to be active
    $update_query = "UPDATE tvshows SET is_active = TRUE WHERE is_active IS NULL";
    mysqli_query($conn, $update_query);
}

// Get TV shows list
$tvshows_query = "SELECT t.*, c.name as category_name FROM tvshows t
                 LEFT JOIN categories c ON t.category_id = c.id
                 ORDER BY t.created_at DESC";
$tvshows_result = mysqli_query($conn, $tvshows_query);

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>TV Shows</h1>
            </div>
            <div class="topbar-actions">
                <div class="topbar-search">
                    <form action="search.php" method="GET">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="Search TV shows..." name="q">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
                <a href="add_tvshow.php" class="btn btn-primary">
                    <i class="fas fa-plus-circle me-2"></i>Add New TV Show
                </a>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">All TV Shows</h5>
                <div>
                    <a href="import_tmdb_tvshow.php" class="btn btn-sm btn-warning">
                        <i class="fas fa-file-import me-1"></i> Import from TMDB
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover datatable" id="tvshowsTable">
                        <thead>
                            <tr>
                                <th class="actions-column">Actions</th>
                                <th>Share</th>
                                <th>ID</th>
                                <th>Title</th>
                                <th>Category</th>
                                <th>Year</th>
                                <th>Seasons</th>
                                <th>Rating</th>
                                <th>Premium</th>
                                <th>Featured</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if(mysqli_num_rows($tvshows_result) > 0): ?>
                                <?php while($tvshow = mysqli_fetch_assoc($tvshows_result)): ?>
                                <tr>
                                    <td class="actions-column">
                                        <!-- Desktop View Buttons -->
                                        <div class="action-buttons d-none d-md-flex">
                                            <a href="edit_tvshow.php?id=<?php echo $tvshow['id']; ?>" class="btn btn-sm btn-primary me-1" data-bs-toggle="tooltip" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="manage_episodes.php?tvshow=<?php echo $tvshow['id']; ?>" class="btn btn-sm btn-warning me-1" data-bs-toggle="tooltip" title="Manage Episodes">
                                                <i class="fas fa-list"></i>
                                            </a>
                                            <a href="manage_links.php?type=tvshow&id=<?php echo $tvshow['id']; ?>" class="btn btn-sm btn-info me-1" data-bs-toggle="tooltip" title="Manage Links">
                                                <i class="fas fa-link"></i>
                                            </a>
                                            <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $tvshow['id']; ?>" class="btn btn-sm btn-success me-1" data-bs-toggle="tooltip" title="View" target="_blank">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="delete_tvshow.php?id=<?php echo $tvshow['id']; ?>" class="btn btn-sm btn-danger btn-delete" data-bs-toggle="tooltip" title="Delete" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই সিরিজটি মুছতে চান?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>

                                        <!-- Mobile View Dropdown -->
                                        <div class="dropdown d-md-none">
                                            <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" id="actionDropdown<?php echo $tvshow['id']; ?>" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="fas fa-ellipsis-v"></i> Actions
                                            </button>
                                            <ul class="dropdown-menu" aria-labelledby="actionDropdown<?php echo $tvshow['id']; ?>">
                                                <li>
                                                    <a class="dropdown-item" href="edit_tvshow.php?id=<?php echo $tvshow['id']; ?>">
                                                        <i class="fas fa-edit me-2"></i> Edit
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="manage_episodes.php?tvshow=<?php echo $tvshow['id']; ?>">
                                                        <i class="fas fa-list me-2"></i> Manage Episodes
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="manage_links.php?type=tvshow&id=<?php echo $tvshow['id']; ?>">
                                                        <i class="fas fa-link me-2"></i> Manage Links
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $tvshow['id']; ?>" target="_blank">
                                                        <i class="fas fa-eye me-2"></i> View
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="shareTVShow(<?php echo $tvshow['id']; ?>, '<?php echo addslashes($tvshow['title']); ?>')">
                                                        <i class="fas fa-share me-2"></i> Share
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item text-danger" href="delete_tvshow.php?id=<?php echo $tvshow['id']; ?>" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই সিরিজটি মুছতে চান?')">
                                                        <i class="fas fa-trash me-2"></i> Delete
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-success" onclick="shareTVShow(<?php echo $tvshow['id']; ?>, '<?php echo addslashes($tvshow['title']); ?>')" title="শেয়ার লিংক তৈরি করুন">
                                            <i class="fas fa-share"></i> শেয়ার
                                        </button>
                                    </td>
                                    <td><?php echo $tvshow['id']; ?></td>
                                    <td><?php echo $tvshow['title']; ?></td>
                                    <td><?php echo $tvshow['category_name']; ?></td>
                                    <td><?php echo $tvshow['start_year']; ?><?php echo $tvshow['end_year'] ? ' - ' . $tvshow['end_year'] : ' - Present'; ?></td>
                                    <td><?php echo $tvshow['seasons']; ?></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-star text-warning me-1"></i>
                                            <?php echo number_format($tvshow['rating'], 1); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if($tvshow['premium_only']): ?>
                                        <span class="badge bg-danger">Yes</span>
                                        <?php else: ?>
                                        <span class="badge bg-secondary">No</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($tvshow['featured']): ?>
                                        <span class="badge bg-success">Yes</span>
                                        <?php else: ?>
                                        <span class="badge bg-secondary">No</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input status-toggle" type="checkbox"
                                                data-id="<?php echo $tvshow['id']; ?>"
                                                data-type="tvshow"
                                                <?php echo (isset($tvshow['is_active']) && $tvshow['is_active']) ? 'checked' : ''; ?>
                                            >
                                            <label class="form-check-label status-label <?php echo (isset($tvshow['is_active']) && $tvshow['is_active']) ? 'text-success' : 'text-danger'; ?>">
                                                <?php echo (isset($tvshow['is_active']) && $tvshow['is_active']) ? 'Active' : 'Inactive'; ?>
                                            </label>
                                        </div>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="10" class="text-center">No TV shows found.</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.actions-column {
    min-width: 150px;
    width: auto;
}

.status-toggle {
    cursor: pointer;
}

.status-label {
    font-size: 0.85rem;
    font-weight: 500;
}

@media (max-width: 768px) {
    .actions-column {
        width: 100px;
    }

    .dropdown-menu {
        min-width: 200px;
    }

    .dropdown-item {
        padding: 8px 12px;
        white-space: normal;
    }
}
</style>

<script>
// Share TV Show Function
function shareTVShow(tvshowId, tvshowTitle) {
    // Prevent double click
    if (window.isSharing) return;
    window.isSharing = true;
    
    // Redirect to shared_links.php with tvshow data
    const url = `shared_links.php?content_type=tvshow&content_id=${tvshowId}&title=${encodeURIComponent(tvshowTitle)}`;
    window.location.href = url;
    
    // Reset after 2 seconds
    setTimeout(() => {
        window.isSharing = false;
    }, 2000);
}

// Remove datatable class to prevent auto-initialization
document.addEventListener('DOMContentLoaded', function() {
    const tvshowsTable = document.getElementById('tvshowsTable');
    if (tvshowsTable) {
        tvshowsTable.classList.remove('datatable');

        // Initialize DataTable with responsive: false
        if (typeof $.fn.DataTable !== 'undefined') {
            $(tvshowsTable).DataTable({
                responsive: false, // Disable responsive behavior
                columnDefs: [
                    { orderable: false, targets: 0 } // Disable sorting for actions column
                ],
                language: {
                    search: "_INPUT_",
                    searchPlaceholder: "Search...",
                    lengthMenu: "Show _MENU_ entries",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries",
                    infoEmpty: "Showing 0 to 0 of 0 entries",
                    infoFiltered: "(filtered from _MAX_ total entries)",
                    paginate: {
                        first: '<i class="fas fa-angle-double-left"></i>',
                        previous: '<i class="fas fa-angle-left"></i>',
                        next: '<i class="fas fa-angle-right"></i>',
                        last: '<i class="fas fa-angle-double-right"></i>'
                    }
                }
            });
        }

        // Initialize tooltips for dynamically loaded content
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.forEach(function(tooltipTriggerEl) {
            new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // Handle status toggle switches
    const statusToggles = document.querySelectorAll('.status-toggle');
    statusToggles.forEach(function(toggle) {
        toggle.addEventListener('change', function() {
            const id = this.dataset.id;
            const type = this.dataset.type;
            const status = this.checked ? 1 : 0;
            const label = this.nextElementSibling;

            // Show loading state
            this.disabled = true;
            label.textContent = 'Updating...';
            label.className = 'form-check-label status-label text-muted';

            // Send AJAX request to update status
            fetch('toggle_status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `id=${id}&type=${type}&status=${status}`
            })
            .then(response => response.json())
            .then(data => {
                this.disabled = false;

                if (data.success) {
                    // Update label
                    label.textContent = status ? 'Active' : 'Inactive';
                    label.className = `form-check-label status-label ${status ? 'text-success' : 'text-danger'}`;

                    // Show success message
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-success alert-dismissible fade show';
                    alertDiv.innerHTML = `
                        ${data.message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;

                    const container = document.querySelector('.container-fluid');
                    container.insertBefore(alertDiv, container.firstChild);

                    // Auto-dismiss after 3 seconds
                    setTimeout(() => {
                        const bsAlert = new bootstrap.Alert(alertDiv);
                        bsAlert.close();
                    }, 3000);
                } else {
                    // Revert toggle state
                    this.checked = !this.checked;

                    // Update label
                    label.textContent = this.checked ? 'Active' : 'Inactive';
                    label.className = `form-check-label status-label ${this.checked ? 'text-success' : 'text-danger'}`;

                    // Show error message
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                this.disabled = false;

                // Revert toggle state
                this.checked = !this.checked;

                // Update label
                label.textContent = this.checked ? 'Active' : 'Inactive';
                label.className = `form-check-label status-label ${this.checked ? 'text-success' : 'text-danger'}`;

                // Show error message
                alert('Error updating status. Please try again.');
            });
        });
    });
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
