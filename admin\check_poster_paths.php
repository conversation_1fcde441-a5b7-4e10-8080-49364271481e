<?php
// Set page title
$page_title = 'Check Poster Paths';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include config file
require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
    exit; // Make sure to exit after redirect
}

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';

// Get movie poster paths
$movies_query = "SELECT id, title, poster, banner FROM movies LIMIT 20";
$movies_result = mysqli_query($conn, $movies_query);

// Get TV show poster paths
$tvshows_query = "SELECT id, title, poster, banner FROM tvshows LIMIT 20";
$tvshows_result = mysqli_query($conn, $tvshows_query);
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title d-flex align-items-center">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Check Poster Paths</h1>
            </div>

            <div class="topbar-actions">
                <div class="dropdown">
                    <button class="btn btn-link dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <img src="assets/img/user.png" class="rounded-circle" width="32" height="32" alt="User">
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user fa-sm me-2 text-gray-400"></i> Profile</a></li>
                        <li><a class="dropdown-item" href="site_settings.php"><i class="fas fa-cogs fa-sm me-2 text-gray-400"></i> Settings</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/logout.php"><i class="fas fa-sign-out-alt fa-sm me-2 text-gray-400"></i> Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="fw-bold text-primary mb-0">Movie Poster and Banner Paths</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Title</th>
                                <th>Poster Path</th>
                                <th>Poster Status</th>
                                <th>Banner Path</th>
                                <th>Banner Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while($movie = mysqli_fetch_assoc($movies_result)): ?>
                            <tr>
                                <td><?php echo $movie['id']; ?></td>
                                <td><?php echo $movie['title']; ?></td>
                                <td><?php echo $movie['poster']; ?></td>
                                <td>
                                    <?php 
                                    if (empty($movie['poster'])) {
                                        echo '<span class="badge bg-danger">Missing</span>';
                                    } else if (strpos($movie['poster'], '/') === 0) {
                                        echo '<span class="badge bg-warning">TMDB Path</span>';
                                    } else if (file_exists('../uploads/' . $movie['poster'])) {
                                        echo '<span class="badge bg-success">OK</span>';
                                    } else {
                                        echo '<span class="badge bg-danger">File Not Found</span>';
                                    }
                                    ?>
                                </td>
                                <td><?php echo $movie['banner']; ?></td>
                                <td>
                                    <?php 
                                    if (empty($movie['banner'])) {
                                        echo '<span class="badge bg-danger">Missing</span>';
                                    } else if (strpos($movie['banner'], '/') === 0) {
                                        echo '<span class="badge bg-warning">TMDB Path</span>';
                                    } else if (file_exists('../uploads/' . $movie['banner'])) {
                                        echo '<span class="badge bg-success">OK</span>';
                                    } else {
                                        echo '<span class="badge bg-danger">File Not Found</span>';
                                    }
                                    ?>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header">
                <h6 class="fw-bold text-primary mb-0">TV Show Poster and Banner Paths</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Title</th>
                                <th>Poster Path</th>
                                <th>Poster Status</th>
                                <th>Banner Path</th>
                                <th>Banner Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while($tvshow = mysqli_fetch_assoc($tvshows_result)): ?>
                            <tr>
                                <td><?php echo $tvshow['id']; ?></td>
                                <td><?php echo $tvshow['title']; ?></td>
                                <td><?php echo $tvshow['poster']; ?></td>
                                <td>
                                    <?php 
                                    if (empty($tvshow['poster'])) {
                                        echo '<span class="badge bg-danger">Missing</span>';
                                    } else if (strpos($tvshow['poster'], '/') === 0) {
                                        echo '<span class="badge bg-warning">TMDB Path</span>';
                                    } else if (file_exists('../uploads/' . $tvshow['poster'])) {
                                        echo '<span class="badge bg-success">OK</span>';
                                    } else {
                                        echo '<span class="badge bg-danger">File Not Found</span>';
                                    }
                                    ?>
                                </td>
                                <td><?php echo $tvshow['banner']; ?></td>
                                <td>
                                    <?php 
                                    if (empty($tvshow['banner'])) {
                                        echo '<span class="badge bg-danger">Missing</span>';
                                    } else if (strpos($tvshow['banner'], '/') === 0) {
                                        echo '<span class="badge bg-warning">TMDB Path</span>';
                                    } else if (file_exists('../uploads/' . $tvshow['banner'])) {
                                        echo '<span class="badge bg-success">OK</span>';
                                    } else {
                                        echo '<span class="badge bg-danger">File Not Found</span>';
                                    }
                                    ?>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header">
                <h6 class="fw-bold text-primary mb-0">Fix Options</h6>
            </div>
            <div class="card-body">
                <p>Use the following tools to fix poster and banner issues:</p>
                <div class="d-flex flex-wrap gap-2">
                    <a href="fix_posters.php" class="btn btn-primary">
                        <i class="fas fa-sync-alt me-2"></i>Fix Posters
                    </a>
                    <a href="fix_banners.php" class="btn btn-success">
                        <i class="fas fa-sync-alt me-2"></i>Fix Banners
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
