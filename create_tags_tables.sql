-- Use existing database
USE tipsbdxy_4525;

-- Create tags table if it doesn't exist
CREATE TABLE IF NOT EXISTS tags (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create movie_tags junction table if it doesn't exist
CREATE TABLE IF NOT EXISTS movie_tags (
    id INT AUTO_INCREMENT PRIMARY KEY,
    movie_id INT NOT NULL,
    tag_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
    <PERSON>OREIG<PERSON> KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE,
    UNIQUE KEY unique_movie_tag (movie_id, tag_id)
);

-- Create tvshow_tags junction table if it doesn't exist
CREATE TABLE IF NOT EXISTS tvshow_tags (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tvshow_id INT NOT NULL,
    tag_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tvshow_id) REFERENCES tvshows(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE,
    UNIQUE KEY unique_tvshow_tag (tvshow_id, tag_id)
);

-- Insert common language tags
INSERT INTO tags (name) VALUES 
('Bangla Dubbed'),
('Hindi'),
('Dual Audio'),
('Telugu'),
('English'),
('Tamil'),
('Malayalam'),
('Punjabi'),
('Marathi'),
('Korean'),
('Japanese'),
('Chinese'),
('Spanish'),
('French'),
('German'),
('Italian'),
('Russian');
