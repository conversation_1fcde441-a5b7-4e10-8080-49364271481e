import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import '../models/download_link.dart';
import '../services/download_manager.dart';
import '../services/video_optimization_service.dart';

class EnhancedVideoPlayer extends StatefulWidget {
  final DownloadLink downloadLink;
  final String title;
  final int contentId;
  final String contentType;
  final Function(Duration) onPositionChanged;
  final Duration initialPosition;
  final Function()? onNextEpisode;

  const EnhancedVideoPlayer({
    super.key,
    required this.downloadLink,
    required this.title,
    required this.contentId,
    required this.contentType,
    required this.onPositionChanged,
    required this.initialPosition,
    this.onNextEpisode,
  });

  @override
  State<EnhancedVideoPlayer> createState() => _EnhancedVideoPlayerState();
}

class _EnhancedVideoPlayerState extends State<EnhancedVideoPlayer>
    with WidgetsBindingObserver {
  late final Player _player;
  late final VideoController _controller;

  // State variables
  bool _isBuffering = true;
  bool _isInitialized = false;
  bool _showControls = true;
  double _volume = 1.0;
  Timer? _positionUpdateTimer;
  Timer? _controlsTimer;

  // Performance tracking
  bool _isLoading = true;
  String _loadingStatus = 'ভিডিও লোড হচ্ছে...';
  double _loadingProgress = 0.0;

  // Download manager
  final DownloadManager _downloadManager = DownloadManager();
  bool _isOffline = false;

  // Optimization service
  final VideoOptimizationService _optimizationService =
      VideoOptimizationService();
  late VideoOptimizationResult _optimizationResult;

  // Audio track support
  List<AudioTrack> _audioTracks = [];
  int _currentAudioTrack = 0;
  bool _usingDummyTracks = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializePlayer();
    _startPositionUpdateTimer();
    _startControlsTimer();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _positionUpdateTimer?.cancel();
    _controlsTimer?.cancel();
    _player.dispose();
    super.dispose();
  }

  void _initializePlayer() async {
    setState(() {
      _loadingStatus = 'ভিডিও বিশ্লেষণ করা হচ্ছে...';
      _loadingProgress = 0.1;
    });

    // Analyze video for optimizations
    _optimizationResult =
        _optimizationService.analyzeVideoUrl(widget.downloadLink.url);
    final playerSettings = _optimizationService.getRecommendedPlayerSettings();

    debugPrint('Video optimization: $_optimizationResult');

    setState(() {
      _loadingStatus = 'প্লেয়ার প্রস্তুত করা হচ্ছে...';
      _loadingProgress = 0.2;
    });

    // Create optimized player with dynamic buffer size
    _player = Player(
      configuration: PlayerConfiguration(
        title: 'CinePix Enhanced Player',
        bufferSize: _optimizationResult.suggestedBufferSize,
        logLevel: MPVLogLevel.error,
      ),
    );

    _controller = VideoController(_player);

    // Setup listeners
    _setupPlayerListeners();

    setState(() {
      _loadingStatus = 'মিডিয়া ফাইল চেক করা হচ্ছে...';
      _loadingProgress = 0.3;
    });

    try {
      // Check for offline version first
      String mediaUrl = widget.downloadLink.url;
      Map<String, String> headers = _optimizationService.getOptimizedHeaders(
        referer: 'https://cinepix.top',
        enableRangeRequests: !_optimizationResult.isLocal,
      );

      final offlineItem = await _downloadManager.getDownloadedItem(
        widget.contentId,
        widget.contentType,
      );

      if (offlineItem != null) {
        final file = File(offlineItem.filePath);
        if (await file.exists()) {
          mediaUrl = 'file://${offlineItem.filePath}';
          headers = {};
          _isOffline = true;

          setState(() {
            _loadingStatus = 'অফলাইন ভার্সন লোড করা হচ্ছে...';
            _loadingProgress = 0.5;
          });
        }
      }

      setState(() {
        _loadingStatus = 'ভিডিও স্ট্রিম শুরু করা হচ্ছে...';
        _loadingProgress = 0.7;
      });

      // Open media with optimizations
      await _player.open(
        Media(
          mediaUrl,
          httpHeaders: headers,
          extras: _optimizationService.getMediaExtras(
            enableHardwareDecoding: playerSettings.enableHardwareDecoding,
            enableReconnect: !_optimizationResult.isLocal,
          ),
        ),
      );

      setState(() {
        _loadingStatus = 'প্রায় প্রস্তুত...';
        _loadingProgress = 0.9;
        _isInitialized = true;
        _isLoading = false;
      });

      // Handle initial position
      if (widget.initialPosition.inSeconds > 0) {
        _showResumeDialog();
      } else {
        _player.play();
      }
    } catch (e) {
      debugPrint('Enhanced player error: $e');
      setState(() {
        _isLoading = false;
        _isInitialized = true;
      });
      _showErrorDialog(e.toString());
    }
  }

  void _setupPlayerListeners() {
    // Buffering listener
    _player.stream.buffering.listen((buffering) {
      if (mounted) {
        setState(() {
          _isBuffering = buffering;
        });
      }
    });

    // Completion listener
    _player.stream.completed.listen((completed) {
      if (completed && mounted) {
        setState(() {
          _showControls = true;
        });

        if (widget.contentType == 'episode' && widget.onNextEpisode != null) {
          _showNextEpisodeDialog();
        }
      }
    });

    // Error listener
    _player.stream.error.listen((error) {
      debugPrint('Player error: $error');
      if (mounted) {
        _showErrorDialog(error);
      }
    });

    // Position listener for smooth progress updates
    _player.stream.position.listen((position) {
      if (mounted && _player.state.playing) {
        widget.onPositionChanged(position);
      }
    });

    // Audio tracks listener
    _player.stream.tracks.listen((tracks) {
      if (mounted) {
        setState(() {
          _audioTracks = tracks.audio;
        });

        // Try to detect audio tracks with a slight delay to ensure media is fully loaded
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted &&
              (_audioTracks.isEmpty ||
                  widget.downloadLink.url.toLowerCase().contains('.mkv'))) {
            _detectAudioTracks();
          }
        });
      }
    });

    // Add a delay and manually check for audio tracks again
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted && _audioTracks.isEmpty) {
        _checkAndAddAudioTracks();
      }
    });
  }

  void _showErrorDialog(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('প্লেব্যাক এরর'),
        content: Text('ভিডিও চালানোর সময় সমস্যা হয়েছে:\n$error'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Go back to previous screen
            },
            child: Text('ঠিক আছে'),
          ),
        ],
      ),
    );
  }

  void _showResumeDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text('পুনরায় চালু করুন?'),
        content: Text(
          'আপনি ${widget.initialPosition.inMinutes} মিনিট ${widget.initialPosition.inSeconds % 60} সেকেন্ড দেখেছেন। আপনি কি সেখান থেকে দেখা চালিয়ে যেতে চান?',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _player.play();
            },
            child: Text('শুরু থেকে'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _player.seek(widget.initialPosition);
              _player.play();
            },
            child: Text('পুনরায় চালু করুন'),
          ),
        ],
      ),
    );
  }

  void _showNextEpisodeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('পরবর্তী এপিসোড'),
        content: Text('আপনি কি পরবর্তী এপিসোড দেখতে চান?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('না'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              widget.onNextEpisode?.call();
            },
            child: Text('হ্যাঁ'),
          ),
        ],
      ),
    );
  }

  // Audio track methods
  void _showAudioTracksDialog() {
    debugPrint('_showAudioTracksDialog called, tracks: ${_audioTracks.length}');

    // If no audio tracks, try to detect tracks first
    if (_audioTracks.isEmpty && !_usingDummyTracks) {
      _detectAudioTracks();
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: Row(
          children: [
            Icon(Icons.audiotrack, color: Colors.white),
            SizedBox(width: 8),
            Text(
              'অডিও ট্র্যাক নির্বাচন করুন',
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'উপলব্ধ অডিও ট্র্যাক সমূহ:',
              style: TextStyle(color: Colors.white70, fontSize: 14),
            ),
            const Divider(color: Colors.white24),
            ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.5,
              ),
              child: _audioTracks.isEmpty
                  ? Center(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: Colors.orange,
                              size: 48,
                            ),
                            SizedBox(height: 16),
                            Text(
                              'কোন অডিও ট্র্যাক পাওয়া যায়নি।\nএটি একটি সমস্যা হতে পারে অথবা ফাইলে একাধিক অডিও ট্র্যাক নাও থাকতে পারে।',
                              style: TextStyle(color: Colors.white70),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    )
                  : ListView.builder(
                      shrinkWrap: true,
                      itemCount: _audioTracks.length,
                      itemBuilder: (context, index) {
                        final track = _audioTracks[index];
                        final isSelected = _currentAudioTrack == index;

                        // Format track title and language
                        String title =
                            track.title ?? 'অডিও ট্র্যাক ${index + 1}';
                        String language = track.language ?? 'অজানা ভাষা';

                        // Clean up title by removing domain names and common prefixes
                        title = title
                            .replaceAll(RegExp(r'https?://[^\s]+'), '')
                            .replaceAll(RegExp(r'www\.[^\s]+'), '')
                            .replaceAll(RegExp(r'[^\w\s\-\(\)]'), '')
                            .trim();

                        if (title.isEmpty) {
                          title = 'অডিও ট্র্যাক ${index + 1}';
                        }

                        // Detect language from title or language field
                        if (title.toLowerCase().contains('bangla') ||
                            title.toLowerCase().contains('bengali') ||
                            language.toLowerCase().contains('ben')) {
                          title = 'বাংলা অডিও';
                        } else if (title.toLowerCase().contains('english') ||
                            language.toLowerCase().contains('eng')) {
                          title = 'ইংরেজি অডিও';
                        } else if (title.toLowerCase().contains('hindi') ||
                            title.toLowerCase().contains('hin')) {
                          title = 'হিন্দি অডিও';
                        }

                        return ListTile(
                          leading: Icon(
                            isSelected
                                ? Icons.radio_button_checked
                                : Icons.radio_button_unchecked,
                            color: isSelected ? Colors.blue : Colors.white54,
                          ),
                          title: Text(
                            title,
                            style: TextStyle(
                              color: isSelected ? Colors.blue : Colors.white,
                              fontWeight: isSelected
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                            ),
                          ),
                          subtitle: language.isNotEmpty
                              ? Text(
                                  'ভাষা: $language',
                                  style: TextStyle(
                                    color: isSelected
                                        ? Colors.blue
                                        : Colors.white54,
                                  ),
                                )
                              : null,
                          tileColor:
                              isSelected ? Colors.blue.withOpacity(0.1) : null,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                            side: BorderSide(
                              color: isSelected ? Colors.blue : Colors.white54,
                            ),
                          ),
                          selected: isSelected,
                          onTap: () {
                            Navigator.of(context).pop();
                            _switchAudioTrack(index);
                          },
                        );
                      },
                    ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: Colors.white70,
            ),
            child: Text('বন্ধ করুন'),
          ),
        ],
      ),
    );
  }

  // Try to detect audio tracks from the media
  void _detectAudioTracks() {
    debugPrint('Detecting audio tracks from media file');

    // Get current tracks
    final tracks = _player.state.tracks;

    setState(() {
      // If we have real tracks, use them
      if (tracks.audio.isNotEmpty) {
        _audioTracks = tracks.audio;
        _usingDummyTracks = false;

        // Log found tracks for debugging
        debugPrint('Found ${_audioTracks.length} audio tracks:');
        for (var i = 0; i < _audioTracks.length; i++) {
          final track = _audioTracks[i];
          debugPrint(
              'Track $i: ${track.title}, Language: ${track.language}, ID: ${track.id}');
        }

        // Show a message to the user
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  '${_audioTracks.length}টি অডিও ট্র্যাক পাওয়া গেছে। আপনি অডিও আইকনে ক্লিক করে ট্র্যাক পরিবর্তন করতে পারেন।'),
              duration: const Duration(seconds: 3),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        // No audio tracks found
        _audioTracks = [];
        _usingDummyTracks = false;

        // Only show message if it's an MKV file (which should have multiple audio tracks)
        if (widget.downloadLink.url.toLowerCase().contains('.mkv') && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'কোন অডিও ট্র্যাক পাওয়া যায়নি। এটি একটি সমস্যা হতে পারে অথবা ফাইলে একাধিক অডিও ট্র্যাক নাও থাকতে পারে।'),
              duration: Duration(seconds: 3),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    });
  }

  // Manually check for audio tracks and add them if needed
  void _checkAndAddAudioTracks() async {
    debugPrint('Manually checking for audio tracks');

    try {
      // Try to get tracks directly from player
      final tracks = _player.state.tracks;

      if (tracks.audio.isNotEmpty) {
        setState(() {
          _audioTracks = tracks.audio;
        });
        debugPrint('Found ${tracks.audio.length} audio tracks on second check');
      } else if (widget.downloadLink.url.toLowerCase().contains('.mkv')) {
        // If still no tracks but it's an MKV file, try to detect tracks
        _detectAudioTracks();
      }
    } catch (e) {
      debugPrint('Error checking audio tracks: $e');
      if (widget.downloadLink.url.toLowerCase().contains('.mkv')) {
        _detectAudioTracks();
      }
    }
  }

  void _switchAudioTrack(int trackIndex) async {
    // Check if we have valid tracks and index
    if (_audioTracks.isEmpty ||
        trackIndex < 0 ||
        trackIndex >= _audioTracks.length) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'অডিও ট্র্যাক পরিবর্তন করা যায়নি: কোন ট্র্যাক পাওয়া যায়নি'),
            duration: Duration(seconds: 2),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    // Update the current track index
    setState(() {
      _currentAudioTrack = trackIndex;
    });

    // Get current position
    final position = _player.state.position;

    // Get the track we're switching to
    final track = _audioTracks[trackIndex];

    // Format track name for display
    String trackName = track.title ?? 'ট্র্যাক ${trackIndex + 1}';

    // Remove domain name from title
    trackName = trackName
        .replaceAll(RegExp(r'https?://[^\s]+'), '')
        .replaceAll(RegExp(r'www\.[^\s]+'), '')
        .replaceAll(RegExp(r'[^\w\s\-\(\)]'), '')
        .trim();

    if (trackName.isEmpty) {
      trackName = 'ট্র্যাক ${trackIndex + 1}';
    }

    // Detect language from title or language field
    if (trackName.toLowerCase().contains('bangla') ||
        trackName.toLowerCase().contains('bengali') ||
        track.language?.toLowerCase().contains('ben') == true) {
      trackName = 'বাংলা অডিও';
    } else if (trackName.toLowerCase().contains('english') ||
        track.language?.toLowerCase().contains('eng') == true) {
      trackName = 'ইংরেজি অডিও';
    } else if (trackName.toLowerCase().contains('hindi') ||
        trackName.toLowerCase().contains('hin')) {
      trackName = 'হিন্দি অডিও';
    }

    try {
      // Select the audio track
      await _player.setAudioTrack(track);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('অডিও ট্র্যাক পরিবর্তন করা হয়েছে: $trackName'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error switching audio track: $e');
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('অডিও ট্র্যাক পরিবর্তন করতে সমস্যা হয়েছে: $e'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.red,
          ),
        );
      }
    }

    // Restore position
    await _player.seek(position);
  }

  void _startPositionUpdateTimer() {
    _positionUpdateTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (_player.state.playing && !_player.state.completed) {
        widget.onPositionChanged(_player.state.position);
      }
    });
  }

  void _startControlsTimer() {
    _controlsTimer?.cancel();
    _controlsTimer = Timer(const Duration(seconds: 5), () {
      if (mounted && _showControls) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    if (_showControls) {
      _startControlsTimer();
    }
  }

  void _handleKeyEvent(KeyEvent event) {
    if (event is! KeyDownEvent) return;

    setState(() {
      _showControls = true;
    });
    _startControlsTimer();

    final key = event.logicalKey;

    if (key == LogicalKeyboardKey.select ||
        key == LogicalKeyboardKey.enter ||
        key == LogicalKeyboardKey.space) {
      // Play/Pause
      if (_player.state.playing) {
        _player.pause();
      } else {
        _player.play();
      }
    } else if (key == LogicalKeyboardKey.arrowLeft) {
      // Skip backward
      final newPosition = _player.state.position - const Duration(seconds: 10);
      _player.seek(newPosition > Duration.zero ? newPosition : Duration.zero);
    } else if (key == LogicalKeyboardKey.arrowRight) {
      // Skip forward
      final newPosition = _player.state.position + const Duration(seconds: 10);
      _player.seek(newPosition);
    } else if (key == LogicalKeyboardKey.arrowUp) {
      // Volume up
      final newVolume = (_volume + 0.1).clamp(0.0, 1.0);
      setState(() {
        _volume = newVolume;
      });
      _player.setVolume(_volume * 100);
    } else if (key == LogicalKeyboardKey.arrowDown) {
      // Volume down
      final newVolume = (_volume - 0.1).clamp(0.0, 1.0);
      setState(() {
        _volume = newVolume;
      });
      _player.setVolume(_volume * 100);
    } else if (key == LogicalKeyboardKey.escape ||
        key == LogicalKeyboardKey.goBack) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Container(
        color: Colors.black,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                value: _loadingProgress,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                strokeWidth: 6,
              ),
              SizedBox(height: 24),
              Text(
                _loadingStatus,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8),
              Text(
                '${(_loadingProgress * 100).toInt()}%',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
              if (_isOffline) ...[
                SizedBox(height: 16),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    'অফলাইন মোড',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      );
    }

    if (!_isInitialized) {
      return Container(
        color: Colors.black,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.error, color: Colors.red, size: 64),
              SizedBox(height: 16),
              Text(
                'ভিডিও লোড করতে সমস্যা হয়েছে',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ],
          ),
        ),
      );
    }

    return KeyboardListener(
      focusNode: FocusNode(),
      autofocus: true,
      onKeyEvent: _handleKeyEvent,
      child: GestureDetector(
        onTap: _toggleControls,
        child: Stack(
          children: [
            // Video player
            Video(
              controller: _controller,
              controls: NoVideoControls,
              wakelock: true,
              fit: BoxFit.contain,
            ),

            // Buffering indicator
            if (_isBuffering)
              Positioned.fill(
                child: Container(
                  color: Colors.black.withOpacity(0.3),
                  child: Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                          strokeWidth: 3,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'বাফারিং...',
                          style: TextStyle(color: Colors.white, fontSize: 16),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

            // Controls overlay
            if (_showControls)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withOpacity(0.7),
                        Colors.transparent,
                        Colors.transparent,
                        Colors.black.withOpacity(0.7),
                      ],
                      stops: [0.0, 0.3, 0.7, 1.0],
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Top bar
                      Container(
                        padding: EdgeInsets.all(16),
                        child: Row(
                          children: [
                            IconButton(
                              icon: Icon(Icons.arrow_back, color: Colors.white),
                              onPressed: () => Navigator.of(context).pop(),
                            ),
                            Expanded(
                              child: Text(
                                widget.title,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            // অডিও ট্র্যাক বাটন যোগ করুন
                            if (_audioTracks.length > 1)
                              IconButton(
                                icon: Icon(Icons.audiotrack, color: Colors.white),
                                onPressed: () => _showAudioTracksDialog(),
                                tooltip: 'অডিও ট্র্যাক পরিবর্তন করুন',
                              ),
                            if (_isOffline)
                              Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: Colors.green,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'অফলাইন',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),

                      // Center controls
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Skip backward
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.5),
                              shape: BoxShape.circle,
                            ),
                            child: IconButton(
                              iconSize: 48,
                              icon: Icon(Icons.replay_10,
                                  color: Colors.white, size: 32),
                              onPressed: () {
                                final newPosition = _player.state.position -
                                    Duration(seconds: 10);
                                _player.seek(newPosition > Duration.zero
                                    ? newPosition
                                    : Duration.zero);
                              },
                            ),
                          ),
                          SizedBox(width: 24),
                          // Play/Pause
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.5),
                              shape: BoxShape.circle,
                            ),
                            child: IconButton(
                              iconSize: 72,
                              icon: Icon(
                                _player.state.playing
                                    ? Icons.pause
                                    : Icons.play_arrow,
                                color: Colors.white,
                                size: 48,
                              ),
                              onPressed: () {
                                if (_player.state.playing) {
                                  _player.pause();
                                } else {
                                  _player.play();
                                }
                                setState(() {});
                              },
                            ),
                          ),
                          SizedBox(width: 24),
                          // Skip forward
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.5),
                              shape: BoxShape.circle,
                            ),
                            child: IconButton(
                              iconSize: 48,
                              icon: Icon(Icons.forward_10,
                                  color: Colors.white, size: 32),
                              onPressed: () {
                                final newPosition = _player.state.position +
                                    Duration(seconds: 10);
                                _player.seek(newPosition);
                              },
                            ),
                          ),
                        ],
                      ),

                      // Bottom controls
                      Container(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          children: [
                            // Progress bar
                            StreamBuilder<Duration>(
                              stream: _player.stream.position,
                              builder: (context, snapshot) {
                                final position = snapshot.data ?? Duration.zero;
                                final duration = _player.state.duration;

                                final max = duration.inMilliseconds > 0
                                    ? duration.inMilliseconds.toDouble()
                                    : 1.0;
                                final value = position.inMilliseconds
                                    .toDouble()
                                    .clamp(0.0, max);

                                return Column(
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          _formatDuration(position),
                                          style: TextStyle(color: Colors.white),
                                        ),
                                        Text(
                                          _formatDuration(duration),
                                          style: TextStyle(color: Colors.white),
                                        ),
                                      ],
                                    ),
                                    SliderTheme(
                                      data: SliderThemeData(
                                        thumbShape: RoundSliderThumbShape(
                                            enabledThumbRadius: 8),
                                        overlayShape: RoundSliderOverlayShape(
                                            overlayRadius: 16),
                                        trackHeight: 4,
                                        activeTrackColor: Colors.blue,
                                        inactiveTrackColor: Colors.grey[700],
                                        thumbColor: Colors.white,
                                        overlayColor:
                                            Colors.blue.withOpacity(0.3),
                                      ),
                                      child: Slider(
                                        value: value,
                                        min: 0,
                                        max: max,
                                        onChanged: (value) {
                                          _player.seek(Duration(
                                              milliseconds: value.toInt()));
                                        },
                                      ),
                                    ),
                                  ],
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));

    return duration.inHours > 0
        ? '$hours:$minutes:$seconds'
        : '$minutes:$seconds';
  }
}
