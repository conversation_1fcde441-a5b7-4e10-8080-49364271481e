<?php
require_once 'includes/header.php';
require_once 'includes/ad_placeholder.php';

// CSS for movie cards
?>
<style>
    /* Movie Card Styles */
    .movie-card-link {
        display: block;
        text-decoration: none;
        color: white;
        height: 100%;
    }

    .movie-card {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        height: 100%;
        margin-bottom: 0;
    }

    .movie-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    }

    .movie-card img {
        width: 100%;
        height: auto;
        aspect-ratio: 2/3;
        object-fit: cover;
        transition: all 0.3s ease;
    }

    .movie-card:hover img {
        filter: brightness(0.7);
    }

    .movie-card-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(to top, rgba(0, 0, 0, 0.9), transparent);
        padding: 15px 10px;
        transition: all 0.3s ease;
    }

    .movie-card:hover .movie-card-overlay {
        background: linear-gradient(to top, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.7));
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
    }

    .movie-card-title {
        font-size: 0.9rem;
        font-weight: bold;
        margin-bottom: 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .movie-card:hover .movie-card-title {
        white-space: normal;
        overflow: visible;
    }

    .movie-card-info {
        font-size: 0.7rem;
        color: #aaa;
        margin-bottom: 5px;
    }

    .movie-card-rating {
        font-size: 0.8rem;
        color: #ffc107;
    }

    .movie-card-buttons {
        position: absolute;
        top: 10px;
        right: 10px;
        display: flex;
        flex-direction: column;
        gap: 5px;
        opacity: 0;
        transition: all 0.3s ease;
    }

    .movie-card:hover .movie-card-buttons {
        opacity: 1;
    }

    .movie-card-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        font-size: 0.8rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .movie-card-btn:hover {
        background-color: #dc3545;
        transform: scale(1.1);
    }

    .premium-badge {
        position: absolute;
        top: 10px;
        left: 10px;
        background-color: #ffc107;
        color: #000;
        padding: 3px 6px;
        border-radius: 3px;
        font-size: 0.7rem;
        font-weight: bold;
        z-index: 2;
    }

    /* Responsive adjustments */
    @media (max-width: 767px) {
        .movie-card-title {
            font-size: 0.8rem;
        }

        .movie-card-info {
            font-size: 0.65rem;
        }
    }
</style>
<?php

// Get category filter
$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : 0;

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$items_per_page = 12;
$offset = ($page - 1) * $items_per_page;

// Build query
$query = "SELECT t.*, c.name as category_name FROM tvshows t
          LEFT JOIN categories c ON t.category_id = c.id";

// Add category filter if set
if ($category_filter > 0) {
    $query .= " WHERE t.category_id = $category_filter";
}

// Add sorting
$query .= " ORDER BY t.start_year DESC";

// Get total count for pagination
$count_query = $query;
$count_result = mysqli_query($conn, $count_query);
$total_items = mysqli_num_rows($count_result);
$total_pages = ceil($total_items / $items_per_page);

// Add limit for pagination
$query .= " LIMIT $offset, $items_per_page";

// Execute query
$result = mysqli_query($conn, $query);

// Get all categories for filter
$categories_query = "SELECT * FROM categories ORDER BY name";
$categories_result = mysqli_query($conn, $categories_query);
?>

<!-- Top Ad Section -->
<?php echo renderAdSection('banner', 'TV Shows Page Top Advertisement', '728x90'); ?>

<!-- Page Header -->
<section class="py-5 bg-dark">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="display-4 fw-bold">TV Shows</h1>
                <p class="lead text-muted">Discover our collection of TV shows across various genres</p>
            </div>
            <div class="col-md-6">
                <form action="<?php echo SITE_URL; ?>/search.php" method="GET" class="d-flex">
                    <input type="hidden" name="type" value="tvshow">
                    <input type="search" name="query" class="form-control me-2" placeholder="Search for TV shows...">
                    <button type="submit" class="btn btn-danger">Search</button>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Category Filter -->
<section class="py-4">
    <div class="container">
        <div class="d-flex flex-wrap">
            <a href="<?php echo SITE_URL; ?>/tvshows.php" class="btn <?php echo $category_filter == 0 ? 'btn-danger' : 'btn-outline-danger'; ?> me-2 mb-2">All</a>
            <?php while($category = mysqli_fetch_assoc($categories_result)): ?>
            <a href="<?php echo SITE_URL; ?>/tvshows.php?category=<?php echo $category['id']; ?>" class="btn <?php echo $category_filter == $category['id'] ? 'btn-danger' : 'btn-outline-danger'; ?> me-2 mb-2"><?php echo $category['name']; ?></a>
            <?php endwhile; ?>
        </div>
    </div>
</section>

<!-- TV Shows Grid -->
<section class="py-5">
    <div class="container">
        <?php if(mysqli_num_rows($result) > 0): ?>
            <div class="row">
                <?php while($tvshow = mysqli_fetch_assoc($result)): ?>
                <div class="col-6 col-sm-4 col-md-3 col-lg-2 mb-3">
                    <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $tvshow['id']; ?>" class="movie-card-link">
                        <div class="movie-card" data-category="<?php echo $tvshow['category_id']; ?>">
                            <?php if($tvshow['premium_only']): ?>
                            <div class="premium-badge">
                                <i class="fas fa-crown"></i>
                            </div>
                            <?php endif; ?>
                            <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $tvshow['poster']; ?>" alt="<?php echo $tvshow['title']; ?>">
                            <div class="movie-card-overlay">
                                <h5 class="movie-card-title"><?php echo $tvshow['title']; ?></h5>
                                <div class="movie-card-info">
                                    <span><i class="fas fa-calendar-alt"></i> <?php echo $tvshow['start_year']; ?><?php echo $tvshow['end_year'] ? ' - ' . $tvshow['end_year'] : ' - Present'; ?></span> •
                                    <span><i class="fas fa-film"></i> <?php echo $tvshow['category_name']; ?></span>
                                </div>
                                <div class="movie-card-rating">
                                    <i class="fas fa-star"></i> <?php echo number_format($tvshow['rating'], 1); ?>
                                </div>
                            </div>
                            <div class="movie-card-buttons">
                                <span class="movie-card-btn" title="More Info">
                                    <i class="fas fa-info-circle"></i>
                                </span>
                                <span class="movie-card-btn add-to-watchlist" data-id="<?php echo $tvshow['id']; ?>" data-type="tvshow" title="Add to Watchlist" onclick="event.stopPropagation(); addToWatchlist(<?php echo $tvshow['id']; ?>, 'tvshow'); return false;">
                                    <i class="fas fa-plus"></i>
                                </span>
                                <?php if($tvshow['trailer_url']): ?>
                                <span class="movie-card-btn play-trailer" data-trailer="<?php echo $tvshow['trailer_url']; ?>" title="Play Trailer" onclick="event.stopPropagation(); playTrailer('<?php echo $tvshow['trailer_url']; ?>'); return false;">
                                    <i class="fas fa-play"></i>
                                </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </a>
                </div>
                <?php endwhile; ?>
            </div>

            <!-- Middle Ad Section -->
            <?php echo renderAdSection('inline', 'TV Shows Grid Advertisement', '728x90'); ?>

            <!-- Pagination -->
            <?php if($total_pages > 1): ?>
            <nav aria-label="Page navigation" class="mt-5">
                <ul class="pagination justify-content-center">
                    <?php if($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?php echo SITE_URL; ?>/tvshows.php?<?php echo $category_filter > 0 ? 'category=' . $category_filter . '&' : ''; ?>page=<?php echo $page - 1; ?>" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php for($i = 1; $i <= $total_pages; $i++): ?>
                    <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                        <a class="page-link" href="<?php echo SITE_URL; ?>/tvshows.php?<?php echo $category_filter > 0 ? 'category=' . $category_filter . '&' : ''; ?>page=<?php echo $i; ?>"><?php echo $i; ?></a>
                    </li>
                    <?php endfor; ?>

                    <?php if($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?php echo SITE_URL; ?>/tvshows.php?<?php echo $category_filter > 0 ? 'category=' . $category_filter . '&' : ''; ?>page=<?php echo $page + 1; ?>" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>

            <!-- Bottom Ad Section -->
            <?php echo renderAdSection('banner', 'TV Shows Bottom Advertisement', '728x90'); ?>

        <?php else: ?>
            <div class="alert alert-info text-center">
                <h4>No TV shows found</h4>
                <p>There are no TV shows available in this category. Please try another category or check back later.</p>
            </div>
        <?php endif; ?>
    </div>
</section>

<?php require_once 'includes/footer.php'; ?>
