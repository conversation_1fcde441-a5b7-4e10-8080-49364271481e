<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// API Helper Functions

// Send API response
function api_response($data, $status_code = 200, $message = 'Success') {
    http_response_code($status_code);
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

// Send API error response
function api_error($message, $status_code = 400) {
    http_response_code($status_code);
    echo json_encode([
        'success' => false,
        'message' => $message,
        'data' => null
    ]);
    exit;
}

// Check if user is authenticated
function is_authenticated() {
    global $request;

    $auth = $request['auth'];
    if (empty($auth)) {
        return false;
    }

    // Check if token is in Bearer format
    if (strpos($auth, 'Bearer ') === 0) {
        $token = substr($auth, 7);
        return verify_token($token);
    }

    return false;
}

// Verify JWT token
function verify_token($token) {
    global $conn;

    // Split token into header, payload, and signature
    $token_parts = explode('.', $token);
    if (count($token_parts) !== 3) {
        return false;
    }

    list($header, $payload, $signature) = $token_parts;

    // Decode payload
    $payload_data = json_decode(base64_decode($payload), true);
    if (!$payload_data) {
        return false;
    }

    // Check if token is expired
    if (isset($payload_data['exp']) && $payload_data['exp'] < time()) {
        return false;
    }

    // Check if user exists
    $user_id = $payload_data['user_id'] ?? 0;
    $query = "SELECT * FROM users WHERE id = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $user_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if (mysqli_num_rows($result) === 0) {
        return false;
    }

    // Verify signature
    $expected_signature = hash_hmac('sha256', "$header.$payload", JWT_SECRET, true);
    $expected_signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($expected_signature));

    return hash_equals($expected_signature, $signature);
}

// Get authenticated user
function get_authenticated_user() {
    global $request, $conn;

    $auth = $request['auth'];
    if (empty($auth)) {
        return null;
    }

    // Check if token is in Bearer format
    if (strpos($auth, 'Bearer ') === 0) {
        $token = substr($auth, 7);

        // Split token into header, payload, and signature
        $token_parts = explode('.', $token);
        if (count($token_parts) !== 3) {
            return null;
        }

        list($header, $payload, $signature) = $token_parts;

        // Decode payload
        $payload_data = json_decode(base64_decode($payload), true);
        if (!$payload_data) {
            return null;
        }

        // Get user from database
        $user_id = $payload_data['user_id'] ?? 0;
        $query = "SELECT id, username, email, name, role, is_premium, premium_expires
                 FROM users WHERE id = ?";
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, 'i', $user_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);

        if (mysqli_num_rows($result) === 0) {
            return null;
        }

        $user = mysqli_fetch_assoc($result);

        return [
            'user_id' => (int)$user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'name' => $user['name'],
            'role' => $user['role'],
            'is_premium' => (bool)$user['is_premium'],
            'premium_expires' => $user['premium_expires']
        ];
    }

    return null;
}

// Check if user is admin
function is_admin() {
    $user = get_authenticated_user();
    return $user && $user['role'] === 'admin';
}

// Generate JWT token
function generate_token($user_id, $username, $role) {
    // Create token header
    $header = [
        'alg' => 'HS256',
        'typ' => 'JWT'
    ];
    $header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode(json_encode($header)));

    // Create token payload
    $payload = [
        'user_id' => $user_id,
        'username' => $username,
        'role' => $role,
        'iat' => time(),
        'exp' => time() + (60 * 60 * 24 * 30) // 30 days
    ];
    $payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode(json_encode($payload)));

    // Create signature
    $signature = hash_hmac('sha256', "$header.$payload", JWT_SECRET, true);
    $signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

    // Return complete token
    return "$header.$payload.$signature";
}

// Format movie data
function format_movie_data($movie) {
    return [
        'id' => (int)$movie['id'],
        'title' => $movie['title'],
        'description' => $movie['description'],
        'release_year' => (int)$movie['release_year'],
        'duration' => (int)$movie['duration'],
        'poster' => $movie['poster'],
        'banner' => $movie['banner'],
        'trailer_url' => $movie['trailer_url'],
        'rating' => (float)$movie['rating'],
        'category_id' => (int)$movie['category_id'],
        'category_name' => $movie['category_name'] ?? null,
        'featured' => (bool)$movie['featured'],
        'premium_only' => (bool)$movie['premium_only'],
        'status' => $movie['status']
    ];
}

// Format TV show data
function format_tvshow_data($tvshow) {
    return [
        'id' => (int)$tvshow['id'],
        'title' => $tvshow['title'],
        'description' => $tvshow['description'],
        'start_year' => (int)$tvshow['start_year'],
        'end_year' => $tvshow['end_year'] ? (int)$tvshow['end_year'] : null,
        'seasons' => (int)$tvshow['seasons'],
        'poster' => $tvshow['poster'],
        'banner' => $tvshow['banner'],
        'trailer_url' => $tvshow['trailer_url'],
        'rating' => (float)$tvshow['rating'],
        'category_id' => (int)$tvshow['category_id'],
        'category_name' => $tvshow['category_name'] ?? null,
        'featured' => (bool)$tvshow['featured'],
        'premium_only' => (bool)$tvshow['premium_only'],
        'status' => $tvshow['status']
    ];
}

// Format episode data
function format_episode_data($episode) {
    return [
        'id' => (int)$episode['id'],
        'tvshow_id' => (int)$episode['tvshow_id'],
        'season_number' => (int)$episode['season_number'],
        'episode_number' => (int)$episode['episode_number'],
        'title' => $episode['title'],
        'description' => $episode['description'],
        'duration' => (int)$episode['duration'],
        'thumbnail' => $episode['thumbnail'],
        'release_date' => $episode['release_date'],
        'is_premium' => (bool)$episode['is_premium']
    ];
}

// Format download link data
function format_download_link_data($link) {
    return [
        'id' => (int)$link['id'],
        'quality' => $link['quality'],
        'url' => $link['url'],
        'server_name' => $link['server_name'],
        'file_size' => $link['file_size'],
        'is_premium' => (bool)$link['is_premium']
    ];
}

// Check if user has access to premium content
function has_premium_access($user) {
    return $user && $user['is_premium'];
}

// Log API request
function log_api_request($endpoint, $method, $user_id = null, $status_code = 200) {
    global $conn;

    $ip_address = $_SERVER['REMOTE_ADDR'];
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

    $query = "INSERT INTO api_logs (endpoint, method, user_id, ip_address, user_agent, status_code)
             VALUES (?, ?, ?, ?, ?, ?)";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'ssissi', $endpoint, $method, $user_id, $ip_address, $user_agent, $status_code);
    mysqli_stmt_execute($stmt);
}
