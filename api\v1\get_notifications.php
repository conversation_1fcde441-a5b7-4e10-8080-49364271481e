<?php
// Include direct config file
require_once '../direct_config.php';

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Check if request method is GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed. Use GET.',
        'data' => null
    ]);
    exit;
}

// Get user ID from query parameter
$user_id = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;

// Check if notifications table exists
$check_table = "SHOW TABLES LIKE 'notifications'";
$result = mysqli_query($conn, $check_table);

if (mysqli_num_rows($result) == 0) {
    // Create notifications table
    $create_table = "CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        image_url VARCHAR(255) NULL,
        action_type ENUM('none', 'movie', 'tvshow', 'url') DEFAULT 'none',
        action_id VARCHAR(100) NULL,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    
    if (!mysqli_query($conn, $create_table)) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to create notifications table: ' . mysqli_error($conn),
            'data' => null
        ]);
        exit;
    }
}

// Prepare query to get notifications
if ($user_id > 0) {
    // Get notifications for specific user and broadcast notifications (user_id IS NULL)
    $sql = "SELECT id, title, message, image_url, action_type, action_id, is_read, created_at 
            FROM notifications 
            WHERE user_id = ? OR user_id IS NULL 
            ORDER BY created_at DESC 
            LIMIT 50";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, 'i', $user_id);
} else {
    // Get only broadcast notifications
    $sql = "SELECT id, title, message, image_url, action_type, action_id, is_read, created_at 
            FROM notifications 
            WHERE user_id IS NULL 
            ORDER BY created_at DESC 
            LIMIT 50";
    $stmt = mysqli_prepare($conn, $sql);
}

// Execute query
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

// Fetch notifications
$notifications = [];
while ($row = mysqli_fetch_assoc($result)) {
    // Format created_at date
    $row['created_at'] = date('Y-m-d H:i:s', strtotime($row['created_at']));
    
    // Convert is_read to boolean
    $row['is_read'] = (bool)$row['is_read'];
    
    $notifications[] = $row;
}

// Close statement
mysqli_stmt_close($stmt);

// Mark notifications as read if requested
if (isset($_GET['mark_as_read']) && $_GET['mark_as_read'] === 'true' && $user_id > 0) {
    $update_sql = "UPDATE notifications SET is_read = TRUE WHERE (user_id = ? OR user_id IS NULL) AND is_read = FALSE";
    $update_stmt = mysqli_prepare($conn, $update_sql);
    mysqli_stmt_bind_param($update_stmt, 'i', $user_id);
    mysqli_stmt_execute($update_stmt);
    mysqli_stmt_close($update_stmt);
}

// Return notifications
http_response_code(200);
echo json_encode([
    'success' => true,
    'message' => 'Notifications retrieved successfully.',
    'data' => [
        'notifications' => $notifications,
        'count' => count($notifications)
    ]
]);

// Close database connection
mysqli_close($conn);
?>
