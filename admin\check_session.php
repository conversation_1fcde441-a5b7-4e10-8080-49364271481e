<?php
// Start session
session_start();

// Display session variables
echo "<h2>Session Variables:</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Check if user is logged in
echo "<h2>Login Status:</h2>";
echo "isLoggedIn(): " . (isLoggedIn() ? "Yes" : "No") . "<br>";

// Check if user is admin
echo "<h2>Admin Status:</h2>";
echo "isAdmin(): " . (isAdmin() ? "Yes" : "No") . "<br>";

// Include functions
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function isAdmin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'admin';
}
?>
