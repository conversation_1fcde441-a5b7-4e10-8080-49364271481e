<?php
// Set page title
$page_title = 'Categories';

require_once '../includes/config.php';

// Check if type column exists in categories table
$check_column_query = "SHOW COLUMNS FROM categories LIKE 'type'";
$check_column_result = mysqli_query($conn, $check_column_query);

if (mysqli_num_rows($check_column_result) == 0) {
    // Add type column if it doesn't exist
    $add_column_query = "ALTER TABLE categories ADD COLUMN type ENUM('both', 'movie', 'tvshow') DEFAULT 'both'";
    mysqli_query($conn, $add_column_query);

    // Update existing categories to have 'both' type
    $update_query = "UPDATE categories SET type = 'both' WHERE type IS NULL";
    mysqli_query($conn, $update_query);
}

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Process form submissions
$success_message = '';
$error_message = '';

// Add Category
if (isset($_POST['add_category'])) {
    $name = sanitize($_POST['name']);
    $type = sanitize($_POST['type']);

    if (empty($name)) {
        $error_message = 'Category name is required.';
    } else {
        // Check if category already exists
        $check_query = "SELECT * FROM categories WHERE name = '$name' AND type = '$type'";
        $check_result = mysqli_query($conn, $check_query);

        if (mysqli_num_rows($check_result) > 0) {
            $error_message = 'Category already exists.';
        } else {
            // Add category
            $add_query = "INSERT INTO categories (name, type) VALUES ('$name', '$type')";

            if (mysqli_query($conn, $add_query)) {
                $success_message = 'Category added successfully.';
            } else {
                $error_message = 'Error adding category: ' . mysqli_error($conn);
            }
        }
    }
}

// Update Category
if (isset($_POST['update_category'])) {
    $category_id = (int)$_POST['category_id'];
    $name = sanitize($_POST['name']);
    $type = sanitize($_POST['type']);

    if (empty($name)) {
        $error_message = 'Category name is required.';
    } else {
        // Check if category already exists
        $check_query = "SELECT * FROM categories WHERE name = '$name' AND type = '$type' AND id != $category_id";
        $check_result = mysqli_query($conn, $check_query);

        if (mysqli_num_rows($check_result) > 0) {
            $error_message = 'Category already exists.';
        } else {
            // Update category
            $update_query = "UPDATE categories SET name = '$name', type = '$type' WHERE id = $category_id";

            if (mysqli_query($conn, $update_query)) {
                $success_message = 'Category updated successfully.';
            } else {
                $error_message = 'Error updating category: ' . mysqli_error($conn);
            }
        }
    }
}

// Delete Category
if (isset($_GET['delete']) && $_GET['delete'] > 0) {
    $category_id = (int)$_GET['delete'];

    // Check if category is in use
    $check_movies_query = "SELECT COUNT(*) as count FROM movies WHERE category_id = $category_id";
    $check_movies_result = mysqli_query($conn, $check_movies_query);
    $movies_count = mysqli_fetch_assoc($check_movies_result)['count'];

    $check_tvshows_query = "SELECT COUNT(*) as count FROM tvshows WHERE category_id = $category_id";
    $check_tvshows_result = mysqli_query($conn, $check_tvshows_query);
    $tvshows_count = mysqli_fetch_assoc($check_tvshows_result)['count'];

    if ($movies_count > 0 || $tvshows_count > 0) {
        $error_message = 'Cannot delete category because it is in use by ' . $movies_count . ' movies and ' . $tvshows_count . ' TV shows.';
    } else {
        // Delete category
        $delete_query = "DELETE FROM categories WHERE id = $category_id";

        if (mysqli_query($conn, $delete_query)) {
            $success_message = 'Category deleted successfully.';
        } else {
            $error_message = 'Error deleting category: ' . mysqli_error($conn);
        }
    }
}

// Get category to edit
$edit_category = null;
if (isset($_GET['edit']) && $_GET['edit'] > 0) {
    $category_id = (int)$_GET['edit'];

    $edit_query = "SELECT * FROM categories WHERE id = $category_id";
    $edit_result = mysqli_query($conn, $edit_query);

    if (mysqli_num_rows($edit_result) > 0) {
        $edit_category = mysqli_fetch_assoc($edit_result);
    }
}

// Get categories list
$categories_query = "SELECT c.*,
                    (SELECT COUNT(*) FROM movies WHERE category_id = c.id) as movies_count,
                    (SELECT COUNT(*) FROM tvshows WHERE category_id = c.id) as tvshows_count
                    FROM categories c
                    ORDER BY c.name";
$categories_result = mysqli_query($conn, $categories_query);

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Categories</h1>
            </div>
            <div class="topbar-actions">
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                    <i class="fas fa-plus-circle me-2"></i>Add New Category
                </button>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <?php if($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <div class="row">
            <?php if($edit_category): ?>
            <!-- Edit Category Form -->
            <div class="col-lg-4 mb-4">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Edit Category</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="" class="needs-validation" novalidate>
                            <input type="hidden" name="category_id" value="<?php echo $edit_category['id']; ?>">

                            <div class="mb-3">
                                <label for="name" class="form-label">Category Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" value="<?php echo $edit_category['name']; ?>" required>
                                <div class="invalid-feedback">
                                    Please enter a category name.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="type" class="form-label">Type <span class="text-danger">*</span></label>
                                <select class="form-select" id="type" name="type" required>
                                    <option value="both" <?php echo $edit_category['type'] == 'both' ? 'selected' : ''; ?>>Both (Movies & TV Shows)</option>
                                    <option value="movie" <?php echo $edit_category['type'] == 'movie' ? 'selected' : ''; ?>>Movies Only</option>
                                    <option value="tvshow" <?php echo $edit_category['type'] == 'tvshow' ? 'selected' : ''; ?>>TV Shows Only</option>
                                </select>
                                <div class="invalid-feedback">
                                    Please select a type.
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" name="update_category" class="btn btn-primary">Update Category</button>
                                <a href="categories.php" class="btn btn-secondary">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Categories List -->
            <div class="col-lg-<?php echo $edit_category ? '8' : '12'; ?> mb-4">
                <div class="card shadow">
                    <div class="card-header bg-white py-3">
                        <h6 class="m-0 font-weight-bold text-primary">All Categories</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle datatable">
                                <thead class="table-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Type</th>
                                        <th>Movies</th>
                                        <th>TV Shows</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if(mysqli_num_rows($categories_result) > 0): ?>
                                        <?php while($category = mysqli_fetch_assoc($categories_result)): ?>
                                        <tr>
                                            <td><?php echo $category['id']; ?></td>
                                            <td><?php echo $category['name']; ?></td>
                                            <td>
                                                <?php if($category['type'] == 'both'): ?>
                                                <span class="badge bg-primary">Both</span>
                                                <?php elseif($category['type'] == 'movie'): ?>
                                                <span class="badge bg-success">Movies Only</span>
                                                <?php else: ?>
                                                <span class="badge bg-danger">TV Shows Only</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($category['movies_count'] > 0): ?>
                                                <a href="movies.php?category=<?php echo $category['id']; ?>" class="text-decoration-none">
                                                    <?php echo $category['movies_count']; ?> movies
                                                </a>
                                                <?php else: ?>
                                                0 movies
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($category['tvshows_count'] > 0): ?>
                                                <a href="tvshows.php?category=<?php echo $category['id']; ?>" class="text-decoration-none">
                                                    <?php echo $category['tvshows_count']; ?> TV shows
                                                </a>
                                                <?php else: ?>
                                                0 TV shows
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="categories.php?edit=<?php echo $category['id']; ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if($category['movies_count'] == 0 && $category['tvshows_count'] == 0): ?>
                                                    <a href="categories.php?delete=<?php echo $category['id']; ?>" class="btn btn-sm btn-danger btn-delete" data-bs-toggle="tooltip" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                    <?php else: ?>
                                                    <button type="button" class="btn btn-sm btn-secondary" data-bs-toggle="tooltip" title="Cannot delete (in use)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="6" class="text-center">No categories found.</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addCategoryModalLabel">Add New Category</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="POST" action="" class="needs-validation" novalidate>
                    <div class="mb-3">
                        <label for="new_name" class="form-label">Category Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="new_name" name="name" required>
                        <div class="invalid-feedback">
                            Please enter a category name.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="new_type" class="form-label">Type <span class="text-danger">*</span></label>
                        <select class="form-select" id="new_type" name="type" required>
                            <option value="both" selected>Both (Movies & TV Shows)</option>
                            <option value="movie">Movies Only</option>
                            <option value="tvshow">TV Shows Only</option>
                        </select>
                        <div class="invalid-feedback">
                            Please select a type.
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" name="add_category" class="btn btn-primary">Add Category</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
