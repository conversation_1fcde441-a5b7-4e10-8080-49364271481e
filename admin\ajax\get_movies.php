<?php
require_once '../../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Get all movies
$query = "SELECT id, title FROM movies ORDER BY title";
$result = mysqli_query($conn, $query);

$movies = [];
if ($result && mysqli_num_rows($result) > 0) {
    while ($row = mysqli_fetch_assoc($result)) {
        $movies[] = $row;
    }
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($movies);
?>
