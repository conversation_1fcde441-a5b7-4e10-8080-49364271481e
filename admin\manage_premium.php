<?php
// Set page title
$page_title = 'Manage Premium';

require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Process form submissions
$success_message = '';
$error_message = '';

// Bulk Update Movies
if (isset($_POST['update_movies'])) {
    $movie_ids = isset($_POST['movie_ids']) ? $_POST['movie_ids'] : [];
    $action = sanitize($_POST['action']);

    if (empty($movie_ids)) {
        $error_message = 'No movies selected.';
    } else {
        $movie_ids_str = implode(',', array_map('intval', $movie_ids));

        if ($action == 'make_premium') {
            $update_query = "UPDATE movies SET premium_only = 1 WHERE id IN ($movie_ids_str)";
            $success_message = count($movie_ids) . ' movies set as premium.';
        } else if ($action == 'make_free') {
            $update_query = "UPDATE movies SET premium_only = 0 WHERE id IN ($movie_ids_str)";
            $success_message = count($movie_ids) . ' movies set as free.';
        }

        if (!mysqli_query($conn, $update_query)) {
            $error_message = 'Error updating movies: ' . mysqli_error($conn);
        }
    }
}

// Bulk Update TV Shows
if (isset($_POST['update_tvshows'])) {
    $tvshow_ids = isset($_POST['tvshow_ids']) ? $_POST['tvshow_ids'] : [];
    $action = sanitize($_POST['action']);

    if (empty($tvshow_ids)) {
        $error_message = 'No TV shows selected.';
    } else {
        $tvshow_ids_str = implode(',', array_map('intval', $tvshow_ids));

        if ($action == 'make_premium') {
            $update_query = "UPDATE tvshows SET premium_only = 1 WHERE id IN ($tvshow_ids_str)";
            $success_message = count($tvshow_ids) . ' TV shows set as premium.';
        } else if ($action == 'make_free') {
            $update_query = "UPDATE tvshows SET premium_only = 0 WHERE id IN ($tvshow_ids_str)";
            $success_message = count($tvshow_ids) . ' TV shows set as free.';
        }

        if (!mysqli_query($conn, $update_query)) {
            $error_message = 'Error updating TV shows: ' . mysqli_error($conn);
        }
    }
}

// Bulk Update Episodes
if (isset($_POST['update_episodes'])) {
    $episode_ids = isset($_POST['episode_ids']) ? $_POST['episode_ids'] : [];
    $action = sanitize($_POST['action']);

    if (empty($episode_ids)) {
        $error_message = 'No episodes selected.';
    } else {
        $episode_ids_str = implode(',', array_map('intval', $episode_ids));

        if ($action == 'make_premium') {
            $update_query = "UPDATE episodes SET is_premium = 1 WHERE id IN ($episode_ids_str)";
            $success_message = count($episode_ids) . ' episodes set as premium.';
        } else if ($action == 'make_free') {
            $update_query = "UPDATE episodes SET is_premium = 0 WHERE id IN ($episode_ids_str)";
            $success_message = count($episode_ids) . ' episodes set as free.';
        }

        if (!mysqli_query($conn, $update_query)) {
            $error_message = 'Error updating episodes: ' . mysqli_error($conn);
        }
    }
}

// Filter parameters
$content_type = isset($_GET['content_type']) ? sanitize($_GET['content_type']) : 'movies';
$category_id = isset($_GET['category_id']) ? (int)$_GET['category_id'] : 0;
$quality = isset($_GET['quality']) ? sanitize($_GET['quality']) : '';
$premium_status = isset($_GET['premium_status']) ? sanitize($_GET['premium_status']) : '';
$search_term = isset($_GET['search']) ? sanitize($_GET['search']) : '';

// Get categories
$categories_query = "SELECT * FROM categories WHERE type IN ('both', '$content_type') ORDER BY name";
$categories_result = mysqli_query($conn, $categories_query);

// Get content based on filters
if ($content_type == 'movies') {
    $content_query = "SELECT m.*, c.name as category_name,
                     (SELECT dl.quality FROM download_links dl WHERE dl.content_id = m.id AND dl.content_type = 'movie' LIMIT 1) as quality
                     FROM movies m
                     LEFT JOIN categories c ON m.category_id = c.id
                     WHERE 1=1";

    if ($category_id > 0) {
        $content_query .= " AND m.category_id = $category_id";
    }

    // Apply quality filter
    if (!empty($quality)) {
        $content_query .= " AND EXISTS (SELECT 1 FROM download_links dl WHERE dl.content_id = m.id AND dl.content_type = 'movie' AND dl.quality = '$quality')";
    }

    if ($premium_status == 'premium') {
        $content_query .= " AND m.premium_only = 1";
    } else if ($premium_status == 'free') {
        $content_query .= " AND m.premium_only = 0";
    }

    if (!empty($search_term)) {
        $content_query .= " AND (m.title LIKE '%$search_term%' OR m.description LIKE '%$search_term%')";
    }

    $content_query .= " ORDER BY m.title";
} else if ($content_type == 'tvshows') {
    $content_query = "SELECT t.*, c.name as category_name,
                     (SELECT sl.quality FROM streaming_links sl WHERE sl.content_id = t.id AND sl.content_type = 'tvshow' LIMIT 1) as quality
                     FROM tvshows t
                     LEFT JOIN categories c ON t.category_id = c.id
                     WHERE 1=1";

    if ($category_id > 0) {
        $content_query .= " AND t.category_id = $category_id";
    }

    // Apply quality filter
    if (!empty($quality)) {
        $content_query .= " AND EXISTS (SELECT 1 FROM streaming_links sl WHERE sl.content_id = t.id AND sl.content_type = 'tvshow' AND sl.quality = '$quality')";
    }

    if ($premium_status == 'premium') {
        $content_query .= " AND t.premium_only = 1";
    } else if ($premium_status == 'free') {
        $content_query .= " AND t.premium_only = 0";
    }

    if (!empty($search_term)) {
        $content_query .= " AND (t.title LIKE '%$search_term%' OR t.description LIKE '%$search_term%')";
    }

    $content_query .= " ORDER BY t.title";
} else if ($content_type == 'episodes') {
    $content_query = "SELECT e.*, t.title as tvshow_title, e.season_number, e.is_premium,
                     (SELECT el.quality FROM episode_links el WHERE el.episode_id = e.id LIMIT 1) as quality
                     FROM episodes e
                     JOIN tvshows t ON e.tvshow_id = t.id
                     WHERE 1=1";

    if ($category_id > 0) {
        $content_query .= " AND t.category_id = $category_id";
    }

    // Apply quality filter
    if (!empty($quality)) {
        $content_query .= " AND EXISTS (SELECT 1 FROM episode_links el WHERE el.episode_id = e.id AND el.quality = '$quality')";
    }

    if ($premium_status == 'premium') {
        $content_query .= " AND e.is_premium = 1";
    } else if ($premium_status == 'free') {
        $content_query .= " AND e.is_premium = 0";
    }

    if (!empty($search_term)) {
        $content_query .= " AND (e.title LIKE '%$search_term%' OR t.title LIKE '%$search_term%')";
    }

    $content_query .= " ORDER BY t.title, e.season_number, e.episode_number";
}

$content_result = mysqli_query($conn, $content_query);

// Get quality options
$quality_options = [];
if ($content_type == 'movies') {
    $quality_query = "SELECT DISTINCT quality FROM download_links WHERE content_type = 'movie' AND quality IS NOT NULL AND quality != '' ORDER BY quality";
} else if ($content_type == 'tvshows') {
    $quality_query = "SELECT DISTINCT quality FROM streaming_links WHERE content_type = 'tvshow' AND quality IS NOT NULL AND quality != '' ORDER BY quality";
} else if ($content_type == 'episodes') {
    $quality_query = "SELECT DISTINCT quality FROM episode_links WHERE quality IS NOT NULL AND quality != '' ORDER BY quality";
}

$quality_result = mysqli_query($conn, $quality_query);
while ($quality_row = mysqli_fetch_assoc($quality_result)) {
    if (!empty($quality_row['quality'])) {
        $quality_options[] = $quality_row['quality'];
    }
}

// Include header
require_once 'includes/header.php';

// Include sidebar
require_once 'includes/sidebar.php';
?>

<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Manage Premium</h1>
            </div>
            <div class="topbar-actions">
                <div class="topbar-search">
                    <form action="manage_premium.php" method="GET">
                        <input type="hidden" name="content_type" value="<?php echo $content_type; ?>">
                        <input type="hidden" name="category_id" value="<?php echo $category_id; ?>">
                        <input type="hidden" name="quality" value="<?php echo $quality; ?>">
                        <input type="hidden" name="premium_status" value="<?php echo $premium_status; ?>">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="Search..." name="search" value="<?php echo $search_term; ?>">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <?php if($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <!-- Filter Section -->
        <div class="card shadow mb-4">
            <div class="card-header bg-white py-3">
                <h6 class="m-0 font-weight-bold text-primary">Filter Content</h6>
            </div>
            <div class="card-body">
                <form method="GET" action="" class="row g-3">
                    <div class="col-md-3">
                        <label for="content_type" class="form-label">Content Type</label>
                        <select class="form-select" id="content_type" name="content_type" onchange="this.form.submit()">
                            <option value="movies" <?php echo $content_type == 'movies' ? 'selected' : ''; ?>>Movies</option>
                            <option value="tvshows" <?php echo $content_type == 'tvshows' ? 'selected' : ''; ?>>TV Shows</option>
                            <option value="episodes" <?php echo $content_type == 'episodes' ? 'selected' : ''; ?>>Episodes</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="category_id" class="form-label">Category</label>
                        <select class="form-select" id="category_id" name="category_id">
                            <option value="0">All Categories</option>
                            <?php while($category = mysqli_fetch_assoc($categories_result)): ?>
                            <option value="<?php echo $category['id']; ?>" <?php echo $category_id == $category['id'] ? 'selected' : ''; ?>><?php echo $category['name']; ?></option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="quality" class="form-label">Quality</label>
                        <select class="form-select" id="quality" name="quality">
                            <option value="">All Qualities</option>
                            <?php foreach($quality_options as $option): ?>
                            <option value="<?php echo $option; ?>" <?php echo $quality == $option ? 'selected' : ''; ?>><?php echo $option; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="premium_status" class="form-label">Premium Status</label>
                        <select class="form-select" id="premium_status" name="premium_status">
                            <option value="">All</option>
                            <option value="premium" <?php echo $premium_status == 'premium' ? 'selected' : ''; ?>>Premium Only</option>
                            <option value="free" <?php echo $premium_status == 'free' ? 'selected' : ''; ?>>Free</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" value="<?php echo $search_term; ?>" placeholder="Title...">
                    </div>
                    <div class="col-12 text-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter me-2"></i>Apply Filters
                        </button>
                        <a href="manage_premium.php" class="btn btn-secondary">
                            <i class="fas fa-undo me-2"></i>Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Content List -->
        <div class="card shadow">
            <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <?php if($content_type == 'movies'): ?>
                    Manage Movies Premium Status
                    <?php elseif($content_type == 'tvshows'): ?>
                    Manage TV Shows Premium Status
                    <?php else: ?>
                    Manage Episodes Premium Status
                    <?php endif; ?>
                </h6>
                <div>
                    <span class="badge bg-primary fs-6 me-2">
                        <?php echo mysqli_num_rows($content_result); ?> items
                    </span>
                </div>
            </div>
            <div class="card-body">
                <?php if(mysqli_num_rows($content_result) > 0): ?>
                <form method="POST" action="">
                    <div class="mb-3">
                        <div class="btn-group">
                            <button type="submit" name="update_<?php echo $content_type; ?>" class="btn btn-primary" onclick="return confirm('Are you sure you want to update the selected items?')">
                                <i class="fas fa-save me-2"></i>Update Selected
                            </button>
                            <select class="form-select" name="action" required style="max-width: 200px;">
                                <option value="">Select Action</option>
                                <option value="make_premium">Make Premium</option>
                                <option value="make_free">Make Free</option>
                            </select>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover align-middle datatable">
                            <thead class="table-light">
                                <tr>
                                    <th>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="selectAll">
                                            <label class="form-check-label" for="selectAll"></label>
                                        </div>
                                    </th>
                                    <?php if($content_type == 'movies' || $content_type == 'tvshows'): ?>
                                    <th>Title</th>
                                    <th>Category</th>
                                    <th>Quality</th>
                                    <th>Premium</th>
                                    <?php else: ?>
                                    <th>TV Show</th>
                                    <th>Season</th>
                                    <th>Episode</th>
                                    <th>Quality</th>
                                    <th>Premium</th>
                                    <?php endif; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while($item = mysqli_fetch_assoc($content_result)): ?>
                                <tr>
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="<?php echo $content_type == 'episodes' ? 'episode_ids' : ($content_type == 'tvshows' ? 'tvshow_ids' : 'movie_ids'); ?>[]" value="<?php echo $item['id']; ?>">
                                            <label class="form-check-label"></label>
                                        </div>
                                    </td>
                                    <?php if($content_type == 'movies'): ?>

                                    <td>
                                        <a href="edit_movie.php?id=<?php echo $item['id']; ?>" class="text-decoration-none fw-bold">
                                            <?php echo $item['title']; ?>
                                        </a>
                                    </td>
                                    <td><?php echo $item['category_name']; ?></td>
                                    <td><?php echo isset($item['quality']) ? $item['quality'] : 'N/A'; ?></td>
                                    <td>
                                        <?php if($item['premium_only']): ?>
                                        <span class="badge bg-danger">Premium</span>
                                        <?php else: ?>
                                        <span class="badge bg-secondary">Free</span>
                                        <?php endif; ?>
                                    </td>
                                    <?php elseif($content_type == 'tvshows'): ?>

                                    <td>
                                        <a href="edit_tvshow.php?id=<?php echo $item['id']; ?>" class="text-decoration-none fw-bold">
                                            <?php echo $item['title']; ?>
                                        </a>
                                    </td>
                                    <td><?php echo $item['category_name']; ?></td>
                                    <td><?php echo isset($item['quality']) ? $item['quality'] : 'N/A'; ?></td>
                                    <td>
                                        <?php if($item['premium_only']): ?>
                                        <span class="badge bg-danger">Premium</span>
                                        <?php else: ?>
                                        <span class="badge bg-secondary">Free</span>
                                        <?php endif; ?>
                                    </td>
                                    <?php else: ?>
                                    <td>
                                        <a href="edit_tvshow.php?id=<?php echo $item['tvshow_id']; ?>" class="text-decoration-none fw-bold">
                                            <?php echo $item['tvshow_title']; ?>
                                        </a>
                                    </td>
                                    <td>Season <?php echo $item['season_number']; ?></td>
                                    <td>
                                        <a href="manage_episode_links.php?episode=<?php echo $item['id']; ?>" class="text-decoration-none">
                                            Episode <?php echo $item['episode_number']; ?>: <?php echo $item['title']; ?>
                                        </a>
                                    </td>
                                    <td><?php echo isset($item['quality']) ? $item['quality'] : 'N/A'; ?></td>
                                    <td>
                                        <?php if($item['is_premium']): ?>
                                        <span class="badge bg-danger">Premium</span>
                                        <?php else: ?>
                                        <span class="badge bg-secondary">Free</span>
                                        <?php endif; ?>
                                    </td>
                                    <?php endif; ?>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </form>
                <?php else: ?>
                <div class="text-center py-4">
                    <p>No content found matching your filters.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Select All Checkbox -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('tbody .form-check-input');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
        });
    }
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
