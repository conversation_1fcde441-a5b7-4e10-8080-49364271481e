/* Design Improvements for Home Page */

/* 1. Main Slider Improvements */
.main-slider {
    position: relative;
    height: 80vh;
    min-height: 600px;
    margin-top: -56px;
    overflow: hidden;
}

.slide-overlay {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.7) 50%, rgba(0, 0, 0, 0.5) 100%);
}

.slide-title {
    font-size: 3.2rem;
    font-weight: 800;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.slide-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
}

.slide-meta span {
    display: flex;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.9rem;
}

.slide-meta span i {
    margin-right: 5px;
    color: var(--primary-color);
}

.slide-description {
    font-size: 1.1rem;
    margin-bottom: 25px;
    line-height: 1.6;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.slide-buttons {
    display: flex;
    gap: 15px;
}

.slide-buttons .btn {
    padding: 12px 25px;
    font-weight: 600;
    border-radius: 30px;
    transition: all 0.3s ease;
}

.slide-buttons .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.slider-controls {
    position: absolute;
    bottom: 30px;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    z-index: 10;
}

.slider-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.slider-dot.active {
    background-color: var(--primary-color);
    transform: scale(1.3);
}

/* 2. Movie Card Improvements */
.movie-card {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    margin-bottom: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    cursor: pointer;
}

.movie-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 50%, rgba(0, 0, 0, 0.9) 100%);
    z-index: 1;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.movie-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.5);
}

.movie-card:hover::before {
    opacity: 1;
}

.movie-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
}

.movie-card:hover img {
    transform: scale(1.1);
    filter: brightness(0.7);
}

.movie-card-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 15px;
    z-index: 2;
    transform: translateY(0);
    transition: transform 0.3s ease;
}

.movie-card:hover .movie-card-overlay {
    transform: translateY(-5px);
}

.movie-card-title {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 8px;
    line-height: 1.3;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.movie-card-info {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 0.8rem;
    color: #ddd;
    opacity: 0.9;
}

.movie-card-rating {
    display: inline-block;
    background-color: rgba(0, 0, 0, 0.7);
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.movie-card-rating i {
    color: #ffc107;
    margin-right: 4px;
}

.movie-card .btn {
    padding: 6px 15px;
    border-radius: 20px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.movie-card .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
}

/* Make entire card clickable */
.card-link {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
    cursor: pointer;
}

/* Premium Badge Improvements */
.premium-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
    color: #000;
    border-radius: 4px;
    padding: 3px 8px;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    z-index: 4;
}

.premium-badge i {
    margin-right: 4px;
}

.premium-badge .quality {
    margin-left: 4px;
    background-color: rgba(0, 0, 0, 0.2);
    padding: 1px 4px;
    border-radius: 2px;
    font-size: 0.65rem;
}

/* 3. Section Improvements */
.section-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.8rem;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(to right, var(--primary-color), #ff6b6b);
    border-radius: 2px;
}

/* Top 10 Section Improvements */
.top-10-carousel {
    padding: 20px 0;
    gap: 25px;
}

.top-10-item {
    position: relative;
    width: 220px;
    flex-shrink: 0;
}

.top-10-rank {
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 8rem;
    font-weight: 900;
    color: #fff;
    text-shadow:
        -2px -2px 0 #000,
        2px -2px 0 #000,
        -2px 2px 0 #000,
        2px 2px 0 #000;
    z-index: 2;
    opacity: 0.8;
}

.top-10-item .movie-card {
    margin-left: 30px;
    height: 300px;
}

/* Featured Carousel Improvements */
.featured-carousel .owl-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    pointer-events: none;
}

.featured-carousel .owl-prev,
.featured-carousel .owl-next {
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.7) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center;
    justify-content: center;
    pointer-events: auto;
    transition: all 0.3s ease;
}

.featured-carousel .owl-prev:hover,
.featured-carousel .owl-next:hover {
    background-color: var(--primary-color) !important;
    transform: scale(1.1);
}

.featured-carousel .owl-prev {
    left: -20px;
}

.featured-carousel .owl-next {
    right: -20px;
}

/* Latest Section Improvements */
.latest-section .movie-card {
    height: 250px;
}

/* Premium Section Improvements */
.premium-section {
    background: linear-gradient(135deg, #141414 0%, #1f1f1f 100%);
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.premium-header {
    background-color: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

.premium-card {
    border: 2px solid rgba(255, 193, 7, 0.3);
}

/* Loading and Error States */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 0;
    background-color: rgba(20, 20, 20, 0.7);
    border-radius: 10px;
    margin: 30px 0;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-text {
    font-size: 1.2rem;
    color: #fff;
    margin-top: 15px;
}

.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 30px;
    background-color: rgba(20, 20, 20, 0.7);
    border-radius: 10px;
    margin: 30px 0;
    border: 1px solid rgba(229, 9, 20, 0.3);
}

.error-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.error-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #fff;
}

.error-message {
    font-size: 1.1rem;
    color: #adb5bd;
    text-align: center;
    max-width: 600px;
    margin-bottom: 20px;
}

.error-button {
    padding: 10px 25px;
    background-color: var(--primary-color);
    color: #fff;
    border: none;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.error-button:hover {
    background-color: #c00;
    transform: translateY(-3px);
}

/* Footer Improvements */
.footer {
    background-color: #0a0a0a;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.social-icons {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    transition: all 0.3s ease;
}

.social-icon:hover {
    transform: translateY(-5px);
}

.social-icon.facebook:hover {
    background-color: #3b5998;
    color: #fff;
}

.social-icon.twitter:hover {
    background-color: #1da1f2;
    color: #fff;
}

.social-icon.instagram:hover {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
    color: #fff;
}

.social-icon.youtube:hover {
    background-color: #ff0000;
    color: #fff;
}

/* Responsive Improvements */
@media (max-width: 1199.98px) {
    .main-slider {
        height: 75vh;
    }

    .slide-title {
        font-size: 2.8rem;
    }

    .top-10-item {
        width: 200px;
    }

    .top-10-rank {
        font-size: 7rem;
        left: -10px;
    }
}

@media (max-width: 991.98px) {
    .main-slider {
        height: 70vh;
        min-height: 500px;
    }

    .slide-title {
        font-size: 2.5rem;
    }

    .slide-description {
        font-size: 1rem;
    }

    .top-10-item {
        width: 180px;
    }

    .top-10-rank {
        font-size: 6rem;
        left: -5px;
    }

    .top-10-item .movie-card {
        margin-left: 20px;
    }
}

@media (max-width: 767.98px) {
    .main-slider {
        height: 60vh;
        min-height: 450px;
    }

    .slide-title {
        font-size: 2.2rem;
    }

    .slide-description {
        font-size: 0.9rem;
        margin-bottom: 15px;
    }

    .slide-buttons {
        flex-wrap: wrap;
    }

    .slide-buttons .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .top-10-item {
        width: 160px;
    }

    .top-10-rank {
        font-size: 5rem;
        left: -5px;
    }

    .top-10-item .movie-card {
        margin-left: 15px;
        height: 250px;
    }

    .featured-carousel .owl-prev {
        left: -10px;
    }

    .featured-carousel .owl-next {
        right: -10px;
    }
}

@media (max-width: 575.98px) {
    .main-slider {
        height: 50vh;
        min-height: 400px;
    }

    .slide-title {
        font-size: 1.8rem;
    }

    .slide-meta span {
        font-size: 0.8rem;
        padding: 3px 8px;
    }

    .slide-description {
        font-size: 0.85rem;
        margin-bottom: 10px;
    }

    .slide-buttons .btn {
        padding: 8px 15px;
        font-size: 0.85rem;
    }

    .section-title {
        font-size: 1.3rem;
    }

    .top-10-item {
        width: 140px;
    }

    .top-10-rank {
        font-size: 4rem;
        left: -5px;
    }

    .top-10-item .movie-card {
        margin-left: 10px;
        height: 200px;
    }

    .movie-card-title {
        font-size: 0.9rem;
    }

    .movie-card-info {
        font-size: 0.7rem;
    }

    .movie-card-rating {
        font-size: 0.7rem;
        padding: 2px 6px;
    }

    .movie-card .btn {
        padding: 4px 10px;
        font-size: 0.8rem;
    }

    .premium-badge {
        padding: 2px 6px;
        font-size: 0.7rem;
    }
}
