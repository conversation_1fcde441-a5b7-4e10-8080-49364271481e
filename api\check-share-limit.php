<?php
require_once '../includes/config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

function sendResponse($success, $message, $data = null, $warning = false) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'warning' => $warning
    ]);
    exit;
}

if (!isset($_GET['token']) || empty($_GET['token'])) {
    sendResponse(false, 'Token is required');
}

$token = mysqli_real_escape_string($conn, $_GET['token']);

// Get shared link details
$query = "SELECT id, access_limit, access_count, expires_at, is_active, title 
          FROM shared_links 
          WHERE link_token = ? AND is_active = 1";
$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, 's', $token);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    sendResponse(false, 'Invalid or inactive share link');
}

$link = mysqli_fetch_assoc($result);

// Check if link has expired
if ($link['expires_at'] && strtotime($link['expires_at']) < time()) {
    sendResponse(false, 'এই শেয়ার লিংকের মেয়াদ শেষ হয়ে গেছে।');
}

// Check if access limit reached
if ($link['access_limit'] > 0 && $link['access_count'] >= $link['access_limit']) {
    sendResponse(false, 'এই শেয়ার লিংকের এক্সেস লিমিট শেষ হয়ে গেছে।');
}

// Check if approaching limit (warning at 80%)
$warning = false;
$warning_message = '';

if ($link['access_limit'] > 0) {
    $usage_percentage = ($link['access_count'] / $link['access_limit']) * 100;
    
    if ($usage_percentage >= 80 && $usage_percentage < 100) {
        $remaining = $link['access_limit'] - $link['access_count'];
        $warning = true;
        $warning_message = "সতর্কতা: এই লিংকে আর মাত্র {$remaining} বার এক্সেস করা যাবে।";
    }
}

// Return success with link details
sendResponse(true, 'Link is valid', [
    'id' => $link['id'],
    'title' => $link['title'],
    'access_count' => $link['access_count'],
    'access_limit' => $link['access_limit'],
    'usage_percentage' => $link['access_limit'] > 0 ? round(($link['access_count'] / $link['access_limit']) * 100, 2) : 0,
    'remaining_access' => $link['access_limit'] > 0 ? max(0, $link['access_limit'] - $link['access_count']) : 'unlimited'
], $warning ? $warning_message : false);
?>
