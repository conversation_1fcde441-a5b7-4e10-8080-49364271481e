<?php
// Database connection
$host = 'localhost';
$username = 'tipsbdxy_4525';
$password = '@mdsrabon13'; // Add your database password here
$database = 'tipsbdxy_4525';

$conn = mysqli_connect($host, $username, $password, $database);

if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// Admin details
$admin_username = 'admin';
$admin_email = '<EMAIL>';
$admin_password = 'admin123';
$admin_role = 'admin';

// Hash password
$hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);

// Check if admin already exists
$check_query = "SELECT * FROM users WHERE username = '$admin_username' OR email = '$admin_email'";
$check_result = mysqli_query($conn, $check_query);

if (mysqli_num_rows($check_result) > 0) {
    // Update existing admin
    $update_query = "UPDATE users SET password = '$hashed_password' WHERE username = '$admin_username' OR email = '$admin_email'";

    if (mysqli_query($conn, $update_query)) {
        echo "Admin password updated successfully!";
    } else {
        echo "Error updating admin: " . mysqli_error($conn);
    }
} else {
    // Insert new admin
    $insert_query = "INSERT INTO users (username, email, password, role) VALUES ('$admin_username', '$admin_email', '$hashed_password', '$admin_role')";

    if (mysqli_query($conn, $insert_query)) {
        echo "Admin user created successfully!";
    } else {
        echo "Error creating admin: " . mysqli_error($conn);
    }
}

mysqli_close($conn);
?>
