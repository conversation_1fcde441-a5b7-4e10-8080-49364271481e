<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set page title
$page_title = 'লাইভ ইউজার ট্র্যাকিং';
$current_page = 'live_tracking.php';

try {
    // Include configuration and functions
    require_once '../../includes/config.php';
    require_once '../../includes/functions.php';

    // Check if user is logged in and is admin
    if (!isLoggedIn() || !isAdmin()) {
        redirect(SITE_URL . '/login.php');
        exit;
    }
} catch (Exception $e) {
    die("Error: " . $e->getMessage());
}

// Include header
include 'includes/header.php';
?>

<!-- Include Sidebar -->
<?php include 'includes/sidebar.php'; ?>

<!-- Main Content -->
<div class="main-content">
    <!-- Page Header -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-title">
                    <i class="fas fa-eye me-3"></i>লাইভ ইউজার ট্র্যাকিং
                </h1>
                <p class="page-subtitle text-muted">রিয়েল-টাইম ইউজার অ্যাক্টিভিটি মনিটরিং</p>
            </div>
            <div class="col-auto">
                <div class="page-actions">
                    <div class="btn-group">
                        <button class="btn btn-outline-primary" id="refreshBtn">
                            <i class="fas fa-sync-alt me-2"></i>রিফ্রেশ
                        </button>
                        <button class="btn btn-outline-success" id="autoRefreshBtn">
                            <i class="fas fa-play me-2"></i>অটো রিফ্রেশ
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Live Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stat-card bg-gradient-success">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number" id="onlineUsers">0</h3>
                                <p class="stat-label">অনলাইন ইউজার</p>
                                <small class="stat-sublabel">
                                    <i class="fas fa-circle text-success me-1"></i>লাইভ
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stat-card bg-gradient-primary">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-play"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number" id="activeStreams">0</h3>
                                <p class="stat-label">অ্যাক্টিভ স্ট্রিম</p>
                                <small class="stat-sublabel">
                                    <i class="fas fa-video me-1"></i>চলমান
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stat-card bg-gradient-warning">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-crown"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number" id="premiumOnline">0</h3>
                                <p class="stat-label">প্রিমিয়াম অনলাইন</p>
                                <small class="stat-sublabel">
                                    <i class="fas fa-star me-1"></i>প্রিমিয়াম
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stat-card bg-gradient-info">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-globe"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number" id="pageViews">0</h3>
                                <p class="stat-label">পেজ ভিউ</p>
                                <small class="stat-sublabel">
                                    <i class="fas fa-clock me-1"></i>আজ
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Live Activity Feed -->
    <div class="row mb-4">
        <div class="col-xl-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-stream me-2"></i>লাইভ অ্যাক্টিভিটি ফিড
                            </h5>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-success">
                                <i class="fas fa-circle me-1"></i>লাইভ
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="activityFeed" class="activity-feed" style="max-height: 400px; overflow-y: auto;">
                        <!-- Activity items will be loaded here -->
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="text-muted mt-2">লাইভ ডেটা লোড হচ্ছে...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Online Users List -->
        <div class="col-xl-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>অনলাইন ইউজার
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div id="onlineUsersList" style="max-height: 400px; overflow-y: auto;">
                        <!-- Online users will be loaded here -->
                        <div class="text-center py-4">
                            <div class="spinner-border text-success" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="text-muted mt-2">অনলাইন ইউজার লোড হচ্ছে...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Popular Content -->
    <div class="row mb-4">
        <div class="col-xl-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-fire me-2"></i>জনপ্রিয় কনটেন্ট (লাইভ)
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div id="popularContent">
                        <!-- Popular content will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Geographic Distribution -->
        <div class="col-xl-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>ভৌগোলিক বিতরণ
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="geoChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Real-time Chart -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>রিয়েল-টাইম ট্রাফিক
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="realTimeChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Page-specific scripts
$page_scripts = '
<script>
class LiveTracker {
    constructor() {
        this.autoRefresh = false;
        this.refreshInterval = null;
        this.chart = null;
        this.chartData = {
            labels: [],
            data: []
        };
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.initChart();
        this.loadInitialData();
    }
    
    bindEvents() {
        document.getElementById("refreshBtn").addEventListener("click", () => {
            this.refreshData();
        });
        
        document.getElementById("autoRefreshBtn").addEventListener("click", () => {
            this.toggleAutoRefresh();
        });
    }
    
    initChart() {
        const ctx = document.getElementById("realTimeChart");
        this.chart = new Chart(ctx, {
            type: "line",
            data: {
                labels: this.chartData.labels,
                datasets: [{
                    label: "অনলাইন ইউজার",
                    data: this.chartData.data,
                    borderColor: "#e50914",
                    backgroundColor: "rgba(229, 9, 20, 0.1)",
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 750
                },
                plugins: {
                    legend: {
                        labels: { color: "#ffffff" }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: { color: "#b3b3b3" },
                        grid: { color: "#333333" }
                    },
                    x: {
                        ticks: { color: "#b3b3b3" },
                        grid: { color: "#333333" }
                    }
                }
            }
        });
    }
    
    async loadInitialData() {
        await this.refreshData();
    }
    
    async refreshData() {
        try {
            // Simulate API call - replace with actual endpoint
            const data = await this.fetchLiveData();
            
            this.updateStats(data.stats);
            this.updateActivityFeed(data.activities);
            this.updateOnlineUsers(data.onlineUsers);
            this.updatePopularContent(data.popularContent);
            this.updateChart(data.chartPoint);
            
        } catch (error) {
            console.error("Error refreshing data:", error);
            cinepixAdmin.showToast("ডেটা রিফ্রেশ করতে সমস্যা হয়েছে", "error");
        }
    }
    
    async fetchLiveData() {
        // Simulate live data - replace with actual API endpoint
        return new Promise(resolve => {
            setTimeout(() => {
                const now = new Date();
                resolve({
                    stats: {
                        onlineUsers: Math.floor(Math.random() * 100) + 50,
                        activeStreams: Math.floor(Math.random() * 30) + 10,
                        premiumOnline: Math.floor(Math.random() * 25) + 5,
                        pageViews: Math.floor(Math.random() * 1000) + 500
                    },
                    activities: this.generateMockActivities(),
                    onlineUsers: this.generateMockUsers(),
                    popularContent: this.generateMockContent(),
                    chartPoint: {
                        time: now.toLocaleTimeString("bn-BD"),
                        value: Math.floor(Math.random() * 100) + 50
                    }
                });
            }, 500);
        });
    }
    
    generateMockActivities() {
        const activities = [
            "নতুন ইউজার রেজিস্ট্রেশন করেছে",
            "প্রিমিয়াম প্ল্যান কিনেছে", 
            "মুভি দেখা শুরু করেছে",
            "টিভি শো এপিসোড দেখছে",
            "রিভিউ দিয়েছে"
        ];
        
        return Array.from({length: 5}, (_, i) => ({
            id: Date.now() + i,
            user: `user${Math.floor(Math.random() * 1000)}`,
            action: activities[Math.floor(Math.random() * activities.length)],
            time: new Date(Date.now() - Math.random() * 300000).toLocaleTimeString("bn-BD"),
            type: ["success", "info", "warning"][Math.floor(Math.random() * 3)]
        }));
    }
    
    generateMockUsers() {
        return Array.from({length: 10}, (_, i) => ({
            id: i + 1,
            username: `user${Math.floor(Math.random() * 1000)}`,
            isPremium: Math.random() > 0.7,
            currentPage: ["Home", "Movies", "TV Shows", "Profile"][Math.floor(Math.random() * 4)],
            lastSeen: "এখনই"
        }));
    }
    
    generateMockContent() {
        const content = [
            "Avengers: Endgame",
            "Money Heist", 
            "Stranger Things",
            "The Dark Knight",
            "Breaking Bad"
        ];
        
        return content.map((title, i) => ({
            title,
            viewers: Math.floor(Math.random() * 50) + 10,
            type: i % 2 === 0 ? "Movie" : "TV Show"
        }));
    }
    
    updateStats(stats) {
        document.getElementById("onlineUsers").textContent = stats.onlineUsers;
        document.getElementById("activeStreams").textContent = stats.activeStreams;
        document.getElementById("premiumOnline").textContent = stats.premiumOnline;
        document.getElementById("pageViews").textContent = stats.pageViews.toLocaleString();
    }
    
    updateActivityFeed(activities) {
        const feed = document.getElementById("activityFeed");
        feed.innerHTML = activities.map(activity => `
            <div class="activity-item p-3 border-bottom">
                <div class="d-flex align-items-center">
                    <div class="activity-icon me-3">
                        <i class="fas fa-user-circle text-${activity.type}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="fw-bold">${activity.user}</div>
                        <div class="text-muted small">${activity.action}</div>
                    </div>
                    <div class="text-muted small">${activity.time}</div>
                </div>
            </div>
        `).join("");
    }
    
    updateOnlineUsers(users) {
        const list = document.getElementById("onlineUsersList");
        list.innerHTML = users.map(user => `
            <div class="user-item p-3 border-bottom">
                <div class="d-flex align-items-center">
                    <img src="../../assets/img/default-avatar.png" class="rounded-circle me-3" width="32" height="32">
                    <div class="flex-grow-1">
                        <div class="fw-bold">${user.username}</div>
                        <div class="text-muted small">${user.currentPage}</div>
                    </div>
                    <div class="text-end">
                        ${user.isPremium ? \'<span class="badge bg-warning"><i class="fas fa-crown"></i></span>\' : \'\'}
                        <div class="text-success small">
                            <i class="fas fa-circle"></i> ${user.lastSeen}
                        </div>
                    </div>
                </div>
            </div>
        `).join("");
    }
    
    updatePopularContent(content) {
        const container = document.getElementById("popularContent");
        container.innerHTML = content.map((item, index) => `
            <div class="content-item p-3 border-bottom">
                <div class="d-flex align-items-center">
                    <div class="rank-badge me-3">
                        <span class="badge bg-primary">${index + 1}</span>
                    </div>
                    <div class="flex-grow-1">
                        <div class="fw-bold">${item.title}</div>
                        <div class="text-muted small">${item.type}</div>
                    </div>
                    <div class="text-end">
                        <div class="fw-bold text-success">${item.viewers}</div>
                        <div class="text-muted small">দর্শক</div>
                    </div>
                </div>
            </div>
        `).join("");
    }
    
    updateChart(point) {
        // Add new data point
        this.chartData.labels.push(point.time);
        this.chartData.data.push(point.value);
        
        // Keep only last 20 points
        if (this.chartData.labels.length > 20) {
            this.chartData.labels.shift();
            this.chartData.data.shift();
        }
        
        // Update chart
        this.chart.data.labels = this.chartData.labels;
        this.chart.data.datasets[0].data = this.chartData.data;
        this.chart.update("none");
    }
    
    toggleAutoRefresh() {
        const btn = document.getElementById("autoRefreshBtn");
        
        if (this.autoRefresh) {
            // Stop auto refresh
            this.autoRefresh = false;
            clearInterval(this.refreshInterval);
            btn.innerHTML = \'<i class="fas fa-play me-2"></i>অটো রিফ্রেশ\';
            btn.classList.remove("btn-danger");
            btn.classList.add("btn-outline-success");
        } else {
            // Start auto refresh
            this.autoRefresh = true;
            this.refreshInterval = setInterval(() => {
                this.refreshData();
            }, 5000); // Refresh every 5 seconds
            
            btn.innerHTML = \'<i class="fas fa-stop me-2"></i>স্টপ\';
            btn.classList.remove("btn-outline-success");
            btn.classList.add("btn-danger");
        }
    }
}

// Initialize live tracker when page loads
document.addEventListener("DOMContentLoaded", function() {
    window.liveTracker = new LiveTracker();
});
</script>
';

// Include footer
include 'includes/footer.php';
?>
