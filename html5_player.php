<?php
// Include config file to get site URL and other settings
require_once 'includes/config.php';

// Get video URL from query parameter or use default
$video_url = isset($_GET['url']) ? $_GET['url'] : 'https://odd-darkness-074fsadsafsafasfjlknmmlkaytr9pe8afnhdklnfalskdftgy.bdmovieshub.workers.dev/994/RRR+2022+-+Hindi+-WEBDL+-+720p.mkv?hash=AgADjg';
$video_title = isset($_GET['title']) ? $_GET['title'] : 'RRR (2022) - Hindi - WEBDL - 720p';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $video_title; ?> - HTML5 Player</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <style>
        body {
            background-color: #111;
            color: #fff;
            padding: 20px;
        }
        .player-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #000;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        .video-info {
            padding: 20px;
            background-color: #222;
            border-radius: 0 0 8px 8px;
        }
        h1 {
            font-size: 24px;
            margin-bottom: 20px;
        }
        .btn-back {
            margin-bottom: 20px;
        }
        video {
            width: 100%;
            border-radius: 8px 8px 0 0;
            background-color: #000;
        }
        /* Custom video controls */
        video::-webkit-media-controls {
            background-color: rgba(0, 0, 0, 0.5);
        }
        video::-webkit-media-controls-panel {
            background-color: rgba(0, 0, 0, 0.5);
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <a href="<?php echo SITE_URL; ?>" class="btn btn-outline-light btn-back">
            <i class="fas fa-arrow-left"></i> Back to Home
        </a>
        
        <div class="player-container">
            <!-- HTML5 Video Player -->
            <video id="video-player" controls autoplay="false" controlsList="nodownload">
                <source src="<?php echo $video_url; ?>" type="video/mp4">
                Your browser does not support the video tag.
            </video>
            
            <div class="video-info">
                <h1><?php echo $video_title; ?></h1>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <span class="badge bg-danger me-2">720p</span>
                        <span class="badge bg-secondary me-2">Hindi</span>
                        <span class="badge bg-info">WEBDL</span>
                    </div>
                    <div>
                        <button id="copy-url" class="btn btn-sm btn-outline-light" data-url="<?php echo SITE_URL; ?>/html5_player.php?url=<?php echo urlencode($video_url); ?>&title=<?php echo urlencode($video_title); ?>">
                            <i class="fas fa-copy"></i> Copy Player URL
                        </button>
                    </div>
                </div>
                
                <div class="alert alert-info">
                    <strong>Note:</strong> If the video doesn't play, it might be due to CORS restrictions or the video format not being supported by the browser.
                </div>
                
                <div class="mt-4">
                    <h5>Player Controls:</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-2">
                                <button id="play-pause" class="btn btn-sm btn-outline-light me-2">
                                    <i class="fas fa-play"></i> Play/Pause
                                </button>
                                <button id="mute-unmute" class="btn btn-sm btn-outline-light me-2">
                                    <i class="fas fa-volume-up"></i> Mute/Unmute
                                </button>
                                <button id="fullscreen" class="btn btn-sm btn-outline-light">
                                    <i class="fas fa-expand"></i> Fullscreen
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <label for="volume-control" class="form-label me-2 mb-0">Volume:</label>
                                <input type="range" class="form-range" id="volume-control" min="0" max="1" step="0.1" value="1" style="width: 100px;">
                                
                                <label for="playback-speed" class="form-label ms-3 me-2 mb-0">Speed:</label>
                                <select id="playback-speed" class="form-select form-select-sm" style="width: 80px;">
                                    <option value="0.5">0.5x</option>
                                    <option value="0.75">0.75x</option>
                                    <option value="1" selected>1x</option>
                                    <option value="1.25">1.25x</option>
                                    <option value="1.5">1.5x</option>
                                    <option value="2">2x</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const videoPlayer = document.getElementById('video-player');
            const playPauseBtn = document.getElementById('play-pause');
            const muteUnmuteBtn = document.getElementById('mute-unmute');
            const fullscreenBtn = document.getElementById('fullscreen');
            const volumeControl = document.getElementById('volume-control');
            const playbackSpeed = document.getElementById('playback-speed');
            
            // Play/Pause button
            playPauseBtn.addEventListener('click', function() {
                if (videoPlayer.paused) {
                    videoPlayer.play();
                    playPauseBtn.innerHTML = '<i class="fas fa-pause"></i> Play/Pause';
                } else {
                    videoPlayer.pause();
                    playPauseBtn.innerHTML = '<i class="fas fa-play"></i> Play/Pause';
                }
            });
            
            // Mute/Unmute button
            muteUnmuteBtn.addEventListener('click', function() {
                videoPlayer.muted = !videoPlayer.muted;
                if (videoPlayer.muted) {
                    muteUnmuteBtn.innerHTML = '<i class="fas fa-volume-mute"></i> Mute/Unmute';
                    volumeControl.value = 0;
                } else {
                    muteUnmuteBtn.innerHTML = '<i class="fas fa-volume-up"></i> Mute/Unmute';
                    volumeControl.value = videoPlayer.volume;
                }
            });
            
            // Fullscreen button
            fullscreenBtn.addEventListener('click', function() {
                if (videoPlayer.requestFullscreen) {
                    videoPlayer.requestFullscreen();
                } else if (videoPlayer.webkitRequestFullscreen) {
                    videoPlayer.webkitRequestFullscreen();
                } else if (videoPlayer.msRequestFullscreen) {
                    videoPlayer.msRequestFullscreen();
                }
            });
            
            // Volume control
            volumeControl.addEventListener('input', function() {
                videoPlayer.volume = volumeControl.value;
                if (volumeControl.value == 0) {
                    videoPlayer.muted = true;
                    muteUnmuteBtn.innerHTML = '<i class="fas fa-volume-mute"></i> Mute/Unmute';
                } else {
                    videoPlayer.muted = false;
                    muteUnmuteBtn.innerHTML = '<i class="fas fa-volume-up"></i> Mute/Unmute';
                }
            });
            
            // Playback speed
            playbackSpeed.addEventListener('change', function() {
                videoPlayer.playbackRate = playbackSpeed.value;
            });
            
            // Update play/pause button state
            videoPlayer.addEventListener('play', function() {
                playPauseBtn.innerHTML = '<i class="fas fa-pause"></i> Play/Pause';
            });
            
            videoPlayer.addEventListener('pause', function() {
                playPauseBtn.innerHTML = '<i class="fas fa-play"></i> Play/Pause';
            });
            
            // Copy URL button
            document.getElementById('copy-url').addEventListener('click', function() {
                const url = this.getAttribute('data-url');
                navigator.clipboard.writeText(url).then(() => {
                    this.innerHTML = '<i class="fas fa-check"></i> Copied!';
                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-copy"></i> Copy Player URL';
                    }, 2000);
                });
            });
        });
    </script>
</body>
</html>
