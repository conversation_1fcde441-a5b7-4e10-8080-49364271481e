import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:cinepix_app/models/download_link.dart';
import 'package:cinepix_app/services/storage_service.dart';

class DownloadItem {
  final String id;
  final String title;
  final String url;
  final String fileName;
  final String filePath;
  final int contentId;
  final String contentType; // 'movie' or 'episode'
  final String quality;
  final int fileSize;
  final DateTime downloadedAt;
  final DateTime? expiresAt;

  DownloadItem({
    required this.id,
    required this.title,
    required this.url,
    required this.fileName,
    required this.filePath,
    required this.contentId,
    required this.contentType,
    required this.quality,
    required this.fileSize,
    required this.downloadedAt,
    this.expiresAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'url': url,
      'fileName': fileName,
      'filePath': filePath,
      'contentId': contentId,
      'contentType': contentType,
      'quality': quality,
      'fileSize': fileSize,
      'downloadedAt': downloadedAt.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
    };
  }

  factory DownloadItem.fromJson(Map<String, dynamic> json) {
    return DownloadItem(
      id: json['id'],
      title: json['title'],
      url: json['url'],
      fileName: json['fileName'],
      filePath: json['filePath'],
      contentId: json['contentId'],
      contentType: json['contentType'],
      quality: json['quality'],
      fileSize: json['fileSize'],
      downloadedAt: DateTime.parse(json['downloadedAt']),
      expiresAt:
          json['expiresAt'] != null ? DateTime.parse(json['expiresAt']) : null,
    );
  }
}

class DownloadManager {
  static final DownloadManager _instance = DownloadManager._internal();
  factory DownloadManager() => _instance;
  DownloadManager._internal();

  Database? _database;
  final StorageService _storageService = StorageService();

  // Download settings
  int maxConcurrentDownloads = 3;
  int autoDeleteDays = 7; // Auto delete after 7 days
  int maxStorageGB = 5; // Max 5GB storage
  String downloadQuality = 'auto'; // auto, 720p, 1080p

  Future<void> initialize() async {
    await _initializeDatabase();
    await _loadSettings();
    await _cleanupExpiredFiles();
  }

  Future<void> _loadSettings() async {
    maxConcurrentDownloads =
        await _storageService.getInt('max_concurrent_downloads') ?? 3;
    autoDeleteDays = await _storageService.getInt('auto_delete_days') ?? 7;
    maxStorageGB = await _storageService.getInt('max_storage_gb') ?? 5;
    downloadQuality =
        await _storageService.getString('download_quality') ?? 'auto';
  }

  Future<void> _initializeDatabase() async {
    final documentsDirectory = await getApplicationDocumentsDirectory();
    final path = join(documentsDirectory.path, 'downloads.db');

    _database = await openDatabase(
      path,
      version: 1,
      onCreate: (db, version) {
        return db.execute(
          'CREATE TABLE downloads(id TEXT PRIMARY KEY, title TEXT, url TEXT, fileName TEXT, filePath TEXT, contentId INTEGER, contentType TEXT, quality TEXT, fileSize INTEGER, downloadedAt TEXT, expiresAt TEXT)',
        );
      },
    );
  }

  // Simplified download implementation without flutter_downloader
  Future<String?> _downloadFile(String url, String filePath) async {
    try {
      final httpClient = HttpClient();
      final request = await httpClient.getUrl(Uri.parse(url));
      request.headers.add('User-Agent', 'CinePix/1.0');

      final response = await request.close();
      if (response.statusCode == 200) {
        final file = File(filePath);
        await response.pipe(file.openWrite());
        return filePath;
      }
      return null;
    } catch (e) {
      debugPrint('Download error: $e');
      return null;
    }
  }

  Future<bool> requestPermissions() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.request();
      return status.isGranted;
    }
    return true;
  }

  Future<String> _getDownloadPath() async {
    Directory? directory;

    if (Platform.isAndroid) {
      directory = await getExternalStorageDirectory();
      directory = Directory('${directory!.path}/CinePix/Downloads');
    } else {
      directory = await getApplicationDocumentsDirectory();
      directory = Directory('${directory.path}/Downloads');
    }

    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }

    return directory.path;
  }

  Future<String?> startDownload({
    required DownloadLink downloadLink,
    required String title,
    required int contentId,
    required String contentType,
  }) async {
    try {
      // Check permissions
      if (!await requestPermissions()) {
        throw Exception('Storage permission denied');
      }

      // Check storage space
      if (!await _hasEnoughStorage()) {
        throw Exception('Not enough storage space');
      }

      final downloadPath = await _getDownloadPath();
      final fileName = _generateFileName(title, downloadLink.quality);

      final filePath = '$downloadPath/$fileName';
      final taskId = DateTime.now().millisecondsSinceEpoch.toString();

      // Save to database first
      final downloadItem = DownloadItem(
        id: taskId,
        title: title,
        url: downloadLink.url,
        fileName: fileName,
        filePath: filePath,
        contentId: contentId,
        contentType: contentType,
        quality: downloadLink.quality,
        fileSize: 0, // Will be updated when download completes
        downloadedAt: DateTime.now(),
        expiresAt: autoDeleteDays > 0
            ? DateTime.now().add(Duration(days: autoDeleteDays))
            : null,
      );

      await _saveDownloadItem(downloadItem);

      // Start download in background
      _downloadFile(downloadLink.url, filePath).then((result) {
        if (result != null) {
          _onDownloadComplete(taskId);
        } else {
          _onDownloadFailed(taskId);
        }
      });

      return taskId;
    } catch (e) {
      debugPrint('Download error: $e');
      return null;
    }
  }

  String _generateFileName(String title, String quality) {
    // Clean title for filename
    final cleanTitle = title.replaceAll(RegExp(r'[^\w\s-]'), '').trim();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${cleanTitle}_${quality}_$timestamp.mp4';
  }

  Future<bool> _hasEnoughStorage() async {
    // Simple storage check - can be improved
    // Assume we need at least 1GB free space
    return true; // Simplified for now
  }

  Future<void> _saveDownloadItem(DownloadItem item) async {
    await _database?.insert(
      'downloads',
      item.toJson(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<void> _onDownloadComplete(String taskId) async {
    // Update file size and other info
    final item = await _getDownloadItemById(taskId);
    if (item != null) {
      final file = File(item.filePath);
      if (await file.exists()) {
        final fileSize = await file.length();

        await _database?.update(
          'downloads',
          {'fileSize': fileSize},
          where: 'id = ?',
          whereArgs: [taskId],
        );
      }
    }
  }

  Future<void> _onDownloadFailed(String taskId) async {
    // Remove failed download from database
    await _database?.delete(
      'downloads',
      where: 'id = ?',
      whereArgs: [taskId],
    );
  }

  Future<List<DownloadItem>> getDownloadedItems() async {
    final List<Map<String, dynamic>> maps =
        await _database?.query('downloads') ?? [];
    return List.generate(maps.length, (i) => DownloadItem.fromJson(maps[i]));
  }

  Future<DownloadItem?> getDownloadedItem(
      int contentId, String contentType) async {
    final List<Map<String, dynamic>> maps = await _database?.query(
          'downloads',
          where: 'contentId = ? AND contentType = ?',
          whereArgs: [contentId, contentType],
        ) ??
        [];

    if (maps.isNotEmpty) {
      return DownloadItem.fromJson(maps.first);
    }
    return null;
  }

  Future<bool> isDownloaded(int contentId, String contentType) async {
    final item = await getDownloadedItem(contentId, contentType);
    if (item != null) {
      final file = File(item.filePath);
      return await file.exists();
    }
    return false;
  }

  Future<void> deleteDownload(String taskId) async {
    final item = await _getDownloadItemById(taskId);
    if (item != null) {
      // Delete file
      final file = File(item.filePath);
      if (await file.exists()) {
        await file.delete();
      }

      // Remove from database
      await _database?.delete(
        'downloads',
        where: 'id = ?',
        whereArgs: [taskId],
      );

      // Note: In simplified implementation, we can't cancel ongoing downloads
    }
  }

  Future<DownloadItem?> _getDownloadItemById(String id) async {
    final List<Map<String, dynamic>> maps = await _database?.query(
          'downloads',
          where: 'id = ?',
          whereArgs: [id],
        ) ??
        [];

    if (maps.isNotEmpty) {
      return DownloadItem.fromJson(maps.first);
    }
    return null;
  }

  Future<void> _cleanupExpiredFiles() async {
    if (autoDeleteDays <= 0) return;

    final now = DateTime.now();
    final expiredItems = await _database?.query(
          'downloads',
          where: 'expiresAt < ?',
          whereArgs: [now.toIso8601String()],
        ) ??
        [];

    for (final itemMap in expiredItems) {
      final item = DownloadItem.fromJson(itemMap);
      await deleteDownload(item.id);
    }
  }

  Future<int> getTotalStorageUsed() async {
    final items = await getDownloadedItems();
    int totalSize = 0;

    for (final item in items) {
      final file = File(item.filePath);
      if (await file.exists()) {
        totalSize += await file.length();
      }
    }

    return totalSize;
  }

  Future<void> updateSettings({
    int? maxConcurrentDownloads,
    int? autoDeleteDays,
    int? maxStorageGB,
    String? downloadQuality,
  }) async {
    if (maxConcurrentDownloads != null) {
      this.maxConcurrentDownloads = maxConcurrentDownloads;
      await _storageService.saveInt(
          'max_concurrent_downloads', maxConcurrentDownloads);
    }
    if (autoDeleteDays != null) {
      this.autoDeleteDays = autoDeleteDays;
      await _storageService.saveInt('auto_delete_days', autoDeleteDays);
    }
    if (maxStorageGB != null) {
      this.maxStorageGB = maxStorageGB;
      await _storageService.saveInt('max_storage_gb', maxStorageGB);
    }
    if (downloadQuality != null) {
      this.downloadQuality = downloadQuality;
      await _storageService.saveString('download_quality', downloadQuality);
    }
  }

  void dispose() {
    // Cleanup if needed
  }
}
