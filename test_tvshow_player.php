<?php
// Include necessary files
require_once 'includes/config.php';
require_once 'includes/streaming_helper.php';

// Test data
$tvshow_id = 1; // Replace with a valid TV show ID from your database
$episode_id = 1; // Replace with a valid episode ID from your database

// Get TV show details
$tvshow_query = "SELECT * FROM tvshows WHERE id = $tvshow_id";
$tvshow_result = mysqli_query($conn, $tvshow_query);
$tvshow = mysqli_fetch_assoc($tvshow_result);

// Get episode details
$episode_query = "SELECT * FROM episodes WHERE id = $episode_id";
$episode_result = mysqli_query($conn, $episode_query);
$episode = mysqli_fetch_assoc($episode_result);

// Get streaming links for this episode
$stream_query = "SELECT * FROM episode_links
                WHERE episode_id = $episode_id
                AND link_type = 'stream'
                ORDER BY quality DESC, server_name ASC";
$stream_result = mysqli_query($conn, $stream_query);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>টিভি শো প্লেয়ার টেস্ট</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #111;
            color: #fff;
            padding: 20px;
            font-family: 'SolaimanLipi', Arial, sans-serif;
        }
        .card {
            background-color: #222;
            border: none;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #333;
            color: #fff;
            border-bottom: 1px solid #444;
        }
        .btn-primary {
            background-color: #e50914;
            border-color: #e50914;
        }
        .btn-primary:hover {
            background-color: #b30710;
            border-color: #b30710;
        }
        .feature-list {
            list-style-type: none;
            padding-left: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #333;
            display: flex;
            align-items: center;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list li i {
            color: #e50914;
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        .episode-list {
            background-color: #333;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .episode-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #444;
            margin-bottom: 10px;
        }
        .episode-number {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #e50914;
            color: white;
            border-radius: 50%;
            margin-right: 15px;
            font-weight: bold;
        }
        .episode-title {
            flex: 1;
        }
        .episode-title h5 {
            margin-bottom: 5px;
        }
        .episode-meta {
            font-size: 14px;
            color: #aaa;
        }
        .stream-links {
            margin-top: 15px;
        }
        .stream-link {
            display: inline-block;
            margin-right: 10px;
            margin-bottom: 10px;
            padding: 8px 15px;
            background-color: #333;
            color: white;
            border-radius: 4px;
            text-decoration: none;
            transition: background-color 0.3s;
        }
        .stream-link:hover {
            background-color: #e50914;
            color: white;
        }
        .premium-badge {
            display: inline-block;
            background-color: #e50914;
            color: white;
            font-size: 12px;
            padding: 3px 8px;
            border-radius: 4px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">টিভি শো প্লেয়ার টেস্ট</h1>

        <div class="card">
            <div class="card-header">
                <h5>টিভি শো তথ্য</h5>
            </div>
            <div class="card-body">
                <?php if ($tvshow): ?>
                <div class="row">
                    <div class="col-md-3">
                        <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $tvshow['poster']; ?>" alt="<?php echo $tvshow['title']; ?>" class="img-fluid rounded">
                    </div>
                    <div class="col-md-9">
                        <h3><?php echo $tvshow['title']; ?></h3>
                        <div class="mb-3">
                            <span class="me-3"><i class="fas fa-star text-warning"></i> <?php echo number_format($tvshow['rating'], 1); ?></span>
                            <span class="me-3"><i class="fas fa-calendar-alt"></i> <?php echo $tvshow['start_year']; ?><?php echo $tvshow['end_year'] ? ' - ' . $tvshow['end_year'] : ' - Present'; ?></span>
                            <span><i class="fas fa-film"></i> <?php echo $tvshow['seasons']; ?> Season<?php echo $tvshow['seasons'] > 1 ? 's' : ''; ?></span>
                        </div>
                        <p><?php echo $tvshow['description']; ?></p>
                    </div>
                </div>
                <?php else: ?>
                <div class="alert alert-warning">টিভি শো পাওয়া যায়নি। সঠিক ID দিয়ে আবার চেষ্টা করুন।</div>
                <?php endif; ?>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5>এপিসোড তথ্য</h5>
            </div>
            <div class="card-body">
                <?php if ($episode): ?>
                <div class="episode-item">
                    <div class="episode-number"><?php echo $episode['episode_number']; ?></div>
                    <div class="episode-title">
                        <h5>
                            S<?php echo $episode['season_number']; ?>E<?php echo str_pad($episode['episode_number'], 2, '0', STR_PAD_LEFT); ?>: <?php echo $episode['title']; ?>
                            <?php if ($episode['is_premium']): ?>
                            <span class="premium-badge">PREMIUM</span>
                            <?php endif; ?>
                        </h5>
                        <div class="episode-meta">
                            <span class="me-3"><i class="fas fa-clock"></i> <?php echo $episode['duration']; ?> min</span>
                            <?php if ($episode['release_date']): ?>
                            <span><i class="fas fa-calendar-alt"></i> <?php echo date('F j, Y', strtotime($episode['release_date'])); ?></span>
                            <?php endif; ?>
                        </div>
                        <p class="mt-2"><?php echo $episode['description']; ?></p>
                    </div>
                </div>
                <?php else: ?>
                <div class="alert alert-warning">এপিসোড পাওয়া যায়নি। সঠিক ID দিয়ে আবার চেষ্টা করুন।</div>
                <?php endif; ?>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5>স্ট্রিমিং লিংকসমূহ</h5>
            </div>
            <div class="card-body">
                <?php if (mysqli_num_rows($stream_result) > 0): ?>
                <div class="stream-links">
                    <?php
                    // Reset pointer to beginning
                    mysqli_data_seek($stream_result, 0);

                    // Get episode thumbnail or use TV show poster as fallback
                    $poster_url = SITE_URL . '/uploads/' . (!empty($episode['thumbnail']) ? $episode['thumbnail'] : $tvshow['poster']);

                    while ($stream = mysqli_fetch_assoc($stream_result)):
                        // Check if it's a Cloudflare Worker link
                        $is_worker_url = (stripos($stream['link_url'], 'workers.dev') !== false);

                        if ($is_worker_url) {
                            // Generate enhanced player URL for worker links
                            $player_url = getEpisodeStreamingUrl(
                                $stream['link_url'],
                                $episode['title'],
                                $poster_url,
                                $episode['is_premium'],
                                $tvshow['title'],
                                $episode['season_number'],
                                $episode['episode_number']
                            );
                        } else {
                            // Use episode_player.php for non-worker links
                            $player_url = SITE_URL . '/episode_player.php?id=' . $episode_id . '&stream=' . $stream['id'];
                        }

                        // Get server name
                        $server_name = !empty($stream['server_name']) ? $stream['server_name'] : 'Server';
                    ?>
                    <a href="<?php echo $player_url; ?>" class="stream-link">
                        <i class="fas fa-play-circle me-2"></i>
                        <?php echo $server_name; ?> (<?php echo $stream['quality']; ?>)
                        <?php if ($stream['is_premium']): ?>
                        <i class="fas fa-crown text-warning ms-1"></i>
                        <?php endif; ?>
                        <?php if ($is_worker_url): ?>
                        <i class="fas fa-bolt text-info ms-1" title="Enhanced Player"></i>
                        <?php endif; ?>
                    </a>
                    <?php endwhile; ?>
                </div>
                <?php else: ?>
                <div class="alert alert-info">এই এপিসোডের জন্য কোন স্ট্রিমিং লিংক পাওয়া যায়নি।</div>
                <?php endif; ?>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5>ব্যবহার নির্দেশিকা</h5>
            </div>
            <div class="card-body">
                <p>টিভি শো ডিটেইলস পেজে এপিসোডের স্ট্রিমিং লিংকগুলি দেখানোর জন্য:</p>
                <ol>
                    <li>অ্যাডমিন প্যানেলে যান</li>
                    <li>এপিসোড লিংক ম্যানেজমেন্টে যান</li>
                    <li>প্রতিটি এপিসোডের জন্য একাধিক স্ট্রিমিং লিংক যুক্ত করুন</li>
                    <li>Cloudflare Worker লিংক যুক্ত করলে সেগুলি স্বয়ংক্রিয়ভাবে উন্নত প্লেয়ারে প্লে হবে</li>
                    <li>টিভি শো ডিটেইলস পেজে এখন "Watch" বাটনে ক্লিক করলে ড্রপডাউন মেনুতে সব স্ট্রিমিং লিংক দেখাবে</li>
                </ol>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>নোট:</strong> Cloudflare Worker লিংক (যেগুলিতে "workers.dev" আছে) স্বয়ংক্রিয়ভাবে উন্নত প্লেয়ারে প্লে হবে। অন্যান্য লিংক সাধারণ প্লেয়ারে প্লে হবে।
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
