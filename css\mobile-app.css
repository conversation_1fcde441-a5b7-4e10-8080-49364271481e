/**
 * Mobile App Style for CinePix PWA
 * Makes the website look and feel like a native mobile app
 */

/* App-like styling for mobile devices */
@media (max-width: 767px) {
    /* Remove browser-specific UI elements when in PWA mode */
    body.pwa-mode {
        overscroll-behavior: none; /* Prevents pull-to-refresh */
        -webkit-touch-callout: none; /* Disables callout on long-press */
        -webkit-user-select: none; /* Disables text selection */
        -webkit-tap-highlight-color: transparent; /* Removes tap highlight */
    }

    /* App-like header */
    .navbar {
        height: 60px;
        padding: 0 15px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    }

    /* Make navbar fixed at bottom like mobile apps */
    .mobile-nav-bottom {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: #000;
        display: flex;
        justify-content: space-around;
        padding: 10px 0;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
        z-index: 1000;
    }

    .mobile-nav-bottom .nav-item {
        text-align: center;
        flex: 1;
    }

    .mobile-nav-bottom .nav-link {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 0.8rem;
        padding: 5px;
    }

    .mobile-nav-bottom .nav-link i {
        font-size: 1.5rem;
        margin-bottom: 5px;
    }

    /* Add padding to main content to account for bottom navbar */
    main {
        padding-bottom: 70px;
    }

    /* App-like card styling */
    .movie-card {
        border-radius: 12px;
        overflow: hidden;
        margin-bottom: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    /* App-like buttons */
    .btn {
        border-radius: 50px;
        padding: 8px 20px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.85rem;
    }

    /* App-like animations */
    .movie-card, .btn, .nav-link {
        transition: all 0.2s ease-out;
    }

    .movie-card:active, .btn:active, .nav-link:active {
        transform: scale(0.95);
    }

    /* Hide footer in app mode */
    body.pwa-mode .footer {
        display: none;
    }

    /* App-like scrolling */
    .horizontal-scroll {
        overflow-x: auto;
        overflow-y: hidden;
        white-space: nowrap;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none; /* Firefox */
        padding: 10px 0;
    }

    .horizontal-scroll::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
    }

    .horizontal-scroll .movie-card {
        display: inline-block;
        width: 140px;
        margin-right: 15px;
        white-space: normal;
        vertical-align: top;
    }

    /* Smooth scrolling for entire app */
    html, body {
        scroll-behavior: smooth;
    }

    /* Hide scrollbars but keep functionality */
    body::-webkit-scrollbar {
        width: 0;
        background: transparent;
    }

    body {
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    /* App-like section headers */
    .section-title {
        font-size: 1.2rem;
        font-weight: 700;
        margin-bottom: 15px;
        padding-left: 15px;
    }

    /* App-like search bar */
    .app-search-bar {
        position: relative;
        margin: 15px;
    }

    .app-search-bar input {
        width: 100%;
        padding: 12px 20px;
        padding-left: 45px;
        border-radius: 50px;
        border: none;
        background-color: rgba(255, 255, 255, 0.1);
        color: #fff;
        font-size: 1rem;
    }

    .app-search-bar i {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: rgba(255, 255, 255, 0.5);
        font-size: 1.2rem;
    }

    /* App-like loading indicators */
    .app-loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100px;
    }

    .app-loading .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        border-top-color: #e50914;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }
}

/* Add iOS-specific styling */
@supports (-webkit-touch-callout: none) {
    body.pwa-mode {
        /* iOS-specific padding for notch/home indicator */
        padding-top: env(safe-area-inset-top);
        padding-bottom: env(safe-area-inset-bottom);
        padding-left: env(safe-area-inset-left);
        padding-right: env(safe-area-inset-right);
    }

    .mobile-nav-bottom {
        padding-bottom: env(safe-area-inset-bottom);
    }
}

/* Add Android-specific styling */
@supports not (-webkit-touch-callout: none) {
    body.pwa-mode {
        /* Android-specific styles */
    }
}
