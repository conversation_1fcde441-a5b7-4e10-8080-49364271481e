import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:cinepix_app/constants/app_constants.dart';
import 'package:cinepix_app/controllers/movie_controller.dart';
import 'package:cinepix_app/controllers/tv_show_controller.dart';
import 'package:cinepix_app/controllers/category_controller.dart';
import 'package:cinepix_app/controllers/watch_history_controller.dart';
import 'package:cinepix_app/models/movie.dart';
import 'package:cinepix_app/models/tv_show.dart';
import 'package:cinepix_app/views/movie_details_screen.dart';
import 'package:cinepix_app/views/tv_show_details_screen.dart';
import 'package:cinepix_app/views/search_screen.dart';
import 'package:cinepix_app/views/profile_screen.dart';
import 'package:cinepix_app/views/category_screen.dart';

import 'package:cinepix_app/views/video_player_screen.dart';
import 'package:cinepix_app/widgets/carousel_slider_widget.dart';
import 'package:cinepix_app/widgets/content_section.dart';
import 'package:cinepix_app/widgets/continue_watching_section.dart';
import 'package:cinepix_app/widgets/genre_section.dart';
import 'package:cinepix_app/widgets/movie_card.dart';
import 'package:cinepix_app/widgets/tv_show_card.dart';
import 'package:cinepix_app/utils/tv_focus_manager.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  // Static reference to the current state
  static _HomeScreenState? of(BuildContext context) {
    return context.findAncestorStateOfType<_HomeScreenState>();
  }

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  // Controllers are not used directly in this class, but kept for reference
  // final MovieController _movieController = Get.find<MovieController>();
  // final TvShowController _tvShowController = Get.find<TvShowController>();
  // final CategoryController _categoryController = Get.find<CategoryController>();
  // final AuthController _authController = Get.find<AuthController>();

  int _currentIndex = 0;

  // Method to change the current tab
  void changeTab(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  final List<Widget> _screens = [
    const _HomeTab(),
    const _MoviesTab(),
    const _TvShowsTab(),
    const ProfileScreen(),
  ];

  // Build a TV-friendly bottom navigation
  Widget _buildTvBottomNavigation() {
    return Container(
      height: 70,
      color: AppConstants.surfaceColor,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildTvNavItem(0, Icons.home, 'Home'),
          _buildTvNavItem(1, Icons.movie, 'Movies'),
          _buildTvNavItem(2, Icons.tv, 'TV Shows'),
          _buildTvNavItem(3, Icons.person, 'Profile'),
        ],
      ),
    );
  }

  // Build a TV-friendly navigation item with focus support
  Widget _buildTvNavItem(int index, IconData icon, String label) {
    final isSelected = _currentIndex == index;

    return TvFocusManager.createFocusableWidget(
      onTap: () {
        setState(() {
          _currentIndex = index;
        });
      },
      autofocus: index == 0,
      focusColor: AppConstants.primaryColor,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: isSelected
              ? AppConstants.primaryColor.withAlpha(50)
              : Colors.transparent,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? AppConstants.primaryColor : Colors.grey,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? AppConstants.primaryColor : Colors.grey,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_currentIndex],
      bottomNavigationBar: TvFocusManager.isAndroidTV()
          ? _buildTvBottomNavigation()
          : BottomNavigationBar(
              currentIndex: _currentIndex,
              onTap: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              type: BottomNavigationBarType.fixed,
              backgroundColor: AppConstants.surfaceColor,
              selectedItemColor: AppConstants.primaryColor,
              unselectedItemColor: Colors.grey,
              items: const [
                BottomNavigationBarItem(
                  icon: Icon(Icons.home),
                  label: 'Home',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.movie),
                  label: 'Movies',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.tv),
                  label: 'TV Shows',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.person),
                  label: 'Profile',
                ),
              ],
            ),
    );
  }
}

class _HomeTab extends StatefulWidget {
  const _HomeTab();

  @override
  State<_HomeTab> createState() => _HomeTabState();
}

class _HomeTabState extends State<_HomeTab> {
  final MovieController _movieController = Get.find<MovieController>();
  final TvShowController _tvShowController = Get.find<TvShowController>();
  final CategoryController _categoryController = Get.find<CategoryController>();
  final WatchHistoryController _watchHistoryController =
      Get.find<WatchHistoryController>();

  @override
  void initState() {
    super.initState();
    _refreshData();
  }

  Future<void> _refreshData() async {
    await Future.wait([
      _movieController.loadFeaturedMovies(),
      _movieController.loadLatestMovies(),
      _movieController.loadPopularMovies(),
      _tvShowController.loadFeaturedTvShows(),
      _tvShowController.loadLatestTvShows(),
      _categoryController.loadCategories(),
      _watchHistoryController.loadWatchHistory(),
    ]);
  }

  // Build genre sections
  List<Widget> _buildGenreSections() {
    final List<Widget> sections = [];

    // Popular genres to display
    final List<String> popularGenres = [
      'Action',
      'Drama',
      'Comedy',
      'Thriller',
      'Romance'
    ];

    for (final genreName in popularGenres) {
      // Find category by name
      final category = _categoryController.categories.firstWhereOrNull(
          (c) => c.name.toLowerCase() == genreName.toLowerCase());

      if (category != null) {
        // Filter movies by category
        final genreMovies = _movieController.latestMovies
            .where((movie) =>
                movie.categoryName.toLowerCase() == genreName.toLowerCase())
            .toList();

        // Filter TV shows by category
        final genreTvShows = _tvShowController.latestTvShows
            .where((tvShow) =>
                tvShow.categoryName.toLowerCase() == genreName.toLowerCase())
            .toList();

        // Combine movies and TV shows
        final genreItems = [...genreMovies, ...genreTvShows];

        if (genreItems.isNotEmpty) {
          sections.add(
            GenreSection(
              genreName: genreName,
              items: genreItems,
              isLoading: false,
              onItemTap: (item) {
                if (item.runtimeType.toString().contains('Movie')) {
                  Get.to(() => MovieDetailsScreen(movieId: item.id));
                } else {
                  Get.to(() => TvShowDetailsScreen(tvShowId: item.id));
                }
              },
              onSeeAllTap: () {
                Get.to(() => CategoryScreen(
                      categoryId: category.id,
                      categoryName: category.name,
                      contentType: 'both',
                    ));
              },
            ),
          );

          sections.add(const SizedBox(height: 24));
        }
      }
    }

    return sections;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          AppConstants.appName,
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              Get.to(() => const SearchScreen());
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: ListView(
          children: [
            const SizedBox(height: 16),

            // Top Rated Slider
            Obx(() {
              final topRatedItems = [
                ..._movieController.topRatedMovies,
                ..._tvShowController.topRatedTvShows,
              ];

              // Sort by rating in descending order
              topRatedItems.sort((a, b) {
                double ratingA = 0.0;
                double ratingB = 0.0;

                if (a is Movie) {
                  ratingA = a.rating;
                } else if (a is TvShow) {
                  ratingA = a.rating;
                }

                if (b is Movie) {
                  ratingB = b.rating;
                } else if (b is TvShow) {
                  ratingB = b.rating;
                }

                return ratingB.compareTo(ratingA);
              });

              // Take top 10 items
              final topItems = topRatedItems.take(10).toList();

              return CarouselSliderWidget(
                items: topItems,
                onItemTap: (item) {
                  if (item.runtimeType.toString().contains('Movie')) {
                    Get.to(() => MovieDetailsScreen(movieId: item.id));
                  } else {
                    Get.to(() => TvShowDetailsScreen(tvShowId: item.id));
                  }
                },
                title: 'টপ রেটিং',
              );
            }),

            const SizedBox(height: 24),

            // Continue Watching section
            Obx(() => ContinueWatchingSection(
                  items: _watchHistoryController.watchHistory,
                  isLoading: _watchHistoryController.isLoading.value,
                  onItemTap: (item) async {
                    // Show loading indicator
                    Get.dialog(
                      const Center(
                        child: CircularProgressIndicator(),
                      ),
                      barrierDismissible: false,
                    );

                    try {
                      if (item.contentType == 'movie') {
                        // For movies, load the movie details to get download links
                        final movieController = Get.find<MovieController>();
                        await movieController.loadMovieDetails(item.contentId);

                        // Get the first download link
                        final downloadLinks = movieController.downloadLinks;
                        if (downloadLinks.isNotEmpty) {
                          // Close loading dialog
                          Get.back();

                          // Navigate to video player with the first download link
                          Get.to(() => VideoPlayerScreen(
                                title: item.title,
                                downloadLink: downloadLinks.first,
                                contentType: 'movie',
                                contentId: item.contentId,
                                backdropUrl: item.backdropUrl,
                              ));
                        } else {
                          // If no download links, navigate to movie details
                          Get.back();
                          Get.to(() =>
                              MovieDetailsScreen(movieId: item.contentId));
                        }
                      } else if (item.contentType == 'episode') {
                        // For episodes, load the TV show details
                        final tvShowController = Get.find<TvShowController>();

                        // Load TV show details - use parentId which contains the tvshow_id
                        await tvShowController.loadTvShowDetails(item.parentId);

                        // Load episode details to get download links
                        final episodeLinks = await tvShowController
                            .loadEpisodeLinks(item.contentId);

                        // Close loading dialog
                        Get.back();

                        if (episodeLinks.isNotEmpty) {
                          // Navigate to video player with the first download link
                          Get.to(() => VideoPlayerScreen(
                                title: item.title,
                                downloadLink: episodeLinks.first,
                                contentType: 'episode',
                                contentId: item.contentId,
                                backdropUrl: item.backdropUrl,
                                tvShowId: item.parentId,
                              ));
                        } else {
                          // If no download links, navigate to TV show details
                          Get.to(() =>
                              TvShowDetailsScreen(tvShowId: item.parentId));
                        }
                      }
                    } catch (e) {
                      // Close loading dialog
                      Get.back();

                      // Show error message
                      Get.snackbar(
                        'Error',
                        'Failed to load content details: ${e.toString()}',
                        snackPosition: SnackPosition.BOTTOM,
                        backgroundColor: Colors.red,
                        colorText: Colors.white,
                      );
                    }
                  },
                  onRemoveTap: (item) {
                    _watchHistoryController.removeFromHistory(item.id);
                  },
                )),

            const SizedBox(height: 24),

            // Latest movies
            Obx(() => ContentSection(
                  title: 'Latest Movies',
                  items: _movieController.latestMovies,
                  isLoading: _movieController.isLoadingLatest.value,
                  onItemTap: (movie) {
                    Get.to(() => MovieDetailsScreen(movieId: movie.id));
                  },
                  onSeeAllTap: () {
                    final context = Get.context!;
                    final homeState = HomeScreen.of(context);
                    if (homeState != null) {
                      homeState.changeTab(1); // Switch to Movies tab
                    }
                  },
                )),

            const SizedBox(height: 24),

            // Latest TV shows
            Obx(() => ContentSection(
                  title: 'Latest TV Shows',
                  items: _tvShowController.latestTvShows,
                  isLoading: _tvShowController.isLoadingLatest.value,
                  onItemTap: (tvShow) {
                    Get.to(() => TvShowDetailsScreen(tvShowId: tvShow.id));
                  },
                  onSeeAllTap: () {
                    final context = Get.context!;
                    final homeState = HomeScreen.of(context);
                    if (homeState != null) {
                      homeState.changeTab(2); // Switch to TV Shows tab
                    }
                  },
                )),

            const SizedBox(height: 24),

            // Popular movies
            Obx(() => ContentSection(
                  title: 'Popular Movies',
                  items: _movieController.popularMovies,
                  isLoading: _movieController.isLoadingPopular.value,
                  onItemTap: (movie) {
                    Get.to(() => MovieDetailsScreen(movieId: movie.id));
                  },
                )),

            const SizedBox(height: 24),

            // Genre sections
            ..._buildGenreSections(),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}

class _MoviesTab extends StatefulWidget {
  const _MoviesTab();

  @override
  State<_MoviesTab> createState() => _MoviesTabState();
}

class _MoviesTabState extends State<_MoviesTab> {
  final MovieController _movieController = Get.find<MovieController>();
  final CategoryController _categoryController = Get.find<CategoryController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Movies'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              Get.to(() => const SearchScreen());
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Categories
          SizedBox(
            height: 50,
            child: Obx(() {
              if (_categoryController.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }

              return TvFocusManager.createFocusableHorizontalList(
                height: 50,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: _categoryController.categories.length,
                onItemTap: (index) {
                  final category = _categoryController.categories[index];
                  Get.to(() => CategoryScreen(
                        categoryId: category.id,
                        categoryName: category.name,
                        contentType: 'movie',
                      ));
                },
                itemBuilder: (context, index) {
                  final category = _categoryController.categories[index];
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: ElevatedButton(
                      onPressed: () {
                        Get.to(() => CategoryScreen(
                              categoryId: category.id,
                              categoryName: category.name,
                              contentType: 'movie',
                            ));
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppConstants.surfaceColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                      child: Text(category.name),
                    ),
                  );
                },
              );
            }),
          ),

          const SizedBox(height: 16),

          // Movies list
          Expanded(
            child: Obx(() {
              if (_movieController.isLoadingLatest.value) {
                return const Center(child: CircularProgressIndicator());
              }

              if (_movieController.latestMovies.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.movie_outlined,
                        size: 64,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No movies available',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                );
              }

              return TvFocusManager.createFocusableGrid(
                padding: const EdgeInsets.all(16),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  childAspectRatio: 0.7,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 16,
                ),
                itemCount: _movieController.latestMovies.length,
                onItemTap: (index) {
                  final movie = _movieController.latestMovies[index];
                  Get.to(() => MovieDetailsScreen(movieId: movie.id));
                },
                itemBuilder: (context, index) {
                  final movie = _movieController.latestMovies[index];
                  return MovieCard(
                    movie: movie,
                    onTap: () {
                      Get.to(() => MovieDetailsScreen(movieId: movie.id));
                    },
                  );
                },
              );
            }),
          ),
        ],
      ),
    );
  }
}

class _TvShowsTab extends StatefulWidget {
  const _TvShowsTab();

  @override
  State<_TvShowsTab> createState() => _TvShowsTabState();
}

class _TvShowsTabState extends State<_TvShowsTab> {
  final TvShowController _tvShowController = Get.find<TvShowController>();
  final CategoryController _categoryController = Get.find<CategoryController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('TV Shows'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              Get.to(() => const SearchScreen());
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Categories
          SizedBox(
            height: 50,
            child: Obx(() {
              if (_categoryController.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }

              return TvFocusManager.createFocusableHorizontalList(
                height: 50,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: _categoryController.categories.length,
                onItemTap: (index) {
                  final category = _categoryController.categories[index];
                  Get.to(() => CategoryScreen(
                        categoryId: category.id,
                        categoryName: category.name,
                        contentType: 'tvshow',
                      ));
                },
                itemBuilder: (context, index) {
                  final category = _categoryController.categories[index];
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: ElevatedButton(
                      onPressed: () {
                        Get.to(() => CategoryScreen(
                              categoryId: category.id,
                              categoryName: category.name,
                              contentType: 'tvshow',
                            ));
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppConstants.surfaceColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                      child: Text(category.name),
                    ),
                  );
                },
              );
            }),
          ),

          const SizedBox(height: 16),

          // TV shows list
          Expanded(
            child: Obx(() {
              if (_tvShowController.isLoadingLatest.value) {
                return const Center(child: CircularProgressIndicator());
              }

              if (_tvShowController.latestTvShows.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.tv_outlined,
                        size: 64,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No TV shows available',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                );
              }

              return TvFocusManager.createFocusableGrid(
                padding: const EdgeInsets.all(16),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  childAspectRatio: 0.7,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 16,
                ),
                itemCount: _tvShowController.latestTvShows.length,
                onItemTap: (index) {
                  final tvShow = _tvShowController.latestTvShows[index];
                  Get.to(() => TvShowDetailsScreen(tvShowId: tvShow.id));
                },
                itemBuilder: (context, index) {
                  final tvShow = _tvShowController.latestTvShows[index];
                  return TvShowCard(
                    tvShow: tvShow,
                    onTap: () {
                      Get.to(() => TvShowDetailsScreen(tvShowId: tvShow.id));
                    },
                  );
                },
              );
            }),
          ),
        ],
      ),
    );
  }
}
