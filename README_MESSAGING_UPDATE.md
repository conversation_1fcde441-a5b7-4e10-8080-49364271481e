# মেসেজিং সিস্টেম আপডেট নির্দেশনা

## ডাটাবেস আপডেট

নিম্নলিখিত SQL কোড phpMyAdmin বা MySQL কমান্ড লাইন ব্যবহার করে চালান:

```sql
-- Add reply_to column to messages table
ALTER TABLE messages ADD COLUMN reply_to INT DEFAULT NULL;
ALTER TABLE messages ADD FOREIGN KEY (reply_to) REFERENCES messages(id) ON DELETE CASCADE;
```

## আপডেট সম্পর্কে

এই আপডেটে নিম্নলিখিত পরিবর্তনগুলি করা হয়েছে:

1. ফ্লোটিং মেসেজ আইকন ঠিক করা হয়েছে যাতে এটি সবসময় দেখা যায়
2. মেসেজ পেজে রিপ্লাই অপশন যুক্ত করা হয়েছে
3. মেসেজ পেজে নতুন মেসেজ পাঠানোর বাটন যুক্ত করা হয়েছে
4. ইউজাররা এখন একে অপরের মেসেজে রিপ্লাই করতে পারবে

## ব্যবহার নির্দেশনা

### ইউজারদের জন্য:
1. মেসেজ পেজে "নতুন মেসেজ পাঠান" বাটনে ক্লিক করে নতুন মেসেজ পাঠাতে পারবেন
2. মেসেজের নিচে "উত্তর দিন" বাটনে ক্লিক করে সেই মেসেজের উত্তর দিতে পারবেন
3. হোম পেজের নিচে ডান দিকে ফ্লোটিং মেসেজ আইকন দেখতে পাবেন

### এডমিনদের জন্য:
1. এডমিন প্যানেলে "Messages" মেনুতে ক্লিক করুন
2. সমস্ত মেসেজ দেখুন এবং উত্তর দিন
3. অপঠিত মেসেজের জন্য একটি লাল ব্যাজ দেখাবে
