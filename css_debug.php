<?php
// CSS ডিবাগিং স্ক্রিপ্ট
// এই ফাইলটি লাইভ সার্ভারে CSS ফাইল লোড হচ্ছে কিনা তা চেক করার জন্য

// সার্ভার এনভায়রনমেন্ট ভেরিয়েবলস দেখুন
echo "<h1>CSS ডিবাগিং ইনফরমেশন</h1>";

echo "<h2>সার্ভার ইনফরমেশন</h2>";
echo "<pre>";
echo "SERVER_NAME: " . $_SERVER['SERVER_NAME'] . "\n";
echo "HTTP_HOST: " . $_SERVER['HTTP_HOST'] . "\n";
echo "REQUEST_URI: " . $_SERVER['REQUEST_URI'] . "\n";
echo "SCRIPT_NAME: " . $_SERVER['SCRIPT_NAME'] . "\n";
echo "DOCUMENT_ROOT: " . $_SERVER['DOCUMENT_ROOT'] . "\n";
echo "PHP_SELF: " . $_SERVER['PHP_SELF'] . "\n";
echo "HTTPS: " . (isset($_SERVER['HTTPS']) ? $_SERVER['HTTPS'] : 'off') . "\n";
echo "</pre>";

// SITE_URL কনফিগারেশন চেক করুন
require_once 'includes/config.php';

echo "<h2>SITE_URL কনফিগারেশন</h2>";
echo "<pre>";
echo "SITE_URL: " . SITE_URL . "\n";
echo "SITE_NAME: " . SITE_NAME . "\n";
echo "</pre>";

// CSS ফাইল চেক করুন
echo "<h2>CSS ফাইল চেক</h2>";
$css_files = [
    'css/style.css',
    'css/profile.css',
    'css/payment_history.css',
    'css/movie-details.css',
    'css/tvshow-details.css',
    'css/movies.css'
];

echo "<table border='1' cellpadding='5'>";
echo "<tr><th>ফাইল</th><th>অবস্থান</th><th>সাইজ</th><th>মডিফাইড</th><th>পারমিশন</th></tr>";

foreach ($css_files as $file) {
    $full_path = __DIR__ . '/' . $file;
    $exists = file_exists($full_path);
    $size = $exists ? filesize($full_path) : 'N/A';
    $modified = $exists ? date("Y-m-d H:i:s", filemtime($full_path)) : 'N/A';
    $perms = $exists ? substr(sprintf('%o', fileperms($full_path)), -4) : 'N/A';
    
    echo "<tr>";
    echo "<td>$file</td>";
    echo "<td>" . ($exists ? 'আছে' : 'নেই') . "</td>";
    echo "<td>$size</td>";
    echo "<td>$modified</td>";
    echo "<td>$perms</td>";
    echo "</tr>";
}
echo "</table>";

// CSS URL টেস্ট করুন
echo "<h2>CSS URL টেস্ট</h2>";
echo "<p>নিচের লিংকগুলো ক্লিক করে দেখুন CSS ফাইল সঠিকভাবে লোড হচ্ছে কিনা:</p>";
echo "<ul>";
foreach ($css_files as $file) {
    $url = SITE_URL . '/' . $file;
    echo "<li><a href='$url' target='_blank'>$url</a></li>";
}
echo "</ul>";

// .htaccess ফাইল চেক করুন
echo "<h2>.htaccess ফাইল চেক</h2>";
$htaccess_path = __DIR__ . '/.htaccess';
if (file_exists($htaccess_path)) {
    echo "<p>.htaccess ফাইল আছে</p>";
    echo "<pre>";
    echo htmlspecialchars(file_get_contents($htaccess_path));
    echo "</pre>";
} else {
    echo "<p>.htaccess ফাইল নেই</p>";
}

// CSS লোডিং টেস্ট
echo "<h2>CSS লোডিং টেস্ট</h2>";
?>

<div id="css-test">
    <div class="test-box">এই বক্সটি লাল হওয়া উচিত</div>
</div>

<style>
    #css-test .test-box {
        width: 200px;
        height: 100px;
        background-color: red;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 20px 0;
    }
</style>

<script>
    // CSS লোড হয়েছে কিনা চেক করুন
    window.onload = function() {
        var styleSheets = document.styleSheets;
        var loadedStyles = [];
        
        for (var i = 0; i < styleSheets.length; i++) {
            try {
                var href = styleSheets[i].href;
                if (href) {
                    loadedStyles.push(href);
                }
            } catch (e) {
                console.error("Error checking stylesheet:", e);
            }
        }
        
        var resultDiv = document.createElement('div');
        resultDiv.innerHTML = "<h3>লোড হওয়া CSS ফাইল:</h3><ul>";
        
        for (var j = 0; j < loadedStyles.length; j++) {
            resultDiv.innerHTML += "<li>" + loadedStyles[j] + "</li>";
        }
        
        resultDiv.innerHTML += "</ul>";
        document.body.appendChild(resultDiv);
    };
</script>

<?php
// ডিবাগিং সাজেশন
echo "<h2>সমাধানের পরামর্শ</h2>";
echo "<ol>";
echo "<li>ব্রাউজার ক্যাশে ক্লিয়ার করুন (Ctrl+F5 চাপুন)</li>";
echo "<li>CSS ফাইলগুলোর পারমিশন 644 সেট করুন: <code>chmod 644 css/*.css</code></li>";
echo "<li>CSS ফোল্ডারের পারমিশন 755 সেট করুন: <code>chmod 755 css</code></li>";
echo "<li>সার্ভারে CSS ফাইলগুলো আপলোড করা হয়েছে কিনা নিশ্চিত করুন</li>";
echo "<li>SITE_URL সঠিকভাবে সেট করা আছে কিনা চেক করুন</li>";
echo "<li>.htaccess ফাইল সঠিকভাবে কনফিগার করা আছে কিনা চেক করুন</li>";
echo "</ol>";
?>
