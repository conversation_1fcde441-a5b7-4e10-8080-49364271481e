import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:cinepix_app/constants/app_constants.dart';
import 'package:cinepix_app/controllers/tv_show_controller.dart';
import 'package:cinepix_app/controllers/auth_controller.dart';
import 'package:cinepix_app/controllers/review_controller.dart';
import 'package:cinepix_app/controllers/watch_history_controller.dart';
import 'package:cinepix_app/models/tv_show.dart';
import 'package:cinepix_app/models/episode.dart';
import 'package:cinepix_app/models/download_link.dart';
import 'package:cinepix_app/views/video_player_screen.dart';
import 'package:cinepix_app/widgets/download_link_card.dart';
import 'package:cinepix_app/widgets/review_list.dart';

class TvShowDetailsScreen extends StatefulWidget {
  final int tvShowId;

  const TvShowDetailsScreen({
    super.key,
    required this.tvShowId,
  });

  @override
  State<TvShowDetailsScreen> createState() => _TvShowDetailsScreenState();
}

class _TvShowDetailsScreenState extends State<TvShowDetailsScreen>
    with SingleTickerProviderStateMixin {
  final TvShowController _tvShowController = Get.find<TvShowController>();
  final AuthController _authController = Get.find<AuthController>();
  final ReviewController _reviewController = Get.put(ReviewController());

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _loadTvShowDetails();
    _tabController = TabController(length: 2, vsync: this);
  }

  Future<void> _loadTvShowDetails() async {
    await _tvShowController.loadTvShowDetails(widget.tvShowId);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _tvShowController.clearCurrentTvShow();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        if (_tvShowController.isLoadingDetails.value) {
          return const Center(child: CircularProgressIndicator());
        }

        final tvShow = _tvShowController.currentTvShow.value;

        if (tvShow == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.grey[600],
                ),
                const SizedBox(height: 16),
                Text(
                  'TV show not found',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () => Get.back(),
                  child: const Text('Go Back'),
                ),
              ],
            ),
          );
        }

        return NestedScrollView(
          headerSliverBuilder: (context, innerBoxIsScrolled) {
            return [
              // App bar with TV show banner
              SliverAppBar(
                expandedHeight: 250,
                pinned: true,
                flexibleSpace: FlexibleSpaceBar(
                  background: Stack(
                    fit: StackFit.expand,
                    children: [
                      // TV show banner
                      CachedNetworkImage(
                        imageUrl: tvShow.banner,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: Colors.grey[850],
                          child: const Center(
                            child: CircularProgressIndicator(),
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: Colors.grey[850],
                          child: const Icon(Icons.error),
                        ),
                      ),

                      // Gradient overlay
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              Colors.black
                                  .withValues(alpha: 179), // 0.7 opacity
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                leading: Container(
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 128), // 0.5 opacity
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed: () => Get.back(),
                  ),
                ),
              ),

              // TV show details
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title and rating
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // TV show poster
                          ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: CachedNetworkImage(
                              imageUrl: tvShow.poster,
                              width: 120,
                              height: 180,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                width: 120,
                                height: 180,
                                color: Colors.grey[850],
                                child: const Center(
                                  child: CircularProgressIndicator(),
                                ),
                              ),
                              errorWidget: (context, url, error) => Container(
                                width: 120,
                                height: 180,
                                color: Colors.grey[850],
                                child: const Icon(Icons.error),
                              ),
                            ),
                          ),

                          const SizedBox(width: 16),

                          // Title, year, seasons, category
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  tvShow.title,
                                  style: const TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Row(
                                  children: [
                                    Text(
                                      tvShow.startYear.toString(),
                                      style: TextStyle(
                                        color: Colors.grey[400],
                                      ),
                                    ),
                                    if (tvShow.endYear != null) ...[
                                      Text(
                                        ' - ${tvShow.endYear}',
                                        style: TextStyle(
                                          color: Colors.grey[400],
                                        ),
                                      ),
                                    ],
                                    const SizedBox(width: 12),
                                    Text(
                                      '${tvShow.seasons} ${tvShow.seasons > 1 ? 'Seasons' : 'Season'}',
                                      style: TextStyle(
                                        color: Colors.grey[400],
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: AppConstants.surfaceColor,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Text(
                                    tvShow.categoryName,
                                    style: TextStyle(
                                      color: AppConstants.primaryColor,
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 16),
                                Row(
                                  children: [
                                    RatingBarIndicator(
                                      rating: tvShow.rating /
                                          2, // Convert 10-scale to 5-scale
                                      itemBuilder: (context, index) =>
                                          const Icon(
                                        Icons.star,
                                        color: Colors.amber,
                                      ),
                                      itemCount: 5,
                                      itemSize: 20.0,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      tvShow.rating.toStringAsFixed(1),
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),

                                // Premium badge
                                if (tvShow.premiumOnly)
                                  Container(
                                    margin: const EdgeInsets.only(top: 16),
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 6),
                                    decoration: BoxDecoration(
                                      color: AppConstants.primaryColor,
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: const Text(
                                      'PREMIUM',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 24),

                      // Description
                      const Text(
                        'Description',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        tvShow.description,
                        style: TextStyle(
                          color: Colors.grey[300],
                          height: 1.5,
                        ),
                      ),

                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ),

              // Tab bar
              SliverPersistentHeader(
                delegate: _SliverAppBarDelegate(
                  TabBar(
                    controller: _tabController,
                    tabs: const [
                      Tab(text: 'Episodes'),
                      Tab(text: 'Reviews'),
                    ],
                    indicatorColor: AppConstants.primaryColor,
                    labelColor: AppConstants.primaryColor,
                    unselectedLabelColor: Colors.grey,
                  ),
                ),
                pinned: true,
              ),
            ];
          },
          body: TabBarView(
            controller: _tabController,
            children: [
              // Episodes tab
              _buildEpisodesTab(tvShow),

              // Related TV shows tab
              _buildRelatedTvShowsTab(),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildEpisodesTab(TvShow tvShow) {
    return Obx(() {
      final seasons = _tvShowController.seasons;

      if (seasons.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.video_library_outlined,
                size: 64,
                color: Colors.grey[600],
              ),
              const SizedBox(height: 16),
              Text(
                'No seasons available',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        );
      }

      return Column(
        children: [
          // Season selector
          Container(
            height: 50,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: seasons.length,
              itemBuilder: (context, index) {
                final season = seasons[index];
                return Obx(() {
                  final isSelected = _tvShowController.currentSeason.value ==
                      season['season_number'];

                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: ElevatedButton(
                      onPressed: () async {
                        await _tvShowController.loadEpisodes(
                            tvShow.id, season['season_number']);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: isSelected
                            ? AppConstants.primaryColor
                            : AppConstants.surfaceColor,
                        foregroundColor:
                            isSelected ? Colors.white : Colors.grey,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                      child: Text('Season ${season['season_number']}'),
                    ),
                  );
                });
              },
            ),
          ),

          // Episodes list
          Expanded(
            child: Obx(() {
              if (_tvShowController.isLoadingEpisodes.value) {
                return const Center(child: CircularProgressIndicator());
              }

              final episodes = _tvShowController.episodes;

              if (episodes.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.video_library_outlined,
                        size: 64,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No episodes available',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                );
              }

              return ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: episodes.length,
                itemBuilder: (context, index) {
                  final episode = episodes[index];

                  return _buildEpisodeCard(episode, tvShow);
                },
              );
            }),
          ),
        ],
      );
    });
  }

  Widget _buildEpisodeCard(Episode episode, TvShow tvShow) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.cardBorderRadius),
      ),
      child: InkWell(
        onTap: () => _loadEpisodeDetails(episode, tvShow),
        borderRadius: BorderRadius.circular(AppConstants.cardBorderRadius),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              // Episode number in circle
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: const Color(0xFFFFEBEE),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    '${episode.episodeNumber}',
                    style: TextStyle(
                      color: AppConstants.primaryColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),

              // Episode info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            episode.title,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (episode.isPremium)
                          Container(
                            margin: const EdgeInsets.only(left: 8),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: AppConstants.primaryColor,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: const Text(
                              'PREMIUM',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${episode.duration} min • ${episode.releaseDate}',
                      style: TextStyle(
                        color: Colors.grey[400],
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      episode.description,
                      style: TextStyle(
                        color: Colors.grey[300],
                        fontSize: 13,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // Play icon
              Icon(
                Icons.play_circle_outline,
                color: AppConstants.primaryColor,
                size: 28,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRelatedTvShowsTab() {
    return Obx(() {
      final tvShow = _tvShowController.currentTvShow.value;

      if (tvShow == null) {
        return const Center(child: CircularProgressIndicator());
      }

      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: ReviewList(
          contentType: 'tvshow',
          contentId: tvShow.id,
        ),
      );
    });
  }

  Future<void> _loadEpisodeDetails(Episode episode, TvShow tvShow) async {
    if (episode.isPremium && !_authController.isPremium.value) {
      _showPremiumDialog();
      return;
    }

    await _tvShowController.loadEpisodeDetails(episode);

    // Check if widget is still mounted before proceeding
    if (!mounted) return;

    final downloadLinks = _tvShowController.downloadLinks;

    if (downloadLinks.isEmpty) {
      // Show a more visible error message
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('No Links Available'),
          content: const Text(
              'No download or streaming links available for this episode.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        ),
      );
      return;
    }

    // Show bottom sheet with download links
    showModalBottomSheet(
      context: context,
      backgroundColor: AppConstants.surfaceColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Episode ${episode.episodeNumber}: ${episode.title}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Select Quality:',
                style: TextStyle(
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: downloadLinks.length,
                  itemBuilder: (context, index) {
                    final link = downloadLinks[index];

                    return DownloadLinkCard(
                      link: link,
                      title:
                          '${tvShow.title} S${episode.seasonNumber}E${episode.episodeNumber}',
                      onPlayTap: () {
                        Navigator.pop(context);
                        _playVideo(episode, link, tvShow);
                      },
                      isPremiumUser: _authController.isPremium.value,
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _playVideo(Episode episode, DownloadLink link, TvShow tvShow) {
    if (episode.isPremium && !_authController.isPremium.value) {
      _showPremiumDialog();
      return;
    }

    // Update watch history when playing video
    final watchHistoryController = Get.find<WatchHistoryController>();
    watchHistoryController.updateWatchHistory(
      contentType: 'episode',
      contentId: episode.id,
      parentId: tvShow.id, // Add tvshow_id as parentId for episodes
      title:
          '${tvShow.title} S${episode.seasonNumber}E${episode.episodeNumber} - ${episode.title}',
      posterUrl: tvShow.poster,
      backdropUrl: tvShow.banner,
      duration: episode.duration * 60, // Convert minutes to seconds
      watchedPosition: 0, // Will be updated by the video player
    );

    Get.to(() => VideoPlayerScreen(
          title:
              '${tvShow.title} S${episode.seasonNumber}E${episode.episodeNumber} - ${episode.title}',
          downloadLink: link,
          contentType: 'episode',
          contentId: episode.id,
          backdropUrl: tvShow.banner,
          tvShowId: tvShow.id, // Pass tvShowId for next episode functionality
        ));
  }

  void _showPremiumDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppConstants.surfaceColor,
        title: Row(
          children: [
            Icon(
              Icons.workspace_premium,
              color: AppConstants.primaryColor,
            ),
            const SizedBox(width: 10),
            const Text('প্রিমিয়াম কন্টেন্ট'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'এই কন্টেন্টটি শুধুমাত্র প্রিমিয়াম ব্যবহারকারীদের জন্য উপলব্ধ।',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 15),
            const Text(
              'প্রিমিয়াম প্যাকেজ:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            _buildPremiumPackageCard('বেসিক', '৳৩০/মাস',
                ['সকল প্রিমিয়াম মুভি', 'সকল প্রিমিয়াম টিভি শো']),
            const SizedBox(height: 8),
            _buildPremiumPackageCard('স্ট্যান্ডার্ড', '৳৬০/মাস', [
              'সকল প্রিমিয়াম মুভি',
              'সকল প্রিমিয়াম টিভি শো',
              'HD কোয়ালিটি'
            ]),
            const SizedBox(height: 8),
            _buildPremiumPackageCard('প্রিমিয়াম', '৳৯০/মাস', [
              'সকল প্রিমিয়াম মুভি',
              'সকল প্রিমিয়াম টিভি শো',
              'HD এবং Ultra HD কোয়ালিটি'
            ]),
            const SizedBox(height: 15),
            const Text(
              'প্রিমিয়াম প্যাকেজ কিনতে সাইট থেকে লগইন করুন।',
              style: TextStyle(fontStyle: FontStyle.italic),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('বাতিল'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Open website for premium purchase
              launchUrl(Uri.parse('https://cinepix.top/premium.php'),
                  mode: LaunchMode.externalApplication);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.workspace_premium, size: 18, color: Colors.white),
                SizedBox(width: 8),
                Text(
                  'প্রিমিয়াম কিনুন',
                  style: TextStyle(
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPremiumPackageCard(
      String title, String price, List<String> features) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.black26,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
            color: AppConstants.primaryColor
                .withValues(alpha: 128)), // 0.5 opacity
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(
                price,
                style: TextStyle(
                    color: AppConstants.primaryColor,
                    fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 5),
          ...features.map((feature) => Padding(
                padding: const EdgeInsets.only(bottom: 3),
                child: Row(
                  children: [
                    Icon(Icons.check_circle,
                        size: 14, color: AppConstants.primaryColor),
                    const SizedBox(width: 5),
                    Text(feature, style: const TextStyle(fontSize: 12)),
                  ],
                ),
              )),
        ],
      ),
    );
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar _tabBar;

  _SliverAppBarDelegate(this._tabBar);

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: AppConstants.backgroundColor,
      child: _tabBar,
    );
  }

  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  double get minExtent => _tabBar.preferredSize.height;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}
