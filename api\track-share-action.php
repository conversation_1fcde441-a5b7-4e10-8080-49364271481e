<?php
require_once '../includes/config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

function sendResponse($success, $message, $data = null) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Only POST method allowed');
}

$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['token']) || !isset($input['action'])) {
    sendResponse(false, 'Token and action are required');
}

$token = mysqli_real_escape_string($conn, $input['token']);
$action = mysqli_real_escape_string($conn, $input['action']);
$link_url = isset($input['link_url']) ? mysqli_real_escape_string($conn, $input['link_url']) : '';

// Get shared link ID
$query = "SELECT id FROM shared_links WHERE link_token = ? AND is_active = 1";
$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, 's', $token);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    sendResponse(false, 'Invalid share link');
}

$link = mysqli_fetch_assoc($result);
$shared_link_id = $link['id'];

// Get user info
$ip_address = $_SERVER['REMOTE_ADDR'];
$user_agent = $_SERVER['HTTP_USER_AGENT'];
$user_id = null;

// Check if user is logged in (if session is available)
session_start();
if (isset($_SESSION['user_id'])) {
    $user_id = $_SESSION['user_id'];
}

// Simple device detection
$device_type = 'Desktop';
if (preg_match('/Mobile|Android|iPhone|iPad/', $user_agent)) {
    $device_type = 'Mobile';
}

// Simple browser detection
$browser = 'Unknown';
if (strpos($user_agent, 'Chrome') !== false) $browser = 'Chrome';
elseif (strpos($user_agent, 'Firefox') !== false) $browser = 'Firefox';
elseif (strpos($user_agent, 'Safari') !== false) $browser = 'Safari';
elseif (strpos($user_agent, 'Edge') !== false) $browser = 'Edge';

// Create action tracking table if not exists
$create_table_query = "CREATE TABLE IF NOT EXISTS shared_link_actions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    shared_link_id INT NOT NULL,
    action_type ENUM('download', 'stream', 'view') NOT NULL,
    link_url TEXT,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    user_id INT NULL,
    device_type VARCHAR(50),
    browser VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (shared_link_id) REFERENCES shared_links(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_shared_link_action (shared_link_id, action_type),
    INDEX idx_created_at (created_at)
)";

mysqli_query($conn, $create_table_query);

// Insert action log
$insert_query = "INSERT INTO shared_link_actions (shared_link_id, action_type, link_url, ip_address, user_agent, user_id, device_type, browser) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
$insert_stmt = mysqli_prepare($conn, $insert_query);
mysqli_stmt_bind_param($insert_stmt, 'isssssss', $shared_link_id, $action, $link_url, $ip_address, $user_agent, $user_id, $device_type, $browser);

if (mysqli_stmt_execute($insert_stmt)) {
    sendResponse(true, 'Action tracked successfully', [
        'action' => $action,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
} else {
    sendResponse(false, 'Failed to track action');
}
?>
