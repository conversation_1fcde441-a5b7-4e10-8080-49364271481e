/**
 * P<PERSON> App JavaScript for CinePix
 * Makes the website behave like a native mobile app
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check if running as PWA
    const isPWA = window.matchMedia('(display-mode: fullscreen)').matches || 
                  window.matchMedia('(display-mode: standalone)').matches ||
                  window.navigator.standalone;
    
    // Add PWA mode class to body if running as PWA
    if (isPWA) {
        document.body.classList.add('pwa-mode');
        
        // Create mobile bottom navigation
        createMobileNavigation();
        
        // Hide address bar on iOS
        window.scrollTo(0, 1);
    }
    
    // Add install prompt for PWA
    let deferredPrompt;
    const installButton = document.getElementById('pwa-install-button');
    
    window.addEventListener('beforeinstallprompt', (e) => {
        // Prevent Chrome 67 and earlier from automatically showing the prompt
        e.preventDefault();
        // Stash the event so it can be triggered later
        deferredPrompt = e;
        
        // Show the install button if it exists
        if (installButton) {
            installButton.style.display = 'block';
            
            installButton.addEventListener('click', () => {
                // Hide the install button
                installButton.style.display = 'none';
                // Show the install prompt
                deferredPrompt.prompt();
                // Wait for the user to respond to the prompt
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('User accepted the install prompt');
                    } else {
                        console.log('User dismissed the install prompt');
                    }
                    // Clear the deferredPrompt variable
                    deferredPrompt = null;
                });
            });
        }
    });
    
    // Function to create mobile bottom navigation
    function createMobileNavigation() {
        // Only create on mobile devices
        if (window.innerWidth > 767) return;
        
        // Create the navigation element
        const mobileNav = document.createElement('div');
        mobileNav.className = 'mobile-nav-bottom';
        
        // Add navigation items
        mobileNav.innerHTML = `
            <div class="nav-item">
                <a href="${siteUrl}" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>Home</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="${siteUrl}/movies.php" class="nav-link">
                    <i class="fas fa-film"></i>
                    <span>Movies</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="${siteUrl}/tvshows.php" class="nav-link">
                    <i class="fas fa-tv"></i>
                    <span>TV Shows</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="${siteUrl}/search.php" class="nav-link">
                    <i class="fas fa-search"></i>
                    <span>Search</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="${siteUrl}/profile.php" class="nav-link">
                    <i class="fas fa-user"></i>
                    <span>Profile</span>
                </a>
            </div>
        `;
        
        // Add to the document
        document.body.appendChild(mobileNav);
        
        // Highlight active nav item
        const currentPage = window.location.pathname;
        const navLinks = mobileNav.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            if (currentPage.includes(link.getAttribute('href'))) {
                link.classList.add('active');
            }
        });
    }
    
    // Get site URL from meta tag
    const siteUrlMeta = document.querySelector('meta[name="site-url"]');
    const siteUrl = siteUrlMeta ? siteUrlMeta.getAttribute('content') : '';
    
    // Add app-like search bar on search page
    if (window.location.pathname.includes('/search.php') && isPWA) {
        const searchForm = document.querySelector('form[action*="search.php"]');
        if (searchForm) {
            searchForm.classList.add('app-search-bar');
            const searchInput = searchForm.querySelector('input[type="search"]');
            if (searchInput) {
                searchInput.placeholder = 'Search movies, TV shows...';
            }
        }
    }
    
    // Add pull-to-refresh functionality
    let touchStartY = 0;
    let touchEndY = 0;
    
    document.addEventListener('touchstart', function(e) {
        touchStartY = e.touches[0].clientY;
    }, false);
    
    document.addEventListener('touchend', function(e) {
        touchEndY = e.changedTouches[0].clientY;
        handleSwipe();
    }, false);
    
    function handleSwipe() {
        if (touchEndY - touchStartY > 100 && window.scrollY === 0) {
            // User pulled down at the top of the page
            if (isPWA) {
                // Show loading indicator
                showLoading();
                // Reload the page
                setTimeout(() => {
                    window.location.reload();
                }, 500);
            }
        }
    }
    
    function showLoading() {
        const loadingEl = document.createElement('div');
        loadingEl.className = 'app-loading';
        loadingEl.innerHTML = '<div class="spinner"></div>';
        
        document.body.prepend(loadingEl);
        
        setTimeout(() => {
            loadingEl.remove();
        }, 2000);
    }
});
