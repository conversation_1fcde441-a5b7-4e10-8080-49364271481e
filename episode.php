<?php
require_once 'includes/header.php';

// Check if episode ID is provided
if (!isset($_GET['id'])) {
    redirect(SITE_URL);
}

$episode_id = (int)$_GET['id'];

// Get episode details
$episode_query = "SELECT e.*, t.title as tvshow_title, t.id as tvshow_id
                 FROM episodes e
                 JOIN tvshows t ON e.tvshow_id = t.id
                 WHERE e.id = $episode_id";
$episode_result = mysqli_query($conn, $episode_query);

if (mysqli_num_rows($episode_result) == 0) {
    redirect(SITE_URL);
}

$episode = mysqli_fetch_assoc($episode_result);

// Check if premium content and user is not premium
if ($episode['is_premium'] && !$is_premium) {
    redirect(SITE_URL . '/premium.php');
}

// Get streaming links for this episode
$stream_query = "SELECT * FROM episode_links
                WHERE episode_id = $episode_id
                AND link_type = 'stream'
                ORDER BY quality DESC, server_name ASC";
$stream_result = mysqli_query($conn, $stream_query);

// Get download links for this episode
$download_query = "SELECT * FROM episode_links
                  WHERE episode_id = $episode_id
                  AND link_type = 'download'
                  ORDER BY quality DESC";
$download_result = mysqli_query($conn, $download_query);

// Get other episodes from the same season
$other_episodes_query = "SELECT * FROM episodes
                        WHERE tvshow_id = {$episode['tvshow_id']}
                        AND season_number = {$episode['season_number']}
                        ORDER BY episode_number ASC";
$other_episodes_result = mysqli_query($conn, $other_episodes_query);

// Get selected stream (default to first available)
$selected_stream_id = isset($_GET['stream']) ? (int)$_GET['stream'] : 0;
$selected_stream = null;

if ($selected_stream_id > 0) {
    // Get specific stream
    $selected_stream_query = "SELECT * FROM episode_links
                             WHERE id = $selected_stream_id
                             AND episode_id = $episode_id
                             AND link_type = 'stream'";
    $selected_stream_result = mysqli_query($conn, $selected_stream_query);

    if (mysqli_num_rows($selected_stream_result) > 0) {
        $selected_stream = mysqli_fetch_assoc($selected_stream_result);
    }
} elseif (mysqli_num_rows($stream_result) > 0) {
    // Use first available stream
    mysqli_data_seek($stream_result, 0);
    $selected_stream = mysqli_fetch_assoc($stream_result);
    mysqli_data_seek($stream_result, 0); // Reset pointer for later use
}
?>

<!-- Episode Header -->
<section class="py-4 bg-dark">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">
                    <?php echo $episode['tvshow_title']; ?> -
                    S<?php echo $episode['season_number']; ?>E<?php echo str_pad($episode['episode_number'], 2, '0', STR_PAD_LEFT); ?>:
                    <?php echo $episode['title']; ?>
                    <?php if($episode['is_premium']): ?>
                    <span class="badge bg-danger">PREMIUM</span>
                    <?php endif; ?>
                </h1>
                <p class="text-muted mb-0">
                    <i class="fas fa-clock me-1"></i> <?php echo $episode['duration']; ?> min
                    <?php if($episode['release_date']): ?>
                    <span class="ms-3"><i class="fas fa-calendar-alt me-1"></i> <?php echo date('F j, Y', strtotime($episode['release_date'])); ?></span>
                    <?php endif; ?>
                </p>
            </div>
            <div>
                <a href="<?php echo SITE_URL; ?>/episode_player.php?id=<?php echo $episode_id; ?>" class="btn btn-danger me-2">
                    <i class="fas fa-play me-1"></i> Watch Episode
                </a>
                <a href="<?php echo SITE_URL; ?>/details.php?type=tvshow&id=<?php echo $episode['tvshow_id']; ?>" class="btn btn-outline-light">
                    <i class="fas fa-arrow-left me-1"></i> Back to Series
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Video Player Section -->
<section class="py-4 bg-black">
    <div class="container">
        <div class="row">
            <div class="col-lg-9">
                <?php if($selected_stream): ?>
                <div class="card bg-dark border-0">
                    <div class="card-body p-0">
                        <div class="ratio ratio-16x9">
                            <?php
                            // Check if the URL is from Cloudflare Workers
                            $is_worker_url = (stripos($selected_stream['link_url'], 'workers.dev') !== false);

                            if ($is_worker_url) {
                                // Use enhanced player for Cloudflare Worker links
                                require_once 'includes/streaming_helper.php';

                                // Get poster URL
                                $poster_url = SITE_URL . '/uploads/' . ($episode["thumbnail"] ? $episode["thumbnail"] : '');

                                $streaming_url = getEpisodeStreamingUrl(
                                    $selected_stream['link_url'],
                                    $episode['title'],
                                    $poster_url,
                                    $episode['is_premium'],
                                    $episode['tvshow_title'],
                                    $episode['season_number'],
                                    $episode['episode_number'],
                                    '', // player_type
                                    $selected_stream['subtitle_url_bn'],
                                    $selected_stream['subtitle_url_en']
                                );

                                echo '<iframe src="' . $streaming_url . '" allowfullscreen frameborder="0"></iframe>';
                            } else {
                                // Use regular iframe for non-worker links
                                echo '<iframe src="' . $selected_stream['link_url'] . '" allowfullscreen></iframe>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="alert alert-info">
                    <p class="mb-0">No streaming links available for this episode.</p>
                </div>
                <?php endif; ?>

                <!-- Server Selection -->
                <?php if(mysqli_num_rows($stream_result) > 0): ?>
                <div class="card bg-dark mt-3">
                    <div class="card-header bg-dark">
                        <h5 class="mb-0">Available Servers</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex flex-wrap">
                            <?php while($stream = mysqli_fetch_assoc($stream_result)): ?>
                            <a href="<?php echo SITE_URL; ?>/episode.php?id=<?php echo $episode_id; ?>&stream=<?php echo $stream['id']; ?>" class="btn <?php echo ($selected_stream && $stream['id'] == $selected_stream['id']) ? 'btn-danger' : 'btn-outline-light'; ?> me-2 mb-2">
                                <?php echo $stream['server_name'] ?? 'Server'; ?> (<?php echo $stream['quality']; ?>)
                                <?php if($stream['is_premium']): ?>
                                <i class="fas fa-crown text-warning ms-1"></i>
                                <?php endif; ?>
                            </a>
                            <?php endwhile; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Episode Description -->
                <div class="card bg-dark mt-3">
                    <div class="card-header bg-dark">
                        <h5 class="mb-0">Episode Description</h5>
                    </div>
                    <div class="card-body">
                        <p><?php echo $episode['description']; ?></p>
                    </div>
                </div>

                <!-- Download Options -->
                <?php if(mysqli_num_rows($download_result) > 0): ?>
                <div class="card bg-dark mt-3">
                    <div class="card-header bg-dark">
                        <h5 class="mb-0">Download Options</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-dark table-hover">
                                <thead>
                                    <tr>
                                        <th>Quality</th>
                                        <th>Size</th>
                                        <th>Subtitles</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while($download = mysqli_fetch_assoc($download_result)): ?>
                                    <tr>
                                        <td><?php echo $download['quality']; ?></td>
                                        <td>
                                            <?php
                                            // Get file size from database or use default based on quality
                                            if(!empty($download['file_size'])) {
                                                echo $download['file_size'];
                                            } else {
                                                // Fallback to default sizes if not specified
                                                if($download['quality'] == '720p') echo '800 MB';
                                                elseif($download['quality'] == '1080p') echo '1.5 GB';
                                                elseif($download['quality'] == '4K') echo '4.2 GB';
                                                else echo '400 MB';
                                            }
                                            ?>
                                        </td>
                                        <td>
                                            <?php if(!empty($download['subtitle_url_bn'])): ?>
                                            <span class="badge bg-success">BN</span>
                                            <?php endif; ?>
                                            <?php if(!empty($download['subtitle_url_en'])): ?>
                                            <span class="badge bg-info">EN</span>
                                            <?php endif; ?>
                                            <?php if(empty($download['subtitle_url_bn']) && empty($download['subtitle_url_en'])): ?>
                                            <span class="badge bg-secondary">None</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($download['is_premium'] && !$is_premium): ?>
                                            <a href="<?php echo SITE_URL; ?>/premium.php" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-crown me-1"></i> Premium
                                            </a>
                                            <?php else: ?>
                                            <?php
                                            // Include streaming helper
                                            require_once 'includes/streaming_helper.php';

                                            // Generate HTML for download and play buttons
                                            echo displayDownloadAndPlayButtons(
                                                $download['link_url'],
                                                $episode['tvshow_title'] . ' - S' . $episode['season_number'] . 'E' . str_pad($episode['episode_number'], 2, '0', STR_PAD_LEFT) . ': ' . $episode['title'],
                                                SITE_URL . '/uploads/' . ($episode['thumbnail'] ? $episode['thumbnail'] : ''),
                                                $download['quality'],
                                                $download['is_premium'],
                                                $download['server_name'],
                                                0, // movie_id
                                                $episode['tvshow_id'], // series_id
                                                $episode['season_number'], // season
                                                $episode['episode_number'], // episode
                                                $download['id'], // link_id
                                                $download['subtitle_url_bn'], // subtitle_url_bn
                                                $download['subtitle_url_en'] // subtitle_url_en
                                            );
                                            ?>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-3 mt-4 mt-lg-0">
                <!-- Episode Navigation -->
                <div class="card bg-dark">
                    <div class="card-header bg-dark">
                        <h5 class="mb-0">Season <?php echo $episode['season_number']; ?> Episodes</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            <?php while($other_episode = mysqli_fetch_assoc($other_episodes_result)): ?>
                            <a href="<?php echo SITE_URL; ?>/episode.php?id=<?php echo $other_episode['id']; ?>" class="list-group-item list-group-item-action bg-dark text-white border-secondary <?php echo $other_episode['id'] == $episode_id ? 'active' : ''; ?>">
                                <div class="d-flex w-100 justify-content-between align-items-center">
                                    <div>
                                        <span class="me-2"><?php echo $other_episode['episode_number']; ?>.</span>
                                        <?php echo $other_episode['title']; ?>
                                        <?php if($other_episode['is_premium']): ?>
                                        <i class="fas fa-crown text-warning ms-1"></i>
                                        <?php endif; ?>
                                    </div>
                                    <small><?php echo $other_episode['duration']; ?> min</small>
                                </div>
                            </a>
                            <?php endwhile; ?>
                        </div>
                    </div>
                </div>

                <!-- Premium Ad -->
                <?php if(!$is_premium): ?>
                <div class="card bg-dark mt-3">
                    <div class="card-body text-center">
                        <h5 class="text-warning"><i class="fas fa-crown me-2"></i>Go Premium</h5>
                        <p>Unlock all premium episodes, HD quality, and ad-free experience.</p>
                        <a href="<?php echo SITE_URL; ?>/premium.php" class="btn btn-danger">Upgrade Now</a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<?php require_once 'includes/footer.php'; ?>
