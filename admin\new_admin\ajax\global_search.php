<?php
header('Content-Type: application/json');

// Include configuration and functions
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(403);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);
$query = $input['query'] ?? '';

if (empty($query) || strlen($query) < 2) {
    echo json_encode([]);
    exit;
}

try {
    $results = [];
    $query = mysqli_real_escape_string($conn, $query);

    // Search in movies
    $movie_query = "SELECT id, title, poster, release_year, is_premium 
                   FROM movies 
                   WHERE title LIKE '%$query%' 
                   ORDER BY title 
                   LIMIT 5";
    $movie_result = mysqli_query($conn, $movie_query);
    if ($movie_result) {
        while ($row = mysqli_fetch_assoc($movie_result)) {
            $results[] = [
                'title' => $row['title'] . ' (' . $row['release_year'] . ')',
                'type' => 'Movie' . ($row['is_premium'] ? ' - Premium' : ''),
                'icon' => 'fas fa-film',
                'url' => 'edit_movie.php?id=' . $row['id']
            ];
        }
    }

    // Search in TV shows
    $tvshow_query = "SELECT id, title, poster, release_year, is_premium 
                    FROM tvshows 
                    WHERE title LIKE '%$query%' 
                    ORDER BY title 
                    LIMIT 5";
    $tvshow_result = mysqli_query($conn, $tvshow_query);
    if ($tvshow_result) {
        while ($row = mysqli_fetch_assoc($tvshow_result)) {
            $results[] = [
                'title' => $row['title'] . ' (' . $row['release_year'] . ')',
                'type' => 'TV Show' . ($row['is_premium'] ? ' - Premium' : ''),
                'icon' => 'fas fa-tv',
                'url' => 'edit_tvshow.php?id=' . $row['id']
            ];
        }
    }

    // Search in users
    $user_query = "SELECT id, username, email, is_premium, role 
                  FROM users 
                  WHERE (username LIKE '%$query%' OR email LIKE '%$query%') 
                  AND role = 'user'
                  ORDER BY username 
                  LIMIT 5";
    $user_result = mysqli_query($conn, $user_query);
    if ($user_result) {
        while ($row = mysqli_fetch_assoc($user_result)) {
            $results[] = [
                'title' => $row['username'],
                'type' => 'User' . ($row['is_premium'] ? ' - Premium' : '') . ' (' . $row['email'] . ')',
                'icon' => 'fas fa-user',
                'url' => 'users.php?search=' . urlencode($row['username'])
            ];
        }
    }

    // Search in categories
    $category_query = "SELECT id, name 
                      FROM categories 
                      WHERE name LIKE '%$query%' 
                      ORDER BY name 
                      LIMIT 3";
    $category_result = mysqli_query($conn, $category_query);
    if ($category_result) {
        while ($row = mysqli_fetch_assoc($category_result)) {
            $results[] = [
                'title' => $row['name'],
                'type' => 'Category',
                'icon' => 'fas fa-tags',
                'url' => 'categories.php?edit=' . $row['id']
            ];
        }
    }

    // Search in episodes
    $episode_query = "SELECT e.id, e.title, e.episode_number, e.season_number, 
                             t.title as show_title
                     FROM episodes e
                     JOIN tvshows t ON e.tvshow_id = t.id
                     WHERE e.title LIKE '%$query%'
                     ORDER BY e.title 
                     LIMIT 3";
    $episode_result = mysqli_query($conn, $episode_query);
    if ($episode_result) {
        while ($row = mysqli_fetch_assoc($episode_result)) {
            $results[] = [
                'title' => $row['title'],
                'type' => 'Episode - ' . $row['show_title'] . ' S' . $row['season_number'] . 'E' . $row['episode_number'],
                'icon' => 'fas fa-list',
                'url' => 'manage_episodes.php?tvshow_id=' . $row['id']
            ];
        }
    }

    // Search in payments (by transaction ID or user)
    if (is_numeric($query) || strpos($query, 'TXN') !== false) {
        $payment_query = "SELECT p.id, p.transaction_id, p.amount, p.status, 
                                u.username
                         FROM payments p
                         JOIN users u ON p.user_id = u.id
                         WHERE p.transaction_id LIKE '%$query%' 
                            OR u.username LIKE '%$query%'
                         ORDER BY p.created_at DESC
                         LIMIT 3";
        $payment_result = mysqli_query($conn, $payment_query);
        if ($payment_result) {
            while ($row = mysqli_fetch_assoc($payment_result)) {
                $results[] = [
                    'title' => 'Payment #' . $row['transaction_id'],
                    'type' => 'Payment - ৳' . number_format($row['amount']) . ' (' . ucfirst($row['status']) . ') - ' . $row['username'],
                    'icon' => 'fas fa-credit-card',
                    'url' => 'payments.php?search=' . urlencode($row['transaction_id'])
                ];
            }
        }
    }

    // Search in reviews
    $review_query = "SELECT r.id, r.rating, r.comment, 
                           u.username, 
                           COALESCE(m.title, t.title) as content_title,
                           r.content_type
                    FROM reviews r
                    JOIN users u ON r.user_id = u.id
                    LEFT JOIN movies m ON r.content_type = 'movie' AND r.content_id = m.id
                    LEFT JOIN tvshows t ON r.content_type = 'tvshow' AND r.content_id = t.id
                    WHERE r.comment LIKE '%$query%' 
                       OR u.username LIKE '%$query%'
                       OR m.title LIKE '%$query%'
                       OR t.title LIKE '%$query%'
                    ORDER BY r.created_at DESC
                    LIMIT 3";
    $review_result = mysqli_query($conn, $review_query);
    if ($review_result) {
        while ($row = mysqli_fetch_assoc($review_result)) {
            $results[] = [
                'title' => 'Review by ' . $row['username'],
                'type' => 'Review - ' . $row['content_title'] . ' (' . $row['rating'] . '★)',
                'icon' => 'fas fa-star',
                'url' => 'reviews.php?id=' . $row['id']
            ];
        }
    }

    // If no results found, suggest some actions
    if (empty($results)) {
        $results[] = [
            'title' => 'No results found for "' . htmlspecialchars($query) . '"',
            'type' => 'Try searching for movies, TV shows, users, or categories',
            'icon' => 'fas fa-search',
            'url' => '#'
        ];
    }

    // Limit total results
    $results = array_slice($results, 0, 10);

    echo json_encode($results);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Search failed: ' . $e->getMessage()]);
}
?>
