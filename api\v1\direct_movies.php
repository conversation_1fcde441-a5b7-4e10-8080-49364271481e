<?php
// Include direct config file
require_once '../direct_config.php';

// Get movies list
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
$category_id = isset($_GET['category_id']) ? (int)$_GET['category_id'] : 0;
$featured = isset($_GET['featured']) ? (int)$_GET['featured'] : 0;
$offset = ($page - 1) * $limit;

// Build query without status filter
$query = "SELECT m.*, c.name as category_name
          FROM movies m
          LEFT JOIN categories c ON m.category_id = c.id
          WHERE 1=1";

if ($category_id > 0) {
    $query .= " AND m.category_id = $category_id";
}

if ($featured > 0) {
    $query .= " AND m.featured = 1";
}

$query .= " ORDER BY m.id DESC LIMIT $limit OFFSET $offset";

$result = mysqli_query($conn, $query);

if (!$result) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to fetch movies: ' . mysqli_error($conn),
        'data' => null
    ]);
    exit;
}

$movies = [];
while ($movie = mysqli_fetch_assoc($result)) {
    $movies[] = [
        'id' => (int)$movie['id'],
        'title' => $movie['title'],
        'description' => $movie['description'],
        'release_year' => (int)$movie['release_year'],
        'duration' => (int)$movie['duration'],
        'poster' => $movie['poster'] ? (strpos($movie['poster'], 'http') === 0 ? $movie['poster'] : SITE_URL . '/uploads/' . $movie['poster']) : '',
        'banner' => $movie['banner'] ? (strpos($movie['banner'], 'http') === 0 ? $movie['banner'] : SITE_URL . '/uploads/' . $movie['banner']) : '',
        'trailer_url' => $movie['trailer_url'],
        'rating' => (float)$movie['rating'],
        'category_id' => (int)$movie['category_id'],
        'category_name' => $movie['category_name'],
        'featured' => (bool)$movie['featured'],
        'premium_only' => (bool)$movie['premium_only'],
        'status' => $movie['status']
    ];
}

// Get total count for pagination without status filter
$count_query = "SELECT COUNT(*) as total FROM movies WHERE 1=1";

if ($category_id > 0) {
    $count_query .= " AND category_id = $category_id";
}

if ($featured > 0) {
    $count_query .= " AND featured = 1";
}

$count_result = mysqli_query($conn, $count_query);
$total = mysqli_fetch_assoc($count_result)['total'];

// Return movies list
http_response_code(200);
echo json_encode([
    'success' => true,
    'message' => 'Success',
    'data' => [
        'movies' => $movies,
        'meta' => [
            'pagination' => [
                'total' => (int)$total,
                'per_page' => $limit,
                'current_page' => $page,
                'last_page' => ceil($total / $limit)
            ]
        ]
    ]
]);
?>
